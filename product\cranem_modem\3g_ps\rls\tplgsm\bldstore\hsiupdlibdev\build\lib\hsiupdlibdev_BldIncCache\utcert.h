/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/tplgsm/utinc/utcert.h#7 $
 *   $Revision: #7 $
 *   $DateTime: 2006/02/09 12:25:17 $
 **************************************************************************
 * File Description
 * ----------------
 * Function Header for WAP Security Functions
 **************************************************************************/
#if !defined (SYSTEM_H)
#  include <system.h>
#endif

#ifndef UTCERT_H
#define UTCERT_H


/***************************************************************************
 * Nested Include Files
 **************************************************************************/

/***************************************************************************
 * Macros
 **************************************************************************/
/* Known public key algorithms */
#define PKT_TYPE_UNKNOWN     0x00
#define PKT_TYPE_RSA         0x01
#define PKT_TYPE_ECC         0x02
#define PKT_TYPE_ECC_PACKED  0x03
#define PKT_TYPE_DH          0x04

/***************************************************************************
 * Manifest Constants
 **************************************************************************/
#define UT_MAX_CERT_NAME_LEN   65

/* Certificate chain warning masks */
#define CERT_WARN_NONE                 0x00000000
#define CERT_WARN_WEAK_SECURITY_MASK   0x000000FF
#define CERT_WARN_MISCONFIG_MASK       0x0000FF00
#define CERT_WARN_ATTACK_MASK          0x00FF0000
#define CERT_WARN_AUTH_FAILED_MASK     0xFF000000


/* Weak security warnings */
#define CERT_WARN_CHAIN                0x00000001
#define CERT_WARN_NO_KEY_USAGE         0x00000002

#define CERT_WARN_TRUSTED_EXPIRED      0x00000100
#define CERT_WARN_CERT_VALIDITY        0x00000200
#define CERT_WARN_TRUSTED_NO_KEY_USAGE 0x00000400
#define CERT_WARN_TRUSTED_KEY_MISUSE   0x00000800

#define CERT_WARN_KEY_MISUSE           0x00010000

#define CERT_WARN_NO_TRUSTED_CERTS     0x01000000
#define CERT_WARN_INVALID_SIGNATURE    0x02000000
#define CERT_WARN_CORRUPTED_CERT       0x04000000
#define CERT_WARN_INTERNAL             0x08000000

#define CIC_IS_ERR_OR_ALERT(rc) ((rc) != CIC_ERR_NONE)

/* General errors */
#define CIC_ERR_UNIT_MASK       0x00FF0000L

#define CIC_ERR_GENERAL_BASE    0x00010000L

#define CIC_ERR_INTERNAL        0x80010000L

#define CIC_ERR_NONE            0x00000000L
#define CIC_ERR_NO_PTR          0x81010001L
#define CIC_ERR_ILLEGAL_PARAM   0x81010002L
#define CIC_ERR_MEMORY          0x81010003L
#define CIC_ERR_SMALL_BUFFER    0x81010004L
#define CIC_ERR_WOULD_BLOCK     0x81010005L
#define CIC_ERR_TIMEOUT         0x81010006L
#define CIC_ERR_BAD_LENGTH      0x81010007L
#define CIC_ERR_NOT_FOUND       0x81010008L
#define CIC_ERR_BAD_CTX         0x81010009L
#define CIC_ERR_BAD_INDEX       0x8101000AL
#define CIC_ERR_RANDOM          0x8101000BL

/***************************************************************************
 * Typed Constants
 **************************************************************************/

 /***************************************************************************
 * Type Definitions
 **************************************************************************/
typedef enum UtCertIdTypeTag
{
  UT_CERT_NO_IDENTITY      = 0,
  UT_CERT_TEXT_IDENTITY    = 1,
  UT_CERT_BINARY_IDENTITY  = 2,
  UT_CERT_SHA_1_PUBLIC_KEY = 254,

  UT_CERT_IDENTITY_UNKNOWN = 255
}UtCertIdType;

typedef struct UtCertIdTag
{
  UtCertIdType type;
  Int16        characterSet;
  Int8         name[UT_MAX_CERT_NAME_LEN];
  Int8         length;
} UtCertId;

typedef enum UtCertTypeTag
{
  UT_WTLS_CERT,
  UT_X509_CERT,
  UT_UNKNOWN_CERT
}UtCertType;


typedef enum CERT_EncodingTag
{
  CERT_ENC_BASE64 = 1,
  CERT_ENC_BINARY = 2
} CERT_Encoding;

typedef enum CERT_FormatTag
{
  CRT_FRM_WTLS        = 1,
  CRT_FRM_X509        = 2
}CERT_Format;

typedef enum CIC_AllocationTypeTag
{
  CIC_ALLOCATION_UNKNOWN          = 0,
  CIC_ALLOCATION_POINTER_COPY     = 1,
  CIC_ALLOCATION_DATA_COPY        = 2
} CIC_AllocationType;


typedef Int32 CIC_Err;

typedef struct UTIL_PtrArrTag
{
  Int32 count;
  Int32 size;
  Int32 step;
  void** data;
} UTIL_PtrArr;

typedef struct UTIL_BufferTag
{
  Int8 mustDealloc;
  Int32 length;
  Int8* data;
}UTIL_Buffer;

typedef struct WTLS_DistinguishedNameTag
{
  UTIL_Buffer country;
  UTIL_Buffer state;
  UTIL_Buffer locality;
  UTIL_Buffer organization;
  UTIL_Buffer org_unit; /* Also: servicename */
  UTIL_Buffer common_name;
  UTIL_PtrArr* fields;
} WTLS_DistinguishedName;


typedef struct WTLS_IdentifierTag
{
  Int16 type;

  Int16 has_fields;
  union WTLS_IdentifierData
  {
    /* Text
     */
    struct WTLS_TextId
    {
      Int16 char_set;
      WTLS_DistinguishedName dist_name;
      UTIL_Buffer name;
    } text;
    struct WTLS_BinaryId
    {
      UTIL_Buffer identifier;
    } binary;
    struct WTLS_KeyHashId
    {
      UTIL_Buffer key_hash;
    } key_hash_sha;
    struct WTLS_X509Name
    {
      UTIL_Buffer distinguished_name;
      WTLS_DistinguishedName dist_name;
    } x509_name;
  } data;
} WTLS_Identifier;


typedef struct CertParseContext CertParseContext;

/* Parsing callback prototypes */
typedef CIC_Err
(*ParseSubjectT)(
    CertParseContext* p,
    WTLS_Identifier** subject
);

typedef struct PKC_PublicKey  PKC_PublicKey;

typedef CIC_Err PKC_PubKeyDestroy_T(
    PKC_PublicKey** pkey
);

struct PKC_PublicKey
{
  Int16 type;
  Int16 ref_count;

  PKC_PubKeyDestroy_T* Destroy;

  /* rsa */
  Int16 key_size; /* In bytes */
  UTIL_Buffer n;  /* modulus */
  UTIL_Buffer e;  /* public exponent */

  /* dh & ecdh */
  UTIL_Buffer key;
};

typedef struct PKC_Signature  PKC_Signature;

#if !defined (USE_INTERNAL_CERT_DEFNS)
typedef CIC_Err PKC_SignatureDestroy_T(
    PKC_Signature** signature
);

struct PKC_Signature
{
  Int16 type;
  Int16 ref_count;

  PKC_SignatureDestroy_T* Destroy;

  union
  {
    struct
    {
      int encoding;
      UTIL_Buffer sign;
    }rsa;
    struct
    {
      UTIL_Buffer r_part;
      UTIL_Buffer s_part;
    }ecc;
  }data;
};
#endif

typedef struct LLElement
{
  struct LLElement* next;
  struct LLElement* prev;
  void* data;
}LLElement;

typedef struct LinkedList
{
  Int16 count;
  LLElement* head;
  LLElement* tail;
}LinkedList;

typedef struct LinkedList CERT_CertList;

typedef struct CERT_CertificateTag
{
  Int16 refCount;
  Int16 formatId;
  struct CERT_CertFormat* format;
  struct CertParseContext* parsed;
  UTIL_Buffer data;
}CERT_Certificate;


#if !defined (USE_INTERNAL_CERT_DEFNS)
typedef struct CERT_SignatureAlg { int fake; } CERT_SignatureAlg;
typedef struct CERT_CertFormat { int fake; } CERT_CertFormat;

#else
typedef struct CERT_CertFormat   CERT_CertFormat;
typedef struct CERT_SignatureAlg CERT_SignatureAlg;
#endif /*USE_INTERNAL_CERT_DEFNS*/

extern CERT_SignatureAlg * CERT_SIG_ALG_RSA_p;
extern CERT_SignatureAlg * CERT_SIG_ALG_ECDSA_3_p;
extern CERT_SignatureAlg * CERT_SIG_ALG_ECDSA_5_p;
extern CERT_SignatureAlg * CERT_SIG_ALG_ECDSA_7_p;

extern CERT_CertFormat * CERT_FMT_WTLS_p;
extern CERT_CertFormat * CERT_FMT_X509_p;



typedef CERT_CertList      UtCertList;
typedef CERT_Certificate   UtCertObj;
typedef CIC_AllocationType UtCertAlloc;
typedef CERT_Encoding      UtCertEnc;
typedef WTLS_Identifier    UtCertWtlsId;
typedef CIC_Err            UtCertErr;
typedef PKC_PublicKey      UtPublicKey;

typedef CERT_CertFormat    UtCertFormat;
typedef CERT_SignatureAlg  UtCertSigAlg;

/***************************************************************************
 *  Macros
 **************************************************************************/

/***************************************************************************
 *  Function Prototypes
 **************************************************************************/
extern Boolean
utDecodeWtlsPublicKeyType ( const Int8 * data_p, Int32 length, Int16 * keyType_p);

extern Boolean
utDecodeX509PublicKeyType ( const Int8 * data_p, Int32 length, Int16 * keyType_p);

extern Boolean
utDecodeWtlsSubject       ( const Int8 * data_p, Int32 length, UtCertId * subject);

extern Boolean
utDecodeX509Subject       ( const Int8 * data_p, Int32 length, UtCertId * subject);

extern Boolean
utValidateWtlsCert        ( const Int8 * data_p, Int32 * length_p );

extern Boolean
utValidateX509Cert        ( const Int8 * data_p, Int32 * length_p );



#if !defined (USE_INTERNAL_CERT_DEFNS)
extern UtCertErr
utVerifyChain(
    UtCertList* chain,
    UtCertList* trusted_certs,
    UtCertSigAlg* supportedSigAlgs_p[],
    void* sessionRef,
    Int32* warnings_summary,
    Int32* warnings_buffer,
    Int16* effective_chain_length,
    UtCertObj** trustedCert_p,
    UtCertErr* extendedErr_p);

extern UtCertErr
utVerifySelfSigned(
    UtCertObj* cert_p,
    UtCertSigAlg* supportedSigAlgs_p[],
    void* sessionRef_p,
    Int32* warnings_p,
    UtCertErr* extendedErr_p);

extern UtCertErr utAddCertificate(
    UtCertList* list_p,
    Int8* data_p,
    Int16 length,
    UtCertFormat* certFormat_p,
    UtCertAlloc certAllocationType,
    UtCertEnc encoding);

extern UtCertErr utCreateCertificate(
    Int8* certData_p,
    Int32* length,
    UtCertFormat* format_p,
    UtCertEnc encoding,
    UtCertAlloc alloc,
    UtCertObj** cert_pp);


extern UtCertErr utParseSubject(
    UtCertObj* cert,
    UtCertWtlsId** subject_pp);

extern UtCertErr utParseIssuer(
    UtCertObj* cert,
    UtCertWtlsId** subject_pp);

extern UtCertErr utParsePublicKey(
    UtCertObj    *cert_p,
    UtPublicKey **pubKey_pp,
    Int16        *param);


extern UtCertErr utCreateCertList(UtCertList** list_pp);

extern UtCertErr utDestroyCertList(UtCertList* list_p);

extern UtCertErr utReleaseCertificate(UtCertObj* cert_p);

UtCertErr utParseValidity(
    UtCertObj *cert_p,
    Int32     *from,
    Int32     *to);

UtCertErr utParseSignature(
    UtCertObj *cert_p,
    PKC_Signature **signature);

#endif /*USE_INTERNAL_CERT_DEFNS*/

#endif
/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/***************************************************************************
 * 3GPP Protocol Stack Copyright (c) TTP Communications PLC 2001
 ***************************************************************************
 *
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urrrbcehs.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2007/05/03 13:53:25 $
 *
 ***************************************************************************
 *
 * File Description:
 *
 *    Header file for RRC's Radio Bearer Control sub-process for EHS sub-module.
 *    Contains definitions for use by RBC EHS module, and other RBC sub-modules.
 *
 ***************************************************************************
 *
 ***************************************************************************/
#if !defined (URRRBCEHS_H)
#define       URRRBCEHS_H

#if defined(UPGRADE_3G_HSDPA)

/***************************************************************************
* Nested Include Files
***************************************************************************/
#include <urrrbc.h>
#include <urrrbcl.h>
#include <cphy_sig.h>
#include <pl_w_globs.h>
#include <cmac_sig.h>

/***************************************************************************
* Macros
***************************************************************************/

/***************************************************************************
 * Types
 ***************************************************************************/

typedef struct UrrRbcEhsDbInputsTag
{
    UrrRbcEhsData *src_p;
    UrrRbcEhsData *dest_p;
}
UrrRbcEhsDbInputs; 

typedef struct UrrRbcEhsFindQueueInputsTag
{
    UMAC_ehs_QueueId    queueId;
    Boolean             isDelQueue;
    Int8                *position;
}
UrrRbcEhsFindQueueInputs;

/* efrats need to check about naming convention */
typedef struct UrrRbcEhsConfigQueueInputsTag
{
    Int8            configId;
    ActivationTime  actTime;
    Boolean         isHhoFailureCase;
}
UrrRbcEhsConfigQueueInputs;

typedef struct UrrRbcEhsReleaseQueueInputsTag
{
    Int8            configId;
    ActivationTime  actTime;
    Boolean         releaseAllQueues; /**< will be set to true when HSDPA is being closed */
}
UrrRbcEhsReleaseQueueInputs;

typedef struct UrrRbcEhsHsdpaInfoWithTransTag
{
    UrrRbcTransaction *trans_p;
    UrrRbcHsdpaInfo *hsdpaInfo_p;
}
UrrRbcEhsHsdpaInfoWithTrans;

typedef struct UrrRbcEhsPichOverHsInfoTag
{
    UPICH_Info_fdd pichInfo;
    Int16 numPichOverHs;
}
UrrRbcEhsPichOverHsInfo;

typedef struct UrrRbcEhsConfigIdActTimeTag
{
    Int8  configId;
    ActivationTime actTime;
}
UrrRbcEhsConfigIdActTime;


/***************************************************************************
* Enums
***************************************************************************/
/* Modification of this enum also requires update of EhsRequestTypesString in urrutm.c */
//ICAT EXPORTED ENUM
typedef enum EhsRequestTypesTag
{
    /*** Common request types ***/
    EHS_INIT,
    EHS_INIT_DATABASES,
    EHS_COPY_DATABASES,
    EHS_SET_MAC_EHS_RESET,
    EHS_INIT_DEL_QUEUE,
    EHS_UPDATE_EHS_QUEUE_LIST,
    EHS_DELETE_EHS_QUEUE,
    EHS_FIND_QUEUE_IN_LIST,
    EHS_CONFIG_EHS_QUEUES,
    EHS_RELEASE_EHS_QUEUES,
    EHS_IS_MAC_CONFIG_DIFF_FROM_BACKUP,
    /*** ECF only request types ***/
    EHS_HS_DSCH_RECEPTION_IN_PCH,
    EHS_SET_TRIGGERED_MEAS_REPORT,
    EHS_SET_HSD_RECEPTION_GENERAL,
    EHS_SET_HSD_RECEPTION_CCCH,
    EHS_SET_HSD_RECEPTION_FACH,
    EHS_PROCESS_HSDSCH_RECEPTION_GENERAL,   
    EHS_PROCESS_HSDSCH_RECEPTION_CELL_FACH_STATE,
    EHS_PROCESS_HSDSCH_RECEPTION_OF_CCCH_ENABLED,
    EHS_SELECT_COMMON_HRNTI,
    EHS_SELECT_INITIAL_COMMON_HRNTI,
    EHS_SELECT_AND_FILL_PDSCH_INFO,
    EHS_SELECT_PICH,
    EHS_SAVE_SYSTEM_INFORMATION_DATA,
    EHS_FILL_SRB1_MAPPING_INFO,
    EHS_FILL_HARQ_SYSTEM_INFO,
    EHS_FILL_HS_SCCH_INFO,
    EHS_GET_SELECTED_PICH_INFO,
    EHS_CONFIG_HS_RB0,
//********** begin
#if defined(UPGRADE_HS_FACH_DRX)
    EHS_PROCESS_HSDSCH_CELL_FACH_DRX,
#endif
//********** end
    EHS_SET_SYS_INFO_PARAMS_VALID
}
EhsRequestTypes;

typedef enum EhsGlobalVariableValueTypesTag
{
    EHS_HS_DSCH_RECEPTION_CELL_FACH_STATE,
    EHS_HS_DSCH_RECEPTION_OF_CCCH_ENABLED,
    EHS_HS_DSCH_RECEPTION_GENERAL,
//********** begin
#if defined(UPGRADE_HS_FACH_DRX)
    EHS_HS_DSCH_CELL_FACH_DRX_VALUE,
#endif
//********** end
    EHS_TRIGGERED_MEASUREMENT_REPORT
}
EhsGlobalVariableValueTypes;

/***************************************************************************
* Function Declarations
***************************************************************************/

/************************ External Interface Functions ***************************/
Boolean UrrRbcEhsHandler(EhsRequestTypes requestType, void* input_p, Boolean setParam);

Boolean UrrRbcEhsCondition1Fulfilled(Boolean checkOnlyCapability);

Boolean UrrRbcEhsCondition2Fulfilled(Boolean checkOnlyCapability);

Boolean UrrRbcEhsGetHsDschReceptionValue(EhsGlobalVariableValueTypes type);

Boolean HsDschInFachIsSupported (UmtsMobileEquipmentData* umts_p);

Boolean HsDschInPchIsSupported (UmtsMobileEquipmentData* umts_p);

Boolean EhsGetHsDschReceptionGeneral (void);
//********** begin
#if defined(UPGRADE_HS_FACH_DRX) 
void  UrrRbcSaveHsDrxCellFachInfo (USysInfoType5_v860ext_IEs_fdd* ecfDrxParams_p);
#endif
//********** end

#if defined(UPGRADE_UL_ECF) // ********** begin	
Boolean UrrRbcEhsGetPagingSystemInfoValid (void);
#endif /* UPGRADE_UL_ECF  */ // ********** end  

void EhsCleanCommonSystemInformation (UrrRbcEhsData *Db_p);/*********** add*/

/************************ External Interface Functions ***************************/

#endif
#endif// UPGRADE_3G_HSDPA 


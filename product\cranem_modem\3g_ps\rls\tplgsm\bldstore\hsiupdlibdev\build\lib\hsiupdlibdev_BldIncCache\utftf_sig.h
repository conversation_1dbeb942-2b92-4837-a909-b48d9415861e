/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/pscommon/3g_ut.mod/api/inc/utftf_sig.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2007/03/19 13:59:30 $
 **************************************************************************
 * File Description: Flexible Trace Framework Control Signal Definitions
 **************************************************************************/

#ifndef UT_FTF_SIG_H
#define UT_FTF_SIG_H

/***************************************************************************
 * Nested Include Files
 **************************************************************************/
#include <system.h>
#include <pssignal.h>
#include <utftftaevents.h>

/***************************************************************************
 * Constants
 **************************************************************************/
/* Maximum number of bytes per traced printout */
#if ! defined (UT_FTF_TRACE_BUFFER_SIZE)
#define UT_FTF_TRACE_BUFFER_SIZE 128
#endif

/* Maximum file name length */
#if ! defined (UT_FTF_MAX_FILE)
#define UT_FTF_MAX_FILE 15
#endif

/* Timing Analysis Constants */
#define UT_FTF_TA_NUM_STATIC_CONTAINERS    100
#define UT_FTF_TA_CONTAINER_SIZE            60
#define UT_FTF_TA_MISFIRE_THRESHOLD         10

/* Task Information Constants */
#if !defined (UT_FTF_TA_MAX_TASK_NAME_LENGTH)
#define UT_FTF_TA_MAX_TASK_NAME_LENGTH      30
#endif /* !UT_FTF_TA_MAX_TASK_NAME_LENGTH */
#define UT_FTF_TA_NUM_TASKS_NAME_PER_SIG    10

/***************************************************************************
 * Type Definitions
 **************************************************************************/

typedef struct UtFlexibleTraceControlReqTag
{
    /* TRUE if enabling tracing                      */
    Boolean enableTrace;

    /* TRUE if disabling tracing                     */
    Boolean disableTrace;

    /* bitmap of trace categories for enable         */
    Int32 traceCategoryEnable;

    /* bitmap of trace categories for disable        */
    Int32 traceCategoryDisable;

    /* sub-process identity                          */
    Int8 subProcess;

    /* TRUE if triggering on external signal         */
    Boolean triggerEnableOnExternal;
    Boolean triggerDisableOnExternal;

    /* external signal to enable trace               */
    SignalId enablingSignalExt;

    /* external signal to disable trace              */
    SignalId disablingSignalExt;

    /* TRUE if triggering on internal signal         */
    Boolean triggerEnableOnInternal;
    Boolean triggerDisableOnInternal;

    /* internal signal to enable trace               */
    Int32 enablingSignalInt;

    /* internal signal to disable trace              */
    Int32 disablingSignalInt;

    /* User-defined condition data                   */
    Int32 conditionData;
}
UtFlexibleTraceControlReq;

typedef struct UtFlexibleTraceOutTag
{
    Int32 subProcess;
    Int32 traceCategory;
    Int32 line;
    char file [UT_FTF_MAX_FILE];
    Boolean useParams;
    Int32 param1;
    Int32 param2;
    Int32 param3;
    Int32 param4;
    Int32 param5;
    Int32 param6;
    /* needs to be last to enable shrinking the signal size */
    char string [UT_FTF_TRACE_BUFFER_SIZE];
}
UtFlexibleTraceOut;

/* Timing Analysis */
typedef struct UtFlexibleTraceTaControlReqTag
{
    /* User profile setting                          */
    utFtfTaProfile userProfile;
}
UtFlexibleTraceTaControlReq;

typedef struct
{
    utFtfTaEvent    event;
    Int32           timeStamp;
    Int32           data;
}
UtFlexibleTraceTaEvent;

typedef struct
{
    Boolean                   output;
    Int32                     flags;
    Int32                     timerFrequency;
    Int32                     numEvents;
}
UtFlexibleTraceTaOutCntrl;

typedef struct
{
    UtFlexibleTraceTaOutCntrl control;
    UtFlexibleTraceTaEvent    events[UT_FTF_TA_CONTAINER_SIZE];
}
UtFlexibleTraceTaOut;

typedef struct
{
    Int32                     taskId;
    Int32                     taskPriority;
    char                      taskName[UT_FTF_TA_MAX_TASK_NAME_LENGTH];
}
UtFlexibleTraceTask;

typedef struct
{
    Int32                     numTasks;
    UtFlexibleTraceTask       taskInfo[UT_FTF_TA_NUM_TASKS_NAME_PER_SIG];
}
UtFlexibleTraceTaskInfo;

#endif

/* END OF FILE */

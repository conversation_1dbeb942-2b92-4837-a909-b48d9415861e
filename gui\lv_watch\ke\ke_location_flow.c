
#include "ke_location_flow.h"
#include "../include/app_adaptor_interface.h"
#if USE_CRANE_WATCH_GPS != 0
#include "lv_drivers\xf_drv\gps\GpsDrvApi.h"
#endif

static void *sCommTimer = NULL;

#if USE_LV_WATCH_EXTRA_WIFI != 0

static ExtraLocationUp sExtraLocUpInfor[MAX_EXT_LOC_UP_NUM] = {0};

void Flw_Expra_Wifi_Scan_Cb(app_adp_wifi_result_t result, app_adp_wifi_ap_list * ap_list);
#endif

#define SETP_COUNT_MAX 20
#define GSENSOR_ACTIVE_THRESHOLD 10
static unsigned int g_step_per_min[SETP_COUNT_MAX] = {0};
#define WIFI_SCAN_MAX_RETRY  3  // 最大重试次数
static uint8_t wifi_scan_retry_count = 0;  // 重试计数器

// WiFi扫描测试相关定义
#define WIFI_SCAN_TEST_TIMEOUT_MS  60000  // 60秒测试超时（增加超时时间）
#define WIFI_SCAN_TEST_MAX_ROUNDS  3      // 最大扫描轮次
#define WIFI_SCAN_TEST_MAX_APS     50     // 测试时最大AP数量（比默认的8或20更大）

// 扩展的WiFi AP项目结构，包含SSID信息
typedef struct {
    uint8_t mac[6];
    int32_t rssi;
    char ssid[33];  // SSID最大32字符 + 结束符
} wifi_scan_test_ap_item;

// 扩展的WiFi AP列表结构，用于测试
typedef struct {
    uint8_t count;
    wifi_scan_test_ap_item item[WIFI_SCAN_TEST_MAX_APS];
} wifi_scan_test_ap_list;

static bool wifi_scan_test_active = false;
static void* wifi_scan_test_timer = NULL;
static uint8_t wifi_scan_test_round = 0;  // 当前扫描轮次
static wifi_scan_test_ap_list* wifi_scan_all_results = NULL;  // 累积扫描结果

void Flw_WifiHandle(START_WIFI_TPYE Tpye);
void Flw_GpsHandle(void);

// WiFi扫描测试接口声明
void Flw_WiFi_Scan_Test_Interface(void);
static void Flw_WiFi_Scan_Test_Callback(app_adp_wifi_result_t result, app_adp_wifi_ap_list * ap_list);
static void Flw_WiFi_Scan_Test_Timeout_Cb(uint32_t param);
static void Flw_WiFi_Get_SSID_From_HAL(wifi_scan_test_ap_item* test_item, uint8_t* mac);


bool Flw_IsStepChange(void)
{
	uint8_t i;

	for(i=0; i<SETP_COUNT_MAX; i++)
	{
		if(g_step_per_min[i] != 0)
			break;
	}		

	if(i >= SETP_COUNT_MAX)
	{
		return false;
	}
	return true;
}


void Flw_SetSteps(void)
{
	uint8_t i;
	static unsigned int step_last = 0;
	unsigned int step_tmp = mmi_get_steps_count();

	printf("Flw_SetSteps %d,%d\r\n",step_last,step_tmp);

	for(i=SETP_COUNT_MAX-1; i>0; i--)
	{
		g_step_per_min[i] = g_step_per_min[i-1];
	}
	
	if(step_tmp > step_last)
		g_step_per_min[0] = step_tmp - step_last;
	else
	{
		g_step_per_min[0] = 0;
	}
	step_last = step_tmp;
}

#define ACTIVE_COUNT_MAX 20

static unsigned int g_active_per_min[SETP_COUNT_MAX] = {0};
static unsigned int count_last = 0;

void Flw_ActiveValidInit(void)
{
	g_active_per_min[0] = 2;
}

#if 0
bool Flw_IsActiveValid(void)
{
	uint8_t i;

	/* judge if the active is change in 2 minutes */
	for(i=0; i<ACTIVE_COUNT_MAX/5; i++)
	{
		WS_PRINTF("Flw_IsActiveValid [%d]=%d", i,g_active_per_min[i]);
		/* 0 means is not change */
		if(g_active_per_min[i] != 0)
			break;
	}		

	if(i >= ACTIVE_COUNT_MAX/5)
	{
		return false;
	}
	return true;
}
#endif

void ke_clear_active(void)
{
#if USE_CRANE_WATCH_GSENSOR
	ke_clear_actives_count(); // clean gsensor_actives
#endif	
	count_last = 0;
	memset(g_active_per_min,0,sizeof(g_active_per_min));
}

bool Flw_IsActiveValid(void)
{
	uint8_t i;
	uint16_t active_Sum = 0;
	for(i=0; i<ACTIVE_COUNT_MAX; i++)
	{
		WS_PRINTF("Flw_IsActiveValid [%d]=%d", i,g_active_per_min[i]);
		/* 0 means is not change */
		//if(g_active_per_min[i] >= 0)
			//break;
		active_Sum += g_active_per_min[i];
		
	}

	ke_clear_active();
	WS_PRINTF("###active_Sum=%d, thres = %d ###\n", active_Sum,GSENSOR_ACTIVE_THRESHOLD);
	if(active_Sum > GSENSOR_ACTIVE_THRESHOLD)
		return true;
	else
		return false;
}


void Flw_UpdateActiveCount(void)
{
	uint8_t i;
	//static unsigned int count_last = 0;
#if USE_CRANE_WATCH_GSENSOR
	unsigned int count_tmp = ke_get_actives_count();
#else
	unsigned int count_tmp = 0;
#endif	
	WS_PRINTF("###%s,%d,%d### \r\n",__FUNCTION__,count_last,count_tmp);
	
	for(i=ACTIVE_COUNT_MAX-1; i>0; i--)
	{
		g_active_per_min[i] = g_active_per_min[i-1];
	}
	
	if(count_tmp > count_last)
	{	
		g_active_per_min[0] = count_tmp - count_last;
	}	
	else
	{
		g_active_per_min[0] = 0;
	}
	
	count_last = count_tmp;

	//for(i=0; i<ACTIVE_COUNT_MAX; i++)
	//{
	//	printf("%s %d",__FUNCTION__, g_active_per_min[i]);
	//}
}

void  Flw_SendMsgWifiHandle(START_WIFI_TPYE Tpye)
{
	ws_client *pme = MMI_ModemAdp_WS_GetClient();
	ws_evt_msg_t  *cmd_info = (ws_evt_msg_t*)malloc(sizeof(ws_evt_msg_t));
	memset((void *)cmd_info, 0x00, sizeof(ws_evt_msg_t));
	cmd_info->client = (void*)pme;
	cmd_info->evt = CMD_S2C_WIFI_HANDLE;
  
	cmd_info->cmd.StartWifiType = Tpye;

	WS_PRINTF("Flw_SendMsgWifiHandle %d", Tpye);
	CWatchService_NofityClient(pme, cmd_info);
	free(cmd_info);
}

void  Flw_SendMsgUpLocation(uint32_t LocationUpType)
{
	ws_client *pme = MMI_ModemAdp_WS_GetClient();
	ws_evt_msg_t  *cmd_info = (ws_evt_msg_t*)malloc(sizeof(ws_evt_msg_t));
	memset((void *)cmd_info, 0x00, sizeof(ws_evt_msg_t));
	cmd_info->client = (void*)pme;
	cmd_info->evt = CMD_DEV_IND_LOCATION;
  
	cmd_info->cmd.LocationUpType = LocationUpType;

	WS_PRINTF("Flw_SendMsgUpLocation %d", LocationUpType);
	CWatchService_NofityClient(pme, cmd_info);
	free(cmd_info);
}

 #if USE_LV_WATCH_EXTRA_WIFI != 0
 void  Flw_SendMsgHandleExtLocation(uint32_t Par)
{
	ws_client *pme = MMI_ModemAdp_WS_GetClient();
	ws_evt_msg_t  *cmd_info = (ws_evt_msg_t*)malloc(sizeof(ws_evt_msg_t));
	ExtraLocationUp ExtLoc;
	ws_printf("enter %s ", __FUNCTION__);
	if(Flw_GetExpraWifi(&ExtLoc) == MAX_EXT_LOC_UP_NUM)  
	{
		free(cmd_info);
		return;
	}
	memset((void *)cmd_info, 0x00, sizeof(ws_evt_msg_t));
	cmd_info->client = (void*)pme;
	cmd_info->evt = CMD_S2C_EXT_LOCATION_HANDLE;

	cmd_info->cmd.ptr = &ExtLoc;

	//WS_PRINTF("Flw_SendMsgUpLocation %d", LocationUpType);
	CWatchService_NofityClient(pme, cmd_info);
	free(cmd_info);
}

 void Flw_StartTimer(int Sec,void (*callback)(uint32_t),uint32_t Par)
{
	
	if(sCommTimer == NULL)
	{
		uos_timer_create(&sCommTimer);
	}
	uos_timer_stop(sCommTimer);
    uos_timer_start(sCommTimer, TICKES_IN_SECOND * Sec, 0, callback, (uint32_t)Par);
}

void Flw_StopTimer(void)
{
	
	if(sCommTimer == NULL)
	{
		return;
	}
	uos_timer_stop(sCommTimer);
}

void Flw_StartTimerToHandleExtLocation(void)
{
	Flw_StartTimer(10, Flw_SendMsgHandleExtLocation, NULL);
}
 
void Flw_HandleExtLocation(void)
{
	ExtraLocationUp ExtLoc;
	
	char Index = Flw_GetExpraWifi(&ExtLoc);

	ws_printf("enter %s Index is %d", __FUNCTION__, Index);
	if(Index == MAX_EXT_LOC_UP_NUM)  
	{
		Flw_StopTimer();
		return;
	}
				
	Flw_StartTimerToHandleExtLocation();

	CWatchService_SendExtLocation(&ExtLoc);
	Flw_ClearExpraWifiInfo(Index);					
}
#endif

#if USE_CRANE_WATCH_GPS != 0

#if defined(__XF_PRO_K927__) || defined(__XF_PRO_K1__) || defined(__XF_PRO_KW19__)

static char g_gpsOnceFixed = 0;
static uint8_t ke_gpsfixed = 0;
static char fixtime = 0;

void SetGpsFixCb(GpsGetPosCallback pCallbackFun)
{
	GpsGetPostionInfo(pCallbackFun);
}

char GetGpsOnceFixed(void)
{
	return g_gpsOnceFixed;
}

void SetGpsOnceFixed(char GpsOnceFixed)
{
	 g_gpsOnceFixed = GpsOnceFixed;
}

uint8_t GetGpsHaveFixed(void)
{
    return ke_gpsfixed;
}
#endif

void Flw_StartGps(ws_kaer_t *userData)
{
	ws_client *pme = MMI_ModemAdp_WS_GetClient();
	if(userData == NULL)
	{	 
		userData = (ws_kaer_t*)pme->adapter->data;
	}
	if(userData->mGpsON == 1)
	{
		SetGpsOnceFixed(0);
		return;
	}
	if(GetGpsHaveFixed() == 0)  
	{
		WS_PRINTF("Flw_StartGps()START COLD GPS ");
	}
	else
	{
		WS_PRINTF("Flw_StartGps()START HOT GPS ");		
	}
	
	userData->mGpsON = 1;
	SetGpsOnceFixed(0);
}

void Flw_JudgeGps(void)
{
	uint8_t Data[5] = {0};
	ws_client *pme = MMI_ModemAdp_WS_GetClient();
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	
	uos_timer_stop(sCommTimer);
	
	GetGpsData(Data);
	WS_PRINTF("Flw_JudgeGps() GetGpsOnceFixed is %d", GetGpsOnceFixed());
	WS_PRINTF("Gps sig 40:%d 35:%d 30:%d 25:%d 20:%d",Data[0],Data[1],Data[2],Data[3],Data[4]);
	

	if(lv_watch_get_activity_obj(ACT_ID_FACTORY_MODE_MAIN)||lv_watch_get_activity_obj(ACT_ID_FACTORY_MODE_AUTOTEST))
	{
		WS_PRINTF("Flw_JudgeGps() FACTORY MODE\r\n");	
	}
	else
	{
		if(GetGpsOnceFixed() == 0)
		{	
			WS_PRINTF("Flw_JudgeGps() is not fixed or signle is weak");	
			Flw_SendMsgWifiHandle(STRAT_WIFI_TPYE_GPS_STARTED);
			return ;
		}
		
		#if USE_LV_WATCH_SPAIN != 0
			Flw_SendMsgWifiHandle(STRAT_WIFI_TPYE_GPS_STARTED);
		#else
			Flw_SendMsgUpLocation(LOCATION_UP_TYPE_GPS);
		#endif
		memset(&userData->mHisLocationInfo.mLastUpWifi, 0, sizeof(ws_wifi_info));
	}	
}

void Flw_JudgeGpsOvertime(uint32_t Par)
{
	Flw_JudgeGps();
}

static void Flw_gps_fix_callback_func(GpsPositionInfo nInfo)
{
	ws_printf("%s enter",__func__);
	ws_printf( "[HANDLE_GPS]fixed=%d,latitude=%f,longitude=%f", nInfo.status,nInfo.latf, nInfo.lonf);

	if(nInfo.status == GSP_NEW_POS)
	{
	    ke_gpsfixed = 1;
		g_gpsOnceFixed = 2;
	
		ws_printf("%s: okok", __func__);
		Flw_JudgeGps();
	}

	ws_printf("[HANDLE_GPS]%s exit %d",__func__);
}

void Flw_GpsHandle(void)
{
	ws_client *pme = MMI_ModemAdp_WS_GetClient();

	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
		
	WS_PRINTF("Flw_GpsHandle() userData->mLocationType =%d",userData->mLocationType);
    // if(!userData->mGpsON) {
    //     WS_PRINTF("GPS is OFF, skip GPS handling");
    //     return;
    // }
	Flw_StartGps(userData);
    SetGpsFixCb(Flw_gps_fix_callback_func);

	if(sCommTimer == NULL)
	{
		uos_timer_create(&sCommTimer);
	}
	uos_timer_stop(sCommTimer);

	if((userData->mLocationType != LOCATION_TYPE_PERIOD) || GetGpsHaveFixed()) 
	{
    	uos_timer_start(sCommTimer, TICKES_IN_SECOND * 50, 0, Flw_JudgeGpsOvertime, 0);
		WS_PRINTF("Flw_GpsHandle() HOT START\r\n");
	}
    else 
    {
    	#if USE_LV_WATCH_PLATFORM_QUANTONG != 0 
		if(ke_GetQuantongVerFlag() != TY_VER)
		{
			//zhejiang guizhou wenzhou cold gps time is 40s
			uos_timer_start(sCommTimer, TICKES_IN_SECOND * 40, 0, Flw_JudgeGpsOvertime, 0);
		}
		else
		#endif
    	uos_timer_start(sCommTimer, TICKES_IN_SECOND * 70, 0, Flw_JudgeGpsOvertime, 0);
		WS_PRINTF("Flw_GpsHandle() COLD START\r\n");
    }
}
#endif

bool g_ke_station_change_flag = false;	

static bool Flw_StationIsChange(ws_location *LastLocationIn, ws_location *CurrLocationIn)
{
	int i = 0;
	int n = 0;
	int result = 1;
	int same_num = 0;

	g_ke_station_change_flag = false;
	
	if((LastLocationIn == NULL) || (CurrLocationIn == NULL))
	{
		return false;
	}

	if(CurrLocationIn->cellType ==  WS_CELL_TYPE_GSM)
	{
		WS_PRINTF("WS_CELL_TYPE_GSM cells[0].lac is %d %d cell_id is %d %d",LastLocationIn->gsm.cells[0].lac,
		CurrLocationIn->gsm.cells[0].lac, LastLocationIn->gsm.cells[0].cell_id, CurrLocationIn->gsm.cells[0].cell_id);
		
		if((LastLocationIn->gsm.cells[0].lac != CurrLocationIn->gsm.cells[0].lac) ||
		(LastLocationIn->gsm.cells[0].cell_id != CurrLocationIn->gsm.cells[0].cell_id))
		{
		
			return true;
		}
	
	}
	else if(CurrLocationIn->cellType ==  WS_CELL_TYPE_LTE)
	{
		WS_PRINTF("WS_CELL_TYPE_LTE tacc is %d  %d cellid is %d %d",
		LastLocationIn->lte.scell.tac, CurrLocationIn->lte.scell.tac,
		LastLocationIn->lte.scell.cellid, CurrLocationIn->lte.scell.cellid);

		if((LastLocationIn->lte.scell.tac != CurrLocationIn->lte.scell.tac) ||
		(LastLocationIn->lte.scell.cellid != CurrLocationIn->lte.scell.cellid))
		{
			i = 0;
			WS_PRINTF("WS_CELL_TYPE_LTE LastLocationIn->lte.num = %d\r\n",LastLocationIn->lte.num);
			while(i<LastLocationIn->lte.num)
			{
				WS_PRINTF("CurrLocationIn is in LastLocationIn cellid is %d  %d",
				CurrLocationIn->lte.scell.cellid, LastLocationIn->lte.cells[i].cellid);
				if(CurrLocationIn->lte.scell.cellid == LastLocationIn->lte.cells[i].cellid)
				{
					WS_PRINTF("CurrLocationIn is in LastLocationIn\n");
					result = 0;
					break;
				}
				i++;
			}
			i = 0;
			WS_PRINTF("CurrLocationIn is in LastLocationIn result = %d\n", result);
			if(result == 1)
			{
				while(i < CurrLocationIn->lte.num)
				{
					n = 0;
					while(n < LastLocationIn->lte.num)
					{
						if(CurrLocationIn->lte.cells[i].cellid == LastLocationIn->lte.cells[n].cellid)
						{
							WS_PRINTF("CurrLocationIn no same cellid = %d\n",
								CurrLocationIn->lte.cells[i].cellid);
							same_num++;
						}
						n++;
					}
					i++;
				}

				WS_PRINTF("CurrLocationIn is in LastLocationIn cells same_num = %d, %d, %d\n", same_num, CurrLocationIn->lte.num, LastLocationIn->lte.num);
				if(same_num < 1)
				{
					g_ke_station_change_flag = true;
				}
				return true;
			}
		}		
	}
	
	return false;
}

void Flw_JudgeLocationTypeHandle(void *pUser, uint32_t par)
{
	WS_PRINTF("CWatchService_GetStationInfoCB_All()");
	ws_client * pme = (ws_client*)pUser;	
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    ws_location* loc_data = &userData->m_base.location;
	
#if EVERY_TIME_UP_LBS != 0
	Flw_SendMsgUpLocation(LOCATION_UP_TYPE_LBS);
	userData->m_base.cellInfoCB = NULL;
    userData->m_base.cellInfoCBPar = 0;
    return;	
#elif EVERY_TIME_GPS_ON != 0 && USE_CRANE_WATCH_GPS != 0
	Flw_GpsHandle();
#elif USE_LV_WATCH_GPS_TEST_BY_TOOL != 0
	Flw_GpsHandle();
#elif EVERY_TIME_WIFI_ON != 0
	Flw_SendMsgWifiHandle(STRAT_WIFI_TPYE_GPS_NO_STARTED);
#else	

#if USE_LV_WATCH_SPAIN != 0
	if(userData->mGpsON == 1)
#else
  	if(Flw_StationIsChange(&(userData->mHisLocationInfo.mLastLocationIn),loc_data) == true  //基站变化	 
	#if defined(__XF_PRO_A16__)
		&& Flw_IsActiveValid() == true
	#endif)
#endif
	{
	#if USE_CRANE_WATCH_GPS != 0 || USE_LV_WATCH_SPAIN != 0
	if(userData->mGpsON)
	{	
		Flw_GpsHandle(); 
	}
	#endif
		ke_clear_active(); // clear active
	}
	else
	{
		WS_PRINTF("LBS is not changed.g_ke_station_change_flag = %d",g_ke_station_change_flag);
		if(true == Flw_IsActiveValid())
		{
			WS_PRINTF("CWatchService_GetStationInfoCB_All(),LBS is not changed but Active! start wifi");
			//开启wifi
			Flw_SendMsgWifiHandle(STRAT_WIFI_TPYE_GPS_NO_STARTED);
		}
		else
		{
			WS_PRINTF("CWatchService_GetStationInfoCB_All(),LBS is not changed and not Active send last position");
			Flw_SendMsgUpLocation(LOCATION_UP_TYPE_LAST);
		}
	}
#endif
    userData->m_base.cellInfoCB = NULL;
    userData->m_base.cellInfoCBPar = 0;
}

void Flw_JudgeLocationWifiHandle(void *pUser, uint32_t par)
{
	WS_PRINTF("CWatchService_GetStationInfoCB_All()");
	ws_client * pme = (ws_client*)pUser;	
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    ws_location* loc_data = &userData->m_base.location;
	
#if USE_LV_WATCH_SPAIN != 0
	Flw_SendMsgWifiHandle(STRAT_WIFI_TPYE_GPS_NO_STARTED);
#else
	if(Flw_StationIsChange(&(userData->mHisLocationInfo.mLastLocationIn),loc_data) == true) 
	{
		Flw_SendMsgWifiHandle(STRAT_WIFI_TPYE_GPS_NO_STARTED);
		ke_clear_active(); // clear active
	}
	else if(true == Flw_IsActiveValid())
	{
		WS_PRINTF("CWatchService_GetStationInfoCB_All(),LBS is not changed but Active! start wifi");
		//开启wifi
		Flw_SendMsgWifiHandle(STRAT_WIFI_TPYE_GPS_NO_STARTED);
	}
	else
	{
		WS_PRINTF("CWatchService_GetStationInfoCB_All(),LBS is not changed and not Active send last position");
		Flw_SendMsgUpLocation(LOCATION_UP_TYPE_LAST);
	}
#endif
    userData->m_base.cellInfoCB = NULL;
    userData->m_base.cellInfoCBPar = 0;
}

void Flw_JudgeLocationPrityWifiHandle(void *pUser, uint32_t par)
{
	WS_PRINTF("CWatchService_GetStationInfoCB_All()");

	ws_client * pme = (ws_client*)pUser;	
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    ws_location* loc_data = &userData->m_base.location;

	#if USE_LV_SHORTEN_BASE_STATION_SEARCH_TIME != 0
	if(loc_data->data.lte.scell.cellid > 0 && loc_data->data.lte.scell.tac > 0)
	{
		CWatchService_Search_CellID(1);
	}
	#endif

	Flw_SendMsgWifiHandle(STRAT_WIFI_TPYE_GPS_NO_STARTED);
	
    userData->m_base.cellInfoCB = NULL;
    userData->m_base.cellInfoCBPar = 0;
}

void Flw_lte_station_info_ind(app_adp_lte_cells_t *ltecell_list)
{
    ws_client *pme = MMI_ModemAdp_WS_GetClient();
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    ws_location  *location = &userData->m_base.location ;
    pfnCellInfo callback = (pfnCellInfo)userData->m_base.cellInfoCB;
    //app_adp_reg_and_home_plmn_info_t * RegHomePlmn = NULL;

    location->cellType = WS_CELL_TYPE_LTE;
    location->lte.scell.mcc = ltecell_list->scell.mcc;
    location->lte.scell.mnc = ltecell_list->scell.mnc;
    location->lte.scell.tac = ltecell_list->scell.tac;
    location->lte.scell.cellid = ltecell_list->scell.cellid;
    location->lte.scell.rsrp = ltecell_list->scell.rsrp;
    location->lte.scell.rsrq = ltecell_list->scell.rsrq;
    //RegHomePlmn = app_adaptor_get_plmn_info_req();
    location->lte.scell.rxlev = convertRsrpToDbm(ltecell_list->scell.rsrp);  //RegHomePlmn->realdbm;
    //lv_mem_free(RegHomePlmn);

	WS_PRINTF("lte_cell_info_ind()mcc=%d, mnc=%d,tac=%d, euarfcn=%d, cell_id=%d, rsrp=%d, rsrq=%d, rxlev=%d\n",
                ltecell_list->scell.mcc,ltecell_list->scell.mnc,ltecell_list->scell.tac,
                ltecell_list->scell.euarfcn,ltecell_list->scell.cellid,ltecell_list->scell.rsrp,ltecell_list->scell.rsrq,location->lte.scell.rxlev);


    location->lte.num = ltecell_list->encell_num;
	WS_PRINTF("lte_cell_info_ind()lte.num=%d\n", ltecell_list->encell_num);
    
    for(int i=0; i<ltecell_list->encell_num; i++)
    {
        location->lte.cells[i].mcc = ltecell_list->encell[i].mcc;

		location->lte.cells[i].mcc = ltecell_list->encell[i].mcc;
		location->lte.cells[i].mnc = ltecell_list->encell[i].mnc;
		location->lte.cells[i].tac = ltecell_list->encell[i].tac;
		location->lte.cells[i].index = ltecell_list->encell[i].index;
        location->lte.cells[i].cellid = ltecell_list->encell[i].cellid;
        location->lte.cells[i].rsrp = ltecell_list->encell[i].rsrp;
        location->lte.cells[i].rsrq = ltecell_list->encell[i].rsrq;
		location->lte.cells[i].rssi = convertRsrpToDbm(ltecell_list->encell[i].rsrp);


        WS_PRINTF("lte_cell_info_ind()[%d]index=%d,euarfcn=%d,cellid=%d,rsrp=%d,rssi=%d,rxlev=%d\n", i,
                    ltecell_list->encell[i].index,ltecell_list->encell[i].euarfcn,ltecell_list->encell[i].cellid,
                    ltecell_list->encell[i].rsrp,location->lte.cells[i].rssi,ltecell_list->encell[i].rxlev);
    }

	WS_PRINTF("lte_cell_info_ind()gsm.num=%d\n", ltecell_list->gncell_num);
    for(int i=0; i<ltecell_list->gncell_num; i++)
    {
    	WS_PRINTF("lte_cell_info_ind()[%d]mcc=%d,mnc=%d,lac=%d,arfcn=%d,bsic=%d,cell_id=%d,rxlev=%d\n", i, 
                    ltecell_list->gncell[i].mcc,ltecell_list->gncell[i].mnc,ltecell_list->gncell[i].lac,
                    ltecell_list->gncell[i].arfcn,ltecell_list->gncell[i].bsic,ltecell_list->gncell[i].cell_id,ltecell_list->gncell[i].rxlev);
    }

	if(callback)
	{
		//wait wifi result!
		if(ltecell_list->encell_num<2 && userData->m_base.cellsGetTimes<5){
			WS_PRINTF("%s times = %d", __FUNCTION__, userData->m_base.cellsGetTimes);
			app_adaptor_set_ncell_bch_req(1);
			userData->m_base.cellsGetTimes++;
		}else{
			callback(userData->m_base.client, userData->m_base.cellInfoCBPar);
			userData->m_base.cellInfoCB = NULL;
			userData->m_base.cellInfoCBPar = NULL;
			userData->m_base.cellsGetTimes = 0;
			lv_mem_free(userData->m_base.location.wifi);
			userData->m_base.location.wifi = NULL;
		}
	}
	lv_mem_free(ltecell_list);

}

void Flw_gsm_station_info_ind(app_adp_gsm_cells_t *gsmcell_list)
{
    ws_client *pme = MMI_ModemAdp_WS_GetClient();
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    ws_location  *location = &userData->m_base.location ;
    pfnCellInfo callback = (pfnCellInfo)userData->m_base.cellInfoCB;
    int i = 0;

    location->cellType = WS_CELL_TYPE_GSM;
    location->gsm.num = 1 + gsmcell_list->gncell_num;
	WS_PRINTF("gsm_cell_info_ind()gsm.num=%d\n", gsmcell_list->gncell_num);

    location->gsm.cells[i].mcc = gsmcell_list->scell.mcc;
    location->gsm.cells[i].mnc = gsmcell_list->scell.mnc;
    location->gsm.cells[i].lac = gsmcell_list->scell.lac;
    location->gsm.cells[i].arfcn = gsmcell_list->scell.arfcn;
    location->gsm.cells[i].bsic = gsmcell_list->scell.bsic;
    location->gsm.cells[i].cell_id = gsmcell_list->scell.cell_id;
    location->gsm.cells[i].rxlev = gsmcell_list->scell.rxlev;
	WS_PRINTF("gsm_cell_info_ind()mcc=%d,mnc=%d,lac=%d,arfcn=%d,bsic=%d,cell_id=%d,rxlev=%d\n", 
                gsmcell_list->scell.mcc,gsmcell_list->scell.mnc,gsmcell_list->scell.lac,
                gsmcell_list->scell.arfcn,gsmcell_list->scell.bsic,gsmcell_list->scell.cell_id,gsmcell_list->scell.rxlev);

    for(i=0; i<gsmcell_list->gncell_num; i++)
    {
        location->gsm.cells[i+1].mcc = gsmcell_list->gncell[i].mcc;
        location->gsm.cells[i+1].mnc = gsmcell_list->gncell[i].mnc;
        location->gsm.cells[i+1].lac = gsmcell_list->gncell[i].lac;
        location->gsm.cells[i+1].arfcn = gsmcell_list->gncell[i].arfcn;
        location->gsm.cells[i+1].bsic = gsmcell_list->gncell[i].bsic;
        location->gsm.cells[i+1].cell_id = gsmcell_list->gncell[i].cell_id;
        location->gsm.cells[i+1].rxlev = gsmcell_list->gncell[i].rxlev;
        
        WS_PRINTF("gsm_cell_info_ind()[%d]mcc=%d,mnc=%d,lac=%d,arfcn=%d,bsic=%d,cell_id=%d,rxlev=%d\n", i,
                    gsmcell_list->gncell[i].mcc,gsmcell_list->gncell[i].mnc,gsmcell_list->gncell[i].lac,
                    gsmcell_list->gncell[i].arfcn,gsmcell_list->gncell[i].bsic,gsmcell_list->gncell[i].cell_id,gsmcell_list->gncell[i].rxlev);
        

    }
   
    lv_mem_free(gsmcell_list);
    if(callback)
    {
		//wait wifi result!
        callback(userData->m_base.client, userData->m_base.cellInfoCBPar);
    }
}

static char Flw_IsSameWifi(char *LastWifiMac,char *ThisWifiMac)
{
	if((LastWifiMac == NULL) || (ThisWifiMac == NULL))
	{
		return 0;
	}
	if(strcmp(LastWifiMac, ThisWifiMac) == 0)
	{
		return 1;
	}
	return 0;
}

static bool Flw_CompareWifi(ws_wifi_info *LastWifi,ws_wifi_info *ThisWifi)
{	
	char Cnt = 0;

	if((LastWifi == NULL) || (ThisWifi == NULL))
	{
		return false;
	}
	if(LastWifi->wifi_count < WIFI_UP_MIN_CNT)
	{
		return false;
	}
	WS_PRINTF("Flw_CompareWifi this_cnt = %d, last_cnt = %d\n", ThisWifi->wifi_count, LastWifi->wifi_count);

	for(int i = 0; i < ThisWifi->wifi_count; i++)
	{
		for(int k = 0; k <  LastWifi->wifi_count; k++)
		{
			if(Flw_IsSameWifi(LastWifi->wifs[k].mac, ThisWifi->wifs[i].mac) == 1)
			{
				Cnt++;				
			}
			//WS_PRINTF("LastWifi->wifs[%d].mac is %s, ThisWifi->wifs[%d].mac is %s cnt is %d",k,LastWifi->wifs[k].mac,i,ThisWifi->wifs[i].mac,Cnt);
		}
	}
	WS_PRINTF("Flw_CompareWifi cnt = %d\n", Cnt);
	if(Cnt >= (ThisWifi->wifi_count/2))
	{
		return true;
	}
	return false;
	
}

static void Flw_CommonTimerCB(uint32_t pUser)
{
   ws_client * pme = (ws_client*)pUser;    
   ws_evt_msg_t  *cmd_info = (ws_evt_msg_t*)malloc(sizeof(ws_evt_msg_t));  /*??????*/
   if(cmd_info)
   {
	   memset((void *)cmd_info, 0x00, sizeof(ws_evt_msg_t));
	   cmd_info->client = pme;
	   cmd_info->evt = CMD_LOCAL_COMMON_TIMER_EXPIRE;
	   CWatchService_NofityClient(pme, cmd_info);
	   free(cmd_info);
   	}		
}

void Flw_NotPeridLocatinConfig(LOCATION_TYPE LocationType)
{
	ws_client *pme = MMI_ModemAdp_WS_GetClient();
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	userData->mPreSendLocCnt = 1;
	// userData->m_base.m_nLastTimeForUploadLoc = 0;
	userData->mLocationType = LocationType;
#if 0
        Flw_JudgeLocationType();
#else	
	#if USE_LV_WATCH_GPS_TEST_BY_TOOL != 1
	#if USE_LV_WATCH_SPAIN != 0
	if(LocationType==LOCATION_TYPE_IMMEDIATE || LocationType==LOCATION_TYPE_SOS || LocationType > LOCATION_TYPE_FENCE)
	#else
	if(LocationType==LOCATION_TYPE_IMMEDIATE || LocationType==LOCATION_TYPE_SOS)
	#endif
	{
    #if USE_LV_WATCH_LOCATION_BLEMAC != 0
		if(wsMuteTime_CheckNow(NULL,NULL))
			Open_bluetooth_preper_Location(Flw_JudgeFirstWifiLocationType);
		else
    #endif
	#if USE_LV_WATCH_SPAIN != 0
		if(userData->mGpsON == 1)
		{
			Flw_JudgeLocationType();
		}
		else
	#endif
	    { 
			Flw_JudgeFirstWifiLocationType();
		}
	}
	else
	#endif
	{
    #if USE_LV_WATCH_LOCATION_BLEMAC != 0
		if(wsMuteTime_CheckNow(NULL,NULL))
			Open_bluetooth_preper_Location(Flw_JudgeLocationType);
		else
	#endif
		{
			Flw_JudgeLocationType();
		}
	}
#endif	
	WS_PRINTF("Flw_NotPeridLocatinConfig() is LocationType is %d",LocationType);
	// uos_timer_stop(pme->adapter->timer_common);
    // uos_timer_start(pme->adapter->timer_common, 20*TICKES_IN_SECOND, 30*TICKES_IN_SECOND, Flw_CommonTimerCB, (uint32_t)pme);
}

void Flw_WifiLocatinConfig(LOCATION_TYPE LocationType)
{
	ws_client *pme = MMI_ModemAdp_WS_GetClient();
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	userData->mPreSendLocCnt = 1;
	userData->m_base.m_nLastTimeForUploadLoc = 0;
	userData->mLocationType = LocationType;

	Flw_JudgeWifiLocationType();

	
	WS_PRINTF("Flw_NotPeridLocatinConfig() is LocationType is %d",LocationType);
	uos_timer_stop(pme->adapter->timer_common);
    uos_timer_start(pme->adapter->timer_common, 20*TICKES_IN_SECOND, 30*TICKES_IN_SECOND, Flw_CommonTimerCB, (uint32_t)pme);
}

static bool Flw_UpdateStation(ws_location *LastLocationIn, ws_location *CurrLocationIn)
{
	uint8_t i=0;
	if((LastLocationIn == NULL) || (CurrLocationIn == NULL))
	{
		return false;
	}
	
	if(CurrLocationIn->cellType ==  WS_CELL_TYPE_GSM)
	{
		LastLocationIn->cellType = WS_CELL_TYPE_GSM;				
		LastLocationIn->gsm.cells[0].lac = CurrLocationIn->gsm.cells[0].lac;
		LastLocationIn->gsm.cells[0].cell_id = CurrLocationIn->gsm.cells[0].cell_id;
		return true;			
	}
	else if(CurrLocationIn->cellType ==  WS_CELL_TYPE_LTE)
	{		
		LastLocationIn->cellType = WS_CELL_TYPE_LTE;	
		LastLocationIn->lte.scell.mcc = CurrLocationIn->lte.scell.mcc;
		LastLocationIn->lte.scell.mnc = CurrLocationIn->lte.scell.mnc;		
		LastLocationIn->lte.scell.tac = CurrLocationIn->lte.scell.tac;
		LastLocationIn->lte.scell.cellid = CurrLocationIn->lte.scell.cellid;
		LastLocationIn->lte.scell.rxlev = CurrLocationIn->lte.scell.rxlev;
		LastLocationIn->lte.num = CurrLocationIn->lte.num;
		for(i=0;i<CurrLocationIn->lte.num;i++)
		{
			if(CurrLocationIn->lte.cells[i].cellid!=0)
			{
				LastLocationIn->lte.cells[i].mcc = CurrLocationIn->lte.cells[i].mcc;
				LastLocationIn->lte.cells[i].mnc= CurrLocationIn->lte.cells[i].mnc;
				LastLocationIn->lte.cells[i].cellid = CurrLocationIn->lte.cells[i].cellid;
				LastLocationIn->lte.cells[i].euarfcn = CurrLocationIn->lte.cells[i].euarfcn;
				LastLocationIn->lte.cells[i].tac= CurrLocationIn->lte.cells[i].tac;
				LastLocationIn->lte.cells[i].rssi= CurrLocationIn->lte.cells[i].rssi;
			}

		}
		return true;				
	}
	return false;
}

void Flw_UpdateLactionData(ws_kaer_t *userData, ws_location *locationIn)
{
	userData->mHisLocationInfo.mLastUpStepCnt = mmi_get_steps_count();
	userData->mPreSendLocCnt = 0;
#if USE_LV_WATCH_SPAIN == 0
	userData->mGpsON = 0;
#endif
	userData->mLocationType = LOCATION_TYPE_PERIOD;
	Flw_UpdateStation(&(userData->mHisLocationInfo.mLastLocationIn),locationIn);
	WS_PRINTF("Flw_UpdateLactionData() mHisLocationInfo.mLastUpStepCnt IS %d ",userData->mHisLocationInfo.mLastUpStepCnt);
}

static void Flw_JudgeWifi(void *pUser, uint32_t par)
{
	ws_client * pme = (ws_client*)pUser;	
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    ws_location* loc_data = &userData->m_base.location;

    ws_wifi_info *wifi = userData->m_base.location.wifi;
    if(wifi && wifi->wifi_count < WIFI_UP_MIN_CNT)
	{
		if(par & STRAT_WIFI_TPYE_GPS_NO_STARTED)  //没有开启gps 开启WiFi
		{
#if USE_CRANE_WATCH_GPS != 0 || USE_LV_WATCH_SPAIN != 0
			WS_PRINTF("Flw_JudgeWifi() wifi->wifi_count < 3 start gps");
			//开启gps
			if(userData->mGpsON == 1){
				Flw_GpsHandle();
			}else if(wifi_scan_retry_count < WIFI_SCAN_MAX_RETRY) {
                wifi_scan_retry_count++;
                WS_PRINTF("[WIFI_DEBUG] Flw_JudgeWifi() retry wifi scan, count=%d/%d", wifi_scan_retry_count, WIFI_SCAN_MAX_RETRY);
                WS_PRINTF("Flw_JudgeWifi() retry wifi scan, count=%d", wifi_scan_retry_count);
                Flw_SendMsgWifiHandle(STRAT_WIFI_TPYE_GPS_NO_STARTED);
            }
			else
			{
				wifi_scan_retry_count = 0;
				WS_PRINTF("[WIFI_DEBUG] Flw_JudgeWifi() max retries reached, giving up WiFi scan");
				WS_PRINTF("Flw_JudgeWifi()only wifi is null, LOCATION_UP_TYPE_LBS");
				//只上报基站
				Flw_SendMsgUpLocation(LOCATION_UP_TYPE_WIFI);
			}
			
#endif			
		}
		else
		{
			WS_PRINTF("[WIFI_DEBUG] WiFi scan successful, resetting retry count (was %d)", wifi_scan_retry_count);
			wifi_scan_retry_count = 0;
	 		ws_location* loc_data = &userData->m_base.location;
	 		if(((mmi_get_steps_count() - userData->mHisLocationInfo.mLastUpStepCnt) < NO_GPS_WIFI_STEP_CNT)  //步数小于400
	 		&&(Flw_StationIsChange(&(userData->mHisLocationInfo.mLastLocationIn),loc_data) == false))  //基站没有变化
		 	{
		 		WS_PRINTF("Flw_JudgeWifi() wifi->wifi_count < 3 step < 400 up last location");
		 		//上报上一次定
		 		Flw_SendMsgUpLocation(LOCATION_UP_TYPE_WIFI);
		 		
		 	}
		 	else
		 	{
		 		WS_PRINTF("Flw_JudgeWifi() wifi->wifi_count < 3 step > 400 up lbs");
				/* if now is 4G station and rxlev is less than -115(means rssi is very low), we upload last time loaction */
				if(loc_data->cellType == WS_CELL_TYPE_LTE
					&& loc_data->lte.scell.rxlev < -115
					)
				{
					Flw_SendMsgUpLocation( LOCATION_UP_TYPE_WIFI);
				}
				else
				{
		 		//上报lbs
		 		Flw_SendMsgUpLocation(LOCATION_UP_TYPE_WIFI);

				memset(&userData->mHisLocationInfo.mLastUpWifi, 0, sizeof(ws_wifi_info));
				}	
		 	}
    	}
	 }
	 #if 1
	else if((Flw_CompareWifi(&(userData->mHisLocationInfo.mLastUpWifi), wifi) == true))
	 {
		
	 	WS_PRINTF("Flw_JudgeWifi() wifi is the same up last location");
	 	Flw_SendMsgUpLocation(LOCATION_UP_TYPE_WIFI); 
	 }
	 #endif
	 else if(wifi&&wifi->wifi_count >= WIFI_UP_MIN_CNT)
	 {
	 	WS_PRINTF("Flw_JudgeWifi() wifi is not the same up wifi");
	 	//上报本次
	 	Flw_SendMsgUpLocation(LOCATION_UP_TYPE_WIFI);
	 }

}

void Flw_Wifi_Scan_Cb_Ext(uint32_t pa, int32_t  pb,void *pc)
{
	MMI_Modem_Wifi_Ctrl_Req(0, 0, 0, 5, 0);
	int n = 0;
	app_adp_wifi_result_t result = pa;
	app_adp_wifi_ap_list * ap_list = pc;

    ws_client *pme = MMI_ModemAdp_WS_GetClient();
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    pfnCellInfo callback = (pfnCellInfo)userData->m_base.cellInfoCB;
    ws_wifi_info *wifi = userData->m_base.location.wifi;

	// 添加详细的扫描结果调试日志
	WS_PRINTF("[WIFI_DEBUG] %s() result=%d, ap_count=%d, retry_count=%d",
	          __FUNCTION__, result, ap_list ? ap_list->count : 0, wifi_scan_retry_count);

	if(ap_list && ap_list->count > 0) {
		WS_PRINTF("[WIFI_DEBUG] Found %d APs:", ap_list->count);
		for(int i = 0; i < ap_list->count && i < 10; i++) { // 限制输出前10个AP避免日志过多
			WS_PRINTF("[WIFI_DEBUG] AP[%d]: MAC=%02x:%02x:%02x:%02x:%02x:%02x, RSSI=%d",
			          i, ap_list->item[i].mac[0], ap_list->item[i].mac[1],
			          ap_list->item[i].mac[2], ap_list->item[i].mac[3],
			          ap_list->item[i].mac[4], ap_list->item[i].mac[5],
			          ap_list->item[i].rssi);
		}
		if(ap_list->count > 10) {
			WS_PRINTF("[WIFI_DEBUG] ... and %d more APs", ap_list->count - 10);
		}
	} else {
		WS_PRINTF("[WIFI_DEBUG] No APs found or scan failed");
	}

	WS_PRINTF("%s() result=%d, %d\n", __FUNCTION__, result, ap_list->count);

	if(wifi == NULL)
    {
        wifi = userData->m_base.location.wifi = (ws_wifi_info*)lv_mem_alloc(sizeof(ws_wifi_info));
    }
	
    if(APP_ADP_WIFI_RESULT_FAILURE != result && ap_list && ap_list->count>0)
    {
        
		memset(wifi, 0, sizeof(ws_wifi_info));

        wifi->wifi_count = ap_list->count;
        
        for(int i=0; i<ap_list->count; i++)
        {
        	if((ap_list->item[i].mac[0]==0)&&(ap_list->item[i].mac[1]==0)&&(ap_list->item[i].mac[2]==0)
				&&(ap_list->item[i].mac[3]==0)&&(ap_list->item[i].mac[4]==0)&&(ap_list->item[i].mac[5]==0))
        		{
        			wifi->wifi_count--;
					continue;
        		}
			if((ap_list->item[i].mac[0]==0xFF)&&(ap_list->item[i].mac[1]==0xFF)&&(ap_list->item[i].mac[2]==0xFF)
				&&(ap_list->item[i].mac[3]==0xFF)&&(ap_list->item[i].mac[4]==0xFF)&&(ap_list->item[i].mac[5]==0xFF))
				{
        			wifi->wifi_count--;
					continue;
        		}
            wifi->wifs[n].ap_name[0]=0;
            sprintf(wifi->wifs[n].rssi, "%d", ap_list->item[i].rssi);
            sprintf(wifi->wifs[n].mac, "%02x:%02x:%02x:%02x:%02x:%02x", 
                    ap_list->item[i].mac[0],ap_list->item[i].mac[1],ap_list->item[i].mac[2],
                    ap_list->item[i].mac[3],ap_list->item[i].mac[4],ap_list->item[i].mac[5]);      
            WS_PRINTF("%s() wifi[%d]rssi!!!!=%s, mac=%s\n", __FUNCTION__, i, wifi->wifs[n].rssi,wifi->wifs[n].mac);
					
			n++;
        }
		
        Hal_Mem_Free(ap_list);
    }
	else
	{
		if(wifi)
			wifi->wifi_count = 0;
		if(ap_list)
			Hal_Mem_Free(ap_list);
	}

	if(callback)
	{
		ws_printf("okokokook 111 !!!!!!!!!!!!Flw_Wifi_Scan_Cb_Ext() wifi_count = %d\n", wifi->wifi_count);
		callback(userData->m_base.client, userData->m_base.cellInfoCBPar);
	}
	else
	{
		ws_printf("okokokook 222 !!!!!!!!!!!!Flw_Wifi_Scan_Cb_Ext() wifi_count = %d\n", wifi->wifi_count);
		Flw_JudgeWifi(userData->m_base.client, userData->m_base.cellInfoCBPar);
	}
	userData->m_base.cellInfoCB = NULL;
    userData->m_base.cellInfoCBPar = 0;
}

void Flw_Wifi_Scan_Cb(app_adp_wifi_result_t result, app_adp_wifi_ap_list * ap_list)
{
	WS_PRINTF("Flw_Wifi_Scan_Cb()");
	MMI_ModemAdp_Rpc_Req(Flw_Wifi_Scan_Cb_Ext, result, 0, ap_list);
}

//获取Wifi信息
static void Flw_GetWifiInfo(ws_client *pme, void* callback, uint32_t par)
{
	if(pme == NULL)
	{
		return;
	}
	WS_PRINTF("%s() line:%d\n", __FUNCTION__, __LINE__);
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    if((userData->m_base.cellInfoCB != NULL) && (userData->m_base.cellInfoCBPar != 0))
    {
        userData->m_base.cellInfoCBPar |= par;
        WS_PRINTF("[WIFI_DEBUG] WiFi scan already in progress, combining parameters");
        return;
    }

    userData->m_base.cellInfoCB = callback;
    userData->m_base.cellInfoCBPar = par;

    // 添加扫描参数调试日志
	#if USE_LV_WATCH_YIXIN !=0
		WS_PRINTF("[WIFI_DEBUG] Starting WiFi scan (YIXIN): rounds=3, max_hotspots=20, timeout=%d, priority=0", WIFI_OVER_TIME);
		app_adaptor_wifi_scan(3, 20, WIFI_OVER_TIME, 0, Flw_Wifi_Scan_Cb);
	#else
		WS_PRINTF("[WIFI_DEBUG] Starting WiFi scan (Normal): rounds=2, max_hotspots=8, timeout=%d, priority=0", WIFI_OVER_TIME);
		app_adaptor_wifi_scan(2, 8, WIFI_OVER_TIME, 0, Flw_Wifi_Scan_Cb);
	#endif
}

//开启wifi
void Flw_WifiHandle(START_WIFI_TPYE Tpye)
{
	ws_client *pme = MMI_ModemAdp_WS_GetClient();

	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	WS_PRINTF("%s() line:%d\n", __FUNCTION__, __LINE__);
#if USE_LV_WATCH_EXTRA_WIFI != 0
	if(Tpye == STRAT_WIFI_TPYE_EXPRA_WIFI_STARTED)
	{
		app_adaptor_wifi_scan(2, 8, WIFI_OVER_TIME, 0, Flw_Wifi_Scan_Cb);
	}
	else
#endif
	{
		Flw_GetWifiInfo(pme, Flw_JudgeWifi, Tpye);
	}

}
//获取基站信息
void Flw_GetStationInfo(ws_client *pme, void* callback, uint32_t par)
{
	WS_PRINTF("%s() line:%d\n", __FUNCTION__, __LINE__);
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;

    if((userData->m_base.cellInfoCB != NULL) && (userData->m_base.cellInfoCBPar != 0))
    {
        userData->m_base.cellInfoCBPar |= par;
        return;
    }
    
    userData->m_base.cellInfoCB = callback;
    userData->m_base.cellInfoCBPar = par;
	
    app_adaptor_lte_cell_info_ind_bind(Flw_lte_station_info_ind);
    app_adaptor_gsm_cell_info_ind_bind(Flw_gsm_station_info_ind);
    //app_adaptor_get_cell_info_req(GET_CELL_INFO_ONCE_MODE);
    app_adaptor_set_ncell_bch_req(1);
}

//判定定位类型
void Flw_JudgeLocationType(void)
{
	WS_PRINTF("%s() line:%d\n", __FUNCTION__, __LINE__);
	ws_client *pme = MMI_ModemAdp_WS_GetClient();
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	userData->mFisrtOPenWifiFlg = 0;
	Flw_GetStationInfo(pme, Flw_JudgeLocationTypeHandle, 0);  //获取基站，并比较基站信息	
}

//wifi location use
void Flw_JudgeWifiLocationType(void)
{
	WS_PRINTF("%s() line:%d\n", __FUNCTION__, __LINE__);
	ws_client *pme = MMI_ModemAdp_WS_GetClient();
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	userData->mFisrtOPenWifiFlg = 0;
	Flw_GetStationInfo(pme, Flw_JudgeLocationWifiHandle, 0);  
}


void Flw_JudgeFirstWifiLocationType(void)
{
	WS_PRINTF("%s() line:%d\n", __FUNCTION__, __LINE__);
	ws_client *pme = MMI_ModemAdp_WS_GetClient();
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	userData->mFisrtOPenWifiFlg = 0;
	Flw_GetStationInfo(pme, Flw_JudgeLocationPrityWifiHandle, 0); 
}

void Flw_InitLocationData(void)
{
	ws_client *pme = MMI_ModemAdp_WS_GetClient();

	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	
	userData->mLocationType = LOCATION_TYPE_PERIOD;
	userData->mLocationRspFlg = 1;
	userData->mPreSendLocCnt = 0;
}

#if USE_LV_WATCH_EXTRA_WIFI != 0 
char Flw_ClearExpraWifiInfo(char index)
{
	ws_printf("enter %s index is %d",__FUNCTION__,index);
	if(index >= MAX_EXT_LOC_UP_NUM)
		return;

	sExtraLocUpInfor[index].mValid = FALSE;
}

char Flw_GetExpraWifi(ExtraLocationUp * extra_loc_data)
{
	char i;
	
	for(i=0; i<MAX_EXT_LOC_UP_NUM; i++)
	{
		if(sExtraLocUpInfor[i].mValid == TRUE)
		{
			break;
		}
	}

	if(i >= MAX_EXT_LOC_UP_NUM)
	{	
		return MAX_EXT_LOC_UP_NUM;
	}	
	else
	{
		*extra_loc_data = sExtraLocUpInfor[i];
		WS_PRINTF("%s()  EXTRA LOCATION %d IS VALID \n", __FUNCTION__,i);
		return i;
	}
}


void Flw_ExpraWiFi_Hanldle(ws_client *user_client,ws_wifi_info *wifi_info)
{
	char i,n;
	ws_client * pme = user_client;	
	ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
	ws_location  *location = &userData->m_base.location ;

	if(pme == NULL)
	{
		WS_PRINTF("%s() ERROR!! PME IS NULL \n", __FUNCTION__);
		return;
	}
	
	for(i=0; i<MAX_EXT_LOC_UP_NUM; i++)
	{
		if(sExtraLocUpInfor[i].mValid == FALSE)
		{
			break;
		}
	}

	if(i >= MAX_EXT_LOC_UP_NUM)
	{
		WS_PRINTF("%s() ERROR!! EXTRA LOCATION IS FULL \n", __FUNCTION__);
	}
	else
	{
		WS_PRINTF("%s() sExtraLocUpInfor[%d] is free \n", __FUNCTION__,i);
		
		/* 1.WIFI 信息 */
		sExtraLocUpInfor[i].mWifi = *wifi_info;

		WS_PRINTF("%s() sExtraLocUpInfor.wifi_count=%d\n",__FUNCTION__,sExtraLocUpInfor[i].mWifi.wifi_count);
		for(n=0; n<sExtraLocUpInfor[i].mWifi.wifi_count; n++)
			WS_PRINTF("%s() wifi[%d] mac=%s, rssi=%s\n", __FUNCTION__, n,sExtraLocUpInfor[i].mWifi.wifs[n].mac,sExtraLocUpInfor[i].mWifi.wifs[n].rssi);
		
		/* 2.基站信息 */
		sExtraLocUpInfor[i].mCellType = userData->m_base.location.cellType;
		if(sExtraLocUpInfor[i].mCellType == WS_CELL_TYPE_LTE)
		{
			sExtraLocUpInfor[i].data.lte.scell = userData->m_base.location.data.lte.scell;
		/*			
			sExtraLocUpInfor.data.lte.scell.mcc = userData->location.data.lte.scell.mcc;
			sExtraLocUpInfor.data.lte.scell.mnc = userData->location.data.lte.scell.mnc;
			sExtraLocUpInfor.data.lte.scell.tac = userData->location.data.lte.scell.tac;
			sExtraLocUpInfor.data.lte.scell.cellid = userData->location.data.lte.scell.cellid;
			sExtraLocUpInfor.data.lte.scell.rxlev = userData->location.data.lte.scell.rxlev;
		*/
			WS_PRINTF("%s() lte.mcc=%d,mnc=%d,tac=%d,cellid=%d,rxlev=%d\n", __FUNCTION__
			,sExtraLocUpInfor[i].data.lte.scell.mcc,sExtraLocUpInfor[i].data.lte.scell.mnc
			,sExtraLocUpInfor[i].data.lte.scell.tac,sExtraLocUpInfor[i].data.lte.scell.cellid
			,sExtraLocUpInfor[i].data.lte.scell.rxlev);
		}
		else
		{		
			sExtraLocUpInfor[i].data.gsm = userData->m_base.location.data.gsm;

			for(n=0; n<sExtraLocUpInfor[i].data.gsm.num; n++)
			{
				WS_PRINTF("%s() gsm[%d]mcc=%d,mnc=%d,lac=%d,cellid=%d,rxlev=%d\n", __FUNCTION__,n
				,sExtraLocUpInfor[i].data.gsm.cells[n].mcc,sExtraLocUpInfor[i].data.gsm.cells[n].mnc
				,sExtraLocUpInfor[i].data.gsm.cells[n].lac,sExtraLocUpInfor[i].data.gsm.cells[n].cell_id
				,sExtraLocUpInfor[i].data.gsm.cells[n].rxlev);
			}
		}

		/* 3.时间 */
		Hal_Rtc_Gettime(&sExtraLocUpInfor[i].mTime);

		/* 4.有效标识 */
		sExtraLocUpInfor[i].mValid = TRUE;

		WS_PRINTF("%s() sExtraLocUpInfor[%d].mValid=%d,year=%d,mon=%d,day=%d,hour=%d,min=%d\n",
			__FUNCTION__,i,sExtraLocUpInfor[i].mValid,sExtraLocUpInfor[i].mTime.tm_year,sExtraLocUpInfor[i].mTime.tm_mon,
			sExtraLocUpInfor[i].mTime.tm_mday,sExtraLocUpInfor[i].mTime.tm_hour,sExtraLocUpInfor[i].mTime.tm_min);		
	}	

}


void Flw_Expra_Wifi_Scan_Cb(app_adp_wifi_result_t result, app_adp_wifi_ap_list * ap_list)
{
	WS_PRINTF("Flw_Expra_Wifi_Scan_Cb()");
	MMI_Modem_Wifi_Ctrl_Req(0, 0, 0, 5, 0);
	int n = 0;

    ws_client *pme = MMI_ModemAdp_WS_GetClient();
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    ws_wifi_info *wifi = NULL;

	if(wifi == NULL)
    {
        wifi = (ws_wifi_info*)lv_mem_alloc(sizeof(ws_wifi_info));
    }
	WS_PRINTF("%s() result=%d, %d\n", __FUNCTION__, result, ap_list->count);

    if(APP_ADP_WIFI_RESULT_FAILURE != result && ap_list && ap_list->count>0)
    {
		memset(wifi, 0, sizeof(ws_wifi_info));

        wifi->wifi_count = ap_list->count;
        
        for(int i=0; i<ap_list->count; i++)
        {
        	if((ap_list->item[i].mac[0]==0)&&(ap_list->item[i].mac[1]==0)&&(ap_list->item[i].mac[2]==0)
				&&(ap_list->item[i].mac[3]==0)&&(ap_list->item[i].mac[4]==0)&&(ap_list->item[i].mac[5]==0))
        		{
        			wifi->wifi_count--;
					continue;
        		}
			if((ap_list->item[i].mac[0]==0xFF)&&(ap_list->item[i].mac[1]==0xFF)&&(ap_list->item[i].mac[2]==0xFF)
				&&(ap_list->item[i].mac[3]==0xFF)&&(ap_list->item[i].mac[4]==0xFF)&&(ap_list->item[i].mac[5]==0xFF))
				{
        			wifi->wifi_count--;
					continue;
        		}
            wifi->wifs[n].ap_name[0]=0;
            sprintf(wifi->wifs[n].rssi, "%d", ap_list->item[i].rssi);
            sprintf(wifi->wifs[n].mac, "%02x:%02x:%02x:%02x:%02x:%02x", 
                    ap_list->item[i].mac[0],ap_list->item[i].mac[1],ap_list->item[i].mac[2],
                    ap_list->item[i].mac[3],ap_list->item[i].mac[4],ap_list->item[i].mac[5]);      
            WS_PRINTF("%s() wifi[%d]rssi!!!!=%s, mac=%s\n", __FUNCTION__, i, wifi->wifs[n].rssi,wifi->wifs[n].mac);
					
			n++;
        }
		
        Hal_Mem_Free(ap_list);
    }
	else
	{
		wifi->wifi_count = 0;
		if(ap_list)
			Hal_Mem_Free(ap_list);
	}

	if((Flw_CompareWifi(&(userData->mHisLocationInfo.mLastUpWifi), wifi) == true)) // 与上次周期定位wifi比较
	{
		memset(wifi,0,sizeof(ws_wifi_info));
	}
	else
	{
		/* 填充发送队*/
		Flw_ExpraWiFi_Hanldle(pme,wifi);
	}

	if(wifi)
		lv_mem_free(wifi);
	
	userData->m_base.cellInfoCB = NULL;
    userData->m_base.cellInfoCBPar = 0;
}


void Flw_StartExpraWIFI_Scan(void *pUser, uint32_t par)
{
	WS_PRINTF("Flw_StartExpraWIFI()\r\n");
	ws_client * pme = (ws_client*)pUser;	
    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    ws_location* loc_data = &userData->m_base.location;
   
  	if(Flw_StationIsChange(&(userData->mHisLocationInfo.mLastLocationIn),loc_data) == true	 	
	#if defined(__XF_PRO_A16__)
		//&& Flw_IsActiveValid() == true
	#endif	
	)
	{
		WS_PRINTF("Flw_StartExpraWIFI(),LBS is changed\n");

		Flw_SendMsgWifiHandle(STRAT_WIFI_TPYE_EXPRA_WIFI_STARTED);	
	}
	else
	{
		WS_PRINTF("Flw_StartExpraWIFI() LBSis not changed\n");
		
	}
	
    userData->m_base.cellInfoCB = NULL;
    userData->m_base.cellInfoCBPar = 0;
}


void Flw_GetExtraLocationInfo(void)
{
	WS_PRINTF("%s() line:%d\n", __FUNCTION__, __LINE__);
	ws_client *pme = MMI_ModemAdp_WS_GetClient();
	
	/* 获取基站 */
	Flw_GetStationInfo(pme, Flw_StartExpraWIFI_Scan, 0);
}
#endif

/**
 * 从HAL层获取SSID信息
 * 这个函数尝试从HAL层的扫描列表中查找对应MAC地址的SSID
 */
static void Flw_WiFi_Get_SSID_From_HAL(wifi_scan_test_ap_item* test_item, uint8_t* mac)
{
	// 由于当前架构限制，我们无法直接从HAL层获取SSID
	// 作为解决方案，我们提供一个基于MAC地址的标识符

	// 首先检查是否是我们关注的目标MAC地址
	if(mac[0] == 0xC6 && mac[1] == 0x99 && mac[2] == 0x0E &&
	   mac[3] == 0x62 && mac[4] == 0xB8 && mac[5] == 0x66) {
		strcpy(test_item->ssid, "TARGET_C6:99:0E:62:B8:66");
	} else if(mac[0] == 0x2E && mac[1] == 0xB0 && mac[2] == 0x52 &&
	          mac[3] == 0x85 && mac[4] == 0x24 && mac[5] == 0x38) {
		strcpy(test_item->ssid, "TARGET_2E:B0:52:85:24:38");
	} else {
		// 对于其他AP，生成基于MAC地址的标识符
		snprintf(test_item->ssid, 33, "AP_%02X%02X%02X", mac[3], mac[4], mac[5]);
	}

	// 注意：要获取真实的SSID，需要修改数据传递链路
	// 从 wlan_scan_result_t -> hal_wlan_scan_result_t -> app_adp_wifi_ap_list
	// 在每个环节都保留SSID信息
}

/**
 * WiFi扫描测试超时回调函数
 */
static void Flw_WiFi_Scan_Test_Timeout_Cb(uint32_t param)
{
	WS_PRINTF("[WIFI_TEST] WiFi scan test timeout after %d ms!", WIFI_SCAN_TEST_TIMEOUT_MS);

	if(wifi_scan_test_timer) {
		uos_timer_stop(wifi_scan_test_timer);
		uos_timer_delete(wifi_scan_test_timer);
		wifi_scan_test_timer = NULL;
	}

	wifi_scan_test_active = false;
	WS_PRINTF("[WIFI_TEST] WiFi scan test completed due to timeout");
}

/**
 * 合并扫描结果，去除重复AP
 */
static void Flw_WiFi_Merge_Scan_Results(app_adp_wifi_ap_list * new_results)
{
	if(!new_results || new_results->count == 0) {
		return;
	}

	if(!wifi_scan_all_results) {
		// 第一次扫描，直接分配内存
		wifi_scan_all_results = (wifi_scan_test_ap_list*)Hal_Mem_Alloc(sizeof(wifi_scan_test_ap_list));
		Hal_Mem_Set(wifi_scan_all_results, 0, sizeof(wifi_scan_test_ap_list));
	}

	// 合并新的扫描结果
	for(int i = 0; i < new_results->count; i++) {
		bool found_duplicate = false;

		// 检查是否已存在相同MAC地址的AP
		for(int j = 0; j < wifi_scan_all_results->count; j++) {
			if(memcmp(wifi_scan_all_results->item[j].mac, new_results->item[i].mac, 6) == 0) {
				// 找到重复AP，保留信号强度更高的
				if(new_results->item[i].rssi > wifi_scan_all_results->item[j].rssi) {
					wifi_scan_all_results->item[j].rssi = new_results->item[i].rssi;
					WS_PRINTF("[WIFI_TEST] Updated AP MAC=%02x:%02x:%02x:%02x:%02x:%02x, new RSSI=%d",
					          new_results->item[i].mac[0], new_results->item[i].mac[1],
					          new_results->item[i].mac[2], new_results->item[i].mac[3],
					          new_results->item[i].mac[4], new_results->item[i].mac[5],
					          new_results->item[i].rssi);
				}
				found_duplicate = true;
				break;
			}
		}

		// 如果是新AP且还有空间，添加到结果中
		if(!found_duplicate && wifi_scan_all_results->count < WIFI_SCAN_TEST_MAX_APS) {
			// 复制MAC地址和RSSI
			memcpy(wifi_scan_all_results->item[wifi_scan_all_results->count].mac,
			       new_results->item[i].mac, 6);
			wifi_scan_all_results->item[wifi_scan_all_results->count].rssi = new_results->item[i].rssi;

			// 尝试从HAL层获取SSID信息
			Flw_WiFi_Get_SSID_From_HAL(&wifi_scan_all_results->item[wifi_scan_all_results->count],
			                           new_results->item[i].mac);

			wifi_scan_all_results->count++;
			WS_PRINTF("[WIFI_TEST] Added new AP MAC=%02x:%02x:%02x:%02x:%02x:%02x, RSSI=%d, SSID=\"%s\"",
			          new_results->item[i].mac[0], new_results->item[i].mac[1],
			          new_results->item[i].mac[2], new_results->item[i].mac[3],
			          new_results->item[i].mac[4], new_results->item[i].mac[5],
			          new_results->item[i].rssi,
			          wifi_scan_all_results->item[wifi_scan_all_results->count-1].ssid);
		}
	}
}

/**
 * WiFi扫描测试回调函数
 */
static void Flw_WiFi_Scan_Test_Callback(app_adp_wifi_result_t result, app_adp_wifi_ap_list * ap_list)
{
	wifi_scan_test_round++;
	WS_PRINTF("[WIFI_TEST] ========== WiFi Scan Test Round %d Result ==========", wifi_scan_test_round);
	WS_PRINTF("[WIFI_TEST] Scan result: %d", result);
	WS_PRINTF("[WIFI_TEST] AP count this round: %d", ap_list ? ap_list->count : 0);

	// 合并当前轮次的扫描结果
	if(result == APP_ADP_WIFI_RESULT_SUCCESS && ap_list && ap_list->count > 0) {
		WS_PRINTF("[WIFI_TEST] Round %d SUCCESS - Found %d APs", wifi_scan_test_round, ap_list->count);
		Flw_WiFi_Merge_Scan_Results(ap_list);
	} else if(result == APP_ADP_WIFI_RESULT_TIMEOUT) {
		WS_PRINTF("[WIFI_TEST] Round %d TIMEOUT", wifi_scan_test_round);
	} else if(result == APP_ADP_WIFI_RESULT_FAILURE) {
		WS_PRINTF("[WIFI_TEST] Round %d FAILURE", wifi_scan_test_round);
	} else {
		WS_PRINTF("[WIFI_TEST] Round %d UNKNOWN result=%d", wifi_scan_test_round, result);
	}

	// 检查是否需要继续扫描
	if(wifi_scan_test_round < WIFI_SCAN_TEST_MAX_ROUNDS) {
		WS_PRINTF("[WIFI_TEST] Starting round %d scan in 2 seconds...", wifi_scan_test_round + 1);

		// 延迟2秒后开始下一轮扫描
		uos_sleep(MS_TO_TICKS(2000));

		#if USE_LV_WATCH_YIXIN !=0
			uint8_t scan_result = app_adaptor_wifi_scan(5, 30, WIFI_OVER_TIME, 0, Flw_WiFi_Scan_Test_Callback);
		#else
			uint8_t scan_result = app_adaptor_wifi_scan(5, 20, WIFI_OVER_TIME, 0, Flw_WiFi_Scan_Test_Callback);
		#endif

		if(scan_result == 0) {
			WS_PRINTF("[WIFI_TEST] Round %d scan started successfully", wifi_scan_test_round + 1);
			return; // 继续等待下一轮结果
		} else {
			WS_PRINTF("[WIFI_TEST] Round %d scan failed to start: %d", wifi_scan_test_round + 1, scan_result);
		}
	}

	// 所有轮次完成，输出最终结果
	WS_PRINTF("[WIFI_TEST] ========== Final Scan Results After %d Rounds ==========", wifi_scan_test_round);

	if(wifi_scan_all_results && wifi_scan_all_results->count > 0) {
		WS_PRINTF("[WIFI_TEST] Total unique APs found: %d", wifi_scan_all_results->count);

		// 输出所有发现的AP
		for(int i = 0; i < wifi_scan_all_results->count; i++) {
			WS_PRINTF("[WIFI_TEST] AP[%d]: SSID=\"%s\", MAC=%02x:%02x:%02x:%02x:%02x:%02x, RSSI=%d",
			          i, wifi_scan_all_results->item[i].ssid,
			          wifi_scan_all_results->item[i].mac[0], wifi_scan_all_results->item[i].mac[1],
			          wifi_scan_all_results->item[i].mac[2], wifi_scan_all_results->item[i].mac[3],
			          wifi_scan_all_results->item[i].mac[4], wifi_scan_all_results->item[i].mac[5],
			          wifi_scan_all_results->item[i].rssi);
		}

		// 分析最终扫描结果质量
		int strong_signal_count = 0;  // RSSI > -60
		int medium_signal_count = 0;  // -60 >= RSSI > -80
		int weak_signal_count = 0;    // RSSI <= -80

		for(int i = 0; i < wifi_scan_all_results->count; i++) {
			if(wifi_scan_all_results->item[i].rssi > -60) {
				strong_signal_count++;
			} else if(wifi_scan_all_results->item[i].rssi > -80) {
				medium_signal_count++;
			} else {
				weak_signal_count++;
			}
		}

		WS_PRINTF("[WIFI_TEST] Final signal strength analysis:");
		WS_PRINTF("[WIFI_TEST] - Strong signals (>-60dBm): %d", strong_signal_count);
		WS_PRINTF("[WIFI_TEST] - Medium signals (-60~-80dBm): %d", medium_signal_count);
		WS_PRINTF("[WIFI_TEST] - Weak signals (<-80dBm): %d", weak_signal_count);

		// 检查目标MAC地址是否被发现
		bool found_target1 = false, found_target2 = false;
		for(int i = 0; i < wifi_scan_all_results->count; i++) {
			// 检查 C6:99:0E:62:B8:66
			if(wifi_scan_all_results->item[i].mac[0] == 0xC6 &&
			   wifi_scan_all_results->item[i].mac[1] == 0x99 &&
			   wifi_scan_all_results->item[i].mac[2] == 0x0E &&
			   wifi_scan_all_results->item[i].mac[3] == 0x62 &&
			   wifi_scan_all_results->item[i].mac[4] == 0xB8 &&
			   wifi_scan_all_results->item[i].mac[5] == 0x66) {
				found_target1 = true;
				WS_PRINTF("[WIFI_TEST] ✓ Target MAC C6:99:0E:62:B8:66 FOUND with RSSI=%d", wifi_scan_all_results->item[i].rssi);
			}

			// 检查 2E:B0:52:85:24:38
			if(wifi_scan_all_results->item[i].mac[0] == 0x2E &&
			   wifi_scan_all_results->item[i].mac[1] == 0xB0 &&
			   wifi_scan_all_results->item[i].mac[2] == 0x52 &&
			   wifi_scan_all_results->item[i].mac[3] == 0x85 &&
			   wifi_scan_all_results->item[i].mac[4] == 0x24 &&
			   wifi_scan_all_results->item[i].mac[5] == 0x38) {
				found_target2 = true;
				WS_PRINTF("[WIFI_TEST] ✓ Target MAC 2E:B0:52:85:24:38 FOUND with RSSI=%d", wifi_scan_all_results->item[i].rssi);
			}
		}

		if(!found_target1) {
			WS_PRINTF("[WIFI_TEST] ✗ Target MAC C6:99:0E:62:B8:66 NOT FOUND");
		}
		if(!found_target2) {
			WS_PRINTF("[WIFI_TEST] ✗ Target MAC 2E:B0:52:85:24:38 NOT FOUND");
		}

	} else {
		WS_PRINTF("[WIFI_TEST] No APs found in any scan round");
	}

	// 清理测试状态
	if(wifi_scan_test_timer) {
		uos_timer_stop(wifi_scan_test_timer);
		uos_timer_delete(wifi_scan_test_timer);
		wifi_scan_test_timer = NULL;
	}

	// 清理累积结果内存
	if(wifi_scan_all_results) {
		Hal_Mem_Free(wifi_scan_all_results);
		wifi_scan_all_results = NULL;
	}

	wifi_scan_test_active = false;
	wifi_scan_test_round = 0;
	WS_PRINTF("[WIFI_TEST] ============ Test Completed ============");

	// 释放当前轮次AP列表内存
	if(ap_list) {
		Hal_Mem_Free(ap_list);
	}
}

/**
 * WiFi扫描测试接口 - 开机启动时调用
 * 功能：测试WiFi扫描功能的完整性和准确性
 */
void Flw_WiFi_Scan_Test_Interface(void)
{
	WS_PRINTF("[WIFI_TEST] ========== WiFi Scan Test Interface Started ==========");

	// 检查是否已有测试在进行
	if(wifi_scan_test_active) {
		WS_PRINTF("[WIFI_TEST] WiFi scan test already active, skipping");
		return;
	}

	// 设置测试状态
	wifi_scan_test_active = true;
	wifi_scan_test_round = 0;

	// 清理之前的累积结果
	if(wifi_scan_all_results) {
		Hal_Mem_Free(wifi_scan_all_results);
		wifi_scan_all_results = NULL;
	}

	// 输出测试配置信息
	WS_PRINTF("[WIFI_TEST] Enhanced Test configuration:");
	WS_PRINTF("[WIFI_TEST] - Test timeout: %d ms", WIFI_SCAN_TEST_TIMEOUT_MS);
	WS_PRINTF("[WIFI_TEST] - WiFi timeout: %d seconds", WIFI_OVER_TIME);
	WS_PRINTF("[WIFI_TEST] - Max scan rounds: %d", WIFI_SCAN_TEST_MAX_ROUNDS);
	WS_PRINTF("[WIFI_TEST] - Target MACs: E8:11:CA:07:D0:DB");

	#if USE_LV_WATCH_YIXIN !=0
		WS_PRINTF("[WIFI_TEST] - Scan mode: YIXIN Enhanced (rounds=5, max_hotspots=30, max_results=30)");
	#else
		WS_PRINTF("[WIFI_TEST] - Scan mode: Normal Enhanced (rounds=5, max_hotspots=20, max_results=20)");
	#endif

	WS_PRINTF("[WIFI_TEST] - System AP limit: %d (increased from 8)", APP_ADP_WIFI_AP_MAX_NUM);

	// 创建测试超时定时器
	if(wifi_scan_test_timer == NULL) {
		uos_timer_create(&wifi_scan_test_timer);
	}

	if(wifi_scan_test_timer) {
		uos_timer_start(wifi_scan_test_timer,
		                MS_TO_TICKS(WIFI_SCAN_TEST_TIMEOUT_MS),
		                0,
		                Flw_WiFi_Scan_Test_Timeout_Cb,
		                0);
		WS_PRINTF("[WIFI_TEST] Test timeout timer started");
	} else {
		WS_PRINTF("[WIFI_TEST] Failed to create test timeout timer");
	}

	// 启动WiFi扫描测试 - 使用增强参数以发现更多AP
	WS_PRINTF("[WIFI_TEST] Starting WiFi scan test...");

	#if USE_LV_WATCH_YIXIN !=0
		WS_PRINTF("[WIFI_TEST] Calling app_adaptor_wifi_scan(5, 30, %d, 0) - Enhanced scan", WIFI_OVER_TIME);
		uint8_t scan_result = app_adaptor_wifi_scan(5, 30, WIFI_OVER_TIME, 0, Flw_WiFi_Scan_Test_Callback);
	#else
		// 增强扫描参数：更多轮数和更大AP数量限制
		WS_PRINTF("[WIFI_TEST] Calling app_adaptor_wifi_scan(5, 20, %d, 0) - Enhanced scan", WIFI_OVER_TIME);
		uint8_t scan_result = app_adaptor_wifi_scan(5, 20, WIFI_OVER_TIME, 0, Flw_WiFi_Scan_Test_Callback);
	#endif

	if(scan_result == 0) {
		WS_PRINTF("[WIFI_TEST] WiFi scan request submitted successfully");
	} else {
		WS_PRINTF("[WIFI_TEST] WiFi scan request failed with error: %d", scan_result);

		// 清理测试状态
		if(wifi_scan_test_timer) {
			uos_timer_stop(wifi_scan_test_timer);
			uos_timer_delete(wifi_scan_test_timer);
			wifi_scan_test_timer = NULL;
		}
		wifi_scan_test_active = false;
	}

	WS_PRINTF("[WIFI_TEST] WiFi scan test interface completed, waiting for results...");
}


/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/tplgsm/utinc/utfname.h#3 $
 *   $Revision: #3 $
 *   $DateTime: 2006/09/01 15:09:47 $
 **************************************************************************
 * File Description
 * ----------------
 * Utility filename manipulation..
 **************************************************************************/

#ifndef UTFNAME_H
#define UTFNAME_H

/***************************************************************************
 * Include Files
 ***************************************************************************/

#include <system.h>

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/
#define DRIVE_SEPARATOR     58    /* The : character */
#define FILE_SEPARATOR      92    /* The \ character */
#define EXTENSION_SEPARATOR 46    /* The . character separates filename extension */

/* This define limits the file path name lengths for signalling that
 * uses filenames. For the maximum file path name lengths for use in file system
 * function calls,
 */
#   define MAX_FILE_PATH_LEN 100

/* This is the maximum file path name length that the FAT filesystem
 * can handle. This value must not be less than MAX_FILE_PATH_LEN.
 * Note that the maximum value supported here is 192, calculated as 15 * 13,
 * rounded down, because each entry can store upto 13 characters and there are
 * 16 entries per block, with one entry reserved for the short file name.
 */
#define MAX_FAT_FILE_PATH_LEN 192

/***************************************************************************
 * Type Definitions
 ***************************************************************************/

typedef struct FilePathTag
{
  Int8  length;
  Int16 name [MAX_FILE_PATH_LEN];
} FilePath;

/***************************************************************************
 * Variables
 ***************************************************************************/

/***************************************************************************
 * Macros
 ***************************************************************************/

/***************************************************************************
 * Local Functions
 ***************************************************************************/

/***************************************************************************
 * Global Functions
 ***************************************************************************/
Boolean fsExtractExtension( Int16* extension_p, Int16* filepath_p, Int8 size );
Boolean fsExtractPath( Int16* path_p, Int16* filepath_p, Int8 size );
Boolean fsExtractFilename( Int16* filename_p, Int16* filepath_p, Int8 size );


#endif /* UTFNAME_H */
/* END OF FILE */






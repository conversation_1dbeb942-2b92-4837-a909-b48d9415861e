/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/api/inc/ucsrtfdp.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/27 15:10:43 $
 **************************************************************************
 * File Description:
 **************************************************************************/

#if !defined (UCSRTFDP_H)
#define       UCSRTFDP_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <uas_asn.h>
#include <ccsd_sig.h>
#include <ucstypes.h>

/***************************************************************************
*   Manifest Constants
***************************************************************************/

/***************************************************************************
*   Macro Functions
***************************************************************************/

/***************************************************************************
*   Types
***************************************************************************/

/***************************************************************************
*   Function declarations
***************************************************************************/

void UcsRtfdpConfigInd (UcsRabIdentity rabIdentity, 
                        BearerIdentity bearerIdentity,
                        UTransmissionTimeInterval transmissionTimeInterval,
                        Int8 numberOfRates,
                        CcsdCsRate activeCsRates[]);
void UcsRtfdpReleaseInd (UcsRabIdentity rabIdentity);
void UcsRtfdpSuspendInd (UcsRabIdentity rabIdentity);
void UcsRtfdpResumeInd (UcsRabIdentity rabIdentity);
void UcsRtfdpConnectCnf (UcsRabIdentity rabIdentity);
void UcsRtfdpDisconnectCnf (UcsRabIdentity rabIdentity);
void UcsRtfdpDataInd (UcsRabIdentity rabIdentity, 
               Int16 bitOffset,
               Int16 bitLength,
               Int8 dataBits []);
void UcsRtfdpDataReq (UcsRabIdentity rabIdentity, 
                    Int16 bitLength,
                    Int8 dataBits []);
void UcsRtfdpConnectReq (UcsRabIdentity rabIdentity,
                       UcsRabType rabType);
void UcsRtfdpDisconnectReq (UcsRabIdentity rabIdentity);
void UcsRtfdpTtiInd (UcsRabIdentity rabIdentity);

void UcsRtfdpBitReverse (Int16 bitOffset,
                         Int16 bitLength,
                         Int8 dataBits []);

#endif

/* END OF FILE */

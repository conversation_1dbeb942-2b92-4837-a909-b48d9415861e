
#if !defined (UMACE_SIG_H)
#define       UMACE_SIG_H

#if defined (UPGRADE_3G_EDCH)

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <system.h>

/***************************************************************************
*   Manifest Constants
***************************************************************************/

/***************************************************************************
*   Macro Functions
***************************************************************************/

/***************************************************************************
*   Types
***************************************************************************/

#if defined (UMACE_UNIT_TEST)
#include <umacmaceif.h>
#include "pl_w_globs.h"
#include <pl_w_shared_memory.h>
#include "umaemuxcommon.h"
#include <umacerlcif.h>

typedef struct UnitestUmaceSessionInitIndTag
{
    Int8        cfn;
}UnitestUmaceSessionInitInd;

typedef struct UnitestUmaceSessionReleasedIndTag
{
    Int8        cfn;
}UnitestUmaceSessionReleasedInd;

typedef struct
{
    Int32       rxDataFormat[2];
    Int32       AGCS_Validity;
    Int32       RGCH_SRLS_Validity;
    Int32       RGCH_NSRLS_Validity;
    Int32       HICH_Validity;
    Int32       ERNTI_Primary_CRC;
    Int32       ERNTI_Secondary_CRC;
    Int32       AGScope;
    Int32       AGValue;
    Int32       Serving_RLS_RGCH;
    Int32       NonServing_RLS_RGCH;
    Int32       HICH_status;
    Int32       reference_ETPR;
    Int32       subframe;
    Int32       cfn;
	//new general
	Int32		uph;		//UE Power Headroom
	Int32		etfcRestrictionControl;
//new restriction
	Int8       etfcRestrictionResult[MAX_ETCFCI_RESTRICTIONS];//7

//new restriction control

    Int32      leRefMultBedSquare[MAX_REFERENCE_ETFCI_LENGTH]; // Leref*Bed^2  (With Q0)
    //for extrapolation stores the results of Keref,m/(Leref,m*(Bed,m)^2)
    //for interpolation stores the results of (Keref,m+1 - Keref,m)/(Leref,m+1*(Bed,m+1)^2 - Leref,m*(Bed,m)^2)
    Int32      ratioValue [MAX_REFERENCE_ETFCI_LENGTH];
    Int8       ratioQ [MAX_REFERENCE_ETFCI_LENGTH];

	Int8 		maxValidEtfci[MAX_VALID_ETFCI_SIZE];//8
	 // CPC parameters 
	UINT8   	MAC_DTX_Enabled;  // 0 - Disabled, 1 - Enabled
    UINT8   	DTX_CYCLE1_2;     // 0 - Cycle1, 1 - Cycle2
	UINT8   	Reset_MAC_Timers; // 0 - FALSE, 1 - TRUE

}SharedMemDataParams;

typedef struct UnitestUmaceDataIndTag
{
    UE_DCH_TTI           edchTti;

    /* MAC variables */
    UmacUlInfoForMacE    macData;

    Boolean dchReadyToSend;
    Boolean isSrbDelayPeriodOver;
    /* Shared memory variables */
    SharedMemDataParams  sharedMemParams;

    /* RLC traffic indication data */
    UmacEdchTrafficReq  rlcTrafficInd;
}UnitestUmaceDataInd;

typedef struct UnitestSiContentTag
{
    Int8                 uph;
    Int8                 tebs;
    Int8                 hlbs;
    Int8                 hlid;
}UnitestSiContent;

typedef struct UnitestLogicChanInfoTag
{
    Int16                pdusNum;
    Int16                pduSize;
    Int8                 ddi;
    Int8                 tsn;
}UnitestLogicChanInfo;

typedef enum UnitestUmaceTxStatusTag
{
    UMACE_TRANSMISSION_SUCCESSFUL    = 0,
    UMACE_TRANSMISSION_UNSUCCESSFUL  = 1,
    UMACE_NO_TRANSMISSION_INDICATION = 2
}
UnitestUmaceTxStatus;

typedef struct UnitestCheckUmaceOutputIndTag
{
    Boolean              isBufferValid;
    Boolean              isSiTxTriggered;
    UnitestSiContent     siContent;
    UnitestUmaceTxStatus statusIndToRlc;
    plwPhyEdchTtiInfo_ts phyEdchTtiInfo;
    UnitestLogicChanInfo logicChansInfo[15];
	Boolean				 isErntiIncluded;
}UnitestUmaceCheckOutputInd;

#endif /* UMACE_UNIT_TEST */

#endif /* UPGRADE_3G_EDCH */

#if defined (ENABLE_PS_L2_TOOL)
typedef struct L2ToolMacePduTag {
    UINT8 processIndex;
    UINT16 bufferSize;
} L2ToolMacePdu;
#endif

#endif /* UMACE_SIG_H */

/* END OF FILE */


/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urrmcrty.h#8 $
 *   $Revision: #8 $
 *   $DateTime: 2007/03/30 13:53:25 $
 **************************************************************************
 * File Description
 **************************************************************************/
 /*******************************	Revision History ***********************
  CQ/Activity	  SW Eng	   Date 		Description
  **********	  gaznelit	   12/07/08		Wrong start value of filter calculationx for Rx-Tx Timer Difference.


  **************************************************************************/

#if !defined (URRMCRTY_H)
#define       URRMCRTY_H

/***************************************************************************
 * Nested Include Files
 **************************************************************************/
#include <string.h>
#include <stddef.h>
#include <uas_ext.h>
#include <urrtypes.h>
#include <urrintsig.h>
#include <urrsirty.h>
#include <urrsirs.h>
#include <urrsmc.h>
#include <urr_sig.h>
#include <urrsirut.h>
#include <math.h>
#include <urrmtc.h>
#include <urrcsr.h>
#include <urrais.h>
#include <urrrbc.h>
#include <urrmcr.h>
#if defined(UPGRADE_UL_ECF) // ********** begin 
#include <urrrbccomedch.h>							
#endif /* UPGRADE_UL_ECF  */ // ********** end	
/* Define the macros so that the Linked List utility functions use the URRC
 * local allocator and deallocator functions rather than calling KiAllocMemory
 * and KiFreeMemory. The local allocator/deallocator routines are much quicker
 * and reduce system loading. The definition of these macros must be done
 * before utllist.h is included, otherwise, utllist.h will use the default
 * allocator/deallocator routines. */
/* CQ00010790, modified begin */
#define UTLL_ALLOC_MEMORY(sIZE, nODE_PP) UrrLocalAllocZeroMemory (sIZE, nODE_PP)

/* CQ00010790, modified end */
#define UTLL_FREE_MEMORY(nODE_PP) UrrLocalFreeMemory (nODE_PP)


#include <utllist.h>

/***************************************************************************
 * Manifest Constants
 **************************************************************************/
#define DUMMY_MEAS_ID                     0

#define POLYPHASE_PERIOD                  4

#define CSR                               1
#define MCR                               2
#define RECOVERY_MEAS                     3
#define DEFAULT_MAX_UE_TX_POWER          33
#define DEFAULT_MIN_UE_TX_POWER         -50

#define ECN0_MAP                         98
#define RSCP_MAP                         928    /* 116 * PHY_PREC2 */
#define RSSI_MAP                         808    /* 101 * PHY_PREC2 */

#define MCR_MIN_ECN0      ((SignedInt16) 0xff40) /* -192,  -24 dB in 1/8ths. */
#define MCR_MIN_RSCP      ((SignedInt16) 0xfc40) /* -960, -120 dB in 1/8ths. */

#define MAX_FILTERS                      16
#define MAX_8_VAL                        255
#define MAX_16_VAL        ((SignedInt16) 0x7FFF)
#define MIN_16_VAL        ((SignedInt16) 0x8000)
#define MAX_32_VAL        ((SignedInt32) 0x7FFFFFFF) /* 2147483647 */
#define MAX_TM_VAL                       0xFFFF      /* Must be > 38399 */
#define MIN_32_VAL        ((SignedInt32) 0x80000000)  /* -2147483648 */

#define MIN_VALID_TM_VAL        0x0
#define MAX_VALID_TM_VAL        38399

/*****************************************************************************
 * The maximum number of frequencies in addition to the current frequency as
 * defined in 8.1.2.1 of 25.133.
 */

#define MCR_MAX_NO_OF_DIFFERENT_FREQS         2

#if !defined (UPGRADE_EXCLUDE_2G)

#define MCR_MIN_RSSI_VAL        -32767       /* SignedInt16.             */
#define IG_EMPTY_CELL_ID_MASK     0x0
#define IG_INVALID_CELL_ID        0xff
#define IG_INVALID_BSIC           0xff
#define IG_TBARRED_BSIC_TIMER     0x7f       /* Tbarred BSIC timer base. */
#define IG_BSIC_GUARD_TIMER       (IG_TBARRED_BSIC_TIMER +  maxCellMeas)
                                             /* BSIC guard timer base. */
#define IG_SORT_LIST_TIMER        (IG_BSIC_GUARD_TIMER +  1)
#endif
/* added for CQ00025454 begin */
#define MCR_RACH_MEAS_GUARD_TIMER (IG_SORT_LIST_TIMER +  1)
#define MCR_RACH_MEAS_GUARD_TIMER_DURATION  10
/* added for CQ00025454 end */

#define RSRP_MAP                         1128        //141*8
#define RSRQ_MAP                         160         //20*8

/* Cell Individual offset, reporting range and hysteresis precision. */
#define CIO_R_H_PREC            2
#define H_F_PREC1               11    /* hysteresis precision for filter */
#define PHY_PREC1               3     /* PHY precision in bits*/
#define PHY_PREC2               8     /* PHY precision in decimals */
#define PHY_F_PREC1             12    /* PHY + filter precision in bits */
#define PHY_F_PREC2             4096  /* PHY + filter precision in decimal */

/* Events bit masks. These can be used for all measurement types. */

#define MCR_EVENT_A             1
#define MCR_EVENT_B             2
#define MCR_EVENT_C             4
#define MCR_EVENT_D             8
#define MCR_EVENT_E             16
#define MCR_EVENT_F             32
#define MCR_EVENT_J             64

/* Precision added for filter in bits. */
/*-----------------12/2/2007 5:37PM-----------------
 * change precision from 512 or 9 moves to 256 or 8 moves. the reason is that in case of using fc and the ttt in event 1B is
 * big than we have overflow of 32 nit which cause to stop the event 1B trigger.
 * --------------------------------------------------*/
#define F_PREC1                 8

/* Precision added for filter in decimal. */
#define F_PREC2                 256

/* Shift by NF/F_PREC - 1 to get .5 accuracy for ECN0 MAP. */

#define ECN0_NF_PREC            4
#define ECN0_F_PREC             2048

#define CLEAR_BIT(mASK, bIT)   ((mASK)  &= ~((Int32)(1 << (bIT))))

#define CLEAR_16BIT(mASK, bIT) ((mASK)  &= ~((Int16)(1 << (bIT))))

#define SET_16BIT(mASK, bIT) ((mASK) |= (Int16)(1 << (bIT)))

#define TEST_16BIT(mASK, bIT)     ((mASK) & (Int16)(1 << (bIT)))

#define TEST_32BIT(mASK, bIT)     ((mASK) & (Int32)(1 << (bIT)))

/* Macro for filtering : F[n] = (1-a).F[n-1] + a.M[n]
 * Scaled by 1024 to remove the need for using floating point variables. */
#define FILTER_VAL(a, f, m) ((((1024 - (a)) * (f)) +                           \
                             ((a) * ((m) << F_PREC1))) / 1024)

#define SET_FAILURE                                                            \
{                                                                              \
    urrMcr.failureData.failure = TRUE;                                         \
}                                                                              \

#define SET_CONFIGURATION_INCOMPLETE                                           \
{                                                                              \
    urrMcr.failureData.cause =                                                 \
        T_UFailureCauseWithProtErr_configurationIncomplete;                    \
}                                                                              \

/* Calculation to get SFN-SFN type 1 */
#define SFN_SFN_TYPE1(frameOff, chipOff) ((frameOff) * 38400 + (chipOff))

/*****************************************************************************
 * Sys info defines.
 */

#define MCR_SIB11 0
#define MCR_SIB12 1

/*****************************************************************************
 * MCR FTF debug defines.
 */

/* Event status. */
#define DEBUG_MCRIA_EVENT_ALL_CELL_REP_QTY_PRESENT      1
#define DEBUG_MCRIA_EVENT_OCCURRED                      2
#define DEBUG_MCRIA_EVENT_ACTIVE_SET_CELL               3
#define DEBUG_MCRIA_EVENT_MONITORED_SET_CELL            4
#define DEBUG_MCRIA_EVENT_DETECTED_SET_CELL             5
#define DEBUG_MCRIA_EVENT_CELL_ID_REP_IND               6
#define DEBUG_MCRIA_EVENT_CELL_SYNC_REP_IND             7
#define DEBUG_MCRIA_EVENT_ADD_TO_RECENTLY_TRIGG         8
#define DEBUG_MCRIA_EVENT_LEAVING_CONDITION_MET         9

/* Event result. */
#define DEBUG_MCRIA_EVENT_CELLS_TRIGGERED_EVENT         1
#define DEBUG_MCRIA_EVENT_START_TIME_TO_TRIGGER         2

/* CQ00063165, add begin */
#define MCR_DEBUG_REQUESTED_MEAS_ON_RACH_MEAS_ID_EVENT_SIZE 50

#define SET_REQUESTED_MEAS_ON_RACH_MEAS_ID_MAP(nEWvAL, iNDEX) \
    urrMcr.debugRequestedMeasOnRachMeasIdMap.event[urrMcr.debugRequestedMeasOnRachMeasIdMap.eventIndex].val = (nEWvAL);     \
    urrMcr.debugRequestedMeasOnRachMeasIdMap.event[urrMcr.debugRequestedMeasOnRachMeasIdMap.eventIndex].callerId = (iNDEX); \
    urrMcr.debugRequestedMeasOnRachMeasIdMap.eventIndex++;                                                                  \
    if (urrMcr.debugRequestedMeasOnRachMeasIdMap.eventIndex >= MCR_DEBUG_REQUESTED_MEAS_ON_RACH_MEAS_ID_EVENT_SIZE)         \
    {                                                                                                                       \
        urrMcr.debugRequestedMeasOnRachMeasIdMap.eventIndex = 0;                                                            \
    }                                                                                                                       \
    RRC_TRACE_2(URRC_MCR, SET_REQUESTED_MEAS_ON_RACH_MEAS_ID_MAP##iNDEX, URRC_MCR_DEFAULT,                                  \
        "%x -> %x", urrMcr.requestedMeasOnRachMeasIdMap, (nEWvAL));                                                         \
    urrMcr.requestedMeasOnRachMeasIdMap = (nEWvAL);   
/* CQ00063165, add end */

/***************************************************************************
 * Type Definitions
 **************************************************************************/

typedef enum McrFilterRequestedTypeTag
{
    MCR_FILTER_INTRA,
    MCR_FILTER_INTER
}
McrFilterRequestedType;

typedef enum McrReportsMayBeSentChangeCauseTag
{
	MCR_NONE,
	MCR_RRC_CONNECTION_RELEASE_RECEIVED,
	MCR_RRC_REACHED_MAX_RLC_AM_DATA_REQs
}
McrReportsMayBeSentChangeCause;

typedef enum McrFilteredMeasReportingPeriodTag
{
    MCR_50MS_REP_PERIOD,
    MCR_200MS_REP_PERIOD
}
McrFilteredMeasReportingPeriod;

typedef enum McrReportingQuantitiesAvailTag
{
    MCR_ALL_REP_QUANTITIES_PRESENT,
    MCR_NOT_ALL_REP_QUANTITIES_PRESENT,
    MCR_REP_QUANTITIES_NOT_REQUIRED
}
McrReportingQuantitiesAvail;

//ICAT EXPORTED STRUCT
typedef struct McrInterFreqListTag
{
    Int8    n;
    UUARFCN freq [MCR_MAX_NO_OF_DIFFERENT_FREQS];
}
McrInterFreqList;

typedef enum McrInterRATMeasTypeTag
{
        spare = 0,
        GSM,
        EUTRA
}
McrInterRATMeasType;

typedef struct McrMeasValDataTag
{
    Boolean                 isDetectedCell;
    Int16                   cellIdPsc;
    SignedInt16             measVal;
}
McrMeasValData;

typedef struct McrMeasValListTag
{
    Int8                    n;
    McrMeasValData          data [maxCellMeas];
}
McrMeasValList;

typedef struct McrPscListTag
{
    Int8                    n;
    Int16                   data [maxCellMeas];
}
McrPscList;

//ICAT EXPORTED STRUCT
typedef struct McrCellIdsListTag
{
    Int8                     n;
    Int8                     cellId [maxCellMeas];
}
McrCellIdsList;

//ICAT EXPORTED STRUCT
typedef struct McrFrequencyListTag
{
    Int8                    n;
    UUARFCN                 frequency [MAX_FDD_INTER_FREQUENCY_CELLS];
}
McrFrequencyList;

typedef struct McrRawValuesTag
{
    SignedInt16                     rscp_13b3;
    SignedInt16                     ecn0_13b3;
    SignedInt16                     pathloss_13b3;
    USFN_SFN_ObsTimeDifference1     sfnSfn;
    Int16                           countC_SFN_High;
    Int16                           off;
    Int16                           tm;
}
McrRawValues;

typedef struct McrFilterValuesTag
{
    SignedInt32                     rscp_19b13[POLYPHASE_PERIOD];
    SignedInt32                     ecn0_19b13[POLYPHASE_PERIOD];
    SignedInt32                     pathloss_19b13[POLYPHASE_PERIOD];
    Int8                            polyPhase_ecn0;
    Int8                            polyPhase_rscp;
    Int8                            polyPhase_pathloss;
    Int8                            current_ecn0;
    Int8                            current_rscp;
    Int8                            current_pathloss;
}
McrFilterValues;

//ICAT EXPORTED STRUCT
typedef struct McrMeasCountsTag
{
    Int8                            noOfSfnSfnMeas;
    Int8                            noOfCfnSfnMeas;
    Int8                            noOfEcn0Meas;
    Int8                            noOfRscpMeas;
}
McrMeasCounts;

typedef struct McrMeasResultsTag
{
    McrRawValues                    rawValues;
    McrFilterValues                 filterValues [MAX_FILTERS];
}
McrMeasResults;

//ICAT EXPORTED STRUCT
typedef struct McrMeasStatusTag
{
    /* bit masks, indicating cells for which the reporting quantity is */
    /* is being measured */
    CellIdMask                      sfnToSfn;
    CellIdMask                      cfnToSfn;
    CellIdMask                      ecn0;
    CellIdMask                      rscp;
}
McrMeasStatus;

//ICAT EXPORTED STRUCT
typedef struct McrFilterDataTag
{
    /* mapping of filterCoefficient to index into filterTable */
    /* when set to MAX_8_VAL, it indicates position is vacant */
    Int8                            filterMap;

    /* number of measurements using the filter */
    Int8                            noOfMeas;
}
McrFilterData;

//ICAT EXPORTED STRUCT
typedef struct McrFiltersTag
{
    /* total number of filters setup */
    Int8                            noOfFilters;
    McrFilterData                   filterData [MAX_FILTERS];
}
McrFilters;

//ICAT EXPORTED STRUCT
typedef struct McrCfnSfnDataTag
{
    Int8                        oldCFN;
    Int8                        newCFN;
    Int8                        noOfCfns;
    Int8                        startCFN;
    Boolean                     startCFNgreaterThanActivation;
    Int16                       sfn;
    Boolean                     configSet;
    UCOUNT_C                    hfnStored;
}
McrCfnSfnData;

//ICAT EXPORTED STRUCT
typedef struct McrCellDataTag
{
    UCellIndividualOffset            cellIndividualOffset;
    Boolean                          referenceTimeDifferenceToCellPresent;
    T_UReferenceTimeDifferenceToCell referenceTimeDifferenceToCellType;
    Int16                            referenceTimeDifferenceToCell;
    Boolean                          readSFN;
    UPrimaryScramblingCode           primaryScramblingCode;
    Boolean                          txPowerPresent;
    UPrimaryCPICH_TX_Power           txPower;
    Boolean                          txDiversityIndicator;
    Int8                             measResIndex;
    McrMeasCounts                    measCounts;
    McrCfnSfnData                    mcrCfnSfnData;
}
McrCellData;

//ICAT EXPORTED STRUCT
typedef struct VirtualActiveSetListTag
{
    Int8                      n;
    McrCellIdsList            virtualActiveSet [MAX_FDD_INTER_FREQUENCY_CELLS];
}
VirtualActiveSetList;

typedef struct McrPeriodicMeasDataTag
{
    KiTimer                     timer;
	Boolean                     IsFirstReport;
}
McrPeriodicMeasData;

typedef struct Mcr1a1cCellTriggDataTag
{
    UPrimaryScramblingCode      psc;
    Int8                        sentReport;
}
Mcr1a1cCellTriggData;

typedef struct Mcr1a1cCellTriggListTag
{
    Int8                        n;
    Mcr1a1cCellTriggData        data [maxCellMeas];
}
Mcr1a1cCellTriggList;

typedef struct McrPeriodicEventDataTag
{
    McrPscList                  cellsRecentlyTrigg;
    Mcr1a1cCellTriggList        cellsTrigg;
    Boolean                     perReportRunning;
    KiTimer                     periodicTimer;
}
McrPeriodicEventData;

typedef struct McrIntraFMTimeToTriggerTag
{
    UIntraFreqEventCriteria_latest  *event;
    McrPscList                  pscList;
    KiTimer                     timer;
}
McrIntraFMTimeToTrigger;

UT_SINGLE_LINK_LIST_DECLARE (McrIntraFMTimeToTriggerList, McrIntraFMTimeToTrigger);

typedef struct Mcr1aEventDataTag
{
    McrPeriodicEventData        perEventData;
    SignedInt16                 logSumVal;
    SignedInt16                 bestCellVal;
    Int8                        numForbiddenAffectCells;
}
Mcr1aEventData;

typedef struct Mcr1bEventDataTag
{
    McrPscList                  cellsToBeDeleted;
    SignedInt16                 logSumVal;
    SignedInt16                 bestCellVal;
    Int8                        numForbiddenAffectCells;
    McrPeriodicEventData        perEventData;
}
Mcr1bEventData;

typedef struct Mcr1dEventDataTag
{
    UPrimaryScramblingCode      bestPsc;
    Boolean                     findBest;
}
Mcr1dEventData;

typedef struct Mcr1eEventDataTag
{
    McrPscList                  cellsTrigg;
    /* flag to indicate if this is the first evaluation of event 1e for the */
    /* active cells, provided active cells are included in triggering */
    /* condition */
    Boolean                     firstEvalActiveCells;
}
Mcr1eEventData;

typedef struct McrIeIntraTimeToTriggerTag
{
    T_UIntraFreqEvent_latest        eventType;
    Int8                        freqIndex;
    McrCellIdsList              cellIdsList;
    KiTimer                     timer;
}
McrIeIntraTimeToTrigger;

UT_SINGLE_LINK_LIST_DECLARE (McrIeIntraTimeToTriggerList, McrIeIntraTimeToTrigger);

typedef struct McrInterFMTimeToTriggerTag
{
    UInterFreqEvent_latest     *event;
    McrFrequencyList            freqList;
    KiTimer                     timer;
}
McrInterFMTimeToTrigger;

UT_SINGLE_LINK_LIST_DECLARE (McrInterFMTimeToTriggerList, McrInterFMTimeToTrigger);

typedef struct McrIntraFMEventsDataTag
{
    /* list of timeToTrigger timers list */
    McrIntraFMTimeToTriggerList     timeToTriggList;
    Mcr1aEventData                  *trigg1aEvent;
    Mcr1bEventData                  *trigg1bEvent;
    McrPeriodicEventData            *trigg1cEvent;
    Mcr1dEventData                  *bestCell1dEvent;
    Mcr1eEventData                  *trigg1eEvent;
    McrPscList                      *trigg1fEvent;
    McrPeriodicEventData            *trigg1jEvent;
}
McrIntraFMEventsData;

typedef struct McrInterFMEventsDataTag
{
    /* timeToTrigger timers list for inter-frequency events */
    McrInterFMTimeToTriggerList     timeToTriggList;
    UUARFCN                         bestFreq2aEvent;
    McrFrequencyList                *trigg2bEvent;
    McrFrequencyList                *trigg2cEvent;
    Boolean                         trigg2dEvent;
    McrFrequencyList                *trigg2eEvent;
    Boolean                         trigg2fEvent;
}
McrInterFMEventsData;

#if ! defined (UPGRADE_EXCLUDE_2G)

//ICAT EXPORTED STRUCT
typedef struct McrGsmCellEventTag
{
    Boolean                         bsicVerified;
    Int8                            cellId;
    UBCCH_ARFCN                     bcch_arfcn;
}
McrGsmCellEvent;

//ICAT EXPORTED STRUCT
typedef struct McrBestCell3dEventTag
{
    Boolean                        firstMeasRcvd;
    McrGsmCellEvent                bestCell;
    SignedInt16                    gsm_CarrierRSSI;
}
McrBestCell3dEvent;

//ICAT EXPORTED STRUCT
typedef struct McrGsmCellEventListTag
{
    Int8                            n;
    McrGsmCellEvent                 gsmCellEvent [maxCellMeas];
}
McrGsmCellEventList;

typedef struct McrGsmTimeToTriggerTag
{
    UInterRATEvent                  *event;
    McrGsmCellEventList             cellList;
    KiTimer                         timer;
}
McrGsmTimeToTrigger;

UT_SINGLE_LINK_LIST_DECLARE (McrGsmTimeToTriggerList, McrGsmTimeToTrigger);

typedef struct McrGsmEventsDataTag
{
    McrGsmTimeToTriggerList         timeToTriggList;
    McrGsmCellEventList             *trigg3aEvent_p;
    McrGsmCellEventList             *trigg3bEvent_p;
    McrGsmCellEventList             *trigg3cEvent_p;
    McrBestCell3dEvent              bestCell3dEvent;
}
McrGsmEventsData;

#endif

typedef struct McrEutraEventTag
{
    Earfcn                             earfcn; //CQ00107230 change  UEARFCN to Earfcn
    UEUTRA_PhysicalCellIdentity         physicalCellIdentity;
}
McrEutraEvent;

typedef struct McrBestCell3dEventForEutraTag
{
    Boolean                          firstMeasRcvd;
    McrEutraEvent        bestCell;
    SignedInt16             measVal;
}
McrBestCell3dEventForEutra;

typedef struct McrEutraEventListTag
{
    Int8                            cellNum;
    McrEutraEvent        eutraEvent [maxCellMeas];      /*modify for CQ00026015, change maxEUTRACellPerFreq to maxCellMeas*/
}
McrEutraEventList;

typedef struct McrEutraTimeToTriggerTag
{
    UInterRATEvent                  *event;
    McrEutraEventList             eventList;
    KiTimer                         timer;
}
McrEutraTimeToTrigger;

UT_SINGLE_LINK_LIST_DECLARE (McrEutraTimeToTriggerList, \
                             McrEutraTimeToTrigger);

typedef struct McrEutraEventsDataTag
{
    McrEutraTimeToTriggerList         timeToTriggList;
    McrEutraEventList             *trigg3aEvent_p;
    McrEutraEventList             *trigg3bEvent_p;
    McrEutraEventList             *trigg3cEvent_p;
    McrBestCell3dEventForEutra      bestCell3dEvent;
}
McrEutraEventsData;

typedef struct McrUeInternalEventTimerTag
{
    T_UUE_InternalEventParam        type;
    UPrimaryScramblingCode          radioLink;
    KiTimer                         timer;
}
McrUeInternalEventTimer;

UT_SINGLE_LINK_LIST_DECLARE (McrUeInternalEventTimersList, McrUeInternalEventTimer);

typedef struct McrUeInternalEventRadioLinksTag
{
    T_UUE_InternalEventParam        type;
    UPrimaryScramblingCode          radioLink;
}
McrUeInternalEventRadioLinks;

UT_SINGLE_LINK_LIST_DECLARE (McrUeInternalEventRadioLinksList, McrUeInternalEventRadioLinks);

typedef struct McrIntraFreqReportingQuantityForRACHTag
{
    USFN_SFN_OTD_Type                   sfn_SFN_OTD_Type;
    UIntraFreqRepQuantityRACH_FDD       intraFreqRepQuantityRACH_FDD;
}
McrIntraFreqReportingQuantityForRACH;


typedef struct McrUpdateMeasOnRACHTag
{
    McrIntraFreqReportingQuantityForRACH    repQtyForRACHia;
    UInterFreqRepQuantityRACH_FDD           repQtyForRACHie;
    UMaxReportedCellsOnRACH                 maxRepCellsOnRACHia;
    UMaxReportedCellsOnRACHinterFreq        maxRepCellsOnRACHie;
}
McrUpdateMeasOnRACH;

//ICAT EXPORTED STRUCT
typedef struct McrMeasQuantityTag
{
    Boolean                             filterPresent;
    /* index into measurement results array corresponding to the filter */
    Int8                                filterIndex;
    UIntraFreqMeasQuantity_FDD          fddMeasQuantity;
}
McrMeasQuantity;

#if ! defined (UPGRADE_EXCLUDE_2G)

//ICAT EXPORTED STRUCT
typedef struct McrGsmMeasQuantityTag
{
    Boolean                             measQtyQualityEstimatePresent;
    McrMeasQuantity                     measQtyQualityEstimate;
    UMeasurementQuantityGSM             measurementQuantity;
    Boolean                             filterIndexPresent;
    Int8                                filterIndex;
    UBSIC_VerificationRequired          bsic_VerificationRequired;
}
McrGsmMeasQuantity;

typedef struct McrGsmReportDataTag
{
    McrPeriodicMeasData                 *periodicMeasData;
    McrGsmEventsData                    *eventsData;
}
McrGsmReportData;
#endif

typedef union McrIntraFreqReportDataTag
{
    McrPeriodicMeasData                 *periodicMeasData;
    McrIntraFMEventsData                *eventsData;
}
McrIntraFreqReportData;

typedef struct McrInterFreqMeasQuantityTag
{
    Boolean                             filterPresent;
    /* index into intra-freq measurement results corresponding to the filter */
    Int8                                activeCellFilterIndex;
    /* index into inter-frequency measurement results corresponding to */
    /* the filter */
    Int8                                filterIndex;
    UIntraFreqMeasQuantity_FDD          fddMeasQuantity;
}
McrInterFreqMeasQuantity;

typedef union McrInterFreqReportDataTag
{
    McrPeriodicMeasData                 *periodicMeasData;
    McrInterFMEventsData                *eventsData;
}
McrInterFreqReportData;

typedef struct McrUeInternalReportingQuantityTag
{
    Boolean     ue_TransmittedPower;
    Boolean     ue_RX_TX_TimeDifference;
}
McrUeInternalReportingQuantity;

typedef struct McrUeInternalRxTxTimeDiffResultTag
{
    UPrimaryScramblingCode              primaryScramblingCode ;
    UUE_RX_TX_TimeDifferenceType1       rxtxTimeDiff;
    Int32                               rxtxTimeDiffFiltered [MAX_FILTERS];
}
McrUeInternalRxTxTimeDiffResult;

typedef struct McrUeInternalMeasResultsTag
{
    /* array of tx power performed according to the filters*/
    SignedInt32                         txPowerFiltered [MAX_FILTERS];
    /* include the last measurements from L1*/
    UeTransmittedPower                  txPower;
    /* Tx power calculated with no filter*/
    SignedInt32                         unFilteredTxPower;
    SignedInt32                         rssiFiltered [MAX_FILTERS];
    SignedInt16                         rssi;
    Int8                                numberOfRadioLinks;
    McrUeInternalRxTxTimeDiffResult     rxTxTimeDiffResult [UPS_MAX_RADIO_LINKS];

    /* indicates if txPower has been updated since last periodic report */
    Boolean                             txPowerUpdated;

    /* Bit Map indicating if rxTx has been updated since last periodic report */
    /* for a particular Radio Link, Bit 0 - radioLink 1 */
    Int8                                rxtxUpdatedBitMap;
}
McrUeInternalMeasResults;

typedef union McrUeInternalReportDataTag
{
    McrPeriodicMeasData                 periodicTimer;

    /* list of timetotrigger timers */
    McrUeInternalEventTimersList        eventTimersList;
}
McrUeInternalTimerData;

typedef struct McrUeInternalEventResultsTag
{
    McrUeInternalTimerData              timerData;
    Boolean                             trigg6aEvent;
    Boolean                             trigg6bEvent;
    Boolean                             trigg6cEvent;
    Boolean                             trigg6dEvent;
    Boolean                             trigg6eEvent;
    McrUeInternalEventRadioLinksList    trigg6f6gEvents;
}
McrUeInternalEventResults;

//ICAT EXPORTED STRUCT
typedef struct McrNonCriticalExtensionsTag
{
    Boolean                                 rel5Event1dNonCriticalDataPresent;
    UIntraFreqEvent_1d_r5                   rel5Event1dNonCriticalData;
}
McrNonCriticalExtensions;

typedef struct McrieNonCriticalExtensionsTag
{
    Boolean                                 interFreqRACHReportingInfoPresent;
    UThreshold_latest                       interFreqRACHReportingThreshold;
    UMaxReportedCellsOnRACHinterFreq        maxReportedCellsOnRACHinterFreq;
    T_UInterFreqRACHReportingInfo_modeInfo  interFreqRACHReportingInfo_modeInfo;
    UInterFreqRepQuantityRACH_FDD           interFreqRepQuantityRACH_FDD;
}
McrieNonCriticalExtensions;


typedef struct McrIntraFreqMeasSysInfoTag
{
    UMeasurementIdentity                    measurementID;
    Boolean                                 intraFreqMeasQuantityPresent;
    UIntraFreqMeasQuantity                  *intraFreqMeasQuantity;
    Boolean                                 reportingQuantityForRACHPresent;
    McrIntraFreqReportingQuantityForRACH    *reportingQuantityForRACH;
    Boolean                                 maxReportedCellsOnRACHPresent;
    UMaxReportedCellsOnRACH                 maxReportedCellsOnRACH;
    Boolean                                 reportingInfoForCellDCHPresent;
    UReportingInfoForCellDCH                *reportingInfoForCellDCH;
    McrNonCriticalExtensions                nonCriticalExtensions;
    Boolean                                 rel5Event1bNonCriticalDataPresent;
    UPeriodicReportingInfo_1b               rel5Event1bNonCriticalData;
}
McrIntraFreqMeasSysInfo;

typedef struct McrInterFreqMeasSysInfoTag
{
    Boolean                                 interFreqRACHReportingInfoPresent;
    UThreshold_latest                       interFreqRACHReportingThreshold;
    UMaxReportedCellsOnRACHinterFreq        maxReportedCellsOnRACHinterFreq;
    T_UInterFreqRACHReportingInfo_modeInfo  interFreqRACHReportingInfo_modeInfo;
    UInterFreqRepQuantityRACH_FDD           interFreqRepQuantityRACH_FDD;
}
McrInterFreqMeasSysInfo;


typedef struct McrIntraFreqMeasTag
{
    Boolean                                 cellsForIntraFreqMeasListPresent;
    UCellsForIntraFreqMeasList              *cellsForIntraFreqMeasList;
    McrMeasQuantity                         measQuantity;
    UIntraFreqReportingQuantity             reportingQuantity;
    Boolean                                 measurementValidityPresent;
    UMeasurementValidity                    measurementValidity;
    UIntraFreqReportCriteria_latest         reportCriteria;
    McrIntraFreqReportData                  reportData;
    McrNonCriticalExtensions                nonCriticalExtensions;
    T_UIntraFreqReportCriteria_latest       previousReportCriteriaBeforeModify;
}
McrIntraFreqMeas;

typedef struct McrInterFreqMeasTag
{
    Boolean                                 cellsForInterFreqMeasListPresent;
    UCellsForInterFreqMeasList              *cellsForInterFreqMeasList;
    McrMeasQuantity                         measQuantity;
    Int8                                    activeCellFilterIndex;
    UInterFreqReportingQuantity             reportingQuantity;
    Boolean                                 measurementValidityPresent;
    UMeasurementValidity                    measurementValidity;
    Boolean                                 setUpdatePresent;
    T_UUE_AutonomousUpdateMode              updateMode;
    UInterFreqReportCriteria_latest         *reportCriteria;
    UIntraFreqEventCriteria_latest          *event1a;
    UIntraFreqEventCriteria_latest          *event1b;
    UIntraFreqEventCriteria_latest          *event1c;
    /* virtual active sets for all non-used frequencies */
    VirtualActiveSetList                    *virtualActiveSetList;
    /* timeToTrigger timers list for 1a, 1b, 1c */
    McrIeIntraTimeToTriggerList             intraTimeToTriggList;
    McrInterFreqReportData                  reportData;
    Boolean 						        event2D2fExist;
}
McrInterFreqMeas;

#if ! defined (UPGRADE_EXCLUDE_2G)
typedef struct McrGsmMeasTag
{
    Boolean                                 cellsForGsmMeasListPresent;
    UCellsForInterRATMeasList               *cellsForGsmMeasList;
    Boolean                                 gsmMeasQuantityPresent;
    McrGsmMeasQuantity                      gsmMeasQuantity;
    Boolean                                 gsmReportingQuantityPresent;
    UInterRATReportingQuantity_gsm          gsmReportingQuantity;
    UInterRATReportCriteria                 *reportCriteria;
    McrGsmReportData                        reportData;
}
McrGsmMeas;

#endif

typedef struct McrEutraMeasQuantityTag
{
    Boolean                             measQtyQualityEstimatePresent;
    McrMeasQuantity                     measQtyQualityEstimate;
    UMeasurementQuantityEUTRA   measurementQuantity;
    Boolean                             filterIndexPresent;
    Int8                                filterIndex;
}
McrEutraMeasQuantity;

typedef struct McrEutraReportDataTag
{
    McrPeriodicMeasData                 *periodicMeasData;
    McrEutraEventsData                    *eventsData;
}
McrEutraReportData;

typedef struct McrEutraMeasTag
{
    Boolean                                 eutraMeasQuantityPresent;
    McrEutraMeasQuantity        eutraMeasQuantity;
    Boolean                                 eutraReportingQuantityPresent;
    UInterRATReportingQuantity_r8_reportingQuantity       eutraReportingQuantity;
    UInterRATReportCriteria      *reportCriteria;
    McrEutraReportData            reportData;
}
McrEutraMeas;


typedef struct McrTrafficVolMeasTag
{
    Int8                                    sequenceNumber;
    Boolean                       trafficVolumeMeasurementObjectListPresent;
    UTrafficVolumeMeasurementObjectList     *trafficVolumeMeasurementObjectList;
    UTrafficVolumeMeasQuantity              trafficVolumeMeasQuantity;
    UTrafficVolumeReportingQuantity         trafficVolumeReportingQuantity;
    Boolean                                 measurementValidityPresent;
    UMeasurementValidity                    measurementValidity;
    UTrafficVolumeReportCriteria            reportCriteria;
}
McrTrafficVolMeas;

typedef struct McrQualityMeasTag
{
    Int8                                    sequenceNumber;
    UQualityReportingQuantity               qualityReportingQuantity;
    UQualityReportCriteria                  reportCriteria;
}
McrQualityMeas;

typedef struct McrUeInternalMeasTag
{
    UUE_InternalMeasQuantity                ue_InternalMeasQuantity;
    Int8                                    filterIndex;
    McrUeInternalReportingQuantity          reportingQuantity;
    UUE_InternalReportCriteria              reportCriteria;
    McrUeInternalMeasResults                measResults;
    McrUeInternalEventResults               eventResults;
}
McrUeInternalMeas;

typedef struct McrUePositionMeasTag
{
    Boolean 							measurementValidityPresent;
    UMeasurementValidity 				measurementValidity;
    T_UUE_Positioning_ReportCriteria_latest 		reportCriteriaTag;
    //Boolean 							sendReleaseInd;	// ********** deleted
	/* ********** begin */
    UPeriodicalReportingCriteria        periodicReportCriteria;

    /* The following flag is only used when method is GPS. 
       There are scenarios like state changes when stop is done internally in GPS chip and MCR doesn't need to send stop */
    Boolean 							sendGpsReleaseInd;  
    UPositioningMethod                  positioningMethod;

    /* the following are only used in case of Cell ID */
    McrPeriodicMeasData                 periodicTimer;
    CphyUeRxTxTimeDiffType2MeasurementInd   measResults; /* cell id measurement results */
	/* ********** end */
}
McrUePositionMeas;

typedef struct McrMeasTypeTag
{
    T_UMeasurementType_latest               tag;
    McrInterRATMeasType     interRATMeasType;   //when measType is interRAT, indicate it is GSM or EUTRA
    union
    {
        McrIntraFreqMeas                    *intraFreqMeas;
        McrInterFreqMeas                    *interFreqMeas;

#if ! defined (UPGRADE_EXCLUDE_2G)
        McrGsmMeas                          *gsmMeas;
#endif

      McrUePositionMeas         *uePositionMeas;

#if defined (ENABLE_3G_POSITIONING)
        UUE_Positioning_Measurement         *ue_positioning_Measurement;
#endif /* ENABLE_3G_POSITIONING */

        McrTrafficVolMeas                   *trafficVolMeas;
        McrQualityMeas                      *qualityMeas;
        McrUeInternalMeas                   *ueInternalMeas;
        
        McrEutraMeas            *eutraMeas;
    }
    u_McrMeasType;
}
McrMeasType;

typedef struct McrMeasControlMsgTag
{
    McrMeasType                             measType;
    UMeasurementReportingMode               measReportingMode;
    Boolean                                 additionalMeasListPresent;
    UAdditionalMeasurementID_List           *additionalMeasList;
}
McrMeasControlMsg;

typedef struct McrUeTxPowerTag
{
    /* max power allowed by the UE (L1 reports and UE capabilities) and the NW  */
    SignedInt32                             maximum;
    /* default value */
    SignedInt32                             minimum;
}
McrUeTxPower;

typedef struct McrCurrentCellTag
{
    UUARFCN                                 currCellFreq;
    UPrimaryScramblingCode                  currCellPsc;
}
McrCurrentCell;

typedef struct McrActiveSetEntryTag
{
    Int8                                    cellId;
    UPrimaryScramblingCode                  activeCellPsc;
}
McrActiveSetEntry;

typedef struct McrActiveSetTag
{
    UUARFCN                                 activeCellFreq;
    Int8                                    noOfActiveCells;
    McrActiveSetEntry                       activeCell [UPS_MAX_RADIO_LINKS];
}
McrActiveSet;

typedef enum McrRequestedMeasOnRachTag
{
    MCR_NO_REQ = 0,
    MCR_INTER,
    MCR_INTRA,
    MCR_BOTH
}
McrRequestedMeasOnRach;

typedef struct McrAwaitSubProcessTag
{
    UrrSubProcessId                         subProcessId;
    UrrSirAllocatedMemList                  memList;
    UMeasuredResultsOnRACH                  *measResultsOnRACH_p;
    UMeasuredResultsOnRACHinterFreq         measResultsOnRACHinterFreq;
    Boolean                                 measResultsOnRACHinterFreqValid;
    Boolean                                 pendMeasOnRach;
}
McrAwaitSubProcess;

UT_SINGLE_LINK_LIST_DECLARE (McrAwaitSubProcessList, McrAwaitSubProcess);

typedef struct McrMeasRepTag
{
    /* Sequence number to identify the message. */
    Int16                                    sequenceNumber;

    /* Used for measReportList. */
    UTransferMode                           transferMode;

    /* Pointer to measurementReport to build additional measurements after
     * receiving getMeasurementConfirmation from CMAC
     * (used for measReportList). */
    UMeasurementReport                      *measRep;

    /* List of pointers allocated to build a DCCH message
    (measurement report/ measurement control failure). */
    UrrSirAllocatedMemList                  memList;

    /* Per Buffer is used in order to free memory allocated in Positioning measurement report */
    PerBuffer               measPerBuffer;
}
McrMeasRep;

UT_SINGLE_LINK_LIST_DECLARE (McrMeasRepList,  McrMeasRep);

typedef struct McrMeasControlSysInfoTag
{
    UTrafficVolumeMeasSysInfo               *trafficVolMeasSysInfo;
    UUE_InternalMeasurementSysInfo          *ueInternalMeasSysInfo;

    McrIntraFreqMeasSysInfo                 *intraFreqMeasSysInfo_p;
    McrInterFreqMeasSysInfo                 *interFreqMeasSysInfo_p;
}
McrMeasControlSysInfo;
/*add for DMCR*/
typedef struct McrSib3OnRachMeasStatusTag
{
    UIntraFreqMeasQuantity_FDD_sib3 reportQuantity;
    UMaxReportedCellsOnRACH reportedCellNum;
   /* Int8 iscpTimeslotMap; */
}McrSib3OnRachMeasStatus;

typedef struct McrMeasIdentityTag
{
    McrMeasControlMsg                       measControlMsg;

    /* Indicates whether particular measurement is in progress. */
    Boolean                                 measInProgress;

    /* Indicates if this measurement information was received in SIB11/SIB12
    or in measurementControl message. */
    Boolean                                 isSysInfo;
    /* added for CQ00015289 begin */
    /* indicate if the measurement need be resumed after hoping or handover failed */
    Boolean                                 resumeFlag;
    /* added for CQ00015289 end */
}
McrMeasIdentity;

typedef struct McrFailureDataTag
{
    Boolean                                 failure;
    T_UFailureCauseWithProtErr              cause;
}
McrFailureData;

typedef struct McrHfnDataTag
{
    Boolean                     incrementing;
    Boolean                     hfnValid;
    UCOUNT_C                    hfnLatest;
    UCOUNT_C                    hfnConfig;
    Int8                        cfnActivation;
}
McrHfnData;

#if defined (ENABLE_BLER_DEBUG)
typedef struct McrQualityDebugTag
{
    Int8    qualitySeqNo;
    Boolean measInProgress;
	Boolean NwQualityMeasInProgress;
}
McrQualityDebug;
#endif

/* CQ00063165, add begin */
typedef struct McrDebugRequestedMeasOnRachMeasIdMapEventTag
{
    Int16 val;
    Int8  callerId;
}
McrDebugRequestedMeasOnRachMeasIdMapEvent;

typedef struct McrDebugRequestedMeasOnRachMeasIdMapTag
{
    McrDebugRequestedMeasOnRachMeasIdMapEvent event[MCR_DEBUG_REQUESTED_MEAS_ON_RACH_MEAS_ID_EVENT_SIZE];
    Int8 eventIndex;
} 
McrDebugRequestedMeasOnRachMeasIdMap;
/* CQ00063165, add end */

typedef struct UrrMcrDataTag
{
    Boolean                     dataInitialised;
    /* Sequence number to identify DCCH message sent to AIS. */
    Int8                        aisMsgSeqNo;

    /* HFN data. */
    Boolean                     csPresent;
    Boolean                     psPresent;
    McrHfnData                  csHfnData;
    McrHfnData                  psHfnData;
    Boolean                     cfnValid;

    /* UE transmit power. */
    McrUeTxPower                ueTxPower;

    McrCurrentCell              currentCell;
    McrActiveSet                activeSet;
    McrState                    state;
    Boolean                     freqChanged;
    Boolean                     awaitCampedOnCell;
    Boolean                     suspended;
    Boolean                     reportsMayBeSent;
	McrReportsMayBeSentChangeCause reportsMayBeSentChanged;

       /*added for ********** start*/
         Boolean                     scellChangedForLte; 
       /*added for ********** end*/

#if defined (ENABLE_BLER_DEBUG)
    McrQualityDebug             qualityDebug;
#endif

#if defined (ENABLE_TX_POWER_DEBUG)
    Boolean                     txPowerDebugMeasInProgress;
#endif

    /* UE capabilities.  */
    Boolean                     fdd_Measurements;
#if ! defined (UPGRADE_EXCLUDE_2G)
    /* UE capabilities.  */
    UGSM_Measurements           gsm_dlCompressedModeCapability;
#endif

     /* Indicates if compressed mode is required for fdd measurements */
     Boolean                    fdd_dlCompressedModeCapability;

   /*added for DMCR DEVELOPMENT start*/
   McrSib3OnRachMeasStatus sib3OnRachMeasStatus;   
   Boolean onRachConfigBySib3;
    /*added for DMCR DEVELOPMENT end*/
    /* Indicates if measurements on RACH are in progress. */
    McrRequestedMeasOnRach      requestedMeasOnRach;

    /* Indicates if Phy reported Meas on Rach */
    McrRequestedMeasOnRach      phyReportedMeasOnRach;

    /* A bitmap of measIds that need RACH (requested MeasOnRach */
    Int16                       requestedMeasOnRachMeasIdMap;

    /* Used to store the current  requestedMeasOnRachMeasIdMap during cell reselection
     * (in case we decide not to perform reselection after NO_CELL is received). */
    Int16                       requestedMeasOnRachMeasIdMapBackup;

    /* list of subProcesses awaiting measured Results on RACH from CPHY */
    McrAwaitSubProcessList      awaitSubProcessList;

    /* list of trafficVolume measurementReports awaiting transit FACH state */
    McrMeasRepList              awaitFACHTransitList;

    /* list of measurement reports awaiting measured results on RACH from CPHY */
    McrMeasRepList              awaitRACHMeasList;

    /* list of measurement reports awaiting additional measurements from MAC */
    McrMeasRepList              awaitCmacMeasResultsList;

//********** start
#if defined (UPGRADE_ESCC)
	/* list of measurement reports awaiting Mac Activation Time Offset*/
    McrMeasRepList              awaitTcHscchSetupCnf; /* ********** modify */
#endif//UPGRADE_ESCC
//********** end

    /* element 1 contains idle mode(SIB11) information */
    /* element 2 contains connected mode (SIB11/SIB12) information */
    McrMeasControlSysInfo       measControlSysInfo [2];

    /* measurementIdentity variable */
    McrMeasIdentity            *measIdentity [maxNoOfMeas];

    McrFailureData              failureData;

    /* pointers allocated to store trafficvolume information of SIB11/SIB12 */
    UrrSirAllocatedMemList      trafficVolMeasSysInfoMemList [2];

    /* pointers allocated to store intra-freq information of SIB11/SIB12 */
    UrrSirAllocatedMemList      intraFreqMeasSysInfoMemList [2];

    /* pointers allocated to store inter-freq information of SIB11/SIB12 */
    UrrSirAllocatedMemList      interFreqMeasSysInfoMemList [2];

    /* pointers allocated to store ueInternal information of SIB11/SIB12 */
    UrrSirAllocatedMemList      ueInternalMeasSysInfoMemList [2];

    /* CR 25.331 2629: if RSCP is lower than -115, use also delta RSCP in measurement report */
    Boolean                     deltaRscpPresent;

    /* Indicates whether an inter frequency hard hand over is in progress */
    Boolean                     ieHardHandOverInProgress;
	/* added for CQ00025454 begin */
    KiTimer                     measOnRachGuardTimer;
	/* added for CQ00025454 end */

    /* CQ00063165, add begin */
    // PTK_CQ00274997 - Add debug info for requestedMeasOnRachMeasIdMap
    McrDebugRequestedMeasOnRachMeasIdMap debugRequestedMeasOnRachMeasIdMap;   
    /* CQ00063165, add end */
}
UrrMcrData;

#if defined(UPGRADE_DSDSWB)
extern  UrrMcrData urrMcr1[URRC_TASK_NUM];
#define urrMcr urrMcr1[URRC_DSDS_TASK]
#else/*UPGRADE_DSDSWB*/
extern  UrrMcrData urrMcr;
#endif/*UPGRADE_DSDSWB*/
/***************************************************************************
 *  Macros
 **************************************************************************/

/***************************************************************************
 * Global Data declarations
***************************************************************************/
extern const Int16 filter50msLookUpTable [];
extern const Int16 filter200msLookUpTable [];
extern const Int16 txPowerFilterLookUpTable [];

/***************************************************************************
 *  Function Prototypes
 **************************************************************************/

/*****************************************************************************
 * Traffic Volume Measurements related functions (defined in urrmcrtv.c).
 */

Boolean UrrMcrTvmConfigComplete         (UTrafficVolumeMeasurement  *tvm_p);
Boolean UrrMcrTvmSysInfoConfigComplete  (UTrafficVolumeMeasSysInfo  *tvm_p);
void    UrrMcrHandleIdleSib11Tvm        (USysInfoType11             *sys_p);
void    UrrMcrHandleIdleSib12Tvm        (UMeasurementControlSysInfo *meas_p);
void    UrrMcrHandleConnectedSib11Tvm   (UMeasurementControlSysInfo *meas_p);
void    UrrMcrHandleConnectedSib12Tvm   (UMeasurementControlSysInfo *meas_p);
void    UrrMcrStoreSibTvmToMeasIdentity (UMeasurementIdentity       measId);
void    McrtvStoreAndStartSibTvm        (UrrSmcConnectedState       state);
Boolean McrtvAnyUlTrchExistsForMeasId(UMeasurementIdentity measId);
void    UrrMcrStoreMeasControlTvm (UTrafficVolumeMeasurement *src_p,
                                   McrTrafficVolMeas **tvm_p);

void    UrrMcrModifyTvm  (UMeasurementControl_latest_IEs *ie_p);

Boolean UrrMcrIsTvmValid (UrrSmcConnectedState       state,
                          UMeasurementIdentity       measId);

void    McrtvDchToFachUraPch (UMeasurementIdentity measId, Boolean *found, Boolean handover);
void    McrtvFachToDch       (UMeasurementIdentity measId, Boolean *found, Boolean handover3G_2GFail);
void McrtvFachToPch (UMeasurementIdentity measId, Boolean *found);
void McrtvPchToFach (UMeasurementIdentity measId);
#if defined(UPGRADE_UL_ECF) // ********** begin 
void McrtvFachToFach (UMeasurementIdentity measId);	
void UrrMcrStartTvmPchToFachComEdch (UMeasurementIdentity measId);			
#endif /* UPGRADE_UL_ECF  */ // ********** end	

void    UrrMcrStartTvm (UMeasurementIdentity measId);
void    UrrMcrStopTvm (UMeasurementIdentity measId);
void    UrrMcrBuildTvmMeasuredResults (UrrSirAllocatedMemList *memList_p,
                                    UMeasuredResults *measRes_p,
                                    CmacAdditionalTrafficMeasurement *tvmInd_p);
void    UrrMcrBuildTvmEventResults (UrrSirAllocatedMemList *memList_p,
                                 UEventResults  **eventRes_p,
                                 CmacTrafficMeasurementInd *tvmInd_p);
void    UrrMcrDeleteTvm (McrTrafficVolMeas *tvm_p);

/*****************************************************************************
 * Quality Measurement related functions ( defined in urrmcrqt.c ).
 */

#if defined (ENABLE_BLER_DEBUG)
void    McrqtStartDebugQualityMeasurement (void);
void    McrqtStopDebugQualityMeasurement  (void);
#endif
Boolean UrrMcrQualityConfigComplete (UQualityMeasurement *quality_p);
Boolean McrqtAnyUlTrchExistsForMeasId(UMeasurementIdentity measId);

void    McrqtStoreAndStartMCQualityMeas (UQualityMeasurement *src_p,
                                         UMeasurementIdentity measId);

void    UrrMcrModifyQualityMeas (UMeasurementControl_latest_IEs *ie_p);
void    UrrMcrStartQualityMeas (UMeasurementIdentity measId);
void    UrrMcrStopQualityMeas (UMeasurementIdentity measId);
void    UrrMcrBuildQualityMeasuredResults (UrrSirAllocatedMemList *memList_p,
                                 UMeasuredResults *measRes_p,
                                 CmacAdditionalQualityMeasurement *measInd_p);
void    UrrMcrBuildQualityEventResults (UrrSirAllocatedMemList *memList_p,
                                     UEventResults **eventRes_p,
                                     CmacQualityMeasurementInd *qualityInd_p);
void    UrrMcrDeleteQualityMeas (McrQualityMeas *quality_p);

/*****************************************************************************
 * UE Internal Measurements related functions (defined in urrmcrui.c).
 */

void    McruiInitData                  (void);
Boolean McruiConfigComplete  (UUE_InternalMeasurement_latest *ue_p);

#if defined (ENABLE_TX_POWER_DEBUG)
void    McruiStartDebugTxPowerMeasurement (void);
#endif /* ENABLE_TX_POWER_DEBUG */

void    McruiStoreAndStartMCUeInternalMeas (UUE_InternalMeasurement_latest *src_p,
                                              UMeasurementIdentity    measId);

void    McruiStartUeInternalMeas  (UMeasurementIdentity       measId);
void    McruiModifyUeInternalMeas (UMeasurementControl_latest_IEs *ie_p);

void    McruiStopUeInternalMeas   (UMeasurementIdentity       measId,
                                   Boolean                    leaveDch);

void    McruiProcessCphyUeInternalMeasInd (
            UMeasurementIdentity         measId,
            CphyUeInternalMeasurementInd *measInd_p);

void    McruiHandleUeInternalTimerExpiry (TimerId              timerId,
                                          UMeasurementIdentity measId);

void    McruiBuildUeInternalMeasuredResults (
            UrrSirAllocatedMemList *memList_p,
            UMeasuredResults       *measRes_p,
            McrUeInternalMeas      *ue_p);

void    McruiDeleteUeInternalMeas (McrUeInternalMeas *ue_p);

McrUeInternalMeas *McruiGetUeInternalMeasPtr (void);

Boolean McruiGetTxPower (McrUeInternalMeas *ue_p,
                         SignedInt16       *txPower_p);

Boolean McruiGetRssi (McrUeInternalMeas *ue_p,
                      SignedInt16       *rssi_p);

Int32 McruiFilterVal (Int16 a, Int32 f, Int32 m,
                      McrFilteredMeasReportingPeriod filteredMeasPeriod);
/*****************************************************************************
 * Intra-frequency Measurements related functions (defined in urrmcria.c).
 */
void    UrrMcriaRemoveNonActiveSetCellFromCellInfo(UrrActiveSetEntry *activeSet, Int8 numLinks);          //-+ PTK_CQ00209029 21-Mar-2012 +-

void    McrUpdateMeasOnRach (McrUpdateMeasOnRACH *updateMeasOnRach,
                                                Int8 index);

void    UrrMcrSendCphyStartMeasOnRachReq (Int8    index,
                                          Boolean report,
                                          Int16    measId);

void    McrSendCphyStopMeasOnRachReq   (void);


void    McriaSendCphyMonitorIntraFreqReq (Int8       measId,
                                          CellIdMask rscp,
                                          CellIdMask ecn0,
                                          CellIdMask sfnsfn,
                                          CellIdMask cfnSfn);

void    UrrMcrHandleIdleSib11IntraFM (USysInfoType11 *sys_p);

void    UrrMcrHandleConnectedSib11IntraFM (UMeasurementControlSysInfo *meas_p,
                                      USysInfoType11_v590NonCriticalExtension
                                                     *nonCriticalExtensions_p);

void    UrrMcrHandleIdleSib11InterFM (USysInfoType11 *sys_p);

void    UrrMcrHandleConnectedSib11InterFM (UMeasurementControlSysInfo *meas_p,
                                      USysInfoType11_v6b0NonCriticalExtensions
                                                     *nonCriticalExtensions_p);

void    UrrMcrHandleIdleSib12IntraFM (UMeasurementControlSysInfo *meas_p,
                                      USysInfoType12_v590NonCriticalExtension
                                                     *nonCriticalExtensions_p);

void    UrrMcrHandleIdleSib12InterFM (UMeasurementControlSysInfo *meas_p,
                                      USysInfoType12_v6b0NonCriticalExtensions
                                                     *nonCriticalExtensions_p);

void    UrrMcrHandleConnectedSib12IntraFM (UMeasurementControlSysInfo *meas_p,
                                      USysInfoType12_v590NonCriticalExtension
                                                     *nonCriticalExtensions_p);

void    UrrMcrHandleConnectedSib12InterFM (UMeasurementControlSysInfo *meas_p,
                                      USysInfoType12_v6b0NonCriticalExtensions
                                                     *nonCriticalExtensions_p);

Boolean UrrMcrNoIntraMeasIsConfigured(void);

void    McriaUpdateActiveCellInfo  (void);

Boolean McriaConfigComplete (UIntraFrequencyMeasurement_latest *intraFM_p);

void    McriaStoreSibIntraFreqCells (
                              Int8                     noOfIntraFreqCells,
                              CsrMcrIntraFreqNCellInfo *intraFreqNCellInfo_p);

void McriaProcessRecoveryRssiMeas(const CphyMonitorIntraFreqCellInd * const intraFreqMeas_p);

void    McriaStoreAndStartSibIntraFM (Boolean handover3G_2GFail, Boolean fromIdle);

void    McriaStoreAndStartMCIntraFM  (
                    UIntraFrequencyMeasurement_latest *ieIntra_p,
                    UMeasurementIdentity           measId,
                    Boolean                        measAlreadyExists);

void    McriaStartIntraFM (UMeasurementIdentity measId,
                           Boolean              statusInfoPresent,
                           Boolean              handoverFail,
                           CellIdMask           rscp,
                           CellIdMask           ecn0,
                           CellIdMask           sfnSfn,
                           CellIdMask           cfnSfn,
                           Boolean              cellListChanged);

void    McriaModifyIntraFM (UMeasurementControl_latest_IEs *ie_p);

void    McriaStopIntraFM   (UMeasurementIdentity       measId,
                            Boolean                    leaveDch,
                            Boolean                    hardHandover);

void    McriaDeleteIntraFM (McrIntraFreqMeas           *intraFM_p);

Boolean McriaBuildIntraFMAddMeasRes (UrrSirAllocatedMemList *memList_p,
                                     UMeasuredResults       *measRes_p,
                                     McrIntraFreqMeas       *intraFM_p);

void    McriaHandleMonitorIntraFreqCellInd (
                                        CphyMonitorIntraFreqCellInd *sig_p);

void    McriaHandleIntraFMTimerExpiry (TimerId timerId,
                                       UMeasurementIdentity measId);

void    McriaHandleActiveSetSignal (UrrInternalSignalActiveSet *sig_p);
void    McriaInitDCHData        (void);
void    McriaInitData           (Boolean activation);
void    McriaStateChangeFromDch (void);
void    UrrMcrDeleteIaData      (void);
void    McriaInitCellHfnData    (McrCfnSfnData *cfnSfnData_p);
McrHfnData *McriaGetHfnDomain   (void);
void    McriaSetHfnConfig       (UActivationTime activationTime);
void    McriaResetCfn           (void);
void    McriaHandleInterFreqHHO (void);
void    McriaHandleFailInterFreqHHO (void);

SignedInt32 McriaFilterVal (Int16 a, SignedInt32 f, SignedInt32 m);

void McriaInitFilterResults(McrFilterValues *filterValues);

/*****************************************************************************
 * Inter-frequency Measurements related functions (defined in urrmcrie.c).
 */

void    McrieInitData            (Boolean activation);
void McrieInitCellInfoList (void);
void    McrieHHODeleteReportData (void);
void    McrieStateChangeFromDch  (void);

/* added for ********** begin */
Boolean McrieCheckNonUsedFreqParaListForEvents(UInterFrequencyMeasurement_latest *interFM_p);
/* added for ********** end */


Boolean McrieConfigComplete (UInterFrequencyMeasurement_latest *interFM_p);

Boolean McrieConfigValid(UInterFrequencyMeasurement_latest *interFM_p);

Boolean McrieFrequencyInfoValid(UInterFreqCellInfoList_latest *cellInfoList_p);

void    McrieStoreSibInterFreqCells (
                              Int8                     noOfInterFreqCells,
                              CsrMcrInterFreqNCellInfo *sibCellInfo_p);

void    McrieStoreAndStartMCInterFM (UInterFrequencyMeasurement_latest *interFM_p,
                                     UMeasurementIdentity       measId);

void    McrieModifyInterFM (UMeasurementControl_latest_IEs *ie_p);

Boolean McrieBuildInterFMAddMeasRes (UrrSirAllocatedMemList *memList_p,
                                     UMeasuredResults       *measRes_p,
                                     McrInterFreqMeas       *interFM_p);

void    McrieStartInterFM  (UMeasurementIdentity measId,
                            Boolean              rssiChanged,
                            Boolean              handoverFail,
                            Boolean              cellListChanged);

void    McrieStopInterFM   (UMeasurementIdentity measId,
                            Boolean              leaveDch,
                            Boolean              handover);

void    McrieDeleteInterFM (McrInterFreqMeas     *interFM_p);
void    UrrMcrDeleteIeData (void);

void    McrieHandleInterFMTimerExpiry (TimerId              timerId,
                                       UMeasurementIdentity measId);

void    McrieHandleMonitorInterFreqCellInd (CphyMonitorInterFreqCellInd *sig_p);
void    UrrMcrCphyMeasureInterFreqCellsInd (RadioResourceControlEntity  *urr_p);

void    McrieSendCphyMonitorInterFreqReq (Int8       measId,
                                          CellIdMask rscp,
                                          CellIdMask ecn0,
                                          CellIdMask sfnsfn,
                                          CellIdMask cfnSfn
                                            , Int8 highPriority
                                          );

void    McrieSetHfnConfig           (UActivationTime activationTime);
void    McrieResetCfn               (void);
void    McrieHandleInterFreqHHO     (void);
void    McrieHandleFailInterFreqHHO (void);
void    McrieProcessEvents (UMeasurementIdentity measId, CellIdMask cellIds, UUARFCN frequency);
void    McrieCopyCellFromCellInfoList (McrCellData             *cellData_p,
                                             UUARFCN                 freq,
                                             UPrimaryScramblingCode  primaryScramblingCode);

#if ! defined (UPGRADE_EXCLUDE_2G)
/*****************************************************************************
 * Inter-RAT Measurements related functions (defined in urrmcrir.c).
 */

Boolean McrirBuildGsmAddMeasRes   (UrrSirAllocatedMemList *memList_p,
                                   UMeasuredResults       *measRes_p,
                                   McrGsmMeas             *interRM_p);

Boolean McrirConfigComplete          (UInterRATMeasurement_latest  *interRM_p);
/* **********,merge **********, begin */
/*Int8    McrirGetCellId               (UBSIC bsic, UBCCH_ARFCN arfcn);*/
Int8    McrirGetCellId               (UBSIC bsic, UBCCH_ARFCN arfcn,Boolean *bsicCompatible);
/* **********,merge **********, end */

/* **********,merge **********, begin */
Boolean McrirGetAllCellIdbyArfcn (Int8* cellId,Int8* cellnum, UBCCH_ARFCN arfcn);
/* **********,merge **********, end */




URadioFrequencyBandGSM
        McrirGetGsmFrequencyBand (UBCCH_ARFCN     arfcn,
                                  UFrequency_Band gsmBandIndicator);

UBCCH_ARFCN McrirGetGsmCellFreq      (Int8                  cellId,
                                      UFrequency_Band       *cellBandMode_p,
                                      UFrequency_Band       *l1BandMode_p);

void    McrirHandleBsicDecodeInd     (Int8               cellId,
                                      UBSIC              *bsic);

void    UrrMcrirHandlePeriodicTimerExpiry (UMeasurementIdentity measId);       //-+ PTK_CQ00237679 4-Oct-2012 +-


void    McrirHandleMonitorGsmCellInd (CphyMonitorGsmCellInd *sig_p);

void    McrirHandleInterRMTimerExpiry (TimerId              timerId,
                                       UMeasurementIdentity measId);

void    McrirInitData       (Boolean activation);
void    UrrMcrDeleteIrData  (void);
void    McrirModifyInterRM  (UMeasurementControl_latest_IEs *ie_p);

void    McrirStartInterRM   (UMeasurementIdentity       measId,
                             Boolean                    handover3G_2GFail);

void    McrirStopInterRM    (UMeasurementIdentity       measId,
                             Boolean                    leaveDch,
                             Boolean                    handover);

void    McrirDeleteInterRM  (McrGsmMeas                 *interRM_p);

void    McrirStoreAndStartMCInterRM (UInterRATMeasurement_latest *interFM_p,
                                     UMeasurementIdentity     measId);

void    McrirStoreSibGsmCells (Int8               noOfGsmCells,
                               CsrMcrGsmNCellInfo *sibCellInfo_p);

//void    McrirHandleInterFreqHHO     (void);
void    McrirHandleFailInterFreqHHO (void);
Boolean McrirActiveSetMeasNeeded (UInterRATReportCriteria *rep_p);
void McrAllocateMemForMeaRepV770NonCriticalExt(
                                            UrrSirAllocatedMemList *memList_p,
                                            UMeasurementReport *measRep_p);

/*****************************************************************************
 * GSM Measurements Manager related functions (defined in urrmcrmg.c).
 */

void    McrmgBsicGuardTimerExpiry      (void);
void    McrmgInitData                  (Boolean dchState);
void    McrmgInitDchState              (Boolean activation);
void    McrmgClrBsicRequiredSet        (void);
void    McrmgSetDchState               (Boolean dchState);
void    McrmgSortListTimerExpiry       (void);
void    McrmgStateChangeToFromCellFach (void);
void    McrmgTbarredBsicTimerExpiry    (Int8 timerId);

#endif

/*****************************************************************************
 * Ue Positioning measurements related functions (defined in urrmcrup.c).
 */
Boolean McrupConfigComplete (UUE_Positioning_Measurement_latest *up_p);
void McrupStoreAndStartMCUePositionMeas (UUE_Positioning_Measurement_latest  *up_p, UMeasurementIdentity measId);
void McrupStopUePositionMeas (UMeasurementIdentity measId);
void McrupDeleteUePositionMeas (McrUePositionMeas *pos_p);
void McrupModifyUePositionMeas (UMeasurementControl_latest_IEs *ie_p);

void McrupAgpsUlDataReq (RrcPsUlDataReq *req_p);

void McrupHandleAssistanceDataDeliveryMsg (UrrInternalSignalProcessRxAirSignal *sig_p);

void McrupFachToDch (UMeasurementIdentity measId);
void McrupDchToFachUraPch (UMeasurementIdentity measId);
void McrupFachToPch (UMeasurementIdentity measId);
void McrupPchToFach (UMeasurementIdentity measId);

void McrupAgpsDlDataRsp (RrcPsDlDataRsp *rsp_p);

/* ********** begin */
void McrupHandleCphyUeRxTxTimeDiffType2MeasInd (UMeasurementIdentity measId, 
                                                CphyUeRxTxTimeDiffType2MeasurementInd  *cphyMeasInd_p);

void McrupHandleUePositioningTimerExpiry (TimerId timerId, UMeasurementIdentity measId);
/* ********** end */

/*****************************************************************************
 * Common functions, defined in file urrmcr.c.
 */

UCPICH_RSCP  McrMapRscp     (SignedInt16 rscpVal, UDeltaRSCPPerCell* deltaRSCP);

UCPICH_Ec_N0 McrMapEcn0     (SignedInt16 ecn0Val);
UPathloss    McrMapPathloss (UPathloss   pathloss);

Boolean McrValidFilter (UFilterCoefficient filterCoeff,
                        McrFilters         *filters_p);

void    McrHandleFilterCoeff (Int8       *filterIndex,
                              McrFilters *filters);

Int8    UrrMcrFilterCoefficientEnumToInt (UFilterCoefficient tag);
Int16   UrrMcrTimeToTriggerEnumToInt (UTimeToTrigger tag);
void    UrrMcrSortMeasValListAsc (McrMeasValList *list_p);
void    McrSortMeasValListDsc    (McrMeasValList *list_p);

void    McrStartPeriodicTimer (UMeasurementIdentity measId,
                               KiTimer              *timer_p,
                               Int32                reportingInterval);

void    McrModifyMeasControlCommonData (UMeasurementControl_latest_IEs *ie_p,
                                        McrMeasControlMsg          *meas_p);

void    UrrMcrBuildCmacAdditionalIdentities (
                                 UAdditionalMeasurementID_List *addMeasList_p,
                                 CmacAdditionalIdentities      *additional_p);

void    UrrMcrSendMeasurementReport (UrrSirAllocatedMemList *memList_p,
                                     UMeasurementReport *measRep_p,
                                     UTransferMode transferMode,
                                     Int16 seqNo);

void    McrBuildAdditionalMeasRes (
                           UrrSirAllocatedMemList        *memList_p,
                           UAdditionalMeasurementID_List *measControlAdd_p,
                           UMeasurementReport            *measRep_p,
                           Int16                          seqNo,
                           Boolean                       *sendMacReq);

void    McrStopMeas       (UMeasurementIdentity measId, Boolean leaveDch);
void    McrDeleteMeasType (McrMeasType          *meas_p);
void    McrDeleteMeas     (UMeasurementIdentity measId);

void    UrrMcrUpdateEngineeringModeDB (void);


Boolean McriaIsIntraDetectedActive (void);  /* CQ00079722 added */

#endif

/* END OF FILE */

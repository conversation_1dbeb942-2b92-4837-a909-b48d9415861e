/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/devices/transports/usb.mod/api/inc/usberr.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/24 10:16:13 $
 **************************************************************************/
/** \file
 * USB Error codes
 **************************************************************************/
#if defined(UPGRADE_USB)

#ifndef USBERR_H
#define USBERR_H

/** \addtogroup PrdDevicesUsb
 * Error codes used in interface signals.
 * @{
 */

/** Error codes used within USB module and interfaces */
typedef enum UsbErrorCodeTag
{
  USB_ERROR_NONE,

  /* Errors associated with device requests and configuration. */
  USB_ERROR_UNRECOGNISED_DEVICE_REQUEST_TYPE,
  USB_ERROR_SET_DESCRIPTOR_NOT_SUPPORTED,
  USB_ERROR_SYNC_FRAME_NOT_SUPPORTED,
  USB_ERROR_GET_INTERFACE_FAILURE,
  USB_ERROR_SET_INTERFACE_FAILURE,
  USB_ERROR_UNRECOGNISED_STANDARD_REQUEST,
  USB_ERROR_GET_STATUS_REQUEST_ERROR,
  USB_ERROR_GET_STATUS_BEHAVIOUR_UNSPECIFIED,
  USB_ERROR_CLEAR_FEATURE_FAILURE,
  USB_ERROR_CLEAR_FEATURE_REQUEST_ERROR,
  USB_ERROR_CLEAR_FEATURE_BEHAVIOUR_UNSPECIFIED,
  USB_ERROR_SET_FEATURE_FAILURE,
  USB_ERROR_SET_FEATURE_REQUEST_ERROR,
  USB_ERROR_SET_FEATURE_BEHAVIOUR_UNSPECIFIED,
  USB_ERROR_SET_ADDRESS_REQUEST_ERROR,
  USB_ERROR_SET_ADDRESS_BEHAVIOUR_UNSPECIFIED,
  USB_ERROR_GET_DESCRIPTOR_REQUEST_ERROR,
  USB_ERROR_GET_DESCRIPTOR_BEHAVIOUR_UNSPECIFIED,
  USB_ERROR_SET_DESCRIPTOR_REQUEST_ERROR,
  USB_ERROR_SET_DESCRIPTOR_BEHAVIOUR_UNSPECIFIED,
  USB_ERROR_GET_CONFIGURATION_REQUEST_ERROR,
  USB_ERROR_GET_CONFIGURATION_BEHAVIOUR_UNSPECIFIED,
  USB_ERROR_SET_CONFIGURATION_REQUEST_ERROR,
  USB_ERROR_SET_CONFIGURATION_BEHAVIOUR_UNSPECIFIED,
  USB_ERROR_GET_INTERFACE_REQUEST_ERROR,
  USB_ERROR_GET_INTERFACE_BEHAVIOUR_UNSPECIFIED,
  USB_ERROR_SET_INTERFACE_REQUEST_ERROR,
  USB_ERROR_SET_INTERFACE_BEHAVIOUR_UNSPECIFIED,
  USB_ERROR_SYNC_FRAME_REQUEST_ERROR,
  USB_ERROR_SYNC_FRAME_BEHAVIOUR_UNSPECIFIED,
  USB_ERROR_COULD_NOT_FIND_REQUESTED_DESCRIPTOR,
  USB_ERROR_UNSUPPORTED_VENDOR_DEVICE_REQUEST,
  USB_ERROR_SET_ADDRESS_IN_WRONG_DEVICE_STATE,

  /* Errors associated with data transmission. */
  USB_ERROR_TRANSMIT_REQUEST_SUCCESS,
  USB_ERROR_TRANSMIT_REQUEST_DEVICE_UNCONFIGURED,
  USB_ERROR_TRANSMIT_REQUEST_ENDPOINT_INVALID,
  USB_ERROR_TRANSMIT_REQUEST_ENDPOINT_HALTED,
  USB_ERROR_TRANSMIT_REQUEST_QUEUE_FULL,
  USB_ERROR_TRANSMIT_REQUEST_QUEUE_NOT_INITIALISED,

  /* Errors associated with device connection. */
  USB_ERROR_DEVICE_CONTROLLER_ATTACHED,
  USB_ERROR_DEVICE_CONTROLLER_ALREADY_CONNECTED,
  USB_ERROR_DEVICE_CONTROLLER_NOT_PRESENT,

  /* Errors associated with the USB task. */
  USB_ERROR_UNEXPECTED_SIGNAL,

  /* The total number of defined errors. */
  USB_ERROR_NUMBER_OF_ERRORS

} UsbErrorCode;

/** @} */ /* End of group */


/*******************************************************************************
 * Global Function Prototypes
 ******************************************************************************/

extern void usbNotifyError(UsbErrorCode errorCode, Boolean isISR);
extern void usbHandleError(UsbErrorCode errorCode);

#endif  /* !defined (USBERR_H) */

#endif /* defined(UPGRADE_USB) */
/* END OF FILE */


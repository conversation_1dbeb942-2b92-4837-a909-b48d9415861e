/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/*--------------------------------------------------------------------------------------------------------------------
INTEL CONFIDENTIAL
Copyright 2006 Intel Corporation All Rights Reserved.
The source code contained or described herein and all documents related to the source code ("Material") are owned
by Intel Corporation or its suppliers or licensors. Title to the Material remains with Intel Corporation or
its suppliers and licensors. The Material contains trade secrets and proprietary and confidential information of
Intel or its suppliers and licensors. The Material is protected by worldwide copyright and trade secret laws and
treaty provisions. No part of the Material may be used, copied, reproduced, modified, published, uploaded, posted,
transmitted, distributed, or disclosed in any way without Intel's prior express written permission.

No license under any patent, copyright, trade secret or other intellectual property right is granted to or
conferred upon you by disclosure or delivery of the Materials, either expressly, by implication, inducement,
estoppel or otherwise. Any license under such intellectual property rights must be express and approved by
Intel in writing.
-------------------------------------------------------------------------------------------------------------------*/

/******************************************************************************
*               MODULE IMPLEMENTATION FILE
*******************************************************************************
* Title: Voice path data
*
* Filename: vpath_data.c
*
* Authors: <AUTHORS>
*
* Description: DSP I/F data buffers
*
* Last Updated:
*
* Notes:
******************************************************************************/
#ifndef VOICE_PATH_DATA_H_
#define VOICE_PATH_DATA_H_

/*----------- Local include files --------------------------------------------*/
#include "gbl_types.h"
#include "mmap.h"   //for pcm buffers convert virtual to physical address
/*----------- Global defines -------------------------------------------------*/

#define PCM_STREAM_USE_DOUBLE_BUFFER

/*----------- Global constant definitions ------------------------------------*/
#define MAX_SPEECH_DATA_FRAME_SIZE	160 //for PCM streaming
#define MAX_CODED_NB_SPEECH_PACKET	19  //for vocoder streaming
#define MAX_CODED_WB_SPEECH_PACKET  33  //for wb-amr vocoder streaming
#define MAX_TONE_PACKET				7  //for tone streaming
/*----------- Global macro definitions ---------------------------------------*/


/*----------- Global type definitions ----------------------------------------*/

/*----------- Extern definition ----------------------------------------------*/
#ifndef _VOICE_PATH_DATA_NO_EXTERN_
  #define EXTERN extern
#else
  #define EXTERN
#endif /* _VOICE_PATH_DATA_NO_EXTERN_ */



/*----------- Global variable declarations -----------------------------------*/
//for PCM streaming
#if defined (PCM_STREAM_USE_DOUBLE_BUFFER)
#ifdef OPT_IPC
EXTERN UINT16 *_pcmUpLinkBuffer1;
EXTERN UINT16 *_pcmUpLinkBuffer2;
EXTERN UINT16 *_pcmDownLinkBuffer1;
EXTERN UINT16 *_pcmDownLinkBuffer2;
#else /*OPT_IPC*/
EXTERN __align(4) UINT16 _pcmUpLinkBuffer1[MAX_SPEECH_DATA_FRAME_SIZE * 2 /*WB-AMR*/];
EXTERN __align(4) UINT16 _pcmUpLinkBuffer2[MAX_SPEECH_DATA_FRAME_SIZE * 2 /*WB-AMR*/];
EXTERN __align(4) UINT16 _pcmDownLinkBuffer1[MAX_SPEECH_DATA_FRAME_SIZE * 2 /*WB-AMR*/];
EXTERN __align(4) UINT16 _pcmDownLinkBuffer2[MAX_SPEECH_DATA_FRAME_SIZE * 2 /*WB-AMR*/];
#endif /*OPT_IPC*/
#ifdef PHS_SW_DEMO_TTC
UINT32 COMM2MSA(UINT32 addr);
UINT32 MSA2COMM(UINT32 addr);
#define _pcmUpLinkBufferPhysicalNonCached1 ((UINT16 *) COMM2MSA((UINT32)&_pcmUpLinkBuffer1[0]))
#define _pcmUpLinkBufferPhysicalNonCached2 ((UINT16 *) COMM2MSA((UINT32)&_pcmUpLinkBuffer2[0]))
#define _pcmDownLinkBufferPhysicalNonCached1 ((UINT16 *) COMM2MSA((UINT32)&_pcmDownLinkBuffer1[0]))
#define _pcmDownLinkBufferPhysicalNonCached2 ((UINT16 *) COMM2MSA((UINT32)&_pcmDownLinkBuffer2[0]))
#else
#define _pcmUpLinkBufferPhysicalNonCached1 ((UINT16 *) VirtualToPhysical((void *)(CACHED_TO_NON_CACHED_OFFSET + ((UINT32)&_pcmUpLinkBuffer1[0]))))
#define _pcmUpLinkBufferPhysicalNonCached2 ((UINT16 *) VirtualToPhysical((void *)(CACHED_TO_NON_CACHED_OFFSET + ((UINT32)&_pcmUpLinkBuffer2[0]))))
#define _pcmDownLinkBufferPhysicalNonCached1 ((UINT16 *) VirtualToPhysical((void *)(CACHED_TO_NON_CACHED_OFFSET + ((UINT32)&_pcmDownLinkBuffer1[0]))))
#define _pcmDownLinkBufferPhysicalNonCached2 ((UINT16 *) VirtualToPhysical((void *)(CACHED_TO_NON_CACHED_OFFSET + ((UINT32)&_pcmDownLinkBuffer2[0]))))
#endif

#else /*PCM_STREAM_USE_DOUBLE_BUFFER*/

#ifdef OPT_IPC
EXTERN UINT16 *_pcmUpLinkBuffer1;
EXTERN UINT16 *_pcmDownLinkBuffer1;
#else /*OPT_IPC*/
EXTERN __align(4) UINT16 _pcmUpLinkBuffer1[MAX_SPEECH_DATA_FRAME_SIZE * 2 /*WB-AMR*/];
EXTERN __align(4) UINT16 _pcmDownLinkBuffer1[MAX_SPEECH_DATA_FRAME_SIZE * 2 /*WB-AMR*/];
#endif /*OPT_IPC*/
#define _pcmUpLinkBufferPhysicalNonCached1 ((UINT16 *) VirtualToPhysical((void *)(CACHED_TO_NON_CACHED_OFFSET + ((UINT32)&_pcmUpLinkBuffer1[0]))))
#define _pcmUpLinkBufferPhysicalNonCached2 ((UINT16 *) VirtualToPhysical((void *)(CACHED_TO_NON_CACHED_OFFSET + ((UINT32)&_pcmUpLinkBuffer1[0]))))
#define _pcmDownLinkBufferPhysicalNonCached1 ((UINT16 *) VirtualToPhysical((void *)(CACHED_TO_NON_CACHED_OFFSET + ((UINT32)&_pcmDownLinkBuffer1[0]))))
#define _pcmDownLinkBufferPhysicalNonCached2 ((UINT16 *) VirtualToPhysical((void *)(CACHED_TO_NON_CACHED_OFFSET + ((UINT32)&_pcmDownLinkBuffer1[0]))))
#endif //PCM_STREAM_USE_DOUBLE_BUFFER*/

//for vocoder streaming
EXTERN __align(4) unsigned short _codedUpLinkFrame[MAX_CODED_WB_SPEECH_PACKET + 1];
EXTERN __align(4) unsigned short _codedDownLinkFrame[MAX_CODED_WB_SPEECH_PACKET];

//for tone streaming
EXTERN unsigned short _toneBuff[MAX_TONE_PACKET];

/*----------- Global API function definition ---------------------------------*/
void vpathDataInit(void);
void vpathCodedDataInit(void);

#undef EXTERN
#endif  /* VOICE_PATH_DATA_H_ */



/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/sys/tmm.mod/api/inc/tmm.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/24 11:37:14 $
 **************************************************************************
 * File Description: Traffic Memory Manager
 **************************************************************************/
#ifndef TMM_H
#define TMM_H


/***************************************************************************
 * Build Checks
 **************************************************************************/
#define TMM_BUILD_CHECK
#include <tmmmem.h>


#if defined (ENABLE_TMM)
/***************************************************************************
 * Nested Include Files
 **************************************************************************/
#include <system.h>
#include <tmmtyp.h>

/***************************************************************************
 * Type Definitions
 **************************************************************************/

/***************************************************************************
 *  Macros
 **************************************************************************/

/***************************************************************************
 *  Function Prototypes
 **************************************************************************/
extern Boolean TmmAllocMemory (TmmPoolId poolId, Int32 requestedBytes, Int8 **mem_pp);
extern Int32 TmmAllocBestMemoryAvail (TmmPoolId poolId, Int32 requestedBytes, Int32 minBytes, Int8 **mem_pp);
extern void TmmFreeMemory (Int8 **mem_pp);
extern void TmmFreeTail (Int32 len, Int8 **mem_pp);
extern void TmmAddFlowControlTask (TmmPoolId poolId, TaskId taskId);
extern void TmmRemoveFlowControlTask (TmmPoolId poolId, TaskId taskId);
extern void TmmInit (void);
extern Int32 TmmGetBlockLength (Int8 *mem_p);
extern Boolean TmmCanAllocFromPool (TmmPoolId poolId);
extern Boolean TmmSetCurrentProfile (TmmProfileId profileId);

#if defined (TMM_ENABLE_REFERENCES)
extern void TmmIncrementBlockRef (Int8 *mem_p);
extern Int8 TmmGetBlockRefs (Int8 *mem_p);
#endif

extern TmmPoolId TmmGetBlockPoolId (Int8 *mem_p);

#if defined (TMM_ENABLE_INTEGRITY_CHECK)
extern void TmmCheckAllocBlockIntegrity (void);
#endif

/*Added by Mason 20091110 for tracing CQ00002147 and CQ00002181 */
extern void TmmTotalBytesAllocated(void);

/*Fix the CQ00031703 start xyzheng 2013-04-03*/
extern void TmmSetHwmAllocCount(TmmPoolId poolId, Int32 hwmAllocCount);
/*Fix the CQ00031703 end xyzheng 2013-04-03*/

#endif /* ENABLE_TMM */
#endif
/* END OF FILE */

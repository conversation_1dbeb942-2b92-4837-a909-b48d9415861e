/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/tplgsm/utinc/ut_ucsembed.h#8 $
 *   $Revision: #8 $
 *   $DateTime: 2006/12/12 19:03:51 $
 **************************************************************************
 * File Description
 * ----------------
 *   Include file for Unicode embedding level functions
 **************************************************************************/

#ifndef UT_UCSEMBED_H
#define UT_UCSEMBED_H


/***************************************************************************
 * Nested Include Files
 **************************************************************************/
#ifndef PC_TTCN_INT_TEST
#include <utinc/ut_ucsdata.h>
#else
#include <ut_ucsdata.h>
#endif
/***************************************************************************
 * Manifest Constants
 **************************************************************************/

/***************************************************************************
 * Typed Constants
 **************************************************************************/

/***************************************************************************
 * Type Definitions
 **************************************************************************/

/* A structure for holding the embedding level and (modified)
 * character types for a string */
typedef struct
{
  Int8 level;      /* Embedding level (0-61) */
  E_UCSBidiType type;  /* Type of the character */
} T_UCSEmbeddingInfo;

/* Status flags for the embedding levels calculation return code */
typedef enum
{
  E_EMBED_OK,     /* All OK */
  E_EMBED_BUFFER  /* Bad buffers provided to the routine */
} E_UCSEmbedCalcRC;

/* Computes the langauge text attribute from the first none neutral character. 
 * Currently all numbers and symbols are regarded as neutral text
 *
 * @param ap_text [in] is a pointer to the input text string.
 * @param a_length [in] is the length of the input string.
 *
 * @return value - BIDI type E_L, E_R, E_ON are possible return values
 *
 */
E_UCSBidiType calcTextBidiType(const Int16 *ap_text,
                                        const Int16 a_length);






























                         
#endif /* UPGRADE_UCS_BIDI  */
/* END OF FILE */

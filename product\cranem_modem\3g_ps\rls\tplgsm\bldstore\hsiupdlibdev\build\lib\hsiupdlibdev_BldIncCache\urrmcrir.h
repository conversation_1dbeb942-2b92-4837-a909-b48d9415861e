/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urrmcrir.h#3 $
 *   $Revision: #3 $
 *   $DateTime: 2006/12/15 14:47:38 $
 **************************************************************************
 * File Description:
 *
 ***************************************************************************
 *
 * Revision Details
 **************************************************************************/

#if !defined (URRMCRIR_H)
#define       URRMCRIR_H

#if !defined (UPGRADE_EXCLUDE_2G)

/***************************************************************************
* Nested Include Files
***************************************************************************/
#include <urrmcrty.h>

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/

/***************************************************************************
*   Macro Functions
***************************************************************************/

/***************************************************************************
 * Types
 ***************************************************************************/
typedef struct IrMeasValDataTag
{
    Int8                           cellIndex;
    SignedInt16                    gsm_CarrierRSSI;
    Boolean                        bsicVerified;
    UBSIC                          bsic;
}
IrMeasValData;

typedef struct IrMeasValListTag
{
    Int8           n;
    IrMeasValData  data [maxCellMeas];
}
IrMeasValList;

typedef struct IrRawValuesTag
{
    SignedInt16                    gsm_CarrierRSSI;
    Boolean                        bsicVerified;
    UBSIC                          bsic;
}
IrRawValues;

typedef struct IrFilterValuesTag
{
    SignedInt32                    gsm_CarrierRSSI;
}
IrFilterValues;

typedef struct IrMeasResultsTag
{
    IrRawValues                    rawValues;
    IrFilterValues                 filterValues [MAX_FILTERS];
}
IrMeasResults;

//ICAT EXPORTED STRUCT
typedef struct IrMeasStatusTag
{
    CellIdMask                     gsm_CarrierRSSI;
    Int32                          gsm_CarrierRSSIupdated;
    Int32                          gsm_CarrierRSSIEventUpdated;
    Int32                          bsicUpdated;
}
IrMeasStatus;

//ICAT EXPORTED STRUCT
typedef struct IrMeasCountsTag
{
    Int8                           noOfGsm_CarrierRSSImeas;
}
IrMeasCounts;

//ICAT EXPORTED STRUCT
typedef struct IrCellDataTag
{
    UGsmCell                       gsmCellInfo;
    UFrequency_Band                gsmBandIndicator;
    /* modified for CQ00005946 start */
    // UInterRATCellIndividualOffset  interRATCellIndividualOffset;
    SignedInt16                    interRATCellIndividualOffset;
    /* modified for CQ00005946 end */
    Int8                           measResIndex;
    IrMeasCounts                   measCounts;
}
IrCellData;

//ICAT EXPORTED STRUCT
typedef struct IrCellInfoTag
{
    IrCellData     cellData;
}
IrCellInfo;

typedef struct IrMeasInfoTag
{
    Int32          measResultsMask;  /* Bit mask indicating which locations in
                                        measResults are occupied.     */
    IrMeasResults  measResults [UPS_MAX_MEASURED_INTER_RAT_CELLS];
                                     /* Measurement results.          */
    IrMeasStatus   measStatus;       /* Ongoing measurements status.  */
}
IrMeasInfo;

typedef struct IrMeasConfigTag
{
    CellIdMask          occupied;
    CellIdMask          oldOccupied;
    IrCellInfo          cellInfo [maxCellMeas];
    UFrequency_Band     gsmBandIndicator;
    IrMeasInfo          *measInfo;
    McrFilters          filters;
    McrBestCell3dEvent  currentBestCell3dEvent;
    UUARFCN             newFreq;
}
IrMeasConfig;

typedef struct IrMeasDataTag
{
    CellIdMask          occupied;       /* Indicates which positions are
                                         * occupied in inter-RAT CELL_INFO_LIST. */
    CellIdMask          oldOccupied;    /* Old monitor set.                  */
    CellIdMask          changed;        /* Cells changed in CELL_INFO_LIST*/
    IrCellInfo          cellInfo [maxCellMeas]; /* Inter-RAT CELL_INFO_LIST. */
    UFrequency_Band     gsmBandIndicator;
    IrMeasInfo          *measInfo;      /* Ongoing inter-RAT measurements data.   */
    McrFilters          filters;
    McrBestCell3dEvent  currentBestCell3dEvent;
    IrMeasConfig        *savedConfig;
	Boolean				interRatCellInfoIndicationStatus;
	UInterRATCellInfoIndication		interRatCellInfoIndication;
}
IrMeasData;

/***************************************************************************
 * Typed Constants
 ***************************************************************************/

/***************************************************************************
 * Function Prototypes
 ***************************************************************************/
void       McrirGetMeasValue (McrGsmMeas     *interRM_p,
                              IrMeasResults  *measRes_p,
                              IrMeasValData  *measData_p);

IrMeasData *McrirGetIrMeasDataPtr  (void);

void        McrirInitMgData        (void);
McrGsmMeas *McrirGetGsmMeasPtr     (void);
/*add for CQ00009291 start*/
void McrirUpdateEvent3AStatus (UMeasurementIdentity measId);
/*add for CQ00009291 end*/

#endif  /* UPGRADE_EXCLUDE_2G */
void McrirExtractGsmReportCriteria (
                          UInterRATReportCriteria *src_p,
                          UInterRATReportCriteria **dest_p_p);
void McrirDeleteGsmReportCriteria (UInterRATReportCriteria    *rep_p);

void McrirInitCellInfoList (void); /*added for DMCR DEVELOPMENT*/

UGSM_CarrierRSSI McrirMapGsmCarrierRssiMeasurements (SignedInt16 meas);//CQ00068137

#endif  /* URRMCRIR_H */




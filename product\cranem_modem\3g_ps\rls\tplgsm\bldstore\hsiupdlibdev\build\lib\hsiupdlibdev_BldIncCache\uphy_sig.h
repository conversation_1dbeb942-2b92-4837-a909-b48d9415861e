/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/l1/3g.typ/api/inc/uphy_sig.h#4 $
 *   $Revision: #4 $
 *   $DateTime: 2007/03/30 13:53:25 $
 *************************************************************************/
/** \file
 * 3G L1 Primitive Data Type definitions for the UPHY Interface
 *************************************************************************/

#if !defined (UPHY_SIG_H)
#define       UPHY_SIG_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <u1_typ.h>
#include <ki_typ.h>

/***************************************************************************
*   Manifest Constants
***************************************************************************/

/** \addtogroup 3G_UPHY
 *
 * This section defines all the primitives exchanged on the data plane of MAC
 * and 3G L1, hereinafter called the PHY interface, to enable transmission and
 * reception of data and for synchronisation on the uplink and the downlink.
 *
 * The PHY interface is illustrated in the figure below:
 *
 * All the primitive definitions are based on the recommendations defined in
 * <tt>[3G TS 25.302 section 10]</tt> with additions to fill any areas not
 * covered by these recommendations.
 *
 * The timing of the primitives in downlink and uplink is as explained below.
 *
 * <B> Timing in Downlink </B>
 *
 * The following figure illustrates how the #PhyDataInd reports the actual
 * frame number of when the BCCH data was received.
 *
 * The position of the #PhyDataInd relative to the received data will be
 * physical layer dependent.
 *
 * The timing in dedicated mode is exactly the same except that frame numbers
 * are reported using the CFN.
 *
 * <B> Timing in Uplink </B>
 *
 * The following figure illustrates the timing relationship for data
 * transmission.
 *
 * The timing of the #PhyFrameInd is such that the protocol stack can respond
 * with the #PhyDataReq before the end of the current frame. The above indicates
 * a typical physical layer solution where the data is encoded in the next
 * frame and transmitted in the following frame. The frame delay from receipt
 * of the #PhyDataReq and transmitting the data will be physical layer
 * dependent. The protocol stack must know the actual delay, as the MAC needs
 * to know the transmission frame number for ciphering. In the above example
 * the MAC ciphers the above data with CFN=3.
 *
 * @{
 */



/** The power left indicated in a #PhyFrameInd primitive for a slot where there
 * is a compressed mode gap.
 */
#define UPHY_POWER_LEFT_TX_OFF              SCHAR_MIN

/** The length of data shown in #PhyDebugDataInfoInd and #PhyDebugDataInfoReq
 * primitives.
 */
#define UPHY_DEBUG_DATA_LENGTH              10

#if defined (PS_L2_R8_API)
#define UPHU_HSDSCH_MAX_TB_PER_TTI			2
#endif
/***************************************************************************
*   Macro Functions
***************************************************************************/

/***************************************************************************
*   Types
***************************************************************************/

 /** Indicates the various states in which the PHY can reside.
  * \c PHY_STATE_IDLE also encompasses the RRC states CELL_PCH and URA_PCH.
 */
typedef enum UPhyStateTag
{
    /** PHY is in NULL state. */
    PHY_STATE_NULL                          = 0,

    /** PHY is in IDLE state. */
    PHY_STATE_IDLE                          = 1,

    /** PHY is in CELL_FACH state. */
    PHY_STATE_FACH                          = 2,

    /** PHY is in CELL_DCH state. */
    PHY_STATE_DCH                           = 3

} UPhyState;

/** Indicates the result (success/failure) of the #PhyAccessReq primitive.
 */
typedef enum URachResultTag
{
    /** Data was sent successfully. */
    UPHY_RACH_RESULT_DATA_SENT_SUCCESSFULLY = 0,

    /** Maximum power reached during ramping cycles. */
    UPHY_RACH_RESULT_REACHED_MAX_POWER      = 1,

    /** NACK received on AICH for PRACH preamble, and is only used in FDD. */
    UPHY_RACH_RESULT_NACK_ON_AICH_RECEIVED  = 2,

    /** Backoff timer when a NACK is received. */
    UPHY_RACH_RESULT_TIMEOUT                = 3,

    /** RACH procedure was aborted by MAC with \c PhyAccessReq primitive. */
    UPHY_RACH_RESULT_ABORT                  = 4,

    /** This value is used only to allow <tt> GCF test case 7.1.2.4a [3G TS
     * 34.123] </tt> pass.
     */
    UPHY_RACH_RESULT_NO_SUBCHANNEL_ASSIGNED = 5
}
URachResult;


/** Indicates the event occurred in the physical layer, which is to be notified
 * to the higher layers.
 */
typedef enum UEventValueTag
{
    /** Failure in hardware */
    PHY_EV_HARDWARE_FAILURE                 = 0,

    /** Failure to decode BCH */
    PHY_EV_BCH_DECODE_FAILURE               = 1,

    /** Failure to detect SFN */
    PHY_EV_SFN_DETECT_FAILURE               = 2,

    /** Failure to decode PCH */
    PHY_EV_PCH_DECODE_FAILURE               = 3,

    /** Failure to decode FACH */
    PHY_EV_FACH_DECODE_FAILURE              = 4,

    /** Failure to decode DCH */
    PHY_EV_DCH_DECODE_FAILURE               = 5
}
UEventValue;


/** Indicates layer 1 status in #PhyDataInd primitive. */
typedef enum UphChStatusTag
{
    /** Status is OK */
    UPHCH_STATUS_OK                         = 0,

    /** Failure to decode TFCI */
    UPHCH_STATUS_TFCI_DECODE_FAIL           = 1,

    /** Failure to decode in BTFD */
    UPHCH_STATUS_BTFD_FAIL                  = 2
}
UphChStatus;

#if !defined (PS_L2_R8_API)
/**\struct PhyAccessReq
 * \brief Access to a RACH transport channel request.
 * This primitive is used by the MAC to request access to a RACH transport
 * channel from the physical layer, and to send data to the physical layer to
 * be sent in the RACH channel if required. This primitive differs from that
 * in <tt>[3G TS 25.302 section 10]</tt> in including data to be sent in the
 * RACH, so that it is available to the physical layer at the beginning of
 * the random access procedure. This primitive initiates the random access
 * procedure.
 * \note Unlike #PhyDataReq data, the TMM data block passed to the physical
 * layer in #PhyAccessReq is not de-allocated by the interface adapter, as MAC
 * may attempt retransmission of the same data (due to back-off or timeout
 * waiting for response on AICH).
 */
typedef struct PhyAccessReqTag
{
    /** Indicates MAC to stop a physical layer RACH access before a
     * #PhyAccessCnf is received. Hence two #PhyAccessReq (s) may be sent
     * consecutively by MAC, if the RACH transmission procedure is aborted by
     * higher layers. Once the procedure is aborted in L1, L1 should send a
     * second #PhyAccessCnf primitive.
     */
    Boolean                                 abortRachAccess;

    /** Indicates the Access Service Class to be employed in the physical layer
     * during RACH access. Range 0 to 7.
     */
    Int8                                    accessServiceClassIndex;

    /** Indicates the Transport Format Combination Index to be used for sending
     * the data. Range 0 to (#UPS_MAX_UL_TFC - 1).
     */
    Int16                                   tfci;

    /** Indicates the gain factor
     * <em style='font-family:Symbol'>b</em><em><sub><small>c</small></sub></em>
     * to be used in weighing the control part of PRACH for the selected TFC.
     */
    UGainFactor                             gainFactorBetaC;

    /** Indicates the gain factor
     * <em style='font-family:Symbol'>b</em><em><sub><small>d</small></sub></em>
     * to be used in weighing the data part of PRACH for the selected TFC.
     */
    UGainFactor                             gainFactorBetaD;

    /** Indicates the spreading factor used in the physical layer, and it is in
     * the range of 32 to 256.
     */
    USpreadingFactor                        spreadingFactor;

    /** Indicates the power offset between the last transmitted preamble and
     * the control part of the message. This is generally added to the
     * preamable power to receive the power of the message control part.
     */
    UPowerOffsetPp_m                        powerOffsetPp_m;
#if defined(DATA_IN_SIGNAL)

    /** Indicates all of the transport blocks for this TTI. And it is used for
     * testing with genie to provide visibility of the data in the primitive. */
    UUlPduList                              pduList;
#else
    /** Indicates a reference to a TMM data block, the protocol stack is
     * responsible for allocation of static memory during initialisation. One
     * static \c UUlPduList is required per CCTrCh, but the same \c UUlPduList
     * may also be used for #PhyDataReq. L1 shall not modify any of the data
     * referenced by \c pduList_p except the parameter \c isConsumed.
     */
    UUlPduList                            * pduList_p;
#endif /* DATA_IN_SIGNAL */
}
PhyAccessReq;
#endif /*PS_L2_R8_API*/


/**\struct PhyAccessCnf
 * \brief Access to a RACH transport channel confirmation.
 * This primitive is used to confirm that data has been sent in the RACH
 * transmission if data was sent using the PhyAccessReq primitive. This
 * primitive is always sent, whether transmission is successful, unsuccessful
 * (NACK on AICH, or timeout waiting for response on AICH), or aborted due to
 * reconfiguration by RRC or explicit abort by MAC. Refer to #PhyAccessReq
 * primitive.
 */
typedef struct PhyAccessCnfTag
{
    /** Indicates if the RACH access attempt is ACK/NACK/NOACK. */
    URachResult                             rachResult;
}
PhyAccessCnf;


/**\struct PhyDataReq
 * \brief Transmit data on physical channel request.
 * This primitive is used to send data from the MAC to the physical layer for
 * transmission. One #PhyDataReq primitive is sent every shortest configured
 * transmission time interval. One #PhyDataReq primitive is used for all
 * configured transport channels. There is one #PhyDataReq primitive per coded
 * composite transport channel (DPCHs).
 */
typedef struct PhyDataReqTag
{
    /** Indicates the TFCI to be used for data transmission. It is in the range
     * 0 to (#UPS_MAX_UL_TFC - 1).
     */
    Int16                                   tfci;

    /** Indicates the gain factor
     * <em style='font-family:Symbol'>b</em><em><sub><small>c</small></sub></em>
     * used for weighing the DPCCH.
     * This gain factor is always the gain factor used with no compressed mode,
     * even during compressed frames.
     */
    UGainFactor                             gainFactorBetaC;

    /** Indicates the gain factor
     * <em style='font-family:Symbol'>b</em><em><sub><small>d</small></sub></em>
     * used for weighing the DPDCHs.
     * This gain factor is always the gain factor used with no compressed mode,
     * even during compressed frames.
     */
    UGainFactor                             gainFactorBetaD;

    /** Indicates the spreading factor for the selected TFC. MAC ensures that
     * the TFC selected is not lower than the minimum configured spreading
     * factor. If compressed mode by higher layer scheduling occurs in this TTI
     * and the number of gaps causes a reduction in spreading factor, MAC shall
     * indicate the reduced spreading factor. If compressed mode by spreading
     * factor reduction (SF/2) occurs in this TTI MAC indicates the normal
     * spreading factor as if compressed mode were not configured.
     */
    USpreadingFactor                        spreadingFactor;
#if defined(UPS_CFG_UL_DATA_CLASS_768KBPS)
    Int8                                    numberOfDpdchs;
#endif /* UPS_CFG_UL_DATA_CLASS_768KBPS */
#if defined (DATA_IN_SIGNAL)
    /** Indicates all of the transport blocks for this TTI. And it is used for
     * testing with genie to provide visibility of the data in the primitive.
     */
    UUlPduList                              pduList;
#else /* DATA_IN_SIGNAL */
    /** Indicates a reference to a TMM data block, the protocol stack is
     * responsible for allocation of static memory during initialisation. One
     * static \c UUlPduList is required per CCTrCh, but the same \c UUlPduList
     * may also be used for #PhyAccessReq.
     */
    UUlPduList                            * pduList_p;
#endif /* DATA_IN_SIGNAL */

    /** Indicates the CFN at which the data should be sent on air. */
    Int8                                    ulCfn;
}
PhyDataReq;

/**\struct PhyDataInd
 * \brief Data received indication.
 * This primitive is used to send data from the physical layer to the MAC.
 * One #PhyDataInd primitive is sent every shortest configured transmission
 * time interval for all transport channels in CELL_DCH and CELL_FACH. There is
 * one #PhyDataInd primitive per coded composite transport channel (P-CCPH,
 * S-CCPCH, DPCHs). This primitive is also used to synchronise new downlink
 * configurations (with respect to activation time).
 */
typedef struct PhyDataIndTag
{

    /** Indicates the downlink frequency of the cell on which data is being
     * indicated to RRC. Together with \c primaryScramblingCode RRC will
     * determine if the BCH data is from the serving or a neighbour cell.
     */
    UUARFCN                                 uarfcn_DL;

    /** Indicates the PSC of the cell on which the data is being indicated to RRC.
     */
    UPrimaryScramblingCode                  primaryScramblingCode;

    /** Indicates either the SFN or CFN in which this data was received. Note
     * that in the case the transport channel was longer than a 10ms TTI, the
     * last frame occupied by this TTI defines the frame number. This parameter
     * is used by MAC (and passed on to RLC and RRC) for synchronisation of
     * new downlink configurations with respect to activation time.
     */
    UFrameNumber                            dlFrameNumber;

    /** Indicates if the status is <tt> ok/TFCI failure/BTFD failure</tt>. */
    UphChStatus                             phChStatus;
#if defined (DATA_IN_SIGNAL)
    /** Indicates all of the transport blocks for this TTI. And it is used for
     * testing with genie to provide visibility of the data in the primitive.
     */
    UDlPduList                              pduList;
#endif /* DATA_IN_SIGNAL */
    /** Indicates a reference to a TMM data block. L1 is responsible for
     * allocation of static memory during initialisation. One static
     * UDlPduList is required per CcTrCh.
     */
    UDlPduList                            * pduList_p;
}
PhyDataInd;


#if defined(DEVELOPMENT_VERSION)

/**\struct PhyDebugDataInfoInd
 * \brief Log #PhyDataInd primitive.

 * Allows logging of the #PhyDataInd primitive sent by L1 to the protocol
 * stack. This primitive is only logged if the number of transport blocks
 * received in a given TTI is greater than zero. Each primitive logged contains
 * the first <tt> UPHY_DEBUG_DATA_LENGTH </tt> bytes of the data. Logging is
 * turned on using \c RLCdec by selecting <tt> Filter > MAC > PhyDataInd </tt>.
 */
typedef struct PhyDebugDataInfoIndTag
{
    UUARFCN                                 uarfcn_DL;
    UPrimaryScramblingCode                  primaryScramblingCode;
    UFrameNumber                            dlFrameNumber;
    UphChStatus                             phChStatus;
    Int8                                    numberOfTransportBlocks;
    UDlPduListInfo                          pduListInfo[UPS_MAX_DL_NO_OF_TB_PER_TTI];
    Int16                                   byteLength;
    Int8                                    data[UPHY_DEBUG_DATA_LENGTH];
}
PhyDebugDataInfoInd;

/**\struct PhyDebugDataInfoReq
 * \brief Log #PhyDataReq primitive.
 
 * Allows logging of the #PhyDataReq primitive sent to L1 by the protocol
 * stack. This primitive is only logged if the number of transport blocks
 * transmitted in a given TTI is greater than zero. Each primitive logged
 * contains the first <tt> UPHY_DEBUG_DATA_LENGTH </tt> bytes of the data.
 * Logging is turned on using \c RLCdec by selecting <tt> Filter > MAC >
 * PhyDataReq </tt>.
 */
typedef struct PhyDebugDataInfoReqTag
{
    Int8                                    cfn;
    Int16                                   tfci;
    UGainFactor                             gainFactorBetaC;
    UGainFactor                             gainFactorBetaD;
    USpreadingFactor                        spreadingFactor;
#if defined(UPS_CFG_UL_DATA_CLASS_768KBPS)
    Int8                                    numberOfDpdchs;
#endif /* UPS_CFG_UL_DATA_CLASS_768KBPS */
    Int8                                    numberOfTransportBlocks;
    UUlPduListInfo                          pduListInfo[UPS_MAX_UL_NO_OF_TB_PER_TTI];
    Int16                                   byteLength;
    Int8                                    data[UPHY_DEBUG_DATA_LENGTH];
}
PhyDebugDataInfoReq;
#endif /* DEVELOPMENT_VERSION */

/**\struct PhyStatusIndTag
 * \brief Status indication to MAC.
 * This primitive is used by the physical layer to indicate to the MAC that
 * an event has occurred.
 */
typedef struct PhyStatusIndTag
{
    /** Indicates an enumeration constant #UEventValue, which describes the
     * event occurred.
     */
    UEventValue                             eventValue;
}
PhyStatusInd;

/**\struct PhyFrameInd
 * \brief Tick from L1 sent every radio frame (10ms).
 * This primitive is sent by the physical layer to the MAC to request data
 * for transmission at a specific radio frame.  It is also used by the protocol
 * stack to handle uplink reconfigurations, which are to be processed at a
 * network specified <tt>Activation time</tt>.
 *
 * One #PhyFrameInd primitive is sent every radio frame (10ms) in RRC states
 * CELL_FACH and CELL_DCH. \note #PhyFrameInd primitive is not sent during
 * initial FACH, hence an initial RACH access cannot rely on receiving
 * #PhyFrameInd primitive. During power saving #PhyFrameInd primitive is also
 * not sent. During these times the MCU uses internal 10ms signals for timing
 */
typedef struct PhyFrameIndTag
{
    /** Indicates the uplink CFN used for synchronisation of uplink
     * configurations (using activation time) in MAC (and subsequently RLC and
     * RRC). It also indicates that the physical layer is requesting data for
     * CFN \c ulCfn (to which MAC replies with a #PhyDataReq, if a new TTI has
     * started).
     */
    Int8                                    ulCfn;

    /** Indicates whether synchronisation has been achieved on dedicated
     * channels, and the SRB delay is complete. Once it is set TRUE, MAC starts
     * transmitting on the DCH transport channels. If synchronisation is lost,
     * it is set FALSE and MAC stops requesting more data to be sent.
     */
    Boolean                                 dchReadyToSend;

    /** Indicates the power difference (in dB) between the maximum allowed
     * power and the power required to transmit the control part in the
     * previous radio frame. In CELL_DCH it indicates the calculated power
     * remaining on DPCCH prior to clipping in each of the slots in the
     * previous frame as
     * <em>P<sub><small>max</small></sub> -
     * P<sub><small>DPCCH</small></sub></em>.
     * In CELL_FACH it indicates
     * the same value for each slot as
     * <em>P<sub><small>max</small></sub> -
     * P<sub><small>reamble_Initial_Power</small></sub></em>.
     * <tt> [3G TS 25.331 section 8.5.7]</tt>.
     * <em>P<sub><small>max</small></sub></em> is the minimum of
     * the maximum allowed power and the maximum UE transmitter power. The value
     * range is between the minimum and maximum power output of the UE <tt> [3G
     * TS 25.101 sections 6.2.1 and *******] </tt> i.e. less than -50 dBm to up
     * to 33 dBm. If the transmitter was off due to compressed mode gaps in the
     * previous frame, L1 should use the special value <tt>
     * UPHY_POWER_LEFT_TX_OFF </tt> in those slots. Where HS-DPCCH may also be
     * transmitted, layer 1 must include the difference these transmissions make
     * on the powerLeft calculation, i.e. the calculation for each slot becomes
     * <em>P<sub><small>max</small></sub> -
     * P<sub><small>DPCCH</small></sub> -
     * P<sub><small>HS-DPCCH</small></sub></em>.
     */
    SignedInt16                              powerLeft[UPS_SLOTS_PER_FRAME];
}
PhyFrameInd;

/**\struct PhyStateInd
 * \brief PHY state change indication.
 * This primitive is sent by the physical layer to the MAC to indicate PHY
 * state changes.
 */
typedef struct PhyStateIndTag
{
    /** Indicates the new PHY state following an internal PHY state change. */
    UPhyState                               phyState;

    /** Indicates the SFN at which the state change takes into effect. */
    Int16                                   sfn;

} PhyStateInd;


#if defined(UPGRADE_3G_HSDPA)
/** Defines HS unassigned signal.
 */
typedef EmptySignal PhyHsUnassignPointerInd;

/** Defines the size of the header required by L1 in addition to the MAC-hs
 * PDU data.
 */
#define UPHY_MAC_HS_PDU_L1_HDR_LEN          12 /* (sizeof(PhyHsDataInd)) */

/** Defines the length of the header in bytes required by L2 in order to
 * efficiently process the received MAC-hs PDU.
 */
#define UPHY_MAC_HS_PDU_L2_HDR_LEN          12

/** Defines the length in bytes of the additional space allocated by MAC-hs in
 * order to ensure the space is available to L1 & L2 for efficient processing
 * in these layers. This is Max(UPHY_MAC_HS_PDU_L1_HDR_LEN,
 * UPHY_MAC_HS_PDU_L2_HDR_LEN).
 */
#define UPHY_MAC_HS_PDU_ADDITIONAL_SPACE    16

/*@}*/ //3G_UPHY


/** Defines the MAC-hs PDU received on HS-DSCH. Only one data block can be
 * received each 2ms TTI.
 */
typedef struct UmacHsDataTag
{
    /** The \c data array size is the maximum MAC-hs PDU size, which is
     * dependent on the HS-DSCH capability as defined in TS 25.306 (see table
     * below). The data array also allows for any additional space required
     * internally by MAC-hs and layer 1. The actual decoded data always starts
     * at position data[UPHY_MAC_HS_PDU_ADDITIONAL_SPACE] in this array
     * (starting with "Version Flag" bits as defined in <tt> [TS 25.321 section
     * 9.2.2]</tt>. Note that the data array always starts on a 32-bit
     * boundary.
     *
     * <TABLE>
     * <TR><TD><B>Data Class (Mbps)</B></TD> <TD><B>Max bits per TTI</B></TD> <TD><B>Max MAC-hs SDUs per TTI</B></TD></TR>
     * <TR><TD>1.2 (cat. 1)            </TD> <TD>7298                   </TD> <TD>70                            </TD></TR>
     * <TR><TD>3.6 (cat. 5)            </TD> <TD>7298                   </TD> <TD>70                            </TD></TR>
     * <TR><TD>7 (cat. 7)              </TD> <TD>14411                  </TD> <TD>70                            </TD></TR>
     * <TR><TD>10 (cat. 9)             </TD> <TD>20251                  </TD> <TD>70                            </TD></TR>
     * <TR><TD>category 10             </TD> <TD>27952                  </TD> <TD>70                            </TD></TR>
     * </TABLE>
     *
     * The constant <tt> UDL_HS_MAX_MAC_HS_PDU_DATA_OCTETS </tt> is set
     * according to HS-DSCH category using the maximum bits per TTI as given
     * above.
     */
    Int8                                    data[UDL_HS_MAX_MAC_HS_PDU_DATA_OCTETS + UPHY_MAC_HS_PDU_ADDITIONAL_SPACE];
}
UmacHsData;

#if defined (PS_L2_R8_API)
typedef struct PhyHsTbMetaDataTag
{
	Int8	*macHsData_p;
	Int16	 bitLength;
	Int8	 byteAlignOffset;
	Int8	 hrntiReceivedInd;
	Int8	 macType;
}PhyHsTbMetaData;
#endif
/**\addtogroup 3G_UPHY */
/*@{*/

/**\struct PhyHsDataInd
 * \brief Data received on HS-PDSCH indication.
 * This primitive is sent by the physical layer to the MAC to send data from
 * layer 1 on HS-DSCH. It may be sent up to every 2ms (i.e. every HS-TTI) when
 * HS-DSCH is configured. It is not necessary for this message to be sent if no
 * data is received on HS-DSCH.
 */
typedef struct PhyHsDataIndTag
{
#if defined (PS_L2_R8_API)
	Int8						numTbs;
	PhyHsTbMetaData				tbMetaData[UPHU_HSDSCH_MAX_TB_PER_TTI];
#else
    //Int8						macType;
    /* Pointer to the begining of the HS data, starting from
         * the Physical Header */
	Int8						*phyHsdataHeader_p;
#endif
}
PhyHsDataInd;
/*@}*/ //3G_UPHY

#if defined (UPGRADE_3G_EDCH)
typedef struct PhyEdchRxChDataIndTag
{
	Int32 ticksCounter;
	Int8 cfn; // 10ms
	Int8 subframe;
	Int16 reserve;
	Int32 dspRestrictionInt32[2];//must be greater than or equal  to  etfcRestrictionDspResults size
}PhyEdchRxChDataInd;              

#if defined (UPGRADE_UL_ECF)
typedef enum UEdchRachResultTag
{
    /** Data was sent successfully */
    UPHY_EDCH_RACH_RESULT_ACK    = 0,

    /** Maximum power reached during ramping cycles */
    UPHY_EDCH_RACH_RESULT_NO_ACK = 1,

    /** NACK received on AICH for PRACH preamble */
    UPHY_EDCH_RACH_RESULT_NACK   = 2
}
UEdchRachResult;

typedef struct PhyEdchAccessCnfTag
{
    /** Indicates if the RACH access attempt is ACK/NACK/NOACK. */
    UEdchRachResult     edchRachResult;
}PhyEdchAccessCnf;

typedef EmptySignal PhyEdchCommonResourceRelInd;

#endif

#endif /* UPGRADE_3G_EDCH */


#if defined(ON_PC) || defined(EXCLUDE_HAW)
/** This primitive is sent by MAC to pass a memory reference to layer 1 when
 * ON_PC is defined.
 */
typedef struct PhyHsAssignPointerReqOnPcTag
{
    /** Indicates the pointer to the buffer allocated by MAC. */
    UmacHsData *umacHsData_p;
}
PhyHsAssignPointerReqOnPc;
#else /* ON_PC */

/** Used by MAC to pass a memory reference to layer 1 so that HS-DSCH data can
 * be written directly into a free position of MAC's reordering buffer.
 * Unlike #PhyDataInd (where layer 1 "owns" statically allocated memory as
 * PduLists), MAC "owns" semi-statically allocated memory areas depending on
 * MAC-hs queue configuration. Ownership is temporarily passed from MAC to L1
 * when this function is called, and is then passed back to MAC when L1 sends
 * the #PhyHsDataInd primitive. This function is only used when data is not
 * passed in primitive. This function shall be called by MAC prior to the
 * configured Activation Time for HS-DSCH (i.e. on reception of \c
 * CmacHsQueueConfigReq primitive, not at the time it is activated). The number
 * of buffers required by L1 is the constant \c UPS_CFG_PHY_MAX_POINTER_ASSIGN
 * in \c uphy_cfg.h (a build-time configurable parameter). Layer 1 shall remove
 * references to any remaining HS-data buffers when it receives
 * #CphyDlTrChReleaseReq primitive for HS-DSCH.
 */
extern void PhyHsAssignPointerReq(UmacHsData *umacHsData_p);
#endif /* ON_PC */

#endif /* UPGRADE_3G_HSDPA */

#endif

/* END OF FILE */


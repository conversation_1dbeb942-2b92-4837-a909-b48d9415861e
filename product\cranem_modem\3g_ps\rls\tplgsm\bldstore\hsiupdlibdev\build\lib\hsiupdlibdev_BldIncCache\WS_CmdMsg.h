/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/


/************************************************************************/
/*                                                                      */
/* Title: IPC Comm. pacakge header file                                 */
/*                                                                      */
/* Filename:  CmdMsg.h                                                 */
/*                                                                      */
/* Authors: <AUTHORS>
/*                                                                      */
/* Target, subsystem: Common Platform, HAL                              */
/************************************************************************/
#ifndef _WS_CmdMsg_H_

  #define _WS_CmdMsg_H_
/*----------------------------------------------------------------------*/

#include "global_types.h"
#include "WS_IPCCommConfig.h"
#include "WS_IPCComm.h"

/*----------------------------------------------------------------------*/

/********************* Sub-Package Definitions ********************/

/*#define SUB_OPCODE_SHIFT    0
#define SUB_OPCODE_MASK     0x003F

#define SET_ID_SHIFT        6
#define SET_ID_MASK         0x03C0

#define DATA_BIT_SHIFT      13
#define DATA_BIT_MASK       0x2000

#define URGENT_BIT_SHIFT    14
#define URGENT_BIT_MASK     0x4000       */

/************ macros *******************/
/*
#define CREATE_OPCODE_FIELD(o,so,si,d,u) \
    { \
        (o) = (o) | ( ((so) << SUB_OPCODE_SHIFT) | ((si) << SET_ID_SHIFT) | ((d) << DATA_BIT_SHIFT) | ((u) << URGENT_BIT_SHIFT) ); \
    }

#define EXTRACT_SUB_OPCODE(o)   ( ((o) & SUB_OPCODE_MASK) >> SUB_OPCODE_SHIFT )  // extracting the sub-opcode out of a given full op-code field
#define EXTRACT_SET_ID(o)       ( ((o) & SET_ID_MASK) >> SET_ID_SHIFT )          // extracting the SET ID out of a given full op-code field
#define EXTRACT_DATA_BIT(o)     ( ((o) & DATA_BIT_MASK) >> DATA_BIT_SHIFT )      // extracting the Data bit out of the opcode field
#define EXTRACT_URGENT_BIT(o)   ( ((o) & URGENT_BIT_MASK) >> URGENT_BIT_SHIFT )  // extracting the Urgent bit out of the opcode field

#define FRAGMENT_OPCODE_FIELD(o,so,si,d,u) \
    { \
        so = EXTRACT_SUB_OPCODE(o); \
        si = EXTRACT_SET_ID(o); \
        d = EXTRACT_DATA_BIT(o); \
        u = EXTRACT_URGENT_BIT(o); \
    }
 */

// send message notification
#define SEND_MESSAGE_NOTIFICATION(si,so,op,ln,dt) \
    {   \
        if (_msgRegDB[si][so] != NULL) \
            _msgRegDB[si][so]->callbackMsgNotify(op,ln,dt); \
    }

#ifdef SPY_CMD
// send spy command notification
#define SEND_SPY_CMD_NOTIFICATION(op,ln,pp,dt) \
    {   UINT8 so, si; \
        si = EXTRACT_SET_ID(op); \
        so = EXTRACT_SUB_OPCODE(op); \
        if (_spyCmdRegDB[si][so] != NULL) \
            _spyCmdRegDB[si][so]->callbackNotifySpyCmd(op,ln,pp,dt); \
    }

#endif


/*********************** Sub-Package Parameters *************************/


/*************** Prototypes *****************/
void IPCSendRdIntToDSP(void);
void cmdMsgCreateOSObjects(void);
void cmdMsgReturnOSObjects(void);
IPC_ReturnCode commandSendToDSP(IPC_CmdMsgParams *cmdToSend, ApplicationID sourceID, UINT32 *dataHeader);


/************************ IPC MSG printing Parameters *************************/
/*#define IPC_MSG_PRINT_DEBUG*/
#ifdef IPC_MSG_PRINT_DEBUG

//ICAT EXPORTED STRUCT
typedef struct
{
	UINT32 IpcMsgNo;
	UINT32 averageIpcMsgTime;
	UINT32 maxIpcMsgTime;
	UINT32 numIpcMsg;
} IpcMsgPrintDb_ts;


#endif
/*----------------------------------------------------------------------*/

#endif  /* _WS_CmdMsg_H_ */

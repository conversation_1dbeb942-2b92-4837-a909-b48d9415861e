/**************************************************************************
 * File Description:
 *
 *    Contains URRC state machines.
 **************************************************************************/

#if !defined (URRSTATES_H)
#define       URRSTATES_H

//ICAT EXPORTED ENUM
typedef enum UrrCerStateTag
{
    CER_IDLE,
    CER_CONNECTING_WAIT_TX,
    CER_RETRYING_WAIT_TX,
    CER_WAIT_CONNECTION_SETUP,
    CER_WAIT_SETUP_COMPLETE_TX,
    CER_REJECTED_WAIT,
    CER_REJECTED_NEW_FREQ_WAIT_SELECTION,
    CER_REJECTED_SAME_FREQ_WAIT_SIBS,
    CER_REJECTED_DIFF_FREQ_WAIT_SIBS,
    CER_REJECTED_NEW_RAT_WAIT_SELECTION,
    CER_DISC_DCH_WAIT_TX,
    CER_DISC_FACH_WAIT_TX,
    CER_CONNECTED,
    CER_WAIT_IDT_TX_CONFIRM,
    CER_WAIT_RBC_PROCESS_RRC_CONN_SETUP,
    CER_WAIT_RACH_MEAS_RESULTS,
    CER_WAIT_RBC_PROCESS_RRC_CON_RELEASE_FACH,
    CER_WAIT_RBC_PROCESS_RRC_CON_RELEASE_DCH,
    CER_DISC_DCH_WAIT_ALL_SIBS_DISTRIBUTED,
    CER_WAIT_CSR_PLMN_ABORT_CNF,
#if defined(UPGRADE_DSDS) && defined(UPGRADE_COMMON_STORE)
    CER_WAIT_IRAT_DS_SUSPEND_CNF,
#endif/*DSDS && UPGRADE_COMMON_STORE*/
    CER_WAIT_CELL_UPDATE_OCCURRED,
    CER_WAIT_RBC_MOVE_TO_FACH
}
UrrCerState;

#endif


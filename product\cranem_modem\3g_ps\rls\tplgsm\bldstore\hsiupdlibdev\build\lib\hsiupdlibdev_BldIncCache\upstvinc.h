/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/api/inc/upstvinc.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2007/03/19 13:59:30 $
 **************************************************************************
 * File Description:
 *
 * This file includes the structure definitions for 3G PS signals. It is
 * #included in tvsignal.h before the union Signal is defined, so that
 * references to signal structures in the SIG_DEF macro in upssig.h
 * (#included within union Signal) can be resolved.
 **************************************************************************/


#if !defined (UPSTVINC_H)
#define       UPSTVINC_H


#if defined (UPGRADE_3G_EDCH)
#include <umace_sig.h>
#endif

#if !defined (EXCLUDE_UMAC)
#include <umac_sig.h>
#endif

#if !defined (EXCLUDE_URLC)
#include <urlc_sig.h>
#include <urlc_dbg_sig.h>
#endif

#if !defined (EXCLUDE_CRLC)
#include <crlc_sig.h>
#endif

#if !defined (EXCLUDE_CBMC)
#include <cbmc_sig.h>
#endif

#if !defined (EXCLUDE_CPDC)
#include <cpdc_sig.h>
#include <ulbgdlbg_sig.h> 
#endif

#if !defined (EXCLUDE_CMAC)
#include <cmac_sig.h>
#endif

#if !defined (EXCLUDE_CCSD)
#include <ccsd_sig.h>
#endif

#if !defined (EXCLUDE_CSDI)
#include <csdi_sig.h>
#endif

#if !defined (EXCLUDE_RRC)
#include <rrc_sig.h>
#include <urrtypes.h>
#include <urrintsig.h>



      
#endif

#if !defined (EXCLUDE_RABMRRC)
#include <rabmrrc_sig.h>
#endif

#if !defined (EXCLUDE_GMMRABM)
#include <gmmrasig.h>
#endif

#if !defined (EXCLUDE_PMMSMS)
#include <pmmsmssig.h>
#endif

#if !defined (EXCLUDE_UPSUT)
#if defined (ENABLE_UPLANE_STATISTICS)
#include <uplanestats_sig.h>
#endif /* ENABLE_UPLANE_STATISTICS */
#endif /* EXCLUDE_UPSUT */

#if !defined (EXCLUDE_ALSU)
#include <alsu_sig.h>
#endif /* EXCLUDE_ALSU */

#if defined (NODEB_GKI_API)
#if !defined (EXCLUDE_NODEBSIG)
#include <nodeb_sig.h>
#endif
#endif

#endif

/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/***************************************************************************
 *  TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 *  Licensed to Marvell International Ltd
 ***************************************************************************
 *
 *    $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urrrieut.h#3 $
 *    $Revision: #3 $
 *    $DateTime: 2007/03/19 13:59:30 $
 *
 ***************************************************************************
 *
 *  File Description:
 *
 *     Header file for the generic IE handling functionality of the RIE
 *     RRC module.
 *
 ***************************************************************************
 *
 ***************************************************************************/

#if !defined (URRRIEUT_H)
#define       URRRIEUT_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <uas_asn.h>
#include <utper.h>          /* For PerBuffer */

/***************************************************************************
 *  Manifest Constants
 ***************************************************************************/

/***************************************************************************
 *   Macro Functions
 ***************************************************************************/
#define URRRIEUT_COPY_LIST(sRC_P, dST_P, tYPE_sRC, tYPE_dST, cOPYFUNC)   \
do{                                                         \
    tYPE_sRC *srcElem_p;                                    \
    tYPE_dST **dstElem_pp;                                  \
                                                            \
    DevAssert(sRC_P != PNULL);                              \
    DevAssert(dST_P != PNULL);                              \
                                                            \
    srcElem_p = sRC_P->firstElement;                        \
    dstElem_pp = &(dST_P->firstElement);                    \
                                                            \
    while (srcElem_p != PNULL)                              \
    {                                                       \
        PerAllocMemory (                                  \
            perBuffer_p,                                    \
            sizeof (tYPE_dST),                              \
            (void**)(dstElem_pp));                          \
                                                            \
        cOPYFUNC(                                           \
            perBuffer_p,                                    \
            &(srcElem_p->data),                             \
            &((*dstElem_pp)->data));                        \
                                                            \
        srcElem_p = srcElem_p->next;                        \
        dstElem_pp = &((*dstElem_pp)->next);                \
    }                                                       \
}while(0)


/* NOTE: DO not put a trailing ';' after the macro invocation. These macros
 * expand to implement a function body and a ';' is illegal after such code.
 * Only the ADS compiler currently reprts this as an error. */

/* MACROs to factorise out the same code that simply acts on a different type
 * to avoid code duplication. Handles messages that have been extended from
 * R3 -> R4 -> R5 -> R6.
 * Defines handlers for R3, R4, R5 and R6 handling as well as extra dispatchers
 * based on the received message protocol version. */

/* Handler for later_than_r3 extensions */
#define DECLARE_PROCESS_CRITICAL_EXTENSIONS(tYPE)                            \
static UrrRxMsgVersion Process##tYPE##_criticalExtensions (                  \
    PerBuffer * perBuffer_p,                                                 \
    tYPE * msg_p,                                                            \
    tYPE##_criticalExtensions * extensions_p);

/* Handler for criticalExtensions_criticalExtensions extensions */
#define DECLARE_PROCESS_CRITICAL_EXTENSIONS_1(tYPE)                          \
static UrrRxMsgVersion Process##tYPE##_criticalExtensions_1 (                \
    PerBuffer * perBuffer_p,                                                 \
    tYPE * msg_p,                                                            \
    tYPE##_criticalExtensions_1 * extensions_1_p);

/* Handler for criticalExtensions_criticalExtensions_criticalExtensions
 * extensions */
#define DECLARE_PROCESS_CRITICAL_EXTENSIONS_2(tYPE)                          \
static UrrRxMsgVersion Process##tYPE##_criticalExtensions_2 (                \
    PerBuffer * perBuffer_p,                                                 \
    tYPE## * msg_p,                                                          \
    tYPE##_criticalExtensions_2 * extensions_2_p);

#define DECLARE_PROCESS_CRITICAL_EXTENSIONS_3(tYPE)                          \
static UrrRxMsgVersion Process##tYPE##_criticalExtensions_3 (                \
    PerBuffer * perBuffer_p,                                                 \
    tYPE## * msg_p,                                                       \
    tYPE##_criticalExtensions_3 * extensions_3_p);

/* Handler for criticalExtensions_criticalExtensions_criticalExtensions
 * _criticalExtensions_criticalExtensions extensions */
#define DECLARE_PROCESS_CRITICAL_EXTENSIONS_4(tYPE)                          \
static UrrRxMsgVersion Process##tYPE##_criticalExtensions_4 (                \
    PerBuffer * perBuffer_p,                                                 \
    tYPE * msg_p,                                                            \
    tYPE##_criticalExtensions_4 * extensions_4);

/* Handler for criticalExtensions_criticalExtensions_criticalExtensions
 * _criticalExtensions_criticalExtensions_criticalExtensions extensions */
#define DECLARE_PROCESS_CRITICAL_EXTENSIONS_5(tYPE)                          \
static UrrRxMsgVersion Process##tYPE##_criticalExtensions_5 (                \
    PerBuffer * perBuffer_p,                                                 \
    tYPE * msg_p,                                                            \
    tYPE##_criticalExtensions_5 * extensions_5);


/* Handler for R3 messages */
#define DECLARE_PROCESS_R3(tYPE,pFIX)                                        \
static void Process##tYPE##_R3 (                                             \
    PerBuffer * perBuffer_p,                                                 \
    tYPE * msg_p,                                                            \
    tYPE##pFIX * r3_p);

/* Handler for R4 messages */
#define DECLARE_PROCESS_R4(tYPE,pFIX)                                        \
static void Process##tYPE##_R4 (                                             \
    PerBuffer * perBuffer_p,                                                 \
    tYPE * msg_p,                                                            \
    tYPE##pFIX * r4_p);

/* Handler for R5 messages */
#define DECLARE_PROCESS_R5(tYPE,pFIX)                                        \
static void Process##tYPE##_R5 (                                             \
    PerBuffer * perBuffer_p,                                                 \
    tYPE * msg_p,                                                            \
    tYPE##pFIX * r5_p);

#define DECLARE_PROCESS_R6(tYPE,pFIX)                                        \
static void Process##tYPE##_R6 (                                             \
    PerBuffer * perBuffer_p,                                                 \
    tYPE * msg_p,                                                            \
    tYPE##pFIX * r6_p);

#define DECLARE_PROCESS_R7(tYPE,pFIX)                                        \
static void Process##tYPE##_R7 (                                             \
    PerBuffer * perBuffer_p,                                                 \
    tYPE * msg_p,                                                            \
    tYPE##pFIX * r7_p);


/* Allocate memory for the _later_than_r3 type and set the tag accordingly */
#define DECLARE_ALLOCATE_LATER_THAN_R3(tYPE,pFIX)                            \
static void AllocateAndSelect##tYPE##_later_than_r3 (                        \
    PerBuffer * perBuffer_p,                                                 \
    tYPE * msg_p,                                                            \
    tYPE##pFIX * received_p);

/* Allocate memory for the _criticalExtensions_criticalExtensions type and
 * set the tag accordingly */
#define DECLARE_ALLOCATE_CRITICAL_EXTENSIONS_1(tYPE,pFIX)                    \
static void AllocateAndSelect##tYPE##_criticalExtensions_1 (                 \
    PerBuffer * perBuffer_p,                                                 \
    tYPE##_criticalExtensions * extensions_p,                                \
    tYPE##pFIX * received_p);

/* Allocate memory for the _criticalExtensions_1_criticalExtensions type and
 * set the tag accordingly */
#define DECLARE_ALLOCATE_CRITICAL_EXTENSIONS_2(tYPE,pFIX)                    \
static void AllocateAndSelect##tYPE##_criticalExtensions_2 (                 \
    PerBuffer * perBuffer_p,                                                 \
    tYPE##_criticalExtensions_1 * extensions_p,                              \
    tYPE##pFIX * received_p);

/* Allocate memory for the _r5 type and set the tag accordingly */
#define DECLARE_ALLOCATE_R5(tYPE,pFIX)                                       \
static void AllocateAndSelect##tYPE##_r5 (                                   \
    PerBuffer * perBuffer_p,                                                 \
    tYPE##_criticalExtensions_1 * extensions_1_p,                            \
    tYPE##pFIX * received_p);

/* Point R6 container of type tYPE_r6 to r6 pointer and set the tag
 * accordingly */
#define DECLARE_ALLOCATE_R6(tYPE,pFIX)                                       \
static void AllocateAndSelect##tYPE##_r6 (                                   \
    PerBuffer * perBuffer_p,                                                 \
    tYPE##_criticalExtensions_2 * extensions_2_p,                            \
    tYPE##pFIX * received_p);

/* Allocate memory for the _r4 type and set the tag accordingly */
#define DECLARE_ALLOCATE_R4(tYPE,pFIX)                                       \
static void AllocateAndSelect##tYPE##_r4 (                                   \
    PerBuffer * perBuffer_p,                                                 \
    tYPE##_criticalExtensions * extensions_1_p,                              \
    tYPE##pFIX * received_p);


/* MACROs to implement the dispatcher functions defined using the
 * DECLARE_PROCESS macros and the explicitly defined global handler. */

/* Generic dispatcher for the _later_than_r3 extended messages. Depending on
 * the received protocol version either call Process_xyz_r3 or call further
 * into the tree (R4 handler) */
#define IMPLEMENT_PROCESS(pREfIX,tYPE)                                       \
UrrRxMsgVersion pREfIX##tYPE (                                               \
    PerBuffer * perBuffer_p,                                                 \
    tYPE * msg_p)                                                            \
{                                                                            \
    UrrRxMsgVersion rxMsgVersion = URR_RX_MSG_VERSION_UNKNOWN;               \
                                                                             \
    DevAssert (msg_p != PNULL);                                              \
    switch (msg_p->tag)                                                      \
    {                                                                        \
        /* R3 message */                                                     \
        case T_##tYPE##_r3:                                                  \
            Process##tYPE##_R3 (perBuffer_p, msg_p, msg_p->choice.r3);       \
            rxMsgVersion = URR_RX_MSG_VERSION_R3;                            \
            break;                                                           \
                                                                             \
        /* Need to look into extensions to determine message version */      \
        case T_##tYPE##_later_than_r3:                                       \
            DevAssert (msg_p->choice.later_than_r3 != PNULL);                \
            rxMsgVersion = Process##tYPE##_criticalExtensions (              \
                               perBuffer_p,                                  \
                               msg_p,                                        \
                               &(msg_p->choice.later_than_r3->               \
                                     criticalExtensions));                   \
            break;                                                           \
                                                                             \
        default:                                                             \
            DevParam (msg_p->tag, 0, 0);                                     \
            break;                                                           \
    }                                                                        \
                                                                             \
    return (rxMsgVersion);                                                   \
}

/* Generic dispatcher for the _criticalExtensions extended messages. Depending
 * on the received protocol version either call Process_xyz_r4 or call further
 * into the tree (R5 handler) */
#define IMPLEMENT_PROCESS_CRITICAL_EXTENSIONS(tYPE)                          \
static UrrRxMsgVersion Process##tYPE##_criticalExtensions (                  \
    PerBuffer * perBuffer_p,                                                 \
    tYPE * msg_p,                                                            \
    tYPE##_criticalExtensions * extensions_p)                                \
{                                                                            \
    UrrRxMsgVersion rxMsgVersion = URR_RX_MSG_VERSION_UNKNOWN;               \
                                                                             \
    DevAssert (extensions_p != PNULL);                                       \
    switch (extensions_p->tag)                                               \
    {                                                                        \
        /* R4 message */                                                     \
        case T_##tYPE##_criticalExtensions_r4:                               \
            Process##tYPE##_R4 (perBuffer_p, msg_p, extensions_p->choice.r4);\
            rxMsgVersion = URR_RX_MSG_VERSION_R4;                            \
            break;                                                           \
                                                                             \
        /* Need to look into extensions to determine message version */      \
        case T_##tYPE##_criticalExtensions_criticalExtensions:               \
            rxMsgVersion = Process##tYPE##_criticalExtensions_1 (            \
                               perBuffer_p,                                  \
                               msg_p,                                        \
                               extensions_p->choice.criticalExtensions);     \
            break;                                                           \
                                                                             \
        default:                                                             \
            DevParam (extensions_p->tag, 0, 0);                              \
            break;                                                           \
    }                                                                        \
                                                                             \
    return (rxMsgVersion);                                                   \
}

/* Generic dispatcher for the _criticalExtensions_1 extended messages.
 * Depending on the received protocol version simply return R5 or R6. No
 * legacy elements are present in R5 messages, so no further processing is
 * required. */
#define IMPLEMENT_PROCESS_CRITICAL_EXTENSIONS_1(tYPE)                        \
UrrRxMsgVersion Process##tYPE##_criticalExtensions_1 (                       \
    PerBuffer * perBuffer_p,                                                 \
    tYPE * msg_p,                                                            \
    tYPE##_criticalExtensions_1 * extensions_1_p)                            \
{                                                                            \
    UrrRxMsgVersion rxMsgVersion = URR_RX_MSG_VERSION_UNKNOWN;               \
                                                                             \
    PARAMETER_NOT_USED (perBuffer_p);                                        \
    PARAMETER_NOT_USED (msg_p);                                              \
                                                                             \
    DevAssert (extensions_1_p != PNULL);                                     \
    switch (extensions_1_p->tag)                                             \
    {                                                                        \
        /* R5 message */                                                     \
        case T_##tYPE##_criticalExtensions_1_r5:                             \
            Process##tYPE##_R5 (perBuffer_p, msg_p, extensions_1_p->choice.r5);\
            rxMsgVersion = URR_RX_MSG_VERSION_R5;                            \
            break;                                                           \
                                                                             \
        /* Unhandled extension */                                            \
        case T_##tYPE##_criticalExtensions_1_criticalExtensions:             \
            rxMsgVersion = URR_RX_MSG_VERSION_R6;                            \
            break;                                                           \
                                                                             \
        default:                                                             \
            DevParam (extensions_1_p->tag, 0, 0);                            \
            break;                                                           \
    }                                                                        \
                                                                             \
    return (rxMsgVersion);                                                   \
}

/* Generic dispatcher for the _criticalExtensions_2 extended messages.
 * Depending on the received protocol version either call Process_xyz_r6
 * function or return R7 version. */
#define IMPLEMENT_PROCESS_CRITICAL_EXTENSIONS_2(tYPE)                        \
UrrRxMsgVersion Process##tYPE##_criticalExtensions_2 (                       \
    PerBuffer * perBuffer_p,                                                 \
    tYPE## * msg_p,                                                          \
    tYPE##_criticalExtensions_2 * extensions_2_p)                            \
{                                                                            \
    UrrRxMsgVersion rxMsgVersion = URR_RX_MSG_VERSION_UNKNOWN;               \
                                                                             \
    PARAMETER_NOT_USED (perBuffer_p);                                        \
    PARAMETER_NOT_USED (msg_p);                                              \
                                                                             \
    DevAssert (extensions_2_p != PNULL);                                     \
    switch (extensions_2_p->tag)                                             \
    {                                                                        \
        /* R6 message */                                                     \
        case T_##tYPE##_criticalExtensions_2_r6:                             \
            Process##tYPE##_R6 (perBuffer_p, msg_p, extensions_2_p->choice.r6);\
            rxMsgVersion = URR_RX_MSG_VERSION_R6;                            \
            break;                                                           \
                                                                             \
        /* Need to look into extensions to determine message version */      \
        case T_##tYPE##_criticalExtensions_2_criticalExtensions:             \
            rxMsgVersion = Process##tYPE##_criticalExtensions_3 (            \
                               perBuffer_p,                                  \
                               msg_p,                    \
                               extensions_2_p->choice.criticalExtensions);   \
            break;                                                           \
                                                                             \
        default:                                                             \
            DevParam (extensions_2_p->tag, 0, 0);                            \
            break;                                                           \
    }                                                                        \
                                                                             \
    return (rxMsgVersion);                                                   \
}

#define IMPLEMENT_PROCESS_CRITICAL_EXTENSIONS_3(tYPE)                        \
UrrRxMsgVersion Process##tYPE##_criticalExtensions_3 (                       \
    PerBuffer * perBuffer_p,                                                 \
    tYPE * msg_p,                                                       \
    tYPE##_criticalExtensions_3 * extensions_3_p)                            \
{                                                                            \
    UrrRxMsgVersion rxMsgVersion = URR_RX_MSG_VERSION_UNKNOWN;               \
                                                                             \
    PARAMETER_NOT_USED (perBuffer_p);                                        \
    PARAMETER_NOT_USED (msg_p);                                              \
                                                                             \
    DevAssert (extensions_3_p != PNULL);                                     \
    switch (extensions_3_p->tag)                                             \
    {                                                                        \
        /* R7 message */                                                     \
        case T_##tYPE##_criticalExtensions_3_r7:                             \
            rxMsgVersion = URR_RX_MSG_VERSION_R7;                            \
            break;                                                           \
                                                                             \
        /* R8 message */                                                     \
        case T_##tYPE##_criticalExtensions_3_criticalExtensions:             \
            rxMsgVersion = Process##tYPE##_criticalExtensions_4 (             \
                               perBuffer_p,                                   \
                               msg_p,                                         \
                               extensions_3_p->choice.criticalExtensions);\
            break;                                                           \
                                                                             \
        default:                                                             \
            DevParam (extensions_3_p->tag, 0, 0);                            \
            break;                                                           \
    }                                                                        \
                                                                             \
    return (rxMsgVersion);                                                   \
}

/* Generic dispatcher for the _criticalExtensions_4 extended messages.
 * Depending on the received protocol version either call Process_xyz_r8
 * function or return R9 version. */
#define IMPLEMENT_PROCESS_CRITICAL_EXTENSIONS_4(tYPE)                        \
UrrRxMsgVersion Process##tYPE##_criticalExtensions_4 (                       \
    PerBuffer * perBuffer_p,                                                 \
    tYPE * msg_p,                                                            \
    tYPE##_criticalExtensions_4 * extensions_4_p)                            \
{                                                                            \
    UrrRxMsgVersion rxMsgVersion = URR_RX_MSG_VERSION_UNKNOWN;               \
                                                                             \
    PARAMETER_NOT_USED (perBuffer_p);                                        \
    PARAMETER_NOT_USED (msg_p);                                              \
                                                                             \
    DevAssert (extensions_4_p != PNULL);                                     \
    switch (extensions_4_p->tag)                                             \
    {                                                                        \
        /* R8 message */                                                     \
        case T_##tYPE##_criticalExtensions_4_r8:                             \
            rxMsgVersion = URR_RX_MSG_VERSION_R8;                            \
            break;                                                           \
                                                                             \
        /* R9 message */                                                    \
        case T_##tYPE##_criticalExtensions_4_criticalExtensions:             \
            rxMsgVersion = Process##tYPE##_criticalExtensions_5 (             \
                               perBuffer_p,                                   \
                               msg_p,                                         \
                               extensions_4_p->choice.criticalExtensions);\
            break;                                                           \
                                                                             \
        default:                                                             \
            DevParam (extensions_4_p->tag, 0, 0);                            \
            break;                                                           \
    }                                                                        \
                                                                             \
    return (rxMsgVersion);                                                   \
}

/* Generic dispatcher for the _criticalExtensions_9 extended messages.
 * Depending on the received protocol version either call Process_xyz_r9
 * function or return unknown version. */
#define IMPLEMENT_PROCESS_CRITICAL_EXTENSIONS_5(tYPE)                   \
UrrRxMsgVersion Process##tYPE##_criticalExtensions_5 (                       \
    PerBuffer * perBuffer_p,                                                 \
    tYPE * msg_p,                                                            \
    tYPE##_criticalExtensions_5 * extensions_5_p)                            \
{                                                                            \
    UrrRxMsgVersion rxMsgVersion = URR_RX_MSG_VERSION_UNKNOWN;               \
                                                                             \
    PARAMETER_NOT_USED (perBuffer_p);                                        \
    PARAMETER_NOT_USED (msg_p);                                              \
                                                                             \
    DevAssert (extensions_5_p != PNULL);                                     \
    switch (extensions_5_p->tag)                                             \
    {                                                                        \
        /* R9 message */                                                     \
        case T_##tYPE##_criticalExtensions_5_r9:                             \
            rxMsgVersion = URR_RX_MSG_VERSION_R9;                            \
            break;                                                           \
                                                                             \
        /* unknown message */                                              \
        case T_##tYPE##_criticalExtensions_5_criticalExtensions:             \
            rxMsgVersion = URR_RX_MSG_VERSION_UNKNOWN;                        \
            break;                                                           \
                                                                             \
        default:                                                             \
            DevParam (extensions_5_p->tag, 0, 0);                            \
            break;                                                           \
    }                                                                        \
                                                                             \
    return (rxMsgVersion);                                                   \
}


/* Generic R3 handler. Convert to R5 message structure and process the received
 * pdu for legacy elements. */
#define IMPLEMENT_PROCESS_R3(tYPE,pFIX)                                      \
static void Process##tYPE##_R3 (                                             \
    PerBuffer * perBuffer_p,                                                 \
    tYPE * msg_p,                                                            \
    tYPE##pFIX * r3_p)                                                       \
{                                                                            \
    AllocateAndSelect##tYPE##_later_than_r3 (perBuffer_p, msg_p, r3_p);      \
    Process##tYPE##_R5 (perBuffer_p, msg_p, r3_p);                           \
}

/* Generic R4 handler. Convert to R5 message structure and process the received
 * pdu for legacy elements. */
#define IMPLEMENT_PROCESS_R4(tYPE,pFIX)                                      \
static void Process##tYPE##_R4 (                                             \
    PerBuffer * perBuffer_p,                                                 \
    tYPE * msg_p,                                                            \
    tYPE##pFIX * r4_p)                                                       \
{                                                                            \
    AllocateAndSelect##tYPE##_criticalExtensions_1 (                         \
        perBuffer_p,                                                         \
        &(msg_p->choice.later_than_r3->criticalExtensions),                  \
        r4_p);                                                               \
    Process##tYPE##_R5 (perBuffer_p, msg_p, r4_p);                           \
}

/* Generic R5 handler. Convert to R6 message structure and process the received
 * pdu for legacy elements. */
#define IMPLEMENT_PROCESS_R5(tYPE,pFIX)                                      \
static void Process##tYPE##_R5 (                                             \
    PerBuffer * perBuffer_p,                                                 \
    tYPE * msg_p,                                                            \
    tYPE##pFIX * r5_p)                                                       \
{                                                                            \
    AllocateAndSelect##tYPE##_criticalExtensions_2 (                         \
        perBuffer_p,                                                         \
        msg_p->choice.later_than_r3->criticalExtensions.choice.criticalExtensions, \
        r5_p);                                                               \
    Process##tYPE##_R6 (perBuffer_p, msg_p, r5_p);                           \
}


/* Allocate memory for the _later_than_r3 type, set the tag and create the
 * reminder of the structure tree. */
#define IMPLEMENT_ALLOCATE_LATER_THAN_R3(tYPE,pFIX)                          \
static void AllocateAndSelect##tYPE##_later_than_r3 (                        \
    PerBuffer * perBuffer_p,                                                 \
    tYPE * msg_p,                                                            \
    tYPE##pFIX * received_p)                                                 \
{                                                                            \
    DevAssert (msg_p != PNULL);                                              \
    PerAllocMemory (perBuffer_p,                                             \
                    sizeof (tYPE##_later_than_r3),                           \
                    (void**)&(msg_p->choice.later_than_r3));                 \
    msg_p->tag = T_##tYPE##_later_than_r3;                                   \
                                                                             \
    AllocateAndSelect##tYPE##_criticalExtensions_1 (                         \
        perBuffer_p,                                                         \
        &(msg_p->choice.later_than_r3->criticalExtensions),                  \
        received_p);                                                         \
}

/* Allocate memory for the _criticalExtensions_1 type, set the tag and create
 * the remainder of the structure tree. */
#define IMPLEMENT_ALLOCATE_CRITICAL_EXTENSIONS_1(tYPE,pFIX)                  \
static void AllocateAndSelect##tYPE##_criticalExtensions_1 (                 \
    PerBuffer * perBuffer_p,                                                 \
    tYPE##_criticalExtensions * extensions_p,                                \
    tYPE##pFIX * received_p)                                                 \
{                                                                            \
    DevAssert (extensions_p != PNULL);                                       \
    PerAllocMemory (perBuffer_p,                                             \
                    sizeof (tYPE##_criticalExtensions_1),                    \
                    (void**)&(extensions_p->choice.criticalExtensions));     \
    extensions_p->tag = T_##tYPE##_criticalExtensions_criticalExtensions;    \
                                                                             \
    AllocateAndSelect##tYPE##_r5 (perBuffer_p,                               \
                                  extensions_p->choice.criticalExtensions,   \
                                  received_p);                               \
}

/* Allocate memory for the _criticalExtensions_2 type, set the tag and create
 * the remainder of the structure tree. */
#define IMPLEMENT_ALLOCATE_CRITICAL_EXTENSIONS_2(tYPE,pFIX)                  \
static void AllocateAndSelect##tYPE##_criticalExtensions_2 (                 \
    PerBuffer * perBuffer_p,                                                 \
    tYPE##_criticalExtensions_1 * extensions_1_p,                            \
    tYPE##pFIX * received_p)                                                 \
{                                                                            \
    DevAssert (extensions_1_p != PNULL);                                     \
    PerAllocMemory (perBuffer_p,                                             \
                    sizeof (tYPE##_criticalExtensions_2),                    \
                    (void**)&(extensions_1_p->choice.criticalExtensions));   \
    extensions_1_p->tag = T_##tYPE##_criticalExtensions_1_criticalExtensions;\
                                                                             \
    AllocateAndSelect##tYPE##_r6 (perBuffer_p,                               \
                                  extensions_1_p->choice.criticalExtensions, \
                                  received_p);                               \
}

/* Assign the r4 type to the top of the allocation tree */
#define IMPLEMENT_ALLOCATE_R4(tYPE,pFIX)                                     \
static void AllocateAndSelect##tYPE##_r4 (                                   \
    PerBuffer * perBuffer_p,                                                 \
    tYPE##_criticalExtensions * extensions_p,                                \
    tYPE##pFIX * received_p)                                                 \
{                                                                            \
    PARAMETER_NOT_USED (perBuffer_p);                                        \
    DevAssert (extensions_p != PNULL);                                       \
                                                                             \
    extensions_p->choice.r4 = received_p;                                    \
    extensions_p->tag = T_##tYPE##_criticalExtensions_r4;                    \
}

/* Assign the r5 type to the top of the allocation tree */
#define IMPLEMENT_ALLOCATE_R5(tYPE,pFIX)                                     \
static void AllocateAndSelect##tYPE##_r5 (                                   \
    PerBuffer * perBuffer_p,                                                 \
    tYPE##_criticalExtensions_1 * extensions_1_p,                            \
    tYPE##pFIX * received_p)                                                 \
{                                                                            \
    PARAMETER_NOT_USED (perBuffer_p);                                        \
    DevAssert (extensions_1_p != PNULL);                                     \
                                                                             \
    extensions_1_p->choice.r5 = received_p;                                  \
    extensions_1_p->tag = T_##tYPE##_criticalExtensions_1_r5;                \
}

/* Assign the r6 type to the top of the allocation tree */
#define IMPLEMENT_ALLOCATE_R6(tYPE,pFIX)                                     \
static void AllocateAndSelect##tYPE##_r6 (                                   \
    PerBuffer * perBuffer_p,                                                 \
    tYPE##_criticalExtensions_2 * extensions_2_p,                            \
    tYPE##pFIX * received_p)                                                 \
{                                                                            \
    PARAMETER_NOT_USED (perBuffer_p);                                        \
    DevAssert (extensions_2_p != PNULL);                                     \
                                                                             \
    extensions_2_p->choice.r6 = received_p;                                  \
    extensions_2_p->tag = T_##tYPE##_criticalExtensions_2_r6;                \
}

/***************************************************************************
 *  Types
 ***************************************************************************/

 /***************************************************************************
 *  Typed Constants
 ***************************************************************************/

/***************************************************************************
 *  Global Data declarations
 ***************************************************************************/

/***************************************************************************
 *  Global Function Prototypes
 ***************************************************************************/

void UrrRieUtCopyUCellIdentity_PerRL_List_In_AddInfoList (
    PerBuffer * perBuffer_p,
    const UCellIdentity_PerRL_List * src_p,
    URL_AdditionInformationList_r6 ** dst_pp,
    Boolean present);

void UrrRieUtCopyUCellIdentity_PerRL_List (
    PerBuffer * perBuffer_p,
    const UCellIdentity_PerRL_List * src_p,
    UDL_InformationPerRL_List_r6 ** dst_pp,
    Boolean present);

void UrrRieUtCopyUDL_AddReconfTransChInfo2List (
    PerBuffer * perBuffer_p,
    const UDL_AddReconfTransChInfo2List * src_p,
    UDL_AddReconfTransChInfoList_r5 ** dst_pp,
    Boolean * present_p);

void UrrRieUtCopyUDL_TPC_PowerOffsetPerRL_List (
    PerBuffer * perBuffer_p,
    const UDL_TPC_PowerOffsetPerRL_List * src_p,
    UDL_InformationPerRL_List_r6 ** dst_pp,
    Boolean present);

void UrrRieUtCopyUDL_TPC_PowerOffsetPerRL_List_In_rlAddInfoList (
    PerBuffer * perBuffer_p,
    const UDL_TPC_PowerOffsetPerRL_List * src_p,
    URL_AdditionInformationList_r6 ** dst_pp,
    Boolean present);

void UrrRieUtCopyUInitialUE_Identity (
    UInitialUE_Identity * src_p,
    UInitialUE_Identity * dst_p);

void UrrRieUtUCapabilityUpdateRequirement_r5 (
    PerBuffer * perBuffer_p,
    UCapabilityUpdateRequirement_r5 * ie_p);

void UrrRieUtUCellUpdateConfirm_r6_IEs (
        PerBuffer * perBuffer_p,
        UCellUpdateConfirm_r6_IEs * confirm_p,
        URRC_TransactionIdentifier * transaction_p);

void UrrRieUtUDL_AddReconfTransChInfoList_r5 (
        UDL_AddReconfTransChInfoList_r5 * list_p);

void UrrRieUtUDL_CommonTransChInfo_r4 (
        PerBuffer * perBuffer_p,
        UDL_CommonTransChInfo_latest * ie_p);

void UrrRieUtUDL_CommonTransChInfo_r4_dl_Parameters (
        PerBuffer * perBuffer_p,
        UDL_CommonTransChInfo_r4_dl_Parameters * ie_p);

void UrrRieUtUDL_CounterSynchronisationInfo_r5 (
        UDL_CounterSynchronisationInfo_r5 * ie_p);

void UrrRieUtUDL_DeletedTransChInfoList_r5 (
        UDL_DeletedTransChInfoList_r5 * list_p);     /* for ********** */

void UrrRieUtUMeasurementCommand_r6 (
        PerBuffer * perBuffer_p,
        UMeasurementCommand_r6 * r6_p);

void UrrRieUtURadioBearerReconfiguration_r6_IEs_specificationMode (
        PerBuffer * perBuffer_p,
        URadioBearerReconfiguration_r6_IEs_specificationMode * mode_p);

void UrrRieUtURadioBearerSetup_r6_IEs_specificationMode (
        PerBuffer * perBuffer_p,
        URadioBearerSetup_r6_IEs_specificationMode * mode_p);

void UrrRieUtURRCConnectionRelease_r4_IEs (
        URRCConnectionRelease_r4_IEs * release_p,
        URRC_TransactionIdentifier * transaction_p);

void UrrRieUtURRCConnectionSetup_r6_IEs_specificationMode (
        PerBuffer * perBuffer_p,
        URRCConnectionSetup_r6_IEs_specificationMode * mode_p);

void UrrRieUtUUE_ConnTimersAndConstants_r5 (
        UUE_ConnTimersAndConstants_r5 * r5_p);

void UrrRieUtURL_AdditionInformationList_r6 (
        PerBuffer * perBuffer_p,
        URL_AdditionInformationList_r6 * rlAddInfoList_p);

void UrrRieUtCopyUlDpchInfo (
        UUL_DPCH_Info_r6 * dst_p,
        UUL_DPCH_Info_r6 * src_p);

void UrrRieUtDlCommonInformation_r6 (UDL_CommonInformation_r6 * dlComInf_p);

void UrrRieUtProcessUDL_InformationPerRL_List_r6_LegacyIEs (
        UDL_InformationPerRL_List_r6 *rlList_p);

void UrrRieUtCopyDlInformationPerRlList_r5bis_to_r6 (
        PerBuffer * perBuffer_p,
        UDL_InformationPerRL_List_r5bis * srcList_p,
        UDL_InformationPerRL_List_r6 ** dstList_p,
        Boolean *listPresent);

void UrrRieUtCopyUlAddReconfTransChInfoList_r6 (
        PerBuffer *perBuffer_p,
        UUL_AddReconfTransChInfoList *srcList_p,
        UUL_AddReconfTransChInfoList_r6 **dstList_pp,
        Boolean listPresent);

void UrrRieUtCopyUlDeletedTransChInfoList_r6 (
        PerBuffer *perBuffer_p,
        UUL_DeletedTransChInfoList *srcList_p,
        UUL_DeletedTransChInfoList_r6 **dstList_pp,
        Boolean listPresent);

void UrrRieUtProcessUSRB_InformationSetupList2_r6_LegacyIEs (
        USRB_InformationSetupList2_r6 *srbList2_p);

void UrrRieUtProcessUSRB_InformationSetupList_r6_LegacyIEs (
        USRB_InformationSetupList_r6 *srbList_p);

void UrrRieUtProcessUUL_LogicalChannelMapping_r6_LegacyIEs (
        UUL_LogicalChannelMapping_r6 *logChMappings_p);

void UrrRieUtProcessUUL_LogicalChannelMappingList_r6_LegacyIEs (
        UUL_LogicalChannelMappingList_r6 *logChMappingsList_p);

void UrrRieUtProcessURAB_InformationSetupList_r6_LegacyIEs (
        URAB_InformationSetupList_r6 *rabList_p);

void UrrRieUtProcessURB_InformationSetupList_r6_LegacyIEs (
        URB_InformationSetupList_r6 *rbList_p);

void UrrRieUtProcessURB_InformationReconfigList_r6_LegacyIEs (
        URB_InformationReconfigList_r6 *rbList_p);

void UrrRieUtProcessURB_InformationAffectedList_r6_LegacyIEs (
        URB_InformationAffectedList_r6 *rbList_p);

void UrrRieUtCopyMBMSSessionIdentity_NCExt (
        PerBuffer *perBuffer_p,
        URadioBearerSetup_r6_IEs *r6_IEs_p,
        URAB_InformationSetupList_r6_ext *srcList_p);

void UrrRieUtCopyTimingMaintainedSyncInd_NCExt (
        UDL_CommonInformation_r6 * dlCommonInfo_p,
        Boolean dlCommonInfoPresent,
        UTimingMaintainedSynchInd * timingMaintainedSyncInd_p);

void UrrRieUtCopyPrimaryPlmnIdentity_NCExt (
        PerBuffer * perBuffer_p,
        UPLMN_Identity * primaryPlmnIdentity_p,
        UCN_InformationInfo_latest * cnInformationInfo_p,
        Boolean * cnInformationInfoPresent_p);

void UrrRieUtCopyHARQPreambleMode_NCExt (
        UHARQ_Preamble_Mode mode,
        UUL_DPCH_Info_r6 * ulDPCHInfo_p,
        Boolean ulDPCHInfoPresent);

void UrrRieUtCopyInterRATCellInfoIndication_NCExt (
        UInterRATCellInfoIndication interRATCellInfoInd,
        UMeasurementControl_r6 * r6_p);

void UrrRieUtUMeasControl_v590ext_IEs_measCommand_v590ext_NCExt (
        UMeasControl_v590ext_IEs_measCommand_v590ext * v590ext_p,
        UMeasurementControl_r6 * r6_p);

void UrrRieUtCopyUIntraFreqReportingCriteria_1b_r5_NCExt (
        PerBuffer * perBuffer_p,
        UIntraFreqReportingCriteria_1b_r5 * intraFreqReportingCriteria_1b_r5_p,
        UMeasurementControl_r6 * r6_p);

void UrrRieUtCopyUIntraFreqEvent_1d_r5_NCExt (
        UIntraFreqEvent_1d_r5 * intraFreqEvent_1d_r5_p,
        UMeasurementControl_r6 * r6_p);

void UrrRieUtCopyUDL_InformationPerRL_List_v6b0ext_to_r7(
	PerBuffer *perBuffer_p,
	UDL_InformationPerRL_List_v6b0ext *r6_list_p,
	Boolean listPresent,
	UDL_InformationPerRL_List_r7 *r7_list_p);
void UrrRieUtCopyUCipheringModeInfo_to_r7(
    UCipheringModeInfo *cipheringModeInfo_p,
    UCipheringModeInfo_r7 *r7_p);

void UrrRieUtCopyUOctetModeRLC_SizeInfoType1_to_r7(
    UOctetModeRLC_SizeInfoType1 *r6_p,
    UDL_AM_RLC_Mode_r7_dl_RLC_PDU_size *r7_p);

void UrrRieUtCopyUDL_AM_RLC_Mode_r5_to_r7(
    UDL_AM_RLC_Mode_r5 *r6_p,
    UDL_AM_RLC_Mode_r7 *r7_p);

void UrrRieUtCopyUDL_RLC_Mode_r6_to_r7(
    UDL_RLC_Mode_r6 *r6_p,
    UDL_RLC_Mode_r7 *r7_p);

void UrrRieUtCopyURLC_Info_r6_to_r7(
    URLC_Info_r6 *r6_p,
    URLC_Info_r7 *r7_p);

void UrrRieUtCopyURLC_InfoChoice_r6_to_r7(
    URLC_InfoChoice_r6 *r6_p,
    URLC_InfoChoice_r7 *r7_p);

void UrrRieUtCopyUMAC_d_FlowIdentityDCHandHSDSCH_to_r7(
    UMAC_d_FlowIdentityDCHandHSDSCH *r5_p,
    UDL_TransportChannelType_r7_dch_and_hsdsch *r7_p);

void UrrRieUtCopyUDL_TransportChannelType_r5_to_r7(
    UDL_TransportChannelType_r5 *r5_p,
    UDL_TransportChannelType_r7 *r7_p);

void UrrRieUtCopyUDL_LogicalChannelMapping_r5_to_r7(
    UDL_LogicalChannelMapping_r5 *r5_p,
    UDL_LogicalChannelMapping_r7 *r7_p);

void UrrRieUtCopyUUL_LogicalChannelMappingList_r6_to_r8(
    UUL_LogicalChannelMappings_r6 *r6_p,
    UUL_LogicalChannelMappings_r8 *r8_p);
    
void UrrRieUtCopyUDL_LogicalChannelMappingList_r5_to_r7(
    UDL_LogicalChannelMappingList_r5 *r5_p,
    UDL_LogicalChannelMappingList_r7 *r7_p);

void UrrRieUtCopyURB_MappingOption_r6_to_r8(
    URB_MappingOption_r6 *r6_p,
    URB_MappingOption_r8 *r8_p);    

void UrrRieUtCopyURB_MappingOption_r6_to_r7(
    URB_MappingOption_r6 *r6_p,
    URB_MappingOption_r7 *r7_p);

void UrrRieUtCopyURB_MappingInfo_r6_to_r7(
    URB_MappingInfo_r6 *r6_p,
    URB_MappingInfo_r7 *r7_p);

void UrrRieUtCopyUSRB_InformationSetup_r6_to_r8(
    PerBuffer * perBuffer_p,
    USRB_InformationSetup_r6 *r6_p,
    USRB_InformationSetup_r8 *r8_p);    

void UrrRieUtCopyUSRB_InformationSetup_r6_to_r7(
    PerBuffer * perBuffer_p,
    USRB_InformationSetup_r6 *r6_p,
    USRB_InformationSetup_r7 *r7_p);

void UrrRieUtCopyUSRB_InformationSetupList_r6_to_r7(
    PerBuffer * perBuffer_p,
    USRB_InformationSetupList_r6 *r6_p,
    USRB_InformationSetupList_r7 *r7_p);

void UrrRieUtCopyUSRB_InformationSetupList_r6_to_r8(
    PerBuffer * perBuffer_p,
    USRB_InformationSetupList_r6 *r6_p,
    USRB_InformationSetupList_r8 *r8_p);    

void UrrRieUtCopyURAB_Info_r6_to_r7(
    URAB_Info_r6 *r6_p,
    URAB_Info_r7 *r7_p);

void UrrRieUtCopyURB_InformationSetup_r6_to_r8(
    PerBuffer * perBuffer_p,
    URB_InformationSetup_r6 *r6_p,
    URB_InformationSetup_r8 *r8_p);

void UrrRieUtCopyURB_InformationSetup_r6_to_r7(
    PerBuffer * perBuffer_p,
    URB_InformationSetup_r6 *r6_p,
    URB_InformationSetup_r7 *r7_p);

void UrrRieUtCopyURB_InformationSetupList_r6_to_r8(
    PerBuffer * perBuffer_p,
    URB_InformationSetupList_r6 *r6_p,
    URB_InformationSetupList_r8 *r8_p);

void UrrRieUtCopyURB_InformationSetupList_r6_to_r7(
    PerBuffer * perBuffer_p,
    URB_InformationSetupList_r6 *r6_p,
    URB_InformationSetupList_r7 *r7_p);

void UrrRieUtCopyURAB_InformationSetup_r6_to_r7(
    PerBuffer * perBuffer_p,
    URAB_InformationSetup_r6 *r6_p,
    URAB_InformationSetup_r7 *r7_p);

void UrrRieUtCopyURAB_InformationSetupList_r6_to_r7(
    PerBuffer * perBuffer_p,
    URAB_InformationSetupList_r6 *r6_p,
    URAB_InformationSetupList_r7 *r7_p);

void UrrRieUtCopyURB_InformationReconfig_r6_to_r7(
    PerBuffer * perBuffer_p,
    URB_InformationReconfig_r6 *r6_p,
    URB_InformationReconfig_r7 *r7_p);

void UrrRieUtCopyURB_InformationReconfigList_r6_to_r7(
    PerBuffer * perBuffer_p,
    URB_InformationReconfigList_r6 *r6_p,
    URB_InformationReconfigList_r7 *r7_p);

void UrrRieUtCopyURB_InformationAffected_r6_to_r7(
    PerBuffer * perBuffer_p,
    URB_InformationAffected_r6 *r6_p,
    URB_InformationAffected_r7 *r7_p);

void UrrRieUtCopyURB_InformationAffectedList_r6_to_r7(
    PerBuffer * perBuffer_p,
    URB_InformationAffectedList_r6 *r6_p,
    URB_InformationAffectedList_r7 *r7_p);

void UrrRieUtCopyUUL_AddReconfTransChInformation_r6_dch_usch_to_r7(
    UUL_AddReconfTransChInformation_r6_dch_usch *r6_p,
    UUL_AddReconfTransChInformation_r7_dch_usch *r7_p);

void UrrRieUtCopyUE_DCH_AddReconf_MAC_d_Flow_non_ScheduledTransGrantInfo_to_r7(
    PerBuffer * perBuffer_p,
    UE_DCH_AddReconf_MAC_d_Flow_non_ScheduledTransGrantInfo *r6_p,
    UE_DCH_AddReconf_MAC_d_Flow_r7_non_ScheduledTransGrantInfo *r7_p);

void UrrRieUtCopyUE_DCH_AddReconf_MAC_d_Flow_transmissionGrantType_to_r7(
    PerBuffer * perBuffer_p,
    UE_DCH_AddReconf_MAC_d_Flow_transmissionGrantType *r6_p,
    UE_DCH_AddReconf_MAC_d_Flow_r7_transmissionGrantType *r7_p);

void UrrRieUtCopyUE_DCH_AddReconf_MAC_d_Flow_to_r7(
    PerBuffer * perBuffer_p,
    UE_DCH_AddReconf_MAC_d_Flow *r6_p,
    UE_DCH_AddReconf_MAC_d_Flow_r7 *r7_p);

void UrrRieUtCopyUE_DCH_AddReconf_MAC_d_FlowList_to_r7(
    PerBuffer * perBuffer_p,
    UE_DCH_AddReconf_MAC_d_FlowList *r6_p,
    UE_DCH_AddReconf_MAC_d_FlowList_r7 *r7_p);

void UrrRieUtCopyUUL_AddReconfTransChInformation_r6_e_dch_to_r7(
    PerBuffer * perBuffer_p,
    UUL_AddReconfTransChInformation_r6_e_dch *r6_p,
    UUL_AddReconfTransChInformation_r7_e_dch *r7_p);

void UrrRieUtCopyUUL_AddReconfTransChInformation_r6_to_r7(
    PerBuffer * perBuffer_p,
    UUL_AddReconfTransChInformation_r6 *r6_p,
    UUL_AddReconfTransChInformation_r7 *r7_p);

void UrrRieUtCopyUUL_AddReconfTransChInfoList_r6_to_r7(
    PerBuffer * perBuffer_p,
    UUL_AddReconfTransChInfoList_r6 *r6_p,
    UUL_AddReconfTransChInfoList_r7 *r7_p);

void UrrRieUtCopyUDL_TransportChannelIdentity_r5_to_r7(
    PerBuffer * perBuffer_p,
    UDL_TransportChannelIdentity_r5 *r5_p,
    UDL_TransportChannelIdentity_r7 *r7_p);
void UrrRieUtCopyUDL_DeletedTransChInfoList_r5_to_r7(
    PerBuffer * perBuffer_p,
    UDL_DeletedTransChInfoList_r5 *r5_p,
    UDL_DeletedTransChInfoList_r7 *r7_p);

void UrrRieUtCopyUHARQ_Info_explicit_to_r7(
    UHARQ_Info_explicit *r5_p,
    UHARQ_Info_r7_explicit *r7_p);

void UrrRieUtCopyUHARQ_Info_memoryPartitioning_to_r7(
    PerBuffer * perBuffer_p,
    UHARQ_Info_memoryPartitioning *r5_p,
    UHARQ_Info_r7_memoryPartitioning *r7_p);

void UrrRieUtCopyUHARQ_Info_to_r7(
    PerBuffer * perBuffer_p,
    UHARQ_Info *r5_p,
    UHARQ_Info_r7 *r7_p);

void UrrRieUtCopyUHSDSCH_Info_to_r9(
    PerBuffer * perBuffer_p,
    UHSDSCH_Info *r5_p,
    UHSDSCH_Info_r9 *r9_p);

void UrrRieUtCopyUHSDSCH_Info_to_r7(
    PerBuffer * perBuffer_p,
    UHSDSCH_Info *r5_p,
    UHSDSCH_Info_r7 *r7_p);

void UrrRieUtCopyUDL_AddReconfTransChInformation_r5_tfs_SignallingMode_to_r9(
    PerBuffer * perBuffer_p,
    UDL_AddReconfTransChInformation_r5_tfs_SignallingMode *r5_p,
    UDL_AddReconfTransChInformation_r9_tfs_SignallingMode *r9_p);

void UrrRieUtCopyUDL_AddReconfTransChInformation_r5_tfs_SignallingMode_to_r7(
    PerBuffer * perBuffer_p,
    UDL_AddReconfTransChInformation_r5_tfs_SignallingMode *r5_p,
    UDL_AddReconfTransChInformation_r7_tfs_SignallingMode *r7_p);

void UrrRieUtCopyUDL_AddReconfTransChInformation_r5_to_r9(
    PerBuffer * perBuffer_p,
    UDL_AddReconfTransChInformation_r5 *r5_p,
    UDL_AddReconfTransChInformation_r9 * r9_p);

void UrrRieUtCopyUDL_AddReconfTransChInformation_r5_to_r7(
    PerBuffer * perBuffer_p,
    UDL_AddReconfTransChInformation_r5 *r5_p,
    UDL_AddReconfTransChInformation_r7 * r7_p);

void UrrRieUtCopyUDL_AddReconfTransChInfoList_r5_to_r9(
    PerBuffer * perBuffer_p,
    UDL_AddReconfTransChInfoList_r5 *r5_p,
    UDL_AddReconfTransChInfoList_r9 *r9_p);
    
void UrrRieUtCopyUDL_AddReconfTransChInfoList_r5_to_r7(
    PerBuffer * perBuffer_p,
    UDL_AddReconfTransChInfoList_r5 *r5_p,
    UDL_AddReconfTransChInfoList_r7 *r7_p);

void UrrRieUtCopyURadioBearerSetup_r6_IEs_complete_to_r7(
    PerBuffer * perBuffer_p,
    URadioBearerSetup_r6_IEs_complete *r6_p,
    URadioBearerSetup_r7_IEs_complete *r7_p);

void UrrRieUtCopyURadioBearerSetup_r6_IEs_specificationMode_to_r7(
    PerBuffer * perBuffer_p,
    URadioBearerSetup_r6_IEs_specificationMode *r6_p,
    URadioBearerSetup_r7_IEs_specificationMode *r7_p);

void UrrRieUtCopyUUL_DPCH_PowerControlInfo_r6_to_r7(
    PerBuffer * perBuffer_p,
    UUL_DPCH_PowerControlInfo_r6 *r6_p,
    UUL_DPCH_PowerControlInfo_r7 *r7_p);

void UrrRieUtCopyUUL_DPCH_Info_r6_present_to_r7(
    UUL_DPCH_Info_r6_present *r6_p,
    UUL_DPCH_Info_r7_present *r7_p);

void UrrRieUtCopyUUL_DPCH_Info_r6_notPresent_to_r7(
    UUL_DPCH_Info_r6_notPresent *r6_p,
    UUL_DPCH_Info_r7_notPresent *r7_p);

void UrrRieUtCopyUUL_DPCH_Info_r6_dpdchPresence_to_r7(
    PerBuffer * perBuffer_p,
    UUL_DPCH_Info_r6_dpdchPresence *r6_p,
    UUL_DPCH_Info_r7_dpdchPresence *r7_p);

void UrrRieUtCopyUUL_DPCH_Info_r6_modeInfo_to_r7(
    PerBuffer * perBuffer_p,
    UUL_DPCH_Info_r6_fdd *r6_p,
    UUL_DPCH_Info_r7_fdd *r7_p);

void UrrRieUtCopyUUL_DPCH_Info_r6_to_r7(
    PerBuffer * perBuffer_p,
    UUL_DPCH_Info_r6 *r6_p,
    UUL_DPCH_Info_r7 *r7_p);

void UrrRieUtCopyUUL_EDCH_Information_r6_to_r7(
    PerBuffer * perBuffer_p,
    UUL_EDCH_Information_r6 *r6_p,
    UUL_EDCH_Information_r7 *r7_p);

void UrrRieUtCopyUHS_SCCH_Info_r6_fdd_to_r7(
    PerBuffer * perBuffer_p,
    UHS_SCCH_Info_r6_fdd *r6_p,
    UHS_SCCH_Info_r7_fdd *r7_p);

void UrrRieUtCopyUHS_SCCH_Info_r6_to_r7(
    PerBuffer * perBuffer_p,
    UHS_SCCH_Info_r6 *r6_p,
    UHS_SCCH_Info_r7 *r7_p);

void UrrRieUtCopyUMeasurement_Feedback_Info_fdd_to_r7(
    PerBuffer * perBuffer_p,
    UMeasurement_Feedback_Info_fdd *r6_p,
    UMeasurement_Feedback_Info_r7_fdd *r7_p);

void UrrRieUtCopyUMeasurement_Feedback_Info_to_r7(
    PerBuffer * perBuffer_p,
    UMeasurement_Feedback_Info *r6_p,
    UMeasurement_Feedback_Info_r7 *r7_p);

void UrrRieUtCopyUMeasurementCommand_r6_to_R7(
    PerBuffer * perBuffer_p,
    UMeasurementCommand_r6 * r6_p,
    UMeasurementCommand_r7 * r7_p);

void UrrRieUtCopyUMeasurementCommand_r6_modify_to_r7(
    PerBuffer * perBuffer_p,
    UMeasurementCommand_r6_modify * r6_p,
    UMeasurementCommand_r7_modify * r7_p);

void UrrRieUtCopyUMeasurementType_r6_to_r7(
    PerBuffer * perBuffer_p,
    UMeasurementType_r6 * r6_p,
    UMeasurementType_r7 * r7_p);

void UrrRieUtCopyUIntraFrequencyMeasurement_r6_to_r7(
    PerBuffer * perBuffer_p,
    UIntraFrequencyMeasurement_r6 * r6_p,
    UIntraFrequencyMeasurement_r7 * r7_p);

void UrrRieUtCopyUIntraFreqReportCriteria_r6_to_r7(
    PerBuffer * perBuffer_p,
    UIntraFreqReportCriteria_r6 * r6_p,
    UIntraFreqReportCriteria_r7 * r7_p);

void UrrRieUtCopyUIntraFreqReportingCriteria_r6_to_r7(
    PerBuffer * perBuffer_p,
    UIntraFreqReportingCriteria_r6 * r6_p,
    UIntraFreqReportingCriteria_r7 * r7_p);

void UrrRieUtCopyUIntraFreqEventCriteriaList_r6_to_r7(
    PerBuffer * perBuffer_p,
    UIntraFreqEventCriteriaList_r6 * r6_p,
    UIntraFreqEventCriteriaList_r7 * r7_p);

void UrrRieUtCopyUIntraFreqEventCriteria_r6_to_r7(
    PerBuffer * perBuffer_p,
    UIntraFreqEventCriteria_r6 * r6_p,
    UIntraFreqEventCriteria_r7 * r7_p);

void UrrRieUtCopyUIntraFreqEvent_r6_to_r7(
    PerBuffer * perBuffer_p,
    UIntraFreqEvent_r6 * r6_p,
    UIntraFreqEvent_r7 * r7_p);

void UrrRieUtCopyUEvent1b_r4_to_r7(
    PerBuffer * perBuffer_p,
    UEvent1b_r4 * r4_p,
    UEvent1b_r7 * r7_p);

void UrrRieUtCopyUInterFrequencyMeasurement_r6_to_r7(
    PerBuffer * perBuffer_p,
    UInterFrequencyMeasurement_r6 * r6_p,
    UInterFrequencyMeasurement_r7 * r7_p);

void UrrRieUtCopyUInterFreqReportCriteria_r6_to_r7(
    PerBuffer * perBuffer_p,
    UInterFreqReportCriteria_r6 * r6_p,
    UInterFreqReportCriteria_r7 * r7_p);

void UrrRieCopyUPeriodicReportingInfo_1b_to_r7(
    PerBuffer * perBuffer_p,
    UIntraFreqReportingCriteria_1b_r5 * r5_p,
    UIntraFreqEventCriteriaList_r7 * r7_p);

void UrrRieUtCopyUDL_HSPDSCH_Information_r6_to_r7(
    PerBuffer * perBuffer_p,
    UDL_HSPDSCH_Information_r6 *r6_p,
    UDL_HSPDSCH_Information_r7 *r7_p);

void UrrRieCopyUDL_HSPDSCH_Information_r6_modeInfo_to_r7(
    PerBuffer * perBuffer_p,
    UDL_HSPDSCH_Information_r6_modeInfo *r6_p,
    UDL_HSPDSCH_Information_r7_modeSpecificInfo *r7_p);
void UrrRieUtCopyUDL_CommonInformation_r6_dl_dpchInfoCommon_to_r7(
    UDL_CommonInformation_r6_dl_dpchInfoCommon *r6_p,
    UDL_CommonInformation_r7_dl_dpchInfoCommon *r7_p);

void UrrRieUtCopyUDL_CommonInformation_r6_modeInfo_to_r7(
    UDL_CommonInformation_r6_modeInfo *r6_p,
    UDL_CommonInformation_r7_modeSpecificInfo *r7_p);

void UrrRieUtCopyUDL_CommonInformation_r6_to_r7(
    UDL_CommonInformation_r6 *r6_p,
    UDL_CommonInformation_r7 *r7_p);

void UrrRieUtCopyUDL_InformationPerRL_r6_modeInfo_to_r7(
    UDL_InformationPerRL_r6_modeInfo *r6_p,
    UDL_InformationPerRL_r7_modeSpecificInfo *r7_p);

void UrrRieUtCopyUDL_FDPCH_InfoPerRL_r6_to_r7(
    UDL_FDPCH_InfoPerRL_r6 *r6_p,
    UDL_FDPCH_InfoPerRL_r7 *r7_p);

void UrrRieUtCopyUDL_InformationPerRL_r6_dl_dpchInfo_to_r7(
    UDL_InformationPerRL_r6_dl_dpchInfo *r6_p,
    UDL_InformationPerRL_r7_dl_dpchInfo *r7_p);

void UrrRieUtCopyUE_AGCH_Information_to_r7(
    UE_AGCH_Information *r6_p,
    UE_AGCH_Information_r7 *r7_p);

void UrrRieUtCopyUDL_InformationPerRL_r6_to_r7(
    PerBuffer * perBuffer_p,
    UDL_InformationPerRL_r6 *r6_p,
    UDL_InformationPerRL_r7 *r7_p);

void UrrRieUtCopyUDL_InformationPerRL_List_r6_to_r7(
    PerBuffer * perBuffer_p,
    UDL_InformationPerRL_List_r6 *r6_p,
    UDL_InformationPerRL_List_r7 *r7_p);

void UrrRieUtCopyUIntegrityProtectionModeInfo_to_r7(
    UIntegrityProtectionModeInfo *r6_p,
    UIntegrityProtectionModeInfo_r7 *r7_p);

void UrrRieUtCopyUCellUpdateConfirm_r6_IEs_to_r7(
    PerBuffer * perBuffer_p,
    UCellUpdateConfirm_r6_IEs * r6_p,
    UCellUpdateConfirm_r7_IEs * r7_p);

void UrrRieUtCopyUDL_DPCH_InfoPerRL_r6_to_r7(
    UDL_DPCH_InfoPerRL_r6 *r6_p,
    UDL_DPCH_InfoPerRL_r7 *r7_p);

void UrrRieUtCopyUE_DPDCH_Info_to_r7(
    UE_DPDCH_Info * r6_p,
    UE_DPDCH_Info_r7 * r7_p);

#endif /* !defined (URRRIEUT_H) */

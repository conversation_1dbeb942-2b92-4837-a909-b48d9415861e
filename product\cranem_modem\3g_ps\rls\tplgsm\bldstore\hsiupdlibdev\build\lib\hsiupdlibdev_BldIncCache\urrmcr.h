/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/
 
/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urrmcr.h#4 $
 *   $Revision: #4 $
 *   $DateTime: 2007/03/30 13:53:25 $
 **************************************************************************
 * File Description:
 *
 *    Header file for URRMCR.C. Contains function call declarations
 *    for use by other URRC modules.
 **************************************************************************/
/*******************************	Revision History ***********************
  CQ/Activity	  SW Eng	   Date              Description
  **********	  efrats      07/08/08        AGPS re-design: update AGPS signals.  
	 
  **************************************************************************/

#if !defined (URRMCR_H)
#define       URRMCR_H

/***************************************************************************
* Nested Include Files
***************************************************************************/
#include <urrmcrty.h>

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/

/***************************************************************************
*   Macro Functions
***************************************************************************/

/******************************************************************************
 *
 * Function     : SET_MCR_STATE
 *
 * Scope        : LOCAL
 *
 * Parameters   : sTATE - New CMR State
 *
 * Returns      : None
 *
 * Description  : Sets the new CMR state and produces optional debug printout.
 *
 *****************************************************************************/
#define SET_MCR_STATE(nEWsTATE)   urrMcr.state = nEWsTATE;

/***************************************************************************
 * Types
 ***************************************************************************/

//ICAT EXPORTED STRUCT
typedef struct McrRscpResultsTag
{
    Boolean                          isActiveSetCell;
    Boolean                          measValid;
    SignedInt16                      rscp;
    Boolean                          referenceTimeDifferenceToCellPresent;
    T_UReferenceTimeDifferenceToCell referenceTimeDifferenceToCellType;
    Int16                            referenceTimeDifferenceToCell;
    Boolean                          readSFN;
    Boolean                          txDiversityIndicator;
}
McrRscpResults;

//ICAT EXPORTED STRUCT
typedef struct McrRscpResultsListTag
{
    Int8           noOfCells;
    McrRscpResults rscpResult [maxCellMeas];
}
McrRscpResultsList;

/***************************************************************************
 * Typed Constants
 ***************************************************************************/

/***************************************************************************
 * Function Prototypes
 ***************************************************************************/
Boolean UrrMcrGetMeasOnRachBeforeUplink(UrrSubProcessId subProcessId,
    Boolean isInterFreqValid);
    
Boolean UrrMcrGetMeasuredResultsOnRach(UrrSubProcessId subProcessId,
    Boolean isInterFreqValid);

//put this function in remark as for now no entity use it anymore (AIS used to call it until CQ00198620

/*Boolean UrrMcrIsPendingMeasOnRachForSubProcess(UrrSubProcessId subProcessId);*/

Boolean UrrMcrRemoveLastRachMeas (UMeasuredResultsOnRACH *measResults_p);

void UrrMcrIntraFreqNCellInfoListInd   (CellIdMask          removeCellIds,
                                        CellIdMask          addCellIds,
                                        Int8                numberOfCells,
                                        UmtsCell            *cellInfo_p,
                                        UUARFCN             freq);

void UrrMcrInterFreqNCellInfoListInd   (CellIdMask          removeCellIds,
                                        CellIdMask          addCellIds,
                                        Int8                numberOfCells,
                                        UInterFrequencyCell *cellInfo_p
                                        );

#if ! defined (UPGRADE_EXCLUDE_2G)
void UrrMcrGsmNCellInfoListInd         (CellIdMask          removeCellIds,
                                        CellIdMask          addCellIds,
                                        Int8                numberOfCells,
                                        UGsmCell            *cellInfo_p,
                                        UFrequency_Band     gsmBandIndicator);
#endif

void UrrMcrServingCellMeasReq (void);

void UrrMcrStopServingCellMeasReq (void);


void UrrMcrIntraFreqMeasReq (
    CellIdMask maskCpichEcn0,
    CellIdMask maskCpichRscp,
    Boolean resetMonitorFlag);

void UrrMcrInterFreqMeasReq (CellIdMask maskCpichEcn0,
                             CellIdMask maskCpichRscp,
                             Int8 highPriority);

#if ! defined (UPGRADE_EXCLUDE_2G)
void UrrMcrmgSendCphyMonitorGsmReq (
                      Int8        measId,
                      UBSIC_VerificationRequired bsicVerificationRequired,
                      CellIdMask  gsm_CarrierRSSI
                      ,Int8 highPriority
                      /*CQ00067513 begin*/
                      ,CellIdMask celllistChanged
                      /*CQ00067513 end*/
                      ,Boolean    newGsmRssiMeasurements  /* CQ00068695 added */
                      );

void UrrMcrmgStopGsmMeasReq     (Int8 measId);
#endif

void UrrMcrStopIntraFreqMeasReq (Int8 measId);
void UrrMcrStopInterFreqMeasReq (Int8 measId);

void  UrrMcrGetDchCellsMeasurementInd (
          UUARFCN                     * const arfcn_p,
          UPrimaryScramblingCode              psc [],
         McrRscpResultsList            *       rscpResults,
         /* ********** begin */
         McrRscpResultsList            *       ecnoResults);

UPrimaryScramblingCode UrrMcriaGetBestMonitoredOrDetectedCell (void); /* ********** */

void UrrMcrProcessInternalSignal (SignalBuffer *signal_p);
void UrrMcrHandleTimers          (RadioResourceControlEntity  *urr_p);
void UrrMcrProcessSib11          (USysInfoType11   *uSysInfoType11_p);
void UrrMcrProcessSib12          (USysInfoType12   *uSysInfoType12_p);

#if defined (ENABLE_3G_POSITIONING)
void UrrMcrProcessSib15          (USysInfoType15   *uSysInfoType15_p);
void UrrMcrProcessSib15Point1    (USysInfoType15_1 *uSysInfoType15P1_p);

void UrrMcrProcessSib15Point2    (USysInfoType15_2  *uSysInfoType15P2_p,
                                  Int8              instanceId);

void UrrMcrProcessSib15Point3    (USysInfoType15_3  *uSysInfoType15P3_p,
                                  Int8              instanceId);

void UrrMcrProcessSib15Point4    (USysInfoType15_4  *uSysInfoType15P4_p);
#endif /* ENABLE_3G_POSITIONING */

void UrrMcrCmacTrafficMeasurementInd      (RadioResourceControlEntity *urr_p);
void UrrMcrCmacQualityMeasurementInd      (RadioResourceControlEntity *urr_p);
void UrrMcrCmacGetMeasurementCnf          (RadioResourceControlEntity *urr_p);
void UrrMcrCphyUeInternalMeasurementInd   (RadioResourceControlEntity *urr_p);
/* CQ00072273 begin */
void UrrMcrCphyUeRxTxTimeDiffType2MeasurementInd (RadioResourceControlEntity *urr_p);
/* CQ00072273 end */
void UrrMcrCphyFreqMeasOnRachCnf     (RadioResourceControlEntity *urr_p);
/* CQ00063613 added begin */
void UrrMcrFakeCphyFreqMeasOnRachCnf     (void);
/* CQ00063613 added end */
void UrrMcrCphyMonitorIntraFreqCellInd    (RadioResourceControlEntity *urr_p);
void UrrMcrCphyMonitorInterFreqCellInd    (RadioResourceControlEntity *urr_p);

void UrrMcrClearAwaitSubProcess    (UrrSubProcessId   subProcess);
#if ! defined (UPGRADE_EXCLUDE_2G)
void UrrMcrmgCphyBsicDecodeInd            (RadioResourceControlEntity *urr_p);
void UrrMcrmgCphyMonitorGsmCellInd        (RadioResourceControlEntity *urr_p);

/*CQ00060693 begin*/
void   UrrMcrMgClearGsmCellBsicStatus(CphyBsicDecodeInd *cphyBsicDecodeInd,
                                                 Int8 cellid,Int8 reportedCellId);
/*CQ00060693 end*/          


#endif

void McruiSetMaximumUeTxPower (void);

void UrrMcrCphyDetectedCellMeasurementInd (RadioResourceControlEntity *urr_p);
void UrrMcrCphyMeasureIntraFreqCellsInd   (RadioResourceControlEntity *urr_p);
void UrrMcrCphyMeasureInterFreqCellsInd   (RadioResourceControlEntity *urr_p);
void UrrMcrInit (Boolean activation, Boolean initCapabilities);                //-+ CQ00236755 24-Sep-2012 +-
void UrrMcrActivate (void);
Int8 UrrMcrBearerIdentityEnumToInt (BearerIdentity tag);

Boolean UrrMcrGetNextMeasId (T_UMeasurementType_latest  measType,
                             UMeasurementIdentity   startId,
                             UMeasurementIdentity   *measId_p);

struct McrMeasType *UrrMcrGetNextMeasPtr (T_UMeasurementType_latest  measType,
                                          UMeasurementIdentity   startId,
                                          UMeasurementIdentity   *measId_p);
/* added for ********** begin */
void McrSetResumeFlagForRevert(void);
void McrCleanMCResumeFlag(void);
/* added for ********** end */

Boolean UrrMcrGetTVIndicator (UrlTrafficVolumeBits trafficVolumeBits);

void UrrMcrAgpsUlDataReq (RadioResourceControlEntity *urr_p);
void UrrMcrAgpsDlDataRsp (RadioResourceControlEntity *urr_p);

void UrrMcrGetStates (McrState *mcrState_p, UrrSubProcessId *awaitSubProcessArray, Int8 maxArraySize);
/* added for ********** begin */
void McrStartMeasOnRachGuardTimer(void);
void McrStopMeasOnRachGuardTimer(void);
/* added for ********** end */
/* added for ********** begin */
void UrrSendRrcDipDataInd(UArfcn uarfcn, ServiceType serviceType);
/* added for ********** end */
/* added for ********** begin */
void UrrMcrStopOnRachMeasImplicity (void);
/* added for ********** end */

void UrrMcrCphyMonitorLteCellInd (RadioResourceControlEntity *urr_p);

void UrrMcrCphyLteCellInfoReq (
                                Int8           numberOfFreqs,
                                ULteFrequencyInfo       *freqInfo_p);

void UrrMcrLteFreqMeasReq (FreqIdMask rsrp, FreqIdMask rsrq, Int8 highPriority);

void UrrMcrStopLteMeasReq (Int8 measId);

//void McreuSendCphyMonitorLteCellReq (Int8  measId, Int8 rsrp, Int8 rsrq, Int8 highPriority);

void McrBuildMeasEventResultV860Ext (UrrSirAllocatedMemList   *memList_p,
                                           UMeasurementReport      *measRep_p, 
                                           Boolean measResult, 
                                           UEUTRA_MeasuredResults   *measRes_p,
                                           Boolean eventResult,
                                           UEUTRA_EventResults  *eventList_p);


/*added for DMCR DEVELOPMENT begin*/
void UrrMcrDeleteCILForDeferredSib (void);
void McrDeleteAllMeasForDeferSib(void);
Boolean UrrMcrProcessSib3 (void);
void UrrMcrInitSib3OnRachMeasStatus (void);
/*added for DMCR DEVELOPMENT end*/

#if defined (RRC_UPGRADE_ENG_MODE_REDESIGN) //********** begin
Boolean UrrMcrEngModeGetMeasOnDch (CiDevWbActiveAndMonitoredSetInfoEng_RRC *metric61);
Boolean UrrMcrEngModeGetInterRatMeasOnDch (CiDevWbInterRatNeighborMeasEng_RRC *metric62);
Boolean UrrMcrEngModeGetSCellMeasOnDch (CiDevWBRfInfoEng_RRC *metric60);
#endif //RRC_UPGRADE_ENG_MODE_REDESIGN :********** end

//********** start
#if defined (UPGRADE_ESCC)
void HandleIntSigAirSignalSendStatus (SignalBuffer *signal_p);
/* ********** add begin */
void UrrMcrEsccSendDelayedE1D (void); 
void UrrMcrEsccAddActivationTimeE1D(UMeasurementReport          *measRep_p,
                                                 UrrSirAllocatedMemList    *memList_p);
/* ********** add end */

#endif//UPGRADE_ESCC
//********** end


#endif


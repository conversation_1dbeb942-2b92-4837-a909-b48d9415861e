/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urr_sig.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2007/03/30 13:53:25 $
 **************************************************************************
 * File Description:
 *
 *    File containing the 'signals' union for URRC.
 **************************************************************************/
#if !defined (URR_STUB_H)
#define       URR_STUB_H

/***************************************************************************
* Nested Include Files
***************************************************************************/



#if !defined (UPGRADE_EXCLUDE_2G)
#include <grrrcsig.h>
#endif
#include <kernel.h>
/* ********** : Upgrade to LTE+WCDMA feature : begin */
//#if !defined (EXCLUDE_GSM_PS_AS) || !defined (EXCLUDE_GSM_L1)
#include "lteUtraGsmIratItf.h"
//#endif


/*********** add end*/

//#if defined(UPGRADE_COMMON_STORE)
#include <rrcdstypes.h>
//#endif/*UPGRADE_COMMON_STORE*/

union Signal
{
    

#if defined (UPGRADE_DSDS)
#if defined(UPGRADE_COMMON_STORE)
    IratDsSuspendReq            iratDsSuspendReq;
    IratDsAbortGsmPsReq         iratDsAbortGsmPsReq;
    IratDsCampOnReq             iratDsCampOnReq;
    EmptySignal                 iratDsSuspendCnf;
    EmptySignal                 iratDsCancelGsmSuspendReq;
    EmptySignal                 iratDsCellSearchFinishInd;
    EmptySignal                 iratDsResumeFinishInd;
    EmptySignal                 iratDsPowerOffCompleteInd;
    EmptySignal                 iratDsCancelAbortGsmPsReq;
    EmptySignal                 iratDsStopIratReq;
#if defined(LTE_DS_SUSPEND_BY_SIM2)
    EmptySignal                 iratDsResumeLtePsInd;
#endif /* LTE_DS_SUSPEND_BY_SIM2 */
#endif/*UPGRADE_COMMON_STORE*/
    IratDsAbortSearchReq        iratDsAbortSearchReq;
    EmptySignal                 iratDsAbortSearchCnf;
/* CQ00109244 added begin */	
#if defined(ENABLE_WAIT_ABORT_SEARCH_CNF)
    EmptySignal                 iratDsPhyConfigFinishInd;
#endif /* ENABLE_WAIT_ABORT_SEARCH_CNF */
/* CQ00109244 added end */	
#endif
#if defined(UPGRADE_LTE_SIM1_PS_SIM2_RESELECTION)
    IratDsControlPchReq         iratDsControlPchReq;
#endif



};
#endif
/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/umaftf.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/27 15:10:43 $
 ***************************************************************************
 * File Description: Implements the Flexible Trace Framework for UMAC
 **************************************************************************/

#if !defined (UMA_FTF_H)
#define       UMA_FTF_H

/* Define URLC sub-process types for the FTF. These needs to be defined before
 * including the ftf header files */
#define UT_FTF_UMAC_UL_SUB_PROCESS    1
#define UT_FTF_UMAC_DL_SUB_PROCESS    2
#define UT_FTF_UMAC_HS_UL_SUB_PROCESS 3
#define UT_FTF_UMAC_HS_DL_SUB_PROCESS 4
#define UT_FTF_UMAC_MAIN_SUB_PROCESS  5

/**************************************************************************
 * Nested Include Files
 **************************************************************************/
#include <utftf_sig.h>
#include <utftf.h>

#if defined (ENABLE_FLEXIBLE_TRACE_FRAMEWORK)
/**************************************************************************
 * Manifest Constants
 **************************************************************************/

/**************************************************************************
 * Type Definitions
 **************************************************************************/
/* Define the FTF data structure for UMAC */
UT_FTF_DEFINE_TRACE_DATA (UMAC)

/**************************************************************************
 * Macros
 **************************************************************************/

/**************************************************************************
 * Function Prototypes
 **************************************************************************/

#endif /* ENABLE_FLEXIBLE_TRACE_FRAMEWORK */

#endif

/* END OF FILE */

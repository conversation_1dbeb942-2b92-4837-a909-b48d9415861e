/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/3g.mod/lib/src/usimutil.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/26 20:17:00 $
 **************************************************************************
 *  File Description :
 *
 *  Header file for usimutil.c
 **************************************************************************/

#if !defined (USIMUTIL_H)
#define USIMUTIL_H

/***************************************************************************
 * Nested Include Files
 **************************************************************************/

#if !defined (SIMDATA_H)
#include "simdata.h"
#endif


#if !defined (SIMDEC_H)
#include "simdec.h"
#endif


#if !defined (ALSU_SIG_H)
#include "alsu_sig.h"
#endif


/***********, Cgliu, 2019-02-26, Begin*/
#if !defined(DMNVMCFG_H)
#include <dmnvmcfg.h>
#endif

/***********, Cgliu, 2019-02-26, End  */


/***************************************************************************
 * Macro
 **************************************************************************/



/***************************************************************************
 * Manifest Constants
 **************************************************************************/
 #define USIM_CCP_RECORD_SIZE   14

/***********, Cgliu, 2019-02-26, Begin*/
extern SimConfigurationData  simConfigurationDataList;

#define SIM_STATUS_CUSTOMER_REQUEST_TIME     SECONDS_TO_TICKS(simConfigurationDataList.simPollingConfigData.pollingInterval)


/***********, Cgliu, 2019-02-26, End  */

 /***************************************************************************
 * Functions
 **************************************************************************/
 Boolean SimUiccCanResetCmdStatus ( SimCommandStatus  cmdStatus );
 Boolean simServiceEnabled (SimUiccService service, SimManagerData  *simData);
void simServiceDisable (SimUiccService service, SimManagerData  *simData);
 /***********, Cgliu, 2022-03-04,Begin*/
#if defined (SUPPORT_PB)
 Int16 SimUiccGetExtFile (Int16 dialNumFile, SimManagerData *simData);

/*----------------------------------------------------
 *                  CCP functions
 * --------------------------------------------------*/
void SimUiccAddCcpRecord ( BearerCapability *ccpData,
                           SimManagerData   *simData,
                           Int8             *recordNumber,
                           Int16             ccpFileId);

Boolean SimUiccCheckDialFileForCcp(SimManagerData     *simData,
                                   Int16               efId,
                                   Int8                recordNumber,
                                   SimDialNumberFile   dnFileId,
                                   Int8                dnRecordNum);

Boolean SimUiccCanDeleteCcpRecord ( Int8                recordNumber,
                                    SimManagerData     *simData,
                                    Int16               ccpId,
                                    SimDialNumberFile   dnFileId,
                                    Int8                dnRecordNum);

Int8 SimUiccGetCcpOffset(SimDialNumberFile     efId,
                         Int16                 recordLength);

Int16 SimUiccGetCcpFile (Int16 dialNumFile, SimManagerData *simData);

void SimUiccDeleteExtChain (Int8                  recordNumber,
                            Int16                 efId,
                            SimManagerData       *simData,
                            SimDialNumberFile     dnFile,
                            Int8                  dnRecordNum);

 void SimUiccDeleteExtRecord(Int8                  recordNumber,
                             Int16                 efId,
                             SimManagerData       *simData);

 Boolean SimUiccCheckExtAvailability (Int16 extFile, SimUiccService service, SimManagerData *simData);

 Boolean SimUiccCanDeleteCcp1Record ( Int8                  ccp1RecordNumber,
                                      SimManagerData       *simData,
                                      SimUiccPhoneBook     *phonebook_p,
                                      Int16                 ccpFileId );

 Boolean SimUiccCheckDialFileForCcp1(SimManagerData       *simData,
                                     SimUiccPbrFileInfo   *fileInfo_p,
                                     Int8                  ccp1RecordNumber);

 Boolean SimUiccHandleCcp  (Int8             oldCcpRecordNum,
                           BearerCapability *newCcp,
                           Boolean           newCcpDefined,
                           Int8             *newCcpRecordNum,
                           Int16             ccpFileId,
                           SimManagerData   *simData,
                           SimDialNumberFile dnFileId,
                           Int8              dnRecordNum);

Boolean SimUiccHandleCcp1  (Int8             oldCcpRecordNum,
                            BearerCapability *newCcp,
                            Boolean           newCcpDefined,
                            Int8             *newCcpRecordNum,
                            Int16             ccpFileId,
                            SimUiccPhoneBook *phonebook_p,
                            SimManagerData   *simData);


Int8 SimUiccGetExtOffset(Int16     efId,
                         Int16     recordLength);


Boolean SimUiccUpdateChangeCounter ( SimManagerData      *simData,
                                  Int16               *changeCounter,
                                  SimUiccPhoneBook    *phonebook_p );


Boolean SimUiccUpdateExt1InAnr (SimUiccAnr         *oldAnr_p,
                                SimUiccAnr         *newAnr_p,
                                Int8                oldExt1RecNum,
                                SimUiccPbrRecord         *pbrRecord_p,
                                Int16               ext1FileId,
                                Int8               *newExt1RecNum_p,
                                SimManagerData     *simData,
                                SimUiccPhoneBook   *phonebook_p);

void SimUiccDeleteExt1Chain (Int8              ext1RecordNum,
                             Int16             ext1FileId,
                             SimManagerData   *simData,
                             SimUiccPhoneBook *phonebook_p );

void SimUiccDeleteExt1Record (Int8              ext1RecordNum,
                             Int16             ext1FileId,
                             SimManagerData   *simData,
                             SimUiccPbrRecord         *pbrRecordNow_p,
                             SimUiccPhoneBook *phonebook_p);

#endif
/***********, Cgliu, 2022-03-04,End  */

Int8 SimUiccGetAbsoluteRecord ( SimRecordReadWriteMode accessMode,
                                SimEfId                efId,
                                SimManagerData        *simData,
                                Int8                   numOfRecords,
                                Int8                   currentRecord);

/***********, Cgliu, 2022-03-04,Begin*/
#if defined (SUPPORT_PB)

Boolean SimUiccHandleExt       (SimDialNumber      *oldDialNum_p,
                                SimDialNumber      *newDialNum_p,
                                Int8                oldExtRecNum,
                                Int16               extFileId,
                                Int8               *newExtRecNum_p,
                                SimManagerData     *simData,
                                SimDialNumberFile   dnFile,
                                Int8                dnRecordNum);

Boolean SimUiccHandleExt1      (SimDialNumber      *oldDialNum_p,
                                SimDialNumber      *newDialNum_p,
                                Int8                oldExtRecNum,
                                SimUiccPbrRecord         *pbrRecord_p,
                                Int8               *newExtRecNum_p,
                                SimManagerData     *simData,
                                SimUiccPhoneBook   *phonebook_p);

Int8 SimUiccGetNextFreeRecord ( Int16                 fileId,
                                SimManagerData        *simData);

SimUiccPhoneBook* SimUiccSetPb ( Boolean            globalPhoneBook,
                                 SimManagerData    *simData);

Boolean SimUiccConvertAdnRecord (Int16             absoluteAdnRecordNumber,
                                 SimUiccPhoneBook *phonebook_p,
                                 Int8             *pbrRecordNum_p,
                                 Int8             *simAdnRecordNum_p);
void SimUiccDeleteUid           ( SimManagerData     *simData,
                                  SimUiccPhoneBook   *phonebook_p,
                                  SimUiccPbrRecord   *pbrRecord_p,
                                  Int8                simAdnRecordNum);
Boolean SimUiccUpdateUid           ( SimManagerData     *simData,
                                  SimUiccPhoneBook   *phonebook_p,
                                  SimUiccPbrRecord   *pbrRecord_p,
                                  Int8                simAdnRecordNum);
Boolean SimUiccReadPhonebookSynchronisationCounter
                                ( SimManagerData      *simData,
                                  SimUiccAppData      *uiccData);
//                                  Int8                *psCounter );

void SimUiccSetHomezoneDir      ( SimManagerData         *simData);

void  SimUiccUpdatePhonebookSyncCounter ( SimManagerData      *simData,
                                                 SimUiccPhoneBook    *phonebook_p );
#if defined (SIM_DEBUG_EXT_RECORDS)
void SimUiccDebugExtensionRecords (Int16 extFileId, SimManagerData *simData);
#endif
/* Modify for CQ00016165 by Qinghong Li 20120203 begin */
Boolean SimUiccCheckBothEfPbrNotExist (SimManagerData *simData);
void SimUiccSet2gPathInUicc (SimManagerData *simData,SimUiccAppData *uiccData);
/* Modify for CQ00016165 by Qinghong Li 20120203 end */
Boolean SimUiccGetExt1Status (SimManagerData *simData,SimUiccPhoneBook *phonebook_p);

/*2013.09.21, merged from kunlun/Emei, CQ00044303, begin*/
/* Add by jungle for CQ00032214 on 2013-04-11 Begin */
Boolean SimUiccGetExt2Status (SimManagerData *simData);
#endif
 /***********, Cgliu, 2022-03-04,End  */

/* Add by jungle for CQ00032214 on 2013-04-11 End */
/*2013.09.21, merged from kunlun/Emei, CQ00044303, end*/
/*CQ00117640, sim dont write repeat data to sim by comparing preserving local simMmlocaldata,2019-12-20,perse,begin*/	 
void SimUiccSetCipherKeys (CipherKeyData *desKeyData,CipherKeyData *srcKeyData);
void SimUiccSetlocInfo(LocationInformation  *desLocInfo,LocationInformation  *srcLocInfo);
void SimUiccSetPslocInfo(RoutingInformation  *desPsLocInfo,RoutingInformation  *srcPsLocInfo);
void SimUiccSetKc(CipheringKeyData *desKeyData,CipheringKeyData *srcKeyData);
	/*CQ00139815, Cgliu, 2022-11-09,Begin*/ 
#if defined(SUPPORT_RPM)
void SimUiccSetRpmDir  (SimManagerData         *simData);
#endif 	
	/*CQ00139815, Cgliu, 2022-11-09,End  */	

void SimUiccSetLocalEpsNscInRead(GmmSimWriteData *simWriteData, GmmSimReadDataCnf *respData) ;
void SimUiccSetRespEpsNscInRead(GmmSimReadDataCnf  *respData,GmmSimWriteData *simWriteData);
void SimUiccSetRespEpsNscInWrite(GmmSimWriteData *desSimWriteData, GmmSimWriteDataReq *srcSimWriteData);
void SimUiccSetEpsPslocInfo(TrackingInfo *desEpsLocInfo,TrackingInfo  *srcEpsLocInfo) ;
void SimUiccSetEpsPslocInfoWithoutTailist(TrackingInfo  *srcEpsLocInfo,TrackingInfo  *desEpsLocInfo) ;
/*CQ00117640, sim dont write repeat data to sim by comparing preserving local simMmlocaldata,2019-12-20,perse,end*/	 
#endif

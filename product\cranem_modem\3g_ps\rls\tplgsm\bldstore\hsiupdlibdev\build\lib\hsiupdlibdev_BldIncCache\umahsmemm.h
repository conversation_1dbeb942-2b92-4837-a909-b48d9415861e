/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/***************************************************************************
 * Marvell International Ltd
 *
 ***************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/umahsmemm.h
 *
 ***************************************************************************
 * File Description:
 *
 *    UMAC High Speed (DL) Memory Manager
 *
 ***************************************************************************/

#ifndef UMAHSMEMM_H
#define UMAHSMEMM_H
/***************************************************************************
* Nested Include Files
***************************************************************************/ 



#include <system.h>



//should be called  at system initialization. 
void UmaHsMemmInitialize( void );
//should be called  at system initialization. Or on  HSPDA session initialization
void     UmaHsMemmInitPointers(Int32 requestedBufferByteLength );
//"free" the pointer.The data is not needed. The pointer can reused. Should be called by MAC HS.
Boolean  UmaHsMemmFreePointerbyPointer(void* Pointer); //TRUE if found FALSE not found
//Decrement L1 counter
void UmaHsMemmL1Decrement(void);
//returns a pointer to a valid UmaHsPdu memory. Should be called by HS when ever it need a pointer.
Boolean  UmaHsMemmGetPointer(  void** foundPointerAddress);
//Free all memory. UmaHsMemmInitPointers should be called in the next session  if we used this function 
void     UmaHsMemmClearAll( void ); 

Int32 UmaHsMemmGetNumPointersInUse(void);
Int32 UmaHsMemmGetNumL1PointersInUse(void);

//Not in use 
// void     UmaHsMemmPrint(char *title);
#endif //UMAHSMEMM_H
/* END OF FILE */


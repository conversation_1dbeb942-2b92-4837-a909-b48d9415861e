/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/3g.mod/lib/src/upde_rb.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/26 20:17:00 $
 **************************************************************************
 * File Description:
 *
 *      The UPDCP Entity - RB.
 **************************************************************************/

#if !defined (UPDE_RB_H)
#define UPDE_RB_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <npdu_typ.h>
#include <upde.h>

/***************************************************************************
* Manifest Constants
***************************************************************************/

/***************************************************************************
* Macro Functions
***************************************************************************/

/***************************************************************************
* Types
***************************************************************************/

/***************************************************************************
* Typed Constants
***************************************************************************/

/***************************************************************************
* Function Prototypes
***************************************************************************/

extern Boolean UpdcpRlcSap (SignalBuffer *sigBuf);

extern Boolean UpdeTransmitRlcSdu (UpdEntity *pde, Npdu *npdu);

extern Boolean UpdeTransmitNpduFromLlist (UpdEntity *pde, UpdcpNpduInfo *npduInfo);
extern  void  UpdeSendAmDataRsp (void);

#if defined (ENABLE_UPLANE_STATISTICS)
extern void    UpdeSendStatistics (SignalBuffer *sigBuf);
#endif /* ENABLE_UPLANE_STATISTICS */

#endif

/* END OF FILE */

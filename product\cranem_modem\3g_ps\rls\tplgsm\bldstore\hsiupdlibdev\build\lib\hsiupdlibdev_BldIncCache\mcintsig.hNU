/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/***************************************************************************
 *
 *                       TTPCom GPRS
 *
 *                  Copyright (c) 2000 TTPCom Ltd.
 *
 ***************************************************************************
 *
 *   $Id: //central/releases/Branch_release_9/tplgsm/gpinc/mcintsig.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2004/01/14 14:10:56 $
 *
 ***************************************************************************
 *
 *  File Description
 *
 *  This is the definitions of signal used between the MAC sequencer and
 *  the MAC background.
 *
 ***************************************************************************/
#if !defined (MCINTSIG_H)
#define       MCINTSIG_H

/***************************************************************************
 * Nested Include Files
 **************************************************************************/
#if defined (DEVELOPMENT_VERSION)
#include <gpgentyp.h>
#include <gp13_typ.h>
#include <rdmacsig.h>

#include <l1macif.h>
#endif /* DEVELOPMENT_VERSION */

/***************************************************************************
 * Manifest Constants
 **************************************************************************/
#define MAX_TX_DEBUG_RECORDS       16
#if !defined(UPGRADE_EDGE)
#define MAX_RX_DEBUG_RECORDS       6
#define MAX_RX_DEBUG_BUFF_OVERUN   1
#else
#define MAX_RX_DEBUG_RECORDS       8
#define MAX_RX_DEBUG_BUFF_OVERUN   2
#define MAX_RX_EGPRS_DEBUG_RECORDS       12
#define MAX_RX_EGPRS_DEBUG_BUFF_OVERUN   2
#endif

#define SEQ_NUM_SPACE_DIV_32       4     /* = 128 / 32 */
#define MAX_TX_DEBUG_BUFF_OVERUN   1
#define MAX_VS_VA_RDLB_RECORDS     16
#define MAX_DEBUG_UL_CTRL_MSGS     8
#define MAX_TX_INFO_REQ_RECORDS    16
#define MAX_TX_PURGE_RECORDS       MAX_SUPPORTED_TIMESLOTS

#define MAC_TX_DEBUG_STALL_BIT 0x01
#define MAC_TX_DEBUG_RETRY_BIT 0x02
#define MAC_TX_DEBUG_PFI_BIT   0x04
#define MAC_TX_DEBUG_TLLI_BIT  0x08
#if defined (UPGRADE_EDGE)
#define MAC_TX_DEBUG_PADDING_BIT 0x10
#endif

#define MAC_RX_DEBUG_BIW_BIT   0x01
#define MAC_RX_DEBUG_SP_BIT    0x02
#define MAC_RX_DEBUG_FBI_BIT   0x04

/***************************************************************************
 * Type Definitions
 **************************************************************************/
typedef enum MacIntUplinkStatusTag
{
    STALLED,
    VS_ADVANCED,
    CV_VALUE_OF_ZERO_SENT
}
MacIntUplinkStatus;

typedef enum MacIntErrorInfoTag
{
/*  XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX */
    MAC_ERR_INV_CS_TO_SEND_TO_RD,   /* 00. Invalid coding scheme in DL block to send to RD                          */
    MAC_ERR_CTRL_MSG_EXPRD,         /* 01. A ctrl msg from GRR has not been sent, the msg has expired               */
    MAC_ERR_DL_ACK_NACK_EXPRD,      /* 02. A dnlink ack/nack msg from GRR has not been sent, the msg has expired    */
    MAC_ERR_CTRL_ACK_EXPRD,         /* 03. A ctrl ack from GRR has not been sent, the msg has expired               */
    MAC_ERR_INV_CS_FROM_RD,         /* 04. An UL block from RD has an invalid coding scheme                         */
    MAC_ERR_INVALID_SSN_FROM_NET,   /* 05. Network is sending us an invalid SSN in the UL ack/nack bitmap           */
    MAC_ERR_INV_RX_WINDOW_PARAMS,   /* 06. The windowing parameters for the DL TBF is incorrect                     */
    MAC_ERR_NET_NACK_ACKED_BLOCK,   /* 07. The network as nacked a previously acked UL data block                   */
    MAC_ERR_UL_ACK_COUNT_EXPIRED,   /* 08. The network has sent us too many UL ack/nacks with the same SSN          */
    MAC_ERR_DL_ACK_NACK_BUFF_FULL,  /* 09. The downlink ack/nack buffer is full                                     */
    MAC_ERR_VS_VA_SAME_COUNT_EXP,   /* 10. The VS and VA params of the UL TBF have not moved                        */
    MAC_ERR_RD_NO_DATA_CV_NOT_ZERO, /* 11. RD has no more data for MAC but the CV in the last BSN is not zero       */
    MAC_ERR_SENT_PKT_REL_TO_GRR,    /* 12. Sent a GrrMacPktReleaseInd to GRR to terminate the TBF                   */
    MAC_ERR_UNACK_DATA_BUFF_FULL,   /* 13. The downlink TBF buffer has been filled up. Either MAC is not clearing
                                     *     it or we have not allocated enough buffer space.                         */
    MAC_ERR_CTRL_MSG_CLASH,         /* 14. A ctrl msg was requested to be sent in the same frame and timeslot as
                                     *     another. The new ctrl msg has been discarded                             */
    MAC_ERR_CTRL_MSG_BUFF_FULL,     /* 15. The uplink control message buffer is full.                               */
    MAC_ERR_LOW_PRI_CTRL_MSG_RPL,   /* 16. The uplink control message buffer is full but a low priority message
                                     *     has been replaced.                                                       */
    MAC_ERR_DATA_BLK_DISCARDED,     /* 17. Blocks have been discarded by L1 in error.                               */
    MAC_ERR_UNKNOWN_BLK_DISCARDED,  /* 18. Blocks have been discarded by L1 in error.                               */
    MAC_ERR_UNKNOWN_BLK_RXD,        /* 19. We have received a block but the block type is unknown.                  */
    MAC_ERR_DISC_CTRL_BLK_INC_TFI,  /* 20. We have discarded an RLCMAC ctrl block with opt header because the TFI
                                     *     contained within the header did not match our uplink or downlink TFI     */
    MAC_ERR_BSN_SENT_BUFF_FULL,     /* 21. The number of blocks sent this period has exceed the sent array size.    */
    MAC_ERR_FLUSHED_UL_CTRL_MSG,    /* 22. An uplink control message was flushed.                                   */
    MAC_ERR_FLUSHED_DL_CTRL_MSG,    /* 23. A downlink control message was flushed.                                  */
    MAC_ERR_FLUSHED_SEL_CTRL_MSG,   /* 24. A control message was flushed in response to a PDCH release.             */
    MAC_ERR_ENG_MODE_NOT_ENABLED,   /* 25. Engineering mode reporting requested but not enabled.                    */
    MAC_ERR_NO_ERROR
}
MacIntErrorInfo;

typedef enum MacIntBlockTypeTag
{
/*  XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX */
    /* TX blocks */
    MAC_DEBUG_UL_NEW_DATA_BLK,
    MAC_DEBUG_UL_PEND_DATA_BLK,
    MAC_DEBUG_UL_NACKD_DATA_BLK,
    MAC_DEBUG_DL_ACK_NACK_BLK,
    MAC_DEBUG_UL_CTRL_ACK,
    MAC_DEBUG_UL_DUMMY_BLK,
    MAC_DEBUG_UL_LOOPBACK_BLK,
    MAC_DEBUG_UL_RESOURCE_REQ,
    MAC_DEBUG_UL_MEAS_REPORT,
    MAC_DEBUG_UL_CELL_CHNG_FAIL,
    MAC_DEBUG_UL_MOBILE_TBF_STAT,
    MAC_DEBUG_UL_PSI_STATUS,


      
    MAC_DEBUG_UL_UNKNOWN_BLK,

    /* Rx blocks */
    MAC_DEBUG_DL_DATA_BLK,
    MAC_DEBUG_UL_ACK_NACK_BLK,
    MAC_DEBUG_DL_DUMMY_BLK,
    MAC_DEBUG_DL_LOOPBACK_BLK,
    MAC_DEBUG_SEG_CTRL_BLK,
    MAC_DEBUG_TIMESLOT_RECONFIG,
    MAC_DEBUG_DL_ASSIGNMENT,
    MAC_DEBUG_UL_ASSIGNMENT,
    MAC_DEBUG_PKT_POLLING_REQ,
    MAC_DEBUG_PWR_CTRL_T_ADV,
    MAC_DEBUG_PKT_ACCESS_REJ,
    MAC_DEBUG_PKT_CELL_CHNG_ORD,
    MAC_DEBUG_PKT_MEAS_ORD,
    MAC_DEBUG_PKT_PAGING_REQ,
    MAC_DEBUG_PKT_PDCH_REL,
    MAC_DEBUG_PKT_PRACH_PARAMS,
    MAC_DEBUG_PKT_QUEUE_NOTI,
    MAC_DEBUG_PKT_TBF_RELEASE,
    MAC_DEBUG_PKT_SI_1,
    MAC_DEBUG_PKT_SI_2,
    MAC_DEBUG_PKT_SI_3,
    MAC_DEBUG_PKT_SI_3BIS,
    MAC_DEBUG_PKT_SI_4,
    MAC_DEBUG_PKT_SI_5,
    MAC_DEBUG_PKT_SI_13,
    MAC_DEBUG_DL_BAD_BLOCK,
    MAC_DEBUG_UNKNOWN_CTRL_BLK
}
MacIntBlockType;

typedef enum UlCtrlMsgBufferStatusTag
{
    CTRL_MSG_BUFF_FREE,
    CTRL_MSG_BUFF_ACCESSING,
    CTRL_MSG_BUFF_PENDING,
    CTRL_MSG_BUFF_SENDING,
    CTRL_MSG_BUFF_SENT
}
UlCtrlMsgBufferStatus;

/***************************************************************************
 *  Macros
 **************************************************************************/
/***************************************************************************
 * Signal Definitions
 **************************************************************************/
typedef struct MacIntUlTbfStatusIndTag
{
    MacIntUplinkStatus status;
}
MacIntUlTbfStatusInd;

#if defined (DEVELOPMENT_VERSION)
typedef struct MacIntCtrlMsgBuffIndTag
{
    Int8                  numRecords;
    UlCtrlMsgBufferStatus status          [MAX_DEBUG_UL_CTRL_MSGS];
    UplinkMessageType     messageType     [MAX_DEBUG_UL_CTRL_MSGS];
    Boolean               sendImmediately [MAX_DEBUG_UL_CTRL_MSGS];
    Int32                 frameNumber     [MAX_DEBUG_UL_CTRL_MSGS];
    CodingScheme          csUsed          [MAX_DEBUG_UL_CTRL_MSGS];
    Int8                  timeslot        [MAX_DEBUG_UL_CTRL_MSGS];
    Boolean               finalAck        [MAX_DEBUG_UL_CTRL_MSGS];
    Boolean               rlcUnackMode    [MAX_DEBUG_UL_CTRL_MSGS];
    MacCtrlAckPriority    priority        [MAX_DEBUG_UL_CTRL_MSGS];
}
MacIntCtrlMsgBuffInd;

typedef struct MacIntErrorIndTag
{
    Int32             errFrameNumber;
    MacIntErrorInfo   status;
    Int32             value1;
    Int32             value2;
    Int32             value3;
}
MacIntErrorInd;

typedef struct MacIntDataContentTag
{
    Int8 dataSize;
#if !defined(UPGRADE_EDGE)
    Int8 content [SIZE_OF_CS_4];
#else
    Int8 content [SIZE_OF_MCS_9];
#endif
}
MacIntDataContent;

typedef struct MacIntDataContentIndTag
{
    Int32             frameNumber;
    Boolean           txFalseRxTrue;
    DataBlockPtr      macBuffLocation_p;
    MacIntDataContent data;
}
MacIntDataContentInd;

#if defined (UPGRADE_EDGE)
typedef struct MacIntTxDataDebugIndTag
{
    Int8             numTxDataBlocks;
    CodingScheme     csUsed              [MAX_TX_DEBUG_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
    Int16            blockSequenceNumber [MAX_TX_DEBUG_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
    Int32            txFrameNumber       [MAX_TX_DEBUG_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
    PuncturingScheme ps                  [MAX_TX_DEBUG_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
    MacIntBlockType  blkType             [MAX_TX_DEBUG_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
    Int8             countdownValue      [MAX_TX_DEBUG_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
    Int8             stallRetryPfiTiBits [MAX_TX_DEBUG_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
    Int16            va                  [MAX_TX_DEBUG_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
    Int16            vs                  [MAX_TX_DEBUG_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
    Int8             tfi                 [MAX_TX_DEBUG_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
    Int16            lastRdBlockAcq      [MAX_TX_DEBUG_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
    Int16            resegmentBsn        [MAX_TX_DEBUG_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
    Boolean          cvValueZeroSent;
    Int8             vsVaCount;
    Boolean          t3182Running;
    Boolean          sendReadyToSendInd;
}
MacIntTxDataDebugInd;
#else
typedef struct MacIntTxDataDebugIndTag
{
    Int8             numTxDataBlocks;
    CodingScheme     csUsed              [MAX_TX_DEBUG_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
    Int8             blockSequenceNumber [MAX_TX_DEBUG_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
    Int32            txFrameNumber       [MAX_TX_DEBUG_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
    MacIntBlockType  blkType             [MAX_TX_DEBUG_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
    Int8             countdownValue      [MAX_TX_DEBUG_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
    Int8             stallRetryPfiTiBits [MAX_TX_DEBUG_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
    Int8             va                  [MAX_TX_DEBUG_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
    Int8             vs                  [MAX_TX_DEBUG_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
    Int8             tfi                 [MAX_TX_DEBUG_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
    Int8             lastRdBlockAcq      [MAX_TX_DEBUG_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
    Int8             resegmentBsn        [MAX_TX_DEBUG_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
    Boolean          cvValueZeroSent;
    Int8             vsVaCount;
    Boolean          t3182Running;
    Boolean          sendReadyToSendInd;
}
MacIntTxDataDebugInd;
#endif

typedef struct MacIntTxStateDebugIndTag
{
    Int8    reTxLength;
    Int8    reTxList [WINDOW_SIZE];

    Int8    pendTxLength;
    Int8    pendTxList [MAX_SUPPORTED_TIMESLOTS * 2];

    Boolean cvValueZeroSent;
    Int8    lastRdBlockAcquired;        /* last data block written by RD  */
    Boolean sendNackedBlocks;           /* Send all nacked blocks first   */
    Int8    pendingAck;                 /* pointer to a block pending ack */
    Int8    vb [WINDOW_SIZE];           /* Acknowledge State Array V(B)   */
}
MacIntTxStateDebugInd;

#if defined (UPGRADE_EDGE)
typedef struct MacIntRxDataDebugIndTag
{
    Int8             numRxDataBlocks;
    Int8             dnlinkTfi;
    CodingScheme     csUsed              [MAX_RX_DEBUG_RECORDS + MAX_RX_DEBUG_BUFF_OVERUN];
    Int16            blockSequenceNumber [MAX_RX_DEBUG_RECORDS + MAX_RX_DEBUG_BUFF_OVERUN];
    Int16            bsn2                [MAX_RX_DEBUG_RECORDS + MAX_RX_DEBUG_BUFF_OVERUN];
    Int32            rxFrameNumber       [MAX_RX_DEBUG_RECORDS + MAX_RX_DEBUG_BUFF_OVERUN];
    Int8             timeslot            [MAX_RX_DEBUG_RECORDS + MAX_RX_DEBUG_BUFF_OVERUN];
    PuncturingScheme ps1                 [MAX_RX_DEBUG_RECORDS + MAX_RX_DEBUG_BUFF_OVERUN];
    PuncturingScheme ps2                 [MAX_RX_DEBUG_RECORDS + MAX_RX_DEBUG_BUFF_OVERUN];
    MacIntBlockType  blkType             [MAX_RX_DEBUG_RECORDS + MAX_RX_DEBUG_BUFF_OVERUN];
    Int8             biwSpFbiBits        [MAX_RX_DEBUG_RECORDS + MAX_RX_DEBUG_BUFF_OVERUN];
    Int8             bsn2BiwFbiBits      [MAX_RX_DEBUG_RECORDS + MAX_RX_DEBUG_BUFF_OVERUN];
    Int8             rrbp                [MAX_RX_DEBUG_RECORDS + MAX_RX_DEBUG_BUFF_OVERUN];
    Int8             usf                 [MAX_RX_DEBUG_RECORDS + MAX_RX_DEBUG_BUFF_OVERUN];
    Int8             tfi                 [MAX_RX_DEBUG_RECORDS + MAX_RX_DEBUG_BUFF_OVERUN];
#if !defined (ENABLE_MAC_EGPRS_RX_DATA_LOGGING)
    Int16            vq                  [MAX_RX_DEBUG_RECORDS + MAX_RX_DEBUG_BUFF_OVERUN];
    Int16            vr                  [MAX_RX_DEBUG_RECORDS + MAX_RX_DEBUG_BUFF_OVERUN];
    Int16            nextSendToRd        [MAX_RX_DEBUG_RECORDS + MAX_RX_DEBUG_BUFF_OVERUN];
#endif
    Int8             powerReduction      [MAX_RX_DEBUG_RECORDS + MAX_RX_DEBUG_BUFF_OVERUN];
    Int16            rxBer               [MAX_RX_DEBUG_RECORDS + MAX_RX_DEBUG_BUFF_OVERUN];
#if defined(ENABLE_MAC_EGPRS_MORE_RX_HDR_DEBUG)
    Int16            meanBepBlock        [MAX_RX_DEBUG_RECORDS + MAX_RX_DEBUG_BUFF_OVERUN];
    Int16            cvBepBlock          [MAX_RX_DEBUG_RECORDS + MAX_RX_DEBUG_BUFF_OVERUN];
#endif
}
MacIntRxDataDebugInd;

typedef struct MacIntEgprsRxDataDebugIndTag
{
    Int8             numRxDataBlocks;
    CodingScheme     csUsed              [MAX_RX_EGPRS_DEBUG_RECORDS + MAX_RX_EGPRS_DEBUG_BUFF_OVERUN];
    Int16            bsn                 [MAX_RX_EGPRS_DEBUG_RECORDS + MAX_RX_EGPRS_DEBUG_BUFF_OVERUN];
    Int32            decodeCompleteFn    [MAX_RX_EGPRS_DEBUG_RECORDS + MAX_RX_EGPRS_DEBUG_BUFF_OVERUN];
    Int8             ddFbiInfo           [MAX_RX_EGPRS_DEBUG_RECORDS + MAX_RX_EGPRS_DEBUG_BUFF_OVERUN];
    Int16            vq                  [MAX_RX_EGPRS_DEBUG_RECORDS + MAX_RX_EGPRS_DEBUG_BUFF_OVERUN];
    Int16            vr                  [MAX_RX_EGPRS_DEBUG_RECORDS + MAX_RX_EGPRS_DEBUG_BUFF_OVERUN];
    Int16            nextSendToRd        [MAX_RX_EGPRS_DEBUG_RECORDS + MAX_RX_EGPRS_DEBUG_BUFF_OVERUN];
}
MacIntEgprsRxDataDebugInd;

#else
typedef struct MacIntRxDataDebugIndTag
{
    Int8             numRxDataBlocks;
    Int8             dnlinkTfi;
    CodingScheme     csUsed              [MAX_RX_DEBUG_RECORDS + MAX_RX_DEBUG_BUFF_OVERUN];
    Int8             blockSequenceNumber [MAX_RX_DEBUG_RECORDS + MAX_RX_DEBUG_BUFF_OVERUN];
    Int32            rxFrameNumber       [MAX_RX_DEBUG_RECORDS + MAX_RX_DEBUG_BUFF_OVERUN];
    Int8             timeslot            [MAX_RX_DEBUG_RECORDS + MAX_RX_DEBUG_BUFF_OVERUN];
    MacIntBlockType  blkType             [MAX_RX_DEBUG_RECORDS + MAX_RX_DEBUG_BUFF_OVERUN];
    Int8             biwSpFbiBits        [MAX_RX_DEBUG_RECORDS + MAX_RX_DEBUG_BUFF_OVERUN];
    Int8             usf                 [MAX_RX_DEBUG_RECORDS + MAX_RX_DEBUG_BUFF_OVERUN];
    Int8             vq                  [MAX_RX_DEBUG_RECORDS + MAX_RX_DEBUG_BUFF_OVERUN];
    Int8             vr                  [MAX_RX_DEBUG_RECORDS + MAX_RX_DEBUG_BUFF_OVERUN];
    Int8             rrbp                [MAX_RX_DEBUG_RECORDS + MAX_RX_DEBUG_BUFF_OVERUN];
    Int8             tfi                 [MAX_RX_DEBUG_RECORDS + MAX_RX_DEBUG_BUFF_OVERUN];
    Int8             powerReduction      [MAX_RX_DEBUG_RECORDS + MAX_RX_DEBUG_BUFF_OVERUN];
    Int16            rxBer               [MAX_RX_DEBUG_RECORDS + MAX_RX_DEBUG_BUFF_OVERUN];
    Int16            status              [MAX_RX_DEBUG_RECORDS + MAX_RX_DEBUG_BUFF_OVERUN];
    Int8             nextSendToRd        [MAX_RX_DEBUG_RECORDS + MAX_RX_DEBUG_BUFF_OVERUN];
}
MacIntRxDataDebugInd;
#endif

typedef struct MacIntRxStateDebugIndTag
{
    Int8  vr;
    Int8  vq;
    Int8  nextSendToRd;
    Int32 vnArray [SEQ_NUM_SPACE_DIV_32];
}
MacIntRxStateDebugInd;

typedef struct MacIntTxRxPtrInfoIndTag
{
    DataBlockPtr txWindow_p [WINDOW_SIZE];
    DataBlockPtr rxWindow_p [WINDOW_SIZE];
}
MacIntTxRxPtrInfoInd;

typedef struct MacIntTxVsVaRdLbDebugIndTag
{
    Int8        numRecords;
    Int32       frameNumber          [MAX_VS_VA_RDLB_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
    Int8        vs                   [MAX_VS_VA_RDLB_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
    Int8        va                   [MAX_VS_VA_RDLB_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
    SignedInt16 vsVaDiff             [MAX_VS_VA_RDLB_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
    Int8        lastRdBlockAcq       [MAX_VS_VA_RDLB_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
    SignedInt16 lastRdBlockAcqVaDiff [MAX_VS_VA_RDLB_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
    Int8        numBlksReqdFromRd    [MAX_VS_VA_RDLB_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
}
MacIntTxVsVaRdLbDebugInd;

typedef struct MacIntTxInfoReqDebugIndTag
{
    Int8        numRecords;
    Int32       frameNumber            [MAX_TX_INFO_REQ_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
    Boolean     csChanged              [MAX_TX_INFO_REQ_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
    Int8        numNackedBlocksPending [MAX_TX_INFO_REQ_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
    Boolean     pendingBlocksChanged   [MAX_TX_INFO_REQ_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
}
MacIntTxInfoReqDebugInd;

typedef struct MacIntTxBlksPurgDebugIndTag
{
    Int8                numRecords;
    Int32               frameNumber   [MAX_TX_PURGE_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
    Int8                numBlksPurged [MAX_TX_PURGE_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
    L1MacTxBlockType    blkType       [MAX_TX_PURGE_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
    Int8                bsn           [MAX_TX_PURGE_RECORDS + MAX_TX_DEBUG_BUFF_OVERUN];
}
MacIntTxBlksPurgDebugInd;

#if defined (UPGRADE_EDGE)
/* NOTE these are exact copies of the EDGE quality structures defined in macmain.h but have been
 * reproduced here as macmain.h already has to include this header. These structures must match
 * the structures in macmain.h to allow correct debug logging*/
typedef struct MacIntBepTsInfoTag
{
    Int8                  timeslot;                             /*The timeslot for these measurements*/
    Int16                 reliability;                          /*The incremental r reliability parameter*/
    Int16                 meanBepTs;                            /*Mean BEP averaged per timeslot per modulation type*/
    Int16                 cvBepTs;                              /*CV BEP averaged per timeslot per modulation type*/
} MacIntBepTsInfo;

typedef struct MacIntBepMeasurementsTag
{
    Int16                 meanBep;                              /*Mean BEP averaged over all timeslots per modulation type*/
    Int16                 cvBep;                                /*Mean CV averaged over all timeslots per modulation type*/
    Int16                 bepCount;                             /*The count of the total number of BEP measurements for a
                                                                 *given modulation type*/
    Int8                  numOfTsMeasurements;                  /*The number of timeslots that have measurements stored*/
    MacIntBepTsInfo       bepTsInfo[MAX_SUPPORTED_TIMESLOTS];   /*Array of timeslot measureements*/
} MacIntBepMeasurements;

typedef struct MacIntEgprsRxQualDebugIndTag
{
    Int8                  bepPeriod;                            /*BEP_PERIOD or BEP_PERIOD2, makes no difference to MAC*/
    Int16                 forgettingFactor;                     /*The e forgetting factor derived from the bepPeriod*/
    MacIntBepMeasurements bepGmskMeasurements;
    MacIntBepMeasurements bep8pskMeasurements;
} MacIntEgprsRxQualDebugInd;
#endif

#endif /* DEVELOPMENT_VERSION */
#endif /* MCINTSIG_H */

/* END OF FILE */


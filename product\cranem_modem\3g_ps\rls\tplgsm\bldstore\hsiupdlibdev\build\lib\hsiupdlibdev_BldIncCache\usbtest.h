/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/devices/transports/usb.mod/pub/src/usbtest.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/24 10:16:13 $
 **************************************************************************
 *  File Description :
 *    Data types etc for TTPCom USB protocol stack test interface.
 **************************************************************************/

#if defined(UPGRADE_USB)
#if defined(USB_TTPCOM_TEST_INTERFACE)

#ifndef USBTEST_H
#define USBTEST_H

/*******************************************************************************
 * Nested Include Files
 ******************************************************************************/
#include <usbappif.h>     /* for public fn data types & structures */

/*******************************************************************************
 * Global Data
 ******************************************************************************/
/* Size in bytes of the [max] data block transferred back and forth during
 * tests. */
#define USB_TEST_MAX_LOOPBACK_DATA_BLOCK_SIZE  240
#define USB_TEST_MAX_STREAMING_DATA_BLOCK_SIZE 512
#define USB_TEST_MAX_DATA_BLOCK_SIZE           USB_TEST_MAX_STREAMING_DATA_BLOCK_SIZE

/*******************************************************************************
 * Global Function Prototypes
 ******************************************************************************/

extern void    usbTestDevAppInitialise( void );
extern void    usbTestVendorDeviceRequest( Int8 logicalEndpoint, Int8* setupPacket );
extern Boolean usbTestVendorDevicePayload( UsbDataQuantum dataQuantum );
extern void    usbTestSetInterfaceInd( Int8 interfaceNumber, Int8 alternateNumber );
extern Boolean usbTestDataReceived( UsbDataQuantum dataQuantum );
extern void    usbTestResetApplication(TaskId taskId);
extern Int32   usbTestMaxOutTransfer( Int8 logicalEndpoint );
extern void    usbTestRxDataPending(Int8 logicalEndpoint, Int16 length);
extern void    usbTestReceiveBufferFreeSpace(Int8 logicalEndpoint, Int16 freeSpace);
extern void    usbTestTransmitComplete(Int8 physicalEndpoint);

#endif /* USBTEST_H */

#endif /* defined(USB_TTPCOM_TEST_INTERFACE) */
#endif /* defined(UPGRADE_USB) */
/* END OF FILE */

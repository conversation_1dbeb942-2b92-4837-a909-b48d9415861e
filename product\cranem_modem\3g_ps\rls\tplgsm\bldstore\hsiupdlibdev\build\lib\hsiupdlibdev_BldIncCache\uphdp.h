/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/phy/3g.mod/api/inc/uphdp.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/11/09 16:04:03 $
 *************************************************************************/
/** \file
 * \ingroup PrdCBEcoreWCDMAUphDp
 * \brief Top level header file for the Datapath module.
 * This file contains constant definitions and type definitions used by the
 * the datapath for both up and down links.
 *************************************************************************/

#ifndef UPHDP_H
#define UPHDP_H

/** \addtogroup PrdCBEcoreWCDMAUphDp
 * @{ */

/*******************************************************************************
** Includes
*******************************************************************************/

#include <system.h>

/*******************************************************************************
** Constants
*******************************************************************************/

/** Maximum code block size for Turbo coding.
 * Value taken from 3GPP TS 25.212 section 4.2.2.2. */
#define UPH_DP_TC_MAX_CODEBLOCKSIZE     (5114)

/** Minimum code block size for Turbo coding.
 *  Value taken from 3GPP TS 25.212 section 4.2.2.2. */
#define UPH_DP_TC_MIN_CODEBLOCKSIZE     (40)

/** Maximum code block size for convolutional coding.
 *  Value taken from 3GPP TS 25.212 section 4.2.2.2. */
#define UPH_DP_CC_MAX_CODEBLOCKSIZE     (504)

/** Minimum code block size for convolutional coding.
 *  Value taken from 3GPP TS 25.212 section 4.2.2.2. */
#define UPH_DP_CC_MIN_CODEBLOCKSIZE     (0)

/** Number of coded tail symbols for Turbo codes divided by the inverse of the
 *  code rate. */
#define UPH_DP_TC_TAIL_BITS             (4)

/** Number of coded tail symbols for convolutional codes divided by the inverse
 *  of the code rate.  */
#define UPH_DP_CC_TAIL_BITS             (8)

/** Inverse of the code rate for a half rate code. */
#define UPH_DP_CHANNEL_CODE_HALF_RATE   (2)

/** Inverse of the code rate for a third rate code. */
#define UPH_DP_CHANNEL_CODE_THIRD_RATE  (3)

/** Inverse of the code rate for Turbo codes. */
#define UPH_DP_CHANNEL_CODE_TURBO_RATE  (UPH_DP_CHANNEL_CODE_THIRD_RATE)

/** Memory of the constituent convolutional codes used in the Turbo coding
 *  scheme. */
#define UPH_DP_TC_CONST_CODE_MEMORY     (3)

/** Number of CRC bits per TrBlk for 0 bit CRC. */
#define UPH_DP_CRC_SIZE_0               (0)

/** Number of CRC bits per TrBlk for 8 bit CRC. */
#define UPH_DP_CRC_SIZE_8               (8)

/** Number of CRC bits per TrBlk for 12 bit CRC. */
#define UPH_DP_CRC_SIZE_12              (12)

/** Number of CRC bits per TrBlk for 16 bit CRC. */
#define UPH_DP_CRC_SIZE_16              (16)

/** Number of CRC bits per TrBlk for 24 bit CRC. */
#define UPH_DP_CRC_SIZE_24              (24)

/** Number of columns in the second interleaving matrix. */
#define UPH_DP_INTL2_COLUMNS            (30)

/** Definition used to access the number of frames per TTI (log2)
 *  in the log2NumFramesPerTti word in the datapath action state
 *  buffers. */
#define UPH_DP_LOG2NUMFRAMESTTI_TRCH_LSB    (0)

/** Definition used to access the number of frames per TTI (log2) in the
 *  log2NumFramesPerTti word in the datapath action state buffers. */
#define UPH_DP_LOG2NUMFRAMESTTI_TRCH_MASK                                      \
    (0x7 << UPH_DP_LOG2NUMFRAMESTTI_TRCH_LSB)

/** Definition used to access the maximum number of frames per TTI
 *  (log2, taken over all the transport channels in the coded composite
 *  transport channel) in the log2NumFramesPerTti word in the datapath action
 * state buffers. */
#define UPH_DP_LOG2NUMFRAMESTTI_MAX_LSB (3)

/** Definition used to access the maximum number of frames per TTI
 *  (log2, taken over all the transport channels in the coded composite
 *  transport channel) in the log2NumFramesPerTti word in the datapath action
 * state buffers. */
#define UPH_DP_LOG2NUMFRAMESTTI_MAX_MASK                                       \
    (0x7 << UPH_DP_LOG2NUMFRAMESTTI_MAX_LSB)

/*******************************************************************************
** Macros
*******************************************************************************/

/*******************************************************************************
** Typedefs
*******************************************************************************/

/** Indicates presence/absence and type of TFCI. */
typedef enum UphDpTfciModeTag
{
    UPH_DP_TFCI_FULL          = 0,  /**< Normal mode TFCI */
    UPH_DP_TFCI_SPLIT_HARD    = 1,  /**< Split mode TFCI: hard split */
    UPH_DP_TFCI_SPLIT_LOGICAL = 2,  /**< Split mode TFCI: logical split */
    UPH_DP_TFCI_NONE                /**< No TFCI */

} UphDpTfciMode;

/** Number of channelisation codes on the coded composite transport
 *  channel. */
typedef enum UphDpNumPhChTag
{
    UPH_DP_PCH_1_CODE    = 1,
    UPH_DP_PCH_2_CODES   = 2,
    UPH_DP_PCH_3_CODES   = 3,
    UPH_DP_PCH_MAX_CODES = 3

} UphDpNumPhCh;

/** Type used to indicate which method is used to generate compressed mode
 *  gaps. */
typedef enum UphDpCmModeTag
{
    UPH_DP_CM_NONE,             /**< No compressed mode gap. */
    UPH_DP_CM_BY_PUNCTURING,    /**< Compressed mode by puncturing.
                                 * \todo This has been removed from ASN so could be
                                 *       removed here too.
                                 */
    UPH_DP_CM_BY_SFREDUCTION,   /**< Compressed mode by sf reduction. */
    UPH_DP_CM_BY_HLSCHEDULING   /**< Compressed mode by higher layer scheduling. */
} UphDpCmMode;

/*******************************************************************************
** Global Data
*******************************************************************************/

/*******************************************************************************
** Global Function Prototypes
*******************************************************************************/

/** @} */ /* End of PrdCBEcoreWCDMAUphDp group */

#endif

/* END OF FILE */

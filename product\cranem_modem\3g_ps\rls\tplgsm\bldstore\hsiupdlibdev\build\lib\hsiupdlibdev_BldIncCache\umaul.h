/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/umaul.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2007/02/02 13:54:44 $
 **************************************************************************
 * File Description:
 *
 *    Contains the internal declarations for UMAC
 **************************************************************************/

#if !defined (UMAUL_H)
#define       UMAUL_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <system.h>
#include <kernel.h>
#include <cmac_sig.h>
#include <umac_sig.h>
#include <uphy_sig.h>
#include <umautl.h>


/******************************************************************************
 * Global Variables
 *****************************************************************************/
#if !defined (ON_PC)
extern USecurityKey umaUlCsCipherKey_g;
extern USecurityKey umaUlPsCipherKey_g;
#endif /* ON_PC */

/******************************************************************************
 * Function Prototypes
 *****************************************************************************/
/* Interface Functions */
size_t UmaUlInit(UmacEntity *umac_p);
void UmaUlToIdle(void);
void UmaUlProcessSignal(SignalBuffer *rxSignal_p);
void UmaUlProcessUphySignal(SignalBuffer *rxSignal_p);
void UmaUlProcessUmacSignal(SignalBuffer *rxSignal_p);
void UmaUlProcessCmacSignal(SignalBuffer *rxSignal_p);
void UmaUlDeleteSavedSignals ( void );

Int8 UmaUlGetMaxConfiguredTti(void);
Int8 UmaUlFramesInMinCfgTti(void);
void UmaUlGetHfn(UmacTmCountcInfo *tmCountcInfo_p);

void UmaUlHandleCipheringConfigReq(CmacCipheringConfigReq *cmacCipheringConfigReq_p);
void UmaUlStartIncrementingHfn(void);
void UmaUlStopIncrementingHfn(void);
void UmaUlIncrementHfn(void);
void UmaUlHandoverCsRabs(CmacHandoverType handoverType, URAB_Identity rabId);
void UmaUlNextConfigHandover(CmacHandoverType handoverType);
void UmaUlUpdateCsConfig(void);
Boolean UmaUlAwaitingUmacDataReq(void);

/* Interface to umameas */
void UmaUlGetMeasurement(UTransportChannelType trChType,
                UTransportChannelIdentity trChId,
                UTrafficVolumeReportingQuantity  *requestMeasurements_p,
                UTrafficVolumeMeasQuantity *interval_p,
                CmacAdditionalTrafficMeasurement *trafficMeasurement);
void UmaUlGetMeasurementAllTrChs(
                UTrafficVolumeReportingQuantity  *requestMeasurements_p,
                UTrafficVolumeMeasQuantity *interval_p,
                CmacAdditionalTrafficMeasurement *trafficMeasurement);

void UmaUlStoreTrafficEvent(UMeasurementIdentity measurementIdentity,
                            Boolean onlyStoreNewTrChs,
                            UTransportChannelType trChType,
                            UTransportChannelIdentity trChId,
                            UTrafficVolumeEventParam *eventCriterion_p);
void UmaUlStoreTrafficEventAllTrChs(
                            UMeasurementIdentity measurementIdentity,
                            Boolean onlyStoreNewTrChs,
                            UTrafficVolumeEventParam *eventCriterion_p);
void UmaUlRemoveTrafficEvent(UMeasurementIdentity measurementIdentity);

/* This function stops U-plane (but not C-plane) Tx until one of the criteria
   in section 14.4.4 of TS 25.331 are fulfilled */
void UmaUlInterruptUserPlaneTransmissions(Int16 inhibitUplaneTxTimeInFrames);
void UmaUlDedicatedClear(void);
void UmaUlPrepareTrChsPriority(void);

void UmaSendUmacTrafficIndNull(void);


#ifdef ENABLE_ULDATA_FUNCTION_CALL
void UmaUlXchHandleTrafficReqFn(UmacTrafficReq *signal_p);
void UmaUlXchHandleNoTrafficReqFn(UmacNoTrafficReq *signal_p);
void UmaUlHandlePhyFrameIndFn(SignalBuffer *rxSignal_p);
#endif
void UmaUlPhyResetAbortRachProcedure(void);


#if defined(PS_L2_R8_API)
void UmaUlProcessCmacRachAccessCnf (CmacRachAccessCnf *cmacRachAccessCnf_p);
#endif /*PS_L2_R8_API*/
/********** - start*/
void UmaUlUpdateCsHfn(UMacHyperFrameNumber prevDlHfn, UMacHyperFrameNumber newDlHfn);
const CmacCsVoiceRabValiditityParams *UmaUlGetVoiceRabValidityParams(BearerIdentity firstRbId);
/********** - end*/
#endif

/* END OF FILE */

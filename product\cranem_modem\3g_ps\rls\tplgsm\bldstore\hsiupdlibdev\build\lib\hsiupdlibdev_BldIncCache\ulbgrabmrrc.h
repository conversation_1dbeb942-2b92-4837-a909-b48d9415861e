/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/3g.mod/lib/src/ulbgrabmrrc.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2011/03/28 20:17:00 $
 **************************************************************************
 * File Description:
 *
 * ULBG RABM RRC functions to process received RRC signals and send signals
 * to DLBG and RRC
 **************************************************************************/

#if !(defined ULBGRABMRRC_H)
#define       ULBGRABMRRC_H

/**** INCLUDE FILES ********************************************************/

#include <system.h>
#include <kernel.h>
#include <xrabmdata.h>
#include <rabmrrc_sig.h>


/**** FUNCTION DECLARATIONS ************************************************/

extern void UlbgRabmRabmRrcEstablishInd(XRabmEntity *sn);
extern void UlbgRabmRabmRrcEstablishRes(XRabmEntity *sn,RabmRrcEstablishRes *rabmRrcEstRes);
extern void UlbgRabmRabmRrcEstablishRej(XRabmEntity *sn);
extern void UlbgRabmRabmRrcReleaseInd(XRabmEntity *sn);
extern void UlbgRabmRabmRrcReleaseRes(XRabmEntity *sn,RabmRrcReleaseRes *rabmRrcRelRes);
extern void UlbgRabmRabmRrcStatusInd(XRabmEntity *sn);

#endif


/* END OF FILE */

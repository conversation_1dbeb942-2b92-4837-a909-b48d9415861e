/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/sys/tmm.mod/api/inc/tmm_sig.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/24 11:37:14 $
 **************************************************************************
 * File Description: Traffic Memory Manager Flow Control Signal Definitions
 **************************************************************************/

#ifndef TMM_SIG_H
#define TMM_SIG_H

/***************************************************************************
 * Nested Include Files
 **************************************************************************/
#include <system.h>
#include <tmmtyp.h>

/***************************************************************************
 * Constants
 **************************************************************************/
#define MAX_TMM_MEM_OP_RECORDS 20

/***************************************************************************
 * Type Definitions
 **************************************************************************/
typedef struct UtMemAboveVhwmIndTag
{
    TmmPoolId poolId;
}
UtMemAboveVhwmInd;

typedef struct UtMemAboveHwmIndTag
{
    TmmPoolId poolId;
	/* If flow control based on allocs count is active, and that was the reason for the HWM signaling, then allocCountAboveHwm value is TRUE */
    Boolean allocCountAboveHwm;    
	
}
UtMemAboveHwmInd;

typedef struct UtMemBelowLwmIndTag
{
    TmmPoolId poolId;
}
UtMemBelowLwmInd;

typedef enum UtMemOperationTag
{
    TMM_NO_OP = 0,
    TMM_FREE_MEM,
    TMM_ALLOC_MEM,
    TMM_TAIL_MEM
}
UtMemOperation;

typedef struct UtMemOpDebugEntryTag
{
    UtMemOperation operation;
    TmmPoolId      poolId;
    Int32          frameNumber;
    Int32          kernelTicks;
    TaskId         taskId;

    Int8           *mem_p;
    Int32          reqBytes;
    Int32          minBytes;
    Int32          blkSize; /* bytes allocated, bytes freed or bytes remaining after a tail */

    Int32          allocdMemory;
    Int16          allocCount;
}
UtMemOpDebugEntry;

typedef struct UtMemOpDebugIndTag
{
    Int16             numRecords;
    UtMemOpDebugEntry record [MAX_TMM_MEM_OP_RECORDS];
}
UtMemOpDebugInd;
#endif

/* END OF FILE */

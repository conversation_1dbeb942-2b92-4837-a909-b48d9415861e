/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/tplgsm/utinc/utmem32.h#5 $
 *   $Revision: #5 $
 *   $DateTime: 2006/02/09 12:25:17 $
 **************************************************************************
 * File Description
 * ----------------
 * Header file Windows shared memory
 **************************************************************************/

#ifndef UTMEM32_H
#define UTMEM32_H


#if defined (ON_PC)
/***************************************************************************
 * Compile Time Error Checking
 **************************************************************************/

/***************************************************************************
 * Nested Include Files
 **************************************************************************/

/***************************************************************************
 * Manifest Constants
 **************************************************************************/

/***************************************************************************
 * Type Definitions
 **************************************************************************/

/***************************************************************************
 * Typed Constants
 **************************************************************************/

 /***************************************************************************
 *  Macros
 **************************************************************************/

/***************************************************************************
 *  Function Prototypes
 **************************************************************************/
extern void
utWin32CreateSharedMemory   (void ** memory_pp,Int32 *fileNumber_p);

extern void
utWin32MapMemory            (void ** memory_pp,Int32  fileNumber);

extern void
utWin32UnmapMemoryByPointer (void **memory_pp);

extern void
utWin32UnmapMemoryByFile    (Int32 fileNumber);
#endif /*ON_PC*/

#endif

/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urlam.h#6 $
 *   $Revision: #6 $
 *   $DateTime: 2007/03/19 13:59:30 $
 **************************************************************************
 * File Description:
 *
 *      Header file for UrlAm.c.
 *      Contains function call declarations, constants and types common to
 *      all URLC modules.
 **************************************************************************/

#if !defined (URLAM_H)
#define       URLAM_H

/***************************************************************************
* Nested Include Files
***************************************************************************/
#include <system.h>
#include <ki_sigbuf.h>
#include <ki_typ.h>
#include <url_sig.h>
#include <urlamtyp.h>
#include <utbitfnc.h>
#include <urlutil.h>
#include <tmm.h>
#include <umacerlcif.h>
#include <urlDebugIf.h>
#if defined (RP4_TEST)
#include <frled.h>
#endif /* RP4_TEST */

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/
/* Header sizes */
#define URL_AM_DATA_DC_BITS             1   /* Size of data PDU DC field (bits) */
#define URL_AM_DATA_SN_BITS             12  /* Size of data PDU SN field (bits) */
#define URL_AM_DATA_P_BITS              1   /* Size of data PDU P field (bits) */
#define URL_AM_DATA_HE_BITS             2   /* Size of data PDU HE field (bits) */
#define URL_AM_CONTROL_PDU_TYPE_BITS    3   /* Size of control PDU type (bits) */
#define URL_AM_SUFI_TYPE_BITS           4   /* Size of SUFI type field (bits) */
#define URL_AM_SUFI_LENGTH_BITS         4   /* Size of SUFI length field (bits) */
#define URL_AM_SUFI_SN_BITS             12  /* Size of SUFI seq num field (bits) */
#define URL_AM_SUFI_L_BITS              4   /* Size of SUFI L field (bits) */
#define URL_AM_SUFI_CW_VALUE_BITS       3   /* Size of SUFI CW field value (bits) */
#define URL_AM_SUFI_CW_E_BITS           1   /* Size of SUFI CW field e-bit (bits) */
#define URL_AM_SUFI_N_BITS              4   /* Size of SUFI N field (bits) */
#define URL_AM_RSN_BITS                 1   /* Size of RESET RSN field (bits) */
#define URL_AM_R1_BITS                  3   /* Size of RESET R1 field (bits) */

/* Total PDU header sizes */
#define URL_AM_DATA_HEADER_BITS         16  /* Size of control PDU header (bits) */
#define URL_AM_CONTROL_HEADER_BITS      4   /* Size of control PDU header (bits) */
#define URL_AM_DATA_HEADER_OCTETS       (URL_AM_DATA_HEADER_BITS / BITS_PER_INT8)

/* Header field constants */
#define URL_AM_PDU_DC_CONTROL           0   /* D/C=Data */
#define URL_AM_PDU_DC_DATA              1   /* D/C=Control */
#define URL_AM_PDU_P_DISABLED           0   /* P=Polling disabled */
#define URL_AM_PDU_P_ENABLED            1   /* P=Polling enabled*/
#define URL_AM_PDU_TYPE_STATUS          0   /* Control PDU type=STATUS */
#define URL_AM_PDU_TYPE_RESET           1   /* Control PDU type=RESET */
#define URL_AM_PDU_TYPE_RESET_ACK       2   /* Control PDU type=RESET_ACK */
#define URL_AM_SUFI_TYPE_NO_MORE        0   /* Control PDU SUFI type=NO_MORE */
#define URL_AM_SUFI_TYPE_WINDOW         1   /* Control PDU SUFI type=WINDOW  */
#define URL_AM_SUFI_TYPE_ACK            2   /* Control PDU SUFI type=ACK     */
#define URL_AM_SUFI_TYPE_LIST           3   /* Control PDU SUFI type=LIST    */
#define URL_AM_SUFI_TYPE_BITMAP         4   /* Control PDU SUFI type=BITMAP  */
#define URL_AM_SUFI_TYPE_RLIST          5   /* Control PDU SUFI type=RLIST   */
#define URL_AM_SUFI_TYPE_MRW            6   /* Control PDU SUFI type=MRW     */
#define URL_AM_SUFI_TYPE_MRW_ACK        7   /* Control PDU SUFI type=MRW_ACK */
#define URL_AM_SUFI_TYPE_POLL           8   /* Control PDU SUFI type=POLL    */

/* Maximum number of length indicators allowed in a data PDU when configured
 * for SDU discard with explicit signalling
 */
#define URL_AM_MAX_EXPLICIT_DISCARD_LIS 15

/* Constants relating to a bitmap SUFI */
#define URL_AM_BITMAP_SUFI_OCTETS       (URL_AM_SN_MODULUS / BITS_PER_INT8)
#define URL_AM_BITMAP_SUFI_ACK          1   /* PDU correctly received */
#define URL_AM_BITMAP_SUFI_NACK         0   /* PDU not correctly received */

#define URL_AM_BITMAP_SUFI_ALL_ACK_BYTE		0xFF /* all bits within a byte contains ACKed PDUs */
#define URL_AM_BITMAP_SUFI_ALL_NACK_BYTE	0x00 /* all bits within a byte contains NACKed PDUs */


/* Minimum number of SDUs structure to validate in order to reassemble 1 RLC SDU */
/*Fixed ********** start 2012-09-12*/
#define URL_AM_DL_MIN_SDUS_TO_VALIADTE	2
/*Fixed ********** end 2012-09-12*/
#ifdef ENABLE_NEW_URLC_OPTIMISATIONS_1
#define UL_SEGMENT_ON 0x1
#define UL_DEL_ON     0x2
#define DL_NACK_ON    0x4
#define DL_RESSEAMBELE_ON 0x8
#define DL_SDU_DELIVER_ON 0x10

#define UL_SEGMENT_OFF 0xFE
#define UL_DEL_OFF    0xFD
#define DL_NACK_OFF   0xFB
#define DL_RESSEAMBELE_OFF 0xF7
#define DL_SDU_DELIVER_OFF 0xEF
#endif


/* Constant defining the maximum size of an MRW STATUS PDU, used to save its
 * contents for retransmsion when MRW timer expires
 */
#define URL_AM_MAX_MRW_OCTETS           BITS_TO_INT8S (                        \
                                            URL_AM_PDU_CONTROL_HEADER_BITS +   \
                                            URL_AM_SUFI_MRW_BITS (             \
                                                URL_AM_SUFI_MAX_MRW_LENGTH ) + \
                                            URL_AM_SUFI_TYPE_BITS /* NO_MORE */\
                                           )
/* Masks */
#define URL_AM_R2_MASK                          0x80    /* mask of Reserved 2 (R2) 25.322 9.2.2.12 */
#define URL_AM_CONTROL_PDU_TYPE_MASK            0x70    /* mask of control PDU type 25.322 9.2.2.2 */

/***************************************************************************
 *   Macro Functions
 ***************************************************************************/

/* Minimum usable size for a data PDU */
#define URL_AM_MIN_DATA_PDU_BITS        (URL_AM_PDU_DATA_HEADER_BITS +         \
                                         BITS_PER_INT8 /* 1 octet of data */)

/* Minimum usable size to allow the smallest of all control PDUs */
#define URL_AM_MIN_CTRL_PDU_BITS        (URL_AM_PDU_CONTROL_HEADER_BITS +      \
                                         URL_AM_SUFI_BITMAP_BITS(1) +          \
                                         URL_AM_SUFI_NO_MORE_BITS)

/* Macro to determine if a bearer is configured for loopback mode, either
 * active or pending
 */
#define URL_AM_LOOPBACK(entity_p)           ((entity_p)->loopback)

/* Macro to indicate that there are UL SDUs which need segmenting */
#define URL_AMTX_SEGMENT_SDU(entity_p)      ((entity_p)->ulSegmentSdu)

/* Macro to indicate that there are ACKd UL PDUs and SDUs that need deleting */
#define URL_AM_UL_LIST_DELETION(entity_p)   ((entity_p)->ulDeletePdusAndSdus)

/* Macro to indicate that DL SDU reassembly should be attempted */
#define URL_AM_DL_SDU_REASSEMBLY(entity_p)  ((entity_p)->dlDataPdus.reassemble)

/* Macro's to access a bearer's UL and DL COUNT-C information for VR(H)-1 */
#define URL_AM_UL_COUNTC_INFO(entity_p)     ((entity_p)->ulCountcInfo)
#define URL_AM_DL_COUNTC_INFO(entity_p)     ((entity_p)->dlCountcInfoHi)

/* Macro to determine if UL data is available for transmission */
#define URL_AM_UL_DATA_AVAILABLE(entity_p)  (Boolean)                                     \
    ((entity_p->bufferInfo.tfcSelection.info.am.numDataBitsForTx    > 0) ||               \
     (entity_p->bufferInfo.tfcSelection.info.am.numControlBitsForTx > 0) ||               \
     (entity_p->bufferInfo.tfcSelection.info.am.numControlOnDataLogChBitsForTx > 0) ||    \
     (entity_p->bufferInfo.trafficVolumeBits > 0)                                         \
    )

/* Macro to determine how many full PDUs can fit into a maximum sized SDU */
#define URL_AM_MAX_PDUS_PER_SDU(pduOctets)                                     \
    (((UPS_MAX_RLC_SDU_DATA_OCTETS) + (pduOctets - 1)) / (pduOctets))

/* Macro to define the maximum size of an AM data PDU, allowing for any defined
 * SDU start offset an extra PDU, to handle a maximum sized SDU which starts at
 * an offset from the base PDU :
 *
 *            Offset
 *               |<----------------------- SDU data --------------------->|
 *               |
 *           ---------------------------------\  \------------------------|
 *  Base SDU |   |                             \  \                   |End|
 *           -----------------------------------\  \----------------------|
 *                                                                    |   |
 *               |---|>------------------> Append >------------------>|---|
 *               |   |
 *           ----------------------------------\  \--------------------
 *  Term SDU |   |End|                          \  \                  |
 *           ------------------------------------\  \------------------
 */
#define URL_AM_RX_SDU_BLOCK_OCTETS(entity_p, firstPduOctetes)               \
    (entity_p->config.dl.sduStartOctetOffset +                              \
        ((URL_AM_MAX_PDUS_PER_SDU (firstPduOctetes) + 1) * firstPduOctetes) \
    )

#define URL_AM_RX_LAST_SDU_OCTET_OCTETS(entity_p, pduOctetes)               \
    (entity_p->config.dl.sduStartOctetOffset + pduOctetes)

/* Out Of Sequence (OOS) Macros */
#define URL_AM_OOS_LIST_INDEX(sN) (sN>>URL_AM_OOS_LIST_DOWNSHIFT)
#define URL_AM_OOS_PDU_OFFSET_ARRAY_INDEX(sN) (sN&URL_AM_OOS_NUM_PDUS_PER_ENTRY_NORM)

#define URL_AM_OOS_IS_SN_IN_ENRTY(sN,entry_p)\
	(((sN >= UT_DOUBLE_LINK_LIST_PAYLOAD(entry_p).firstSn)\
	  &&  (sN <= UT_DOUBLE_LINK_LIST_PAYLOAD(entry_p).lastSnStored)) ? TRUE : FALSE)
/***************************************************************************
 * Function Prototypes
 ***************************************************************************/

/* ==========================================================================
 * Functions in UrlAm.c
 * ========================================================================== */

extern  void
UrlAmTxRxHandler
    (UrlAmTxRxEntity                                  * entityData_p,
     SignalBuffer                                     * rxSignal_p
    );
extern  void
UrlAmProtocolError
    (UrlAmTxRxEntity                                  * entityData_p,
     CrlcStatusCause                                    cause,
     int                                                line
    );
extern  void
UrlAmResetProtocol
    (UrlAmTxRxEntity                                  * entityData_p,
     UrlAmResetType                                     cause,
     UrlSequenceNumber                                  ulSn,
     Boolean                                            ulRsn,
     UrlHyperFrameNumber                                ulHfn,
     UrlAmTxState                                       txState,
     UrlSequenceNumber                                  dlSn,
     Boolean                                            dlRsn,
     UrlHyperFrameNumber                                dlHfn
    );
extern  void
UrlAmCrlcResumeReq
    (UrlAmTxRxEntity                                  * entityData_p
    );
/*********** merged for FRBD 20140806 start*/
#if defined(UPGRADE_3G_UL_EL2)
extern  void
UrlAmUpdateResetStateAfterReEstablish
    (UrlAmTxRxEntity                                  * entityData_p
    );
#endif
/*********** merged for FRBD 20140806 end*/
extern char *
UrlAmGetPduStatusTypeString
    (UrlAmTxDataPduStatus                               status
    );

#if defined (ON_PC)
extern  char *
UrlAmGetCtrlPduTypeString
    (UrlAmCtrlPduType                                   pduType
    );
extern  char *
UrlAmGetCrlcStatusCauseString
    (CrlcStatusCause                                    cause
    );
extern  char *
UrlAmGetBearerReleaseCause
    (UrlBearerReleaseCause                              cause
    );
extern  char *
UrlAmGetSduDiscardCause
    (UrlcUlDiscardReason                                cause
    );
#endif /* ON_PC */
extern  void
UrlAmDiagnosticsReq
    (UrlAmTxRxEntity                                  * entityData_p
    );

#if defined (DEVELOPMENT_VERSION)
#if defined (URL_DL_PDU_TRACK)
/***************************************************************************
 * PDU Tracking RAM Log - Functions
 ***************************************************************************/
extern  void
UrlAmRxPduTrack
    (UrlAmTxRxEntity                               * entityData_p,
     UrlPduEventType                                 event,
     UrlSequenceNumber                               sn
    );
#endif /* URL_DL_PDU_TRACK */
#endif /* DEVELOPMENT_VERSION */

/* ==========================================================================
 * Functions in UrlAmTx.c
 * ========================================================================== */

extern  void
UrlAmTxCrlcConfigReq
    (UrlAmTxRxEntity                                  * entityData_p,
     CrlcConfigReq                                    * crlcConfigReq_p
    );
extern  void
UrlAmTxUrlcAmDataReq
    (UrlAmTxRxEntity                                  * entityData_p ,
     UrlcAmDataReq                                    * urlcAmDataReq_p
    );
extern  void
UrlAmTxCrlcStopReq
    (UrlAmTxRxEntity                                  * entityData_p
    );
/*modified ********** start*/
extern  void
UrlAmTxCrlcHaltReq
    (UrlAmTxRxEntity                                  * entityData_p,
     CrlcHaltReq						  * crlcHaltReq_p
    );
/*modified ********** end*/
extern  void
UrlAmTxCrlcContinueReq
    (UrlAmTxRxEntity                                  * entityData_p
    );
extern  void
UrlAmTxCrlcPrepareForCipherCfgChangeReq
    (UrlAmTxRxEntity                                  * entityData_p,
     CrlcPrepareCipherCfgChangeReq                    * crlcPrepareReq_p
    );
extern  Boolean
UrlAmTxGetTransportBlock
    (UrlAmTxRxEntity                                  * entityData_p,
     Boolean                                            control,
     dtcF8DescriptorInfo                              * cipherInfo_p,
     UrlcDebugUlPduInfo                               * debugPduInfo_p,
     cipherType_te									  * cipherAlogithm_p
    );

#if defined (UPGRADE_3G_EDCH)
extern  Int32
UrlAmTxGetTransportBlockEdch
    (UrlAmTxRxEntity                                  * entityData_p,
     Boolean                                            control,
     dtcF8DescriptorInfo                              * cipherInfo_p,
#if defined (URL_DEBUG_EDCH_OVER_DTC)
     UrlDebugRlcPduInfo                               * debugPduInfo_p,

#else
	 UrlcDebugUlPduInfo								  * debugPduInfo_p,
#endif
     UmacEdchTxReqId_t                                  edchTxReqId,
     cipherType_te									  * cipherAlgorithm_p
    );
extern  void UrlAmTxUmacTxStatusIndEdch
    (UrlAmTxRxEntity                                  * entityData_p,
     UmacEdchTxStatusInd                              * umacTxStatusInd_p
    );
#endif /* UPGRADE_3G_EDCH */

extern  Boolean
UrlAmTxPollTriggered
    (UrlAmTxRxEntity                                  * entityData_p,
     UrlAmTxCause                                       cause
    );
extern  void
UrlAmTxUpdateBufferInfo
    (UrlAmTxRxEntity                                  * entityData_p,
     int                                                line
    );
extern  void
UrlAmTxNewResetPdu
    (UrlAmTxRxEntity                                  * entityData_p,
     Boolean                                            rsn,
     UrlHyperFrameNumber                                hfn,
     Int8                                               txCount
    );
extern  void
UrlAmTxAppendWindowSufi
    (UrlAmTxCtrlPdu                                   * ctrlPdu_p,
     UrlSequenceNumber                                  wsn
    );
extern  Boolean
UrlAmTxCheckSduDiscardTimer
    (UrlAmTxRxEntity                                  * entityData_p,
     KernelTicks                                        kernelTicks
    );
extern  UT_DOUBLE_LINK_LIST_NODE_TYPE(UrlAmTxCtrlList) *
UrlAmTxNewStatusPdu
    (UrlAmTxRxEntity                                  * entityData_p,
     Int8                                               flags,
     UrlAmTxCause                                       cause,
     Int8                                               txCount
    );
extern  UrlAmTxCause
UrlAmTxDestroyUntransmittedAckNackReports
    (UrlAmTxRxEntity                                  * entityData_p
    );
extern  void
UrlAmTxNewAckNackStatusReport
    (UrlAmTxRxEntity                                  * entityData_p,
     UrlAmTxCause                                       cause
    );
extern  void
UrlAmTxRetryMrwStatusReport
    (UrlAmTxRxEntity                                  * entityData_p,
     Int8                                               txCount
    );
extern  void
UrlAmTxNewResetAckPdu
    (UrlAmTxRxEntity                                  * entityData_p,
     Boolean                                            rsn,
     UrlHyperFrameNumber                                hfn
    );
#if defined (ENABLE_NEW_URLC_OPTIMISATIONS)
extern void
UrlAmTxDelDataPduPayload
    (UrlAmTxDataPdu                                   * payload_p
    );
extern  Boolean
UrlAmTxSnInDataList
    (UrlAmTxRxEntity                                  * entityData_p,
     UrlAmTxDataPdu                                  ** dataPdu_pp,
     UrlSequenceNumber                                  sn
    );
#else /* !ENABLE_NEW_URLC_OPTIMISATIONS */
extern  Boolean
UrlAmTxSnInDataList
    (UrlAmTxRxEntity                                  * entityData_p,
     UT_DOUBLE_LINK_LIST_NODE_TYPE(UrlAmTxDataList)  ** startNode_p_p,
     UrlSequenceNumber                                  sn
    );
#endif /* ENABLE_NEW_URLC_OPTIMISATIONS */


#if defined (ENABLE_NEW_URLC_OPTIMISATIONS)
extern  Boolean
UrlAmTxSetAckState
    (UrlAmTxRxEntity                            * const entityData_p,
     UrlAmTxDataPdu                                   * pdu_p,
     UrlAmTxDataPduStatus                               ackState
    );
#else /* !ENABLE_NEW_URLC_OPTIMISATIONS */
extern  Boolean
UrlAmTxSetAckState
    (UrlAmTxDataPdu                                   * pdu_p,
     UrlAmTxDataPduStatus                               ackState
    );
#endif /* ENABLE_NEW_URLC_OPTIMISATIONS */

extern  void
UrlAmTxNewMrwAckStatusReport
    (UrlAmTxRxEntity                                  * entityData_p,
     Int8                                               nLength
    );
extern  Boolean
UrlAmTxNewPduPreparationAllowed
    (UrlAmTxRxEntity                                  * entityData_p
    );
extern  Boolean
UrlAmTxNewSduSegment
    (UrlAmTxRxEntity                                  * entityData_p
    );
extern  void
UrlAmTxUmacTxStatusInd
    (UrlAmTxRxEntity                                  * entityData_p,
     UmacTxStatusInd                                  * umacTxStatusInd_p
    );
extern  UrlAmRetransmitStatus
UrlAmTxRetransmitDataPdu
    (UrlAmTxRxEntity                                  * entityData_p,
#if defined (ENABLE_NEW_URLC_OPTIMISATIONS)
     UrlAmTxDataPdu                                   * dataPdu_p,
     UrlAmTxCause                                       cause
#else /* !ENABLE_NEW_URLC_OPTIMISATIONS */
     UT_DOUBLE_LINK_LIST_NODE_TYPE(UrlAmTxDataList)   * dataNode_p,
     UrlAmTxCause                                       cause,
     UT_DOUBLE_LINK_LIST_NODE_TYPE(UrlAmRtxDataList) ** rtxStart_pp
#endif /* ENABLE_NEW_URLC_OPTIMISATIONS */
    );
extern  void
UrlAmTxReset
    (UrlAmTxRxEntity                                  * entityData_p,
     UrlAmResetType                                     cause,
     UrlSequenceNumber                                  sn,
     Boolean                                            rsn,
     UrlHyperFrameNumber                                hfn,
     UrlAmTxState                                       txState,               //-+ CQ00238599 11-Oct-2012 +-
     Boolean                                            discardRrcMessages     //-+ CQ00238599 11-Oct-2012 +-
    );
extern  void
UrlAmTxBackgroundDeleteComplete
    (UrlAmTxRxEntity                                  * entityData_p
    );
extern  void
UrlAmTxBackgroundDelete
    (UrlAmTxRxEntity                                  * entityData_p
    );
extern  Boolean
UrlAmTxNewWindowReport
    (UrlAmTxRxEntity                                  * entityData_p
    );
extern  void
UrlAmTxSduBufferShrinkage
    (UrlAmTxRxEntity                                  * entityData_p
    );

#if defined (DEVELOPMENT_VERSION)
extern  void
UrlInjectAmUlErrorsHandler
    (CrlcTestModeParameters * crlcTestModeParameters_p
    );
#endif /* DEVELOPMENT_VERSION */

#if defined (ENABLE_NEW_URLC_OPTIMISATIONS)
extern void UrlAmTxUpdateVtA
    (UrlAmTxRxEntity                                  * entityData_p,
     const UrlSequenceNumber                            sequenceNumber);
#endif /* ENABLE_NEW_URLC_OPTIMISATIONS */

/* ==========================================================================
 * Functions in UrlAmRx.c
 * ========================================================================== */

extern  void
UrlAmRxCrlcConfigReq
    (UrlAmTxRxEntity                                  * entityData_p,
     CrlcConfigReq                                    * crlcConfigReq_p
    );
extern  void
UrlAmRxGetPduInfo
    (UrlAmTxRxEntity                                  * entityData_p,
     UmacDlPduListInfo                                * pduListInfo_p,
     Int8                                               pduNum,
#if defined(UMTS7_PRE_R8)
     Int8*												dstPtr,
#endif
     dtcF8DescriptorInfo                              * cipherInfo_p,
     cipherType_te									  * cipherAlgorithm
    );
extern  UrlcDebugDlPduStatus
UrlAmRxUmacDataInd
    (UrlAmTxRxEntity                                  * entityData_p,
     UmacDlPduListInfo                                * pduListInfo_p,
     Int32                                              pduNum,
     dtcF8DescriptorInfo                              * cipherInfo_p,
     UrlSequenceNumber                                  sn,
     KeySeqId                                           ksi,
     Int32												numDtcDescriptorsInDataInd
    );
extern  Boolean
UrlAmRxReassembleSdu
    (UrlAmTxRxEntity                                  * entityData_p
    );
#ifdef ENABLE_NEW_URLC_OPTIMISATIONS_1
extern void
UrlAmRxDeliverSdus
(UrlAmTxRxEntity                                  * entityData_p
    );
#else
extern Boolean
UrlAmRxDeliverSdus
    (void
    );
#endif 

#if defined(RLC_LOW_PRIOR_OPT)
extern void UrlAmRxDeliverSrbSdus(UrlAmTxRxEntity*);
#endif
extern  Boolean
UrlAmRxCheckStatusTriggers
    (UrlAmTxRxEntity                                  * entityData_p
    );
extern  void
UrlAmRxAboveHighWaterMark
    (UrlAmTxRxEntity                                  * entityData_p
    );
extern  void
UrlAmRxAboveVeryHighWaterMark
    (UrlAmTxRxEntity                                  * entityData_p
    );
extern  void
UrlAmRxBelowLowWaterMark
    (UrlAmTxRxEntity                                  * entityData_p
    );
extern  void
UrlAmRxUrlcAmDataIndSend
    (UrlAmTxRxEntity                                  * entityData_p
    );
extern  void
UrlAmRxReset
    (UrlAmTxRxEntity                                  * entityData_p,
     UrlAmResetType                                     cause,
     UrlSequenceNumber                                  sn,
     Boolean                                            rsn,
     UrlHyperFrameNumber                                hfn
    );
extern  void
UrlAmRxCloseTestLoopReq
    (UrlAmTxRxEntity                                  * entityData_p,
     Int16                                              rlcSduBits
    );
extern  void
UrlAmRxCrlcOpenTestLoopReq
    (UrlAmTxRxEntity                                  * entityData_p
    );
extern  Boolean
UrlAmRxProcessAckNack
    (UrlAmTxRxEntity                                  * entityData_p,
#if defined (ENABLE_NEW_URLC_OPTIMISATIONS)
     UrlSequenceNumber                                  startingSn,
#else /* !ENABLE_NEW_URLC_OPTIMISATIONS */
     UT_DOUBLE_LINK_LIST_NODE_TYPE(UrlAmTxDataList)   * startNode_p,
#endif /* ENABLE_NEW_URLC_OPTIMISATIONS */
     UrlUsage                                           usage
    );

/* Out Of Sequence (OOS) */
extern UrlAmRxOosEntity *
UrlAmRxOosConstructor
    (UrlAmTxRxEntity                                  * entityData_p
    );
extern void
UrlAmRxOosDestructor
    (UrlAmTxRxEntity                                  * entityData_p
    );
extern void
UrlAmRxOosReset
    (UrlAmTxRxEntity                                  * entityData_p
    );
extern void
UrlAmRxOosGetSnAddress
    (UrlAmTxRxEntity                                  * entityData_p,
     UrlSequenceNumber                                  sn,
     dtcF8DescriptorInfo                              * cipherInfo_p,
     Int8                                            ** dstData_pp,
     Int16                                            * dstBitOffset
    );
extern void
UrlAmRxOosInheritPdus
    (UrlAmTxRxEntity                                  * entityData_p,
     UT_DOUBLE_LINK_LIST_NODE_TYPE(UrlAmRxSduList)    * sduNode_p
    );
extern void
UrlAmRxOosMrwDiscard
    (UrlAmTxRxEntity                                  * entityData_p,
     UrlSequenceNumber                                  snMRW,
     Int32                                              windowSize,
     Int8                                             * snPresentFlags_p,
     Int8                                             * liPresentFlags_p
    );
extern void
UrlAmRxOosFillSdu
    (UrlAmTxRxEntity                                  * entityData_p,
     UT_DOUBLE_LINK_LIST_NODE_TYPE(UrlAmRxSduList)    * sduNode_p
    );
/* Downlink Flow Control Module */
extern void
UrlAmDfcConstructor
    ( UrlAmTxRxEntity   * entityData_p
    );
extern void
UrlAmDfcRxHwm
    ( UrlAmTxRxEntity   * entityData_p
    );
extern void
UrlAmDfcRxVhwm
    ( UrlAmTxRxEntity   * entityData_p
    );
extern void
UrlAmDfcRxLwm
    ( UrlAmTxRxEntity   * entityData_p
    );
extern void
UrlAmDfcRxTimerExp
    ( UrlAmTxRxEntity   * entityData_p
    );
extern void
UrlAmDfcRxNumPdusMoreThanOne
    ( UrlAmTxRxEntity   * entityData_p
    );

#if defined (DEVELOPMENT_VERSION)
extern  void
UrlInjectAmDlErrorsHandler
    (CrlcTestModeParameters * crlcTestModeParameters_p
    );
#endif /* DEVELOPMENT_VERSION */

void UrlAmSetUlState (UrlAmTxRxEntity *entityData_p, UrlAmState ulState, int line);
void UrlAmSetDlState (UrlAmTxRxEntity *entityData_p, UrlAmState dlState, int line);
void UrlAmSetTxState (UrlAmTxRxEntity *entityData_p, UrlAmTxState txState, int line);

#if defined(UMTS7_PRE_R8)&& !defined(ENABLE_URLC_UNIT_TEST)
extern void ulbgprocessPsXoffInd(UrlcXoffInd *urlcXoffInd);
#endif
#endif /* URLAM_H */

/* END OF UrlAm.h */

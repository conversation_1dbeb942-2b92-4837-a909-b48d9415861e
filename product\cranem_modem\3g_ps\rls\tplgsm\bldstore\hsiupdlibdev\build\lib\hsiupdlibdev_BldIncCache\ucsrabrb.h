/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/api/inc/ucsrabrb.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2006/11/08 16:38:01 $
 **************************************************************************
 * File Description:
 *
 * Header for CSDI routines for keeping track of radio bearers and
 * radio access bearers.
 **************************************************************************/

#if !defined (UCSRABRB_H)
#define       UCSRABRB_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <uas_asn.h>
#include <csdi_sig.h>

/***************************************************************************
*   Manifest Constants
***************************************************************************/

/***************************************************************************
*   Macro Functions
***************************************************************************/

/***************************************************************************
*   Types
***************************************************************************/


typedef struct UcsAmrSavedBitsTag
{
    Boolean present;
    Int16 bitLength;
    /* actually the A,B,C bits are of different maximum length, but
       it's simpler to waste a little space here */
    Int8 bits[UCS_AMR_MAX_NUM_BYTES];
}
UcsAmrSavedBits;

typedef struct UcsAmrRabInfoTag
{
    /* numberOfRBs is either 2 or 3. */
    Int8 numberOfRBs;
    BearerIdentity  bearerIdentity [3];

    /* the data saved here are of DL speech, which we have to save
       until we have a complete set of A, B, possibly C, bits. */
    UrlcSduStatus status;
    UcsAmrSavedBits savedDlBits[3];
	CsCodecType codecType;
}
UcsAmrRabInfo;

typedef struct UcsCsdRabInfoTag
{
    BearerIdentity bearerIdentity;
}
UcsCsdRabInfo;

typedef struct UcsRabInfoTag
{
    UcsRabIdentity rabIdentity;
    /* configured is whether we've had a CcsdConfigReq without a
       subsequent Release. */
    Boolean configured;
    /* connected is whether we've had a CsdiConnectReq without a
       subsequent Disconnect */
    Boolean connected;
    /* active is whether we've had a CsdiResumeReq without a
       subsequent CsdiSuspendReq.  Only applicable when we're
       configured. */
    Boolean active;

    Boolean inUse;

    /* The most recent config data */
    Int8            numberOfRates;
    UTransmissionTimeInterval transmissionTimeInterval;
    CcsdCsRate      activeCsRates[UPS_MAX_UL_TFC];

    /* The cfn when we last received data */
    Int8 currentCfn;

    UcsRabType rabType;
    union
    {
        UcsAmrRabInfo amr;
        UcsCsdRabInfo csd;
    }
    info;
}
UcsRabInfo;

/***************************************************************************
*   Function declarations
***************************************************************************/

void UcsRbLookup (BearerIdentity bearerIdentity,
             Int8 *rbIndex_p,
             UcsRabInfo **rabInfo_pp,
             Boolean *found_p);
void UcsRabLookup (UcsRabIdentity rabIdentity,
              UcsRabInfo **rabInfo_pp,
              Boolean *found_p);
void UcsRabAddByBearers (UcsRabIdentity rabIdentity,
                         Int8 numberOfRBs,
                         UcsRabInfo **rabInfo_pp);
void UcsRabCheckTypeByBearers (UcsRabIdentity rabIdentity,
                               UcsRabInfo *rabInfo_p,
                               Int8 numberOfRBs);
void UcsRabAddByType (UcsRabIdentity rabIdentity,
                 UcsRabType rabType,
                 UcsRabInfo **rabInfo_pp);
void UcsRabInitialise (void);
void UcsRabInfoFinal (UcsRabInfo *rabInfo_p);
void UcsRabSetBearers (UcsRabInfo *rabInfo_p,
                  Int8 numberOfRBs,
                  BearerIdentity bearerIdentity1,
                  BearerIdentity bearerIdentity2,
                  BearerIdentity bearerIdentity3);

#endif

/* END OF FILE */

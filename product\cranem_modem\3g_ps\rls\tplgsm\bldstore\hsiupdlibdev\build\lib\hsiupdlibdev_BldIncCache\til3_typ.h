/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/pscommon/2g.typ/api/inc/til3_typ.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/26 22:05:37 $
 **************************************************************************
 *  File Description :
 *      types common to mmxx_sig.h rr_sig.h and alti_sig.h
 **************************************************************************/

#ifndef TIL3_TYP_H
#define TIL3_TYP_H

/****************************************************************************
 * Nested Include Files
 ****************************************************************************/

/****************************************************************************
 * Macros
 ****************************************************************************/

/****************************************************************************
 * Types
 ****************************************************************************/

typedef enum TestedDeviceTag
{
    TD_NORMAL_OPERATION         =   0,
    TD_SPEECH_DECODER           =   1,
    TD_SPEECH_ENCODER           =   2,
    TD_ACOUSTIC_DEVICES         =   4
}
TestedDevice;

#endif
/* END OF FILE */








































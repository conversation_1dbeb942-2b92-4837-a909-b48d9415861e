/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/umadl.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/27 15:10:43 $
 **************************************************************************
 * File Description:
 *
 *    Contains the internal declarations for UMAC
 **************************************************************************/

#if !defined (UMADL_H)
#define       UMADL_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <system.h>
#include <kernel.h>

#include <cmac_sig.h>
#include <umac_sig.h>
#include <uphy_sig.h>

#include <umautl.h>


#if !defined (ENABLE_UMAC_UNIT_TEST) && !defined(ENABLE_URLC_UNIT_TEST)
#define ENABLE_HS_PDULISTINFO_FUNCTION_CALL
#endif

#if defined(ENABLE_ULDATA_FUNCTION_CALL)
#define URL_WAIT_RLC_ULDATA_FUNCTIONCALL        KiWaitSemaphore (KI_URLC_ULDATA_FUNCTIONCALL)
#define URL_RELEASE_RLC_ULDATA_FUNCTIONCALL     KiIncSemaphore (KI_URLC_ULDATA_FUNCTIONCALL)
#define URL_WAIT_UL_CONFIG_CHANGE_IND           KiWaitSemaphore (KI_URLC_UL_CONFIG_CHANGE_IND)
#define URL_RELEASE_UL_CONFIG_CHANGE_IND        KiIncSemaphore (KI_URLC_UL_CONFIG_CHANGE_IND)
#define URL_WAIT_DL_CONFIG_CHANGE_IND           KiWaitSemaphore (KI_URLC_DL_CONFIG_CHANGE_IND)
#define URL_RELEASE_DL_CONFIG_CHANGE_IND        KiIncSemaphore (KI_URLC_DL_CONFIG_CHANGE_IND)
#endif
/***************************************************************************
 * Manifest Constants
 ***************************************************************************/
#define UMAC_MAX_DL_TR_CH_TYPES                 (2)
                                    /* DCH, FACH for now (future: DSCH...) */
                                    /* Needs to cope with logChId 1..16 */
#define UMAC_MAX_DL_LOGICAL_CHANNELS_PER_TR_CH (1+\
                                                (maxLoCHperRLC*maxRBMuxOptions))

#define UMAC_LOG_CH_ID_SINGLE_MAPPING           0
#define UMAC_LOG_CH_ID_FIRST                    1

#define UMAC_IS_SINGLE_MAPPING                  0x80

#define UMAC_FDD_FACH_TCTF_BCCH_LENGTH          2
#define UMAC_FDD_FACH_TCTF_CXCH_LENGTH          8
#define UMAC_FDD_FACH_TCTF_BCCH                 0x00
#define UMAC_FDD_FACH_TCTF_DXCH                 0x03
#define UMAC_FDD_FACH_TCTF_CCCH                 0x40
#define UMAC_FDD_FACH_TCTF_CTCH                 0x80

#define UMAC_RLC_DC_LENGTH                      1 /* URL_AM_DATA_DC_BITS */
#define UMAC_RLC_POLL_LENGTH                    1 /* URL_AM_DATA_P_BITS */
#define UMAC_RLC_HE_LENGTH                      2 /* URL_AM_DATA_HE_BITS */
#define UMAC_RLC_HE_E_BIT_SET                   URL_PDU_E_LI
#define UMAC_RLC_HE_MAX_ALLOWED                 0x02
#define UMAC_RLC_AM_HEADER_LENGTH               URL_AM_PDU_DATA_HEADER_BITS
                                                /* (UMAC_RLC_DC_FIELD_LENGTH + \
                                                URL_AM_SN_BITS +               \
                                                UMAC_RLC_POLL_LENGTH +         \
                                                UMAC_RLC_HE_LENGTH) */


#define UMAC_RLC_DC_MASK                        0x80
#define UMAC_RLC_SN_HIGH_MASK                   0x7F
#define UMAC_RLC_SN_HIGH_SHIFT                  5
#define UMAC_RLC_SN_LOW_MASK                    0xF8
#define UMAC_RLC_SN_LOW_SHIFT                   3
#define UMAC_RLC_POLL_MASK                      0x04
#define UMAC_RLC_HE_MASK                        0x03

#define UMAC_RLC_DC_MASK_4                      0x08
#define UMAC_RLC_SN_HIGH_MASK_4                 0x07
#define UMAC_RLC_SN_HIGH_SHIFT_4                9
#define UMAC_RLC_SN_MED_SHIFT_4                 1
#define UMAC_RLC_SN_LOW_MASK_4                  0x80
#define UMAC_RLC_SN_LOW_SHIFT_4                 7
#define UMAC_RLC_POLL_MASK_4                    0x40
#define UMAC_RLC_HE_MASK_4                      0x30
#define UMAC_RLC_HE_SHIFT_4                     4

/* Size of bitmap is dependent on how long an int is in implementation */
/* This is so that we can make counting the number of bits set in the  */
/* bitmap map to the architecture we're running on (32-bit or 16-bit)  */
#define UMAC_MAX_SIZE_OF_QUALITY_REPORTING_WINDOW (512/(sizeof(unsigned int)* \
                                                             BITS_PER_INT8))

/* See TS 25.133 section 9.1.5 */
#define UMAC_MAX_BLER_LOG_INDEX                 63
#define UMAC_BLER_FACTOR                        61285

#define UMAC_EXTRACT_CRNTI_FIELD                0xffff
#define UMAC_EXTRACT_E_HEADER                   0x01
#define UMAC_EXTRACT_HEADER_BITS                0x0004
#define UMAC_EXTRACT_HE_HEADER                  0x0003
#define UMAC_EXTRACT_DC_BITS                    0x8000
#define UMAC_EXTRACT_COUNTC                     0x7FF8

#define UMAC_MAX_DL_DATA_IND_PTRS        	  	(UPS_MAX_OVERLAPPING_UMAC_PDU_LIST_IND)
#define UMADL_MAX_DEBUG_PARAMS                  2

/***************************************************************************
 * Macros
 ***************************************************************************/
/*************************************************************************
 *
 * Function:    UMA_DL_EXTRACT_RLC_AM_HDR
 *
 * Scope:       LOCAL
 *
 * Parameters:
 *  in    : data_p - pointer to start of header
 *
 * Returns:     None
 *
 * Description: Extracts the RLC AM data header fields.
 *
 *************************************************************************/
#define UMA_DL_EXTRACT_RLC_AM_HDR(hdr_p, hdr, data_p, rlcInfo_p, cipherInfo_p, headerOffset)    \
{                                                                               \
    hdr_p = (data_p + (headerOffset / BITS_PER_INT8));                          \
    hdr = *hdr_p;                                                               \
    if(hdr & UMAC_RLC_DC_MASK)                                                  \
    {                                                                           \
        *rlcInfo_p |= UMAC_RLC_INFO_AM_DC;                                      \
        cipherInfo_p->countC = (((Int32)(hdr & UMAC_RLC_SN_HIGH_MASK)) << UMAC_RLC_SN_HIGH_SHIFT);  \
        hdr = *(hdr_p + 1);                                                     \
        cipherInfo_p->countC |= (((Int32)(hdr & UMAC_RLC_SN_LOW_MASK)) >> UMAC_RLC_SN_LOW_SHIFT);   \
        if(hdr & UMAC_RLC_POLL_MASK)                                            \
            *rlcInfo_p |= UMAC_RLC_INFO_AM_POLL;                                \
        hdr = (hdr & UMAC_RLC_HE_MASK);                                         \
        if(hdr == 2)	/*Fixed CQ34239 added by taoye  20130510*/				\
			*rlcInfo_p |= UMAC_RLC_INFO_LAST_OCTET;								\
        else																	\
			if(hdr == UMAC_RLC_HE_E_BIT_SET)									\
			{																	\
            	*rlcInfo_p |= UMAC_RLC_INFO_E;                                  \
			}																	\
        else                                                                    \
            if(hdr > UMAC_RLC_HE_MAX_ALLOWED)                                   \
            {                                                                   \
                cipherInfo_p->bearer = RB_INVALID;                              \
                cipherInfo_p->key_p = PNULL;                                    \
                UDTC_IF_DISABLE_CIPHERING(cipherInfo_p->dtcModeReg);            \
            }                                                                   \
        headerOffset += 16;                                                     \
        }                                                                       \
}                                                                               \

#define UMA_DL_EXTRACT_RLC_AM_HDR_4(hdr_p, hdr, data_p, rlcInfo_p, cipherInfo_p, headerOffset)  \
{                                                                               \
    hdr_p = (data_p + (headerOffset / BITS_PER_INT8));                          \
    hdr = *hdr_p;                                                               \
    if(hdr & UMAC_RLC_DC_MASK_4)                                                \
    {                                                                           \
        *rlcInfo_p |= UMAC_RLC_INFO_AM_DC;                                      \
        cipherInfo_p->countC = (((Int32)(hdr & UMAC_RLC_SN_HIGH_MASK_4)) << UMAC_RLC_SN_HIGH_SHIFT_4);  \
        hdr = *(hdr_p+1);                                                       \
        cipherInfo_p->countC |= (((Int32)(hdr)) << UMAC_RLC_SN_MED_SHIFT_4);    \
        hdr = *(hdr_p+2);                                                       \
        cipherInfo_p->countC |= (((Int32)(hdr & UMAC_RLC_SN_LOW_MASK_4)) >> UMAC_RLC_SN_LOW_SHIFT_4);   \
        if(hdr & UMAC_RLC_POLL_MASK_4)                                          \
            *rlcInfo_p |= UMAC_RLC_INFO_AM_POLL;                                \
        hdr = ((hdr & UMAC_RLC_HE_MASK_4)>>UMAC_RLC_HE_SHIFT_4);                \
		if(hdr == 2)	/*Fixed CQ34239 added by taoye	20130510*/				\
			*rlcInfo_p |= UMAC_RLC_INFO_LAST_OCTET; 							\
		else																	\
			if(hdr == UMAC_RLC_HE_E_BIT_SET)									\
			{																	\
				*rlcInfo_p |= UMAC_RLC_INFO_E;									\
			}																	\
        else                                                                    \
            if(hdr > UMAC_RLC_HE_MAX_ALLOWED)                                   \
            {                                                                   \
                cipherInfo_p->bearer = RB_INVALID;                              \
                cipherInfo_p->key_p = PNULL;                                    \
                UDTC_IF_DISABLE_CIPHERING(cipherInfo_p->dtcModeReg);            \
            }                                                                   \
        headerOffset += 16;                                                     \
        }                                                                       \
}                                                                               \

#define dlCtFieldRequired(a) (((a)->singleMappingAndId & UMAC_IS_SINGLE_MAPPING) ? 0 : 1)

/*************************************************************************
 *
 * Function:     UmaDlMacDextractLogChIdFromCt
 *
 * Scope:        LOCAL
 *
 * Parameters:
 *  in    : data_p - pointer to the received data
 *  in/out: sourceBitOffset_p - pointer to the UE type and UE id within
 *                      the received PDU list
 * in     : dlTrChMappingInfo_p - pointer to the downlink RB mapping info
 *                      for the TrCh on which this PDU list was received.
 *
 * Returns:      UmacLogicalChannelIdentity - the MAC logical channel
 *               identity from the received C/T field in the MAC header.
 *
 * Description:  See Returns.
 *
 *************************************************************************/
 #define  UmaDlMacDextractLogChIdFromCt(data_p, sourceBitOffset_p, dlTrChMappingInfo_p) \
    dlCtFieldRequired(dlTrChMappingInfo_p)?  \
    UtilExtract8BitField(data_p, sourceBitOffset_p, UMAC_CT_FIELD_LENGTH)+UMAC_LOGICAL_CHANNEL_START_IN_CT :\
        dlTrChMappingInfo_p->singleMappingAndId & (~UMAC_IS_SINGLE_MAPPING) \


/***************************************************************************
 * Type Definitions
 ***************************************************************************/
 
//ICAT EXPORTED ENUM
typedef enum UmaDlDebugMacCshStatusTag
{
	UMA_DL_DEBUG_MACCSH_DEFAULT,
	UMA_DL_DEBUG_MACCSH_NO_TRCH_DXCH,	
	UMA_DL_DEBUG_MACCSH_NO_CT,			
	UMA_DL_DEBUG_MACCSH_NO_ID_MATCH,		
	UMA_DL_DEBUG_MACCSH_NO_TRCH_CXCH,
	UMA_DL_DEBUG_MACCSH_INVALID_TCTF,	
	UMA_DL_DEBUG_MACCSH_BAD_CRC_OR_ZERO_TB,
	UMA_DL_DEBUG_MACCSH_HEADER_EXCEEDS_MAX_SIZE
	
}UmaDlDebugMacCshStatus;


//ICAT EXPORTED STRUCT
typedef struct UmaDlDebugAmrFrameTag
{
    Int16       bearer;
    Int32   countC;      
	/* frameQualityIndication is set to TRUE if there is no CRC error */
	Boolean		frameQualityIndication;	
	/* The actual size sent to debug depends on bitSizePerChannel */
	Int16		bitLengthPerChannel[3];
	Int8		amrFrame[100];
}UmaDlDebugAmrFrame;


typedef struct UmaDlDebugAmrMacSduTag
{
	UDlPduStatus	status;	
	Int16			bitLength;
	Int16			bitOffset;
    Int8            bearer;
    Int32           countC;    
	Int8		  * data_p;
}UmaDlDebugAmrMacSduInfo;

typedef struct DlBearerIdentitiesTag
{
    BearerIdentity bearerIdentity[UPS_MAX_DL_NO_OF_TB_PER_TTI];
}
DlBearerIdentities;

typedef struct UmaDlQualityEventReportTag
{
    UMeasurementIdentity    measId;
    CmacQualityEvent        report;
}
UmaDlQualityEventReport;

typedef struct UmaDlWindowRefTag
{
    Int8                   intOffset;
    unsigned int           intBitmask;
}
UmaDlWindowRef;

typedef struct UmaDlEventCriteriaTag
{
    UMeasurementIdentity   measurementIdentity;
/* The configuration */
    Int16  /* 1 to 512 */  totalCRC;
    Int16  /* 1 to 512 */  badCRC;
    Int16  /* 1 to 512 */  pendingAfterTrigger;
/* Event prohibit implementation */
    Int16 /* 1 to 512 */   pendingCrcsLeftAfterTrigger;
/* Whether we've actually received enough CRCs for first event */
    Int16                  noCrcSinceStart;
/* The window record */
    Int16                  noBadCrcs;
    UmaDlWindowRef         begin;
    UmaDlWindowRef         end;
    unsigned int           window[UMAC_MAX_SIZE_OF_QUALITY_REPORTING_WINDOW];
}
UmaDlEventCriteria;

typedef struct UmaDlPeriodicalCriteraTag
{
/* The measured results */
    Int32                  noBadCrcs;
    Int32                  totalCrcs; /* Worst case is 6400*32 for periodical */
#if defined (ENABLE_UPLANE_STATISTICS)
    Int32                    noBadCrcsStats;
    Int32                    totalCrcsStats;
    UDL_TransportChannelBLER maxdlTrChBlerStats[UPS_MAX_DL_NO_OF_TRCH];
#endif /* ENABLE_UPLANE_STATISTICS */
}
UmaDlPeriodicalCriteria;

typedef struct UmaDlQualityInfoTag
{
    Int8                    numberOfEventCriteria;
   /* There are up to 2 event criteria (25.133 section 8.2.3) [link traffic]*/
    UmaDlEventCriteria      eventCriteria[maxMeasParEvent];
   /* There is only *one* valid reporting quantity (25.133 section 8.2.2) */
    UmaDlPeriodicalCriteria periodical;
}
UmaDlQualityInfo;

typedef struct UmacDlTrCHMappingInfoTag
{
    /* These can only be ones from T_UDL_TransportChannelType */
    UTransportChannelType               trChType;
    UTransportChannelIdentity           trChIdentity;
    UmacConfigType                      configType;
    Int8                                ttiInFrames;
    UmaDlQualityInfo                    quality;
    Int8                                singleMappingAndId;
    /* the following arrays are indexed by logChId */
    BearerIdentity           rbIdentity[UMAC_MAX_DL_LOGICAL_CHANNELS_PER_TR_CH];
    UmacDomainMode           domainMode[UMAC_MAX_DL_LOGICAL_CHANNELS_PER_TR_CH];
}                            /* Logical channel => rbId mapping (LogCh index) */
UmacDlTrCHMappingInfo;       /* Should actually be 15, rather than 16, given  */
                             /* MAC header definition(ULogicalChannelIdentity)*/


typedef struct UmacDlTrChWithEventTag
{
    UTransportChannelIdentity           trChId;
    Int8                                eventId;
}
UmacDlTrChWithEvent;

typedef struct UmacDlEventsTag
{
    Int8                                numberOfEvents;
    UmacDlTrChWithEvent                 fired[UPS_MAX_DL_NO_OF_TRCH];
}
UmacDlEvents;

typedef struct UmacDlMeasurementEventTag
{
    UMeasurementIdentity                measId;
    UmacDlEvents                        events;
}
UmacDlMeasurementEvent;

UT_DOUBLE_LINK_LIST_DECLARE(UmacDlTrCHMappingInfoList, UmacDlTrCHMappingInfo)
/* Worst case list length: maxTrCh * 2 (DCH, FACH)  DSCH etc not supported */

UT_DOUBLE_LINK_LIST_DECLARE(UmacDlMeasurementEventList, UmacDlMeasurementEvent)

typedef struct UmaDlDataIndPointerTag
{
    UdtcRequestInfo                cipherReq;
    UDlPduList                     *pduList_p;
#if !defined (DATA_IN_SIGNAL)
    Boolean                         inUse;
#endif
}
UmaDlDataIndPointer;
/********** - start*/
typedef enum UmaDlDecipheredSidCheckResultTag
{
    UMA_DL_DEC_SID_CHECK_RESULT_BAD,
    UMA_DL_DEC_SID_CHECK_RESULT_POSSIBLY_GOOD,
    UMA_DL_DEC_SID_CHECK_RESULT_GOOD
}UmaDlDecipheredSidCheckResult;
/********** - end*/

typedef struct UmaDlEntityTag
{
    /* A pointer back to the Umac Entity */
    UmacEntity                     *umac_p;
    /* Mappings for going TrCH=>RB (DL), given a logical channel */
    UmacDlTrCHMappingInfoList       dlMappingInfoList;
    /* This array lists the tmCsRb identities */
#ifdef UPGRADE_DSDSWB_L2
    Int8                            noTmCsRbs[SIM_NUM];
#else
    Int8                            noTmCsRbs;
#endif
    BearerIdentity                  tmCsRb[maxRB];
    Int8                            tmCsRbTtiInFrames[maxRB];
    Int8                            maxFramesInTti;
    /* Quality measurement events */
    UmacDlMeasurementEventList      measList;
    /* DL COUNT-C, KSI and ciphering information */
    UmacCipheringInfo               csCipherInfo;
    UmacCipheringInfo               psCipherInfo;
    KiUnitQueue                     pendingPhyDataInds;

#if defined(DEVELOPMENT_VERSION)
    Int16                           numTbsForCrc;
    Int16                           maxNumTbsForCrc;
    Int16                           numCrcs;
    Int16                           curNumCrcs;
#endif

#if !defined(DATA_IN_SIGNAL)
    Int8                            curDataIndPtr;
    UmaDlDataIndPointer             dataIndPtr[UMAC_MAX_DL_DATA_IND_PTRS];
#endif /* !DATA_IN_SIGNAL */
    SignalBuffer                    umacDataIndPending;

#if defined(UPGRADE_3G_HSDPA)
    SignalBuffer                    umacHsDataInd;
#endif /* UPGRADE_3G_HSDPA */

    /* CBS */
    Boolean                         ctchIdValid;
    UTransportChannelIdentity       ctchIdValue;

} UmaDlEntity;


/******************************************************************************
 * Global Variables
 *****************************************************************************/
extern  UmaDlEntity* umaDl_p;

#if !defined (ON_PC)
extern USecurityKey umaDlCsCipherKey_g;
extern USecurityKey umaDlPsCipherKey_g;
#endif /* ON_PC */

/******************************************************************************
 * Function Prototypes
 *****************************************************************************/
 UmacDlTrCHMappingInfo *getDlTrChMappingInfo(
                UTransportChannelType trChType,
                UTransportChannelIdentity trChIdentity);

/* Interface to umameas */
void UmaDlClearPeriodicalOneTrCh(UTransportChannelIdentity trChIdentity);
void UmaDlClearPeriodicalAllTrChs(void);
Boolean UmaDlMeasPeriodicalOneTrCh(CmacQualityMeasurement *qualityMeasurement,
                            UTransportChannelIdentity trChIdentity);
Int8 UmaDlMeasPeriodicalAllTrChs(CmacQualityMeasurement *qualityMeasurements);
void UmaDlStoreQualityEvent(UMeasurementIdentity measurementIdentity,
                            Boolean onlyStoreNewTrChs,
                            UQualityReportingCriteriaSingle *eventCriterion);
void UmaDlRemoveQualityEvent(UMeasurementIdentity measurementIdentity);

/* State Machine Handlers for DL */
void UmaDlHandlePhyDataInd(SignalBuffer *sigbuf_p);
void UmaDlSetIsConsumedTrue(UDlPduList *pduList_p);
void UmaDlHandleUmacDlPduListInfoInd(SignalBuffer *sigbuf_p);
#if defined(UPGRADE_3G_HSDPA)
void UmaDlHandleUmacHsPduListInfoInd(SignalBuffer *sigbuf_p);
#endif /* UPGRADE_3G_HSDPA */
void UmaDlHandleUmacDlPduListInfoRsp(SignalBuffer *sigbuf_p);
void UmaDlF8chainComplete(void);
void UmaDlHandleDlRbMappingConfigReq(SignalBuffer *sigbuf_p);
void UmaDlHandleHfnAbortReq(SignalBuffer *sigbuf_p);
void UmaDlHandleTrchCombinationConfigReq(SignalBuffer *sigbuf_p);
/* Interface Functions to umamain */
#if defined(DEVELOPMENT_VERSION)
void UmaDlInjectCrcs(Int16 crcEachNblocks, Int16 crcBlocks);
#endif /* DEVELOPMENT_VERSION */
void UmaDlStartIncrementingHfn(Boolean isCipheringActivationTimePresent);
void UmaDlStopIncrementingHfn(void);
Boolean UmaDlConfigurationComplete(UmacInternalState umacState);
void UmaDlRemoveFachConfig(void);
void UmaDlHandleUlTmBearers(SignalBuffer *sigbuf_p);
void UmaDlGetHfn(UmacTmCountcInfo *tmCountcInfo_p);
void UmaDlIncrementHfn(void);
void UmaDlHandleCipheringConfigReq(CmacCipheringConfigReq *cmacCipheringConfigReq_p);
Int8 UmaDlGetMaxConfiguredTti(void);
size_t UmaDlInit (UmacEntity *umac_p);
void UmaDlSendUmacDlConfigChangeInd(void);

void UmaDlToIdle (void);
void UmaDlBindPhyXhsDataInd (UmacEnhancedDlType type);
void UmaDlResetXhsState(void);

/********** - start*/
void UmaDlUpdateCsHfn(UMacHyperFrameNumber newHfn);

void SendHfnMeasurementInd(void);
/********** - end*/

#endif //UMADL_H
/* END OF FILE */

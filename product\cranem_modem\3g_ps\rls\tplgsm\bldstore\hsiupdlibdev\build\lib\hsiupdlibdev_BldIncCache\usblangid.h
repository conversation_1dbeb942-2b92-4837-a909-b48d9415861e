/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

#if defined(UPGRADE_USB)

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/devices/transports/usb.mod/pub/src/usblangid.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/24 10:16:13 $
 **************************************************************************
 *  File Description :
 *    USB Organisation language ID's, V1.0 29 March, 2000.
 *    Note that the host may only support a subset of the valid ID's.
 **************************************************************************/

#ifndef USBLANGID_H
#define USBLANGID_H
#endif

/*******************************************************************************
 * Manifest Constants
 ******************************************************************************/

#define AFRIKAANS                     0x0436
#define ALBANIAN                      0x041c
#define ARABIC_SAUDI_ARABIA           0x0401
#define ARABIC_IRAQ                   0x0801
#define ARABIC_EGYPT                  0x0c01
#define ARABIC_LIBYA                  0x1001
#define ARABIC_ALGERIA                0x1401
#define ARABIC_MOROCCO                0x1801
#define ARABIC_TUNISIA                0x1c01
#define ARABIC_OMAN                   0x2001
#define ARABIC_YEMEN                  0x2401
#define ARABIC_SYRIA                  0x2801
#define ARABIC_JORDAN                 0x2c01
#define ARABIC_LEBANON                0x3001
#define ARABIC_KUWAIT                 0x3401
#define ARABIC_UAE                    0x3801
#define ARABIC_BAHRAIN                0x3c01
#define ARABIC_QATAR                  0x4001
#define ARMENIAN                      0x042b
#define ASSAMESE                      0x044d
#define AZERI_LATIN                   0x042c
#define AZERI_CYRILLIC                0x082c
#define BASQUE                        0x042d
#define BELARUSSIAN                   0x0423
#define BENGALI                       0x0445
#define BULGARIAN                     0x0402
#define BURMESE                       0x0455
#define CATALAN                       0x0403
#define CHINESE_TAIWAN                0x0404
#define CHINESE_PRC                   0x0804
#define CHINESE_HONG_KONG_SAR_PR C    0x0c04
#define CHINESE_SINGAPORE             0x1004
#define CHINESE_MACAU_SAR             0x1404
#define CROATIAN                      0x041a
#define CZECH                         0x0405
#define DANISH                        0x0406
#define DUTCH_NETHERLANDS             0x0413
#define DUTCH_BELGIUM                 0x0813
#define ENGLISH_UNITED_STATES         0x0409
#define ENGLISH_UNITED_KINGDOM        0x0809
#define ENGLISH_AUSTRALIAN            0x0c09
#define ENGLISH_CANADIAN              0x1009
#define ENGLISH_NEW_ZEALAND           0x1409
#define ENGLISH_IRELAND               0x1809
#define ENGLISH_SOUTH_AFRICA          0x1c09
#define ENGLISH_JAMAICA               0x2009
#define ENGLISH_CARIBBEAN             0x2409
#define ENGLISH_BELIZE                0x2809
#define ENGLISH_TRINIDAD              0x2c09
#define ENGLISH_ZIMBABWE              0x3009
#define ENGLISH_PHILIPPINES           0x3409
#define ESTONIAN                      0x0425
#define FAEROESE                      0x0438
#define FARSI                         0x0429
#define FINNISH                       0x040b
#define FRENCH_STANDARD               0x040c
#define FRENCH_BELGIAN                0x080c
#define FRENCH_CANADIAN               0x0c0c
#define FRENCH_SWITZERLAND            0x100c
#define FRENCH_LUXEMBOURG             0x140c
#define FRENCH_MONACO                 0x180c
#define GEORGIAN                      0x0437
#define GERMAN_STANDARD               0x0407
#define GERMAN_SWITZERLAND            0x0807
#define GERMAN_AUSTRIA                0x0c07
#define GERMAN_LUXEMBOURG             0x1007
#define GERMAN_LIECHTENSTEIN          0x1407
#define GREEK                         0x0408
#define GUJARATI                      0x0447
#define HEBREW                        0x040d
#define HINDI                         0x0439
#define HUNGARIAN                     0x040e
#define ICELANDIC                     0x040f
#define INDONESIAN                    0x0421
#define ITALIAN_STANDARD              0x0410
#define ITALIAN_SWITZERLAND           0x0810
#define JAPANESE                      0x0411
#define KANNADA                       0x044b
#define KASHMIRI_INDIA                0x0860
#define KAZAKH                        0x043f
#define KONKANI                       0x0457
#define KOREAN                        0x0412
#define KOREAN_JOHAB                  0x0812
#define LATVIAN                       0x0426
#define LITHUANIAN                    0x0427
#define LITHUANIAN_CLASSIC            0x0827
#define MACEDONIAN                    0x042f
#define MALAY_MALAYSIAN               0x043e
#define MALAY_BRUNEI_DARUSSALAM       0x083e
#define MALAYALAM                     0x044c
#define MANIPURI                      0x0458
#define MARATHI                       0x044e
#define NEPALI_INDIA                  0x0861
#define NORWEGIAN_BOKMAL              0x0414
#define NORWEGIAN_NYNORSK             0x0814
#define ORIYA                         0x0448
#define POLISH                        0x0415
#define PORTUGUESE_BRAZIL             0x0416
#define PORTUGUESE_STANDARD           0x0816
#define PUNJABI                       0x0446
#define ROMANIAN                      0x0418
#define RUSSIAN                       0x0419
#define SANSKRIT                      0x044f
#define SERBIAN_CYRILLIC              0x0c1a
#define SERBIAN_LATIN                 0x081a
#define SINDHI                        0x0459
#define SLOVAK                        0x041b
#define SLOVENIAN                     0x0424
#define SPANISH_TRADITIONAL_SORT      0x040a
#define SPANISH_MEXICAN               0x080a
#define SPANISH_MODERN SORT           0x0c0a
#define SPANISH_GUATEMALA             0x100a
#define SPANISH_COSTA RICA            0x140a
#define SPANISH_PANAMA                0x180a
#define SPANISH_DOMINICAN_REPUBLIC    0x1c0a
#define SPANISH_VENEZUELA             0x200a
#define SPANISH_COLOMBIA              0x240a
#define SPANISH_PERU                  0x280a
#define SPANISH_ARGENTINA             0x2c0a
#define SPANISH_ECUADOR               0x300a
#define SPANISH_CHILE                 0x340a
#define SPANISH_URUGUAY               0x380a
#define SPANISH_PARAGUAY              0x3c0a
#define SPANISH_BOLIVIA               0x400a
#define SPANISH_EL_SALVADOR           0x440a
#define SPANISH_HONDURAS              0x480a
#define SPANISH_NICARAGUA             0x4c0a
#define SPANISH_PUERTO_RICO           0x500a
#define SUTU                          0x0430
#define SWAHILI_KENYA                 0x0441
#define SWEDISH                       0x041d
#define SWEDISH_FINLAND               0x081d
#define TAMIL                         0x0449
#define TATAR_TATARSTAN               0x0444
#define TELUGU                        0x044a
#define THAI                          0x041e
#define TURKISH                       0x041f
#define UKRAINIAN                     0x0422
#define URDU_PAKISTAN                 0x0420
#define URDU_INDIA                    0x0820
#define UZBEK_LATIN                   0x0443
#define UZBEK_CYRILLIC                0x0843
#define VIETNAMESE                    0x042a
#define HID_USAGE_DATA_DESCRIPTOR     0x04ff
#define HID_VENDOR_DEFINED_1          0xf0ff
#define HID_VENDOR_DEFINED_2          0xf4ff
#define HID_VENDOR_DEFINED_3          0xf8ff
#define HID_VENDOR_DEFINED_4          0xfcff

#endif /* defined(UPGRADE_USB) */
/* END OF FILE */

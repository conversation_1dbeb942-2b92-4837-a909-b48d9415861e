/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/api/inc/uf8chain.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/27 15:10:43 $
 **************************************************************************
 * File Description:
 *
 *      Header file for UCipher.c
 *      Contains function call declarations, constants and types for use
 *      by other 3GPP f8 and f9 algorithms.
 **************************************************************************/

#if !defined (UF8CHAIN_H)
#define       UF8CHAIN_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <system.h>
#include <ucipher.h>

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/

/***************************************************************************
*   Macro Functions
***************************************************************************/

/***************************************************************************
 * Types
 ***************************************************************************/

/***************************************************************************
 * Typed Constants
 ***************************************************************************/

typedef struct UCipherInfoTag
{
     Int16                  bearer;
     Int16                  bitLength;
     Int8                 * key_p;         /* PNULL when not ciphered */
     Int32                  countC;        /* HFN/SN  or HFN/CFN */
     Int16                  srcBitOffset;  /* May be 0..65535, i.e. not 0..7 */
     Int16                  dstBitOffset;  /* May be 0..65535, i.e. not 0..7 */
     Int8                 * srcData_p;
     Int8                 * dstData_p;     /* May be PNULL when RLC rejects */
     struct UCipherInfoTag * next_p;
}
UCipherInfo;

/***************************************************************************
 * Function Prototypes
 ***************************************************************************/

extern void              f8chain
    (UDirection             dir,
     Int16                  numberOfBlocks,
     UCipherInfo            cipherInfo
    );

#if !defined(DATA_IN_SIGNAL)
UCipherInfo *f8chainAlloc(Int16 numberOfBlocks);
void f8chainFree(UCipherInfo **cipherInfo_pp);
#endif /* !DATA_IN_SIGNAL */

#endif /* UF8CHAIN */
/* End of Uf8chain.h */

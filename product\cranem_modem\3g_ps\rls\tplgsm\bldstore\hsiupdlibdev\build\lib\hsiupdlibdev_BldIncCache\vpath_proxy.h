/*
* Copyright (C) 2023 ASR Microelectronic Ltd.
*
* Author: <PERSON><PERSON> <<EMAIL>>
*
* This file contains proprietary information.
* No dissemination allowed without prior written permission from
* ASR Microelectronic Ltd.
*
* File Description:
*
* This file implements proxy for audio platform driver
*/

#include "audio_def.h"
#include "gbl_types.h"
#include "IPCComm.h"
#include "AudioHAL.h"
typedef AUDIOHAL_DEVICE_PORT_T DevicePort;

typedef struct {
    void(*open)(DevicePort port);
    void(*hw_params)(DevicePort port, UINT32 format);
    void(*close)(DevicePort port);
    void(*config)(DevicePort port, UINT32 rate, UINT32 channel);
} DeviceOps;

int vpathHook(DeviceOps* ops);
int vpathTick(void);
int vpathTickTx(UINT16* buffer, UINT32 rate, UINT32 channel);
int vpathTickRx(UINT16* buffer, UINT32 rate, UINT32 channel);
int vpathRegister(UINT16* tx_buffer, UINT16* rx_buffer, UINT32 rate, UINT32 channel);
int vpathRegisterTx(UINT16* buffer, UINT32 rate, UINT32 channel);
int vpathRegisterRx(UINT16* buffer, UINT32 rate, UINT32 channel);
int vpathConfig(UINT32 rate, UINT32 channel);
int vpathSetMode(UINT32 mode);//0 for pwm, 1 for external codec

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urlftf.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/27 15:10:43 $
 ***************************************************************************
 * File Description: Implements the Flexible Trace Framework for URLC
 **************************************************************************/

#if !defined (URL_FTF_H)
#define       URL_FTF_H

/* Define URLC sub-process types for the FTF. These needs to be defined before
 * including the ftf header files*/
#define UT_FTF_URLC_TM_UL_SUB_PROCESS 1
#define UT_FTF_URLC_TM_DL_SUB_PROCESS 2
#define UT_FTF_URLC_UM_UL_SUB_PROCESS 3
#define UT_FTF_URLC_UM_DL_SUB_PROCESS 4
#define UT_FTF_URLC_AM_UL_SUB_PROCESS 5
#define UT_FTF_URLC_AM_DL_SUB_PROCESS 6
#define UT_FTF_URLC_MAIN_SUB_PROCESS  7
#define UT_FTF_URLC_UTIL_SUB_PROCESS  8

/**************************************************************************
 * Nested Include Files
 **************************************************************************/
#include <utftf.h>
#include <utftf_sig.h>

#if defined (ENABLE_FLEXIBLE_TRACE_FRAMEWORK)
/**************************************************************************
 * Manifest Constants
 **************************************************************************/

/**************************************************************************
 * Type Definitions
 **************************************************************************/
/* Define the FTF data structure for URLC */
UT_FTF_DEFINE_TRACE_DATA (URLC)

/**************************************************************************
 * Macros
 **************************************************************************/

/**************************************************************************
 * Function Prototypes
 **************************************************************************/

#endif /* ENABLE_FLEXIBLE_TRACE_FRAMEWORK */

#endif

/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/


/************************************************************************/
/*                                                                      */
/* Title: IPC Comm. pacakge header file                                 */
/*                                                                      */
/* Filename:  WS_IPCTest.h                                                 */
/*                                                                      */
/* Authors: <AUTHORS>
/*                                                                      */
/* Target, subsystem: Common Platform, HAL                              */
/************************************************************************/
#ifndef _WS_IPCTest_H_

  #define _WS_IPCTest_H_

/*----------------------------------------------------------------------*/

void AAAPMsgReceivedNotification(UINT16 msgOpCode, UINT16 msgLength, UINT8 *msgData);

void AAAPDataReceivedNotification(IPC_DataStructReceived *dataParams, IPC_CmdMsgParams *msgAttachedToData);

void AAAPDataBufferFreeNotification(UINT32 *data, IPC_DataChannelNumber chanID);

void AAAPDataChannelFreeNotification(IPC_DataChannelNumber chanID);

IPC_ReturnCode AAAPGetDataPointer(UINT32 **dataLocationPtr, UINT16 dataSize, IPC_DataChannelNumber chanID, IPC_CmdMsgParams* msg);




#endif  /* _IPCCommTest_H_ */




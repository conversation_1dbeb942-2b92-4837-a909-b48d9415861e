/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/*******************************************************************************
 *
 *         TTPCom USB Protocol Stack
 *
 *         Copyright (c) 2002 TTPCom Limited
 *
 *******************************************************************************
 *
 *   $Id: //central/main/wsd/platforms/hermon/devices/transports/usb.mod/pub/src/usbhermonep.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2007/01/12 13:10:41 $
 *
 *******************************************************************************
 *
 * Endpoint, Interface and Configuration constants.
 *
 ******************************************************************************/
#if defined(UPGRADE_USB)
/* USB product */
#if defined(UPGRADE_HERMON_PLATFORM)
/* on Hermon platform */

/***************************************************************************
 * This file contains the EP constants for the Hermon silicon, where
 * the endpoint types, directions and numbers are hardwired
 ***************************************************************************/

#ifndef USB_HERMON_EP_H
#define USB_HERMON_EP_H

/***************************************************************************
 * On the Hermon USB silicon there are 24 endpoints
 *
 * EP0 - control - bidirectional fifo 16 bytes
 * EPB-EPX configurable end points
 ***************************************************************************/
#define USB_EP_ZERO         0
#define USB_NUM_ENDPOINTS_ON_DEVICE 24

#if !defined(USB_DYNAMIC_CONFIGURATION)
/* 
** Assign Functions to Physical endpoints 
*/
typedef enum UsbLogicalEndpointTag
{
  USB_MAST_IN_PHYSICAL_ENDPOINT = 20,
  USB_MAST_OUT_PHYSICAL_ENDPOINT,
  USB_EMMI_IN_PHYSICAL_ENDPOINT,
  USB_EMMI_OUT_PHYSICAL_ENDPOINT,
  USB_COMM_CTRL_IN_PHYSICAL_ENDPOINT,
  USB_COMM_DATA_IN_PHYSICAL_ENDPOINT,
  USB_COMM_DATA_OUT_PHYSICAL_ENDPOINT
} UsbLogicalEndpoint;
#endif
/* Assign Functions to Physical endpoints where there's a composite
 * function:
 */

#define USB_PID                        USB_TTPCOM_PID_COMPOSITE
#define USB_DEV_CLASS                  USB_DEVICE_CLASS_NONE

/* Configurations.
 *
 * (Not explicitly 'endpoint' related but required by the same code
 * and controlled by the same build switches.)
 *
 * Currently only one configuration option is required, so number it '1'
 *
 * "True" USB configurations are not used in practice.  None of the host
 * driver for the applications make use of it, and no real application has
 * needed to use this facility.
 */
#define USB_NUM_CONFIGURATIONS           1
#define USB_CONFIGURATION_VALUE          1

#define USB_EMMI_CONFIGURATION_VALUE     USB_CONFIGURATION_VALUE
#define USB_MAST_CONFIGURATION_VALUE     USB_CONFIGURATION_VALUE
#define USB_COMM_CONFIGURATION_VALUE     USB_CONFIGURATION_VALUE
#define USB_DIAG_CONFIGURATION_VALUE     USB_CONFIGURATION_VALUE


/*
 * Comms Class parameter that may be dependent on the physical max
 * packet size of an endpoint, especially where silicon forces use of
 * a particular endpoint.
 *
 * Hermon: There is only one Int endpoint, #5, which has an 8-byte FIFO.
 */
#define USB_COMM_MAX_INTERRUPT_PACKET_SIZE 8


/*
 * Max packet size of Endpoint 0.  Typically 8 but can be device dependent.
 */
#define USB_MAX_PACKET_SIZE_EP0 16

   
/* USB_HERMON_EP_H ends */
#endif
/* UPGRADE_HERMON_PLATFORM ends */
#endif
/* UPGRADE_USB ends */
#endif
/* END OF FILE */

/**************************************************************************
 * Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/umaehs.h
 *   $Revision: #1 $
 *   $DateTime: 2006/10/27 15:10:43 $
 **************************************************************************
 * File Description:
 *
 *    Functional interfaces to the upper MAC-hs module (lower MAC-hs in L1).
 **************************************************************************/

#if !defined (UMAEHS_H)
#define       UMAEHS_H

#if defined(UPGRADE_3G_HSDPA)
#include <pl_w_globs.h>
#include <umaxhsphy.h>
#include <umautl.h>

/***************************************************************************
* Nested Include Files
***************************************************************************/

//Global definitions
void UmaEhsHandleRecoveryInd(SignalBuffer *sigbuf_p);


size_t  UmaEhsInit(UmacEntity *parent);
void UmaEhsHandleCmacEhsQueueConfigReq(SignalBuffer *sigbuf_p);
void UmaEhsHandleCmacEhsQueueReleaseReq(SignalBuffer *sigbuf_p);
#if defined (PS_L2_R8_API)
void UmaEhsHandlePhyHsDataInd(PhyHsDataInd *phyHsDataInd_p);
#else
void UmaEhsHandlePhyHsDataInd(PhyHsDataIndHeader *tbInfo_p);
#endif
void UmaEhsHandleUmacEhsDataRsp(SignalBuffer *sigbuf_p);
void UmaEhsMapLogChToQueueId (Int8 logChId, Int8 queueId);
/*CQ51477 add by taoye for Samsung issue(496786.1) begin 20131227*/
void UmaEhsMapLogChToNull (Int8 logChId);
/*CQ51477 add by taoye for Samsung issue(496786.1) end 20131227*/
Boolean UmaEhsCheckT1Timers(void);
void UmaEhsStopResetTimers(void);
void UmaEhsHandleResetTimerExpiry(SignalBuffer *rxSignal_p);
void UmaEhsReleaseDatabase(void);
void UmaEhsToIdle(void);
void UmaEhsDemultiplex(void);
void UmaEhsDeliverPendingMacEhsPdus(void);
Boolean UmaEhsDidDlMacEhsPduReceived(void);

#if !defined (DTC_SUPPORT_RETAIN_CIPHER_STATE)
dtcF8DescriptorInfo *UmaEhsAllocateDtcDescriptor(void);
#endif /* DTC_SUPPORT_RETAIN_CIPHER_STATE */

#if defined (ENABLE_UMAC_UNIT_TEST)
void HandleUmaEhsUnitTestDataInd(SignalBuffer * rxSignal_p);
void UmaEhsAssignUnitTestDataPtr(Int8 *umacEhsData_p);

#endif
/* Interface to umamain */
/*
void UmaHsDebugMacHs (Boolean debugMacHs);
void UmaHsHandlePhyHsDataInd(SignalBuffer *sigbuf_p);
void UmaHsHandlePhyHsUnassignPointerInd(SignalBuffer *sigbuf_p);
void UmaHsHandleUmacHsDataRsp(SignalBuffer *sigbuf_p);
void UmaHsHandleCmacHsQueueConfigReq(SignalBuffer *sigbuf_p);
void UmaHsHandleCmacHsQueueReleaseReq(SignalBuffer *sigbuf_p);
void UmaHsCheckT1Timers(void);
size_t UmaHsInit (UmacEntity *umac_p);
void UmaHsToIdle (void);

//moved from umadl.c
void UmaHsFillPduListInfoInd_create( void );
void UmaHsFillPduListInfoInd(Int8 *data_p, UCipherInfo **hsCipherInfo_pp,
                            Int32 dataBitOffset, Int8 currentPdu,
                            Int8 numberPdus, Int16 pduSizeInBits,
                            Int8 macDflowId);
void UmaHsSendPduListInfoInd(Int8 tsn, Int8 numberMacDpdus);
void UmaHsRequestStatusInd(void);
void UmaHsHandleUmacHsPduListInfoRsp(SignalBuffer *sigbuf_p);
*/

#endif /* UPGRADE_3G_HSDPA */
#endif
/* END OF FILE */



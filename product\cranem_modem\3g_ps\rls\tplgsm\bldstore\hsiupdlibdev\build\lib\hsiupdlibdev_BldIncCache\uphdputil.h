/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/phy/3g.mod/api/inc/uphdputil.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/11/09 16:04:03 $
 **************************************************************************
 * File Description: Utility functions for the datapath actions
 **************************************************************************/
 /** \file
  * \ingroup PrdCBEcoreWCDMAUphDp
  * \brief Header file for UphDpUtil.
  */

#ifndef UPHDPUTIL_H
#define UPHDPUTIL_H

/** \addtogroup PrdCBEcoreWCDMAUphDp
  * @{ */

/*******************************************************************************
** Includes
*******************************************************************************/

#include <system.h>

#include <uas_asn.h>

/*******************************************************************************
** Constants
*******************************************************************************/

/*******************************************************************************
** Macros
*******************************************************************************/

/*******************************************************************************
** Typedefs
*******************************************************************************/

/*******************************************************************************
** Global Data
*******************************************************************************/

/*******************************************************************************
** Global Function Prototypes
*******************************************************************************/

void uphGetTtiInfo (
    const   UTransmissionTimeInterval,
            Int16*,
            Int16*);



     
void uphGetCrcInfo (
      
    const   UCRC_Size,
            Int16*);



     
void uphChannelCodingSize (
      
    const   T_UChannelCodingType,
    const   UCodingRate,
    const   Int16,
            Int16*,
            Int16*,
            Int16*);

Int16 uphCalcNumCodeBlocks (
    const   Int16,
    const   Int16,
    const   Int16,
            Int16*,
            Int16*);
#endif

/** @} */ /* End of addtogroup PrdCBEcoreWCDMAUphDp */

/* END OF FILE */

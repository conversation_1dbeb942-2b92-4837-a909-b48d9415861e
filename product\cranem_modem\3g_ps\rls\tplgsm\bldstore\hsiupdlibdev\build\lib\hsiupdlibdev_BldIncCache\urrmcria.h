/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urrmcria.h#4 $
 *   $Revision: #4 $
 *   $DateTime: 2007/03/19 13:59:30 $
 **************************************************************************
 * File Description:
 *
 ***************************************************************************
 *
 * Revision Details
 **************************************************************************/

#if !defined (URRMCRIA_H)
#define       URRMCRIA_H

/***************************************************************************
* Nested Include Files
***************************************************************************/
#include <urrmcrty.h>

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/
//meaning of this one is that RRC should consider sending RSSI_IND to AB every MCR_MAX_SERVING_CELL_MEAS_COUNT on DCH.
#define MCR_MAX_SERVING_CELL_MEAS_COUNT 20

/***************************************************************************
*   Macro Functions
***************************************************************************/

/***************************************************************************
 * Types
 ***************************************************************************/
typedef struct IaCellIdsListDataTag
{
    Int8                        id;
    Boolean                     isDetectedCell;
}
IaCellIdsListData;

typedef struct IaCellIdsListTag
{
    Int8                        n;
    IaCellIdsListData           data [UPS_MAX_MEASURED_INTRA_FREQ_CELLS];
}
IaCellIdsList;

typedef struct IaMeasResultsTag
{
    McrMeasResults              results;
}
IaMeasResults;

//ICAT EXPORTED STRUCT
typedef struct IaCellInfoTag
{
    McrCellData                 cellData;
    Boolean                     isActiveCell;
}
IaCellInfo;

typedef struct IaDetectedCellInfoTag
{
    UPrimaryScramblingCode      primaryScramblingCode;
    Int8                        measResIndex;
}
IaDetectedCellInfo;

typedef struct IaDetectedCellsTag
{
    Int8                        noOfCells;
    IaDetectedCellInfo          detectedCellInfo [UPS_MAX_MEASURED_INTRA_FREQ_CELLS];
    McrMeasCounts               measCounts;
    Int8                        measStatus;
}
IaDetectedCells;

typedef struct IaMeasInfoTag
{
    /* Bit mask indicating which locations in measResults are occupied. */
    Int32                       measResultsMask;

    /* Measurement results. */
    IaMeasResults               measResults [UPS_MAX_MEASURED_INTRA_FREQ_CELLS];

    /* Ongoing measurements status. */
    McrMeasStatus               measStatus;

    /* Detected Cells list. */
    IaDetectedCells             *detectedCells;

    McrMeasCounts               activeSetMeasCounts;

    Int8                        activeSetRssiCount;

    SignedInt16                        rxLev;

    SignedInt16                 rssi_13b3;
}
IaMeasInfo;

typedef struct IaMeasConfigTag
{
    CellIdMask   occupied;
    IaCellInfo   cellInfo [maxCellMeas];
    IaMeasInfo   *measInfo;
    McrFilters   filters;
    UUARFCN      newFreq;
}
IaMeasConfig;

typedef struct IaMeasDataTag
{
    /* Indicates which positions are occupied in CELL_INFO_LIST. */
    CellIdMask                  occupied;

    /* Intra-frequency CELL_INFO_LIST. */
    IaCellInfo                  cellInfo [maxCellMeas];

    /* On-going intra-frequency measurements data. */
    IaMeasInfo                  *measInfo;

    /* List of filters in use. */
    McrFilters                  filters;

    /* Saved configuration data. */
    IaMeasConfig                *savedConfig;

    /* indicates if measurement of active set cells is needed, but the NW hasn't configure yet a new CELL_INFO_LIST after HHO */
    Boolean                     activeSetCellsMeasIsNeeded;
}
IaMeasData;

/***************************************************************************
 * Typed Constants
 ***************************************************************************/

/***************************************************************************
 * Function Prototypes
 ***************************************************************************/
void    McriaInitMeasResults        (McrMeasResults  *measRes_p);

McrReportingQuantitiesAvail
        McriaAllCellRepQtyPresent (McrMeasQuantity          *measQty_p,
                                   UCellReportingQuantities *repQty_p,
                                   McrMeasResults           *measRes_p,
                                   Boolean                  sameFreq,
                                   Boolean                  readSFNindicator);

void    McriaIaUpdateFilterData      (Int8 filterIndex);

IaMeasData *UrrMcrGetIaMeasDataPtr (void);

void    McriaExtractForbiddenCellList (UrrSirAllocatedMemList      *memList_p,
                                       UForbiddenAffectCellList_r4 *srcList_p,
                                       UForbiddenAffectCellList_r4 **destList_p);

void    McriaProcessRssiMeas (void);

void    McriaStoreRawValues (Boolean                txPowerPresent,
                             UPrimaryCPICH_TX_Power txPower,
                             McrCfnSfnData          *cfnSfnData_p,
                             UCellMeasurementReport *rep_p,
                             McrRawValues           *rawVal_p);

void    McriaStoreFilterValues (Boolean                        txPowerPresent,
                                UPrimaryCPICH_TX_Power         txPower,
                                UCellMeasurementReport         *rep_p,
                                McrFilterData                  *filterData_p,
                                McrFilterValues                *filterVal_p,
                                McrFilterRequestedType          requestedFilterType);

void    McriaGetFirstVacantPos (Int32 mask,
                                Int8  maxVal,
                                Int8  *index_p);

SignedInt16 McriaGetMeasValue (McrMeasQuantity       *measQty_p,
                               McrMeasResults        *measRes_p,
                               Boolean               useCIO,
                               UCellIndividualOffset cellOffset);

Boolean McriaIsMeasValueDefined (McrMeasQuantity *measQty_p,
                                 McrMeasResults  *measRes_p);

void    McriaIncActiveSetMeasCounts (UIntraFreqMeasQuantity_FDD measQty,
                                     UCellReportingQuantities   *repQty_p);

void    McriaDecActiveSetMeasCounts (UIntraFreqMeasQuantity_FDD measQty,
                                     UCellReportingQuantities   *repQty_p);

void    McriaDecActiveSetMeasCountsForCell (
                                UIntraFreqMeasQuantity_FDD measQty,
                                UCellReportingQuantities   *activeRepQty_p,
                                UCellReportingQuantities   *monitorRepQty_p,
                                UPrimaryScramblingCode     psc);

void    McriaIaCheckMeasStatus (CellIdMask rscp,
                                CellIdMask ecn0,
                                CellIdMask sfnSfn,
                                CellIdMask cfnSfn,
                                Boolean    cellListChanged);

void    McriaGetBestActiveCell (McrMeasQuantity                 *measQty_p,
                                Boolean                         listPresent,
                                UForbiddenAffectCellList_latest *list_p,
                                UPrimaryScramblingCode          *psc,
                                SignedInt16                     *measVal,
                                Boolean                         useCIO);

void    McriaCalActiveSetLogSum (McrMeasQuantity               *measQty_p,
                                 UForbiddenAffectCellList_latest *list_p,
                                 SignedInt16                     *logSum_p);

void    McriaSetStartCFNgtrActTime (McrCfnSfnData *cfnSfnData_p,
                                    Int8          cfnActivation);

void    McriaSortMeasValList   (UIntraFreqMeasQuantity_FDD  measQty,
                                McrMeasValList             *list_p);

Boolean McriaEvent1a1eOccurred (UIntraFreqMeasQuantity_FDD measQty,
                                SignedInt16                leftVal,
                                SignedInt16                rightVal);

Boolean McriaIsForbiddenCell   (UForbiddenAffectCellList_latest  *list_p,
                                UPrimaryScramblingCode          psc);

Boolean McriaEvent1b1fOccurred (UIntraFreqMeasQuantity_FDD measQty,
                                SignedInt16                leftVal,
                                SignedInt16                rightVal);

Boolean McriaEvent1cOccurred   (UIntraFreqMeasQuantity_FDD measQty,
                                SignedInt16                leftVal,
                                SignedInt16                rightVal);

void    McriaBuildMeasValList   (Boolean                  activeSet,
                                 Boolean                  monitoredSet,
                                 Boolean                  detectedSet,
                                 McrMeasQuantity          *measQty_p,
                                 UCellReportingQuantities *actSetRepQty_p,
                                 McrIntraFreqMeas         *intraFM_p,
                                 McrMeasValList           *list_p);

void    McriaSetCellMeasuredResults (UPrimaryScramblingCode   psc,
                                     McrMeasQuantity          *measQty_p,
                                     UCellReportingQuantities *repQty_p,
                                     McrMeasResults           *measRes_p,
                                     UrrSirAllocatedMemList   *memList_p,
                                     UCellMeasuredResults     *cellMeasRes_p,
                                     Boolean                  sameFreq,
                                     Boolean                  readSFNindicator,
                                     UDeltaRSCPPerCell   *deltaRscp);

void McriaCreateMeasReportNCExtParameters (UrrSirAllocatedMemList *memList_p,
                                           UMeasurementReport_v390NonCriticalExt  **v390NCExt_p);

void    McriaBuildIntraEventResults (T_UIntraFreqEvent_latest     eventType,
                                     McrPscList               *pscList_p,
                                     UrrSirAllocatedMemList   *memList_p,
                                     UMeasurementReport       *measRep_p);

void    McriaDeleteForbiddenCellList (UForbiddenAffectCellList_latest *list_p);

McrMeasQuantity *McriaGetMeasQuantityPtr (UIntraFreqMeasQuantity_FDD measQtyType);
/* added for CQ00003139 begin */
Boolean McriaAllActiveCellsMeasPresent (
                                 UIntraFreqMeasQuantity_FDD measQty);
/* added for CQ00003139 end */

/*add for CQ00009291 start*/
Boolean McriaGetIntraFreqMeasFilter (McrMeasQuantity *measQuantity_p);
/*add for CQ00009291 end*/

McrFilters *UrrMcrGetIaMeasDataFilter(void);
SignedInt16 UrrMcrServingCellRscp(void);

void McriaInitCellInfoList (Boolean freqChanged, UUARFCN freq);/*added for DMCR DEVELOPMENT*/

#endif




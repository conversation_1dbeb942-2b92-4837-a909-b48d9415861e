/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/api/inc/urlc_dbg_sig.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2007/03/19 13:59:30 $
 **************************************************************************
 *  File Description :
 *
 * Debug signals used in URLC for outputting the bearer entity data
 *
 **************************************************************************/

#if !defined (URLC_DBG_SIG_H)
#define       URLC_DBG_SIG_H

/***************************************************************************
 * Nested Include Files
 **************************************************************************/
#include <urlamtyp.h>
#include <urlumtyp.h>
#include <urltmtyp.h>

/***************************************************************************
 * Manifest Constants
 **************************************************************************/


/***************************************************************************
 * Type Definitions
 **************************************************************************/

/* AM bearer entity debug signal */
typedef UrlAmTxRxEntity UrlcDebugAmTxRxEntityInd;

/* UM bearer entity debug signal */
typedef UrlUmTxEntity UrlcDebugUmTxEntityInd;
typedef UrlUmRxEntity UrlcDebugUmRxEntityInd;

/* TM bearer entity debuyg signal */
typedef UrlTmTxEntity UrlcDebugTmTxEntityInd;
typedef UrlTmRxEntity UrlcDebugTmRxEntityInd;



#endif

/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/3g.mod/lib/src/updcp.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/26 20:17:00 $
 **************************************************************************
 * File Description:
 *
 *      The function interface between the task and UPDCP implementation.
 **************************************************************************/

#if !defined (UPDCP_H)
#define UPDCP_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <gpcntr.h>

/***************************************************************************
* Manifest Constants
***************************************************************************/

/***************************************************************************
* Macro Functions
***************************************************************************/

/***************************************************************************
* Types
***************************************************************************/

/***************************************************************************
* Typed Constants
***************************************************************************/

/***************************************************************************
* Function Prototypes
***************************************************************************/

void    UpdcpInit (void);
Boolean UpdcpProcessSignal (SignalBuffer *sigBuf);

#endif

/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urrcsr.h#7 $
 *   $Revision: #7 $
 *   $DateTime: 2007/03/30 13:53:25 $
 **************************************************************************
 * File Description:
 *
 *    Header file for URRCSR.C. Contains function call declarations
 *    for use by other URRC modules.
 **************************************************************************/

#if !defined (URRCSR_H)
#define       URRCSR_H

/***************************************************************************
* Nested Include Files
***************************************************************************/
#include <cphy_sig.h>
#include <ups_typ.h>
#include <urrtypes.h>
#include <urrintsig.h>
#include <urr_sig.h>
#include <urrmtc.h>
#include <urrsirty.h>
#include <urralloc.h>
#include <uas_asn.h>
#include <urrcsrty.h>
#include <rrutility.h> //********** add
/*CQ00070208 add begin*/
#if defined UPGRADE_PLMS
#include <plms_sig.h>
#endif/*UPGRADE_PLMS*/

#if defined UPGRADE_PLMS
#include <urrl1asig.h>
#endif/*UPGRADE_PLMS*/
/*CQ00070208 add end*/
/* ********** add begin */
#if defined UPGRADE_CSG1
#include <lterrcbase.h>
#endif //UPGRADE_CSG1
/* ********** add end */

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/
#define CSRR_MAX_RSSI                                 -25
#define CSRR_MIN_RSSI                                -115
#define CSRR_MIM_RSCP                                -120
#define CSRR_RSSI_RANGE   (CSRR_MAX_RSSI - CSRR_MIN_RSSI)
#define CSRR_MAX_RXLEV                                 63
#define CSRR_MIN_RXLEV                                  0

#define CSRR_RXLEV_RSSI_MAPPING  ((SignedInt16) \
                                  (CSRR_MAX_RXLEV * 10 / CSRR_RSSI_RANGE))

#define CSRP_STOP_CELL_DETECTION 0xFFFA


/***************************************************************************
 *  Macro Functions
 ***************************************************************************/
/* for CQ00020620 START  PTK_CQ00236875 */
/******************************************************************************
 *
 * Function     : SET_RECOVERY_FROM_DCH_FAILURE
 *
 * Scope        : LOCAL
 *
 * Parameters   : vALUE - recovery from DCH failure value
 *                iNDEX - Indicates the place this MACRO was called from
 *
 * Returns      : None
 *
 * Description  : Sets the recovery from DCH failure variable
 *
 *****************************************************************************/
#define SET_RECOVERY_FROM_DCH_FAILURE(vALUE, iNDEX) \
        if (csrData.recoveryFromDchFailure != vALUE)                                       \
        {                                                                                  \
            RRC_TRACE_2(URRC_CSR, SET_RECOVERY_FROM_DCH_FAILURE_##iNDEX, URRC_CSR_DEFAULT, \
                 "Set recoveryFromDchFailure %d -> %d", csrData.recoveryFromDchFailure, vALUE); \
        }                                                                                  \
        csrData.recoveryFromDchFailure = vALUE;
/* for CQ00020620 END  PTK_CQ00236875 */

#if defined (UPGRADE_PLMS) //CQ00079847 begin
/******************************************************************************
 *
 * Function     : SET_FG_PLMS_IN_PROGRESS
 *
 * Scope        : LOCAL
 *
 * Parameters   : value to set
 *
 * Returns      : None
 *
 * Description  : sets the current value of the variable fgPlmsInProgress and
 *                produces debug printout.
 *
 *****************************************************************************/
#define SET_FG_PLMS_IN_PROGRESS(vAL,iNDEX)                             \
      RRC_TRACE_2 (URRC_CSR, SET_FG_PLMS_IN_PROGRESS##iNDEX, URRC_CSR_DEFAULT,       \
                "fgPlmsInProgress changed. Old value = %d, new value = %d",  \
                csrpData.fgPlmsInProgress,       \
                vAL);                         \
      csrpData.fgPlmsInProgress = vAL;                                   

//CQ00079847 end

#define SET_PLMN_LAST_RATS_TO_SEARCH(vAL,iNDEX)                             \
      RRC_TRACE_2 (URRC_CSR, SET_FG_PLMN_LAST_RATS_TO_SEARCH##iNDEX, URRC_CSR_DEFAULT,       \
                "lastRatsToSearch. Old value = %d, new value = %d",  \
                csrpData.lastRatsToSearch,       \
                vAL);                         \
      csrpData.lastRatsToSearch = vAL;                                   
#endif

/***************************************************************************
 * Types
 ***************************************************************************/

      /************** CSR - MCR interface types (Start) ***************/

typedef struct CsrMcrIntraFreqNCellInfoTag
{
    Boolean                 servingCell;
    UIntraFreqCellID        intraFreqCellId;
    UmtsCell                cellInfo;
    UCellIndividualOffset   cellIndividualOffset;
    Boolean                 primaryCPICH_TX_PowerPresent;
    UPrimaryCPICH_TX_Power  primaryCPICH_TX_Power;
}
CsrMcrIntraFreqNCellInfo;

typedef struct CsrMcrInterFreqNCellInfoTag
{
    UInterFreqCellID        interFreqCellId;
    UInterFrequencyCell     cellInfo;
    UCellIndividualOffset   cellIndividualOffset;
    Boolean                 primaryCPICH_TX_PowerPresent;
    UPrimaryCPICH_TX_Power  primaryCPICH_TX_Power;
}
CsrMcrInterFreqNCellInfo;

typedef struct CsrMcrInterFreqNCellIdTag
{
    UUARFCN                 uarfcn_dl;
    UPrimaryScramblingCode  primaryScramblingCode;
}
CsrMcrInterFreqNCellId;


#if ! defined (UPGRADE_EXCLUDE_2G)
typedef struct CsrMcrGsmNCellInfoTag
{
    UInterRATCellID         gsmCellID;
    UGsmCell                cellInfo;
}
CsrMcrGsmNCellInfo;
#endif

  typedef struct CsrMcrLteFrequencyInfoTag
  {
      Earfcn                          earfcn;//********** change  UEARFCN to Earfcn
      UEUTRA_MeasurementBandwidth     measurementBandwidth;
      Int8                            numberOfLteBlackListedCells;
      UEUTRA_PhysicalCellIdentity     lteBlackListedCell[maxEUTRACellPerFreq];
  }
  CsrMcrLteFrequencyInfo;


      /************** CSR - MCR interface types (End) ***************/

/***************************************************************************
 * Typed Constants
 ***************************************************************************/

/***************************************************************************
 * Global Data declarations
 ***************************************************************************/

/***************************************************************************
 * Global Function Prototypes
 ***************************************************************************/

/* CSR functions */
void  UrrCsrCnInformationInfoInd (UCN_InformationInfo_latest * const cnInfo_p);

void UrrCsrCnInformationInfoFullInd (
          UCN_InformationInfoFull * const cnInfoFull_p,
          Boolean primary_plmn_IdentityPresent,
          UPLMN_Identity primary_plmn_Identity);

void  UrrCsrHandleTimers (RadioResourceControlEntity *urr_p);

#if ! defined (UPGRADE_EXCLUDE_2G)
void  UrrCsrNasInfoUponHandoverToUmtsInd (
          NasInfoForRrcUponHandoverToUmts * const nasInfo_p);

void  UrrCsrGrrRcReselectToUmtsReq (RadioResourceControlEntity *urr_p);

void  UrrCsrGrrRcReselectToGsmCnf (RadioResourceControlEntity *urr_p);

void UrrCsrRabmRrcPdpCtxStatusReq(RadioResourceControlEntity *urr_p);

void  UrrCsrUmphReselectToUmtsFailCnf (RadioResourceControlEntity *urr_p);

void  UrrCsrCphyCellSelectInd (RadioResourceControlEntity *urr_p);

void UrrCsrGrrRcGsmPlmnListInd (RadioResourceControlEntity *urr_p);
#if !defined (L1_SUPPORT_GSM_FG_PLMN_SEARCH) && !defined UPGRADE_PLMS /*********** add UPGRADE_PLMS*/
void UrrCsrGrrRcGsmPlmnListCnf (RadioResourceControlEntity *urr_p);
#endif

#endif

/* ********** begin */
void UrrCsrErrRcLtePlmnListInd (RadioResourceControlEntity *urr_p);
#if !defined (L1_SUPPORT_GSM_FG_PLMN_SEARCH) && !defined UPGRADE_PLMS /*********** add*/
void UrrCsrErrRcLtePlmnListCnf (RadioResourceControlEntity *urr_p);
/* ********** end */
#endif /*********** add*/


void CsrFinishConnectedNonDchOosIcsIfPossible  (CsrOosConnNonDchRecoveryFinishSource source);

/* Merge CQ00058285 begin */
void CsrReleasePendingAndSendCnfIfNeeded(void);
/* Merge CQ00058285 end */


void CsrReleasePendingAndSendCnfToMmIfNeeded(void);


void  UrrCsrRrcActReq (RadioResourceControlEntity *urr_p);

void  UrrCsrRrcPlmnListReq (RadioResourceControlEntity *urr_p);

void  UrrCsrRrcPlmnListAbortReq (RadioResourceControlEntity *urr_p);
void  UrrCsrSetCellSelectConfigReq (RadioResourceControlEntity *urr_p); /* CQ00115560 added */
void  UrrCsrBarredCellUpdateReq (RadioResourceControlEntity *urr_p);/* CQ00116138 added */
void  UrrCsrProcessInternalSignal (SignalBuffer *signal_p);

UMaxGsmTxPower
      UrrCsrPowerClassToDbm (UUE_PowerClass powerClass);

void  UrrCsrInit (const Boolean totalInitialisation,
                  const Boolean initialiseBandRegion);

Boolean UrrCsrUeIsCampedOnHplmn (Boolean useRrcEstablishReq,
                                             UAreaIdentity *areaIdentityFromNas_p);

void UrrCsrClearMmInfoPlmnData (void);

Boolean UrrCsrIsUeInHplmnCountry (UAreaIdentity *areaIdentityFromNas_p);

#if !defined (UPGRADE_EXCLUDE_2G)
void  UrrCsrGrrrcGsmInfoInd (RadioResourceControlEntity *urr_p);
#endif

void  UrrCsrIratErrcUrrEutraInfoInd (RadioResourceControlEntity *urr_p);

void  UrrCsrDedicatedPriorityInformationInd (
                       UDedicatedPriorityInformation *dedicatedPriorityInfo_p);

ActivateStatus
      UrrCsrGetActivateStatus (void);

#if !defined (UPGRADE_EXCLUDE_2G)
Boolean
      UrrCsrThereAreGsmCellsUsingGsmBandEvent (
          const UFrequency_Band band,
                Boolean         *validCellInfoList);
#endif

Boolean
      UrrCsrGetTxDiversityIndicator (void);

void  UrrCsrGetServingCellAreaId (CellAreaIdentity *servingCellAreaId_p);

Boolean UrrCsrGetCbsForbiddenDueToPlms(void); /* CQ00068019 PTK_CQ00304242 */

/* DATA decoding services */
Int16 UrrCsrDecodeURefTimeDiffToCell (
          const UReferenceTimeDifferenceToCell * const uRefTimeDiff_p);

/* CSR - L1 interface functions */
void UrrCsrCphyDetectedCellMeasInd (RadioResourceControlEntity * urr_p);    /* CQ00082411 added */

void  UrrCsrCphyFindCellCnf (RadioResourceControlEntity * urr_p);

void  UrrCsrCphyNextCellCnf (RadioResourceControlEntity * urr_p);

void  UrrCsrCphyCellSelectCnf (RadioResourceControlEntity * urr_p);

void  UrrCsrCphyRssiScanCnf (RadioResourceControlEntity * urr_p);

void UrrCsrCmacRachFailuresAlertInd(RadioResourceControlEntity * urr_p);    /* CQ00069148 added */

void  UrrCsrCphyDrxFindCellCnf (RadioResourceControlEntity * urr_p);

void  UrrCsrCphyDrxNextCellCnf (RadioResourceControlEntity * urr_p);

void  UrrCsrCphyDrxRssiScanCnf (RadioResourceControlEntity * urr_p);

#if !defined (UPGRADE_PLMS)
void CsrReportScanResult(CsrStoredSearchTable *storedSearchTable_p); //CQ00118692 added
#endif
void  UrrCsrCphyUeCapabilitySent (void);

void  UrrCsrCphyUeCapabilityConfigCnf (RadioResourceControlEntity *urr_p);

void UrrCsrPlwCphyNoActionInd (RadioResourceControlEntity *urr_p);

#if ! defined (UPGRADE_EXCLUDE_2G)
void  UrrCsrCphyGsmListMeasInd (RadioResourceControlEntity * urr_p);

void  UrrCsrCphyGsmBcchDecodeInd (RadioResourceControlEntity * urr_p);

void UrrCsrCphyGsmMultiBcchDecodeInd (RadioResourceControlEntity * urr_p);

#endif

void  UrrCsrServingCellMeasIndEvent (RadioResourceControlEntity * urr_p);

void  UrrCsrCphyMonitorIntraFreqCellInd (
            const CphyMonitorIntraFreqCellInd * const intraFreqMeas_p);

void  UrrCsrCphyMonitorInterFreqCellInd (
            const CphyMonitorInterFreqCellInd * const interFreqMeas_p);

#if !defined (UPGRADE_EXCLUDE_2G)
void  UrrCsrCphyMonitorGsmCellInd (
            const CphyMonitorGsmCellInd * const gsmMeas_p);
#endif

/* CSR - Multiband functions */
FddBandsMask UrrCsrGetCsrConfiguredBandModeBits (void);  /*CQ00107063 changed from Int16 to Int32*/   

FddBandsMask UrrCsrGetCsrPhySupportedFddBandsBits (void);

/* CSR - SIR interface functions */
void  UrrCsrMibInd (
          const UMasterInformationBlock * const mib_p,
          const UrrSirCellType                  cellType,
          Boolean                               sib5bisScheduled);

Boolean UrrCsrCheckAndGetDsacParam(CsrDsacData **dsacData_pp, Plmn *plmn_p, Int8 *plmnIndex_p); /* CQ00083952 */

/* CQ00065100, added begin */
Boolean UrrCsrCheckAndGetPpacParam(CsrPpacData **ppacData_p, Plmn *plmn_p, Int8 *plmnIndex_p); /* CQ00083952 */
/* CQ00065100, added end */

void  UrrCsrSib1Ind (
          const USysInfoType1 * const   sib1_p,
          const UrrSirCellType          cellType);

void  UrrCsrSib3Ind (
          const USysInfoType3 * const   sib3_p,
          const UrrSirCellType          cellType);

void  UrrCsrSib4Ind (
          const USysInfoType4 * const   sib4_p,
          const UrrSirCellType          cellType);

void  UrrCsrSib5Ind (
          const USysInfoType5 * const   sib5_p,
          const UrrSirCellType          cellType);

void  UrrCsrSib6Ind (
          const USysInfoType6 * const   sib6_p,
          const UrrSirCellType          cellType);

void  UrrCsrSib11Ind (
          const USysInfoType11 * const  sib11_p,
          const UrrSirCellType          cellType);

void  UrrCsrSib11bisInd (
    const USysInfoType11bis * const  sib11bis_p,
    const UrrSirCellType          cellType);

void  UrrCsrSib12Ind (
          const USysInfoType12 * const  sib12_p,
          const UrrSirCellType          cellType);

void  UrrCsrSib18Ind (
          const USysInfoType18 * const  sib18_p,
          const UrrSirCellType          cellType);

/* ********** - add begin */
#if defined UPGRADE_CSG1
void  UrrCsrSib20Ind (
          const USysInfoType20 * const  sib20_p,
          const UrrSirCellType          cellType);
#endif //UPGRADE_CSG1
/* ********** - add end */
void  UrrCsrSib19Ind (
          const USysInfoType19 * const  sib19_p,
          const UrrSirCellType          cellType);

void UrrCsrSib19ArrivedInd (const UrrSirCellType cellType); // ********** added

/* ********** begin */
void CsrSibSchedulingInfoInd (
          UrrInternalSignalIndicateSibScheduling * const sig_p);
/* ********** end */

void  UrrCsrCphyMonitorLteCellInd (
          const CphyMonitorLteCellInd * const lteMeas_p);

void  UrrCsrLteReselectRequest (RadioResourceControlEntity *urr_p);

Boolean UrrCsrIsRedirectionFromLteInProgress (void); // ********** added

void  CsrrIratReselectAckEvent (IratReselectionAck * const signal_p);

void  CsrBuildAndSendCphyDrxFindLteCellReq (const Int8  lteBandIndex);

void  UrrCsrCphyDrxFindLteCellCnf (RadioResourceControlEntity * urr_p);

void CsrBuildAndSendCphyLteNcellBchReq (Boolean bchOn,
                         const Earfcn earfcn,//********** change  UEARFCN to Earfcn
                         const UEUTRA_PhysicalCellIdentity physicalCellIdentity
/* ********** add begin */
#if defined UPGRADE_CSG1
						 ,Boolean readSib9,
						 LteNcellSib1Info *sib1Info 
#endif //UPGRADE_CSG1
/* ********** add end */

														);

void  UrrCsrCphyLteNcellBchInd (RadioResourceControlEntity * urr_p);

void  UrrCsrCphyLteNcellStopBchCnf (RadioResourceControlEntity * urr_p);

void  CsrBuildAndSendCphyDrxFindLteCellAbortReq (void);

void  UrrCsrCphyDrxFindLteCellAbortCnf (RadioResourceControlEntity * urr_p);

/* CSRDB functions (CSR DATABASE) */
Boolean
      UrrCsrdbCompareFreqInfoAndServingCell (
         Boolean                uarfcnPresent,
         UUARFCN                uarfcn,
         Boolean                primaryScramblingCodePresent,
         UPrimaryScramblingCode primaryScramblingCode);

void  UrrCsrdbGetFachMeasCoefficient (
         Int8                           *fachMeasOccasionCoeff,
         Boolean                        *interFreqFDDmeasIndicator,
         Boolean                        *interRATmeasIndicatorPresent,
         UFACH_MeasurementOccasionInfo_inter_RAT_meas_ind
                                        *interRATmeasIndicators);


void UrrCsrdbCheckDomainDsacRestrictions(Boolean *csDomainIsRestricted_p,
                                                    Boolean *psDomainIsRestricted_p,
                                                    Int16    csDomainRestrictionsBitmap,
                                                    Int16    psDomainRestrictionsBitmap);

/* CQ00065100, added begin */
Boolean UrrCsrdbCheckPpacRestrictions (Boolean *pagingRestrictedOnCs, Boolean *pagingRestrictedOnPs,
                                                  Boolean *locationRegistrationRestrictedOnCs, Boolean *locationRegistrationRestrictedOnPs,
                                                  UPagingPermissionWithAccessControlParameters_pagingResponseRestrictionIndicator	pagingResponseRestrictionIndicator,
									  			  UPagingPermissionWithAccessControlParameters_locationRegistrationRestrictionIndicator  locationRegistrationRestrictionIndicator,
									   			  Int16    locationRegistrationRestrictionsBitmap);
/* CQ00065100, added end */

#if ! defined (UPGRADE_EXCLUDE_2G)
/* CQ00050379,merge CQ00002919, begin */
/*Int8  UrrCsrdbGetGsmCellId (UBSIC bsic, UBCCH_ARFCN arfcn);*/
Int8  UrrCsrdbGetGsmCellId (UBSIC bsic, UBCCH_ARFCN arfcn,Boolean *bsicCompatible);
/* CQ00050379,merge CQ00002919, end */


/* CQ00060693, begin */
Boolean UrrCsrdbGetAllGsmCellIdbyArfcn (Int8* cellId,Int8* cellnum, UBCCH_ARFCN arfcn);
/* CQ00060693, end */


#endif

void  UrrCsrdbGetIntraFreqNCellInfoList (
         Int8                       *noOfIntraFreqCells_p,
         CsrMcrIntraFreqNCellInfo   *intraFreqNCellInfo_p);

void  UrrCsrdbGetInterFreqNCellInfoList (
         Int8                       *noOfInterFreqCells,
         CsrMcrInterFreqNCellInfo   *interFreqNCellInfo_p);

#if ! defined (UPGRADE_EXCLUDE_2G)
void  UrrCsrdbGetGsmNCellInfoList (
          Int8                      *noOfInterRATCells_p,
          CsrMcrGsmNCellInfo        *gsmNCellInfo_p);

UBCCH_ARFCN
      UrrCsrdbGetGsmCellFreq (Int8            cellId,
                              UFrequency_Band *cellBandMode_p,
                              UFrequency_Band *l1BandMode_p);
#endif

void  UrrCsrdbGetLteFreqInfoList(
                      Int8                      *noOfLteFreqInfo_p,
                      CsrMcrLteFrequencyInfo    *lteFreqInfo_p);

Int8 UrrCsrdbGetNumOfLteFreqInfoList (void);
Boolean UrrCsrCheckLteFreqSupported (void);
//LteBandsMask UrrCsrGetsupportedTddLteBandsFromErrc (void);//********** removed
//LteBandsMask UrrCsrGetsupportedFddLteBandsFromErrc (void);//********** removed
TddLteBandsMask UrrCsrGetsupportedTddLteBandsFromErrc (void);//********** added
FddLteBandsMask UrrCsrGetsupportedFddLteBandsFromErrc (void);//********** added
#if defined (UPGRADE_4G_BAND_EXT)
EutraBandMask UrrCsrGetsupportedFddLteBandsExt (void);
#endif

/* DMCR DEVELOPMENT begin */
Boolean CsrGetDeferredMeasurementStatus (void);

Boolean CsrCheckDeferredMeasurementValid (void);

void  CsrGetIntraFreqReportingQuantitySib3 (
         UIntraFreqMeasQuantity_FDD_sib3 *intraFreqReportingQuantitySib3_p);
/* DMCR DEVELOPMENT end */


Boolean
      UrrCsrdbGetTxPower (
          Boolean                   isServingCell,
          UPrimaryScramblingCode    primaryScramblingCode,
          UMaxGsmTxPower            *cellTxPower);

#if ! defined (UPGRADE_EXCLUDE_2G)
void  UrrCsrdbUpdateBsicVerified (Int8 cellId, Boolean bsicVerified);
#endif

/* CSRP functions (PLMN SELECTION) */
Boolean CsrpGsm1900BandIsUsedInThisCountry (const Mcc mcc, const Mnc mnc);

/*********** add begin*/
Boolean CsrpGsm1900BandIsUsedInThisCountryRetVal (const Mcc mcc, const Mnc mnc, UFrequency_Band 
*frequencyBandRet);
Boolean CsrGetCurrentPlmn (CsrSCellInfo * cellInfo_p, PlmnListEntry *plmn);
/*********** add end*/

/* CSRC functions (SELECT CELL) */

/* CSRS functions (CELL SELECTION) */

/* CSRR functions (CELL RESELECTION) */

#if defined (DEVELOPMENT_VERSION)
/* also include the file and line number */
void UrrCsrSetNoCellSentSubstituted (Boolean invalid,
                                               const char *const file_p,
                                               const Int32 line);
#else
void UrrCsrSetNoCellSentSubstituted (Boolean invalid);
#endif


#if defined (DEVELOPMENT_VERSION)
#define UrrCsrSetNoCellSent(vALID) \
    UrrCsrSetNoCellSentSubstituted ((vALID), (MODULE_NAME),(__LINE__))
#else
#define UrrCsrSetNoCellSent(vALID) \
    UrrCsrSetNoCellSentSubstituted ((vALID))
#endif

#if defined (DEVELOPMENT_VERSION)
/* also include the file and line number */
/*************************************************************************/

void UrrCsrSetValidSuspensionSubstituted (Boolean invalid,
                                               const char *const file_p,
                                               const Int32 line);
#else
void UrrCsrSetValidSuspensionSubstituted (Boolean invalid);
#endif

#if defined (DEVELOPMENT_VERSION)
#define UrrCsrSetValidSuspension(vALID) \
    UrrCsrSetValidSuspensionSubstituted ((vALID), (MODULE_NAME),(__LINE__))
#else
#define UrrCsrSetValidSuspension(vALID) \
    UrrCsrSetValidSuspensionSubstituted ((vALID))
#endif

#if ! defined (UPGRADE_EXCLUDE_2G)
UBCCH_ARFCN
      UrrCsrrGetGsmCellFreq (Int8 cellId);
#endif

Boolean  UrrCsrrProcessRssiMeas (SignedInt16 cpich_RSCP, SignedInt16 *oldReportedRscp_p,SignedInt16 cpich_ecn0);/* ********** added cpich_ecn0*/

Boolean
      UrrCsrrIsServingCellBarred (void);

void  UrrCsrrGetCandidateCellsUponDchToIdleInd (void);

void UrrCsrrSetServingCellMeasActive(Boolean isEnabled);

Boolean CsrIsServingPlmn3Digit(void);

Boolean UrrCsrIsCampedOnCell (void);

void CsrActionsUponStartingToSelectACell (UrrInternalSignalSelectCell * const requestedCellInfo_p);

void  CsrInternalSignalSelectCell (
                UrrInternalSignalSelectCell *requestedCellInfo_p);

void  CsrUeModeStateChangeEvent (
          const UrrSmcModeAndState *newModeAndState_p,
          const UrrSmcModeAndState *currentModeAndState_p);

 void CsrUeSuspendedToConnectedNonDch (
                const UrrSmcConnectedState newState);

void  CsrStartCellSelection (
        CsrSource      source,
        ActivationTime activationTime,
        Boolean        clearPrimaryDb,
        Boolean        clearBestCellSoFar,
        Boolean        icsInitiated
		/*********** add begin*/
#if defined (URR_MRAT_ICS_SEARCH)
			, CsrsPlmsIcsStageType	icsStageType
#endif
		/*********** add end*/
        );

void  CsrRrcActReq (CsrGmmInfo *gmmInfo_p);

CsrCellTypes CsrGetLatestBestRankedCell(void);
Boolean CsrIsUarfcnOnSupportedBand (const UUARFCN uarfcn_dl);

/* ********** begin */
#if defined (SS_IPC_SUPPORT)    /* ********** modified */
Boolean CsrIsUarfcnOnOnlyRegistrationBand (const UUARFCN uarfcn_dl, FddBandsMask fddBandsOnlyReg);
#endif
/* ********** end */

Boolean UrrCsrGetDetectedOnNonDch(void);

void UrrCsrSetDetectedOnNonDch(Boolean status);

Boolean UrrCsrIsDetectedOnNonDchAllowed(Int8 numOfMonitor, UrrSmcModeAndState modeAndState);

void UrrCsrrStopIntraDetectedFreqMeas(void);

Int8 UrrCsrrkCountNumOfMonitorCellsAfterRanking(void);

void UrrCsrdbInitIntraFreqCellInfo (
                     CsrIntraFreqNCellInfo * const cell_p,
               const UIntraFreqCellID              cellId);
			   
/* ********** modify begin */
#if !defined UPGRADE_PLMS
void UrrCsrGetStates (CsrProcId                 *csrState_p,
                           CsrsSearchStatus     *csrsState_p,
                           CsrcState            *csrcState_p,
                           CsrpState            *csrpState_p,
                           CsrrReselectorState  *csrrState_p);
#else
void UrrCsrGetStates (CsrProcId            *csrState_p,
                      CsrsSearchStatus     *csrsState_p, 
					  CsrcState            *csrcState_p,
					  CsrpActiveState      *csrpActiveState_p, 
					  CsrpPlmsState        *csrpPlmsState_p, 
					  CsrrReselectorState  *csrrState_p);
#endif
/* ********** modify end */

Boolean UrrCsrIsCampedOnPlmn (Plmn *plmn_p);
Boolean CsrCheckIfRequestedPlmnValid(void);/* ********** added */

Rac UrrCsrGetRacFromPrimaryDb(void);                                           //-+ ********** 2-Jan-2012 +-
Boolean UrrCsrGetRrcIsOosAndMmInformed(void);                                  //-+ ********** 2-Jan-2012 +-

/* ********** added begin */
void UrrCsrSetStartIcsUponPhyConfigFinish (Boolean start);
void  UrrCsrStopCellSelectionTimer (Boolean stopAsExpired);

Boolean UrrCsrDecreaseMaxBadCrc(void);
/* ********** added end */

void CsrHandleSib18Update (const USysInfoType18 * const sib18_p);

#if defined (ON_PC)
void UrrCsrCphyUeCapabilityConfigCnf_fake(void);
#endif

void  CsrClearForbiddenList (void); /* ********** add */
void UrrCsrSetBcchErrorDuringBcchMod (Boolean bcchError);/* ********** add  */

/* Add for ********** */
#if defined (UPGRADE_DSDS)
void CsrTriggerCellSelection(void);
#if !defined(UPGRADE_COMMON_STORE)
void CsrSendGrrRcResumeReq (void);   /* for ********** */
void UrrCsrGrrRcResumeReq (RadioResourceControlEntity *urr_p);  /* for ********** */
void UrrCsrSetSearchSuspendByCsr (Boolean searchSuspendBycsr);
Boolean UrrCsrGetSearchSuspendByCsr (void);
#else
/* ********** added begin */
#if defined (URR_MRAT_ICS_SEARCH)
Boolean UrrCsrsIsAbortingIcs (void);
void UrrCsrCheckRecovryNeeded (void);
#endif
/* ********** added end */
void CsrSendIratDsCellSearchFinishInd (void);   /* for ********** */
void UrrCsrIratDsCellSearchFinishInd (RadioResourceControlEntity *urr_p);  /* for ********** */
#endif/*UPGRADE_COMMON_STORE*/
void CsrStorePendingRequest (CsrProcedure requestType,UrrSubProcessId process, void *request_p);/* CQ00112625 added */
//void CsrActionPendingRequest (void);  /* for CQ00015546 */
void UrrCsrSetReadingSibs (Boolean readingsibs);
Boolean UrrCsrGetReadingSibs (void);
/* for CQ00036735 Begin */
void UrrCsrSetTriggerSelect121Flag (Boolean triggerSelectFlag);
Boolean UrrCsrGetTriggerSelect121Flag (void);
/* for CQ00036735 End */
#if defined(UPGRADE_COMMON_STORE)
Boolean UrrCheckForegroundSearchOngoing (Int8 simId);   /* CQ00031540 add */
/* CQ00059612 add begin */
#else
Boolean UrrCheckForegroundSearchOngoing (void);   /* CQ00031540 add */
#endif/*UPGRADE_COMMON_STORE*/
/* CQ00059612 add begin */
#if !defined(UPGRADE_COMMON_STORE)
void UrrCsrSetgrrRcReselectingFlag (Boolean grrRcReselectingFlag);
Boolean UrrCsrGetgrrRcReselectingFlag (void);
#else
void UrrCsrSetDsReselectingFlag (Boolean grrRcReselectingFlag);
Boolean UrrCsrGetDsReselectingFlag (void);
#endif/*UPGRADE_COMMON_STORE*/
/* CQ00059612 add end */
/* CQ00107934 added begin */
void UrrCsrNotifyOtherCardSearchFinish (void);
Boolean UrrCsrGetReselectingFlagOfOtherCard (void);
Boolean UrrCsrGetReselectingFlag (void);
Boolean UrrCsrSetReselectingFlag (Boolean reselectingFlag);
Boolean UrrCsrGetIratReselectingFlag (Int8 simId);
Boolean UrrCsrSetIratReselectingFlag (Int8 simId,Boolean iratFlag);
Boolean UrrCsrGetRrcActiveFlag (void);
/* CQ00107934 added end */
Boolean UrrCsrSetRrcActiveFlag (Boolean activeFlag);//CQ00108006 added
/* CQ00076013 added begin */
ActivateStatus CsrBuildAndSendFakeRrcActInd (const CsrSourceForDataToSendToMm getDataFrom,
                                                    OldSignalType                    oldSignalType);
/* CQ00076013 added end */
#endif

void CsrActionPendingRequest (void);  /* for CQ00015546 */


void CsrProcessInterFreqPlmnList (
                UPLMNsOfInterFreqCellsList  * const plmnsOfInterFreqCellsList_p,
                CsrInterFreqNCellInfo       *       interFreqNCellInfo_p,
                Int8                                noOfCells,
                MultiplePlmnTable           *       defaultPlmnList_p);

void CsrProcessIntraFreqPlmnList (
                UPLMNsOfIntraFreqCellsList  * const plmnsOfIntraFreqCellsList_p,
                CsrIntraFreqNCellInfo       *       intraFreqNCellInfo_p,
                Int8                                noOfCells,
                MultiplePlmnTable           *       defaultPlmnList_p);

#if ! defined (UPGRADE_EXCLUDE_2G)
void CsrProcessGsmEquivalentPlmnList (
                UPLMNsOfInterRATCellsList   * const plmnsOfInterRATCellsList_p,
                CsrGsmNCellInfo             *       gsmNCellInfo_p,
                Int8                                noOfCells,
                MultiplePlmnTable           *       defaultPlmnList_p);
#endif

void CsrCommonActionsOnIntSigSelectCell (UrrInternalSignalSelectCell *const sig_p);  /* ********** add */

SignedInt16 UrrCsrGetSValue (SignedInt16 pccpch_RSCP);
Boolean UrrCsrScellSuitable (void);

void CsrSendIratUrrErrcUmtsInfoInd (void);/*********** add*/

#if ! defined (UPGRADE_EXCLUDE_2G)
void CsrSendGrrRcUmtsInfoInd (void);/*********** add*/
Boolean CsrrCheckGSMOOSInIdleOngoing (void);/*********** add */
#endif

/* ********** - add begin */
#if defined UPGRADE_CSG1
/*CSRCSG functions*/
void CsrHandleRrcCsgSelectReq (Int8 numOfCsgCells, CsrCell *csgCellList);
#endif //UPGRADE_CSG1
/* ********** - add end */

#if defined (RRC_UPGRADE_ENG_MODE_REDESIGN) //********** begin
Boolean UrrCsrEngModeGetMeasNonDch(CiDevWbActiveAndMonitoredSetInfoEng_RRC *metric61);
Boolean UrrCsrEngModeGetInterRatMeasNonDch(CiDevWbInterRatNeighborMeasEng_RRC *metric62);
Boolean UrrCsrEngModeGetSCellMeasNonDch(CiDevWBRfInfoEng_RRC *metric60);

#endif //RRC_UPGRADE_ENG_MODE_REDESIGN :********** end
//********** start 
#if defined(ENABLE_END_OF_DRX_MEAS_IND)
void UrrCsrCphyEndOfDrxMeasInd (RadioResourceControlEntity *urr_p);
#endif /* ENABLE_END_OF_DRX_MEAS_IND */
//********** end 

void UrrCsrRequestStopIratReq (RadioResourceControlEntity *urr_p);     /* ********** add */
void UrrCsrIratDsStopIratReq (RadioResourceControlEntity *urr_p);

/* **********  added begin */
void CsrFakeNextRssiScanWithSpecifyBand (const CsrRssiScanType rssiScanType,
                                const   CsrRssiScanStage rssiScanStage,
                                const CsrSearchType searchType,
                                CphyDrxRssiScanCnf *cphyDrxRssiScanCnf_p,
                                FddBandIndex currentBandIndex);

void CsrCovertRssiScanResultToOtherband (                                   
                                CphyDrxRssiScanCnf *newCphyDrxRssiScanCnf_p,
                                CphyDrxRssiScanCnf *oldCphyDrxRssiScanCnf_p,
                                FddBandIndex bandIndex);       
                                
Boolean CsrCheckAdditionalFreqForOverlapband(FddBandIndex *newBandIndex);
/* **********  added end */

/* ********** add begin */
Boolean CsrCheckPlmnListInHplmn (Int16 numberOfPlmn,Plmn *plmn_p);
/* ********** add end */

#if defined (UPGRADE_PLMS)
void UrrCsrCphyFgBchCnf (RadioResourceControlEntity *urr_p);
void UrrCsrHandleIrrIcsEvalReq (RadioResourceControlEntity *urr_p);
#endif/*UPGRADE_PLMS*/
void UrrCsrHandleIrrIcsCampingReq(RadioResourceControlEntity *urr_p);
void UrrCsrHandleIrrIcsSearchCnf(RadioResourceControlEntity *urr_p);
void UrrCsrHandleIrrIcsEvalAbortReq (RadioResourceControlEntity *urr_p);
void UrrCsrHandleIrrIcsAbortCnf(RadioResourceControlEntity *urr_p);
Boolean UrrCsrIsPlmsIcsInProgress(void);
void UrrCsrHandleCphySetWbCnf(RadioResourceControlEntity *urr_p);
void UrrCsrHandleRrcDeactReq(RadioResourceControlEntity *urr_p);
void CsrsAbortIcsUponL1Cnf (void);
void UrrCsrHandleCphyIcsInitCnf(RadioResourceControlEntity *urr_p);
/* ********** added begin */
void UrrCsrHandleIrrIcsReselectionRejByNasInd (RadioResourceControlEntity *urr_p);
/* ********** added end */
/* ********** added begin */
void CsrSetCbsForbiddenDueToPlms(
                Boolean cbsForbiddenDueToPlms, 
                Boolean notifyRbc, 
                Int8    callerId);
/* ********** added end */
/*********** add begin*/
Boolean CsrrCheckRedirectToLteOngoing (void);
/*********** add end*/

#if defined (URR_MRAT_ICS_SEARCH)
void UrrCsrHandleRrcRequestStopIratReq(RadioResourceControlEntity *urr_p);/*********** add*/
Boolean UrrCsrIsWaitForIratReselection(void); /*********** add*/
#endif/*URR_MRAT_ICS_SEARCH*/

Boolean CsrGetSimPresent (void);/*********** add*/
Boolean CsrCheckIfUsefulPlmnExist(void);/*********** add*/
//void UrrTraceSib19memory(Int8 action);/*********** add*/ /*********** remove*/

Boolean CsrpIsFreqInPlmnFreqList (Int8 numFreqs, Int32 *freq_p, Int32 freqToFind); /*********** add*/// ********** change Int16 to Int32

#if defined(ENABLE_WB_PCH_SIM2_MEASUREMENT)
void urrCsrrResumeMeasurementAfterResumeBySim2 (void);
#endif
#if defined (ENABLE_SEARCH_ABORT_BY_SIM2_DEACT)
void UrrCsrIratDsAbortSearchReq (RadioResourceControlEntity * urr_p);
void UrrCsrHandleIratDsPowerOffCompleteInd (void);
#endif
void CsrAbortSuspendedPlmnSearch (Boolean isAbortForOOS ); //********** added
#if defined (ENABLE_SEARCH_ABORT_BY_SIM2_SERVICE)
void CsrrAbortSearchEventBySim2Request(void);
void UrrCsrClearPendingRequstFromNasDueSuspend (void);
void CsrAbortFinishForSuspend (void);
void CsrAbortFinishForIratDsAbortSearchReq (void);
void UrrCsrAbortReselection (CsrAbortReselectCause cause);
#endif//ENABLE_SEARCH_ABORT_BY_SIM2_SERVICE    
#if (defined(UPGRADE_DSDSWB) || defined(UPGRADE_DSDSLTE)) && defined(UPGRADE_PLMS)
Boolean UrrCsrIsInIcsCampToOtherRat(void);
#endif

Boolean UrrCsrIgnoreCellSearchInConnectedState(void);

/* ********** added begin */
#if defined (UPGRADE_WIFI)
void UrrCsrHandleIratDsWifiStartReq(RadioResourceControlEntity *urr_p);
    void UrrCsrHandleIratDsWifiFinishInd(RadioResourceControlEntity *urr_p);
#endif/*UPGRADE_WIFI*/
/* ********** added end */
Boolean CsrCheckIfRrcDeactReqPended (void);/* ********** added */

#endif /* !defined (URRCSR_H) */


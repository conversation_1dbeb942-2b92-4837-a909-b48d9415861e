/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/3g.mod/lib/src/ulbgupde.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2011/03/27 20:17:00 $
 **************************************************************************
 * File Description:
 *
 *      The UPDCP Entity.
 **************************************************************************/

#if !defined (ULBGUPDE_H)
#define ULBGUPDE_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <cpdc_sig.h>
#include <urlc_sig.h>
#include <ulbgupde_rab.h>
#include <gpcntr.h> 
#include <updlltyp.h>

/***************************************************************************
* Manifest Constants
***************************************************************************/

#define NUM_OF_RABS                         16
/* Added by Daniel for ********** 20120803, begin */
/*Added by liubin for Wujing optimization in U-plane, 2012-07-26,begin*/
#define NUM_OF_VALID_RBS                    33
/*Added by liubin for Wujing optimization in U-plane, 2012-07-26,end*/
/* Added by Daniel for ********** 20120803, end */

#define PDU_TYPE_MASK                       0xE0
#define PDU_TYPE_PID_HDR_COMP               0x00
#define PDU_TYPE_PID_HDR_COMP_AND_SEQNUM    0x01
#define PDU_TYPE_SHIFT                      5

#define PDCP_PID_MASK                       0x1F
#define PDCP_PID_NO_HDR_COMP                0

#define PDCP_PDU_MIN_OFFSET                 3   /* 3 bytes: Pdu type + PID and Seq Num */

#define PDCP_SEQNUM_PDU_OFFSET              3

#define PDCP_DATA_PDU_OFFSET                1

/***************************************************************************
* Macro Functions
***************************************************************************/

/***************************************************************************
* Types
***************************************************************************/
typedef struct UlbgUpdEntityTag
{
    /* Semi-static (configured) variables */
    UrabId                      rabId;
    UrlcSap                     rlcSap;
    Boolean                     lossless;           /* The support of lossless SRNS Relocation */
    Int16                       snWindowSize;       /* Only applicable when lossless */
    Boolean                     noHeader;           /* The use of the PDCP-No-Header PDU */

    /* Dynamic variables */
    Boolean                     ulSuspended;        /* Is UL transmission suspended? */
    Int16                       sendSn;             /* Next UL_Send PDCP SN */
    Boolean                     firstNpduSent;
    Int16                       numSendSn;          /* Number of sendSn's used (must be
                                                       < snWindowSize for lossless)
                                                       For non-lossless it used to process
                                                       discard's */
    Int16                       unackedAndUntransmitNum; // for IP packet number flow control
    Boolean                     loopback;           /* DL to UL loopback */
    Boolean                     loopbackClosed;     /* loopback closed (enabled) */
    UT_DOUBLE_LINK_LIST_NODE_TYPE (UpdcpNpduLList) *nextSendNpdu;  /* Next N-PDU to send to RLC
                                                       when window size was exceeded */
    Int16                       receiveSn;          /* Next expected DL_Receive PDCP SN */ 
    Boolean                     firstNpduReceived;  /* Lossless SRNS */
	
    UrlcMessageUnitIdentifier   mui;                /* Message Unit Identifier */
    Boolean                     syncSn;             /* SN synchronization is in effect */
    Boolean                     syncMuiPresent;     /* syncMui (below) is written */
    UrlcMessageUnitIdentifier   syncMui;            /* MUI of the first seqNum PDU */
    UpdcpNpduLList              updcpNpduLList;     /* N-PDU's waiting for cnf from RLC in AM */
    Boolean                     enableGlobalDcomp;
    Boolean                     enableGlobalPcomp;
}
UlbgUpdEntity;

#if defined (ENABLE_UPLANE_STATISTICS)
/* PDCP statistics */
typedef struct UlbgUpdStatisticsTag
{
    Int16                       numUlPdusDiscarded; /* Number of UL PDUs discarded */
}
UlbgUpdStatistics;
#endif /* ENABLE_UPLANE_STATISTICS */

/***************************************************************************
* Typed Constants
***************************************************************************/

/***************************************************************************
* Function Prototypes
***************************************************************************/


void UlbgUpdeInit(UrabId rabId);

UlbgUpdEntity* UlbgUpdeGetRab(UrabId rabId);
UlbgUpdEntity* UlbgUpdeGetRb(BearerIdentity rbIdentity);

void UlbgUpdeConfigure(	UrabId         rabId,
                        UrlcSap        *rlcSap,
                        UPDCP_Info_r4  *pdcp_Info,
                        Boolean         loopback);

void UlbgUpdeReConfigure(UlbgUpdEntity *pde,
                      UrlcSap       *rlcSap,
                      UPDCP_Info_r4 *pdcp_Info,
                      Boolean        snSync,
                      Boolean        reInitHC,
                      Boolean        loopback);

void UlbgUpdeRelease(UrabId rabId);

/* Added by Daniel for ********** 20120803, begin */
/*Added by liubin for Wujing optimization in U-plane, 2012-07-26,begin*/
UrabId UlbgUpdeSetRabFromRb(BearerIdentity bearerIdentity, UrabId rabId);
UrabId UlbgUpdeGetRabFromRb(BearerIdentity bearerIdentity);
/*Added by liubin for Wujing optimization in U-plane, 2012-07-26,end*/
/* Added by Daniel for ********** 20120803, end */

/***************************************************************************
* Globals
***************************************************************************/
#if defined (ENABLE_UPLANE_STATISTICS)
/* PDCP statistics */
extern UlbgUpdStatistics updStatistics;
#endif /* ENABLE_UPLANE_STATISTICS */
#endif



/* END OF FILE */

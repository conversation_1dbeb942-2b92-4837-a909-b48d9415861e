/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/api/inc/urltypes.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2006/11/08 16:38:01 $
 **************************************************************************
 * File Description:
 *
 *    Type definitions for URLC which may be required in all URLC modules.
 **************************************************************************/

#if !defined (URLTYPES_H)
#define       URLTYPES_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <system.h>
#include <ups_typ.h>
#include <l3_typ.h>     /* Needed for TcUmtsTestLoopMode in UrlLoopbackInfo */
#include <ki_typ.h>
#include <u1_cfg.h>

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/
/* Size of request table */
#define MAX_TX_REQ_ID 16

/* Bearer identity masks used on UMAC_DATA_IND */
#define URL_BEARER_PROCESSED_MASK       0x80
#define URL_BEARER_ID_MASK              0x7F

/* PDU element sizes */
#define URL_UM_PDU_HEADER_BITS          8       /* Size of UM PDU header
                                                 * (bits)
                                                 */
#define URL_UM_PDU_HEADER_OCTETS        1       /* Size of UM PDU header
                                                 * (octets)
                                                 */
#define URL_UM_SN_BITS                  7       /* Size of UM sequence numbers
                                                 * (bits)
                                                 */
#define URL_UM_SN_MODULUS               128     /* UM sequence number modulus:
                                                 *      7 bits=0-127
                                                 */
#define URL_AM_PDU_DATA_HEADER_OCTETS   2       /* Size of control PDU header
                                                 * (octets)
                                                 */
#define URL_AM_PDU_DATA_HEADER_BITS     16      /* Size of control PDU header
                                                 * (bits)
                                                 */
#define URL_AM_PDU_CONTROL_HEADER_BITS  4       /* Size of control PDU header
                                                 * (bits)
                                                 */
#define URL_AM_SN_BITS                  12       /* Size of AM sequence numbers
                                                 * (bits)
                                                 */
#define URL_AM_SN_MODULUS               4096    /* AM sequence number modulus:
                                                 *      12 bits=0-4095
                                                 */
#define URL_UM_HFNI_BITS                25      /* Size of HFNI field (bits) */
#define URL_AM_HFNI_BITS                20      /* Size of HFNI field (bits) */

#if defined UPGRADE_3G_EDCH
#define NUM_OCTET_ALLOCATED_FOR_LI      20      /* 2 octet for LI=15 * max LI's (10) = 20 */
#endif /*UPGRADE_3G_EDCH*/

/* PDU header manipulation */
#define URL_PDU_E_BITS                  1
#define URL_PDU_E_MASK                  0x01
#define URL_PDU_LI7_MASK                0xFE
#define URL_PDU_LI15_HIGH_MASK          0xFF
#define URL_PDU_LI15_LOW_MASK           URL_PDU_LI7_MASK

/* Length indicators sizes */
#define URL_PDU_LI7_BITS                7
#define URL_PDU_LI7_OCTETS              BITS_TO_INT8S (URL_PDU_LI7_BITS)
#define URL_PDU_LI15_BITS               15
#define URL_PDU_LI15_OCTETS             BITS_TO_INT8S (URL_PDU_LI15_BITS)
#define URL_PDU_LI_OCTETS_TO_BITS(liOctets)  ((liOctets*8)-1)  /*CQ00067395 merged for FRBD 20140806*/

/* PDU length indicator values */
#define URL_PDU_E_LI                    1                   /* More LI's */
#define URL_PDU_E_DATA                  0                   /* Last LI */
#define URL_PDU_LI7_EXACT               (Int8 )  0x00
#define URL_PDU_LI15_EXACT              (Int16)0x0000
#define URL_PDU_LI15_ONE_SHORT          (Int16)0x7FFB
#define URL_PDU_LI7_START               (Int8 )  0x7C
#define URL_PDU_LI15_START              (Int16)0x7FFC
#define URL_PDU_LI7_RESERVED            (Int8 )  0x7D
#define URL_PDU_LI15_RESERVED           (Int16)0x7FFD
#define URL_PDU_LI7_PIGGYBACK_STATUS    (Int8 )  0x7E
#define URL_PDU_LI15_PIGGYBACK_STATUS   (Int16)0x7FFE
#define URL_PDU_LI7_PAD                 (Int8 )  0x7F
#define URL_PDU_LI15_PAD                (Int16)0x7FFF

/* The RLC PDU contains a segment of an SDU but neither the
 * first octet nor the last octet of this SDU (25.322 CR 0280) */
#define URL_PDU_LI7_MIDDLE_SEGMENT      (Int8 )  0x7E
#define URL_PDU_LI15_MIDDLE_SEGMENT     (Int16)0x7FFE

/* The first data octet in this RLC PDU is the first octet
 * of an RLC SDU and the last octet in this RLC PDU is the
 * last octet of the same RLC SDU (25.322 CR 0297) */
#define URL_PDU_LI7_FULL_SDU            (Int8 )  0x7D
#define URL_PDU_LI15_FULL_SDU           (Int16)0x7FFD

/* The first data octet in this RLC PDU is the first octet
 * of an RLC SDU and the second last octet in this RLC PDU
 * is the last octet of the same RLC SDU. The remaining one
 * octet in the RLC PDU is ignored (25.322 CR 0297) */
#define URL_PDU_LI15_FULL_SDU_PLUS_OCT  (Int16)0x7FFA

/* Special header extension indicates that the final SDU segment
 * of PDU was exactly filled with the last segment of the SDU.
 * The special header extension value is '10'.
 */
#define URL_PDU_SPECIAL_HEADER_EXT      (Int16)  0x2


#define URL_PDU_MINUMUM_USABLE_OCTETS   1   /* Minimum number of octets for a
                                             * PDU to be usable
                                             */
#define URL_SDU_ONE_SHORT_OCTETS        -1  /* Size of remaining SDU data that
                                             * causes the use of the ONE_SHORT
                                             * length indicator
                                             */

/* Maximum value for AM SUFI lengths */
#define URL_AM_SUFI_MAX_LIST_LENGTH     15  /* Max number of (SN, L) entries
                                             * in a LIST SUFI
                                             *  0    = Illegal
                                             *  1-15 = 1-15 entries
                                             */
#define URL_AM_SUFI_MAX_BITMAP_LENGTH   16  /* Max number of octets in a
                                             * BITMAP SUFI
                                             *  0-15 = 1-16 octets
                                             */
#define URL_AM_SUFI_MAX_RLIST_LENGTH    15  /* Max number of CW's in a
                                             * RLIST SUFI:
                                             *  0    = Only FSN is present
                                             *  1-15 = 1-15 CW's
                                             */
#define URL_AM_SUFI_MAX_MRW_LENGTH      15  /* Max number of SN_MWR's in a
                                             * MRW SUFI
                                             *  0    = 1 SN_MRW, where the
                                             *         discarded SDU extends
                                             *         above configured tx
                                             *         window
                                             *  1-15 = 1-15 SN_MRW's
                                             */

/* Macros to define the sizes of AM control PDU bodies, excuding the header */
#define URL_AM_SUFI_NO_MORE_BITS        (URL_AM_SUFI_TYPE_BITS)
#define URL_AM_SUFI_WINDOW_BITS         ((URL_AM_SUFI_TYPE_BITS) +             \
                                         (URL_AM_SUFI_SN_BITS))
#define URL_AM_SUFI_ACK_BITS            ((URL_AM_SUFI_TYPE_BITS) +             \
                                         (URL_AM_SUFI_SN_BITS))
#define URL_AM_SUFI_LIST_BITS(len)      ((URL_AM_SUFI_TYPE_BITS) +             \
                                         (URL_AM_SUFI_LENGTH_BITS) +           \
                                         ((len) * ((URL_AM_SUFI_SN_BITS) +     \
                                                   (URL_AM_SUFI_L_BITS)        \
                                                  )                            \
                                         )                                     \
                                        )
#define URL_AM_SUFI_BITMAP_BITS(len)    ((URL_AM_SUFI_TYPE_BITS) +             \
                                         (URL_AM_SUFI_LENGTH_BITS) +           \
                                         (URL_AM_SUFI_SN_BITS) +               \
                                         (BITS_TO_INT8S(len)*BITS_PER_INT8)    \
                                        )
#define URL_AM_SUFI_RLIST_BITS(len)     ((URL_AM_SUFI_TYPE_BITS) +             \
                                         (URL_AM_SUFI_LENGTH_BITS) +           \
                                         (URL_AM_SUFI_SN_BITS) +               \
                                         ((len) *                              \
                                          ((URL_AM_SUFI_CW_VALUE_BITS) +       \
                                           (URL_AM_SUFI_CW_E_BITS)             \
                                          )                                    \
                                         )                                     \
                                        )
#define URL_AM_SUFI_MRW_BITS(len)       ((URL_AM_SUFI_TYPE_BITS) +             \
                                         (URL_AM_SUFI_LENGTH_BITS) +           \
                                         (URL_AM_SUFI_N_BITS) +                \
                                         (((len) == 0)                         \
                                          ? (URL_AM_SUFI_SN_BITS)              \
                                          : ((len) * (URL_AM_SUFI_SN_BITS))    \
                                         )                                     \
                                        )
#define URL_AM_SUFI_MRW_ACK_BITS        ((URL_AM_SUFI_TYPE_BITS) +             \
                                         (URL_AM_SUFI_N_BITS) +                \
                                         (URL_AM_SUFI_SN_BITS)                 \
                                        )
#define URL_AM_RESET_BITS               (URL_AM_HFNI_BITS)
#define URL_AM_RESET_ACK_BITS           (URL_AM_HFNI_BITS)

/* Maximum data length values (one less than lowest possible LI value) */
#define URL_PDU_MAX_DATA_OCTETS_LI7     ((URL_PDU_LI7_START)-1)
#define URL_PDU_MAX_DATA_OCTETS_LI15    ((URL_PDU_LI15_ONE_SHORT)-1)

/* Maximum allowable PDU sizes */
#define URL_UM_MAX_7_BIT_PDU_OCTETS     125
#define URL_UM_MAX_15_BIT_PDU_OCTETS    ((URL_UM_PDU_HEADER_OCTETS)+           \
                                         (URL_PDU_LI15_OCTETS)+                \
                                         (URL_PDU_MAX_DATA_OCTETS_LI15)        \
                                        )
#define URL_AM_MAX_7_BIT_PDU_OCTETS     126
#define URL_AM_MAX_15_BIT_PDU_OCTETS                                           \
        ((BITS_TO_INT8S (URL_AM_PDU_DATA_HEADER_BITS))+                        \
         (URL_PDU_LI15_OCTETS)+(URL_PDU_MAX_DATA_OCTETS_LI15)                  \
        )

/* Maximum size of a control PDU data is defined by the maximum size of the
 * largest possible control PDU type, which, in Release 99 of 25.322, is the
 * LIST SUFI
 */
#define URL_AM_CTRL_MAX_DATA_OCTETS                                            \
    BITS_TO_INT8S (URL_AM_PDU_CONTROL_HEADER_BITS +                            \
                   URL_AM_SUFI_LIST_BITS (URL_AM_SUFI_MAX_LIST_LENGTH)         \
                  )

/* When a bit aligned PDU needs octet aligning, it needs to be bit copied into
 * a buffer which is large enough to hold all octets of the unaligned data +
 * two extra octet for aligment, then copy the data into into the 2nd octet
 * of the buffer, so that it can be aligned into the 1st octet
 */
#define URL_BIT_ALIGNMENT_SAFETY_ZONE   2

/* Maximum number of cipher configurations per bearer */
#define URL_MAX_CIPHER_CONFIGS          3

/***************************************************************************
*   Macro Functions
***************************************************************************/

/* PDU single octet field access macros */
#define URL_PDU_E_GET(octet)        (octet & URL_PDU_E_MASK)
#define URL_PDU_E_SET(octet,e)      (octet & URL_PDU_LI7_MASK) |               \
                                    (e ? URL_PDU_E_LI : URL_PDU_E_DATA)
#define URL_UM_PDU_SN_GET(octet)    (octet >> 1)
#define URL_AM_PDU_SN_GET(word)     ((word >> 3) & 0x0FFF)

#define URL_UM_NEXT_SN(sn)          ((sn+1)%URL_UM_SN_MODULUS)
#define URL_UM_PREV_SN(sn)          ((sn+URL_UM_SN_MODULUS-1)%URL_UM_SN_MODULUS)
#define URL_AM_NEXT_SN(sn)          ((sn+1)%URL_AM_SN_MODULUS)
#define URL_AM_PREV_SN(sn)          ((sn+URL_AM_SN_MODULUS-1)%URL_AM_SN_MODULUS)

/* Constant which defines an illegal sequence number */
#define URL_SN_UNDEFINED                0xFFFF

/* Macros to access the userId field of timers, which has the following
 * structure:
 *   MSB                                                        LSB
 *   15  14  13  12  11  10  9   8   7   6   5   4   3   2   1   0
 * |-------------------------------|---------------|---------------|
 * |   |   |   |   |   |   |   |   |   |   |   |   |   |   |   |   |
 * |-------------------------------|---------------|---------------|
 * |Unused |    Bearer identity    |   Optional    |Timer identity |
 *                                    Timer data
 *
 * Timers belonging to UrlMain will have Bearer identity = RB_INVALID & 0x3F
 */
#define URL_TIMER_SET(userId,b,t,d)     (userId) = (KiTimerUserValue)         \
                                                   (                          \
                                                    ((b & 0x3F) << 8) |       \
                                                    ((d & 0x0F) << 4) |       \
                                                    (t & 0x0F)                \
                                                   )
#define URL_TIMER_BEARER_SET(userId,b)  (userId) = (userId & 0x3F00) |         \
                                                   ((b & 0x3F) << 8)
#define URL_TIMER_BEARER_GET(userId)    ((userId >> 8) & 0x003F)
#define URL_TIMER_DATA_SET(userId,d)    (userId) = (userId & 0xFF0F) |         \
                                                   ((d & 0x0F) << 4)
#define URL_TIMER_DATA_GET(userId)      ((userId >> 4) & 0x000F)
#define URL_TIMER_ID_SET(userId,t)      (userId) = (userId & 0xFFF0) |         \
                                                   (t & 0x0F)
#define URL_TIMER_ID_GET(userId)        (userId & 0x000F)

/* UM hyper rate Frame Number (HFN) */
#define URL_UM_HFN_MODULUS              0x02000000  /* 25 bits */
#define URL_UM_NEXT_HFN(hfn)            ((hfn + 1) % (URL_UM_HFN_MODULUS))
#define URL_UM_PREV_HFN(hfn)            (((hfn) + (URL_UM_HFN_MODULUS - 1)) %  \
                                            (URL_UM_HFN_MODULUS))
/* As a UM COUNT-C is HFN=25 MSB's/SN=7 LSB's and a startValue must set the 20
 * MSB's of the HFN, then to convert startValue into a UM HFN, it needs to be
 * shifted << 32-(20-7)=5, with LSB's of HFN set to 0
 */
#define URL_UM_START_TO_HFN(startValue) ((UCOUNT_C)(startValue) << 5)

/* AM hyper rate Frame Number (HFN) */
#define URL_AM_HFN_MODULUS              0x0100000    /* 20 bits */
#define URL_AM_NEXT_HFN(hfn)            ((hfn + 1) % (URL_AM_HFN_MODULUS))
#define URL_AM_PREV_HFN(hfn)            (((hfn) + (URL_AM_HFN_MODULUS - 1)) %  \
                                            (URL_AM_HFN_MODULUS))

/* As a AM COUNT-C is HFN=20 MSB's/SN=12 LSB's and a startValue must set the 20
 * MSB's of the HFN, then startValue translates directly into HFN
 */
#define URL_AM_START_TO_HFN(startValue) (UCOUNT_C)(startValue)

/* Constant which defines when a HFN is not defined by a cipher configiration */
#define URL_HFN_UNDEFINED               0xFFFFFFFF

/* COUNT-C, made from HFN and SN */
#define URL_UM_COUNT_C(hfn,sn)          (((UCOUNT_C)(hfn) & 0x01FFFFFF) <<     \
                                            URL_UM_SN_BITS) | (sn)
#define URL_UM_GET_COUNT_C_HFN(countC)  ((UrlHyperFrameNumber)(countC) >>      \
                                            URL_UM_SN_BITS)
#define URL_UM_GET_COUNT_C_SN(countC)   ((UrlSequenceNumber)(countC) & 0x07F)
#define URL_AM_COUNT_C(hfn,sn)          (((UCOUNT_C)(hfn) & 0x000FFFFF) <<     \
                                            URL_AM_SN_BITS) | (sn)
#define URL_AM_GET_COUNT_C_HFN(countC)  ((UrlHyperFrameNumber)(countC) >>      \
                                            URL_AM_SN_BITS)
#define URL_AM_GET_COUNT_C_SN(countC)   ((UrlSequenceNumber)(countC) & 0xFFF)

/* Mask defining which COUNT-C bits are significant to URRC, so that any COUNT-C
 * change which alters these bits should be informed to URRC in a CrlcCountCInd
 */
#define URL_COUNT_C_IND_MASK            0xFFFFF000

/* Definitions of bits within the UrlDataSegmentType octet :
 *       MSB                                                                                                         LSB
 *     |-----------------------------------------------------------------------------------------------------------------|
 *     | 12-15  |     11     |    10    |  9   |   8    |   7   |    6     |  5  |   4   |   3   |   2   |   1   |   0   |
 *     |-----------------------------------------------------------------------------------------------------------------|
 *     | Unused | Special HE | Full+oct | Full | Middle | LI 15 | Reserved | Pad |Status |Length | Short | Exact | Start |
 *
 *  where :
 *
 *      Start    : START LI present flag
 *      Exact    : EXACT LI present flag
 *      Short    : ONE SHORT LI present flag
 *      Length   : Length LI present flag
 *      Status   : Piggyback status present flag
 *      Pad      : Pad LI present flag
 *      Reserved : Reserved
 *      LI 15    : 0 =  7 bit LI's, 1 = 15 bit LI's
 *      Middle   : Middle SDU PDU
 *      Full     : PDU is whole SDU 
 *      Full+oct : PDU is whole SDU plus one octet
 *      Special HE: Special header extension indicates that the final SDU segment
 *                  of PDU was exactly filled with the last segment of the SDU
 */
#define URL_SEG_LI_START                0x0001
#define URL_SEG_LI_EXACT                0x0002
#define URL_SEG_LI_ONE_SHORT            0x0004
#define URL_SEG_LI_LENGTH               0x0008
#define URL_SEG_LI_PIGGYBACK_STATUS     0x0010
#define URL_SEG_LI_PAD                  0x0020
#define URL_SEG_LI_RESERVED             0x0040
#define URL_SEG_LI_7                    0x0000
#define URL_SEG_LI_15                   0x0080
#define URL_SEG_LI_MIDDLE               0x0100
#define URL_SEG_LI_FULL_SDU             0x0200
#define URL_SEG_LI_FULL_SDU_PLUS_OCT    0x0400

/* The final SDU segment of PDU was exactly filled with the last segment of
 * the SDU, no concatenation in this PDU and specialHeValue is configured. */
#define URL_SEG_SPECIAL_HEADER_EXT      0x0800

/* Special values of UrlDataSegmentType */
#define URL_SEG_TYPE_UNKNOWN            0xFFFF    /* PDU not parsed yet */
#define URL_SEG_TYPE_ERROR                                                     \
    (URL_SEG_LI_PAD | URL_SEG_LI_PIGGYBACK_STATUS | URL_SEG_LI_ONE_SHORT |     \
        URL_SEG_LI_EXACT | URL_SEG_LI_START)

#define URL_BITS_TO_OCTETS(bits)        ((bits) >> 3)

#if defined (ENABLE_UPLANE_STATISTICS)
/* Values for throughput statistics */
#define BITS_PER_128INT8                (BITS_PER_INT8 * 128)
#define BITS_PER_256INT8                (BITS_PER_INT8 * 256)
#define BITS_PER_512INT8                (BITS_PER_INT8 * 512)
#define BITS_PER_1024INT8               (BITS_PER_INT8 * 1024)
#endif /* ENABLE_UPLANE_STATISTICS */

#if defined (UPGRADE_3G_EDCH)
/* Maximum traffic bits reported to MAC / MACE */
#define URL_MAX_TX_BITS                 UPS_EDCH_MAX_TB_BITS
/* Maximum number of PDUs that can be transmitted in one EDCH TTI
 * assuming 128 bits is the smaller RLC PDU size */
#define URL_MAX_EDCH_PDUS_PER_TTI       (UPS_EDCH_MAX_TB_BITS / 128)
#else
#define URL_MAX_EDCH_PDUS_PER_TTI       0
#define URL_MAX_TX_BITS                 UPS_MAX_UL_TB_BITS
#endif /* UPGRADE_3G_EDCH */

#if defined (UPGRADE_3G_HSDPA)
#define URL_MAX_HSDPA_PDUS_PER_TTI      UPS_MAX_MACD_PDUS_IN_TTI
#else
#define URL_MAX_HSDPA_PDUS_PER_TTI      0
#endif /* UPGRADE_3G_HSDPA */

/***************************************************************************
 * Types
 ***************************************************************************/

typedef Int16                           UrlcMessageUnitIdentifier;
typedef Int16                           UrlSequenceNumber;
typedef Int16                           UrlLengthIndicator;
typedef Int32                           UrlHyperFrameNumber;
typedef Int16                           UrlDataSegmentType;     /* See above */

/* Loopback information */
typedef struct UrlLoopbackInfoTag
{
    Int8                        numberOfSduSizes;           /* 1-MAX_UL_TF */
    Int16                       sduBits [UPS_MAX_UL_TF];/* 0-65535 */
} UrlLoopbackInfo;

/* Note that to avoid comparison problems, the SN and HFN of the first enty must
 * track the bottom of the window VT(A)/VR(R)
 */
typedef struct UrlCipherConfigTag
{
    KeySeqId                    ksi;        /* Cipher key identity :
                                             *   Bits 0-3 : 0-6 = KSI
                                             *                7 = UPS_NO_KEY_SET
                                             *   Bit    4 : 0 = CS, 1 = PS
                                             *
                                             *   0xFF = UPS_INVALID_KSI
                                             */
    UCipheringAlgorithm_r7      algorithm;  /* Algorithm UEA0, UEA1 or UEA2 */
    UrlSequenceNumber           sn;         /* SN when key becomes active */
    UrlHyperFrameNumber         hfn;        /* HFN that starts at SN or
                                             * URL_HFN_UNDEFINED
                                             */
    UrlHyperFrameNumber         hfn_old;        /*Fixed CQ00074134 20141028*/
} UrlCipherConfig;

/* Type for an array of cipher configurations */
typedef UrlCipherConfig     UrlCipherConfigList [URL_MAX_CIPHER_CONFIGS];

/* Type for a pointer to an array of cipher configurations */
typedef UrlCipherConfigList (* UrlCipherConfigList_p);

/* Structure to store the COUNT-C and KSI of the youngest PDU */
typedef struct UrlCountcInfoTag
{
    UCOUNT_C                    countC; /* COUNT-C */
    KeySeqId                    ksi;    /* Cipher key identity */
} UrlCountcInfo;

/* As the maximum number of LIs per PDU is not specified, this structure allows
 * optimisation of DL data PDU parsing, by avoiding the need to extract the LIs
 * twice (once to count them and again to process them) without a prohibitively
 * large array; once the initial array is full, another linked array can be
 * allocated ...
 */
typedef struct UrlLengthIndicatorListTag
{
    UrlLengthIndicator                  li [URL_AM_SUFI_MAX_LIST_LENGTH];
    struct UrlLengthIndicatorListTag  * next_p;
} UrlLengthIndicatorList;

#if defined (ENABLE_UPLANE_STATISTICS)
/* SDU size counters for throughput statistics
 */
typedef struct UrlSduSizeCountersTag
{
    /* sum of SDU sizes (bytes) this value must be stored in bytes to avoid
     * wrapping
     */
    Int32   sumSize;
    Int32   minSduBits;             /* smallest SDU size (bits) */
    Int32   maxSduBits;             /* largest SDU size (bits) */
    Int32   numSdusSize0000To0127;  /* num. SDUs with 0<=size<128 bytes */
    Int32   numSdusSize0128To0255;  /* num. SDUs with 128<=size<256 bytes */
    Int32   numSdusSize0256To0511;  /* num. SDUs with 256<=size<512 bytes */
    Int32   numSdusSize0512To1023;  /* num. SDUs with 512<=size<1024 bytes */
    Int32   numSdusSize1024OrMore;  /* num. SDUs with 1024<=size bytes */
} UrlSduSizeCounters;

/* PDU discard counters for throughput statistics
 */
typedef struct UrlPduDiscardCountersTag
{
    Int32       numDuplicatedPdus;      /* num. duplicated PDUs */
    Int32       numInconsistentPdus;    /* num. parse error PDUs */
    Int32       numOtherDiscardedPdus;  /* num. other error PDUs */
} UrlPduDiscardCounters;

/* Status PDU statistics
 */
typedef struct UrlStatusPduStatisticsTag
{
    Int32   numStatusPdus;   /* num. status PDUs */
    Int32   listSufiCount;   /* num. LIST SUFIs */
    Int32   bitmapSufiCount; /* num. BITMAP SUFIs */
    Int32   rlistSufiCount;  /* num. RLIST SUFIs */
    Int32   mrwSufiCount;    /* num. MRW SUFIs */
    Int32   mrwAckSufiCount; /* num. MRW-ACK SUFIs */
    Int32   nackdSnCount;    /* num. NACKd SNs */
} UrlStatusPduStatistics;

/* PDU counters for throughput statistics
 */
typedef struct UrlPduStatisticsTag
{
    UrlStatusPduStatistics  statusPdus;
    Int32                   numDataPdus;
    Int32                   numResetPdus;
    Int32                   numResetAckPdus;
    Int32                   numStatusPaddingPdus;
    Int32                   maxLis;            /* max LIs per PDU */
    Int32                   pollReqCount;
} UrlPduStatistics;

/* uplink statistics values
 */
typedef struct UrlUplinkStatisticsTag
{
    KernelTicks             startTick;          /* time since establishment */
    UrlSduSizeCounters      sduSize;
    UrlPduStatistics        pduStats;
    Int32                   numDiscardedSdus;   /* discarded SDUs */
} UrlUplinkStatistics;

/* downlink statistics values
 */
typedef struct UrlDownlinkStatisticsTag
{
    KernelTicks             startTick;    /* time since establishment */
    UrlSduSizeCounters      sduSize;
    UrlPduStatistics        pduStats;
    UrlPduDiscardCounters   discardedPdus; /* only needed for downlink */
    Int32                   reestablishmentCount; /* reestablishments */
} UrlDownlinkStatistics;
#endif /* ENABLE_UPLANE_STATISTICS */

#if defined (ENABLE_UL_THROUGHPUT_IND)
typedef struct UrlUlTpStatisticsTag
{
    KernelTicks             lastReportTick;     /* time since last report */
    Int32                   totalDataSent;
    Int32                   lastReportedUlTp;
    Int32                   lastReportedPendTxBytes;
} UrlUlTpStatistics;
#endif /* ENABLE_UL_THROUGHPUT_IND */


#if defined (URLC_TASK_FLOW_PROTECT)
typedef struct UrlTaskFlowSemaDebugRecordTag
{
    Int32                   lastStartTime;
    Int32                   lastEndTime;
    Int32                   maxDeltaTime;
} UrlTaskFlowSemaDebugRecord;
#endif


/***************************************************************************
 * Typed Constants
 ***************************************************************************/

/* Bearer release cause */
typedef enum UrlBearerReleaseCauseTag
{
    URL_BEARER_RELEASE                      = 0,
    URL_BEARER_RE_ESTABLISHMENT             = 1
} UrlBearerReleaseCause;

/* Function usage */
typedef enum UrlUsageTag
{
    URL_USAGE_FOREGROUND                    = 0,    /* Processing a signal */
    URL_USAGE_BACKGROUND                    = 1     /* Low priority background
                                                     * operation
                                                     */
} UrlUsage;

#if defined (URLC_TASK_FLOW_PROTECT)
typedef enum UrlSemaCallIdTag
{
    URL_SEMA_SERVICE_LOW_PRIORITY_OPERATIONS    = 0,
    URL_SEMA_PASS_SIGNAL_TO_TX_ENTITY           = 1,
    URL_SEMA_DELIVER_REASSAMBLED_DL_SDU         = 2,
    URL_SEMA_BG_BUF_GROWTH                      = 3,
    URL_SEMA_UMAC_UPDATE_TRAFFIC_IND            = 4,
    URL_SEMA_UMAC_TRAFFIC_IND_UM                = 5,
    URL_SEMA_UMAC_TRAFFIC_IND_AM                = 6,
    URL_SEMA_UMAC_TRAFFIC_IND_COUNTC_CHANGE     = 7,
    URL_SEMA_SDU_BUF_SHRINK                     = 8,
    URL_SEMA_DL_PDU_LIST_INFO_IND_FN            = 9,
    URL_SEMA_UMAC_DATA_IND_UM                   = 10,
    URL_SEMA_UMAC_DATA_IND_AM                   = 11,
    URL_SEMA_UMAC_DATA_IND_SATISFY_AM_POLLS     = 12,
    URL_SEMA_UMAC_TX_STATUS_IND                 = 13,
    URL_SEMA_UMAC_UL_CONFIG_CHANGE_IND          = 14,
    URL_SEMA_UMAC_DL_CONFIG_CHANGE_IND          = 15,
    URL_SEMA_HS_PDU_LIST_INFO_IND_FN            = 16,
    URL_SEMA_UMAC_HS_DATA_IND_UM                = 17,
    URL_SEMA_UMAC_HS_DATA_IND_AM                = 18, 
    URL_SEMA_UMAC_HS_REQUEST_STATUS_IND         = 19, 
    URL_SEMA_UM_DATA_REQ                        = 20,
    URL_SEMA_AM_DATA_REQ                        = 21,
    URL_SEMA_CRLC_CONFIG_REQ                    = 22,
    URL_SEMA_CRLC_STOP_REQ                      = 23,
    URL_SEMA_CRLC_HALT_REQ                      = 24,
    URL_SEMA_CRLC_CONTINUE_REQ                  = 25,
    URL_SEMA_CRLC_SUSPEND_REQ                   = 26,
    URL_SEMA_CRLC_RESUME_REQ                    = 27,
    URL_SEMA_CRLC_ABORT_CIPHER_CONFIG_REQ       = 28,
    URL_SEMA_CRLC_RELEASE_REQ                   = 29,
    URL_SEMA_CRLC_CLOSE_TEST_LOOP_MODE_REQ      = 30,
    URL_SEMA_CRLC_OPEN_TEST_LOOP_MODE_REQ       = 31,
    URL_SEMA_CRLC_TEST_MODE_REQ                 = 32,
    URL_SEMA_TIMER_EXPIRY                       = 33,
    URL_SEMA_UT_MEM_ABOVE_HWM_IND               = 34,
    URL_SEMA_UT_MEM_ABOVE_VHWM_IND              = 35,
    URL_SEMA_UT_MEM_BELOW_LWM_IND               = 36,
    URL_SEMA_CRLC_DEACTIVATE_REQ                = 37,
    URL_SEMA_EDCH_UPDATE_TRAFFIC_IND_FN         = 38,
    URL_SEMA_EDCH_TX_STATUS_IND_FN              = 39,
    URL_SEMA_CHECK_TX_SDUS_AMOUNT_WM            = 40,
    URL_SEMA_EHS_PDU_LIST_INFO_IND_FN           = 41,
    URL_SEMA_UMAC_EHS_DATA_IND_TM               = 42,
    URL_SEMA_UMAC_EHS_DATA_IND_UM               = 43,
    URL_SEMA_UMAC_EHS_DATA_IND_AM               = 44,
    URL_SEMA_EDCH_TRAFFIC_IND_FN      		    = 45,
    URL_SEMA_PS_DATA_RSP						= 46,
    URL_SEMA_PROCESS_ACK_NACK                   = 47,
    URL_SEMA_PROCESS_ACK_SUFI                   = 48,
    URL_SEMA_PROCESS_BITMAP_SUFI                = 49,
    URL_SEMA_PROCESS_LIST_SUFI                  = 50,
    URL_SEMA_PHY_FRAME_IND                      = 51,
    /* ensure this is last */
    URL_SEMA_NUM_CALLS
} UrlSemaCallId;
#endif


#endif /* URLTYPES_H */
/* END OF UrlTypes.H */

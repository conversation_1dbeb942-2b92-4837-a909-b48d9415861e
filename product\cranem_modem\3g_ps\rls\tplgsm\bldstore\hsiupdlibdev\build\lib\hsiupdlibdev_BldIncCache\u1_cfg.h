/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/l1/3g.typ/api/cfg/u1_cfg.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2007/03/30 13:53:25 $
 *************************************************************************/
/** \file
 * 3G L1 Configuration definitions for the UPHY Interface
 *************************************************************************/

#if !defined (U1_CFG_H)
#define       U1_CFG_H

#include <system.h>
/***************************************************************************
* Nested Include Files
***************************************************************************/

/***************************************************************************
*   Manifest Constants
***************************************************************************/

/** Defines GSM L1 physical block size in bytes. */
#define CPHY_GSM_L1_PHYSICAL_BLOCK_SIZE       23

#if defined   (UPS_CFG_DL_DATA_CLASS_32KBPS)
/** Defines the maximum sum of number of bits of all transport blocks being
 * received at an arbitrary time instant.
 */
# define UPS_MAX_DL_TB_BITS                     640
/** Defines the maximum total number of transport blocks received within TTIs
 * that end at the same time.
 */
# define UPS_MAX_DL_NO_OF_TB_PER_TTI            8
/** Defines the maximum number of simultaneous transport channels available on
 * the downlink; and it does not contain the BCH of the neighbour cell.
 */
# define UPS_MAX_DL_NO_OF_TRCH                  8
/** Defines the maximum number of transport format combinations available on
 * the downlink.
 */
# define UPS_MAX_DL_TFC                         32
/** Defines the maximum number of transport formats available on the downlink.
 */
# define UPS_MAX_DL_TF                          32
/** Defines the maximum number of AM entities that could exist. */
# define UPS_MAX_AM_ENTITIES                    4

#elif defined (UPS_CFG_DL_DATA_CLASS_64KBPS)
/** Defines the maximum sum of number of bits of all transport blocks being
 * received at an arbitrary time instant.
 */
# define UPS_MAX_DL_TB_BITS                     6400//3840
/** Defines the maximum total number of transport blocks received within TTIs
 * that end at the same time.
 */
# define UPS_MAX_DL_NO_OF_TB_PER_TTI            8
/** Defines the maximum number of simultaneous transport channels available on
 * the downlink; and it does not contain the BCH of the neighbour cell.
 */
# define UPS_MAX_DL_NO_OF_TRCH                  8
/** Defines the maximum number of transport format combinations available on
 * the downlink.
 */
# define UPS_MAX_DL_TFC                         48
/** Defines the maximum number of transport formats available on the downlink.
 */
# define UPS_MAX_DL_TF                          64
/** Defines the maximum number of AM entities that could exist. */
# define UPS_MAX_AM_ENTITIES                    4

#elif defined (UPS_CFG_DL_DATA_CLASS_128KBPS)
/** Defines the maximum sum of number of bits of all transport blocks being
 * received at an arbitrary time instant.
 */
# define UPS_MAX_DL_TB_BITS                     3840
/** Defines the maximum total number of transport blocks received within TTIs
 * that end at the same time.
 */
# define UPS_MAX_DL_NO_OF_TB_PER_TTI            16
/** Defines the maximum number of simultaneous transport channels available on
 * the downlink; and it does not contain the BCH of the neighbour cell.
 */
# define UPS_MAX_DL_NO_OF_TRCH                  8
/** Defines the maximum number of transport format combinations available on
 * the downlink.
 */
# define UPS_MAX_DL_TFC                         96
/** Defines the maximum number of transport formats available on the downlink.
 */
# define UPS_MAX_DL_TF                          64
/** Defines the maximum number of AM entities that could exist. */
# define UPS_MAX_AM_ENTITIES                    5

#else /* 384KBPS */
/** Defines the maximum sum of number of bits of all transport
 * blocks being received at an arbitrary time instant.
 */
/************/
/*# define UPS_MAX_DL_TB_BITS					  6400*/
# define UPS_MAX_DL_TB_BITS                     8064
/************/
/** Defines the maximum total number of transport blocks received
 * within TTIs that end at the same time.
 */
# define UPS_MAX_DL_NO_OF_TB_PER_TTI            32
/** Defines the maximum number of simultaneous transport channels available on
 * the downlink; and it does not contain the BCH of the neighbour cell.
 */
# define UPS_MAX_DL_NO_OF_TRCH                  8
/** Defines the maximum number of transport format combinations available on
 * the downlink.
 */
# define UPS_MAX_DL_TFC                         128
/** Defines the maximum number of transport formats available on the downlink.
 */
# define UPS_MAX_DL_TF                          64
/** Defines the maximum number of AM entities that could exist. */
# define UPS_MAX_AM_ENTITIES                    6
#endif /* UPS_CFG_DL_DATA_CLASS_X */

/* Definition of the number of toggle buffers for DL PDU, used for allowing MAC handling delays.
 * Important Note: This value should be a power of 2. Otherwise, the 
 * HAW_DL_INCREMENT_TOGGLE_INDEX should be changed, because it uses 
 * (UPS_MAX_OVERLAPPING_PHY_DATA_IND-1) for the modulus function */
#define UPS_MAX_OVERLAPPING_PHY_DATA_IND	  8//	4

/* Defines the maximum overlapping UMAC_PDU_LIST_IND signals that can be processed by L2.
 * This should be exactly one more then UPS_MAX_OVERLAPPING_PHY_DATA_IND, as RLC needs
 * The resources of the transaction until it finishes the processing of the UMAC_DATA_IND. */
#define UPS_MAX_OVERLAPPING_UMAC_PDU_LIST_IND	(UPS_MAX_OVERLAPPING_PHY_DATA_IND+1)

#if defined   (UPS_CFG_UL_DATA_CLASS_32KBPS)
/** Defines the maximum number of uplink physical channels. */
# define UPS_MAX_UL_PHY_CH                      1
/** Defines the minimum spreading factor. */
# define UPS_MIN_SPREADING_FACTOR               32
/** Defines the maximum sum of number of bits of all transport
 * blocks being transmitted at an arbitrary time instant.
 */
# define UPS_MAX_UL_TB_BITS                     640
/** Defines the maximum total number of transport blocks transmitted within
 * TTIs that start at the same time in the uplink.
 */
# define UPS_MAX_UL_NO_OF_TB_PER_TTI            4
/** Defines the maximum number of simultaneous transport channels on the
 * uplink.
 */
# define UPS_MAX_UL_NO_OF_TRCH                  4
/** Defines the maximum number of tranport format combinations available on the
 * uplink.
 */
# define UPS_MAX_UL_TFC                         16
/** Defines the maximum number of tranport formats available on the uplink. */
# define UPS_MAX_UL_TF                          32

#elif defined (UPS_CFG_UL_DATA_CLASS_64KBPS)
# if defined (UPS_CFG_DL_DATA_CLASS_32KBPS)
# error Downlink data class must be >= Uplink data class
# endif /* UPS_CFG_DL_DATA_CLASS_32KBPS */
/** Defines the maximum number of uplink physical channels. */
# define UPS_MAX_UL_PHY_CH                      1
/** Defines the minimum spreading factor. */
# define UPS_MIN_SPREADING_FACTOR               16
/** Defines the maximum sum of number of bits of all transport blocks being
 * transmitted at an arbitrary time instant.
 */
# define UPS_MAX_UL_TB_BITS                     3840
/** Defines the maximum total number of transport blocks transmitted within
 * TTIs that start at the same time in the uplink.
 */
# define UPS_MAX_UL_NO_OF_TB_PER_TTI            8
/** Defines the maximum number of simultaneous transport channels on the uplink.
 */
# define UPS_MAX_UL_NO_OF_TRCH                  8
/** Defines the maximum number of tranport format combinations available on the
 * uplink.
 */
# define UPS_MAX_UL_TFC                         32
/** Defines the maximum number of tranport formats available on the uplink. */
# define UPS_MAX_UL_TF                          32

#elif defined (UPS_CFG_UL_DATA_CLASS_128KBPS)
# if defined (UPS_CFG_DL_DATA_CLASS_32KBPS) || defined (UPS_CFG_DL_DATA_CLASS_64KBPS)
# error Downlink data class must be >= Uplink data class
# endif /* UPS_CFG_DL_DATA_CLASS_32KBPS || UPS_CFG_DL_DATA_CLASS_64KBPS */
/** Defines the maximum number of uplink physical channels. */
# define UPS_MAX_UL_PHY_CH                      1
/** Defines the minimum spreading factor. */
# define UPS_MIN_SPREADING_FACTOR               8
/** Defines the maximum sum of number of bits of all transport
 * blocks being transmitted at an arbitrary time instant.
 */
# define UPS_MAX_UL_TB_BITS                     3840
/** Defines the maximum total number of transport blocks transmitted within
 * TTIs that start at the same time in the uplink.
 */
# define UPS_MAX_UL_NO_OF_TB_PER_TTI            8
/** Defines the maximum number of simultaneous transport channels on the
 * uplink.
 */
# define UPS_MAX_UL_NO_OF_TRCH                  8
/** Defines the maximum number of tranport format combinations available on the
 * uplink.
 */
# define UPS_MAX_UL_TFC                         32
/** Defines the maximum number of tranport formats available on the uplink. */
# define UPS_MAX_UL_TF                          32

#else /* 384KBPS */
# if defined (UPS_CFG_DL_DATA_CLASS_32KBPS) || defined (UPS_CFG_DL_DATA_CLASS_64KBPS) || \
     defined (UPS_CFG_DL_DATA_CLASS_128KBPS)
# error Downlink data class must be >= Uplink data class
# endif /* UPS_CFG_DL_DATA_CLASS_X */
/** Defines the maximum number of uplink physical channels. */
# define UPS_MAX_UL_PHY_CH                      1
/** Defines the minimum spreading factor. */
# define UPS_MIN_SPREADING_FACTOR               4
/** Defines the maximum sum of number of bits of all transport blocks being
 * transmitted at an arbitrary time instant.
 */
# define UPS_MAX_UL_TB_BITS                     6400
/** Defines the maximum total number of transport blocks transmitted within
 * TTIs that start at the same time in the uplink.
 */
# define UPS_MAX_UL_NO_OF_TB_PER_TTI            16
/** Defines the maximum number of simultaneous transport channels on the uplink.
 */
# define UPS_MAX_UL_NO_OF_TRCH                  8
/** Defines the maximum number of tranport format combinations available on the
 * uplink.
 */
# define UPS_MAX_UL_TFC                         64
/** Defines the maximum number of tranport formats available on the uplink. */
# define UPS_MAX_UL_TF                          32
#endif /* UPS_CFG_UL_DATA_CLASS_X */

#if !defined (UPS_TRANSPORT_BLOCK_ALIGNMENT_IN_OCTETS)
/** Defines how the start of each transport block must be aligned.
 * This has the following options:
 *     - 0 = No alignment
 *     - 1 = Byte aligned
 *     - 2 = Word aligned
 *     - 4 = Longword aligned
 */
#define UPS_TRANSPORT_BLOCK_ALIGNMENT_IN_OCTETS 2
#endif /* UPS_TRANSPORT_BLOCK_ALIGNMENT_IN_OCTETS */

#if !defined (UPS_MAX_RADIO_LINKS)
/** Defines the maximum number of radio links that can be supported in L1. */
# define UPS_MAX_RADIO_LINKS                    6  /* Range of 6 to 8 */
#endif /* UPS_MAX_RADIO_LINKS */

#define UPS_ONE_RADIO_LINK                   1

/* Maximum number of intra-freq cells the physical layer can measure */
#if !defined (UPS_MAX_MEASURED_INTRA_FREQ_CELLS)
#  if defined (UPGRADE_HERMON_PLATFORM)
    /* JWW:2004-10-21 - changes to allow PS to match Intel L1 */
/** Defines the maximum number of intra-frequency cells that can be measured by
 * L1.
 */
# define UPS_MAX_MEASURED_INTRA_FREQ_CELLS      32
#  else
/** Defines the maximum number of intra-frequency cells that can be measured by
 * L1.
 */
# define UPS_MAX_MEASURED_INTRA_FREQ_CELLS      12
#  endif
#endif /* UPS_MAX_MEASURED_INTRA_FREQ_CELLS */

/** Defines the maximum CRC size in octets.
 * 24 comes from the maximum of UCRC_Size enum in rrcXXX_asn.h */
#define UPS_MAX_CRC_SIZE_IN_OCTETS          BITS_TO_INT8S(24)

/** Defines the number of slots per frame, which is 15. */
#define UPS_SLOTS_PER_FRAME             15
#define MAX_STORED_CELL_PARAMETERS_ID         32   /*modify for one image*/
#define MAX_TDD_INTER_FREQUENCY_CELLS       32     /*modify for one image*/

/** Defines the maximum stored scrambling codes. */
#define MAX_STORED_SCRAMBLING_CODES         32

/** Defines the size of an array that holds all the reception
 * periods in a CTCH-BS (specified in 33.102).
 */
#define CBS_RECEPTION_PERIOD_OFFSET_ARRAY_SIZE      256

/** Defines the maximum number of FDD inter-frequency cells. */
#define MAX_FDD_INTER_FREQUENCY_CELLS       8

/** Defines the BCH transport block size in bits. */
#define UPS_BCH_TB_SIZE_BITS      246

/** Defines the BCH transport block size in octets. */
#define UPS_BCH_TB_SIZE_OCTETS    BITS_TO_INT8S(UPS_BCH_TB_SIZE_BITS)


#if defined(UPGRADE_3G_HSDPA)

/** Defines the maximum number of MAC-D PDUs permissible in a 2ms TTI. */
#  define UPS_MAX_MACD_PDUS_IN_TTI              70
   /* FDD must have at least 50kB buffer for HSDPA */

/** Define the maximum number of mac-hs SDUs allowed per 1 UmacEhsDataInd . */
# define UMAHS_MAX_SDUS_PER_DATA_IND			UPS_MAX_MACD_PDUS_IN_TTI

/** Define the maximum number of mac-ehs reordering PDUs permissible in a 2ms TTI. */
# define UMAEHS_MAX_REORDERING_PDU_PER_TTI		(3)  // Accodring to 25.321 Section 9.1.4

/** Define the maximum number of mac-ehs SDUs permissible in a 2ms TTI. */
# define  UMAEHS_MAX_REORDERING_SDU_PER_TTI		(26) // Accodring to 25.321 Section 9.1.4

/** Define the maximum number of mac-ehs reordering PDUs allowed per 1 UmacEhsDataInd . */
# define UMAEHS_MAX_REORDERNG_PDU_PER_DATA_IND	(UMAEHS_MAX_REORDERING_PDU_PER_TTI)

/** Define the maximum number of mac-ehs SDUs allowed per 1 UmacEhsDataInd . */
# define UMAEHS_MAX_SDUS_PER_DATA_IND    		(UMAEHS_MAX_REORDERNG_PDU_PER_DATA_IND*UMAEHS_MAX_REORDERING_SDU_PER_TTI)


#  if defined (UPS_CFG_HS_DSCH_CATEGORY_11)
/** Defines the maximum number of channelisation codes L1 is capable of
 * receiving simultaneously on HS-DSCH.
 */
#   define UPS_HS_DSCH_MAX_RX_CODES               5
/** Defines the minimum HS-DSCH TTI reception distance supported by L1. 1 means
 * continuous HS-DSCH reception (every TTI). 2 means L1 does not support
 * continuous HS-DSCH reception: there must be a gap of 1 TTI between each TTI
 *
where HS-DSCH data is received.
 */
#   define UPS_HS_DSCH_MIN_INTER_TTI              2
/** Defines the maximum number of bits which can be received on HS-DSCH in each
 * 2ms TTI.
 */
#   define UPS_HS_DSCH_MAX_BITS_PER_TTI           3630
/** Defines the maximum number of soft memory bits available on HS-DSCH to all
 * the HARQ processes.
 */
#   define UPS_HS_DSCH_TOTAL_SOFT_BITS            1440


#  elif defined (UPS_CFG_HS_DSCH_CATEGORY_12)
/** Defines the maximum number of channelisation codes L1 is capable of
 * receiving simultaneously on HS-DSCH.
 */
#   define UPS_HS_DSCH_MAX_RX_CODES               5
/** Defines the minimum HS-DSCH TTI reception distance supported by L1. 1 means
 * continuous HS-DSCH reception (every TTI). 2 means L1 does not support
 * continuous HS-DSCH reception: there must be a gap of 1 TTI between each TTI
 * where HS-DSCH data is received.
 */
#   define UPS_HS_DSCH_MIN_INTER_TTI              1
/** Defines the maximum number of bits which can be received on HS-DSCH in each
 * 2ms TTI.
 */
#   define UPS_HS_DSCH_MAX_BITS_PER_TTI           3630
/** Defines the maximum number of soft memory bits available on HS-DSCH to all
 * the HARQ processes.
 */
#   define UPS_HS_DSCH_TOTAL_SOFT_BITS            2880


#  elif defined (UPS_CFG_HS_DSCH_CATEGORY_1)
/** Defines the maximum number of channelisation codes L1 is capable of
 * receiving simultaneously on HS-DSCH.
 */
#   define UPS_HS_DSCH_MAX_RX_CODES               5
/** Defines the minimum HS-DSCH TTI reception distance supported by L1. 1 means
 * continuous HS-DSCH reception (every TTI). 2 means L1 does not support
 * continuous HS-DSCH reception: there must be a gap of 1 TTI between each TTI
 * where HS-DSCH data is received.
 */
#   define UPS_HS_DSCH_MIN_INTER_TTI              3
/** Defines the maximum number of bits which can be received on HS-DSCH in each
 * 2ms TTI.
 */
#   define UPS_HS_DSCH_MAX_BITS_PER_TTI           7298
/** Defines the maximum number of soft memory bits available on HS-DSCH to all
 * the HARQ processes.
 */
#   define UPS_HS_DSCH_TOTAL_SOFT_BITS            19200


#  elif defined (UPS_CFG_HS_DSCH_CATEGORY_2)
/** Defines the maximum number of channelisation codes L1 is capable of
 * receiving simultaneously on HS-DSCH.
 */
#   define UPS_HS_DSCH_MAX_RX_CODES               5
/** Defines the minimum HS-DSCH TTI reception distance supported by L1. 1 means
 * continuous HS-DSCH reception (every TTI). 2 means L1 does not support
 * continuous HS-DSCH reception: there must be a gap of 1 TTI between each TTI
 * where HS-DSCH data is received.
 */
#   define UPS_HS_DSCH_MIN_INTER_TTI              3
/** Defines the maximum number of bits which can be received on HS-DSCH in each
 * 2ms TTI.
 */
#   define UPS_HS_DSCH_MAX_BITS_PER_TTI           7298
/** Defines the maximum number of soft memory bits available on HS-DSCH to all
 * the HARQ processes.
 */
#   define UPS_HS_DSCH_TOTAL_SOFT_BITS            28800


#  elif defined (UPS_CFG_HS_DSCH_CATEGORY_3)
/** Defines the maximum number of channelisation codes L1 is capable of
 * receiving simultaneously on HS-DSCH.
 */
#   define UPS_HS_DSCH_MAX_RX_CODES               5
/** Defines the minimum HS-DSCH TTI reception distance supported by L1. 1 means
 * continuous HS-DSCH reception (every TTI). 2 means L1 does not support
 * continuous HS-DSCH reception: there must be a gap of 1 TTI between each TTI
 * where HS-DSCH data is received.
 */
#   define UPS_HS_DSCH_MIN_INTER_TTI              2
/** Defines the maximum number of bits which can be received on HS-DSCH in each
 * 2ms TTI.
 */
#   define UPS_HS_DSCH_MAX_BITS_PER_TTI           7298
/** Defines the maximum number of soft memory bits available on HS-DSCH to all
 * the HARQ processes.
 */
#   define UPS_HS_DSCH_TOTAL_SOFT_BITS            28800


#  elif defined (UPS_CFG_HS_DSCH_CATEGORY_4)
/** Defines the maximum number of channelisation codes L1 is capable of
 * receiving simultaneously on HS-DSCH.
 */
#   define UPS_HS_DSCH_MAX_RX_CODES               5
/** Defines the minimum HS-DSCH TTI reception distance supported by L1. 1 means
 * continuous HS-DSCH reception (every TTI). 2 means L1 does not support
 * continuous HS-DSCH reception: there must be a gap of 1 TTI between each TTI
 * where HS-DSCH data is received.
 */
#   define UPS_HS_DSCH_MIN_INTER_TTI              2
/** Defines the maximum number of bits which can be received on HS-DSCH in each
 * 2ms TTI.
 */
#   define UPS_HS_DSCH_MAX_BITS_PER_TTI           7298
/** Defines the maximum number of soft memory bits available on HS-DSCH to all
 * the HARQ processes.
 */
#   define UPS_HS_DSCH_TOTAL_SOFT_BITS            38400


#  elif defined (UPS_CFG_HS_DSCH_CATEGORY_5)
/** Defines the maximum number of channelisation codes L1 is capable of
 * receiving simultaneously on HS-DSCH.
 */
#   define UPS_HS_DSCH_MAX_RX_CODES               5
/** Defines the minimum HS-DSCH TTI reception distance supported by L1. 1 means
 * continuous HS-DSCH reception (every TTI). 2 means L1 does not support
 * continuous HS-DSCH reception: there must be a gap of 1 TTI between each TTI
 * where HS-DSCH data is received.
 */
#   define UPS_HS_DSCH_MIN_INTER_TTI              1
/** Defines the maximum number of bits which can be received on HS-DSCH in each
 * 2ms TTI.
 */
#   define UPS_HS_DSCH_MAX_BITS_PER_TTI           7298
/** Defines the maximum number of soft memory bits available on HS-DSCH to all
 * the HARQ processes.
 */
#   define UPS_HS_DSCH_TOTAL_SOFT_BITS            57600


#  elif defined (UPS_CFG_HS_DSCH_CATEGORY_6)
/** Defines the maximum number of channelisation codes L1 is capable of
 * receiving simultaneously on HS-DSCH.
 */
#   define UPS_HS_DSCH_MAX_RX_CODES               5
/** Defines the minimum HS-DSCH TTI reception distance supported by L1. 1 means
 * continuous HS-DSCH reception (every TTI). 2 means L1 does not support
 * continuous HS-DSCH reception: there must be a gap of 1 TTI between each TTI
 * where HS-DSCH data is received.
 */
#   define UPS_HS_DSCH_MIN_INTER_TTI              1
/** Defines the maximum number of bits which can be received on HS-DSCH in each
 * 2ms TTI.
 */
#   define UPS_HS_DSCH_MAX_BITS_PER_TTI           7298
/** Defines the maximum number of soft memory bits available on HS-DSCH to all
 * the HARQ processes.
 */
#   define UPS_HS_DSCH_TOTAL_SOFT_BITS            67200


#  elif defined (UPS_CFG_HS_DSCH_CATEGORY_7)
/** Defines the maximum number of channelisation codes L1 is capable of
 * receiving simultaneously on HS-DSCH.
 */
#   define UPS_HS_DSCH_MAX_RX_CODES               10
/** Defines the minimum HS-DSCH TTI reception distance supported by L1. 1 means
 * continuous HS-DSCH reception (every TTI). 2 means L1 does not support
 * continuous HS-DSCH reception: there must be a gap of 1 TTI between each TTI
 * where HS-DSCH data is received.
 */
#   define UPS_HS_DSCH_MIN_INTER_TTI              1
/** Defines the maximum number of bits which can be received on HS-DSCH in each
 * 2ms TTI.
 */
#   define UPS_HS_DSCH_MAX_BITS_PER_TTI           14411
/** Defines the maximum number of soft memory bits available on HS-DSCH to all
 * the HARQ processes.
 */
#   define UPS_HS_DSCH_TOTAL_SOFT_BITS            115200


#  elif defined (UPS_CFG_HS_DSCH_CATEGORY_8)
/** Defines the maximum number of channelisation codes L1 is capable of
 * receiving simultaneously on HS-DSCH.
 */
#   define UPS_HS_DSCH_MAX_RX_CODES               10
/** Defines the minimum HS-DSCH TTI reception distance supported by L1. 1 means
 * continuous HS-DSCH reception (every TTI). 2 means L1 does not support
 * continuous HS-DSCH reception: there must be a gap of 1 TTI between each TTI
 * where HS-DSCH data is received.
 */
#   define UPS_HS_DSCH_MIN_INTER_TTI              1
/** Defines the maximum number of bits which can be received on HS-DSCH in each
 * 2ms TTI.
 */
#   define UPS_HS_DSCH_MAX_BITS_PER_TTI           14411
/** Defines the maximum number of soft memory bits available on HS-DSCH to all
 * the HARQ processes.
 */
#   define UPS_HS_DSCH_TOTAL_SOFT_BITS            134400


#  elif defined (UPS_CFG_HS_DSCH_CATEGORY_9)
/** Defines the maximum number of channelisation codes L1 is capable of
 * receiving simultaneously on HS-DSCH.
 */
#   define UPS_HS_DSCH_MAX_RX_CODES               15
/** Defines the minimum HS-DSCH TTI reception distance supported by L1. 1 means
 * continuous HS-DSCH reception (every TTI). 2 means L1 does not support
 * continuous HS-DSCH reception: there must be a gap of 1 TTI between each TTI
 * where HS-DSCH data is received.
 */
#   define UPS_HS_DSCH_MIN_INTER_TTI              1
/** Defines the maximum number of bits which can be received on HS-DSCH in each
 * 2ms TTI.
 */
#   define UPS_HS_DSCH_MAX_BITS_PER_TTI           20251
/** Defines the maximum number of soft memory bits available on HS-DSCH to all
 * the HARQ processes.
 */
#   define UPS_HS_DSCH_TOTAL_SOFT_BITS            172800


#  elif defined (UPS_CFG_HS_DSCH_CATEGORY_10)
#if defined (UPS_CFG_HS_DSCH_CATEGORY_EXT_10)
/** Defines the maximum number of channelisation codes L1 is capable of
 * receiving simultaneously on HS-DSCH.
 */
#   define UPS_HS_DSCH_MAX_RX_CODES               15
/** Defines the minimum HS-DSCH TTI reception distance supported by L1. 1 means
 * continuous HS-DSCH reception (every TTI). 2 means L1 does not support
 * continuous HS-DSCH reception: there must be a gap of 1 TTI between each TTI
 * where HS-DSCH data is received.
 */
#   define UPS_HS_DSCH_MIN_INTER_TTI              1
/** Defines the maximum number of bits which can be received on HS-DSCH in each
 * 2ms TTI.
 */
#   define UPS_HS_DSCH_MAX_BITS_PER_TTI           27952
/** Defines the maximum number of soft memory bits available on HS-DSCH to all
 * the HARQ processes.
 */
#   define UPS_HS_DSCH_TOTAL_SOFT_BITS            172800

#elif defined (UPS_CFG_HS_DSCH_CATEGORY_EXT_14)
/** Defines the maximum number of channelisation codes L1 is capable of
 * receiving simultaneously on HS-DSCH.
 */
#   define UPS_HS_DSCH_MAX_RX_CODES               15
/** Defines the minimum HS-DSCH TTI reception distance supported by L1. 1 means
 * continuous HS-DSCH reception (every TTI). 2 means L1 does not support
 * continuous HS-DSCH reception: there must be a gap of 1 TTI between each TTI
 * where HS-DSCH data is received.
 */
#   define UPS_HS_DSCH_MIN_INTER_TTI              1
/** Defines the maximum number of bits which can be received on HS-DSCH in each
 * 2ms TTI.
 */
#   define UPS_HS_DSCH_MAX_BITS_PER_TTI           42192
/** Defines the maximum number of soft memory bits available on HS-DSCH to all
 * the HARQ processes.
 */
#   define UPS_HS_DSCH_TOTAL_SOFT_BITS            259200
#else
#   error HS-DSCH category ext is not defined, but HS-DSCH category is 10.
#endif /* UPS_CFG_HS_DSCH_CATEGORY_EXT_XX */


#  else
#   error HS-DSCH category is not defined, but UPGRADE_3G_HSDPA is on.
#  endif /* UPS_CFG_HS_DSCH_CATEGORY_X */
#endif /* UPGRADE_3G_HSDPA */


#if defined (UPGRADE_3G_EDCH)
#if defined (UPS_CFG_EDCH_CATEGORY_1)
/** Defines the maximum number of bits of an E-DCH 
 * transport block transmitted within a 10 ms E-DCH TTI
 */
#define UPS_EDCH_MAX_TB_BITS                     7110

#elif defined (UPS_CFG_EDCH_CATEGORY_2)
/** Defines the maximum number of bits of an E-DCH 
 * transport block transmitted within a 10 ms E-DCH TTI
 */
#define UPS_EDCH_MAX_TB_BITS                     14484

#elif defined (UPS_CFG_EDCH_CATEGORY_3)
/** Defines the maximum number of bits of an E-DCH 
 * transport block transmitted within a 10 ms E-DCH TTI
 */
#define UPS_EDCH_MAX_TB_BITS                     14484

#elif defined (UPS_CFG_EDCH_CATEGORY_4)
/** Defines the maximum number of bits of an E-DCH 
 * transport block transmitted within a 10 ms E-DCH TTI
 */
#define UPS_EDCH_MAX_TB_BITS                     20000

#elif defined (UPS_CFG_EDCH_CATEGORY_5)
/** Defines the maximum number of bits of an E-DCH 
 * transport block transmitted within a 10 ms E-DCH TTI
 */
#define UPS_EDCH_MAX_TB_BITS                     20000

/* CQ00065791 Begin*/
#elif defined (UPS_CFG_EDCH_CATEGORY_6)
/* Make sure no other E-DCH category defined */
#if defined (UPS_CFG_EDCH_CATEGORY_7) || defined (UPS_CFG_EDCH_CATEGORY_8) || defined (UPS_CFG_EDCH_CATEGORY_9)
#error "E-DCH category 6 defined with another E-DCH category"
#endif

/** Defines the maximum number of bits of an E-DCH 
 * transport block transmitted within a 10 ms E-DCH TTI
 */
#define UPS_EDCH_MAX_TB_BITS                     20000

#elif defined (UPS_CFG_EDCH_CATEGORY_7)
/* Make sure no other E-DCH category defined */
#if defined (UPS_CFG_EDCH_CATEGORY_8) || defined (UPS_CFG_EDCH_CATEGORY_9)
#error "E-DCH category 7 defined with another E-DCH category"
#endif

/** Defines the maximum number of bits of an E-DCH transport block 
 */
#define UPS_EDCH_MAX_TB_BITS                     22996

#else

#error "UPGRADE_3G_EDCH but no E-DCH category defined"
/* CQ00065791 */
#endif //UPS_CFG_EDCH_CATEGORY_X
#endif /* UPGRAGDE_3G_EDCH */


#endif  /* U1_CFG_H */

/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/
/**************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/3p.mod/lib/src/xrabmmain.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2013/12/13 15:06:06 $
 **************************************************************************
 * File Description:
 *
 * DLBG main module external functions
 **************************************************************************/

#ifndef XRABMMAIN_H
#define XRABMMAIN_H

/***************************************************************************
 * Nested Include Files
 **************************************************************************/
#include <system.h>
#include <kernel.h>
#include <xrabmdata.h>
#include <dlbgref.h>


/************************** VARIABLES **************************************/
extern Int8 gUlbgWorkSimIndex;
extern XRabmEntity xRabmEntity[SIM_NUM];


/************************** FUNCTION DECLARATIONS **************************/
extern XRabmEntity* GetUlbgSndcpEntity (void);
extern void UlbgInit(void); 
extern void XRabmMain(SignalBuffer *receivedSig);
extern void UlbgRabmInit(XRabmEntity *sn);
extern void UlbgSndcpInit (XRabmEntity *sn);
extern void SndcpMain(XRabmEntity *ulbgSn_p,SignalBuffer *receivedSignal);
extern void UlbgRabmMain(XRabmEntity *sn,SignalBuffer *receivedSignal);

#if defined(SUPPORT_PSM_EDRX)
extern void L2SynchPdpCtx (Int8 simId);	//WQS, 2022-06-22, add for PSM
#endif

#endif

/* END OF FILE */

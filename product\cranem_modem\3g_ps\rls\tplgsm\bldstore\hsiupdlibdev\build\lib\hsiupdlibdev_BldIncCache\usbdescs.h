/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/devices/transports/usb.mod/pub/src/usbdescs.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/24 10:16:13 $
 ***************************************************************************
 * File Description:
 *
 * Constants, data types and macros for USB standard descriptor manipulation.
 **************************************************************************/

#if defined(UPGRADE_USB)

#ifndef USBDESCS_H
#define USBDESCS_H

/*******************************************************************************
 * Manifest Constants
 ******************************************************************************/

/* USB Specification 1.1 coded in BCD */
#define USB_SPECIFICATION_1POINT1 0x0110

/* Maximum packet size for USB Specification 1.1. */
#define USB_MAX_PACKET_SIZE_1POINT1 64

/* Used when a string does not exist. */
#define USB_STRING_NOT_DEFINED_INDEX 0

/* Length of the standard descriptor codes. */
#define USB_DEVICE_DESCRIPTOR_LENGTH               18
#define USB_CONFIGURATION_DESCRIPTOR_LENGTH         9
#define USB_INTERFACE_DESCRIPTOR_LENGTH             9
#define USB_ENDPOINT_DESCRIPTOR_LENGTH              7
#define USB_CS_INTERFACE_FUNCTIONAL_DESCRIPTOR_LENGTH_HDR      5
#define USB_CS_INTERFACE_FUNCTIONAL_DESCRIPTOR_LENGTH_CMGMT    5
#define USB_CS_INTERFACE_FUNCTIONAL_DESCRIPTOR_LENGTH_ABCON    4

/* The bit pattern which sets an endpoint address as IN. */
#define ENDPOINT_DIRECTION_IN  0x80

/* The bit pattern which sets an endpoint address as OUT. */
#define ENDPOINT_DIRECTION_OUT  0x00

/* The bit mask which gets an endpoint direction from its address. */
#define ENDPOINT_DIRECTION_MASK  0x80

/* The bit mask which gets an endpoint number from its address. */
#define ENDPOINT_NUMBER_MASK  0x0F

/*
 * GetStatus(DEVICE) related constants
 */

/* The bit field setting of a GetStatus(DEVICE) result to show bus-powered. */
#define USB_GET_STATUS_DEVICE_BUS_POWERED  0x0000

/* The bit field setting of a GetStatus(DEVICE) result to show self-powered. */
#define USB_GET_STATUS_DEVICE_SELF_POWERED  0x0001

/* The bit field setting of a GetStatus(DEVICE) result to show remote wakeup disabled. */
#define USB_GET_STATUS_DEVICE_REMOTE_WAKEUP_DISABLED  0x0000

/* The bit field setting of a GetStatus(DEVICE) result to show remote wakeup enabled. */
#define USB_GET_STATUS_DEVICE_REMOTE_WAKEUP_ENABLED  0x0002

/*
 * GetStatus(ENDPOINT) related constants
 */

/* The bit field setting of a GetStatus(ENDPOINT) result to show endpoint not halted. */
#define USB_GET_STATUS_ENDPOINT_NOT_HALTED  0x0000

/* The bit field setting of a GetStatus(ENDPOINT) result to show endpoint halted. */
#define USB_GET_STATUS_ENDPOINT_HALTED  0x0001




/*
 * Constants used in Comms Class Functional Descriptors
 */
/* Descriptor identifiers */
#define USB_CS_INTERFACE_FUNCTIONAL_DESCRIPTOR_TYPE           0x24
#define USB_CS_INTERFACE_FUNCTIONAL_DESCRIPTOR_SUBTYPE_HDR    0x00
#define USB_CS_INTERFACE_FUNCTIONAL_DESCRIPTOR_SUBTYPE_CMGMT  0x01
#define USB_CS_INTERFACE_FUNCTIONAL_DESCRIPTOR_SUBTYPE_ABCON  0x02

/* bmCapability bitmaps for Call Management descriptors: */
#define USB_COMM_BMCAP_CMGMT_DEVICE_HAS_CMGMT            0x01
/* Set to indicate that this device contains call-management capabilities.
 * If Unset, the following one is irrelevant. */
#define USB_COMM_BMCAP_CMGMT_ALLOW_CMGMT_OVER_DATA_IF    0x02
/* Set to allow 'AT' commands coming over the main data interface,
 * Unset to force AT commands to come over the separate CTRL interface. */

/* bmCapability bitmaps for Abstract Control descriptor: */
#define USB_COMM_BMCAP_ABCON_DEV_SUPPORTS_NETCON_NOTIFY  0x08
#define USB_COMM_BMCAP_ABCON_DEV_SUPPORTS_SEND_BREAK     0x04
#define USB_COMM_BMCAP_ABCON_DEV_SUPPORTS_LINE_STATES    0x02
#define USB_COMM_BMCAP_ABCON_DEV_SUPPORTS_COMM_FEATURES  0x01

/* bDescriptor subtypes in Comms Class Functional Descriptors
 * See table 25 of document "USB Class definitions for Comms Devices" */
#define USB_COMM_UNION_FUNCTIONAL_DESCRIPTOR_TYPE        0x06

/*******************************************************************************
 * Types used in Prototypes and Globals
 ******************************************************************************/

/* Standard descriptor codes. */
typedef enum UsbStandardDescriptorTypeTag
{
  USB_STANDARD_DESCRIPTOR_TYPE_DEVICE         = 1,
  USB_STANDARD_DESCRIPTOR_TYPE_CONFIGURATION  = 2,
  USB_STANDARD_DESCRIPTOR_TYPE_STRING         = 3,
  USB_STANDARD_DESCRIPTOR_TYPE_INTERFACE      = 4,
  USB_STANDARD_DESCRIPTOR_TYPE_ENDPOINT       = 5
} UsbStandardDescriptorType;

/* Device class
 *
 * Specifying CLASS_NONE effectively delegates the class information
 * to the interface descriptor. This is generally what we do in order to
 * enable mixed-class composite devices. */
typedef enum UsbDeviceClassTag
{
  USB_DEVICE_CLASS_NONE           = 0x00, /* No device class */
  USB_DEVICE_CLASS_COMMS          = 0x02, /* Communications device class */
  USB_DEVICE_CLASS_VENDOR         = 0xFF  /* Vendor specific class */
} UsbDeviceClass;

/* Device subclass */
typedef enum UsbDeviceSubClassTag
{
  USB_SUB_DEVICE_CLASS_NONE       = 0x00 /* No subclass */
} UsbbDeviceSubClass;

/* Device protocol */
typedef enum UsbDeviceProtocolTag
{
  USB_DEVICE_PROTOCOL_NONE        = 0x00 /* No protocol */
} UsbDeviceProtocol;

/* Configuration attributes */
typedef enum UsbConfigAttributesTag
{
  USB_CONFIG_ATTR_RESERVED_ONE    = 0x80, /* Reserved flag must be one */
  USB_CONFIG_ATTR_SELF_POWERED    = 0x40, /* Self-powered */
  USB_CONFIG_ATTR_REMOTE_WAKEUP   = 0x20, /* Remote wakeup capable */
  USB_CONFIG_ATTR_RESERVED_ZERO   = 0x1f  /* Reserved - must be zero */
} UsbConfigAttributes;

/* Power consumption limits */
typedef enum UsbConfigMaxPowerTag
{
  USB_CONFIG_MAX_POWER_ZERO       = 0,
  USB_CONFIG_MAX_POWER_LOW        = 50,
  USB_CONFIG_MAX_POWER_MAX        = 250
} UsbConfigMaxPower;

/* Interface class */
typedef enum UsbInterfaceClassTag
{
  USB_INTERFACE_CLASS_RESERVED     = 0x00, /* Reserved for future use */
  USB_INTERFACE_CLASS_COMMS_DEVICE = 0x02, /* Communications Interface */
  USB_INTERFACE_CLASS_DATA         = 0x0A, /* [Comms] Data Interface */
  USB_INTERFACE_CLASS_MASS_STORAGE = 0x08, /* Mass storage, Bulk only  */
  USB_INTERFACE_CLASS_VENDOR       = 0xFF  /* Vendor specific */
} UsbInterfaceClass;


/* Interface subclasses */
#define USB_INTERFACE_SUB_CLASS_ZERO    0x00 /* No subclass */
#define USB_INTERFACE_SUB_CLASS_VENDOR  0xFF /* Vendor specific  */
#define USB_INTERFACE_SUB_CLASS_CM_ABST 0x02 /* COMMS_DEVICE: Abstract Control Model */
#define USB_INTERFACE_SUB_CLASS_MS_SCSI 0x06 /* MASS_STORAGE: SCSI */


/* Interface protocols */
#define USB_INTERFACE_PROTOCOL_ZERO       0x00 /* No protocol */
#define USB_INTERFACE_PROTOCOL_VENDOR     0xFF /* Vendor specific */
#define USB_INTERFACE_PROTOCOL_V25TER     0x01 /* COMMS_DEVICE: V.25ter (AT) */
#define USB_INTERFACE_PROTOCOL_BULK_ONLY  0x50 /* MASS_STORAGE: Bulk only */


/* Endpoint attributes */
typedef enum UsbEndpointAttributesTag
{
  USB_ENDPOINT_TYPE_CONTROL     = 0x00,
  USB_ENDPOINT_TYPE_ISOCHRONOUS = 0x01,
  USB_ENDPOINT_TYPE_BULK        = 0x02,
  USB_ENDPOINT_TYPE_INTERRUPT   = 0x03,
  USB_ENDPOINT_TYPE_INVALID     = 0x04,
  USB_ENDPOINT_TYPE_MASK        = 0x03
} UsbEndpointAttributes;

typedef enum UsbEndpointAddressTag
{
  USB_ENDPOINT_ATTR_DIRECTION_OUT  = 0x00,   /* OUT endpoint */
  USB_ENDPOINT_ATTR_DIRECTION_IN   = 0x80,   /* IN endpoint */
  USB_ENDPOINT_ATTR_NUMBER_MASK    = 0x0f,
  USB_ENDPOINT_ATTR_DIRECTION_MASK = 0x80
} UsbEndpointAddress;



/* Get descriptor request data. */
typedef struct UsbDescriptorRequestTag
{
  Int8    type;
  Int8    index;
  Int8    interfaceNumber;
  Int8    alternateNumber;
  Int8    endpointAddress;
  Int16   langID;
} UsbDescriptorRequest;

/* Holds string descriptor data. */
typedef struct UsbStringDescriptorTag
{
  Int8    index;   /* The index of the string descriptor. */
  Int8*   string;  /* Pointer the string descriptor itself. */
  void*   next;    /* Pointer to next string descriptor struct (index + 1). */
} UsbStringDescriptor;

/*******************************************************************************
 * Macros
 ******************************************************************************/

/*
 * The following macros can be used to form a USB descriptor set for a device.
 */

/*
 * The USB device descriptor.
 *
 * USB_DEVICE_DESCRIPTOR(
 *   bcdUSB,             USB Specification release number
 *   bDeviceClass,       Class code
 *   bDeviceSubClass,    Subclass code
 *   bDeviceProtocol,    Protocol code
 *   bMaxPacketSize0,    Maximum packet size for EP0
 *   idVendor,           Vendor ID
 *   idProduct,          Product ID
 *   bcdDevice,          Device release number
 *   iManufacturer,      String index to manufacturer name
 *   iProduct,           String index to product name
 *   iSerialNumber,      String index to serial number
 *   bNumConfigurations
 * )
 *
 */

#define USB_DEVICE_DESCRIPTOR(                                               \
          bcdUSB, bDeviceClass, bDeviceSubClass, bDeviceProtocol,            \
          bMaxPacketSize0, idVendor, idProduct, bcdDevice,                   \
          iManufacturer, iProduct, iSerialNumber, bNumCofigurations          \
        )                                                                    \
                                                                             \
  USB_DEVICE_DESCRIPTOR_LENGTH,         /* Length of descriptor */           \
  USB_STANDARD_DESCRIPTOR_TYPE_DEVICE,  /* Type of descriptor */             \
  ((bcdUSB) & 0xFF),                    /* USB Specification (LSB) */        \
  ((bcdUSB) >> 8) & 0xFF,               /* USB Specification (MSB) */        \
  bDeviceClass,                         /* Class code */                     \
  bDeviceSubClass,                      /* Subclass code */                  \
  bDeviceProtocol,                      /* Protocol Code */                  \
  bMaxPacketSize0,                      /* Maximum packet size for EP0 */    \
  ((idVendor) & 0xFF),                  /* Vendor ID (LSB) */                \
  ((idVendor) >> 8) & 0xFF,             /* Vendor ID (MSB) */                \
  ((idProduct) & 0xFF),                 /* Product ID (LSB) */               \
  ((idProduct) >> 8) & 0xFF,            /* Product ID (MSB) */               \
  ((bcdDevice) & 0xFF),                 /* Device release number (LSB) */    \
  ((bcdDevice) >> 8) & 0xFF,            /* Device release number (MSB) */    \
  iManufacturer,                        /* Index to manufacturer string */   \
  iProduct,                             /* Index to product string */        \
  iSerialNumber,                        /* Index to serial string */         \
  bNumCofigurations                     /* Number of configurations */

/*
 * The USB configuration descriptor.
 *
 * USB_CONFIGURATION_DESCRIPTOR(
 *   wTotalLength,           Total length of configuration
 *   bNumInterfaces,         Number of interfaces in configuration
 *   bConfigurationValue,    Configuration value ID
 *   iConfiguration,         Index to configuration string
 *   bmAttributes,           Configuration attributes
 *   bMaxPower               Device maximum power (2mA steps)
 * )
 *
 */

#define USB_CONFIGURATION_DESCRIPTOR(                                              \
          wTotalLength, bNumInterfaces, bConfigurationValue,                       \
          iConfiguration, bmAttributes, bMaxPower                                  \
        )                                                                          \
                                                                                   \
  USB_CONFIGURATION_DESCRIPTOR_LENGTH,         /* Length of descriptor */          \
  USB_STANDARD_DESCRIPTOR_TYPE_CONFIGURATION,  /* Type of descriptor */            \
  ((wTotalLength) & 0xFF),                     /* Total length (LSB) */            \
  ((wTotalLength) >> 8) & 0xFF,                /* Total length (MSB) */            \
  bNumInterfaces,                              /* Number of interfaces  */         \
  bConfigurationValue,                         /* Configuration value ID */        \
  iConfiguration,                              /* Index to configuration string */ \
  (bmAttributes|USB_CONFIG_ATTR_RESERVED_ONE), /* Attributes */                    \
  bMaxPower                                    /* Maximum power required */

/*
 * The USB interface descriptor.
 *
 * USB_INTERFACE_DESCRIPTOR(
 *   bInterfaceNumber,      Number of the interface
 *   bAlternateSetting,     Alternate setting of interface
 *   bNumEndpoints,         Number of endpoints in interface/alternate
 *   bInterfaceClass,       Class code
 *   bInterfaceSubClass,    Subclass code
 *   bInterfaceProtocol,    Protocol code
 *   iInterface             Index to interface string
 * )
 *
 */

#define USB_INTERFACE_DESCRIPTOR(                                                    \
          bInterfaceNumber, bAlternateSetting, bNumEndpoints,                        \
          bInterfaceClass, bInterfaceSubClass, bInterfaceProtocol, iInterface        \
        )                                                                            \
                                                                                     \
  USB_INTERFACE_DESCRIPTOR_LENGTH,         /* Length of descriptor */                \
  USB_STANDARD_DESCRIPTOR_TYPE_INTERFACE,  /* Type of Descriptor */                  \
  bInterfaceNumber,                        /* Number of this interface */            \
  bAlternateSetting,                       /* Alternate number of this interface */  \
  bNumEndpoints,                           /* Number of endpoints */                 \
  bInterfaceClass,                         /* Class code */                          \
  bInterfaceSubClass,                      /* Subclass code */                       \
  bInterfaceProtocol,                      /* Protocol code */                       \
  iInterface                               /* Index to interface string */

/*
 * The USB endpoint descriptor.
 *
 * USB_ENDPOINT_DESCRIPTOR(
 *   bEndpointAddress,      Endpoint address
 *   bmAttributes,          Endpoint attributes
 *   wMaxpacketSize,        Maximum packet size (bytes)
 *   bInterval              Polling interval (ms)
 * )
 *
 */

#define USB_ENDPOINT_DESCRIPTOR(                                            \
          bEndpointAddress, bmAttributes, wMaxPacketSize, bInterval         \
        )                                                                   \
                                                                            \
  USB_ENDPOINT_DESCRIPTOR_LENGTH,          /* Length of descriptor */       \
  USB_STANDARD_DESCRIPTOR_TYPE_ENDPOINT,   /* Type of descriptor */         \
  bEndpointAddress,                        /* Endpoint address */           \
  bmAttributes,                            /* Endpoint attributes */        \
  ((wMaxPacketSize) & 0xFF),               /* Maximum packet size (LSB) */  \
  ((wMaxPacketSize) >> 8) & 0xFF,          /* Maximum packet size (MSB) */  \
  bInterval                                /* Polling interval */

/*
 * The USB string descriptor.
 *
 * USB_STRING_DESCRIPTOR(
 *   nChars      Number of characters in string
 * )
 *
 */

#define USB_STRING_DESCRIPTOR(nCHARS)                                     \
  ((USB_STANDARD_DESCRIPTOR_TYPE_STRING)<<8) /* Type of descriptor */     \
 + (2 + ((nCHARS) * 2))                      /* Length of descriptor */



/*
 * The following macros can be used for extracting data fields from descriptors.
 */

/* General descriptors */

#define USB_DESCRIPTOR_GET_LENGTH(dESCRIPTOR_p) ((dESCRIPTOR_p)[0])
#define USB_DESCRIPTOR_GET_TYPE(dESCRIPTOR_p) ((dESCRIPTOR_p)[1])

/* Device descriptors */

#define USB_DEVICE_DESCRIPTOR_GET_VERSION(dESCRIPTOR_p)                 \
          (((Int16) ((dESCRIPTOR_p)[2])) | (((Int16) ((dESCRIPTOR_p)[3])) << 8))
#define USB_DEVICE_DESCRIPTOR_GET_CLASS(dESCRIPTOR_p) ((dESCRIPTOR_p)[4])
#define USB_DEVICE_DESCRIPTOR_GET_SUB_CLASS(dESCRIPTOR_p) ((dESCRIPTOR_p)[5])
#define USB_DEVICE_DESCRIPTOR_GET_PROTOCOL(dESCRIPTOR_p) ((dESCRIPTOR_p)[6])
#define USB_DEVICE_DESCRIPTOR_GET_MAX_PACKET(dESCRIPTOR_p) ((dESCRIPTOR_p)[7])
#define USB_DEVICE_DESCRIPTOR_GET_VENDOR(dESCRIPTOR_p)                 \
          (((Int16) ((dESCRIPTOR_p)[8])) | (((Int16) ((dESCRIPTOR_p)[9])) << 8))
#define USB_DEVICE_DESCRIPTOR_GET_PRODUCT(dESCRIPTOR_p)                 \
          (((Int16) ((dESCRIPTOR_p)[10])) | (((Int16) ((dESCRIPTOR_p)[11])) << 8))
#define USB_DEVICE_DESCRIPTOR_GET_DEVICE(dESCRIPTOR_p)                 \
          (((Int16) ((dESCRIPTOR_p)[12])) | (((Int16) ((dESCRIPTOR_p)[13])) << 8))
#define USB_DEVICE_DESCRIPTOR_GET_MANUFACTURER_INDEX(dESCRIPTOR_p) ((dESCRIPTOR_p)[14])
#define USB_DEVICE_DESCRIPTOR_GET_PRODUCT_INDEX(dESCRIPTOR_p) ((dESCRIPTOR_p)[15])
#define USB_DEVICE_DESCRIPTOR_GET_SERIAL_INDEX(dESCRIPTOR_p) ((dESCRIPTOR_p)[16])
#define USB_DEVICE_DESCRIPTOR_GET_NUM_CONFIGS(dESCRIPTOR_p) ((dESCRIPTOR_p)[17])

/* Configuration descriptors */

#define USB_CONFIG_DESCRIPTOR_GET_TOTAL_LENGTH(dESCRIPTOR_p)                 \
          (((Int16) ((dESCRIPTOR_p)[2])) | (((Int16) ((dESCRIPTOR_p)[3])) << 8))
#define USB_CONFIG_DESCRIPTOR_GET_NUM_INTERFACES(dESCRIPTOR_p) ((dESCRIPTOR_p)[4])
#define USB_CONFIG_DESCRIPTOR_GET_CONFIG_NO(dESCRIPTOR_p) ((dESCRIPTOR_p)[5])
#define USB_CONFIG_DESCRIPTOR_GET_CONFIG_STRING_INDEX(dESCRIPTOR_p) ((dESCRIPTOR_p)[6])
#define USB_CONFIG_DESCRIPTOR_GET_CONFIG_ATTRIBUTES(dESCRIPTOR_p) ((dESCRIPTOR_p)[7])
#define USB_CONFIG_DESCRIPTOR_GET_CONFIG_MAX_POWER(dESCRIPTOR_p) ((dESCRIPTOR_p)[8])

/* Interface Descriptors */

#define USB_INTERFACE_DESCRIPTOR_GET_INTERFACE_NO(dESCRIPTOR_p) ((dESCRIPTOR_p)[2])
#define USB_INTERFACE_DESCRIPTOR_GET_ALTERNATE_NO(dESCRIPTOR_p) ((dESCRIPTOR_p)[3])
#define USB_INTERFACE_DESCRIPTOR_GET_NUM_ENDPOINTS(dESCRIPTOR_p) ((dESCRIPTOR_p)[4])
#define USB_INTERFACE_DESCRIPTOR_GET_CLASS(dESCRIPTOR_p) ((dESCRIPTOR_p)[5])
#define USB_INTERFACE_DESCRIPTOR_GET_SUB_CLASS(dESCRIPTOR_p) ((dESCRIPTOR_p)[6])
#define USB_INTERFACE_DESCRIPTOR_GET_PROTOCOL(dESCRIPTOR_p) ((dESCRIPTOR_p)[7])
#define USB_INTERFACE_DESCRIPTOR_GET_STRING_ID(dESCRIPTOR_p) ((dESCRIPTOR_p)[8])

/* Endpoint Descriptors */

#define USB_ENDPOINT_DESCRIPTOR_GET_ADDRESS(dESCRIPTOR_p) ((dESCRIPTOR_p)[2])
#define USB_ENDPOINT_DESCRIPTOR_GET_ATTRIBUTES(dESCRIPTOR_p) ((dESCRIPTOR_p)[3])
#define USB_ENDPOINT_DESCRIPTOR_GET_MAX_PACKET_SIZE(dESCRIPTOR_p) ((dESCRIPTOR_p)[4])
#define USB_ENDPOINT_DESCRIPTOR_GET_INTERVAL(dESCRIPTOR_p) ((dESCRIPTOR_p)[6])

#define USB_ENDPOINT_DESCRIPTOR_GET_ENDPOINT_NO(dESCRIPTOR_p)  \
    (USB_ENDPOINT_DESCRIPTOR_GET_ADDRESS(dESCRIPTOR_p) & USB_ENDPOINT_ATTR_NUMBER_MASK)
#define USB_ENDPOINT_DESCRIPTOR_GET_DIRECTION(dESCRIPTOR_p) ((UsbEndpointAttributes) \
    (USB_ENDPOINT_DESCRIPTOR_GET_ADDRESS(dESCRIPTOR_p) & USB_ENDPOINT_ATTR_DIRECTION_MASK))

#define USB_ENDPOINT_DESCRIPTOR_GET_TYPE(dESCRIPTOR_p) \
    (USB_ENDPOINT_DESCRIPTOR_GET_ATTRIBUTES(dESCRIPTOR_p) & USB_ENDPOINT_TYPE_MASK)

/* String Descriptors */

#define USB_STRING_DESCRIPTOR_GET_NUMBER_OF_STRINGS(dESCRIPTOR_p)              \
          (((Int16) ((dESCRIPTOR_p)[0])) | (((Int16) ((dESCRIPTOR_p)[1])) << 8))
#define USB_STRING_DESCRIPTOR_GET_LANGID(dESCRIPTOR_p)   \
          (((Int16) ((dESCRIPTOR_p)[0])) | (((Int16) ((dESCRIPTOR_p)[1])) << 8))
#define USB_STRING_DESCRIPTOR_GET_DESC(dESCRIPTOR_p) ((const Int8 *) &((dESCRIPTOR_p)[2]))

#endif /* USBDESCS_H */
#endif /* UPGRADE_USB */
/* END OF FILE */

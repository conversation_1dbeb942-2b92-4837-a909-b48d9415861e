/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/sys/tmm.mod/api/shinc/tmmtyp.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2006/12/15 14:47:38 $
 **************************************************************************
 * File Description: Traffic Memory Manager Types
 **************************************************************************/

#ifndef TMMTYP_H
#define TMMTYP_H

/***************************************************************************
 * Nested Include Files
 **************************************************************************/
#include <system.h>
#include <ki_typ.h>

/***************************************************************************
 * Manifest Constants
 **************************************************************************/
/* The maximum number of flow control tasks that can be registered per pool */
#define TMM_MAX_FLOW_CTRL_IDS 6

/* The maximum number of entities that can reference a particular TMM block */
#define TMM_MAX_REFERENCES    0xFF

/* The following products require reference counts for TMM blocks. Enable
 * TMM_ENABLE_REFERENCES for these products. */


      

#if defined (TMM_PC_SIMULATION)
#define TMM_ENABLE_REFERENCES
#endif

/***************************************************************************
 * Type Definitions
 **************************************************************************/
 /***************************************************************************
 * Name        : TmmProfileId
 * Description : Set the profiles for default TMM allocations
 ***************************************************************************/
typedef enum TmmProfileIdTag
{
/* Setup the profile Ids */
#define TMM_GET_PROFILE_NAME
    #include <tmmmem.h>
    TMM_NUM_PROFILES
}
TmmProfileId;

/***************************************************************************
 * Name        : TmmMemoryManagerId
 * Description : Memory Manager Identifiers
 ***************************************************************************/
typedef enum TmmMemoryManagerIdTag
{
/* Setup the TMM memory manager IDs */
#define TMM_GET_MEM_MGR_NAME
    #include <tmmmem.h>
    TMM_INVALID_MEM_MGR
}
TmmMemoryManagerId;

/***************************************************************************
 * Name        : TmmPoolId
 * Description : Pool Identifiers.
 ***************************************************************************/
typedef enum TmmPoolIdTag
{
/* Setup the TMM Pool IDs */
#define TMM_GET_VIRT_POOL_NAME
    #include <tmmmem.h>
    TMM_VIRTUAL_POOL_END,

#define TMM_GET_REAL_POOL_NAME
    #include <tmmmem.h>
    TMM_REAL_POOL_END,

#define TMM_GET_MAPPED_POOL_NAME
    #include <tmmmem.h>

    TMM_NUM_POOLS
}
TmmPoolId;

/***************************************************************************
 * Name        : TmmBlockHeader
 * Description : Each TMM block allocated has an associated header. This
 *               holds information specific to the block and is required by
 *               TMM for its internal management.
 ***************************************************************************/
typedef struct TmmBlockHeaderDataTag
{
    Int32               blockLength; /* Size of the TMM block excluding this header */

#if defined (DEVELOPMENT_VERSION)
    Int32               checkValue; /* A check value to make sure that the header was not overwritten */
#endif

#if defined (TMM_ENABLE_EXTENDED_DEBUG)
    void                *prevBlock_p;   /* To implement a linked list */
    void                *nextBlock_p;   /* To implement a linked list */
    Int32               callerAddress;  /* Address of the function that requested the TMM memory */
    TaskId              taskId;         /* Task ID of the function that requested the TMM memory */
#endif

#if defined (TMM_ENABLE_REFERENCES)
    Int8                references;     /* The number of entities refering to this block */
#endif

    TmmPoolId           poolId;         /* The pool that the block was allocated from */
    TmmMemoryManagerId  memMgrId;       /* The memory manager that allocated this block */
}
TmmBlockHeaderData;

typedef struct TmmBlockHeader1Tag
{
    TmmBlockHeaderData  data;

#if !defined (ON_PC)
    /* Force the header to be 32 bytes to fit cache line */
    Int8                filler[32 - (offsetof(TmmBlockHeaderData, memMgrId)+1)];
#endif /* ON_PC */
}
TmmBlockHeader;

/***************************************************************************
 * Name        : TmmPoolProperties
 * Description : This structure is an element in a table. This table
 *               holds the properties of each particular pool.
 ***************************************************************************/
typedef struct TmmPoolPropertiesTag
{
    Int32               poolSize;      /* Size of the memory pool in bytes */
    TmmMemoryManagerId  memMgrId;
    Int32               vhwm;           /* Very high water mark */
    Int32               hwm;           /* High water mark */
    Int32               lwm;           /* Low water mark */
    Int32               minTailLen;    /* The minimum tail length */
    Int16               maxAllocCount; /* This is the maximum number of allocations before flow control registered tasks are denied allocations */
    Int16               hwmAllocCount; /* The number of allocations from this pool before a HWM signal is sent */
    Int16               lwmAllocCount; /* If we are flow controlled off, then the number of
                                        * allocs must reach this level before we flow control back on */
}
TmmPoolProperties;

/***************************************************************************
 * Name        : TmmFlowCtrlData
 * Description : This structure holds a task registered to receive flow
 *               control signalling from a specific TMM pool.
 ***************************************************************************/
typedef struct TmmFlowCtrlDataTag
{
    /* The number of times the task ID has registered for flow control. */
    Int8   numInstances;

    /* The task ID registered for flow control. This field is only valid if
     * numInstances is non-zero. */
    TaskId taskId;
}
TmmFlowCtrlData;

/***************************************************************************
 * Name        : TmmPoolState
 * Description : This enumerates the pool state. It indicates if the specific
 *               pool is normal or being flow controlled.
 ***************************************************************************/
typedef enum TmmPoolStateTag
{
    TMM_POOL_STATE_NORMAL,     /* Pool is normal */
    TMM_POOL_STATE_OVERLOADED,  /* The amount of free memory is less than the current high water mark */
    TMM_POOL_STATE_VERY_OVERLOADED /* The amount of free memory is less than the current very high water mark */
}
TmmPoolState;

/***************************************************************************
 * Name        : TmmPoolData
 * Description : Holds data specific to each memory pool. Each memory pool
 *               that TMM uses has an instance of this structure.
 ***************************************************************************/
typedef struct TmmPoolDataTag
{
    Int32            allocatedMemory;                      /* The amount of memory allocated */
    TmmPoolState     state;                                /* The state of the pool */
    Boolean 	  overloadedDueToAllocCount;  /* In the case that pool state changes to TMM_POOL_STATE_OVERLOADED due to allocation count, this bit rises. */ 
    Int8             numFlowCtrlIds;                       /* The number of tasks registered to receive flow ctrl data from this pool */
    TmmFlowCtrlData  flowCtrlData [TMM_MAX_FLOW_CTRL_IDS]; /* Tasks registered for flow control from this pool */
    Int16            allocationCount;                      /* Number of allocations from this pool */
}
TmmPoolData;

/***************************************************************************
 * Name        : TmmProfileData
 * Description : Profile data
 ***************************************************************************/
typedef struct TmmProfileDataTag
{
    TmmPoolId mappedPool [TMM_VIRTUAL_POOL_END];
}
TmmProfileData;

/***************************************************************************
 * Name        : TmmStateData
 * Description : This structure holds the main TMM state data. There is only
 *               one instance of this structure.
 ***************************************************************************/
typedef struct TmmStateDataTag
{
    /* This structure holds individual pool data. Only Virtual pools and
     * Real pools can be accessed by callers of TMM. Therefore, we do not
     * need to allocate space for Mapped pools. */
    TmmPoolData poolData [TMM_REAL_POOL_END];
}
TmmStateData;

/****************************************************************************************
 * Name        : TmmFlowControlSignal
 * Description : This enumerates the signals that TmmSignalLwmHwm function can send
 ***************************************************************************************/
typedef enum TmmFlowControlSignalTag
{
	TMM_FLOW_CTRL_MEM_BELOW_LWM_IND = 0,
	TMM_FLOW_CTRL_MEM_ABOVE_HWM_IND = 1,
	TMM_FLOW_CTRL_MEM_ABOVE_VWHM_IND
}
TmmFlowControlSignal;
#endif



/* END OF FILE */

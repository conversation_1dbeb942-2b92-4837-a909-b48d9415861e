/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urltmrx.h#3 $
 *   $Revision: #3 $
 *   $DateTime: 2007/03/19 13:59:30 $
 **************************************************************************
 * File Description:
 *
 *      Header file for UrlTmRx.c.
 *      Contains function call declarations, constants and types for use
 *      by other URLC modules.
 **************************************************************************/

#if !defined (URLTMRX_H)
#define       URLTMRX_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <urltmtyp.h>

/***************************************************************************
*   Macro Functions
***************************************************************************/

/* Macro to determine if a bearer is configured for loopback mode, either
 * active or pending
 */
#define URL_TMRX_LOOPBACK(entity_p)          ((entity_p)->loopback)

/***************************************************************************
 * Typed Constants
 ***************************************************************************/

/***************************************************************************
 * Function Prototypes
 ***************************************************************************/

extern void
UrlTmRxHandler
    (UrlTmRxEntity                                    * entityData_p,
     SignalBuffer                                     * rxSignal_p
    );
extern  void
UrlTmRxGetPduInfo
    (UrlTmRxEntity                                    * entityData_p,
     UmacDlPduListInfo                                * pduListInfo_p,
     Int8                                               pduNum,
     Int8                                               numPdus,
     dtcF8DescriptorInfo                              * cipherInfo_p,
     UFrameNumber                                       frameNumber,
     UUARFCN                                            arfcn,
     UPrimaryScramblingCode                             scramblingCode
    );
extern  void
UrlTmRxUmacDataInd
    (UrlTmRxEntity                                    * entityData_p,
     UmacDlPduListInfo                               * pduListInfo_p,
     Int8                                               pduNum,
     Int8                                               numPdus,
     dtcF8DescriptorInfo                              * cipherInfo_p,
     Int16                                              fn
    );
extern  void
UrlTmRxCloseTestLoopReq
    (UrlTmRxEntity                                    * entityData_p,
     TcUmtsTestLoopMode                                 loopbackMode,
     Int8                                               numberOfSduSizes,
     Int16                                              rlcSduBits[UPS_MAX_UL_TF]
    );
extern  void
UrlTmRxLoopbackUrlcTmDataInd
    (BearerIdentity                                     bearerIdentity,
     SignalBuffer                                     * loopbackSignal_p
    );
extern  void
UrlTmRxAboveHighWaterMark
    (UrlTmRxEntity                                    * entityData_p
    );
extern  void
UrlTmRxDiagnosticsReq
    (UrlTmRxEntity                                    * entityData_p
    );

#if defined(UMTS7_PRE_R8)&& !defined(ENABLE_URLC_UNIT_TEST)

extern Boolean DlbgProcessUrlcTmDataInd(UrlcPsTmDataInd *urlcPsTmDataInd);
#endif

#endif /* URLTMRX_H */

/* END OF UrlTmRx.h */

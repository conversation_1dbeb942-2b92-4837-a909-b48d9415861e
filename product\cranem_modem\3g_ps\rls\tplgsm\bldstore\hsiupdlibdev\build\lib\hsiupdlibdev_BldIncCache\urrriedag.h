/*------------------------------------------------------------
(C) Copyright [2006-2011] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/***************************************************************************
*
* File Description: urrriedag.h
*		RRC RIE messsages handling functionality header file
*		Automatically generated by the release upgrade tool
*
***************************************************************************/

#if !defined (URRRIEDAG_H)
#define       URRRIEDAG_H

#include <uas_asn.h>
#include <utper.h>
#include <urrtypes.h>

void Copy_UrrRie_CCCH_UCellUpdateConfirm_r6_IEs_UCellUpdateConfirm_r9_IEs (
	PerBuffer *perBuffer_p,
	UrrRxMsgVersion rxMsgVersion,
	UCellUpdateConfirm_CCCH *msg_p);

void Copy_UrrRie_CCCH_UCellUpdateConfirm_r7_IEs_UCellUpdateConfirm_r9_IEs (
	PerBuffer *perBuffer_p,
	UrrRxMsgVersion rxMsgVersion,
	UCellUpdateConfirm_CCCH *msg_p);

void Copy_UrrRie_CCCH_UCellUpdateConfirm_r8_IEs_UCellUpdateConfirm_r9_IEs (
	PerBuffer *perBuffer_p,
	UrrRxMsgVersion rxMsgVersion,
	UCellUpdateConfirm_CCCH *msg_p);

void Copy_UrrRie_CCCH_URRCConnectionSetup_r6_IEs_URRCConnectionSetup_r9_IEs (
	PerBuffer *perBuffer_p,
	UrrRxMsgVersion rxMsgVersion,
	URRCConnectionSetup *msg_p);

void Copy_UrrRie_CCCH_URRCConnectionSetup_r7_IEs_URRCConnectionSetup_r9_IEs (
	PerBuffer *perBuffer_p,
	UrrRxMsgVersion rxMsgVersion,
	URRCConnectionSetup *msg_p);

void Copy_UrrRie_CCCH_URRCConnectionSetup_r8_IEs_URRCConnectionSetup_r9_IEs (
	PerBuffer *perBuffer_p,
	UrrRxMsgVersion rxMsgVersion,
	URRCConnectionSetup *msg_p);

void Copy_UrrRie_DCCH_UActiveSetUpdate_r6_IEs_UActiveSetUpdate_r9_IEs (
	PerBuffer *perBuffer_p,
	UrrRxMsgVersion rxMsgVersion,
	UActiveSetUpdate *msg_p);

void Copy_UrrRie_DCCH_UActiveSetUpdate_r7_IEs_UActiveSetUpdate_r9_IEs (
	PerBuffer *perBuffer_p,
	UrrRxMsgVersion rxMsgVersion,
	UActiveSetUpdate *msg_p);

void Copy_UrrRie_DCCH_UActiveSetUpdate_r8_IEs_UActiveSetUpdate_r9_IEs (
	PerBuffer *perBuffer_p,
	UrrRxMsgVersion rxMsgVersion,
	UActiveSetUpdate *msg_p);

void Copy_UrrRie_DCCH_UCellUpdateConfirm_r6_IEs_UCellUpdateConfirm_r9_IEs (
	PerBuffer *perBuffer_p,
	UrrRxMsgVersion rxMsgVersion,
	UCellUpdateConfirm *msg_p);

void Copy_UrrRie_DCCH_UCellUpdateConfirm_r7_IEs_UCellUpdateConfirm_r9_IEs (
	PerBuffer *perBuffer_p,
	UrrRxMsgVersion rxMsgVersion,
	UCellUpdateConfirm *msg_p);

void Copy_UrrRie_DCCH_UCellUpdateConfirm_r8_IEs_UCellUpdateConfirm_r9_IEs (
	PerBuffer *perBuffer_p,
	UrrRxMsgVersion rxMsgVersion,
	UCellUpdateConfirm *msg_p);

void Copy_UrrRie_DCCH_UMeasurementControl_r6_IEs_UMeasurementControl_r9_IEs (
	PerBuffer *perBuffer_p,
	UrrRxMsgVersion rxMsgVersion,
	UMeasurementControl *msg_p);

void Copy_UrrRie_DCCH_UMeasurementControl_r7_IEs_UMeasurementControl_r9_IEs (
	PerBuffer *perBuffer_p,
	UrrRxMsgVersion rxMsgVersion,
	UMeasurementControl *msg_p);

void Copy_UrrRie_DCCH_UMeasurementControl_r8_IEs_UMeasurementControl_r9_IEs (
	PerBuffer *perBuffer_p,
	UrrRxMsgVersion rxMsgVersion,
	UMeasurementControl *msg_p);

void Copy_UrrRie_DCCH_UPhysicalChannelReconfiguration_r6_IEs_UPhysicalChannelReconfiguration_r9_IEs (
	PerBuffer *perBuffer_p,
	UrrRxMsgVersion rxMsgVersion,
	UPhysicalChannelReconfiguration *msg_p);

void Copy_UrrRie_DCCH_UPhysicalChannelReconfiguration_r7_IEs_UPhysicalChannelReconfiguration_r9_IEs (
	PerBuffer *perBuffer_p,
	UrrRxMsgVersion rxMsgVersion,
	UPhysicalChannelReconfiguration *msg_p);

void Copy_UrrRie_DCCH_UPhysicalChannelReconfiguration_r8_IEs_UPhysicalChannelReconfiguration_r9_IEs (
	PerBuffer *perBuffer_p,
	UrrRxMsgVersion rxMsgVersion,
	UPhysicalChannelReconfiguration *msg_p);

void Copy_UrrRie_DCCH_URadioBearerReconfiguration_r6_IEs_URadioBearerReconfiguration_r9_IEs (
	PerBuffer *perBuffer_p,
	UrrRxMsgVersion rxMsgVersion,
	URadioBearerReconfiguration *msg_p);

void Copy_UrrRie_DCCH_URadioBearerReconfiguration_r7_IEs_URadioBearerReconfiguration_r9_IEs (
	PerBuffer *perBuffer_p,
	UrrRxMsgVersion rxMsgVersion,
	URadioBearerReconfiguration *msg_p);

void Copy_UrrRie_DCCH_URadioBearerReconfiguration_r8_IEs_URadioBearerReconfiguration_r9_IEs (
	PerBuffer *perBuffer_p,
	UrrRxMsgVersion rxMsgVersion,
	URadioBearerReconfiguration *msg_p);

void Copy_UrrRie_DCCH_URadioBearerRelease_r6_IEs_URadioBearerRelease_r9_IEs (
	PerBuffer *perBuffer_p,
	UrrRxMsgVersion rxMsgVersion,
	URadioBearerRelease *msg_p);

void Copy_UrrRie_DCCH_URadioBearerRelease_r7_IEs_URadioBearerRelease_r9_IEs (
	PerBuffer *perBuffer_p,
	UrrRxMsgVersion rxMsgVersion,
	URadioBearerRelease *msg_p);

void Copy_UrrRie_DCCH_URadioBearerRelease_r8_IEs_URadioBearerRelease_r9_IEs (
	PerBuffer *perBuffer_p,
	UrrRxMsgVersion rxMsgVersion,
	URadioBearerRelease *msg_p);

void Copy_UrrRie_DCCH_URadioBearerSetup_r6_IEs_URadioBearerSetup_r9_IEs (
	PerBuffer *perBuffer_p,
	UrrRxMsgVersion rxMsgVersion,
	URadioBearerSetup *msg_p);

void Copy_UrrRie_DCCH_URadioBearerSetup_r7_IEs_URadioBearerSetup_r9_IEs (
	PerBuffer *perBuffer_p,
	UrrRxMsgVersion rxMsgVersion,
	URadioBearerSetup *msg_p);

void Copy_UrrRie_DCCH_URadioBearerSetup_r8_IEs_URadioBearerSetup_r9_IEs (
	PerBuffer *perBuffer_p,
	UrrRxMsgVersion rxMsgVersion,
	URadioBearerSetup *msg_p);

void Copy_UrrRie_DCCH_USecurityModeCommand_r3_IEs_USecurityModeCommand_r7_IEs (
	PerBuffer *perBuffer_p,
	UrrRxMsgVersion rxMsgVersion,
	USecurityModeCommand *msg_p);

void Copy_UrrRie_DCCH_UTransportChannelReconfiguration_r6_IEs_UTransportChannelReconfiguration_r9_IEs (
	PerBuffer *perBuffer_p,
	UrrRxMsgVersion rxMsgVersion,
	UTransportChannelReconfiguration *msg_p);

void Copy_UrrRie_DCCH_UTransportChannelReconfiguration_r7_IEs_UTransportChannelReconfiguration_r9_IEs (
	PerBuffer *perBuffer_p,
	UrrRxMsgVersion rxMsgVersion,
	UTransportChannelReconfiguration *msg_p);

void Copy_UrrRie_DCCH_UTransportChannelReconfiguration_r8_IEs_UTransportChannelReconfiguration_r9_IEs (
	PerBuffer *perBuffer_p,
	UrrRxMsgVersion rxMsgVersion,
	UTransportChannelReconfiguration *msg_p);

void Copy_UrrRie_DCCH_UURAUpdateConfirm_r5_IEs_UURAUpdateConfirm_r7_IEs (
	PerBuffer *perBuffer_p,
	UrrRxMsgVersion rxMsgVersion,
	UURAUpdateConfirm *msg_p);

void Copy_UrrRie_DCCH_UUTRANMobilityInformation_r5_IEs_UUTRANMobilityInformation_r7_IEs (
	PerBuffer *perBuffer_p,
	UrrRxMsgVersion rxMsgVersion,
	UUTRANMobilityInformation *msg_p);

void Copy_UUL_EDCH_Information_r6_to_UUL_EDCH_Information_r9 (
	PerBuffer *perBuffer_p,
	UUL_EDCH_Information_r6 *source_p,
	UUL_EDCH_Information_r9 *target_p);

void Copy_UDL_HSPDSCH_Information_r6_to_UDL_HSPDSCH_Information_r9 (
	PerBuffer *perBuffer_p,
	UDL_HSPDSCH_Information_r6 *source_p,
	UDL_HSPDSCH_Information_r9 *target_p);

void Copy_UDL_CommonInformation_r6_to_UDL_CommonInformation_r8 (
	PerBuffer *perBuffer_p,
	UDL_CommonInformation_r6 *source_p,
	UDL_CommonInformation_r8 *target_p);

#if !defined (ENABLE_UMTS_CAP_DOWNGRADE)
void Copy_UUL_EDCH_Information_r8_to_UUL_EDCH_Information_r9 (
    PerBuffer *perBuffer_p,
    UUL_EDCH_Information_r8 *source_p,
    UUL_EDCH_Information_r9 *target_p);
void Copy_UDL_HSPDSCH_Information_r8_to_UDL_HSPDSCH_Information_r9 (
    PerBuffer *perBuffer_p,
    UDL_HSPDSCH_Information_r8 *source_p,
    UDL_HSPDSCH_Information_r9 *target_p);
#endif//ENABLE_UMTS_CAP_DOWNGRADE

void Copy_USRB_InformationSetupList2_r7_to_USRB_InformationSetupList2_r8 (
  PerBuffer *perBuffer_p,
  USRB_InformationSetupList2_r7 *source_p,
  USRB_InformationSetupList2_r8 *target_p);

#if !defined (ENABLE_UMTS_CAP_DOWNGRADE)
void Copy_UUL_EDCH_Information_r7_to_UUL_EDCH_Information_r9 (
  PerBuffer *perBuffer_p,
  UUL_EDCH_Information_r7 *source_p,
  UUL_EDCH_Information_r9 *target_p);

void Copy_UDL_HSPDSCH_Information_r7_to_UDL_HSPDSCH_Information_r9 (
  PerBuffer *perBuffer_p,
  UDL_HSPDSCH_Information_r7 *source_p,
  UDL_HSPDSCH_Information_r9 *target_p);
#endif//ENABLE_UMTS_CAP_DOWNGRADE

void Copy_UDL_CommonInformation_r7_to_UDL_CommonInformation_r8 (
  PerBuffer *perBuffer_p,
  UDL_CommonInformation_r7 *source_p,
  UDL_CommonInformation_r8 *target_p);



#endif

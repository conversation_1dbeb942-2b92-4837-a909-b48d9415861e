/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/3g.mod/lib/src/ulbgupde_rb.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2011/04/04 20:17:00 $
 **************************************************************************
 * File Description:
 *
 *      The UPDCP Entity - RB.
 **************************************************************************/

#if !defined (ULBGUPDE_RB_H)
#define ULBGUPDE_RB_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <npdu_typ.h>
#include <ulbgupde.h>
#include <psinterface.h>

/***************************************************************************
* Manifest Constants
***************************************************************************/

/***************************************************************************
* Macro Functions
***************************************************************************/

/***************************************************************************
* Types
***************************************************************************/

/***************************************************************************
* Typed Constants
***************************************************************************/

/***************************************************************************
* Function Prototypes
***************************************************************************/

extern Boolean UlbgUpdcpRlcSap (SignalBuffer *sigBuf);

extern Boolean UlbgUpdeTransmitRlcSdu (UlbgUpdEntity *pde, Npdu *npdu,SnUlSduList  *srcPlatNode);
extern Boolean UlbgUpdeTransmitNpduFromLlist (UlbgUpdEntity *pde, UpdcpNpduInfo *npduInfo); 

#if defined (ENABLE_UPLANE_STATISTICS)
extern void    UpdeSendStatistics (SignalBuffer *sigBuf);
#endif /* ENABLE_UPLANE_STATISTICS */

/* Added by Daniel for ********** 20120806, begin */
#if defined(UMTS7_PRE_R8)
extern void ulbgprocessPsAmDataCnf(UrlcPsAmDataCnf *urlcPsAmDataCnf);
#ifdef ENABLE_OPT_DATA_FLOW_OVER_SHMEM
extern void ulbgprocessPsUmDataCnf(UrlcPsUmDataCnf *urlcPsUmDataCnf);
#endif
extern void ulbgprocessPsUlSduDiscardInd(UrlcUlSduDiscardInd *urlcUlSduDiscardInd);
extern void ulbgprocessPsAmDlSduDiscardInd(UrlcAmDlSduDiscardInd *urlcAmDlSduDiscardInd);
extern void ulbgprocessPsXonInd(UrlcXonInd *urlcXonInd);
extern void ulbgprocessPsXoffInd(UrlcXoffInd *urlcXoffInd);
#endif
/* Added by Daniel for ********** 20120806, end */

#endif

/* END OF FILE */

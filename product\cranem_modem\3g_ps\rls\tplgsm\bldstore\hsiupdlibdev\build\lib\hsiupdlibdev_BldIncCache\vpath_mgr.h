/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/*--------------------------------------------------------------------------------------------------------------------
INTEL CONFIDENTIAL
Copyright 2006 Intel Corporation All Rights Reserved.
The source code contained or described herein and all documents related to the source code ("Material") are owned
by Intel Corporation or its suppliers or licensors. Title to the Material remains with Intel Corporation or
its suppliers and licensors. The Material contains trade secrets and proprietary and confidential information of
Intel or its suppliers and licensors. The Material is protected by worldwide copyright and trade secret laws and
treaty provisions. No part of the Material may be used, copied, reproduced, modified, published, uploaded, posted,
transmitted, distributed, or disclosed in any way without Intel's prior express written permission.

No license under any patent, copyright, trade secret or other intellectual property right is granted to or
conferred upon you by disclosure or delivery of the Materials, either expressly, by implication, inducement,
estoppel or otherwise. Any license under such intellectual property rights must be express and approved by
Intel in writing.
-------------------------------------------------------------------------------------------------------------------*/

/******************************************************************************
*               MODULE IMPLEMENTATION FILE
*******************************************************************************
* Title: vpath_mgr
*
* Filename: vpath_mgr.h
*
* Authors: <AUTHORS>
*
* Description:
*
* Last Updated:
*
* Notes:
******************************************************************************/
#ifndef VOICE_PATH_MGR_H_
#define VOICE_PATH_MGR_H_

/*----------- Local include files --------------------------------------------*/

/*----------- Global defines -------------------------------------------------*/

/*----------- Global macro definitions ---------------------------------------*/

/*----------- Global type definitions ----------------------------------------*/

/*----------- Extern definition ----------------------------------------------*/
#ifndef _VOICE_PATH_MGR_NO_EXTERN_
  #define EXTERN extern
#else
  #define EXTERN
#endif /* _VOICE_PATH_MGR_NO_EXTERN_ */

/*----------- Global variable declarations -----------------------------------*/

/*----------- Global constant definitions ------------------------------------*/

/*----------- Global function prototypes -------------------------------------*/
void vpathOsResourcesCreate(void);



#undef EXTERN

#endif /*VOICE_PATH_MGR_H_*/



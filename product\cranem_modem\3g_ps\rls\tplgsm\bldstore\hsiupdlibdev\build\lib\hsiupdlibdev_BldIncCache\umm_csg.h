/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/


#if !defined (UMM_CSG_H)
#define       UMM_CSG_H

#if defined (UPGRADE_CSG1)

/***************************************************************************
 * Nested Include Files
 **************************************************************************/


//#include <system.h>

extern void UmmCsgMmrCsgSearchAutoReq (MobilityManagerEntity *mm);
extern void UmmCsgMmrCsgSearchListReq (MobilityManagerEntity *mm);
extern void UmmCsgMmrCsgSelectReq (MobilityManagerEntity *mm);
extern void UmmCsgRrAutoCsgSelectCnf (MobilityManagerEntity *mm);

extern void UmmCsgRrCsgListCnf (MobilityManagerEntity *mm);
extern void UmmCsgRrCsgSelectCnf (MobilityManagerEntity *mm);

extern Boolean UmmCsgCreateRrCsgWhiteList(MobilityManagerEntity *mm, RrCsgWhiteList  *whiteList_p);

extern void UmmCsgStoreMsData(MobilityManagerEntity *mm);
extern void UmmCsgImmediateRemoveFromWhiteList(MobilityManagerEntity * mm);


extern void EmmConstructErrcAutoCsgSelectReq (MobilityManagerEntity *mm);
extern void EmmConstructErrcCsgSelectReq (MobilityManagerEntity *mm);
extern void EmmConstructErrcCsgListReq         (MobilityManagerEntity *mm);

extern void EmmConstructRrcAutoCsgSelectReq (MobilityManagerEntity *mm);
extern void EmmConstructRrcCsgSelectReq (MobilityManagerEntity *mm);
extern void EmmConstructRrcCsgListReq         (MobilityManagerEntity *mm);


extern void EmmConstructGrrAutoCsgSelectReq (MobilityManagerEntity *mm);
extern void EmmConstructGrrCsgSelectReq (MobilityManagerEntity *mm);
extern void EmmConstructGrrCsgListReq         (MobilityManagerEntity *mm);



extern void EmmConstructMmrCsgSearchAutoCnf (MobilityManagerEntity *mm);
extern void UmmConstructMmrCsgSelectCnf  (MobilityManagerEntity *mm);
extern void UmmConstructMmrCsgSearchListCnf (MobilityManagerEntity *mm);


void UmmCsgStoreBroadcastCsgInfo(	MobilityManagerEntity *mm,
											ESystemInformation       *eSysInfo,
											USystemInformation       *uSysInfo);

Boolean UmmCsgIsFoundOnRrc(MobilityManagerEntity *mm, USystemInformation    *sysInfo);

Boolean UmmCsgIsFoundOnErrc(MobilityManagerEntity *mm, ESystemInformation  *sysInfo);
#endif

#endif

/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/***************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 ***************************************************************************
 *
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urrrie.h#3 $
 *   $Revision: #3 $
 *   $DateTime: 2007/03/19 13:59:30 $
 *
 ***************************************************************************
 *
 * File Description:
 *
 *    Header file for the external interface to the Release Independent
 *    Module of the RRC.
 *
 ***************************************************************************
 *
 ***************************************************************************/

#if !defined (URRRIE_H)
#define       URRRIE_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <uas_asn.h>
#include <utper.h>          /* For PerBuffer */
#include <urrtypes.h>       /* For UrrRxMsgVersion */

/***************************************************************************
 * Manifest constants
 ***************************************************************************/

/***************************************************************************
 * Macro Functions
 ***************************************************************************/

/***************************************************************************
 * Types
 ***************************************************************************/

 /***************************************************************************
 * Typed constants
 ***************************************************************************/

/***************************************************************************
 * Global Data declarations
 ***************************************************************************/
extern Int32 RRC_TRACE_MASK;

/***************************************************************************
 * Global Function Prototypes
 ***************************************************************************/

/* Top level message handlers */
UrrRxMsgVersion UrrRieProcessDlDcchMessage (
    PerBuffer * perBuffer_p,
    UDL_DCCH_Message * msg_p);

UrrRxMsgVersion UrrRieProcessDlCcchMessage (
    PerBuffer * perBuffer_p,
    UDL_CCCH_Message * msg_p);

#if ! defined (UPGRADE_EXCLUDE_2G)
UrrRxMsgVersion UrrRieProcessHandoverToUTRAN (
    PerBuffer * perBuffer_p,
    UHandoverToUTRANCommand * msg_p);

void UrrRieProcessHandoverToUTRAN_r6_to_r9 (
	PerBuffer * perBuffer_p,
    UHandoverToUTRANCommand * msg_p);

void UrrRieProcessHandoverToUTRAN_r7_to_r9 (
	PerBuffer * perBuffer_p,
    UHandoverToUTRANCommand * msg_p);

void UrrRieProcessHandoverToUTRAN_r8_to_r9 (
	PerBuffer * perBuffer_p,
    UHandoverToUTRANCommand * msg_p);
	
#endif

UrrRxMsgVersion UrrRieProcessSysInfoType1 (
    PerBuffer * perBuffer_p,
    USysInfoType1 * sib1_p);

void UrrRieProcessSysInfoType16 (PerBuffer * perBuffer_p, USysInfoType16 * sib16_p);

/* Verify the received message against UE Capability */
Boolean UrrRieVerifyMsgVersion (
    UrrRxMsgVersion rxMsgVersion);

/* Extended Messages */
void UrrRieDdUPhysicalChannelReconfiguration_R7 (
    PerBuffer * perBuffer_p,
    UrrRxMsgVersion    rxMsgVersion,
    UPhysicalChannelReconfiguration *msg_p);


UMeasurementControl_latest_IEs * UrrRieGetUMeasurementControl_latest_IEs (
    const UMeasurementControl * msg_p);

URRCConnectionRelease_latest_IEs * UrrRieGetCcchURRCConnectionRelease_latest_IEs (
    const URRCConnectionRelease_CCCH * msg_p);

UAssistanceDataDelivery_r3_IEs * UrrRieGetUAssistanceDataDelivery_r3_IEs (
    const UAssistanceDataDelivery * msg_p);


void UrrRieSetRxMsgRcOk (Boolean val);

Boolean UrrRieGetRxMsgRcOk (void);


#endif /* !defined (URRRIE_H) */

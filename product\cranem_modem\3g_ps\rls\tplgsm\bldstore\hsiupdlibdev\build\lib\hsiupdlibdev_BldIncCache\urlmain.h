/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urlmain.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2007/03/22 15:29:52 $
 **************************************************************************
 * File Description:
 *
 *    Header file for URL.C. Contains function call declarations
 *    for use by other URLC modules.
 **************************************************************************/

#if !defined (URLMAIN_H)
#define       URLMAIN_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <system.h>
#include <gkitimer.h>
#include <ki_sigbuf.h>
#include <gkitask.h>
#include <gkisig.h>
#include <gkimem.h>
#include <url_sig.h>
#include <urltypes.h>
#include <urltmtx.h>
#include <urltmrx.h>
#include <urlumtx.h>
#include <urlumrx.h>
#include <urlam.h>
#include <urlDebugIf.h>
#include <urlstatistics.h>

#if !defined(ON_PC)
#include <ci_dev_engm.h>
#endif /* !ON_PC */


/***************************************************************************
 * Manifest Constants
 ***************************************************************************/
/* As there are no acknowledgedments for AM control PDUs, we can't be certain
 * that AM flow control messages (WINDOW SUFI) have been received by the
 * network, so it may be necassry to retransmit, after a guard period
 */
#define URL_FLOW_CONTROL_GUARD_TIME     MILLISECONDS_TO_TICKS(200)
/*fix cr CQ00001567 begin*/
//#define URL_FLOW_CONTROL_MAX_REPEATS    3
#define URL_FLOW_CONTROL_MAX_REPEATS    10
/*fix cr CQ00001567 end*/

#define URL_PENDING_UL_PS_BYTES_LWM           (45000)
#define URL_PENDING_UL_PS_BYTES_HWM           (60000)
#define URL_PENDING_UL_PS_BYTES_WM_DELTA      (URL_PENDING_UL_PS_BYTES_HWM-URL_PENDING_UL_PS_BYTES_LWM)
#define URL_PENDING_UL_PS_BYTES_APPS_DELAY    (30000)
#define URL_PENDING_UL_PS_BYTES_VHWM          (URL_PENDING_UL_PS_BYTES_HWM+URL_PENDING_UL_PS_BYTES_APPS_DELAY)
#define URL_MAX_ALLOWED_NO_TX_DATA_TTIS       (8) /* Must be power of 2 */
#define URL_NO_TX_DATA_TTIS_NUM_INC(a)        (a = ((a + 1) & (URL_MAX_ALLOWED_NO_TX_DATA_TTIS-1)))

#define URL_MAX_RTX_LIST_LEN                        (120) /*  Configured to support two full E-DCH (5.7mbps) TTIs, 
                                                              *  with an RLC PDU size of 336 bits */
#if defined (ENABLE_NEW_URLC_OPTIMISATIONS)		
#define URL_MAX_NACKED_UL_PDUS_HANDLE_PER_EDCH_TTI (60) /* Max number of RLC PDUs in E-DCH TTI: Round(20,000 / 336) */
#define URL_MAX_NACKED_UL_PDUS_HANDLE_PER_R99_TTI (24) /* Two times maximum number of RLC PDUs in R99 TTI: 2*Round(3840 / 336) */
#define URL_MAX_NACKED_UL_PDUS_HANDLE_INIT_VALUE URL_MAX_NACKED_UL_PDUS_HANDLE_PER_R99_TTI 
#endif /* ENABLE_NEW_URLC_OPTIMISATIONS */		

//#define URLC_INJECT_ERRORS
#ifdef UPGRADE_DSDSWB_L2 
extern Int8 UpActiveSimid;
extern Int8 RlcActiveSimid;
#define RLCSIMID (KiThisTask()== UP_RLC_TASK_ID)?0:1
#endif

/***************************************************************************
*   Macro Functions
***************************************************************************/
#ifdef UPGRADE_DSDSWB_L2 
/* Macro to provide a shortcut to a bearer TX entity */
#define URL_TX_ENTITY(b)                (urlGDB_gp->bearer [RLCSIMID][(b)].txEntity_p)
#define URL_TX_ENTITY_UP(b)            (urlGDB_gp->bearer [UpActiveSimid][(b)].txEntity_p)

/* Macros to provide a shortcut to a bearer TX entitiy data */
#define URL_TMTX_ENTITY_DATA(b)         (URL_TX_ENTITY(b)->data_p.tm)
#define URL_UMTX_ENTITY_DATA(b)         (URL_TX_ENTITY(b)->data_p.um)
#define URL_AMTX_ENTITY_DATA(b)         (URL_TX_ENTITY(b)->data_p.am)

#define URL_TMTX_ENTITY_DATA_UP(b)         (URL_TX_ENTITY_UP(b)->data_p.tm)
#define URL_UMTX_ENTITY_DATA_UP(b)         (URL_TX_ENTITY_UP(b)->data_p.um)
#define URL_AMTX_ENTITY_DATA_UP(b)         (URL_TX_ENTITY_UP(b)->data_p.am)


/* Macro to provide a shortcut to a bearer RX entity */
#define URL_RX_ENTITY(b)                (urlGDB_gp->bearer [RLCSIMID][(b)].rxEntity_p)
#define URL_RX_ENTITY_UP(b)                (urlGDB_gp->bearer [UpActiveSimid][(b)].rxEntity_p)


/* Macros to provide a shortcut to a bearer RX entitiy data */
#define URL_TMRX_ENTITY_DATA(b)         (URL_RX_ENTITY(b)->data_p.tm)
#define URL_UMRX_ENTITY_DATA(b)         (URL_RX_ENTITY(b)->data_p.um)
#define URL_AMRX_ENTITY_DATA(b)         (URL_RX_ENTITY(b)->data_p.am)

#define URL_TMRX_ENTITY_DATA_UP(b)         (URL_RX_ENTITY_UP(b)->data_p.tm)
#define URL_UMRX_ENTITY_DATA_UP(b)         (URL_RX_ENTITY_UP(b)->data_p.um)
#define URL_AMRX_ENTITY_DATA_UP(b)         (URL_RX_ENTITY_UP(b)->data_p.am)
#else
/* Macro to provide a shortcut to a bearer TX entity */
#define URL_TX_ENTITY(b)                (urlGDB_gp->bearer [(b)].txEntity_p)

/* Macros to provide a shortcut to a bearer TX entitiy data */
#define URL_TMTX_ENTITY_DATA(b)         (URL_TX_ENTITY(b)->data_p.tm)
#define URL_UMTX_ENTITY_DATA(b)         (URL_TX_ENTITY(b)->data_p.um)
#define URL_AMTX_ENTITY_DATA(b)         (URL_TX_ENTITY(b)->data_p.am)

/* Macro to provide a shortcut to a bearer RX entity */
#define URL_RX_ENTITY(b)                (urlGDB_gp->bearer [(b)].rxEntity_p)

/* Macros to provide a shortcut to a bearer RX entitiy data */
#define URL_TMRX_ENTITY_DATA(b)         (URL_RX_ENTITY(b)->data_p.tm)
#define URL_UMRX_ENTITY_DATA(b)         (URL_RX_ENTITY(b)->data_p.um)
#define URL_AMRX_ENTITY_DATA(b)         (URL_RX_ENTITY(b)->data_p.am)
#endif
/** Engineering mode opcode */
/** TODO: replace with values from ci_dev_engm.h */
#define URL_ENG_MODE_RESET				0x04000000
#define URL_ENG_MODE_RLC_STATISTICS 	0x02000000

/***************************************************************************
 * Types
 ***************************************************************************/

typedef struct UrlTxEntityTag
{
    UrlBearerMode       mode;
    union
    {
        UrlTmTxEntity     * tm;
        UrlUmTxEntity     * um;
        UrlAmTxRxEntity   * am;
    }                   data_p;
} UrlTxEntity;

typedef struct UrlRxEntityTag
{
    UrlBearerMode       mode;
    union
    {
        UrlTmRxEntity     * tm;
        UrlUmRxEntity     * um;
        UrlAmTxRxEntity   * am;
    }                   data_p;
} UrlRxEntity;

typedef struct UrlBearerTag
{
    UrlTxEntity       * txEntity_p;
    UrlRxEntity       * rxEntity_p;

    /* Indicates that the bearer has a pending configuration, with activation time now.
     * the configuration will be activated upon receival of MAC indication. */
    SignalBuffer        pendingActTimeNowConfig;

    /* Current debug filter for the radio bearer */
    UrlDebugBearerFilter    debugFilter;
} UrlBearer;

#ifdef ENABLE_NEW_URLC_OPTIMISATIONS_1
typedef struct UrlBearerfreqTag
{
	UrlBearerMode	  txmode;
	UrlBearerMode	  rxmode;
	Int8			  opMask;
} UrlBearerfreq;
#endif

/* Note: Attempts to use an array of arrays  */
typedef struct UrlCipherKeysTag
{
    USecurityKey key[UPS_KSI_ARRAY_SIZE];
} UrlCipherKeys;

typedef struct UrlEngInfoStatisticsTag
{
    Boolean             enable;
    KiTimer             statisticsTimer;
    KernelTicks         lastReportTick;     /* time since last report */
    Int32               reportTimerPeriodicity;
    Int32               totalDataSent;
    Int32               totalDataReceived;
    Int8                inaccuracyPercent;
} UrlEngInfoStatistics;

typedef struct UrlThroughputTag
{
    Boolean             enable;
    KiTimer             throughputTimer;
    KernelTicks         lastReportTick;     /* time since last report */
    Int32               reportTimerPeriodicity;
    Int32               totalDataSent;
    Int32               totalDataReceived;
    Int32               totalPduDataSent;
    Int32               totalPduDataReceived;
    Int8                inaccuracyPercent;
} UrlThroughput;


//ICAT EXPORTED STRUCT
typedef struct
{
    Int32               lwmBytes;
    Int32               hwmBytes;
    Int32               appDelayBytes;
} UrlUlDataWaterMarks;

//ICAT EXPORTED STRUCT
typedef struct
{
    Int32               pendingUlSdusBytes;
    Int32               requestedUlSdusBytes;
} UrlRequestedUlData;

/* Declare typedef for singly linked list carrying SignalBuffer */
UT_SINGLE_LINK_LIST_DECLARE (UrlSignalList, SignalBuffer)

#if defined(RLC_LOW_PRIOR_OPT)
/* Declare a single linked list of active Radio Bearers */
UT_SINGLE_LINK_LIST_DECLARE(UrlcActiveAmBearersList, UrlAmTxRxEntity*)
#endif
/***************************************************************************
 * Typed Constants
 ***************************************************************************/

typedef enum UrlTimerIdentitiesTag
{
    URL_TIMER_FLOW_CONROL_GUARD     = URL_AM_NUM_TIMERS,
    URL_TIMER_UL_TP_STATISTICS,
    URL_TIMER_ENG_INFO_STATISTICS,
    /*********** added 20110907 start*/
    URL_TIMER_PS_MONITOR,
    /*********** added 20110907 end*/
    URL_TIMER_THROUGHPUT,
	/*Fixed ********** 20130813 start*/
    URL_TIMER_BUFFER_FULL
/*Fixed ********** 20130813 end*/
    /* Add other timers */
} UrlTimerIdentities;

typedef enum UrlTmmWmStateTag
{
    URL_TMM_BELOW_LWM               = 0,
    URL_TMM_ABOVE_HWM               = 1,
    URL_TMM_ABOVE_VHWM              = 2
} UrlTmmWaterMarkState;

typedef  struct countCinfoTag
{
    UrlCountcInfo                   ul;
    UrlCountcInfo                   dl;
} countCinfo;

#if defined (DEVELOPMENT_VERSION)
typedef  struct crlcTestModeUlErrorsTag
{
    Int16        number;
    Int16        repeat;
} crlcTestModeUlErrorsT;

typedef  struct crlcTestModeDlErrorsTag
 {
     Int16        number;
     Int16        repeat;
 } crlcTestModeDlErrors;
#endif /* DEVELOPMENT_VERSION */


typedef struct edchConsumedBearerTag
{
    Int32           numOfBearers;
    BearerIdentity  usedBearersForEdch[RB_NUM_BEARERS];
} edchConsumedBearer;

typedef  struct UrlcDebugUlPduInfoMetaDataTag
{
    UrlcDebugUlPduInfo      debugPduInfo;
    dtcF8DescriptorInfo     *cipherInfo_p;
    Int8                    headerBits;
    Int16                   header;
    Boolean                 dataFiltered;
    Int32                   totalNumberOfDescriptorAllocated;
} UrlcDebugUlPduInfoMetaData;

typedef struct
{
    Boolean                 receivedPrepareCipherCfgChangeReq;
    UrlSequenceNumber       dlSn;
} PrepareCipherCfgChangeReq;

typedef struct UrlGDbTag
{
#ifdef UPGRADE_DSDSWB_L2 
    BearerIdentity          highestUsedBearer[SIM_NUM];
#else
    BearerIdentity          highestUsedBearer;
#endif
    CrlcTestModeParameters  testMode;
    countCinfo              counCinfo [RB_NUM_BEARERS];
#ifdef UPGRADE_DSDSWB_L2 
    UrlBearer               bearer [SIM_NUM][RB_NUM_BEARERS];
#else
    UrlBearer               bearer [RB_NUM_BEARERS];
#endif
    UrlCipherKeys           *urlCipherKeys_p;
    Boolean                 urlDfcIsFlowCntrlOn;
    edchConsumedBearer      consumedBearer;
    SignedInt32             UrlAmRxOosTmmAllocations;
    KiTimer                 urlDfcTimer;
#if defined (URLC_INJECT_ERRORS)
    crlcTestModeUlErrorsT   crlcTestModeUlErrors;
    crlcTestModeDlErrors    crlcTestModeDlErrors;
#endif // URLC_INJECT_ERRORS
    Boolean etfcSelection;
#if defined (UPGRADE_3G_EDCH)
#if !defined (URL_DEBUG_EDCH_OVER_DTC)
    UrlcDebugUlPduInfoMetaData debug[URL_MAX_EDCH_PDUS_PER_TTI];
#else
    UrlDebugEdch            debugEdch;
#endif //URL_DEBUG_EDCH_OVER_DTC
#endif //UPGRADE_3G_EDCH
    KernelTicks             maxDiffTicks;
#if defined (UPGRADE_3G_HSDPA) && defined (URL_DEBUG_HSDPA_OVER_DTC)
    UrlDebugHs              debugHs;
#endif //UPGRADE_3G_HSDPA && URL_DEBUG_HSDPA_OVER_DTC

    UrlRbsDebug                 debugRbs;

    PrepareCipherCfgChangeReq   receivedCipherConfiguration;
    UrlEngInfoStatistics        urlEngInfoStatistics;
    UrlThroughput               urlThroughput;
    UrlcDebugStatistics         urlcDebugStatistics;
    Boolean                     diagConnected;
    Boolean                     isRlcDebugEncodedDiagFiltered;
    SignedInt32                 remainExpectedUlPsDataBitsFromApp;

#if defined (URLC_TASK_FLOW_PROTECT) && (URLC_TASK_FLOW_EXTENDED_DEBUG)
    UrlTaskFlowSemaDebugRecord  taskFlowSemaDebugArray[URL_SEMA_NUM_CALLS];
#endif //URLC_TASK_FLOW_PROTECT && URLC_TASK_FLOW_EXTENDED_DEBUG
#if !defined(RLC_LOW_PRIOR_OPT)
    Boolean                     amDlSduToDeliver;
#endif
    BearerIdentity              nextAmRbForAmDataInd;
#if defined (ENABLE_NEW_URLC_OPTIMISATIONS)		
	Int32						remainNackedUlPdusToHandleInCurTti;
#endif /* ENABLE_NEW_URLC_OPTIMISATIONS */		
	Boolean						ackOrNackReceived;
#ifdef UPGRADE_DSDSWB_L2 
       BearerIdentity                   lastAmRbRecievedInDataInd[SIM_NUM];
#else
	BearerIdentity				lastAmRbRecievedInDataInd;
#endif
	Boolean                     crlcPsDataReqReceived;
#if defined(RLC_LOW_PRIOR_OPT)
    UrlcActiveAmBearersList     activeAmBearersList;
#endif
	/*********** merged for FRBD 20140806 start*/

#if !defined (ON_PC)
    /** Engineering mode info */
    Boolean                     activePsDomain;
    Boolean                     psDomainRb[RB_BCCH];

    /** Engineering mode filter */
    Boolean                     rlcResetEnable;
    Boolean                     rlcStatisticsEnable;
#endif /* !ON_PC */

#if defined (UPGRADE_UL_ECF)
    Boolean                     umacEdchUlDataUpdateReceived;
#endif
/*********** merged for FRBD 20140806 end*/
} UrlGDb;

/***************************************************************************
 * Function Prototypes
 ***************************************************************************/

#if defined (ON_PC)
extern  void
UpRlcTaskExitRoutine
    (void
    );
extern  void
UpRlcBgTaskExitRoutine
    (void
    );
#endif

extern  void
UrlPassSignalToBearerTxEntity
    (BearerIdentity                                     b,
     SignalBuffer                                     * rxSignal_p
    );

extern void
UmacDlPduListInfoIndFn
    (UmacDlPduListInfoInd                             * umacDlPduListInfoInd_p
    );
#if defined (UPGRADE_3G_HSDPA)
extern void
UmacHsPduListInfoIndFn
    (UmacHsPduListInfoInd                             * umacHsPduListInfoInd_p
    );

extern void
UmacEhsPduListInfoIndFn
    (UmacEhsPduListInfoInd                             * umacEhsPduListInfoInd_p
    );
#endif /* UPGRADE_3G_HSDPA */


#if defined(UMTS7_PRE_R8)
extern void UrlAmTxUrlcAmDataReqFn(UrlcAmDataReq * urlcAmDataReq_p);
extern void UrlUmTxUrlcUmDataReqFn(UrlcUmDataReq * urlcUmDataReq_p);
extern void UrlTmTxUrlcTmDataReqFn(UrlcTmDataReq * urlcTmDataReq_p);
extern void UrlPsDataRsp(UrlcPsDataRsp* urlcPsDataRsp_p);
#endif

#if defined(UMTS7_PRE_R8)&& !defined(ENABLE_URLC_UNIT_TEST)

extern void ulbgprocessPsUlSduDiscardInd(UrlcUlSduDiscardInd *urlcUlSduDiscardInd);

#endif
#if !defined (ENABLE_UP_MIPS_TEST)
KI_ENTRY_POINT UpRlcTask (void);
#endif
#if !defined (ENABLE_URLC_UNIT_TEST)
KI_ENTRY_POINT UpRlcBgTask (void);
#endif /* ENABLE_URLC_UNIT_TEST */

extern  void
UrlDestroySignal
    (SignalBuffer                                     * rxSignal_p
    );

/* Down Link Flow Control Module */
extern void
UrlDfcStartTimer
    (void
    );
extern void
UrlDfcStopTimer
    (void
    );
extern Boolean
UrlDfcIsFlowCntrlOn
    ( void
    );
extern UrlTmmWaterMarkState
UrlDfcGetWaterMarkState
    ( void
    );
extern void
UrlRequestNewUlPsDataIfNeeded
    (void
    );
#if defined (UPS_ENABLE_RLC_DL_SIGNAL_FLOW_CNTRL)
/* Down Link Signal Flow Control Module */
extern Boolean
UrlDsfcIsFlowSuspended
    (void
    );
Boolean
UrlDsfcIncSigTxCount
    (void
    );
#endif /* UPS_ENABLE_RLC_DL_SIGNAL_FLOW_CNTRL */

#if defined (ENABLE_UL_THROUGHPUT_IND)
extern void
UrlCalcUlPdcpBearersNum
    (void
    );
#endif /* ENABLE_UL_THROUGHPUT_IND */

#if defined (UPGRADE_3G_EDCH)
extern BearerIdentity
UrlUmacEdchTrafficInd
    (UmacEdchTrafficInd  *UmacEdchTrafficInd_p
    );
#endif /* UPGRADE_3G_EDCH */

void UrlSendCrlcNotifyPsDataInd(void);
#if !defined (FALCON_1803_PLATFORM)  && !defined (PS_1802)
/* CI function used by RLC to set the uplink data bytes to be transmitted from apps */
extern void  CI_DataReqMaxLimitSet_REQ_CNF(unsigned long newMaxLimit);

/* CI function used by RLC to retrieve to pending uplink data bytes in apps queue */
extern Int32 CI_GetTotalPendingBuffersValue(void);
#endif
extern void UrlAmTxSduDiscardForRrcReselection (void);

/***************************************************************************
 * Global Variables
 ***************************************************************************/
extern UrlGDb           *urlGDB_gp;
#if defined (ON_PC)
extern Int32            urlDataBaseMutexHandle;
#else
extern UrlCipherKeys    urlCipherKeysTable_g;
#endif /* ON_PC */

#endif /* URLMAIN_H */


/**
 * @file lv_watch.c
 *
 */

/*********************
 *      INCLUDES
 *********************/
#include "lv_watch.h"
#include "../../board.h"
#if USE_TOUCH_TRACKER
#include "../lv_drivers/indev/touch_tracker.h"
#endif

#if USE_WATCH_SPORT
//can do something only for watch_aport
#endif

#if USE_LV_WATCH != 0

#include <stdio.h>

// 包含WiFi扫描测试接口头文件
#include "ke/ke_location_flow.h"

// WiFi扫描测试延迟任务回调函数
static void wifi_scan_test_delayed_task(lv_task_t * task)
{
    printf("[WIFI_TEST] System startup completed, starting WiFi scan test...\n");

    // 调用WiFi扫描测试接口
    Flw_WiFi_Scan_Test_Interface();

    // 删除一次性任务
    lv_task_del(task);
}

/*********************
 *      DEFINES
 *********************/
#ifndef USE_POWER_ON_GIF
#define USE_POWER_ON_GIF 0
#endif

/**********************
 *      TYPEDEFS
 **********************/
static watch_info_t * watch_info;
lv_task_t * lowbattery_task_p = NULL;

#if USE_LV_WATCH_SIMCHECK
lv_task_t * checksim_task_p = NULL;
#endif
static MMI_MODEM_PIN_STATUS_TYPE pintype = MMI_MODEM_PIN_DISABLED;
static bool watch_ui_wake_up_flag = false; // wake up flag other than key and alarm
static lv_task_t * watch_ui_wake_up_task = NULL;
bool gcf_test = false;
static lv_task_t * monitor_task = NULL;
static uint8_t suspend_reason = WATCH_SUSPEND_UI;
static bool is_wakeup_lcd = true;
#if USE_LV_WATCH_SD_AUTO_MOUNT != 0
static lv_task_t * sd_mount_task = NULL;
static int usb_conn_status = 0;
#endif
typedef struct {
    lv_watch_Activity_Id_t act;
    bool top;
}lv_watch_suspend_t;

static bool is_charge_powerup = false;

//lyj add for merge noscreen and screen
extern int gui_is_lcd_valid(void);

static lv_ll_t suspend_ll;

/**********************
 *  STATIC PROTOTYPES
 **********************/
#if USE_POWER_ON_GIF != 0
static void main_task(lv_task_t * param);
#endif
static lv_obj_t * watch_create(bool silentReset);
static void watch_powerup_onkey_common_process(void);
static void watch_powerup_alarm_common_process(void);
static void watch_powerup_silent_reset_process(void);
static void watch_powerup_charging_common_process(void);
static void watch_start_activity_monitor_task(void);
static void watch_shut_down(hal_rtc_t * poweron_time);
static void watch_normal_charging_status_change(VOID * para);
#if USE_LV_WATCH_SD_AUTO_MOUNT != 0
static void watch_start_sd_mount_task(void);
#endif
static void watch_suspend_mng_init(void);
static bool watch_get_suspend_enable(void);
#if USE_LV_WATCH_SPAIN!=0
bool get_eco_enable();
int get_eco_period();
bool eco_enable=false;
uint8_t eco_period=60;
#endif
/**********************
 *  STATIC VARIABLES
 **********************/

static lv_task_t * main_task_p;

static bool watch_ready = false;
static bool watch_lcd_status = true;  // true means wakeup, false means sleep
static bool watch_sleep_lcd_only = false;
static bool watch_silent_reset = false;
static bool watch_ambient = false;
static bool watch_alarm_flag = false;
#if USE_LV_WATCH_CT_KEY_TOHOME_OR_BACK!=0
bool gKeyWakeup = false;
#endif

#if USE_LV_WATCH_NAV_KEY_UNLOCK !=0 || (USE_LV_WATCH_TTS != 0) ||(USE_LV_WATCH_FZH_WAKEUP_TO_STU!=0)
static bool watch_launcher_ready = false;
bool watch_is_launcher_ready(void)
{
    return watch_launcher_ready;
}

void watch_set_launcher_ready_state(bool ready_flag)
{
    watch_launcher_ready = ready_flag;
}
#endif

#if (USE_LV_WATCH_WS_CT != 0)
static bool ws_poweron_silent_reset = false;
bool ws_poweron_is_silent_reset()
{
	return ws_poweron_silent_reset;
}
#endif

/**********************
 *      MACROS
 **********************/

/**********************
 *   GLOBAL FUNCTIONS
 **********************/
void watch_restart_req(void)
{

    lv_obj_t * activity_obj = lv_watch_get_activity_obj(ACT_ID_POWER_OFF_CHARGING);

    if(activity_obj) {
		watch_set_ready_state(false);
        lv_obj_t * watch_obj;
        lv_power_off_charging_ext_t * charging_ext = NULL;

        lv_watch_get_child_obj(activity_obj, lv_watch_obj_signal, &watch_obj);
        if(watch_obj) charging_ext = lv_obj_get_ext_attr(watch_obj);
        if(NULL == charging_ext) return;
        if(NULL != charging_ext->battery_task) {
            lv_task_del(charging_ext->battery_task);
            charging_ext->battery_task = NULL;
        }
        lv_watch_png_cache_all_free();

        // delete activity
        lv_watch_activity_ext_t * ext = lv_obj_get_ext_attr(activity_obj);
        if(NULL != ext) {
            ext->prepare_destory(activity_obj);
            lv_obj_del(activity_obj);
            watch_powerup_onkey_common_process();

#if USE_LV_WATCH_MODEM_ADAPTOR
            if(!setting_is_flying_mode()) {
                watch_modem_power_up_req();
            }
#endif
        } else {
            printf("ext is NULL in watch_restart_req!\n");
        }
    }else if (is_power_off_alarm_wating()){
			watch_set_ready_state(false);
			stop_alarm_rering_timer();
			watch_powerup_onkey_common_process();
		#if USE_LV_WATCH_MODEM_ADAPTOR
			watch_modem_power_up_req();
		#endif
    } else {
#if 0
        sos_btn_action(NULL);
#endif
    }
}

bool charge_powerup_status(void)
{
    return is_charge_powerup;
}

void watch_power_on_req(void)
{
    HAL_POWERUP_REASON reason = Hal_Powerup_Get_Reason();
	uint16_t Bat_Volt = Hal_Battery_Get_Real_Vol();
	//cplog_printf("watch_power_on_req1111, Bat_Volt=%d\n",Bat_Volt);
	// PM803 need to wait to read bat volt
	if(Pmic_is_pm803()){
		uint8_t cnt = 0;
		while (Bat_Volt == 0 && cnt < 6){
			uos_sleep(120);  
			Bat_Volt = Hal_Battery_Get_Real_Vol();
			//cplog_printf("watch_power_on_req, Bat_Volt=%d, cnt=%d\n",Bat_Volt,cnt);
			cnt++;
		}
	}
	
	//cplog_printf("watch_power_on_req22222, Bat_Volt=%d\n",Bat_Volt);
	if (Bat_Volt < 3400 && !(HAL_POWERUP_BAT == reason || HAL_POWERUP_USB == reason))
	{
		uos_sleep(600);
		Bat_Volt = Hal_Battery_Get_Real_Vol();
		if (Bat_Volt < 3400){
			Hal_Backlight_Off();
			pmic_sw_pdown();
			return;
		}
	}

#if USE_LV_THEME_WATCH_NIGHT != 0
    lv_theme_t * th = LV_THEME_WATCH_NIGHT_INIT();
    lv_theme_set_act(th);
#endif

    UI_NV_Restroe_Req();

    setting_init();
    phone_init();
    alarm_init();
#if USE_LV_WATCH_SPAIN != 0
	init_date_and_timezone();
#endif
    phonebook_init();
#if USE_LV_WATCH_XF_SLEEP!= 0
	sleep_time_init();
#endif
	#if USE_LV_WATCH_LOCK_ICCID != 0
	myNVWatch_Get_Cur_ICCID();
	#endif
#if USE_LV_WATCH_VIDEOCALL
    voip_call_init();
#endif
#if USE_LV_WATCH_CALLLOG != 0 && USE_LV_WATCH_UNREADCALL_IMG != 0
	init_unread_call_numbers();
#endif
    app_adaptor_shutdown_bind(watch_shut_down);
    Hal_Register_Low_Power_Ind(watch_low_power_ind_cb);
    Hal_Set_Voice_Call_Status(VOICE_CALL_END);

#if USE_LV_WATCH_MODEM_ADAPTOR
    watch_modem_adp_init_req();

#if USE_LV_WATCH_MOBILE_NETWORK
    MMI_Modem_Apn_Info_t *apn_info = mobile_network_get_apn_info();
    watch_modem_set_apn_info(apn_info);
#endif

    bool power_up_req = true;
#if USE_LV_WATCH_USB_POWERON != 1
    if(HAL_POWERUP_BAT == reason || HAL_POWERUP_USB == reason) {
        power_up_req = false;
    }
#endif

    if(power_up_req && !setting_is_flying_mode()) {
        watch_modem_power_up_req();
    }
#endif
#if USE_LV_WATCH_ALIPAY != 0
	alipay_init();
#else
	XF_alipay_init();
#endif

#if USE_LV_WATCH_SD_AUTO_MOUNT != 0
    usb_conn_status = Hal_Get_Usb_Status();
#endif
#if USE_LV_WATCH_KAER_SETTINGS != 0
{
	extern void Ke_SettingsInit(void);
	
	printf("Enter %s,reson=%d",__FUNCTION__,reason);
	Ke_SettingsInit();	
}	
#endif

    watch_suspend_mng_init();
    if((HAL_POWERUP_ONKEY == reason) || (HAL_POWERUP_REBOOT == reason)) {
        // unknow is for soft reboot
        watch_powerup_onkey_common_process();
    } else if(HAL_POWERUP_RTC_ALARM == reason) {
#if USE_LV_WATCH_CYCLE_POWERON_CLOCK_ARRAY != 0
		if(alarm_is_poweron_clock()==1)
		{
		// no insert usb, excute here,is power on clock,
			alarm_set_poweron_clock(0);
			watch_powerup_onkey_common_process();
		}
		else
#elif USE_LV_WATCH_SCHEDULE_POWERON_OFF!=0 || USE_LV_WATCH_SETTING_POWERON_OFF !=0
		if(alarm_in_poweroff_type_is_shecdule()==ALARM_TYPE_POWERON){
			set_alarm_make_schedule_poweronoff_silent(true);
			watch_powerup_onkey_common_process();
		}
		else if(alarm_in_poweroff_type_is_shecdule()==ALARM_TYPE_POWEROFF){
			set_alarm_make_schedule_poweronoff_silent(true);
			Hal_Power_Off(HAL_TYPE_POWER_OFF);
		}
		else
#endif
		{
        watch_powerup_alarm_common_process();
		}
    } else if(HAL_POWERUP_SILENT_RESET == reason) {
        watch_silent_reset = true;
#if (USE_LV_WATCH_WS_CT != 0)
		ws_poweron_silent_reset = true;
#endif
        watch_powerup_silent_reset_process();
    }
#if 1//USE_LV_WATCH_POWER_OFF_CHARGING != 0
    else if(HAL_POWERUP_BAT == reason || HAL_POWERUP_USB == reason) {
	#if USE_LV_WATCH_USB_POWERON != 0
		watch_powerup_onkey_common_process();
	#else			
		watch_powerup_charging_common_process();
	#endif	
    }
#endif
    else {
        printf("error mode %d in watch_power_off_req!\n", reason);
    }

    // 创建延迟任务，在系统完全启动后执行WiFi扫描测试
    // 延迟10秒确保所有系统组件都已初始化完成
    lv_task_t * wifi_test_task = lv_task_create(wifi_scan_test_delayed_task, 10000, LV_TASK_PRIO_LOW, NULL);
    if(wifi_test_task) {
        lv_task_once(wifi_test_task);
        printf("[WIFI_TEST] WiFi scan test task scheduled for 10 seconds delay\n");
    } else {
        printf("[WIFI_TEST] Failed to create WiFi scan test task\n");
    }
}

void watch_default_set(void)
{
#if LV_USE_MULTI_LANG
    lv_lang_set_text_func(lang_get_text_by_id);
#endif
    /*set background image*/
#ifndef USE_WATCH_LITE_NO_BG_IMG
    watch_info->bg = ICON_BACKGROUND;
#else
    watch_info->bg = "";
#endif
#if USE_XPHONE_MAINMENU_EFFECT != 0 || USE_XPHONE_MAINMENU_EFFECT2 != 0
    main_menu_read_type();
#endif 	
}

void * watch_get_bg(void)
{
    if(watch_info == NULL)
        return NULL;
    return watch_info->bg;
}

void * watch_set_bg(const void * src)
{
    watch_info->bg = (void *)src;
    return watch_info->bg;
}
watch_dial_type_t read_dial_type(void)
{
    //read file system to know dial type
    //UI_NV_Read_Req(NV_SECTION_UI_LAUNCHER, 0, sizeof(uint8_t), &(watch_info->dailtype));
    return watch_info->dailtype;
}
void watch_set_dial_type(watch_dial_type_t dial_type)
{
    //read file system to know dial type
     watch_info->dailtype = dial_type;
}
#if USE_XPHONE_MAINMENU_EFFECT != 0 || USE_XPHONE_MAINMENU_EFFECT2 != 0
void watch_set_main_menu_type(uint8_t dial_type)
{
    //read file system to know dial type
     watch_info->main_menu_type = dial_type;
}


uint8_t watch_get_main_menu_type(void)
{
    if(watch_info == NULL)
        return 0;

     return watch_info->main_menu_type;
}
#else
uint8_t watch_get_main_menu_type(void)
{
    return 0;
}
#endif 

#if LV_WATCH_SIMULATOR
void show_shutdown_screen(void * para)
{
    lv_obj_t * shutdown_screen = lv_obj_create(lv_layer_top(), NULL);
    lv_obj_set_size(shutdown_screen, LV_HOR_RES, LV_VER_RES);
    lv_obj_set_style_local_bg_color(shutdown_screen, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_BLACK);
}

void power_key(struct _lv_obj_t * obj, lv_event_t event)
{
    if(event == LV_EVENT_CLICKED) {
        printf("this is power key\n");
        lv_watch_go_home();
    } else if (event == LV_EVENT_LONG_PRESSED) {
        shutdown_confirm_btn_action(NULL, LV_EVENT_CLICKED);
        Hal_Timer_Start(1000, show_shutdown_screen, NULL, 0);
    }
}

void option_key(struct _lv_obj_t * obj, lv_event_t event)
{
    if(event == LV_EVENT_CLICKED) {
        printf("this is option key\n");
        setting_create_event_cb(NULL, LV_EVENT_CLICKED);
    }
}
#endif

void watch_poweron_logo(void)
{
    lv_obj_t * activity_obj = NULL;
    lv_watch_activity_ext_t activity_ext;
    memset(&activity_ext, 0, sizeof(lv_watch_activity_ext_t));
    activity_ext.actId = ACT_ID_POWON_LOGO;
    activity_ext.create = NULL;
    activity_ext.prepare_destory = NULL;
    activity_obj = lv_watch_creat_activity_obj(&activity_ext);
    lv_obj_set_style_local_border_side(activity_obj, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_BORDER_SIDE_NONE);
    LV_ASSERT_MEM(activity_obj);

    lv_obj_t * poweron_logo = lv_img_create(activity_obj, NULL);
    lv_img_set_src(poweron_logo, ICON_POWERON_LOGO);
    lv_obj_align(poweron_logo, NULL, LV_ALIGN_IN_TOP_LEFT, 0, 0);
    lv_refr_now(NULL);

#if LV_WATCH_SIMULATOR
    extern bool simulator_begin;

    lv_coord_t x_offset = (LV_HOR_RES - LV_HOR_RES_MAX)/2;
    lv_coord_t y_offset = (LV_VER_RES - LV_VER_RES_MAX)/2;

    lv_obj_t * top = lv_img_create(lv_layer_sys(), NULL);
    lv_img_set_src(top, ICON_WATCH_SIMULATOR);
    lv_obj_set_pos(top, 0, 0);

    lv_obj_t * btn_power =  lv_btn_create(top, NULL);
    lv_obj_set_pos(btn_power,  LV_HOR_RES - x_offset, LV_VER_RES_MAX/4);
    lv_obj_set_size(btn_power, LV_HOR_RES/4, LV_HOR_RES/2);
    lv_obj_set_event_cb(btn_power, power_key);
    lv_obj_set_style_local_bg_opa(btn_power, LV_BTN_PART_MAIN, LV_STATE_DEFAULT, LV_OPA_0);
    lv_obj_set_style_local_border_opa(btn_power, LV_BTN_PART_MAIN, LV_STATE_DEFAULT, LV_OPA_0);
    lv_obj_set_style_local_outline_width(btn_power, LV_BTN_PART_MAIN, LV_STATE_DEFAULT, 0);

    lv_obj_t * btn_option = lv_btn_create(top, NULL);
    lv_obj_set_size(btn_option, LV_HOR_RES/4, LV_HOR_RES/2);
    lv_obj_align(btn_option, btn_power, LV_ALIGN_OUT_BOTTOM_MID, 0, 0);
    lv_obj_set_event_cb(btn_option, option_key);
    lv_obj_set_style_local_bg_opa(btn_option, LV_BTN_PART_MAIN, LV_STATE_DEFAULT, LV_OPA_0);
    lv_obj_set_style_local_border_opa(btn_option, LV_BTN_PART_MAIN, LV_STATE_DEFAULT, LV_OPA_0);
    lv_obj_set_style_local_outline_width(btn_option, LV_BTN_PART_MAIN, LV_STATE_DEFAULT, 0);
    lv_refr_now(NULL);

    simulator_begin = true;
    lv_obj_set_pos(top, x_offset, y_offset);
#endif
}

static void watch_poweron_logo_del(void)
{
    lv_obj_t * activity_obj = lv_watch_get_activity_obj(ACT_ID_POWON_LOGO);
    if(activity_obj) {
        lv_obj_del(activity_obj);
        lv_img_cache_invalidate_src(ICON_POWERON_LOGO);
    }
}

void watch_low_power_ind_cb(void * para)
{
    (void)para;
    printf("%s\n", __FUNCTION__);

#if !FAKE_BATTERY
    if(0 == Hal_Pm_Get_State()) {
        Hal_Pm_WakeUp();
    } else {
        watch_wakeup_lcd(true);
    }
#if USE_LV_WATCH_LOWBATTERY != 0  
	lv_task_ready(lowbattery_task_p);
#endif
#endif
}

void watch_low_power_ind_proc(uint32_t bat_percent)
{
    (void)bat_percent;

    watch_shut_down(NULL);
}

/**********************
 *   STATIC FUNCTIONS
 **********************/
static void watch_shut_down(hal_rtc_t * poweron_time)
{
#if USE_LV_WATCH_VOICE_MSG != 0
    voice_msg_shut_down();
#endif /* USE_LV_WATCH_VOICE_MSG */
    alarm_shutdown(poweron_time);
}

static void watch_powerup_onkey_common_process(void)
{
#if USE_POWER_ON_GIF != 0
    if(HAL_POWERUP_REBOOT == Hal_Powerup_Get_Reason()){
		main_task_p = lv_task_create(main_task, 6000, LV_TASK_PRIO_MID, NULL);
    }else{
#if defined(USE_WATCH_LITE)
        main_task_p = lv_task_create(main_task, 2000, LV_TASK_PRIO_MID, NULL);
#else
        main_task_p = lv_task_create(main_task, 3500, LV_TASK_PRIO_MID, NULL);
#endif
    }
#else /*USE_POWER_ON_GIF*/
    watch_create(false);
#if USE_LV_WATCH_MODEM_ADAPTOR
    watch_message_handle_init();
#endif
#if USE_LV_WATCH_ENGINEER_MODE != 0
    watch_modem_pin_status_change_ind(watch_get_pintype());
#endif
#endif /*USE_POWER_ON_GIF*/

#if USE_LV_DM != 0
#if USE_LV_WATCH_NO_LINK_NET != 1
{
	if(ke_GetAutoRegisterState() != 1)
	{
	    WS_PRINTF("AutoRegister is close ");
	    return;
	}
	//telecom dm start
    extern void dm_start(void);
    dm_start();	
		//china mobile dm init
#if USE_LV_WATCH_CHINA_MOBILE_DM != 0
	extern void dm_init();
	dm_init();
#endif
}
#endif
#endif
}

static void alarm_watch_delay(void * para)
{
#if USE_LV_WATCH_MODEM_ADAPTOR
	watch_message_handle_init_for_charging();
#endif
	poweroff_alarm_ring(0);
	watch_set_ready_state(true);
	watch_start_activity_monitor_task();
}

static void watch_powerup_alarm_common_process(void)
{
    watch_create(false);
	
#if (USE_LV_WATCH_WS_CT != 0)
	wsMuteTime_Check_For_Powerup(); /*read from nv to check mute state*/
#endif

    /* play alarm tone */
#if USE_LV_WATCH_ALLVOL_SET != 0
	Hal_NFFS_File_Play_Repeat(AUDIO_ALARM, query_current_volume(AUDIO_ALARM_VOL));
#else
    Hal_NFFS_File_Play_Repeat(AUDIO_ALARM, query_current_volume());
#endif
    /* display alarm animation */
	Wakeup_GuiTask(true);
    alarm_create(NULL);

#if USE_LV_WATCH_MODEM_ADAPTOR
    watch_message_handle_init();
#endif
}

static void watch_powerup_silent_reset_process(void)
{
    watch_create(true);
#if USE_LV_WATCH_MODEM_ADAPTOR
    watch_message_handle_init();
#endif
	watch_modem_pin_status_change_ind(watch_get_pintype());

#if USE_LV_DM != 0
#if USE_LV_WATCH_NO_LINK_NET != 1
{
	if(ke_GetAutoRegisterState() != 1)
	{
	    WS_PRINTF("AutoRegister is close ");
	    return;
	}
	//telecom dm start
    extern void dm_start(void);
    dm_start();	
		//china mobile dm init
#if USE_LV_WATCH_CHINA_MOBILE_DM != 0
	extern void dm_init();
	dm_init();
#endif
}
#endif
#endif
}

static void watch_powerup_charging_common_process(void)
{
#if USE_LV_WATCH_MODEM_ADAPTOR
    watch_message_handle_init_for_charging();
#endif

    power_off_charging_create(NULL);
    watch_set_ready_state(true);
    watch_start_activity_monitor_task();
}

#if USE_POWER_ON_GIF != 0
gif_info_t * power_on_gif = NULL;

static void poweron_play_callback(void * result)
{
    //printf("set AudioHAL_SetResBufCnt back to 4\n");
    AudioHAL_SetResBufCnt(4);
}
#if USE_LV_WATCH_POWERONOFF_PIC != 0
static uint8_t g_is_poweron = 0;
#endif
static void main_task(lv_task_t * task)
{
    lv_obj_t * activity_obj = NULL;
	uint8_t volume;
	#if USE_LV_WATCH_ALLVOL_SET != 0
	volume=HAL_AUDIO_SPK_LEVEL_7;
	#else
	volume=query_current_volume();
	#endif
//    LV_AUDIO_DECLARE(audio_power_on);
    lv_task_set_period(main_task_p, 1000);
#if USE_LV_WATCH_POWERONOFF_PIC != 0
	if(!g_is_poweron)
#else
    if(!power_on_gif) 
#endif
	{
        lv_watch_activity_ext_t activity_ext;
        memset(&activity_ext, 0, sizeof(lv_watch_activity_ext_t));
        activity_ext.actId = ACT_ID_ONOFF_GIF;
        activity_obj = lv_watch_creat_activity_obj(&activity_ext);
        if(activity_obj == NULL) return;
        //lv_obj_set_style(activity_obj, &lv_style_plain);
		#if USE_LV_WATCH_POWERONOFF_PIC != 0
		g_is_poweron = 1;
		lv_obj_t * poweron_logo = lv_img_create(activity_obj, NULL);
	    lv_img_set_src(poweron_logo, ICON_POWERON_LOGO);
	    lv_obj_align(poweron_logo, NULL, LV_ALIGN_IN_TOP_LEFT, 0, 0);

		#else
        power_on_gif = gif_open(activity_obj, ANIM_POWER_ON, 1);
		#endif

        /*fix: when power on, audio is very stuck when ril init*/
        //printf("set AudioHAL_SetResBufCnt 20\n");
        AudioHAL_SetResBufCnt(20);
		
		#if (USE_LV_WATCH_WS_CT != 0)
		wsMuteTime_Check_For_Powerup(); /*read from nv to check mute state*/
		#endif
		
	#if USE_LV_WATCH_SCHEDULE_POWERON_OFF_SILENT!=0
		if(get_alarm_schedule_poweronoff_is_silent())
		{
			set_alarm_make_schedule_poweronoff_silent(false);
			volume=HAL_AUDIO_SPK_MUTE;
		}
	#endif	
		
    #if USE_POWER_ON_VOICE_AUDIO != 0
        Hal_NFFS_File_Play_Onetime(AUDIO_POWER_ON, (HAL_SPEAKER_GAIN)volume, poweron_play_callback, 0);
	#elif (USE_LV_WATCH_POWERON_OFF_SILENT != 0)
		printf("power on not play music\n");
	#elif (USE_LV_WATCH_WS_CT != 0)
			if(!WatchUtility_IsMuteTime()) 
			{
				Hal_NFFS_File_Play_Onetime(AUDIO_POWER_ON, (HAL_SPEAKER_GAIN)volume, poweron_play_callback, 0);
			}
	
    #elif USE_LV_WATCH_WS_KAER != 0
	////////////here power on
		if(ke_fota_get_update_status() == 2)
		{
			ke_fota_set_start_flag(0,NULL);
		}
		else if(ke_get_except_reboot_status() == 1)
		{
			ke_set_except_reboot_state(0);
		}
		else
		{
			wsMuteTime_Check_For_Powerup();
			if(!WatchUtility_IsMuteTime())
			{
			#if USE_LV_WATCH_KEY_KW19 != 1
				if((Hal_Powerup_Get_Reason() != HAL_POWERUP_SILENT_RESET) && (WatchUtility_is_night_mute_time() == false))
		        	Hal_NFFS_File_Play_Onetime(AUDIO_POWER_ON, (HAL_SPEAKER_GAIN)volume, poweron_play_callback, 0);
			#endif
			}
		}
    #else 
        Hal_NFFS_File_Play_Onetime(AUDIO_POWER_ON, (HAL_SPEAKER_GAIN)volume, poweron_play_callback, 0);
    #endif

    #if USE_LV_WATCH_PONOFF_VIB != 0
		#if defined(__XF_PRO_A89__)		
		Hal_Vibrator_Play_Onetime(NULL, 200);
		#else
		Hal_Vibrator_Play_Onetime(NULL, 1600);
		#endif
    #endif
	#if USE_LV_WATCH_A17_LEDEFFECT!=0
		led_light_on_for_poweronoff();
	#endif
	
		lv_task_set_period(main_task_p, 500);
    }

	#if USE_LV_WATCH_POWERONOFF_PIC != 0
	if(g_is_poweron && Hal_Tone_Play_Finished()==1)
	#else
    if(power_on_gif->once_done && Hal_Tone_Play_Finished()==1) 
	#endif
	{
		#if USE_LV_WATCH_POWERONOFF_PIC != 0 
		g_is_poweron = 0;
		#endif
        gif_close(power_on_gif);
        activity_obj = lv_watch_get_activity_obj(ACT_ID_ONOFF_GIF);
        if(activity_obj) {
            lv_obj_del(activity_obj);
			#if USE_LV_WATCH_POWERONOFF_PIC != 0
			lv_img_cache_invalidate_src(ICON_POWERON_LOGO);
			#endif
        }
		
        power_on_gif = NULL;
        watch_create(false);
#if USE_LV_WATCH_MODEM_ADAPTOR
        watch_message_handle_init();
#endif
#if USE_LV_WATCH_ENGINEER_MODE != 0
        watch_modem_pin_status_change_ind(watch_get_pintype());
#endif

#if USE_WATCH_FACE_UNLOCK != 0
		if (get_faceunlock_state())
			watch_password_create(NULL, CHECK_PASSWORD, NULL, NULL);
#endif
#if USE_LV_WATCH_HELP_GUIDE != 0
	if (!get_first_guide_state()){
		help_guide_create_btn_action(NULL,LV_EVENT_CLICKED);
	}
#endif

	// heroengine start
#ifdef FEATURE_HERO_ENGINE_APP
	hero_timer_init_task();
#endif
	// heroengine end

        lv_task_del(main_task_p);
    }
}
#endif

#if USE_LV_WATCH_SIMCHECK
static void simcheck_task(lv_task_t * param)
{
    //no sim and no emergency call is ongoing
    if((false == watch_modem_sim_present_check_req()
		#if USE_LV_WATCH_WS_KAER != 0
		 && (MMI_Modem_Sim_Present_Check_Req(MMI_MODEM_SIM_1) == false)
		#endif
		) &&
       (NULL == phone_get_activity_obj() && NULL == factorymode_get_ext())
       && (WATCH_SHUTDOWN_UNPERFORMED == get_shutdown_performance_flg())
       && (NULL == lv_watch_get_activity_obj(ACT_ID_FACTORY_MODE_MAIN))
       && !setting_is_flying_mode()) {
        watch_modem_sim_soft_reset_req();
        printf("sim soft reset has been run___________________\n");
    }
}
#endif
#if defined(__XF_HEADPHONE__)
extern lv_phone_obj_ext_t * phone_get_ext2(void);
typedef void (*AUDIOHAL_HeadsetReport_T) (UINT32 plug, UINT32 type, UINT32 event);
extern void AudioHAL_AifBindHeadsetDetectionCB(AUDIOHAL_HeadsetReport_T cb);

void HeadsetEventHandler_ex(UINT32 plug, UINT32 type, UINT32 event)
{
	lv_phone_obj_ext_t * ext = phone_get_ext2();
	
	if(ext==NULL)
		return;

	if(plug==1&&event==3)   //AUDIOHAL_HEADPHONE_PLUG_IN==1; HOOK_KEY_PRESS==3;
	{


		if(ext!=NULL&&ext->phone_ui_id == PHONE_UI_RING)
		{
		cplog_printf("+++++++HeadsetEventHandler_ex++++++3+++++++\n");	
	           ring_hook_action(NULL,LV_EVENT_CLICKED);    
		}
		else if(ext!=NULL&&(ext->phone_ui_id == PHONE_UI_CONNECTED||ext->phone_ui_id == PHONE_UI_DIALING))
		{
		cplog_printf("+++++++HeadsetEventHandler_ex++++++4+++++++\n");	
			ring_off_action(NULL);
		}
	}
}
#endif

static void watch_start_wifi_bt_delay(void * para)
{
    (void)para;
#if USE_LV_BLUETOOTH != 0
#if USE_LV_WLAN != 0
    if(!hal_wlan_delay_to_start_bt(bluetooth_start_without_ui)) {
        bluetooth_start_without_ui();
    }
#else
    bluetooth_start_without_ui();
#endif
#endif
}
#if USE_LV_WATCH_SPAIN!=0
bool get_eco_enable()
{
    return eco_enable;
}
void set_eco_enable(bool data)
{
    eco_enable=data;
}
int get_eco_period()
{
    return eco_period;
}
void set_eco_period(int data)
{
    eco_period=data;
}
#endif
static lv_obj_t * watch_create(bool silentReset)
{
    #if USE_LV_WATCH_SPAIN != 0
    //跌倒检测开关线程
    uos_create_task(fall_detection_task, NULL, 64, 4096, 200, "fall_detection_task");

    //开机后检测是否开启ECO模式
    nv_watch_settings_t settings;
	UI_NV_Read_Req(NV_SECTION_UI_SETTINGS, 0, sizeof(nv_watch_settings_t), (UINT8 *)&settings);
    set_eco_period(settings.eco_period);
    if (settings.eco_enable == 1)
    {
        CWatchService_ECO_mode(1);
        printf("$$$$open ECO MODE\n");
    }
    else{
        CWatchService_ECO_mode(0);
        printf("$$$$close ECO MODE\n");
    }
    printf("$$$$$$$$$$$$$$$$settings.eco_enable[0],%d,%d\n",settings.eco_enable,settings.eco_period);
    #endif
    watch_poweron_logo_del();
    watch_info = lv_mem_alloc(sizeof(watch_info_t));
    LV_ASSERT_MEM(watch_info);
    if(watch_info == NULL) return NULL;

    watch_default_set();

    watch_start_activity_monitor_task();

    lv_obj_t * obj = launcher_activity_create();

#if USE_LV_WATCH_SD_AUTO_MOUNT != 0
    if((HAL_USB_CONNECTED == Hal_Get_Usb_Status()) && (false == silentReset)) {
        setting_sd_mount_capture_by_usb();
    }
#endif

#if USE_LV_WATCH_LOWBATTERY != 0
   

	watch_wakeup_time_reset();
    //printf("$$$$$$$$$$$$$$$$startlowbattery_task\n");
    #if USE_LV_WATCH_SPAIN != 0
        lowpower_create_task();
    #else
        lowbattery_task_p = lv_task_create(lowbattery_task, 10000, LV_TASK_PRIO_MID, NULL);
    #endif
#endif 

#if USE_LV_WATCH_TTS != 0
	tts_init();
#endif
#if USE_LV_WATCH_SIMCHECK
    checksim_task_p = lv_task_create(simcheck_task, WATCH_CHECK_SIM_STATUS_PERIOD, LV_TASK_PRIO_MID, NULL);
#endif

    if(!silentReset) {
        watch_set_ready_state(true);
		#if (USE_LV_WATCH_TTS != 0)&&(USE_LV_WATCH_WS_CT != 0)
		watch_set_launcher_ready_state(true);
		#endif
    }

    if(watch_alarm_flag) {
        watch_set_alarm_flag(false);

        /* play tone start */
		#if USE_LV_WATCH_ALLVOL_SET != 0
		Hal_NFFS_File_Play_Repeat(AUDIO_ALARM, query_current_volume(AUDIO_ALARM_VOL));
		#else
        Hal_NFFS_File_Play_Repeat(AUDIO_ALARM, query_current_volume());
		#endif

        /* display alarm animation */
        alarm_create(NULL);
    }

    Hal_Register_Charger_Status_Ind(watch_normal_charging_status_change);
	
#if USE_LV_WLAN != 0
    setting_init_mobile_data_switch();
    wlan_init();
#endif

    Hal_Timer_Start(50, watch_start_wifi_bt_delay, NULL, false);
    Hal_Audio_Manage_Init();

#if defined(USE_LV_VAD) && (USE_LV_VAD > 0)
    voice_wakeup_init();
#endif

#if defined(__XF_LEBAO_KOUDAI__)
	lebao_system_init();
#endif

#if defined(ZFB_SUPPORT)
#if UPAY_DEBUG
	set_upay_debug_enable(true);
#endif
	alipay_sys_init();
#endif

#if USE_LV_WATCH_INNER_MUSIC!=0
	audio_player_inner_music_init();
#endif

#if defined(__XF_HEADPHONE__)  
	AudioHAL_AifBindHeadsetDetectionCB(HeadsetEventHandler_ex);
#endif

#if (USE_LV_WATCH_TTS != 0)||USE_LV_WATCH_NAV_KEY_UNLOCK!=0||USE_LV_WATCH_FZH_WAKEUP_TO_STU!=0
	watch_set_launcher_ready_state(true);
#endif

#if defined(__XF_ATA_TEST_SUPPORT__)
	hwtest_set_ready();
#endif

#if USE_LV_WATCH_LIMIT_USE_YD_MOBILE_MEP!=0
	watch_modem_query_sim_status(RIL_SIM_1);
#endif
#if USE_LV_WATCH_RESER_BIND != 0
    extern void delayed_check_first_boot(lv_task_t * task);
    lv_task_t * first_boot_task = lv_task_create(delayed_check_first_boot, 1000, LV_TASK_PRIO_MID, NULL);
    lv_task_once(first_boot_task);
    ws_printf("watch_powerup_silent_reset_process\n");
#endif 

    return obj;
}

static void watch_wakeup_state_clean(lv_task_t * task)
{
    watch_ui_wake_up_task = NULL;
    watch_ui_wake_up_flag = false;
}

static void watch_wakeup_task_clean(void)
{
    if(NULL != watch_ui_wake_up_task) {
        lv_task_del(watch_ui_wake_up_task);
        watch_ui_wake_up_task = NULL;
    }
    watch_ui_wake_up_flag = false;
}

void watch_wakeup_time_reset(void)
{
    if(watch_ui_wake_up_flag) {
        if(NULL != watch_ui_wake_up_task) lv_task_reset(watch_ui_wake_up_task);
    } else {
        watch_ui_wake_up_flag = true;
        watch_ui_wake_up_task = lv_task_create(watch_wakeup_state_clean,
                                               setting_get_backlight_timeout() * 1000,
                                               LV_TASK_PRIO_HIGHEST,
                                               NULL);
        lv_task_once(watch_ui_wake_up_task);
    }
}

void watch_wake_up_ambient(bool is_suspend)
{
    printf("%s\n",__FUNCTION__);
    dial_ambient_update();
    if(is_suspend)
        Hal_Pm_Resume();
    watch_ambient = true;
    ambient_status_set(true);
    lv_task_ready(monitor_task);
}

void watch_exit_ambient(void)
{
     printf("%s\n", __FUNCTION__);
     #ifndef BUILD_IN_PC_SIMULATOR
     lv_obj_t * ambient_obj = lv_watch_get_activity_obj(ACT_ID_DIAL_AMBIENT);
     dial_ambient_destroy(ambient_obj);
     #endif
     dial_clock_update_immediately();
     Hal_Touch_Wakeup();
     watch_ambient = false;
     ambient_status_set(false);
     Hal_ambient_flag_set(AMBIENT_OFF);
}

// watch is waked up by other event except touch pad
void watch_wake_up(bool wakeup_dev)
{
    printf("%s watch_ui_wake_up_flag %d, wakeup %d, ambient %d\n",
           __FUNCTION__, watch_ui_wake_up_flag, wakeup_dev, Hal_ambient_flag_get());
    if(Hal_ambient_flag_get() == AMBIENT_OFF)
    {
	#if USE_LV_WATCH_SLIDE_UNLOCK !=0
		if(watch_is_in_slide_unlock_enable()==1)
			slide_unlock_dial_clock_update_immediately(); 
		else
	#endif
        dial_clock_update_immediately();
    }

    watch_wakeup_time_reset();
    Hal_Pm_Resume();
#if USE_LV_WATCH_SIMCHECK
    if(NULL != checksim_task_p) {
        lv_task_ready(checksim_task_p);
        printf("checksim_task_p is ready\n");
    }
#endif
    if(wakeup_dev) {
        watch_set_lcd_status(true);
    } else {
        watch_set_lcd_status(false);
    }
}

#if USE_WATCH_FACE_UNLOCK != 0
void watch_wake_up_face(bool wakeup_dev)
{
    printf("%s watch_ui_wake_up_flag %d, wakeup %d, ambient %d\n",
           __FUNCTION__, watch_ui_wake_up_flag, wakeup_dev, Hal_ambient_flag_get());
    if(Hal_ambient_flag_get() == AMBIENT_OFF)
        dial_clock_update_immediately();

    watch_wakeup_time_reset();
    Hal_Pm_Resume();
	face_unlock_create_btn_action(NULL, LV_EVENT_CLICKED);
#if USE_LV_WATCH_SIMCHECK
    if(NULL != checksim_task_p) {
        lv_task_ready(checksim_task_p);
        printf("checksim_task_p is ready\n");
    }
#endif
    if(wakeup_dev) {
        watch_set_lcd_status(true);
    } else {
        watch_set_lcd_status(false);
    }
}
#endif

#if USE_LV_WATCH_SLIDE_UNLOCK != 0
void watch_wake_up_to_slideunlock(bool wakeup_dev)
{
    printf("%s watch_ui_wake_up_flag %d, wakeup %d, ambient %d\n",
           __FUNCTION__, watch_ui_wake_up_flag, wakeup_dev, Hal_ambient_flag_get());
    if(Hal_ambient_flag_get() == AMBIENT_OFF){
		if(watch_is_in_slide_unlock_enable()==1)
			slide_unlock_dial_clock_update_immediately(); 
		else
        	dial_clock_update_immediately();
    }

    watch_wakeup_time_reset();
    Hal_Pm_Resume();
	
#if USE_LV_WATCH_T7_SLIDE_UNLOCK !=0 && USE_LV_WATCH_CYTEK4_UI==0
		lv_obj_t * top_act = lv_watch_get_top_activity_obj();
		lv_obj_t * tabview;
		lv_watch_tabview_ext_t * tabview_ext;
		uint16_t tab_cur = 0;
	
	
		if(top_act==NULL)
		 return;
		lv_watch_activity_ext_t * ext = lv_obj_get_ext_attr(top_act);
		if(ext==NULL)
		 return;
		lv_watch_Activity_Id_t top_act_id = ext->actId;
	
		if(top_act_id==ACT_ID_LAUNCHER)
		{
			tabview = lv_watch_tabview_get_from_obj(top_act);
			if(tabview!=NULL)
			{
				tabview_ext = lv_obj_get_ext_attr(tabview);
				if(tabview_ext!=NULL)
				{
					tab_cur = tabview_ext->tab_cur; 
					if(tab_cur ==1)
					{
						slide_unlock_btn_action(NULL);
					}
				}
			}
		}
#else
	slide_unlock_btn_action(NULL);
#endif
#if USE_LV_WATCH_SIMCHECK
    if(NULL != checksim_task_p) {
        lv_task_ready(checksim_task_p);
        printf("checksim_task_p is ready\n");
    }
#endif
    if(wakeup_dev) {
        watch_set_lcd_status(true);
    } else {
        watch_set_lcd_status(false);
    }
}
#endif

#if USE_LV_WATCH_NAV_KEY_UNLOCK != 0	
void watch_wake_up_to_nav_keyunlock(bool wakeup_dev)
{
    printf("%s watch_ui_wake_up_flag %d, wakeup %d, ambient %d\n",
           __FUNCTION__, watch_ui_wake_up_flag, wakeup_dev, Hal_ambient_flag_get());
    if(Hal_ambient_flag_get() == AMBIENT_OFF)
        dial_clock_update_immediately();

    watch_wakeup_time_reset();
    Hal_Pm_Resume();
	key_unlock_btn_action(NULL);
#if USE_LV_WATCH_SIMCHECK
    if(NULL != checksim_task_p) {
        lv_task_ready(checksim_task_p);
        printf("checksim_task_p is ready\n");
    }
#endif
    if(wakeup_dev) {
        watch_set_lcd_status(true);
    } else {
        watch_set_lcd_status(false);
    }
}
#endif

#if USE_LV_WATCH_FZH_WAKEUP_TO_STU!=0
void watch_wake_up_to_act_stuinfo(bool wakeup_dev)
{
    printf("%s watch_ui_wake_up_flag %d, wakeup %d, ambient %d\n",
           __FUNCTION__, watch_ui_wake_up_flag, wakeup_dev, Hal_ambient_flag_get());
    if(Hal_ambient_flag_get() == AMBIENT_OFF)
        dial_clock_update_immediately();

    watch_wakeup_time_reset();
    Hal_Pm_Resume();
	stu_show_create_btn_action(NULL);
#if USE_LV_WATCH_SIMCHECK
    if(NULL != checksim_task_p) {
        lv_task_ready(checksim_task_p);
        printf("checksim_task_p is ready\n");
    }
#endif
    if(wakeup_dev) {
        watch_set_lcd_status(true);
    } else {
        watch_set_lcd_status(false);
    }
}

#endif

bool watch_get_wakeup_lcd(void)
{
    bool wakeup = is_wakeup_lcd;
    is_wakeup_lcd = true;
    return wakeup;
}
//only wakeup gui but not set lcd on
void watch_disable_wakeup_lcd(void)
{
    is_wakeup_lcd = false;
}

void watch_suspend(lv_task_t * task)
{
    
    if(watch_silent_reset) {
        watch_silent_reset = false;
        if(Hal_ambient_flag_get() == AMBIENT_ON) {
             //printf("if ambient already enabled in backlight_task , no need do suspend again\n");
             return;
        }
        printf("%s: backlight status %d\n", __FUNCTION__, Hal_Get_Backlight_Status());
        if(0 == Hal_Get_Backlight_Status()) {
            watch_set_lcd_status(false);
        }
    }

    uint16_t period = setting_get_backlight_timeout() * 1000;
    uint32_t inact_time = lv_disp_get_inactive_time(NULL);
    uint32_t wake_up_flag = 0;

    if(watch_ui_wake_up_task) {// add assert for issue #37984
        if(watch_ui_wake_up_task->period != period) {
            printf("fatal error: memory of wakeup task was over write, period is %d\n",
                   watch_ui_wake_up_task->period);
            //not assert, just restart the wakeup task(#50495)
            watch_wakeup_task_clean();
            watch_wakeup_time_reset();
        }
    }

    /*if lcd is off, no calling or wifi, suspend at once. */
    if(!watch_get_lcd_status() || watch_ambient) {
        period = 0;
        watch_ambient = false;
        watch_wakeup_task_clean();
    }

    printf("%s, battery %d, call statue %d, ui wakeup %d, inact_time %d ****************\n",
           __FUNCTION__, Hal_Battery_Get_Status(), call_params.state, watch_ui_wake_up_flag, inact_time);
    uint8_t reason = *(uint8_t *)(task->user_data);
    if((reason == WATCH_SUSPEND_KEY) ||
             ((!watch_ui_wake_up_flag) &&
             (period <= inact_time) &&
	#if USE_LV_WATCH_SOS != 0    
             SOS_OFF == get_sos_status() &&
	#endif             
             watch_voip_suspend_check()&&
             watch_get_suspend_enable())) {
        if(dial_switch_to_ambient()) {
            if(Hal_ambient_flag_get() == AMBIENT_OFF) {
                Hal_ambient_flag_set(AMBIENT_ON);
            }
        }
        if(watch_get_lcd_status()) {
            #if USE_LV_WATCH_SLIDE_CHANGE_THEME != 0
            dial_play_theme_audio_task_destroy();
            #endif
            watch_set_lcd_status(false);
        #if USE_LV_WATCH_ALIPAY != 0
            alipay_se_enter_lpm_by_suspend();
        #endif
        }

        uint8_t is_suspend = 1;
        static uint8_t suspend_cnt = 0;
        if(watch_sleep_lcd_only) {
            if(Hal_ambient_flag_get() == AMBIENT_ON) {
                if(29 == suspend_cnt ++) {
                    suspend_cnt = 0;
                    is_suspend = 0;
                    wake_up_flag = PM_RESUME_AMBIENT;
                } else {
                    return;
                }
            } else {
                return;
            }
        }

        if(is_suspend){
            wake_up_flag = Hal_Pm_Suspend();
        	}

        printf("pm wake up, wake_up_flag 0x%08x ################\n", wake_up_flag);
        if(wake_up_flag & PM_RESUME_ALL) {
            // update dial clock and refresh screen before lcd on

			#if USE_LV_WATCH_CT_KEY_TOHOME_OR_BACK!=0
			 if(PM_RESUME_NORMALKEY & wake_up_flag){
				gKeyWakeup = true;
			 }
			#endif
            if((PM_RESUME_UI & wake_up_flag) &&
                    (phone_is_monitor_on() ||false == watch_get_wakeup_lcd()
					#if defined(__XF_XMLY__)
					|| LIBXMLYAPI_get_wakeup_is_sleep()
					#endif
#ifdef __XF_GPS_SUPPORT__

						|| (0 == GpsIsSleepIn())
#endif /* __XF_GPS_SUPPORT__ */
                    )) {
                watch_wake_up(false);
            } else if(PM_RESUME_OVP & wake_up_flag) {
                HAL_CHG_STATUS chg_sts_curr = Hal_Charger_Get_Status();
                if(chg_sts_curr == HAL_CHG_CONNECTED) {
                    printf("Usb vbus on, PM_RESUME_OVP without lcd.\n");
                    watch_wake_up(false);
                } else {
                    watch_wake_up(true);
                }
            } else if((PM_RESUME_AMBIENT & wake_up_flag)) {
                watch_wake_up_ambient(is_suspend);
#if defined(USE_VAD) && (USE_VAD > 0)
            } else if((PM_RESUME_VAD & wake_up_flag)) {
                watch_wake_up(true);
                // sos_create(NULL);
#endif

            } else if(PM_RESUME_UI_TASK & wake_up_flag) {
                printf("PM_RESUME_UI_TASK without lcd.\n");
                //watch_wakeup_time_reset();
                Hal_Pm_Resume();
            } else if(PM_RESUME_SDK & wake_up_flag) {
                watch_wake_up(false);
#if USE_WATCH_FACE_UNLOCK != 0		
			} else if(PM_RESUME_ONKEY & wake_up_flag) {
				watch_wake_up_face(true);
#elif USE_LV_WATCH_SLIDE_UNLOCK != 0		
			} else if((PM_RESUME_NORMALKEY & wake_up_flag)||(PM_RESUME_ONKEY & wake_up_flag)) {
				watch_wake_up_to_slideunlock(true);
#elif USE_LV_WATCH_NAV_KEY_UNLOCK != 0		
			} else if((PM_RESUME_NORMALKEY & wake_up_flag)||(PM_RESUME_ONKEY & wake_up_flag)) {
				watch_wake_up_to_nav_keyunlock(true);
#elif USE_LV_WATCH_FZH_WAKEUP_TO_STU!=0
			} else if((PM_RESUME_NORMALKEY & wake_up_flag)||(PM_RESUME_ONKEY & wake_up_flag)) {
				watch_wake_up_to_act_stuinfo(true);
#elif USE_LV_DIAL_LOCK_ACT != 0
			} else if(PM_RESUME_ONKEY & wake_up_flag) {
				dial_lock_main_btn_action(0,LV_EVENT_CLICKED);
				watch_wake_up(true);
#endif	
            } else {
                watch_wake_up(true);
            }
        }
    }
}

void watch_suspend_by_ui(void)
{
    suspend_reason = WATCH_SUSPEND_UI;
    watch_suspend(monitor_task);
}

void watch_suspend_by_key(void)
{
    suspend_reason = WATCH_SUSPEND_KEY;
    watch_suspend(monitor_task);
    suspend_reason = WATCH_SUSPEND_UI;
}

static void watch_start_activity_monitor_task(void)
{
    if(NULL == monitor_task) {
        monitor_task = lv_task_create(watch_suspend, 1000, LV_TASK_PRIO_HIGH, &suspend_reason);
    }
}

void watch_reset_monitor_task(void)
{
    if(NULL != monitor_task) {
        lv_task_reset(monitor_task);
    }
}

#if USE_LV_WATCH_MODEM_ADAPTOR
void watch_modem_power_up_cnf(void)
{
}

void watch_modem_power_off_cnf(void)
{
}
#endif

bool watch_is_ready(void)
{
    return watch_ready;
}

void watch_set_ready_state(bool ready_flag)
{
    watch_ready = ready_flag;
}

static void watch_suspend_mng_init(void)
{
    _lv_ll_init(&suspend_ll, sizeof(lv_watch_suspend_t));
}

/**
 *set suspend enable from app
 *param (in) bool: enable, false for disable suspend
 *param (in) lv_watch_Activity_Id_t: act id of app
 *param (in) bool: top, true for checking only when act is on top, false for checking whenever
 *return void
 */
void watch_set_suspend_enable(bool enable, lv_watch_Activity_Id_t act, bool top)
{
    lv_watch_suspend_t * suspend;

    printf("%s enable:%d, act: %d, top: %d\n", __FUNCTION__, enable, act, top);
    _LV_LL_READ(suspend_ll, suspend) {
        if(suspend->act == act) {
            if(enable == true) {
                _lv_ll_remove(&suspend_ll, suspend);
                lv_mem_free(suspend);
            }
            return;
        }
    }

    if(enable == false) {
        suspend = _lv_ll_ins_head(&suspend_ll);
        suspend->act = act;
        suspend->top = top;
    }
}

/**
 *get suspend enable status
 *return bool: true for suspend enable, flase for suspend disable
 */
static bool watch_get_suspend_enable(void)
{
    //check phone call status
    if(!watch_phone_suspend_check()) return false;

    //check voip call status
    if(!watch_voip_suspend_check()) return false;

#if USE_TOUCH_TRACKER
    if(touch_tracker_is_recording() || touch_tracker_is_playing()) return false;
#endif

    //check suspend act list
    lv_obj_t * top_act = lv_watch_get_top_activity_obj();
    lv_watch_activity_ext_t * ext = lv_obj_get_ext_attr(top_act);
    lv_watch_Activity_Id_t top_act_id = ext->actId;
    lv_watch_suspend_t * suspend;

    _LV_LL_READ(suspend_ll, suspend) {
        if(suspend->top == true) {
            if(suspend->act == top_act_id) return false;
        } else {
            return false;
        }
    }

    //check ...

    return true;
}

#if defined(__XF_NFC_THN_SUPPORT__)
void watch_set_nfc_suspend_enable(bool enable)
{
	watch_set_suspend_enable(enable, ACT_ID_NFC, 0);
}
#endif

void watch_set_sleep_lcd_only(bool enable)
{
    printf("%s : %d\n", __FUNCTION__, enable);
    watch_sleep_lcd_only = enable;
}

bool watch_get_sleep_lcd_only(void)
{
    return watch_sleep_lcd_only;
}
#if defined(__XF_PRO_T7__)&&USE_LV_WATCH_SLIDE_UNLOCK != 0	
extern bool ap_background;
#endif
void watch_set_lcd_status(bool wakeup)
{
    if(wakeup == watch_lcd_status) return;

	//heroapp start
#if 0 //def FEATURE_HERO_ENGINE_APP
	if(wakeup)
	{
		hero_engine_app_refresh_screen();
	}
#endif
	//heroapp end
	
    watch_lcd_status = wakeup;

    if(wakeup){
		#if USE_LV_WATCH_MUTE_LCD_OFF != 0
		if(WatchUtility_IsMuteTime() 
			&& (!((xj_occupy_is_qanswer() == 1) || (CWatchService_Is_Dati_Status() == 1) || CWatchService_Wrong_list_exist() > 0))
			)
		{
			// mute list lcd off
		}
		else
		#endif
        if(Hal_ambient_flag_get() == AMBIENT_ON)
            watch_exit_ambient();
        else
            Hal_Lcd_Touch_Wakeup();
#if USE_LV_WLAN != 0
        hal_wlan_lcd_wakeup(true);
#endif
        ambient_status_set(false);
#if USE_LV_WATCH_STUDENT_INFO!=0
		student_info_display_immediately();
		ws_printf("student_info_display_immediately is finished\n");
#endif

    } else {
        if(Hal_ambient_flag_get() == AMBIENT_ON) {
            Hal_Touch_Sleep();
            ambient_status_set(true);
        } else
            Hal_Lcd_Touch_Sleep();
#if USE_LV_WLAN != 0
        hal_wlan_lcd_wakeup(false);
#endif
		
	#if USE_WS_SLEEP_LOCK_DIAL!=0 ||USE_LV_LG_LOCK_KEY_ON_IDLEDIAL!=0
		ws_key_set_lock(true);
	#endif
	#if USE_LV_WATCH_CT_KEY_TOHOME_OR_BACK!=0
		gKeyWakeup=false;
	#endif
	
	#if defined(__XF_PRO_T7__)&&USE_LV_WATCH_SLIDE_UNLOCK != 0	
	if(ap_background)
	{
		slide_unlock_btn_action(NULL);
	}
#endif

	#if USE_LV_WATCH_YUNAN_DATI != 0 || USE_XJ_YKT_LZ != 0
		if(CWatchService_Is_Dati_Status() == 1)
		{
			CWatchService_Exit_DATI();
		}
	#endif

    #if USE_LV_WATCH_STUDENT_INFO != 0
		if(lv_watch_get_activity_obj(ACT_ID_STUDENT_INFO))
		{
			lv_obj_del(lv_watch_get_activity_obj(ACT_ID_STUDENT_INFO));
		}
		if(lv_watch_get_activity_obj(ACT_ID_SMS_MSG_INCOMING))
		{
			lv_obj_del(lv_watch_get_activity_obj(ACT_ID_SMS_MSG_INCOMING));
		}
    #endif
    }
}

bool watch_get_lcd_status(void)
{
    return watch_lcd_status;
}

static void watch_normal_charging_status_change(VOID * para)
{
    (void)para;

    printf("%s\n", __FUNCTION__);
    Wakeup_GuiTask(true);
	watch_wakeup_time_reset();
#if 0//defined(__XF_BATT_TEMP_NTC__) || defined(__XF_BATT_TEMP_SOC__)
    Wakeup_GuiTask(true);
#endif
#if USE_LV_WATCH_SD_AUTO_MOUNT != 0
    watch_start_sd_mount_task();
#endif
#if defined(__XF_LCD_SIZE_128X96__)&& (defined(__XF_BATT_TEMP_NTC__) || defined(__XF_BATT_TEMP_SOC__))
	if((uint8_t) para == 4)
	{
		Wakeup_GuiTask(true);
		watch_set_lcd_status(true);
		watch_wakeup_time_reset();
	}
#endif
    charger_connect_status_indication((uint8_t) para );
}

void watch_set_pintype(MMI_MODEM_PIN_STATUS_TYPE pinreqtype)
{
    pintype = pinreqtype;
}

MMI_MODEM_PIN_STATUS_TYPE watch_get_pintype(void)
{
    return pintype;
}

static void watch_wakeup_lcd_handler(lv_task_t * para)
{
    bool *always = (bool*)para->user_data;
    if(!(*always) && phone_is_monitor_on()) {
        return;
    }

    if(!watch_get_lcd_status()) {
        printf("%s\n", __FUNCTION__);
        watch_set_lcd_status(true);
        watch_wakeup_time_reset();
    }
}

static bool para;
void watch_wakeup_lcd(bool always)
{
    para = always;
    lv_task_t * task = lv_task_create(watch_wakeup_lcd_handler, 0, LV_TASK_PRIO_HIGHEST, (void *)&para);
    lv_task_once(task);
}

#if USE_LV_WATCH_SD_AUTO_MOUNT != 0
static void sd_mount_task_handle(lv_task_t * task)
{
    int usb_sta = Hal_Get_Usb_Status();

    if(usb_conn_status != usb_sta) {
        printf("%s:usb old status = %d, usb new status = %d, sd_user = %d\n", __FUNCTION__, usb_conn_status, usb_sta, HAL_sd_user_get());
        watch_set_lcd_status(true);
        if(HAL_USB_CONNECTED == usb_sta) {
            setting_sd_mount_on_clear_ui();
            setting_sd_mount_capture_by_usb();
        } else {
            setting_sd_mount_off_clear_ui();
            setting_sd_mount_remove();
        }
        usb_conn_status = usb_sta;
    }
}

static void watch_start_sd_mount_task(void)
{
    if(NULL == sd_mount_task) {
        sd_mount_task = lv_task_create(sd_mount_task_handle, 100, LV_TASK_PRIO_HIGH, NULL);
    }
}
#endif

void watch_set_alarm_flag(bool alarm_flag)
{
    watch_alarm_flag = alarm_flag;
}


#if USE_DRV_HEART !=0
static uint32_t health_motion_flag;
uint32_t health_motion_get_flag(void)
{
	return health_motion_flag;
}

void health_motion_set_flag(uint32_t flag)
{
	health_motion_flag |= flag;
}

void health_motion_clear_flag(uint32_t flag)
{
	health_motion_flag &=~ flag;
}

void health_motion_clear_all_flag(void)
{
	health_motion_flag = 0;
}
#endif

#if USE_CRANE_WATCH_GSENSOR != 0 && (defined(__XF_CUST_QG__)|| defined(__XF_CUST_SXL__)|| defined(__XF_CUST_XW__))
void gsensor_drv_update_ind()
{
}
#endif

#if defined(__XF_ATA_TEST_SUPPORT__)
void hwtest_Speaker()
{
	#if USE_LV_WATCH_ALLVOL_SET != 0
	Hal_NFFS_File_Play_Onetime(AUDIO_POWER_ON, (HAL_SPEAKER_GAIN)query_current_volume(AUDIO_INCOMING_CALL_VOL), poweron_play_callback, 0);
	#else
	Hal_NFFS_File_Play_Onetime(AUDIO_POWER_ON, (HAL_SPEAKER_GAIN)query_current_volume(), poweron_play_callback, 0);
	#endif
}
#endif

#endif/* USE_LV_WATCH */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/devices/transports/usb.mod/api/inc/usbdriver.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2007/01/12 13:10:41 $
 **************************************************************************/
/** \file
 * USB device controller driver API
 *
 *           THIS CODE MUST BE KEPT PLATFORM INDEPENDENT
 *
 * Code below this API is platform-dependent, the API itself is <b>not</b>.
 **************************************************************************/
#if defined(UPGRADE_USB)

#ifndef USBDRIVER_H
#define USBDRIVER_H

/***************************************************************************
 * This defines a client-visible functional interface.
 ***************************************************************************/






      

/* Note. This isn't strictly a DD or PD interface, but a hybrid of the two.
 * The functionality across this interface is that for the USB Device
 * Controller, thus in in the nature of a DD Device Driver, however, the
 * platform dependence is built into the driver code, so the separation
 * of generic and platform-dependent code is also at this interface -
 * in the nature of a 'PD' API.
 *
 * Hence - we name this pragmatically, as the 'USB Driver' API.
 */

/** \defgroup PrdDevicesUsbDriverApi USB Driver API
 * \ingroup PrdDevicesUsb
 * \ingroup Api
 * USB Driver API.
 * Functions named usbDriverXxx() are exported by the USB platform driver
 * and are called by higher layers (e.g. the USB stack).
 *
 * Device Controller Layer (DCL) functions named usbDclXxx() are exported
 * by the core stack so they may be called by the device driver.
 *
 * @{
 */

/*******************************************************************************
 * Types used in Prototypes and Globals
 ******************************************************************************/
/** USB Transaction Status */
typedef enum UsbTransactionStatusTag
{
  /** Transmitted packet is acknowledged */
  USB_IN_STATUS_ACK,
  /** Transmitted packet is refused */
  USB_IN_STATUS_NAK,
  /** Transmit endpoint is 'closed' at the destination */
  USB_IN_STATUS_STALL,
  /** <i>reserved</i> <b>ISO transfers are not currently supported</b> */
  USB_IN_STATUS_ISOCHRONOUS_TRANSFER,

  /** Received packet is acknowledged */
  USB_OUT_STATUS_ACK,
  /** Received packet is refused */
  USB_OUT_STATUS_NAK,
  /** Receive endpoint is 'closed' */
  USB_OUT_STATUS_STALL,
  /** <i>reserved</i> <b>ISO transfers are not currently supported</b> */
  USB_OUT_STATUS_ISOCHRONOUS_TRANSFER,

  /** Receive error - Bit-stuffing, CRC etc. */
  USB_OUT_STATUS_MEDIA_ERROR,
  /** Received packet is SETUP - cannot be NAK'd */
  USB_OUT_STATUS_SETUP_PACKET,

  /** Unknown State */
  USB_TRANSACTION_STATUS_UNDEFINED
} UsbTransactionStatus;

/*******************************************************************************
 * Function Prototypes for functions the Driver Module is required to support
 ******************************************************************************/

/* USB device controller functions. */

/** Connect the device controller to the USB.
 * This function performs intialisation actions and then
 * connects the USB device controller to the USB.
 * Its typical actions include resetting the device, configuring
 * interrupt and mask registers, flushing the EP0 FIFOs and enabling the EP0
 * receiver, and setting the device to an operational mode so that it responds
 * to resets from the USB host.
 *
 * This function should be called following a
 * power reset and once only. Note that the endpoint lookup registers are not
 * configured until a reset event is received from the host.
 * \return Nothing.
 */
extern void usbDriverConnect(void);

/** USB interrupt handler.
 * This function is the interrupt handler for the USB device controller
 * pointed to by the system's interrupt vector table. It services
 * all interrupts generated by the USB device controller.
 * \return Nothing.
 */
void usbDriverInterruptHandler(void);

/** USB hardware present and correct?
 * This function determines if the USB device controller can be detected
 * (and found to be working). It may perform actions such as verifying
 * read/write operations to the device controller and verifying the
 * device-controller revision number.
 * Exactly how this is done depends upon the USB device controller concerned.
 * \return TRUE if the USB device controller is found, else FALSE.
 */
extern Boolean usbDriverDetectDevice(void);

/** Reset the USB device controller.
 * This function performs a software reset of the USB device controller.
 * \return Nothing.
 */
extern void usbDriverInitialiseUsbDeviceController(void);

/** Set the address of the USB device controller.
 * This function updates (programs) the USB device controller address.
 * Whilst in Address or
 * Configured state, the USB device controller will only respond to transaction
 * sent to the programmed address.
 * \param [in] address The new device address.
 * \return Nothing.
 */
extern void usbDriverSetAddress(Int8 address);

/** Activates remote wakeup.
 * This function enables the USB device controller to generate remote wakeup
 * signalling.
 */
extern Boolean usbDriverActivateRemoteWakeup(void);


/* Endpoint functions. */

/** Allocate an endpoint.
 * This function attempts to allocate the specified endpoint to a
 * receiver/transmitter of the USB device controller.  The allocation can fail,
 * for instance, because the USB device controller concerned has insufficient
 * receivers/transmitters for the desired configuration.  Note that the default
 * control endpoint, EP0, will always be allocated.
 * \param [in] endpointAddress The endpoint address.
 * \return TRUE if the endpoint is successfully allocated, else FALSE.
 */
extern Boolean usbDriverAllocateEndpoint(Int8 endpointAddress);

/** Deallocate an endpoint.
 * This function attempts to de-allocate the specified endpoint from the USB
 * device controller.  The de-allocation fails if the endpoint was not already
 * allocated.  Note that it is not possible to de-allocate the default control
 * endpoint, EP0.
 * \param [in] endpointAddress The endpoint address.
 * \return TRUE if the endpoint is successfully deallocated, else FALSE.
 */
extern Boolean usbDriverDeallocateEndpoint(Int8 endpointAddress);

/** Re-enable enpoint transfers.
 * This function re-enables a NAKing endpoint so that USB transfers can occur
 * on the specified endpoint transmitter/receiver so that, under normal
 * operation, the endpoint (excluding isochronous endpoints) should ACK
 * transactions.
 * \param [in] endpointAddress The endpoint address.
 * \return Nothing.
 */
extern void usbDriverEnableEndpointTransfers(Int8 endpointAddress);

/** Disable endpoint transfers.
 * This function disables transfers on the specified endpoint transmitter/receiver
 * so that, under normal operation, the endpoint (excluding isochronous endpoints)
 * should NAK transactions.
 * \param [in] endpointAddress The endpoint address.
 * \return Nothing.
 */
extern void usbDriverDisableEndpointTransfers(Int8 endpointAddress);

/** Flush the endpoint FIFO.
 * This function flushes the specified endpoint transmitter/receiver FIFO of
 * any stored data.  This function does not change the endpoint operational
 * state (eg disable).
 * \param [in] endpointAddress The endpoint address.
 * \return Nothing.
 */
extern void usbDriverFlushEndpointFifo(Int8 endpointAddress);

/** Stall an endpoint.
 * Stalls the specified endpoint transmitter/receiver.
 * \param [in] endpointAddress The endpoint address.
 * \return Nothing.
 */
extern void usbDriverStallEndpoint(Int8 endpointAddress);

/** Un-stall an endpoint.
 * Un-stalls the specified endpoint transmitter/receiver.
 * \param [in] endpointAddress The endpoint address.
 * \return Nothing.
 */
extern void usbDriverUnstallEndpoint(Int8 endpointAddress);

/** Transfer data from the USB device to the Stack.
 *
 * This function performs the transfer of data from the device to the USB Stack's
 * receive buffer for that endpoint.  It is typically called in response to an
 * OUT ACK interrupt, but must be tolerant of being called when there is no data
 * in the device (in which case no action is taken).
 *
 * Normally, each data transfer will be a USB packet but it cannot be guaranteed
 * that the data transfer boundaries match USB packet boundaries.  For example,
 * under load and if the device FIFO is larger than a packet size, multiple
 * packets may queue up in the device and then be read as a single unit.
 * It is up to the Application code to handle this uncertainty in a reliable fashion.
 *
 * Note that if the build directive USB_TASK_DATA_TRANSFERS is defined,
 * usbDriverReadData() will be called from the USB stack task.
 *
 * Without USB_TASK_DATA_TRANSFERS defined, usbDriverReadData() is called as part
 * of the USBD controller ISR.  Hence, in the former case the function
 * usbDriverReadData() will have to protect against USBD controller interrupts
 * during access to the USBD controller registers.
 *
 * \param [in] physicalEndpoint Number of the endpoint to read from.
 * \return Nothing.
 */
extern void usbDriverReadData( Int8 physicalEndpoint );


/* Initial device status function. */

/** Returns the initial device status.
 * This function determines the initial device status as returned to
 * the host in a GetStatus(device) request.
 * \return The initial device status.
 */
extern Int16 usbDriverDeviceInitialStatus(void);


/* Event functions. */

/** Gets the SETUP packet.
 * This function reads a SETUP packet from the USB device controller.
 *
 * \param [in] physicalEndpoint The receiving endpoint number.
 * \param [out] setupPacket Pointer to the SETUP packet.
 * \return Nothing.
 */
extern void usbDriverGetSetupPacket(Int8 physicalEndpoint, Int8* setupPacket);


/* I/O functions. */

/** Does endpoint contains data?
 * This function determines if the endpoint receiver FIFO contains some data.
 *
 * \param [in] physicalEndpoint The endpoint Number
 * \return TRUE if FIFO contains some data, else FALSE
 */
extern Boolean usbDriverGotSomeRxData(Int8 physicalEndpoint);

/** Returns the status of the last IN transaction.
 * This function reads the status of the last IN transaction.
 * This function is called by
 * the stack in response to a usbDclTxDoneEvent() from the driver.
 *
 * /param [in] physicalEndpoint The endpoint number.
 * /return UsbTransactionStatus The status of the IN transaction.
 */
extern UsbTransactionStatus usbDriverGetInStatus(Int8 physicalEndpoint);

/** Returns the status of the last OUT transaction.
 * This function reads the status of the last OUT transaction.
 *
 * /param [in] physicalEndpoint The endpoint number.
 * /return UsbTransactionStatus The status of the OUT transaction.
 */
extern UsbTransactionStatus usbDriverGetOutStatus(Int8 physicalEndpoint);

/** Retransmit.
 * This function attempts to retransmit the last transmission.
 *
 * /param [in] physicalEndpoint The endpoint number.
 * /return Nothing.
 */
extern void usbDriverRetransmitPacket(Int8 physicalEndpoint);

/** Transmits data from the USB stack to the host.
 *
 * This function transmits a USB packet containing the provided data on the
 * specified endpoint. It typically involves loading the transmitter FIFO and
 * enabling the transmitter with the correct toggle bit setting.
 * Note that if the build directive USB_ISR_DATA_TRANSFERS is defined,
 * usbDriverTxEPx() will be called as part of the USBD controller ISR.  Without
 * USB_ISR_DATA_TRANSFERS defined, usbDriverTxEpx() is called from the USB stack
 * task.  Hence, in the latter case the function usbDriverTxEPx() will have to
 * protect against USBD controller interrupts during access to the USBD
 * controller registers.
 *
 * \param [in] physicalEndpoint The endpoint number.
 * \param [in] pData Pointer to the data to be transmitted.
 * \param [in] dataLength The amount of data to be transmitted in the packet.
 * \return Nothing.
 */
extern void usbDriverTxEPx(Int8 physicalEndpoint, Int8* pData, Int32 dataLength);

/** Read data from an OUT Endpoint.
 *
 * Reads data from a receive FIFO and stores in a receive request.
 *
 * \param [in] physicalEndpoint The endpoint number.
 * \param [out] pData Pointer to the read data store.
 * \param [in] length Maximum (bytes) ammount of data to be read.
 * \return The number of bytes read
 */
extern Int16 usbDriverRxEPx(Int8 physicalEndpoint,  Int8* pData, Int16 length);

/** Allow transistion to Status stage of Control transfers.
 * This function informs the USB device controller driver that the status stage
 * of a control transfer is now permissible.  The Stack will have already changed
 * the Stack Context usbControlTransferState to CONTROL_TRANSFER_STATE_STATUS_DEVICE_TO_HOST
 * or _HOST_TO_DEVICE depending on the direction of the control transaction.
 *
 * \param [in] physicalEndpoint The endpoint number.
 * \returns Nothing.
 */
extern void usbDriverControlTransferStatusStage(Int8 physicalEndpoint);

/** Enumerate the USB device.
 *
 * This function configures the USBD controller so that it may respond to requests
 * from the host and (re)enumerate.  The function is called as the final part
 * of the reset sequence in response to the USB host signalling a bus reset to
 * the USBD.  One action of this function must be to set the USB stack context
 * resetInProgress to FALSE.
 *
 * Note that the endpoint lookup registers are configured as part of the reset
 * event handler.
 *
 * \return Nothing.
 */
extern void usbDriverEnumerate(void);

/** USB cable state change.
 * This function is called each time the state (insertion/removal) of
 * the USB cable changes.  (The cable detection mechanism is not necessarily
 * within the driver, but the driver needs to be made aware of such changes.)
 *
 * \param [in] usbCableInserted TRUE if USB cable is inserted, else FALSE.
 * \return Nothing.
 */
extern void usbDriverUsbCableStateChange(Boolean usbCableInserted);


/* Reset triggering. */

/** Can USB connection be reset.
 * Interrogation call from stack to see if the driver/platform can cause
 * the USB connection to be reset. i.e. can the physical detachment and
 * reattachment of the device be simulated electrically, indirectly
 * resulting in the host resetting the USB connection to our device.
 *
 * This either might be by forcing
 * one of the D lines, or simply an electrical disconnection from the bus.
 *
 * \return TRUE if a USB bus reset can be triggered, else FALSE
 */
extern Boolean usbDriverCanTriggerReset( void );

/** Triggers a reset.
 * If the hardware can trigger a USB reset (see usbDriverCanTriggerReset),
 *  do so.
 *
 * \return Nothing.
 */
extern void usbDriverTriggerReset( void );


/** Detach from the bus. */
extern void usbDriverDetach( void );

/** Attach to the bus. */
extern void usbDriverAttach( void );

/* Pipe event control functions used to reduce interrupt loading */

/** Mask retry interrupts.
 * This function disables retry interrupts - to prevent the stack
 * from being held out by interrupts.
 *
 * \param [in] physicalEndpoint The endpoint number.
 * \return Nothing.
 */
extern void usbDriverDisableEndpointEvents( Int8 physicalEndpoint );

/** Unmask retry interrupts.
 * This function re-enables retry interrupts
 *
 * \param [in] physicalEndpoint The endpoint number.
 * \return Nothing.
 */
extern void usbDriverEnableEndpointEvents( Int8 physicalEndpoint );



/*******************************************************************************
 * Function Prototypes for functions exported by the Device Control Layer
 * of the Stack - call-backs used by the Driver Module
 ******************************************************************************/

/** Transmit event.
 * This function is called by the device driver when the driver has determined
 * that a Transmit event has occurred. These events occur following completion
 * of an IN transaction. The endpoint to which the event relates is given in
 * the parameter.
 *
 * \param [in] physicalEndpoint The endpoint number.
 * \return Nothing.
 */
extern void usbDclTxDoneEvent(Int8 physicalEndpoint);

/** Receive event.
 * This function is called by the device driver when the driver has determined
 * that a Receive event has occurred (e.g the receipt of a data packet by the device).
 * These events occur following completion of an OUT transaction.
 * The endpoint to which the event relates is given in the parameter.
 *
 * \param [in] physicalEndpoint The endpoint number.
 * \return Nothing.
 */
extern void usbDclRxDoneEvent(Int8 physicalEndpoint);

/** Request storage for receive data.
 *
 * This function is called as part of the data-read operation.
 * It allows the driver to request suitable storage for the received data from
 * the DCL receive buffer manager. The function returns the address to write
 * the data to. The amount of data to be transferred is given via a parameter,
 * in such a way that the function may modify this value should this be necessary
 * to fit the buffering mechanism.
 *
 * If it is not possible to (currently) accept
 * the received data the function returns PNULL.
 *
 * Called by Driver so parameter must be physical endpoint.
 *
 * \param [in] physicalEndpoint The endpoint number.
 * \param [in] dataLengthPtr Pointer to requested data length.
 * \return Pointer to start of available buffer, or PNULL if none allowable data
 *         length returned through dataLengthPtr.
 */
extern Int8* usbDclGetRxBufferWritePtr(Int8 physicalEndpoint, Int16 * dataLengthPtr);

/** Send a usbReceiveBufferFreeSpaceCnf signal to the USB target task.
 * Creates a usbReceiveBufferFreeSpaceCnf confirmation containing the amount of
 * free space in a rx data store.
 *
 * \param [in] physicalEndpoint The endpoint number.
 * \return Nothing.
 */
extern void usbDclRxBufferFreeSpace(Int8 physicalEndpoint);

/** Called after received data was written to the RX buffer.
 *
 * Once the data has been written to the address returned by usbDclGetRxBufferWritePtr()
 * this function is called to explicitly inform the DCL what has been done.
 * The dataPtr parameter will match the address provided by the DCL, and the
 * length will be that verified or adjusted by the DCL in the usbDclGetRxBufferWritePtr()
 * call. It is this function that will update buffer pointers and raise the data-received
 * indication to the application layers.
 *
 * \param [in] physicalEndpoint The endpoint number.
 * \param [in] dataPtr Pointer to where data has been written
 * \param [in] dataLength How many bytes have been written
 * \return Nothing.
 */
extern void usbDclDataRx(Int8 physicalEndpoint, Int8* dataPtr, Int16 dataLength);

/** Frame Event.
 *
 * Response to a frame interrupt. Update frame number in USB context.
 * \param [in] newFrameNumber  The updated frame number.
 * \return Nothing.
 */
extern void usbDclFrameEvent(Int16 newFrameNumber);

/** Called periodically to check if any deadlocked data that needs clearing.
 *
 * Called periodically (probably at the frame rate) as a timeout
 * for any deadlocked data that needs clearing.
 * Make sure we only affect OUT endpoints (i.e that have Rx buffers), that are
 * not 'paused',.. etc.
 *
 * \param [in] physicalEndpoint  Endpoint to poll.
 * \return Nothing.
 */
extern void usbDclFramePoll(Int8 physicalEndpoint);

/** Bus Reset Event.
 * Sends a usbBusResetInd signal to the USB stack task.
 *
 * \return Nothing.
 */
extern void usbDclBusResetInd(void);

/** Allow/Disallow power saving.
 *
 * Sends a SIG_USB_ALLOW_POWER_SAVING_IND signal to the USB target task to
 * either allow or disallow the target to enter the power saving state.
 *
 * \param [in] allowPowerSaving TRUE if power saving allowed.
 * \param [in] isISR TRUE if being called from an ISR.
 * \return Nothing.
 */
extern void usbDclAllowPowerSaving(Boolean allowPowerSaving, Boolean isISR);

/** Report max OUT buffering capacity.
 *
 * Returns the maximum allowed transfer (bytes) on an OUT endpoint before which
 * the stack should be informed of the new data.
 *
 * \param [in] physicalEndpoint The endpoint number.
 * \return The maximum allowed transfer on the physical endpoint (bytes).
 */
extern Int16 usbDclMaxOutTransfer(Int8 physicalEndpoint);

/** Is Cable Present?
 * This function enables the driver to determine if a cable is
 * currently attached.
 *
 * \return TRUE if a cable is attached, else FALSE
 * */
extern Boolean usbDclUsbCableIsPresent( void );

/** @} */ /* End of group */


/* To do: This should not be in *this* header file. */
extern void usbDclUsbCableStateChangeInd (Boolean usbCableInserted);


#endif /* (USBDRIVER_H) */
#endif /* (UPGRADE_USB) */

/* END OF FILE */


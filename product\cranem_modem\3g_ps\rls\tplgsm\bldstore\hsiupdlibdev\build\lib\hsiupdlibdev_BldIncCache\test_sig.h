/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/2g.mod/api/shinc/test_sig.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2007/01/12 13:10:41 $
 **************************************************************************
 *  File Description :
 *      test signals used to perform 'white' box testing of each unit
 **************************************************************************/


#ifndef TEST_SIG_H
#define TEST_SIG_H

/***************************************************************************
 * Nested Include Files
 **************************************************************************/
#include  <system.h>
#include  <kitqid.h>

#if defined (CC_TEST)
#   include <cctypes.h>
#endif

#if defined (MM_TEST)
#   include <mmtypes.h>
#endif

#if defined (RR_TEST)
#   include <rrdata.h>
#endif

#if defined (CB_TEST)
#   include <cb.h>
#endif

#if defined (SMTL_TEST)
#   include <smtlprot.h>
#endif

#if defined (SMRL_TEST)
#   include <smrlprot.h>
#endif

#if defined (SMCM_TEST)
#   include <smcmprot.h>
#endif
/***************************************************************************
 * Type Definitions
 **************************************************************************/


#if defined (RR_TEST)
typedef struct RrTestSignalTag
{
    Boolean     setRrData;      /* only used sending signals to RR */
    Boolean     monitorReq;     /* if TRUE, send a dump of internal data */
    TaskId      returnTask;     /* process to receive monitor signal */
    RrData      rrData;
}
RrTestSignal;
#else
typedef Int8 RrTestSignal;
#endif /* RR_TEST */

#if defined (CB_TEST)
typedef struct CbTestSignalTag
{
    SignedInt16 useMemory;      /* used to grab available memory */
    Boolean     monitorReq;     /* if TRUE, send a dump of internal data */
    TaskId      returnTask;     /* process to receive monitor signal */
    CbData      cbData;
}
CbTestSignal;
#else
typedef Int8
CbTestSignal;
#endif /* CB_TEST */

#if defined (MM_TEST)
typedef enum MmTestTypeTag
{
    MMTEST_SET_STATE,
    MMTEST_GET_STATE,
    MMTEST_SET_EVENT,
    MMTEST_SET_ACTION,
    MMTEST_START_TIMER,
    MMTEST_STOP_TIMER,
    MMTEST_SET_BROADCAST_DATA,
    MMTEST_SET_SIM_DATA,
    MMTEST_SET_LOC_UPDATE_DATA,
    MMTEST_SET_CM_CONNECTION,
    MMTEST_SET_ME_DATA,
    MMTEST_DUMP_MM
}
MmTestType;

typedef struct MmTestReqTag
{
    MmTestType                testType;
    MobilityManagerEntity     mmTest;
}
MmTestReq;
#else
typedef Int8
MmTestReq;
#endif /* MM_TEST */

#if defined (CC_TEST)
typedef struct CcTestUpdatesTag
{
    Boolean             allOfCc;        /* if true the following are not needed */
    Boolean             state;      /* U0, U0.1, U3...U19 as in GSM 04.08 */
    Boolean             genStatusEnq;     /* status timer */
    Boolean             setMainTimerRunning;
    Int8                timerExps;  /* number to load expiries with */
}
CcTestUpdates;

typedef struct CcTestSignalTag
{
    Err                 status;
    int                 line;
    char                fileName[13];

    Boolean             request;
    Boolean             update;
    CcTestUpdates       action;
    Boolean             responce;
    CallControlEntity   cc;
}
CcTestSignal;
#else
typedef Int8
CcTestUpdates;

typedef Int8
CcTestSignal;
#endif /* CC_TEST */

#if defined (SMCM_TEST)
typedef struct TestSmcmTag
{
   Boolean     dataValid;
   Int8        entityNum;
   SmcmData            smcmData;
}
TestSmcm;
#else
typedef Int8
TestSmcm;
#endif /* SMCM_TEST */

#if defined (SMRL_TEST)
typedef struct TestSmrlTag
{
   Boolean     dataValid;
   Int8        entityNum;
   SmrlData    smrlData;
} TestSmrl;
#else
typedef Int8
TestSmrl;
#endif

#if defined (SMTL_TEST)
typedef struct TestSmtlTag
{
    SmtlData    smtlData;
} TestSmtl;
#else
typedef Int8
TestSmtl;
#endif
#endif /* TEST_SIG_H */

/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/umc_sig.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2007/03/30 13:53:25 $
 **************************************************************************
 * File Description:
 *
 *    File containing the 'signals' union for UMAC.
 **************************************************************************/

#if !defined (UMA_SIG_H)
#define       UMA_SIG_H

#if defined (URLC_UMAC_MERGED)

#include <urluma_sig.h>

#else

/***************************************************************************
* Nested Include Files
***************************************************************************/
#include <ki_sig.h>
#include <cmac_sig.h>
#include <cmace_sig.h>
#include <umac_sig.h>
#include <umace_sig.h>
#include <uphy_sig.h>
#include <ccsd_sig.h>
#if defined (ENABLE_UPLANE_STATISTICS)
#include <uplanestats_sig.h>
#endif /* ENABLE_UPLANE_STATISTICS */
#if defined (ENABLE_FLEXIBLE_TRACE_FRAMEWORK)
#include <utftf_sig.h>
#endif

union Signal
{
#if defined(PS_L2_R8_API)
	CmacRachAccessReq               cmacRachAccessReq;
	CmacRachAccessCnf               cmacRachAccessCnf;
#if defined (UPGRADE_UL_ECF)    
      CmacEdchAccessCnf               cmacEdchAccessCnf;
#endif
#endif /*PS_L2_R8_API*/
    CmacRNTIConfigReq               cmacRNTIConfigReq;
    CmacCipheringConfigReq          cmacCipheringConfigReq;
    CmacHfnConfigReq                cmacHfnConfigReq;
    CmacHfnConfigCnf                cmacHfnConfigCnf;
    CmacRachFailuresAlertInd        cmacRachFailuresAlertInd;   /* CQ00069148 added */
    CmacHfnAbortReq                 cmacHfnAbortReq;
    CmacDeactivateReq               cmacDeactivateReq;
    CmacCsRabConfigReq              cmacCsRabConfigReq;
    CmacHandoverReq                 cmacHandoverReq;
    CmacRachConfigReq               cmacRachConfigReq;
    CmacRachTxStatusInd             cmacRachTxStatusInd; 
    CmacUlDedicatedTfcsConfigReq    cmacUlDedicatedTfcsConfigReq;
    CmacUlTfcSubsetConfigReq        cmacUlTfcSubsetConfigReq;
    CmacUlMinTfcSetConfigReq        cmacUlMinTfcSetConfigReq;
    CmacDlRbMappingConfigReq        cmacDlRbMappingConfigReq;
    CmacUlRbMappingConfigReq        cmacUlRbMappingConfigReq;
    CmacUlTrChConfigReq             cmacUlTrChConfigReq;
    CmacTrafficMeasurementInd       cmacTrafficMeasurementInd;
    CmacTrafficMeasurementReq       cmacTrafficMeasurementReq;
    CmacMeasurementStopReq          cmacMeasurementStopReq;
    CmacQualityMeasurementInd       cmacQualityMeasurementInd;
    CmacQualityMeasurementReq       cmacQualityMeasurementReq;
    CmacGetMeasurementReq           cmacGetMeasurementReq;
    CmacGetMeasurementCnf           cmacGetMeasurementCnf;
    CmacHfnMeasurementInd           cmacHfnMeasurementInd;
    CmacActivationTimeReq           cmacActivationTimeReq;
    CmacActivationTimeCnf           cmacActivationTimeCnf;
    CmacChangeCsHfnParams           cmacChangeCsHfnExported; /********** - add*/
#if defined (UPGRADE_ESCC)
	CmacActivationTimeOffsetReq		cmacActivationTimeOffsetReq;
	CmacActivationTimeOffsetCnf		cmacActivationTimeOffsetCnf;
#endif /* ESCC */
    CmacInternalTimerExpiry         cmacInternalTimerExpiry;
    CmacInternalMeasEvent           cmacInternalMeasEvent;
    CmacInternalReconfig            cmacInternalReconfig;
    CmacInternalResolveRbTrChConfig cmacInternalResolveRbTrChConfig;
    CmacInternalUlTmBearers         cmacInternalUlTmBearers;
    CmacTestModeReq                 cmacTestModeReq;
#if defined(UPGRADE_3G_HSDPA)
    CmacHsQueueConfigReq            cmacHsQueueConfigReq;
    CmacHsQueueReleaseReq           cmacHsQueueReleaseReq;
	CmacEhsQueueConfigReq           cmacEhsQueueConfigReq;
    CmacEhsQueueReleaseReq          cmacEhsQueueReleaseReq;	
	/*Fixed CQ23028 taoye modify begin 20121012*/
    CmacHRNTIConfigReq              cmacHRNTIConfigReq;   	
	/*Fixed CQ23028 taoye modify end 20121012*/
#endif /* UPGRADE_3G_HSDPA */
#if defined (UPGRADE_3G_EDCH)
    CmacEdchTfcsConfigReq           cmacEdchTfcsConfigReq;
    CmacEdchMacdFlowConfigReq       cmacEdchMacdFlowConfigReq;
    CmacEdchReleaseReq              cmacEdchReleaseReq;
#if defined (UPGRADE_UL_ECF)
    CmacEdchRachConfigReq           cmacEdchRachConfigReq;
    CmacEdchRachTxStatusInd         cmacEdchRachTxStatusInd;
	PhyEdchAccessCnf                phyEdchAccessCnf;
#endif
#endif /* UPGRADE_3G_EDCH */

#if defined(RRC_UPGRADE_ENG_MODE_REDESIGN)
	CmacEngEdchModeInfoReq				cmacEngEdchModeInfoReq;
#endif /** RRC_UPGRADE_ENG_MODE_REDESIGN */

    CmacUlTrChCombinationConfigReq  cmacUlTrChCombinationConfigReq;
    CmacDlTrChCombinationConfigReq  cmacDlTrChCombinationConfigReq;

#if defined(DEVELOPMENT_VERSION)
#if defined(ON_PC)
    CmacInternalTestRachReq         cmacInternalTestRachReq;
    CmacInternalTestRachCnf         cmacInternalTestRachCnf;
    CmacInternalTestCm              cmacInternalTestCm;
	CmacInternalTestReq				cmacInternalTestReq;					
#endif
    CmacDebugTfc                    cmacDebugTfc;
#endif

    UmacTrafficReq                  umacTrafficReq;
    UmacNoTrafficReq                umacNoTrafficReq;
    UmacUpdateTrafficInd            umacUpdateTrafficInd;
    UmacTrafficInd                  umacTrafficInd;
    UmacDataReq                     umacDataReq;
    UmacDataInd                     umacDataInd;
    UmacDlPduListInfoInd            umacDlPduListInfoInd;
    UmacDlPduListInfoRsp            umacDlPduListInfoRsp;
    UmacF8ChainComplete             umacF8ChainComplete;
    UmacTxStatusInd                 umacTxStatusInd;
    UmacUlConfigChangeInd           umacUlConfigChangeInd;
    UmacDlConfigChangeInd           umacDlConfigChangeInd;
#if defined(UPGRADE_3G_HSDPA)
    UmacHsPduListInfoInd            umacHsPduListInfoInd;
    UmacHsPduListInfoRsp            umacHsPduListInfoRsp;
    UmacHsDataInd                   umacHsDataInd;
    UmacHsDebugPhyHsDataInd         umacHsDebugPhyHsDataInd;
	UmacEhsPduListInfoInd           umacEhsPduListInfoInd;
    UmacEhsPduListInfoRsp           umacEhsPduListInfoRsp;
	UmacEhsDataInd					umacEhsDataInd;
	UmacEhsRecoveryInd				umacEhsRecoveryInd;
#if defined (ENABLE_UMAC_UNIT_TEST)
	UmacEhsUnitTestDataInd			umacEhsUnitTestDataInd;
	UmacEhsQueueResetInd			umacEhsQueueResetInd;
#endif//ENABLE_UMAC_UNIT_TEST

#endif /* UPGRADE_3G_HSDPA */

#if defined(DEVELOPMENT_VERSION)
    UmacDebugTfcSelection           umacDebugTfcSelection;
#endif /* DEVELOPMENT_VERSION */
#if defined (UPGRADE_3G_EDCH)
 
    PhyEdchRxChDataInd               phyEdchRxChDataInd;
#endif /* UPGRADE_3G_EDCH */

#if defined (UMACE_UNIT_TEST)
    UnitestUmaceSessionInitInd       unitestUmaceSessionInitInd;
    UnitestUmaceSessionReleasedInd   unitestUmaceSessionReleasedInd;
    UnitestUmaceDataInd             unitestUmaceDataInd;
    UnitestUmaceCheckOutputInd      unitestUmaceCheckOutputInd;
#endif /* UMACE_UNIT_TEST */

#if defined (ENABLE_UL_THROUGHPUT_IND)
    UmacMaxPsUlTranspTpInd          umacMaxPsUlTranspTpInd;
#endif /* ENABLE_UL_THROUGHPUT_IND */

#if !defined (PS_L2_R8_API)
    PhyAccessReq                    phyAccessReq;
#endif /*PS_L2_R8_API*/
    PhyAccessCnf                    phyAccessCnf;
    PhyDataReq                      phyDataReq;
    PhyDataInd                      phyDataInd;
    PhyStatusInd                    phyStatusInd;
    PhyFrameInd                     phyFrameInd;
    PhyStateInd                     phyStateInd;

#if defined(DEVELOPMENT_VERSION)
    PhyDebugDataInfoInd             umacPhyDebugDataInfoInd;
    PhyDebugDataInfoReq             umacPhyDebugDataInfoReq;
#endif
#if defined(UPGRADE_3G_HSDPA)
    PhyHsDataInd                    phyHsDataInd;
#if defined(ON_PC) || defined(EXCLUDE_HAW)
    PhyHsAssignPointerReqOnPc       phyHsAssignPointerReq;
#endif /* ON_PC */
#endif

#if defined (ENABLE_FLEXIBLE_TRACE_FRAMEWORK)
    UtFlexibleTraceControlReq       utFlexibleTraceControlReq;
#endif
    CcsdConfigReq                   ccsdConfigReq;
    CcsdReleaseReq                  ccsdReleaseReq;
    CcsdSuspendReq                  ccsdSuspendReq;
    CcsdResumeReq                   ccsdResumeReq;
    CcsdUlTtiInd                    ccsdUlTtiInd;
    CcsdDlTtiInd                    ccsdDlTtiInd;

    KiTimerExpiry                   kiTimerExpiry;
#if defined(ENABLE_PS_L2_TOOL)
	L2ToolPlwPhyDataInd				l2ToolPlwPhyDataInd;
#endif
#if defined (ENABLE_UPLANE_STATISTICS)
    UplaneStatisticsReport          uplaneStatisticsReport;
#endif /* ENABLE_UPLANE_STATISTICS */
};

#endif

#endif

/* END OF FILE */


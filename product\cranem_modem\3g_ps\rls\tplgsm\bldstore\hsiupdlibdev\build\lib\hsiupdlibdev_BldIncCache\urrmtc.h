/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urrmtc.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/27 15:10:43 $
 **************************************************************************
 * File Description:
 *
 *    Header file for URRMTC.C.
 **************************************************************************/

#if !defined (URRMTC_H)
#define       URRMTC_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <kernel.h>
#include <utper.h>
#include <urrtypes.h>
#include <diag.h>
#if defined(UPGRADE_DSDS) && defined(UPGRADE_COMMON_STORE)
#include <rrcdsutils.h>
#endif/*DSDS && UPGRADE_COMMON_STORE*/

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/

#define ENCODED_DATA_LENGTH_OCTETS  1000
#define ENCODED_DATA_LENGTH_BITS    (ENCODED_DATA_LENGTH_OCTETS * BITS_PER_INT8)

/* Boolean+Int32+Int8(4bits) = 37 bits; then round up to 40 bits = 5 OCTETS */
#define ENCODED_INTEG_PROT_INFO_LENGTH_OCTETS 5
#define ENCODED_INTEG_PROT_INFO_LENGTH_BITS   37

#define MAX_SIG_TRACE_LOG_COUNT    30
#define MAX_PT_DELTA_IN_SECS       30  // Periodinc tracing reporting period

/***************************************************************************
*  Macro Functions
***************************************************************************/
#if defined (KI_RLG_ENABLE_URRC_TRACE_LOG)
#define URR_LOG_RLG_TRACE_STATE(kiStateMachineTypeInd, newState, oldState) \
    UrrMtcRlgLogUrrcTraceStateChange(MODULE_NAME, __LINE__, kiStateMachineTypeInd, oldState, newState);
#else
#define URR_LOG_RLG_TRACE_STATE
#endif //KI_RLG_ENABLE_URLC_TRACE_LOG

/***************************************************************************
 * Types
 ***************************************************************************/

typedef enum UrrcMtcTimerIdentityTag
{
    URRC_MTC_EXPERIMENT_TIMER = URRC_MTC_TIMER_BASE + 1,
    URRC_MTC_ENG_MODE_TIMER   = URRC_MTC_TIMER_BASE + 2
}
UrrcMtcTimerIdentity;

typedef enum UrrMtcTraceCategoryTag
{
    URRC_CSR_DEFAULT = 0x00000001,
    URRC_CSR_DEEP    = 0x00000002,
    URRC_CSR_HCS     = 0x00000004,
    URRC_CSR_RANKING = 0x00000008,
    URRC_MTC_DEFAULT = 0x00000010,
    URRC_MTC_DEEP    = 0x00000020,
    URRC_SMC_DEFAULT = 0x00000040,
    URRC_SMC_DEEP    = 0x00000080,
    URRC_RBC_DEFAULT = 0x00000100,
    URRC_RBC_DEEP    = 0x00000200,
    URRC_RBC_SPARE_1 = 0x00000400,
    URRC_RBC_SPARE_2 = 0x00000800,
    URRC_MCR_DEFAULT = 0x00001000,
    URRC_MCR_IA      = 0x00002000,
    URRC_MCR_IE      = 0x00004000,
    URRC_MCR_IR      = 0x00008000,
    URRC_CER_DEFAULT = 0x00010000,
    URRC_CER_SPARE_1 = 0x00020000,
    URRC_CER_SPARE_2 = 0x00040000,
    URRC_MCR_DEEP    = 0x00080000,
    URRC_CMR_DEFAULT = 0x00100000,
    URRC_CMR_DEEP    = 0x00200000,
    URRC_CMR_SPARE_2 = 0x00400000,
    URRC_CMR_SPARE_3 = 0x00800000,
    URRC_AIS_DEFAULT = 0x01000000,
    URRC_AIS_DEEP    = 0x02000000,
    URRC_AIS_SPARE_1 = 0x04000000,
    URRC_AIS_SPARE_2 = 0x08000000,
    URRC_SIR_DEFAULT = 0x10000000,
    URRC_SIR_DEEP    = 0x20000000,
    URRC_SIR_SPARE_1 = 0x40000000
}
UrrMtcTraceCategory;
/***************************************************************************
 * Typed Constants
 ***************************************************************************/

/***************************************************************************
 * Global Function Prototypes
 ***************************************************************************/

KI_ENTRY_POINT UrrTask (void);
#if defined(UPGRADE_DSDSWB)

void UrrMtcInit (RadioResourceControlEntity *urr_p);
void ProcessExternalSignal (RadioResourceControlEntity *urr_p);

void ProcessInternalSignals (RadioResourceControlEntity *urr_p);
#endif/*UPGRADE_DSDSWB*/

void UrrMtcCreateInternalSignal (       SignalBuffer          *intSig_p,
                                        SignalId              signalType,
                                        Int16                 signalSize,
                                        UrrSubProcessId       currentSubProcess,
                                        UrrSubProcessId       destinationSubProcess);

void UrrMtcSendInternalSignal (SignalBuffer *intSig_p);
void UrrMtcEnqueueInternalSignal (SignalBuffer *signal_p,
                                         KiUnitQueue  *queue_p);
Boolean UrrMtcDequeueInternalSignal (         KiUnitQueue  *queue_p , SignalBuffer *signal_p);
void UrrMtcFlushEnqueuedInternalSignals ( KiUnitQueue *queue_p);
void UrrMtcFlushSirEnqueuedInternalSignals ( KiUnitQueue *queue_p);
void UrrMtcFlushSirEnqueuedInternalSpecifiedSignal ( KiUnitQueue *queue_p, SignalId signalType); /* CQ00049767, added */

#if defined (DEVELOPMENT_VERSION)
#define SET_CURRENT_SUB_PROCESS_ID(ID) UrrMtcSetCurrentSubProcess (ID)
#else
#define SET_CURRENT_SUB_PROCESS_ID(ID)
#endif

#if defined (DEVELOPMENT_VERSION)
void UrrMtcSetCurrentSubProcess (UrrSubProcessId subProcessId);
void UrrMtcOutputRrcDebugInformationInd (Int16 byteLength, Int8 *data);

void UrrMtcOutputRrcDebugMscRx (UrrSubProcessId destination, SignalId signal);
void UrrMtcOutputRrcDebugMscTx (TaskId destination, SignalId signal);
void UrrMtcOutputRrcDebugMscExtEnqueue (SignalId signal);
void UrrMtcOutputRrcDebugMscExtDequeue (SignalId signal);
void UrrMtcOutputRrcDebugMscInt (UrrSubProcessId source,
                                 UrrSubProcessId destination,
                                 SignalId signal);
void UrrMtcOutputRrcDebugMscIntEnqueue (SignalId signal);
void UrrMtcOutputRrcDebugMscIntDequeue (SignalId signal);

void UrrMtcOutputRrcDebugMscTimerExpiry (UrrSubProcessId destination,
                                         KiTimerUserValue value,
                                         KiTimerId        id);
void UrrMtcOutputRrcDebugMscTimerStart (KiTimer *timer);
void UrrMtcOutputRrcDebugMscTimerStop (KiTimer *timer);

void UrrMtcOutputRrcDebugMscFunctionCall (UrrSubProcessId destination,
                                          const char * funcName);
#endif

    /* Return TRUE if the CN identity is the same as that of the UE */
Boolean UrrMtcMatchUeId (UPagingRecord_cn_Identity *cnId_p);

    /* InitialUE_Identity storage and test functions */
void UrrMtcStoreInitialUeIdentity (UInitialUE_Identity *initialUeId_p,
                                   UCN_DomainIdentity domainId);

void UrrMtcConvertPlmnFromIntToDigits (Plmn           plmnIn,
                                       UPLMN_Identity *plmnOut);

    /* Access functions for the u-RNTI and c-RNTI */
UU_RNTI UrrMtcGetUrnti (void);
USRNC_Identity UrrMtcGetSrncId (void);
void    UrrMtcSetUrnti (UU_RNTI uRnti);
Boolean UrrMtcMatchesCurrentUrnti (UU_RNTI *ueUrnti_p);
Boolean UrrMtcGetCrnti (UC_RNTI *cRnti_p);
void    UrrMtcSetCrnti (UC_RNTI cRnti);
void    UrrMtcClearCrnti (void);

    /* Access functions for umtsOnlyMode variable */
void    UrrMtcSetUmtsOnlyMode (Boolean umtsOnlyMode);
Boolean UrrMtcGetUmtsOnlyMode (void);

    /* Interface for the Access Class variable. */
Int16 UrrMtcGetAccessClass (void);
void UrrMtcSetAccessClass (Int16 newAccessClass);

    /* Access functions to store the parts of the InitialUE_Identity */
void UrrMtcStoreUeIdTmsi (UTMSI_GSM_MAP newTmsi,
                          UAreaIdentity *areaIdentity_p);
void UrrMtcStoreUeIdTmsiRelease (void);
void UrrMtcStoreUeIdPtmsi (UP_TMSI_GSM_MAP newPtmsi,
/* CQ00051259 add begin */
                           Boolean isPTmsiMapped,
/* CQ00051259 add end */
                           UAreaIdentity *areaIdentity_p);
void UrrMtcStoreUeIdPtmsiRelease (void);
void UrrMtcStoreUeIdImei (UIMEI newImei);
void UrrMtcStoreUeIdImeiRelease (void);
void UrrMtcStoreUeIdImsi (UIMSI_GSM_MAP *newImsi_p);
UIMSI_GSM_MAP *UrrMtcGetUeIdImsi (void);
void UrrMtcSetInitialUeIdentity (void);
UInitialUE_Identity *UrrMtcGetInitialUeIdentity (void);
Boolean UrrMtcInitialUeIdentityEqual (UInitialUE_Identity *ueIdA_p);

void UrrMtcSetGsmClassmark2 (UGSM_Classmark2 gsmClassmark2);
void UrrMtcGetGsmClassmark2 (UGSM_Classmark2 *gsmClassmark2);
void UrrMtcSetGsmClassmark3 (UGSM_Classmark3 gsmClassmark3);
void UrrMtcGetGsmClassmark3 (UGSM_Classmark3 *gsmClassmark3);

Boolean UrrMtcGetMccFromSim (Int8 *firstByte_p);

void UrrMtcProcessSib1 (USysInfoType1 *uSysInfoType1_p);

void UrrMtcProcessConnTimersAndConstants (
                                  UrrConnTimerSource             source,
                                  Boolean                        present,
                                  UUE_ConnTimersAndConstants_latest  *ctc_p,
                                  Boolean                        n_312extPresent,
                                  UN_312ext                      n_312ext,
                                  Boolean                        n_315extPresent,
                                  UN_315ext                      n_315ext,
                                  Boolean                        t_323extPresent,
                                  UT_323                         t_323ext);

#if !defined (UPGRADE_EXCLUDE_2G)
void UrrMtcSetConnectedModeTimersAndConstsToDefault (void);
#endif

Boolean UrrMtcGetDebugAirInterfaceInfo (void);
Boolean UrrMtcGetIntegrityProtectionDisabled (void);
/* Engineering mode functions - declared here to avoid exporting
 * RRC specific stuff outside the RRC. */
#if defined (RRC_UPGRADE_ENG_MODE_REDESIGN) //********** begin
extern void UrrMtcEngModeInfoReq (RadioResourceControlEntity *urr_p);
extern void UrrMtcEngModeSendUeState (UrrSmcModeAndState oldModeAndState, UrrSmcModeAndState newModeAndState);
extern void UrrMtcEngModeInit (void);
#endif //RRC_UPGRADE_ENG_MODE_REDESIGN :********** end

void UrrSendCellLockReq(RrcCellLockReq *rrcCellLockReq_p);
void UrrMtcRrcCellLockCnf(RadioResourceControlEntity *urr_p);

/* Engineering mode functions - declared here to avoid exporting
 * RRC specific stuff outside the RRC. */
extern void UrrMtcEmRrcEngInfoReq (RadioResourceControlEntity *urr_p);
extern void UrrMtcEmHandleTimers (void);
extern void UrrMtcEmInit (void);

void UrrMtcInitPeriodicTracing(void);
void UrrMtcPeriodicTracingHandler(void);
void UrrMtcPeriodicTracingLogSignal(Int32 sigID);
void UrrMtcStartDebugTimer(Int16 timeout);

/* Add for ********** begin */
#if defined (UPGRADE_DSDS)
#if !defined(UPGRADE_COMMON_STORE)
void SendGrrRcSuspendReq (GrrRcSuspendCause cause);
void SendGrrRcSuspendCnf (void);
#endif
void SendCphySuspendReq (void);
void SendCphyResumeReq (void);
Boolean UrrMtcGetSuspendFlag (void);
Boolean UrrMtcGetSuspendDuePSFlag (void);  /* for ********** */
void UrrClearDsInfoAfterMoveToOtherRat (void);//********** added
#if defined (ENABLE_WB_PCH_SIM2_MEASUREMENT)
Boolean UrrMtcGetSentCphyResumeByPchFlagOfOtherCard (void);/* ********** added */
Boolean UrrMtcGetResumeDuePchFlag (void);
#endif
#if !defined(UPGRADE_COMMON_STORE)
Boolean UrrMtcGetSendResumeReqFlag (void);
void UrrMtcSetSendResumeReqFlag (Boolean sendResumeReqFlag);
#endif
void SendCphyStopActivityReq (void);    /* for ********** */
void SendCphyCancelStopActivityReq (void);   /* for ********** */
#if defined(UPGRADE_COMMON_STORE)
void SendIratDsAbortGsmPSReq (Boolean isPagingFailureInd);  /* for **********,CQ00018244 */
#else
void SendGrrRcAbortPSReq (Boolean isPagingFailureInd);  /* for **********,CQ00018244 */
#endif

#if !defined(UPGRADE_COMMON_STORE)
void SendGrrRcCancelAbortPsReq (void); /*CQ00024790*/
void SendGrrRcPowerOffCompleteInd (void); /*CQ00025299*/
void SendGrrRcResumeCnf (void);        /* for ********** */
Boolean UrrMtcGetGrrrcResumeCnfFlag (void); /* for ********** */
void UrrMtcSetGrrrcResumeCnfFlag (Boolean waitGrrrcResumeCnfFlag);  /* for ********** */
#endif
void UrrMtcSetReceivedRrcDeactReqWaitResumeFlag(
                                      Boolean receivedRrcDeactReqWaitResumeFlag);/* ********** */
Boolean UrrMtcGetReceivedRrcDeactReqWaitResumeFlag(void);       /* ********** */
Boolean UrrMtcGetSuspendCnfDelayedFlag (void);/*********** add*/
void SendCphyAbortPlmnReq (void);    /*********** add*/
#if defined(UPGRADE_COMMON_STORE)
void UrrMtcHandleIratDsSuspendReq (IratSuspendCause  cause);
void UrrMtcHandleIratDsSuspendReq_BG (IratSuspendCause  cause); //********** added
#endif
Boolean UrrIsReselectionOrServiceOnOtherCard (void);
Boolean UrrIsReselectionNonConnectedStateOrServiceOnOtherCard (void);

Boolean UrrMtcGetWaitForResumeFinishFlag (void);
Boolean UrrMtcSetWaitForResumeFinishFlag (Boolean waitFlag);
Boolean UrrMtcGetSuspendL1Flag (void);
Boolean UrrMtcSetSuspendL1Flag (Boolean suspendFlag);
void UrrMtcSetUrrSuspendedFlag (Boolean suspendFlag); //********** added

#endif /*UPGRADE_DSDS*/
/* Add for ********** end */


#if defined (KI_RLG_ENABLE_URRC_TRACE_LOG)
void UrrMtcRlgLogUrrcTraceStateChange(
    const char  *file_p,
    Int32       line,
    KiStateMachineTypeInd  kiStateMachineTypeInd,
    Int32                  oldState,
    Int32                  newState);
#endif

void UrrMtcSetNetworkmode(NetworkMode networkMode);
NetworkMode UrrMtcGetNetworkmode(void);
Boolean UrrMtcGetSupportGsmMode(void);
Boolean UrrMtcGetSupportLteMode(void);
#if defined(UPGRADE_DSDSWB)
Boolean UrrMtcGetOtherCardSupportLteMode(void);
#endif
void UrrMtcIratErrcUrrLteUeCapInfoInd (RadioResourceControlEntity *urr_p);
Int16 UrrMtcGetlteueCapByteString (Int8 *lteueCapByteString);/*********** change to int16*/
/*********** START */
#if defined UPGRADE_CSG1
Boolean UrrMtcGetSupportUmtsMode(void);
#endif //UPGRADE_CSG1
/*********** END */
/*********** add begin*/
void UrrMtcSetisPcsNW (Boolean isPcsNWPresent, Boolean isPcsNW);
Boolean UrrMtcGetisPcsNW (Boolean *isPcsNW);
/*********** add end*/
void UrrMtcSendRrcSacCommonInfoInd(void);   /* CQ00054105, added *//*CQ00084302 change*/

#if !defined ENABLE_GRR_UNIT_TESTING    /* ********** add for GRR UT */
void UrrMtcSendRrcMonitorUeInd (UmtsMonitorEventType enventId); //********** added
#endif

#if defined (UPGRADE_ECID)
extern void UrrMtcEmRrcEcidMeasReq (RadioResourceControlEntity *urr_p); //********** add
#endif/*UPGRADE_ECID*/

void UrrMtcPrintNetworkmode(void); /*********** add */

#if defined(UPGRADE_DS_PHASE_II_WITH_NAS)
void urrSendRrcDsPagingFailureInd(void);
#endif

#if !defined(UPGRADE_COMMON_STORE)
void UrrMtcHandleGrrRcSuspendReq (GrrRcSuspendCause  cause);
#endif

#if (defined(UPGRADE_DSDSWB) || defined(UPGRADE_DSDSLTE)) && defined(UPGRADE_PLMS)
void urrMtcHandleBufferedSuspendReqAfterIrat (Boolean iratSuccess,AsRat rat);
#endif

/****************** Debug Functions Section ****************/
#if defined (UPGRADE_GENIE_INTERNAL) /* ********** changed from ON_PC*/

#define RRC_TRACE_TEXT(cAT2,cAT3,cATEGORY,tEXT) \
    if (RRC_TRACE_MASK & cATEGORY) \
    { \
        printf("[%s]  , " tEXT, #cAT3); \
    }

#define RRC_TRACE_1(cAT2,cAT3,cATEGORY,tEXT,vAL1)\
    if (RRC_TRACE_MASK & cATEGORY) \
    { \
         printf("[%s]  , " tEXT, #cAT3,vAL1 ); \
    }


#define RRC_TRACE_2(cAT2,cAT3,cATEGORY,tEXT,vAL1,vAL2)\
    if (RRC_TRACE_MASK & cATEGORY) \
    { \
        printf("[%s]  , " tEXT, #cAT3,vAL1,vAL2); \
    }

#define RRC_TRACE_3(cAT2,cAT3,cATEGORY,tEXT,vAL1,vAL2,vAL3)\
    if (RRC_TRACE_MASK & cATEGORY) \
    { \
        printf("[%s]  , " tEXT, #cAT3,vAL1,vAL2,vAL3); \
    }

#define RRC_TRACE_4(cAT2,cAT3,cATEGORY,tEXT,vAL1,vAL2,vAL3,vAL4)\
    if (RRC_TRACE_MASK & cATEGORY) \
    { \
        printf("[%s]  , " tEXT, #cAT3,vAL1,vAL2,vAL3,vAL4); \
    }

#define RRC_TRACE_5(cAT2,cAT3,cATEGORY,tEXT,vAL1,vAL2,vAL3,vAL4,vAL5)\
    if (RRC_TRACE_MASK & cATEGORY) \
    { \
        printf("[%s]  , " tEXT, #cAT3,vAL1,vAL2,vAL3,vAL4,vAL5); \
    }

#define RRC_TRACE_6(cAT2,cAT3,cATEGORY,tEXT,vAL1,vAL2,vAL3,vAL4,vAL5,vAL6)\
    if (RRC_TRACE_MASK & cATEGORY) \
    { \
        printf("[%s]  , " tEXT, #cAT3,vAL1,vAL2,vAL3,vAL4,vAL5,vAL6); \
    }

#define RRC_TRACE_7(cAT2,cAT3,cATEGORY,tEXT,vAL1,vAL2,vAL3,vAL4,vAL5,vAL6,vAL7)\
    if (RRC_TRACE_MASK & cATEGORY) \
    { \
        printf("[%s]  , " tEXT, #cAT3,vAL1,vAL2,vAL3,vAL4,vAL5,vAL6,vAL7); \
    }

#define RRC_TRACE_8(cAT2,cAT3,cATEGORY,tEXT,vAL1,vAL2,vAL3,vAL4,vAL5,vAL6,vAL7,vAL8)\
    if (RRC_TRACE_MASK & cATEGORY) \
    { \
        printf("[%s]  , " tEXT, #cAT3,vAL1,vAL2,vAL3,vAL4,vAL5,vAL6,vAL7,vAL8); \
    }

#define RRC_TRACE_9(cAT2,cAT3,cATEGORY,tEXT,vAL1,vAL2,vAL3,vAL4,vAL5,vAL6,vAL7,vAL8,vAL9)\
    if (RRC_TRACE_MASK & cATEGORY) \
    { \
        printf("[%s]  , " tEXT, #cAT3,vAL1,vAL2,vAL3,vAL4,vAL5,vAL6,vAL7,vAL8,vAL9); \
    }

#define RRC_TRACE_10(cAT2,cAT3,cATEGORY,tEXT,vAL1,vAL2,vAL3,vAL4,vAL5,vAL6,vAL7,vAL8,vAL9,vAL10)\
    if (RRC_TRACE_MASK & cATEGORY) \
    { \
        printf("[%s]  , " tEXT, #cAT3,vAL1,vAL2,vAL3,vAL4,vAL5,vAL6,vAL7,vAL8,vAL9,vAL10); \
    }

#define RRC_TRACE_11(cAT2,cAT3,cATEGORY,tEXT,vAL1,vAL2,vAL3,vAL4,vAL5,vAL6,vAL7,vAL8,vAL9,vAL10,vAL11)\
    if (RRC_TRACE_MASK & cATEGORY) \
    { \
        printf("[%s]  , " tEXT, #cAT3,vAL1,vAL2,vAL3,vAL4,vAL5,vAL6,vAL7,vAL8,vAL9,vAL10,vAL11); \
    }

#define RRC_TRACE_12(cAT2,cAT3,cATEGORY,tEXT,vAL1,vAL2,vAL3,vAL4,vAL5,vAL6,vAL7,vAL8,vAL9,vAL10,vAL11,vAL12)\
    if (RRC_TRACE_MASK & cATEGORY) \
    { \
        printf("[%s]  , " tEXT, #cAT3,vAL1,vAL2,vAL3,vAL4,vAL5,vAL6,vAL7,vAL8,vAL9,vAL10,vAL11,vAL12); \
    }

#define RRC_TRACE_STRUCT(cAT2,cAT3,cATEGORY,tEXT,pTR,sIZE)\
    if (RRC_TRACE_MASK & cATEGORY) \
    { \
        printf("[%s]  , " tEXT, #cAT3,pTR,sIZE); \
    }

#else

#define RRC_TRACE_TEXT(cAT2,cAT3,cATEGORY,tEXT) \
    if (RRC_TRACE_MASK & cATEGORY) \
    { \
        DIAG_FILTER(PS_3G_RRC, cAT2, cAT3, DIAG_INFORMATION); \
        diagTextPrintf(tEXT); \
    }

#define RRC_TRACE_1(cAT2,cAT3,cATEGORY,tEXT,vAL1)\
    if (RRC_TRACE_MASK & cATEGORY) \
    { \
        DIAG_FILTER(PS_3G_RRC, cAT2, cAT3, DIAG_INFORMATION); \
        diagPrintf(tEXT,vAL1); \
    }

#define RRC_TRACE_2(cAT2,cAT3,cATEGORY,tEXT,vAL1,vAL2)\
    if (RRC_TRACE_MASK & cATEGORY) \
    { \
        DIAG_FILTER(PS_3G_RRC, cAT2, cAT3, DIAG_INFORMATION); \
        diagPrintf(tEXT,vAL1,vAL2); \
    }

#define RRC_TRACE_3(cAT2,cAT3,cATEGORY,tEXT,vAL1,vAL2,vAL3)\
    if (RRC_TRACE_MASK & cATEGORY) \
    { \
        DIAG_FILTER(PS_3G_RRC, cAT2, cAT3, DIAG_INFORMATION); \
        diagPrintf(tEXT,vAL1,vAL2,vAL3); \
    }

#define RRC_TRACE_4(cAT2,cAT3,cATEGORY,tEXT,vAL1,vAL2,vAL3,vAL4)\
    if (RRC_TRACE_MASK & cATEGORY) \
    { \
        DIAG_FILTER(PS_3G_RRC, cAT2, cAT3, DIAG_INFORMATION); \
        diagPrintf(tEXT,vAL1,vAL2,vAL3,vAL4); \
    }

#define RRC_TRACE_5(cAT2,cAT3,cATEGORY,tEXT,vAL1,vAL2,vAL3,vAL4,vAL5)\
    if (RRC_TRACE_MASK & cATEGORY) \
    { \
        DIAG_FILTER(PS_3G_RRC, cAT2, cAT3, DIAG_INFORMATION); \
        diagPrintf(tEXT,vAL1,vAL2,vAL3,vAL4,vAL5); \
    }

#define RRC_TRACE_6(cAT2,cAT3,cATEGORY,tEXT,vAL1,vAL2,vAL3,vAL4,vAL5,vAL6)\
    if (RRC_TRACE_MASK & cATEGORY) \
    { \
        DIAG_FILTER(PS_3G_RRC, cAT2, cAT3, DIAG_INFORMATION); \
        diagPrintf(tEXT,vAL1,vAL2,vAL3,vAL4,vAL5,vAL6); \
    }

#define RRC_TRACE_7(cAT2,cAT3,cATEGORY,tEXT,vAL1,vAL2,vAL3,vAL4,vAL5,vAL6,vAL7)\
    if (RRC_TRACE_MASK & cATEGORY) \
    { \
        DIAG_FILTER(PS_3G_RRC, cAT2, cAT3, DIAG_INFORMATION); \
        diagPrintf(tEXT,vAL1,vAL2,vAL3,vAL4,vAL5,vAL6,vAL7); \
    }

#define RRC_TRACE_8(cAT2,cAT3,cATEGORY,tEXT,vAL1,vAL2,vAL3,vAL4,vAL5,vAL6,vAL7,vAL8)\
    if (RRC_TRACE_MASK & cATEGORY) \
    { \
        DIAG_FILTER(PS_3G_RRC, cAT2, cAT3, DIAG_INFORMATION); \
        diagPrintf(tEXT,vAL1,vAL2,vAL3,vAL4,vAL5,vAL6,vAL7,vAL8); \
    }

#define RRC_TRACE_9(cAT2,cAT3,cATEGORY,tEXT,vAL1,vAL2,vAL3,vAL4,vAL5,vAL6,vAL7,vAL8,vAL9)\
    if (RRC_TRACE_MASK & cATEGORY) \
    { \
        DIAG_FILTER(PS_3G_RRC, cAT2, cAT3, DIAG_INFORMATION); \
        diagPrintf(tEXT,vAL1,vAL2,vAL3,vAL4,vAL5,vAL6,vAL7,vAL8,vAL9); \
    }

#define RRC_TRACE_10(cAT2,cAT3,cATEGORY,tEXT,vAL1,vAL2,vAL3,vAL4,vAL5,vAL6,vAL7,vAL8,vAL9,vAL10)\
    if (RRC_TRACE_MASK & cATEGORY) \
    { \
        DIAG_FILTER(PS_3G_RRC, cAT2, cAT3, DIAG_INFORMATION); \
        diagPrintf(tEXT,vAL1,vAL2,vAL3,vAL4,vAL5,vAL6,vAL7,vAL8,vAL9,vAL10); \
    }

#define RRC_TRACE_12(cAT2,cAT3,cATEGORY,tEXT,vAL1,vAL2,vAL3,vAL4,vAL5,vAL6,vAL7,vAL8,vAL9,vAL10,vAL11,vAL12)\
    if (RRC_TRACE_MASK & cATEGORY) \
    { \
        DIAG_FILTER(PS_3G_RRC, cAT2, cAT3, DIAG_INFORMATION); \
        diagPrintf(tEXT,vAL1,vAL2,vAL3,vAL4,vAL5,vAL6,vAL7,vAL8,vAL9,vAL10,vAL11,vAL12); \
    }

#define RRC_TRACE_STRUCT(cAT2,cAT3,cATEGORY,tEXT,pTR,sIZE)\
    if (RRC_TRACE_MASK & cATEGORY) \
    { \
        DIAG_FILTER(PS_3G_RRC, cAT2, cAT3, DIAG_INFORMATION); \
        diagStructPrintf(tEXT,pTR,sIZE); \
    }
#endif
/*******************************************************/

#endif

/* END OF FILE */

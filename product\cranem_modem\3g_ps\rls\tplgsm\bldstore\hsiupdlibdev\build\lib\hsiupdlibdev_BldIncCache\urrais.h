/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urrais.h#7 $
 *   $Revision: #7 $
 *   $DateTime: 2007/03/30 13:53:25 $
 **************************************************************************
 * File Description:
 *
 *    Header file for URRAIS.C. Contains function call declarations
 *    for use by other URRC modules.
 **************************************************************************/
 /*******************************	Revision History ***********************
  CQ/Activity	  SW Eng	   Date              Description
  **********	  efrats      07/08/08        AGPS re-design: Get encoded ASN1 data & size.

  **************************************************************************/
#if !defined (URRAIS_H)
#define       URRAIS_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <urrtypes.h>
#include <urr_sig.h>
#include <utper.h>
#include <urrsirut.h>
#include <urrrbc.h>
#include <rrc_sig.h>
#include <uas_asn.h>

#if defined (RRC_UPGRADE_ENG_MODE_REDESIGN) //********** begin
#include <urrmtcem.h>
#endif //RRC_UPGRADE_ENG_MODE_REDESIGN :********** end

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/

    /* MaxTransactions for the TRANSACTIONS variable */
#define URRAIS_MAX_TRANSACTIONS         25

    /* Maximum number of SRB's handled by integrity protection as per */
    /* 25.331 section 10.3.3.17                                       */
#define MAX_INTEGRITY_PROTECTION_SRB    5

/* **********, modified begin */
/* #define RADIO_FREQUENCY_BAND_GSM_SIZE   5 */
#define RADIO_FREQUENCY_BAND_GSM_SIZE   4
/* **********, modified end */

//#define RADIO_FREQUENCY_BAND_FDD_SIZE   7
//#define RADIO_FREQUENCY_BAND_FDD2_SIZE  7

#define DEBUG_UNWRITTEN_VALUE           0xCC

#define MAX_PENDING_AM_DATA_IND         16 //**********

//********** remove begin
//#if defined (RRC_UPGRADE_ENG_MODE_REDESIGN) //********** begin
//********** remove end
// Max data octets that can come from L2 - enlarge it if needed
#define MAX_INCOMING_DATA_OCTETS		1520
//********** remove begin
//#endif //RRC_UPGRADE_ENG_MODE_REDESIGN :********** end
//********** remove end
/* ********** added begin */
#if defined (UPGRADE_3G_DBDC)
#define MAX_OF_BAND_COMBINATION_SUPPORTED 5
#endif
/* ********** added end */

/***************************************************************************
*   Macro Functions
***************************************************************************/


/***************************************************************************
 * Types
 ***************************************************************************/
typedef enum TransactionIdTypeTag
{
    TYPE1,  /* as 25.331 8.6.3.11 first category (RADIO BEARER SETUP etc) */
    TYPE2,  /* as 25.331 8.6.3.11 second category (RRC CONNECTION SETUP etc) */
    TYPE3,  /* as 25.331 8.6.3.11 third category (HANDOVER FROM UTRAN COMMAND etc) */
    TYPE4   /* as 25.331 8.6.3.11 fourth category (all the rest) */
}
TransactionIdType;

typedef struct DownlinkMessageTableTag
{
    Int8                    messageType;
    Boolean                 integrityCheck;
    TransactionIdType       tiType;
    Boolean                 processOnError;
    UrrSubProcessId         destProcess;
    UReceivedMessageType    rxMessageType;
}
DownlinkMessageTable;

    /* INTEGRITY_PROTECTION_INFO UE variable see 25.331 section 13.4.10 */
typedef struct UrrAisIntegrityProtectionInfoTag
{
    UrrAisIntegrityProtectionStarted          status;
    Boolean                                   reconfiguration;
    /* type of the AI message that contains the integrity reconfiguration */
    UReceivedMessageType                      msgType;
    UIntegrityProtInitNumber                  fresh;
    UCN_DomainIdentity                        cnDomain;
    UIntegrityProtectionAlgorithm_latest      integrityAlgorithm;
        
    UrrAisIntegrityProtectionSrbSpecificInfo  srb[MAX_INTEGRITY_PROTECTION_SRB];
    Int8                                      failureCounter;
#if defined (DEVELOPMENT_VERSION)
    /* thse are recorded to include in SIG_RRC_DEBUG_INTEGRITY_PROTECT_FAIL_IND */
    UrrAisIntegrityProtectionSrbSpecificInfo  debugTheSrbInProgress;
    Int32                                     debugMac;
    Int32                                     debugXmac;
    RrcDebugDiscardReason                     debugDiscardReason;
    URRC_MessageSequenceNumber                debugRrc_MessageSequenceNumber;
    Boolean                                   debugFirstCSSecurityCommand;
    Boolean                                   debugFirstPSSecurityCommand;
#endif
}
UrrAisIntegrityProtectionInfo;

    /* TRANSACTIONS definitions */
typedef enum TransactionAddTypeTag
{
    FIRST,      /* add transaction as first for message type */
    REPLACE,    /* replace existing transaction */
    ADD         /* add additional transaction */
}
TransactionAddType;

typedef enum TransactionToFindTag
{
    ANY_TRANSACTION,      /* search for any transaction */
    SPECIFIC_TRANSACTION  /* search for specific transaction */
}
TransactionToFind;

typedef struct UrrAisTransactionsTag
{
        /* The UIdentificationOfReceivedMessage contains */
        /* both the Transaction ID and the Message Type  */
    UIdentificationOfReceivedMessage    id;
    Boolean                             isValid;
}
UrrAisTransactions;

    /* For PROTOCOL_ERROR_INFORMATION variable */
typedef struct ProtocolErrorInformationTag
{
    Boolean             set;
    UProtocolErrorCause cause;
}
ProtocolErrorInformation;

typedef struct TxSignalQueueStoreTag
{
    BearerIdentity                  radioBearer;
    Boolean                         notifyUmTx;
    Int16                           messageIdentifier;
    UrrAisAirSignalType             messageType;
    Int8                            messageTag;
    Boolean                         isSecurityRespMsg; /* used to suspend all messages
                                                        * on an RB except those needed
                                                       * to rsp'd to security cfg */
    UrrSubProcessId                 originatingSubProcess;
    UrrAisStates                    state;
    Boolean                         muiValid;
    UrlcMessageUnitIdentifier       mui;

    Boolean                         msgTypeRequiresIntegProtInfo;
    Boolean                         initialEncodeWithIntegProtInfo;

    PerBuffer                       perBufferOut;
#if defined(DATA_IN_SIGNAL)
    Int8                           *perBufferOutBuffer_p;
#else
    Int8                           *tmmMemory_p;
#endif
    struct TxSignalQueueStore      *prev_p;
    struct TxSignalQueueStore      *next_p;
}
TxSignalQueueStore;

typedef enum UeCapabilityStateTag
{
    AIS_UE_CAP_IDLE,
    AIS_UE_CAP_WAIT_CONFIRM,
    AIS_UE_CAP_WAIT_T304_EXPIRY,
    AIS_UE_CAP_WAIT_CELL_UPDATE_TO_CELL_FACH,
    AIS_UE_CAP_CELL_UPDATE_OCCURRED /* ADD: ********** */
}
UeCapabilityState;

typedef enum SuspendConditionTag
{
    SECURITY_RSP_RB_2_SUSPEND,
    RB_134_SUSPEND,
    SRB_PEND_CFG_SUSPEND
}
SuspendCondition;

typedef enum IntegrityCheckInfoPresentTag
{
    INTEG_CHECK_INFO_NOT_PRESENT,
    INTEG_CHECK_INFO_PRESENT,
    INTEG_CHECK_INFO_PRESENT_INVALID
}
IntegrityCheckInfoPresent;

typedef struct UeCapabilityTag
{
    Boolean                                     ue_RadioAccessCapabilityPresent;
    UUE_RadioAccessCapability                   ue_RadioAccessCapability;

    Boolean                                     ue_RATSpecificCapabilityPresent;
    UInterRAT_UE_RadioAccessCapabilityList      ue_RATSpecificCapability;
    UInterRAT_UE_RadioAccessCapability_gsm      gsm_use_pointer_to_access;

    Boolean                                     eUTRA_RadioAccessCapability_Present;
    
    Boolean                                     v370NonCriticalExtensionsPresent;
    UUECapabilityInformation_v370NonCriticalExt v370NonCriticalExtensions;

    /* this is saved from the GSM classmark that NAS has translated for RRC,
     * default is all FALSE */
    UGsmSecurityCapability                      gsmSecurityCapability;
/* ********** added begin */
#if defined (UPGRADE_3G_DBDC)
    Int8                                numOfBandCom;
    Int8                                bandComList[MAX_OF_BAND_COMBINATION]; // configuation index according to TS25101. 0 is the first.
#endif
/* ********** added end */
}
UeCapability;

typedef struct InterRatHoInfoTransferredTag
{
    UeCapability        *ueCapabilityRaw_p;
    USTART_Value        startCs; /* START-CS */
    USTART_Value        startPs; /* START-PS */
    /* "Predefined configuration status information" is defined in 13.4.10a,
     * but it is not currently used so not defined here */
}
InterRatHoInfoTransferred;

/* Link list to queue RrcDataReq */
UT_SINGLE_LINK_LIST_DECLARE (RrcDataReqQueue, RrcDataReq);

/* structure used to pass an AIS message into AIS */
typedef struct SendAirSignalTag
{
    BearerIdentity              radioBearer;
    Boolean                     notifyUmTx; /* only used for RRC conn release */
    Int16                       messageIdentifier;
    UrrAisAirSignalType         messageType;
    Boolean                     isSecurityRespMsg; /* used to suspend all messages
                                                    * on an RB except those needed
                                                    * to rsp'd to security cfg */
    union
    {
        UUL_CCCH_MessageType *ul_CCCH_Message;
        UUL_DCCH_MessageType *ul_DCCH_Message;
    }
    message;
}
SendAirSignal;

typedef struct DelayedUrlcAmDataCnfTag
{
    /* ********** - the RLC received SDU with 2 PDUs:
          1.UrlcAmDataCnf 
          2.UrlcAmDataInd
          due to round robin consideration,the UrlcAmDataInd was sent to the RRC first
          and the situation finnaly caused Integrity Fail Indication while checking the integrity of the UrlcAmDataInd
          The solution is to keep the UrlcAmDataInd until arrival of UrlcAmDataCnf and then handle theUrlcAmDataInd
          It happend only at Huawei labs*/
                  
    Boolean                         waitingForUrlcAmDataCnf;
    /* indicate whether RBC is waiting for UrlcAmDataCnf */
    UrlcAmDataInd                   tempUrlcAmDataInd[MAX_PENDING_AM_DATA_IND];
    /* stored UrlcAmDataInd structure */
    KiTimer                         urlcAmDataCnfTimer;
    /* timer DB */
    Boolean                         timerIsRunning;

    Int8                            numOfPendingAmDataInd;  
    /* indicate whether the timer was activated */

	UrlcMessageUnitIdentifier       messageUnitIdentifier;/*********** add*/
}
DelayedUrlcAmDataCnf;

#if defined(UPGRADE_UL_ECF) // ********** begin  				
typedef enum UdtPendingReasonTag
{
    AIS_UDT_NOT_PENDING,
    AIS_UDT_PENDING_WAIT_CELL_UPDATE_CONFIRM,
    AIS_UDT_PENDING_WAIT_CELL_FACH
}
UdtPendingReason;			
#endif /* UPGRADE_UL_ECF  */ // ********** end

typedef struct UrrAisInfoTag
{
        /* PROTOCOL_ERROR_INDICATOR variable - see 25.331 */
    UProtocolErrorIndicator         protocolErrorIndicator;
        /* PROTOCOL_ERROR_REJECT variable - see 25.331 */
    Boolean                         protocolErrorReject;
        /* PROTOCOL_ERROR_INFORMATION variable - see 25.331 */
    ProtocolErrorInformation        protocolErrorInformation;

        /* Instantiation of the TRANSACTIONS variable */
    UrrAisTransactions           acceptedTransactions [URRAIS_MAX_TRANSACTIONS];
    UrrAisTransactions           rejectedTransactions [URRAIS_MAX_TRANSACTIONS];

        /* pointer to head of Tx message queue */
    TxSignalQueueStore              *txSignalQueueStore_p;

        /* The associated PerBuffer's, an incoming and outgoing */
    PerBuffer                       perBufferIn;
    Boolean                         perBufferInUsed;
        /* And the perBuffer static message pointers */
    UPCCH_Message                   *pcchMsg_p;
    UDL_CCCH_Message                *dlCcchMsg_p;
    UDL_DCCH_Message                *dlDcchMsg_p;
    UBCCH_FACH_Message              *dlBcchFachMsg_p;

        /* Integrity protection variables */
    UrrAisIntegrityProtectionInfo   integrityProtectionInfo;
        /* Integrity protection variables saved when the Air Interface message
         * was received, if "integrity mode info" IE was present */
    UrrAisIntegrityProtectionInfo   savedIntegrityProtectionInfo;
        /* Integrity protection variables saved when the Air Interface message
         * was received, if "integrity mode info" IE was present.
         * This field is reserved for SECURITY MODE COMMAND which can only
         * occur one at a time,  because if it is aborted we have to get
         * back to this context. The field 'savedIntegrityProtectionInfo' may
         * have been overwritten in the meantime. */
    UrrAisIntegrityProtectionInfo   savedSmcIntegrityProtectionInfo;

        /* message unit identifier (MUI) for AM data req/cnf */
    UrlcMessageUnitIdentifier       messageUnitIdentifier;

        /* memory for RRC STATUS signal */
    UrrSirAllocatedMemList          rrcStatusMemList;

        /* variables for UPLINK DIRECT TRANSFER signal */
    UUL_DCCH_MessageType            *uplinkDirectTransfer_p;
    UrrSirAllocatedMemList          ulDirXfrMemList;
    UNAS_MessagePriority            ulDirXfrPriority;
    RrcDataReqQueue                 rrcDataReqQueue;

        /* flag so we know to deallocate memory */
    Boolean                         ulDirXfrSent;
#if defined(UPGRADE_UL_ECF) // ********** begin  				
    Boolean                         udtIsPending;
    UdtPendingReason                udtIsPendingReason;				
#endif /* UPGRADE_UL_ECF  */ // ********** end
    /**************** UE CAPABILITY variables ********************/
    /* The UE capabilities are passed to AS by NAS. This is the record of those
     * capabilites. */
    UeCapability                    ueCapRaw;
#if !defined (UPGRADE_RR_MEM_OPT)
        /* RRC Variable: UE_CAPABILITY_TRANSFERRED */
    UeCapability                    ueCapTransferred;
        /* RRC Variable: UE_CAPABILITY_REQUESTED */
    UeCapability                    ueCapRequested;
#else
    /* RRC Variable: UE_CAPABILITY_TRANSFERRED */
    UeCapability                    *ueCapTransferred_p;
    /* RRC Variable: UE_CAPABILITY_REQUESTED */
    UeCapability                    *ueCapRequested_p;
#endif
#if !defined (UPGRADE_EXCLUDE_2G)
        /* RRC Variable: INTER_RAT_HANDOVER_INFO_TRANSFERRED */
    InterRatHoInfoTransferred       interRatHandoverInfoTransferred;
#endif
        /* RRC Variable: INTER_RAT_HANDOVER_INFO_TRANSFERRED */
    InterRatHoInfoTransferred       interRatHandoverInfoTransferredLte;
    UrrSirAllocatedMemList          ueCapInfoMemList;
    UeCapabilityState               ueCapState;
    KiTimer                         t304;
    Boolean                         t304running;
    Int8                            v304;
    UUL_DCCH_MessageType            *ueCapabilityInformation_p;

    /* Flag to indicate if in connected mode or rx msgs should be discarded */
    Boolean                         inConnectedMode;

    /* Flag to indicate if RB_2 messages (apart from security cfg response messages)
     * should be suspended */
    Boolean                         rb2IsSuspended;

    /* Flag to indicate if RB_134 messages should be suspended */
    Boolean                         rb134IsSuspended;

    /* Flag to indicate if SRB messages pending config should be suspended */
    Boolean                         srbPendingCfgSuspended;

    /* Flag to indicate if the UE has service or not in a non CELL_DCH state */
    Boolean                         noService;

    /* the number of UrlcAmDataReq that have not been acknowledged yet */
    Int8                            numberOfAmUmDataReqQueuedInRlc;

    Boolean                         awaitAuthFailureCnf;

    /* Ais holds classmark rawData for PCS & DCS */
    UGSM_Classmark3_str classmark3_DCS;
    UGSM_Classmark3_str classmark3_PCS;

    /* process scenario  when the UE sent configuration complete message but didn't receive UrlcAmDataCnf yet  */
    DelayedUrlcAmDataCnf            delayedUrlcAmDataCnf;

    /* TRUE between the point AIS requested measOnRach from MCR until the point MCR responded */
    Boolean                         dtMeasOnRachOngoing;

    Boolean                         securityModeIntergrityFailure [maxCNdomains];/*********** add*/

    Boolean                         t304Expired; /*CQ00040426 add begin*/
//********** remove begin
//#if defined (RRC_UPGRADE_ENG_MODE_REDESIGN) //********** begin
//********** remove end
	//copy of the incoming  RLC SDU data to be used later to be sent  NAS
	Int8							incomingData[MAX_INCOMING_DATA_OCTETS];
//********** remove begin
//#endif//RRC_UPGRADE_ENG_MODE_REDESIGN :********** end
//********** remove end
#if defined UPGRADE_SMC_REL10
    Int16 securityModeCommandMessageIdentifier; /* ********** add */
#endif //UPGRADE_SMC_REL10	

/* ********** added begin */
#if defined (ENABLE_WAIT_ABORT_SEARCH_CNF)
    UBCCH_ModificationInfo         bcchModificationInfo;
#endif

}
UrrAisInfo;
/* ********** added end */

/***************************************************************************
 * Variables
***************************************************************************/

/***************************************************************************
 * Function Prototypes
 ***************************************************************************/
void UrrAisInit (Boolean initialisation);

void UrrAisUrlcAmDataInd (RadioResourceControlEntity *urr_p);

void UrrAisUrlcTmDataInd (RadioResourceControlEntity *urr_p);

void UrrAisUrlcUmDataInd (RadioResourceControlEntity *urr_p);

void UrrAisProcessInternalSignal (SignalBuffer *signal_p);

void UrrAisCrlcSequenceNumCnf (RadioResourceControlEntity *urr_p);

void UrrAisRrcDataReq (RadioResourceControlEntity *urr_p);

void UrrAisUrlcUmDataTransmittedInd (RadioResourceControlEntity *urr_p);

void UrrAisUrlcAmDataCnf (RadioResourceControlEntity *urr_p);

void UrrAisUrlcUlSduDiscardInd (RadioResourceControlEntity *urr_p);

void UrrAisUrlcAmDlSduDiscardInd (RadioResourceControlEntity *urr_p);

void UrrAisHandleTimers (RadioResourceControlEntity *urr_p);

void UrrAisCmacRachTxStatusInd (RadioResourceControlEntity *urr_p);

#if defined(UPGRADE_UL_ECF) // ********** begin  				
void UrrAisCmacEdchRachTxStatusInd (RadioResourceControlEntity *urr_p);		
#endif /* UPGRADE_UL_ECF  */ // ********** end

void UrrAisGetInitialUeIdentityAs64bit (UrrIdentityValue64Bit *result_p);

void UrrAisGetImsiAs64bit (UrrIdentityValue64Bit *result_p);

void UrrAisNetworkAuthFailReq (RadioResourceControlEntity *urr_p);


/* RAM log saving related functions */
#if defined (KI_RLG_ENABLE_URRC_PDU_LOG)
void UrrAisSaveAirInterfaceToRamLog (Int8* data, Int16 length,BearerIdentity bearerIdentity,UTransportChannelType trChType,USIB_Type sib_Type);
#endif /* KI_RLG_ENABLE_URRC_PDU_LOG */

    /* Access functions */

    /* PROTOCOL_ERROR variables */
void UrrAisSetProtocolErrorIndicator (UProtocolErrorIndicator newProtocolError);
UProtocolErrorIndicator UrrAisGetProtocolErrorIndicator (void);
void UrrAisSetProtocolErrorReject (Boolean newProtocolErrorReject);
Boolean UrrAisGetProtocolErrorReject (void);
void UrrAisSetProtocolErrorInformation (UProtocolErrorCause cause);
Boolean UrrAisGetProtocolErrorInformation (UProtocolErrorCause *cause_p);
void UrrAisClearProtocolErrorInformation (void);

    /* TRANSACTIONS variable access functions */
void UrrAisClearTransaction (UrrAisTransactionAcceptReject type,
                             UReceivedMessageType messageType,
                             URRC_TransactionIdentifier transactionId,
                             TransactionToFind transactionToFind);

    /* Integrity protection variables */
void UrrAisIntegProtSetInfo (const UrrAisIntegrityProtectionInfo *newInfo_p);
const UrrAisIntegrityProtectionInfo *UrrAisIntegProtGetInfo (void);
void UrrAisIntegProtSetStatus (
                           const UrrAisIntegrityProtectionStarted *newStatus_p);
const UrrAisIntegrityProtectionStarted *UrrAisIntegProtGetStatus (void);

    /* transaction identifier remote access functions */
Boolean UrrAisTransactionPresentForMessageType (
                              UrrAisTransactionAcceptReject type,
                              UReceivedMessageType          messageType,
                              URRC_TransactionIdentifier    transactionId,
                              TransactionToFind             transactionToFind,
                              URRC_TransactionIdentifier    *firstIdFound_p);

Boolean UrrAisClearTransactionPerMessageType (UReceivedMessageType messageType);/* added for CQ00054906 */

Boolean UrrAisCheckIfTransactionRejected (UReceivedMessageType msgType,
                                          URRC_TransactionIdentifier transId);
void UrrAisClearAcceptedTransaction (UReceivedMessageType msgType,
                                     URRC_TransactionIdentifier transId);
void UrrAisClearRejectedTransaction (UReceivedMessageType msgType,
                                     URRC_TransactionIdentifier transId);
void UrrAisClearTransactionDependingOnStatus (
                                    UReceivedMessageType msgType,
                                    URRC_TransactionIdentifier transId,
                                    UrrAisTransactionAcceptReject transStatus);

    /* Air interface PER encode / decode message prototypes */
Boolean UrrAispiDlPcchMessageDecode (const UrlcSdu  *incomingData_p,
                                     PerError       *perError);
Boolean UrrAispiDlCcchMessageDecode (const UrlcSdu *incomingData_p,
                                     PerError       *perError);
Boolean UrrAispiDlDcchMessageDecode (const UrlcSdu *incomingData_p,
                                     PerError       *perError);
Boolean UrrAispiDlBcchFachMessageDecode (const UrlcSdu *incomingData_p,
                                         PerError       *perError);
#if !defined (UPGRADE_EXCLUDE_2G)
Boolean UrrAispiUHandoverToUTRANCommandDecode (
                            const Int8     *encodedData_p,
                            PerError       *perError,
                            UHandoverToUTRANCommand **handoverToUtranCommand_pp);
#endif
void UrrAispiUlCcchMessageEncode (TxSignalQueueStore              *store_p,
                                  UUL_CCCH_Message                *ulCcchMsg_p);
void UrrAispiUlDcchMessageEncode (TxSignalQueueStore              *store_p,
                                  UUL_DCCH_Message                *ulDcchMsg_p);
Int16 UrrAispiCalculateUlDcchMessageBitLen (UUL_DCCH_Message *ulDcchMsg_p);
void UrrAispiEncodeAndUpdateIntegrityProtectionInfo (
                                     UIntegrityCheckInfo *integrityCheckInfo_p,
                                     TxSignalQueueStore  *pendingTxSignal_p);
void UrrAispiAddMissingInitalIntegProtInfo (
                                TxSignalQueueStore          *pendingTxSignal_p,
                                BearerIdentity               radioBearer);

PerBuffer * UrrAisGetPerBufferIn(void);
        

    /* message table prototypes */
Boolean UrrAispiDlCcchMessageSearch (T_UDL_CCCH_MessageType messageType,
                                     Int8 *index_p);
Boolean UrrAispiDlDcchMessageSearch (T_UDL_DCCH_MessageType messageType,
                                     Int8 *index_p);
DownlinkMessageTable *UrrAispiGetPtrToDlCcchMessageTableEntry (
                                                            Int8 messageIndex);
DownlinkMessageTable *UrrAispiGetPtrToDlDcchMessageTableEntry (
                                                            Int8 messageIndex);

void UrrAisSendRrcStatus (UReceivedMessageType rxMsgType,
                          URRC_TransactionIdentifier rxTransId,
                          UProtocolErrorCause cause);

#if defined (NON_NEZHA)
void UrrAisProcessUeCapabilityChanged (RrcMeDataReq *rrcMeDataReq_p);
#else
void UrrAisProcessUeCapabilityChanged (UrrMeDataReq *rrcMeDataReq_p);
#endif

void UrrAisReleaseAirSignal (void);

void UrrAisFreeTxPerBuffer (void);

void UrrAisSetIntegrityProtectionInfoReconfigToTrue (
                                              UReceivedMessageType msgType);

void UrrAisSetIntegrityProtectionInfoReconfigToFalse (void);

UrrAisIntegrityProtectionStarted UrrAisIntegrityProtectionInfoStatus (void);

Boolean UrrAisGetIntegrityProtectionInfoReconfig (
                                           UReceivedMessageType *msgType_p);

UrrAisIntegrityProtectionInfo *UrrAisGetIntegrityProtectionInfo (void);

void UrrAisCalculateCountIStartValue (UCN_DomainIdentity cnDomain,
                                         KeySeqId  ksi,
                                         USTART_Value *startValue_p);

void UrrAisClearIntegrityProtectionInfo (void);

Boolean UrrAisGetUeCapabilityTransferred (
                                UeCapability **ueCapTransferred_pp);

Boolean UrrAisGetUeCapabilityTransferredHspdschSupported (void);
void UrrAisSetConnectedModeIndicator (Boolean connected);

void UrrAisActivate (void);

#if !defined (UPGRADE_EXCLUDE_2G)
void UrrAisSendDebugGsmAirInterfaceRxInd (
                            GrrRcHandoverToUmtsReq *grrRcHandoverToUmtsReq_p);
#endif

void UrrAisSetSendf9Debug (Boolean enabled);



UGSM_Classmark3_str UrrAisGetClassmark3DCS (void);
UGSM_Classmark3_str UrrAisGetClassmark3PCS (void);


Boolean UrrAisIntegrityProtectionRequiredDcch (
                                        T_UUL_DCCH_MessageType messageType,
                                        BearerIdentity bearerIdentity);

Boolean UrrAisIntegrityProtectionRequiredCcch (
                                        T_UUL_CCCH_MessageType messageType);

IntegrityCheckInfoPresent UrrAispiDlDcchMessagePrepareIntegrityCheckInfo (
                                       const UrlcSdu  *incomingData_p,
                                       BearerIdentity bearerIdentity,
                                       UIntegrityCheckInfo *integrityCheckInfo_p);

IntegrityCheckInfoPresent UrrAispiDlCcchMessagePrepareIntegrityCheckInfo (
                                        const UrlcSdu  *incomingData_p,
                                        BearerIdentity bearerIdentity,
                                        UIntegrityCheckInfo *integrityCheckInfo_p);

Boolean UrrAisSetErrorFlags (PerBuffer *perBuffer_p, PerError perError);

#if !defined (UPGRADE_EXCLUDE_2G)
void UrrAisFillRrcNasTxQueueInfoInd (RrcNasTxQueueInfoInd *msg_p);
#endif

void UrrAisSetRb0RrcSnToNewConfig (void);

#if defined (URRC_DEBUG_RBC_ST_SUB_PROCESS)
void UrrAisDumpCountIInfo (void);
#endif

void UrrAisRestoreSavedIntegrityProtectionConfiguration (void);

void UrrAisSetSuspendCondition (SuspendCondition suspendCondition);

void UrrAisClearSuspendCondition (SuspendCondition suspendCondition);

void UrrAisInitialiseUeCapabilityProcedure (void);

void UrrAisCopyUeCapabilityRequestedToUeCapabilityTransferred (void);

void UrrAisUpdateUeCapabilityTransferredFromUeCapabilityRequested (void);

Boolean UrrAisGetRequestedRatSpecificCapability (
            UInterRAT_UE_RadioAccessCapabilityList **ue_RATSpecificCapability_pp);

Boolean UrrAisGetRequestedRadioAccessCapabilityExt (
            UUECapabilityInformation_v370NonCriticalExt **v370NonCriticalExt_pp);

Boolean UrrAisGetRawRadioAccessCapabilityExt (
            UUECapabilityInformation_v370NonCriticalExt **v370NonCriticalExt_pp);

Boolean UrrAisGetRequestedRadioAccessCapability (
                        UUE_RadioAccessCapability **ue_RadioAccessCapability_pp);

Boolean UrrAisGetRawRadioAccessCapability (
                        UUE_RadioAccessCapability **ue_RadioAccessCapability_pp);

void UrrAisSetDefaultCapabilityUpdateRequirement (void);

void UrrAisProcessCapabilityUpdateRequirementIe (
            UCapabilityUpdateRequirement_r5  *capUpdateReq_p
            ,
            Boolean                           laterNonCriticalExtensionsPresent,
            UUECapabilityEnquiry_laterNCritExt   *laterNonCriticalExtensions
            );

void UrrAisProcessCapabilityUpdateRequirementIe_Latest (
            UCapabilityUpdateRequirement_latest  *capUpdateReq_p);


#if defined (NON_NEZHA)
void UrrAisProcessRrcMeDataReq (RrcMeDataReq *rrcMeDataReq_p);
#else
void UrrAisProcessRrcMeDataReq (UrrMeDataReq *rrcMeDataReq_p);
#endif
void AisSetPhyEdchCat_R6(
            UPhysicalChannelCapability_edch_r6  *info_p); //CQ00111602 added

Boolean UrrAisFindRatSpecificCapability (
                    UInterRAT_UE_RadioAccessCapabilityList *ratCapList_p,
                    T_UInterRAT_UE_RadioAccessCapability    rat,
                    UInterRAT_UE_RadioAccessCapability      **ratCap_p);

Boolean UrrAisFindEUTRACapability (UeCapability   *ueCap);
Boolean UrrAisFindEUTRACapabilityOfueCapRequested (void);
void UrrAisFakeSeteEUTRARadioAccessCapability(void);
Boolean UrrAisGetRequestedeUTRASpecificCapability (void);

void UrrAisClearUeCapabilityTransferred (void);

void UrrAisClearUeCapabilityRequested (void);

USTART_Value UrrAisGetInterRatHandoverInfoTxdStartValueByDomain (UCN_DomainIdentity cnDomain); //********** add

//#if !defined (UPGRADE_EXCLUDE_2G) //********** remove
void UrrAisSetInterRatHandoverInfoTransferredStartCs (USTART_Value startCs);

USTART_Value UrrAisGetInterRatHandoverInfoTransferredStartCs (void);

void UrrAisUpdateUeCapTransferredWithInterRatHoInfoTransferred (void);

//#endif //********** remove

void UrrAisSendDebugLteAirInterfaceRxInd (
                            IratHandoverRequest *iratHandoverRequest_p);
void UrrAisSetInterRatHandoverInfoTransferredStartPs (USTART_Value startPs);

USTART_Value UrrAisGetInterRatHandoverInfoTransferredStartPs (void);

void UrrAisHandleRb2UlSnForSmcInterruptByCellUpdate (void);

void UrrAisRestoreSavedHfnAndRrcSnForUlRb2 (void);

void UrrAisRestoreSavedHfnAndRrcSnForDlRb2 (void);

void UrrAisRestoreSavedHfnAndRrcSnForAllUlRbExceptRb2 (void);

Boolean UrrAisCheckForResultsOnRachInCcchMsg (
    TxSignalQueueStore      *store_p,
    Boolean                **measResultsPresent_p,
    UMeasuredResultsOnRACH **measResults_p,
    UUL_CCCH_Message        *ulCcchMsg_p);

void UrrAispiAddMissingInitialIntegProtInfo (
    TxSignalQueueStore          *pendingTxSignal_p,
    BearerIdentity               radioBearer,
    Int16                        rrcPaddedSduLength);

void UrrAisSendAirSignal (
    SendAirSignal   *sendAirSig_p,
    UrrSubProcessId  sourceSubProcess);

void CheckDataSentWatermarks(Boolean reset);

/* efrats - **********: get encoded ASN1 data for Ue Positioning measurement */
Int32 UrrAisGetEncodedDataSize (void);
void UrrAisGetEncodedDataP (Int8* encodedDataP);

UeCapability *UrrAisGetUeCapabilityRequestedPointer (void);
UeCapability *UrrAisGetUeCapabilityTransferredPointer (void);
UUE_RadioAccessCapability_v370ext * UrrAisGetMeasCapaInfo(void);
UUE_RadioAccessCapabBandFDDList2 *UrrAisGetUeRadioAccessCapabBandFDDList2(void);

UeCapability * UrrAisGetUeCapRaw(void);

void UrrAisBuildRadioAccessCapabBandFDD(
    UUECapabilityInformation_v370NonCriticalExt* uECapInfo_v370NonCriticalExt_p,
    FddBandsMask requestedBandModeBits);

void UrrAisGetStates (UrrAisStates *aisStatesArray_p, UrrSubProcessId *aisOriginatingSubProcessesArray_p, Int8 maxArraySize);

Boolean UrrAisIntegrityFailuresLimit (void);

void UrrAisSetWaitingForUrlcAmDataCnf (void);
#if defined UPGRADE_SMC_REL10
void UrrAisSaveSecurityModeCommandMessageId (Int16 messageIdentifier); /* ********** add */
#endif //UPGRADE_SMC_REL10
#if defined (RRC_UPGRADE_ENG_MODE_REDESIGN) //********** begin
Int8 UrrMtcEngModeGetTransactionId (SendAirSignal *sendAirSig_p);
#endif //RRC_UPGRADE_ENG_MODE_REDESIGN :********** end

/*********** add begin*/
void AisClearModeIntergrityFailure (void);
Boolean AisCheckModeIntergrityFailure (UCN_DomainIdentity cnDomain, RrcKeySequence *ksi);/*********** change*/
/*********** add end*/

void UrrAisClearHsDschInFachAndPchCapability (void);/*CQ00051974 add*/
void UrrAisClearEUTRACapabilityOfueCapRequested (void);/*CQ00054129 add*/

/* ********** Begin */
Boolean UrrAisIsUl16QamSupported(void);
Boolean UrrAisIsEdchCat7Supported(void);
Boolean UrrAisIsTwoDrxInPchSupported(void);
Boolean UrrAisIsPowerIntepolationWithout16QamSupported(void);
Boolean UrrAisIsHsdschCat24Supported(void);
Boolean UrrAisIsNoNeedForCmOnAdjCellSupported(void);
Boolean UrrAisIsMaciSupported(void);
Boolean UrrAisIsEcfUlSupported(void);
/* ********** End */
Boolean UrrAisIsHsDschCellFachDrxSupported(void);//********** added

//********** start
Boolean UrrAisIsEsccSupported(void);
//********** end

/* ********** begin */
Boolean UrrAisIsRxTxTimeDiffType2Supported(void);
/* ********** end */
Boolean UrrAisIsReselToLteInFachForHighPriorityLayersSupported(void);// ********** added

#if defined(UPGRADE_UL_ECF) // ********** begin
void UrrAisHandleCmacEdchRachTxStatusInd (RadioResourceControlEntity *urr_p);
#endif /* UPGRADE_UL_ECF  */ // ********** end

//********** start
#if defined (UPGRADE_ESCC)
Boolean UrrAisEsccCheckIfSCCNeedToBeProcess(UServingCellChangeMsgType   servingCellChangeMsgType,
                                                            UServingCellChangeTrId     servingCellChangeTrId);
#endif//UPGRADE_ESCC
//********** end

// ********** begin 
#if defined(UPGRADE_3G_MFBI) || defined(UPGRADE_4G_FROM_3G_MFBI)
Boolean UrrAisIs3GMfbiSupported(void);
Boolean UrrAisIs4GFrom3GMfbiSupported(void);
#endif 
// ********** end 
/* ********** added begin */
Boolean UrrAisIsNoNeedForCMOnInterBandSupported(void);
/* ********** added end */
/* ********** added begin */
Boolean UrrAisIs3GDBDCSupported(void);
UUE_CapabilityContainer_IEs_v920NonCriticalExtensions * AisEnableUeCapInfoV920Ext(
            UUE_CapabilityContainer_IEs_v860NonCriticalExtensions  *v860nce_p,
            UrrSirAllocatedMemList *memList_p);
#if defined (UPGRADE_3G_DBDC)
void UrrAisSaveBandCombinationList(CphyUeCapabilityConfigCnf*  cphyUeCapabilityConfigCnf_p);
Boolean UrrAisBuildBandCombList(UUE_RadioAccessCapabBandCombList*  bandCombList_p);
#endif
/* ********** added end */

void UrrAisDisabledUEA2UIA2 (void); /*********** add *//*********** add end*/

/* ********** added begin */
#if defined (ENABLE_WAIT_ABORT_SEARCH_CNF)
Boolean AisHandleIratAbortSearchCnf(void);
#endif
/* ********** added end */

#endif

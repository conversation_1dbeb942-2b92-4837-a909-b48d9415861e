/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/3g.mod/lib/src/usimcmd.h#3 $
 *   $Revision: #3 $
 *   $DateTime: 2007/02/02 13:54:44 $
 **************************************************************************
 *  File Description :
 *
 *   Contains declarations for functions in usimcmd.c
 **************************************************************************/

#if !defined (USIMCMD_H)
#define USIMCMD_H
#if defined (UPGRADE_3G)
/***************************************************************************
 * Nested Include Files
 **************************************************************************/

#if !defined (ALSI_SIG_H)
#include "alsi_sig.h"
#endif

#if !defined (SIMDATA_H)
#include "simdata.h"
#endif

#if !defined (SIMDEC_H)
#include "simdec.h"
#endif



/***************************************************************************
 * Macros
 **************************************************************************/


/***************************************************************************
 * constants
 **************************************************************************/


/***************************************************************************
 * Type Definitions
 **************************************************************************/

typedef union SimUiccPinNumTag
{
   SimChvNum                chvNum;
   SimUiccKeyRefValue       keyRef;

}SimUiccPinNum;

/***************************************************************************
 *  Function Prototypes
 **************************************************************************/
Int8 SimUiccGetEfNumber(Int16 efId, SimDirId rootDir);

Int8 SimUiccGetAbsoluteAddress ( Int16                   efId,
                                 SimDirId                rootDir,
                                 SimManagerData         *simData,
                                 SimRecordReadWriteMode  mode );
#if defined (UPGRADE_SIM_APP_TOOLKIT)
void UsatExecuteTerminalProfCommand ( SimManagerData        *simData,
                                      SimDataBlock          *dataBlock);

void UsatExecuteFetchCommand (  Int8              expectedSimatCommLength,
                                SimManagerData    *simData,
                                SimDataBlock      *dataBlock );

Boolean UsatExecuteEnvelopeCommand ( SimManagerData        *simData,
                                     SimDataBlock          *dataBlock,
                                     Int8               *simInfoToFetch ); /* Mod by jungle for CQ00055562 on 2014-03-04 */

void UsatExecuteTerminalRespCommand ( SimManagerData        *simData,
                                      SimDataBlock          *dataBlock );
#endif

void SimUiccExecuteSelectDirCommand (Int16                  dirId,
                                     Boolean                returnStatus,
                                     SimUiccLogicalChannel  logicalChannel,
                                     SimManagerData        *simData);
/***********, cgliu, 2012-06-04,begin*/
void SimUiccExecuteSelectAidCommand  ( SimUiccLogicalChannel ch,
                                       SimManagerData          *simData,
                                       /* Add by jungle for CQ00027520 on 2013-01-28 Begin */
                                       Boolean                  returnStatus,
                                       /* Add by jungle for CQ00027520 on 2013-01-28 End */                              
                                       /* Add by jungle for CQ00069162 on 2014-08-27 Begin */
                                       Boolean                  fciFlag,
                                       /* Add by jungle for CQ00069162 on 2014-08-27 End */                                       
                                       Boolean                  lastOccurence );
/***********, cgliu, 2012-06-04,end  */
void SimUiccExecuteSelectEfCommand (Int16                   efId,
                                    Boolean                 returnStatus,
                                    SimUiccLogicalChannel   logicalChannel,
                                    SimManagerData         *simData );

void SimUiccExecuteReadRecordCommand (Int16                   efId,
                                      SimRecordReadWriteMode  mode,
                                      Int8                    recordNumber,
                                      SimManagerData         *simData,
                                      SimDataBlock           *dataBlock,
                                      SimUiccLogicalChannel   logicalChannel);



void SimUiccExecuteReadBinaryCommand (Int16                     efId,
                                      Int16                     numberOfBytes,
                                      Int16                     offset,
                                      SimManagerData           *simData,
                                      SimDataBlock             *dataBlock,
                                      SimUiccLogicalChannel     logicalChannel);




void SimUiccExecuteUpdateBinaryCommand ( Int16                   efId,
                                         Int16                   offset,
                                         SimDataBlock           *dataBlock,
                                         SimManagerData         *simData,
                                         SimUiccLogicalChannel   logicalChannel);

void SimUiccExecuteActivateFileCommand ( Int16                   efId,
                                         SimManagerData         *simData,
                                         SimUiccLogicalChannel   logicalChannel);

void SimUiccExecuteDirStatusCommand ( SimManagerData        *simData,
                                      SimUiccLogicalChannel  logicalChannel);

void SimUiccExecuteUpdateRecordCommand ( Int16                   efId,
                                         SimRecordReadWriteMode  mode,
                                         SimDataBlock           *dataBlock,
                                         Int8                    recordNumber,
                                         SimManagerData         *simData,
                                         SimUiccLogicalChannel   logicalChannel);


void SimUiccExecuteIncreaseCommand ( Int16                             efId,
                                     SimDataBlock                     *dataBlock,
                                     SimManagerData                   *simData,
                                     SimDataBlock                     *newValue,
                                     SimDataBlock                     *actualIncrement,
                                     SimUiccLogicalChannel             logicalChannel);

void SimUiccExecuteActivateFileCommand (Int16                             efId,
                                        SimManagerData                   *simData,
                                        SimUiccLogicalChannel             logicalChannel);

void SimUiccExecuteDeactivateFileCommand (Int16                              efId,
                                          SimManagerData                    *simData,
                                          SimUiccLogicalChannel              logicalChannel);

void SimUiccExecuteSearchRecordCommand ( Int16                    efId,
                                         SimRecordDefinition     *recDef,
                                         SimManagerData          *simData,
                                         Int8                     maxNumRecords,
                                         SimDataBlock            *dataBlock,
                                         SimUiccLogicalChannel    logicalChannel);


void SimUiccExecuteGsmAuthCommand ( Rand                  rand,
                                    SimManagerData       *simData,
                                    CipheringKeyData     *cipherData,     /*Kc*/
                                    Res                  *res);

void SimUiccExecute3GAuthCommand ( Rand                  rand,
                                   Aut                   autn,
                                   SimManagerData       *simData,
                                   Aut                  *auts,          /* if synchronisation fails */
                                   CipherKeyData        *key,           /* includes IK and CK */
                                   CipheringKeyData     *cipherData,     /*Kc*/
                                   Res                  *res);

void SimUiccExecuteRunGsmAlgCommand ( Rand                  rand,
                                      SimManagerData       *simData,
                                      CipheringKeyData     *cipherData,
                                      Res                  *res);

void SimUiccExecuteVerifyPinCommand ( SimUiccPinValue        pinValue,
                                      SimUiccPinNum          pinNum,
                                      Boolean                retryCountOnly,
                                      SimManagerData        *simData,
                                      SimUiccLogicalChannel  logicalChannel);

void SimUiccExecuteChangePinCommand (SimUiccPinValue         oldPinValue,
                                     SimUiccPinValue         newPinValue,
                                     SimManagerData         *simData,
                                     SimUiccPinNum           pinNum, /*Indicates which PIN is to be changed */
                                     SimUiccLogicalChannel   logicalChannel);
void SimUiccExecuteDisablePinCommand ( SimUiccPinValue       pinValue,
                                       SimManagerData       *simData,
                                       SimUiccLogicalChannel logicalChannel,
                                       SimUiccKeyRefValue    replacementKeyReference,  /*Which PIN is going to be used as a replacement*/
                                       SimUiccPinNum         pinNum);      /* Which PIN is to be disabled */
void SimUiccExecuteEnablePinCommand ( SimUiccPinValue        pinValue,
                                      SimManagerData        *simData,
                                      SimUiccLogicalChannel  logicalChannel,
                                      SimUiccPinNum          pinNum );
void SimUiccExecuteUnblockPinCommand (SimUiccPinValue        unblockPinValue,
                                      SimUiccPinValue        newPinValue,
                                      Boolean                retryCountOnly,
                                      SimManagerData        *simData,
                                      SimUiccLogicalChannel  logicalChannel,
                                      SimUiccPinNum          pinNum );

void SimUiccExecuteManageChannelCommand ( SimManagerData  *simData,
                                          Int8            *logicalChannel,
                                          Boolean          open);

/***********, cgliu, 2012-06-04,begin*/
void SimUiccExecuteSimGenAccessCommand ( Int8            *commandData,
                                         Int16            commandLength,
                                         SimDataBlock    *dataBlock,
                                         SimManagerData  *simData,
                                         Int8             logicChNum);
/***********, cgliu, 2012-06-04,end  */

void SimUiccExecuteAidStatusCommand (SimManagerData          *simData,
                                     Boolean                  activated,
                                     SimUiccLogicalChannel    logicalChannel);

void SimUiccSetModeOfSelection (SimManagerData *simData,  SimUiccAppData *uiccData);


void SimUiccSetAbsoluteAddress ( Int16                  efId,
                                 SimDirId               rootDir,
                                 SimManagerData         *simData,
                                 Int8                   absoluteAddress,
                                 Int8                   numRecords,
                                 Int8                   recordLength,
                                 SimEfStructure         efType );

void SimUiccSetRootDir (SimDirId dirId, SimManagerData *simData, SimUiccLogicalChannel logicalChannel );


#endif
#endif

/* END OF FILE */

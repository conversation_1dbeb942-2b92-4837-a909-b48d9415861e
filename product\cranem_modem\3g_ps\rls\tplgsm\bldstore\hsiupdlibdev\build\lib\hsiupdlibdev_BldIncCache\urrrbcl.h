/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/***************************************************************************
 * 3GPP Protocol Stack Copyright (c) TTP Communications PLC 2001
 ***************************************************************************
 *
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urrrbcl.h#6 $
 *   $Revision: #6 $
 *   $DateTime: 2007/03/30 13:53:25 $
 *
 ***************************************************************************
 *
 * File Description:
 *
 *    Header file for RRC's Radio Bearer Control sub-process.
 *    Contains definitions for use by other RBC modules, but not available
 *    to other processes in the RRC.
 *
 ***************************************************************************
 *
 ***************************************************************************/
#if !defined (URRRBCL_H)
#define       URRRBCL_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <urrsirty.h>
#include <urrrbc.h>
#include <cphy_sig.h>
#include <haw_util.h>
#include <release_api.h>
#if !defined (UPGRADE_EXCLUDE_2G)
#include <grrrcsig.h>
#endif

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/
#define     INVALID_CIPHER_RB               0 /*RB 0 is never ciphered*/
#define     INVALID_CN_DOMAIN           ((UCN_DomainIdentity)(maxCNdomains + 1))

#define     MAX_COUNTC_ENTRIES          (maxRB)

    /* any value returned when RB not present in one direction 25.331-8.1.15 */
#define     ANY_START_VALUE             0

#define     MAX_TM_HFN                      ((1 << 24) - 1) /* 24 bits */

#define     NON_SRB_ID                  MAX_SRB_ID + 1

/* Default separation of UL to DL in Frequency Info IE    */
/* Units are 5*F where F is in MHz                        */
/* Default in 190 MHz (25.331, section *********) */
#define  UL_TO_DL_DEFAULT_SEPARATION   ((UUARFCN)TXRX_SEPARATION_BAND_1)

/* Manifest Constants for DPCH offset calculations */

/* Maximum value of DPCH Frame Offset */
#define MAX_DPCH_FRAME_OFFSET 149

/* Minimum value of DPCH Frame Offset */
#define MIN_DPCH_FRAME_OFFSET 0

/* Value with which to modulate the S-RNTI, in order to calculate the DOFF in a
 * handover to UTRAN procedure *******. */
#define DOFF_MAXIMUM_VALUE     600

/* The number of chips per frame. This is used to check or calculate
 * (in the case of handover to UTRAN) the DOFF and DPCH frame offset relationship */
#define CHIPS_PER_FRAME  38400

/* The step size in chips of the DOFF that is signalled in ASN.1 format */
#define DOFF_STEP_SIZE_IN_CHIPS    512

/* The step size in chips of the DPCH frame offset that is signalled in ASN.1 format */
#define DPCH_FRAME_OFFSET_STEP_SIZE_IN_CHIPS    256

/* invalid DL PDU SIZE */
#define INVALID_DL_AM_PDU_SIZE 0xFFFFFFFF

/* transport channel range 1 - 32 */
#define RRC_INVALID_TRANSPORT_CHANNEL 0

#if defined (DEVELOPMENT_VERSION)
#define UrrRbcSetTrans_pSuccess(tRANS_P, sUCCESS) \
       UrrRbcSetTrans_pSuccessSubstituted ((tRANS_P), (sUCCESS), (MODULE_NAME),(__LINE__))
#else
#define UrrRbcSetTrans_pSuccess(tRANS_P, sUCCESS) \
       UrrRbcSetTrans_pSuccessSubstituted ((tRANS_P), (sUCCESS))
#endif


/***************************************************************************
*   Macro Functions
***************************************************************************/

#define PREPARE_ECF_HS_TRANS_INFO(dUMMY,sTATE,tRANS,hSiNFO) \
{ \
    dUMMY.rrcState = sTATE;                 \
    dUMMY.trans_pp = tRANS;                 \
    dUMMY.hsdpaInfo_p = hSiNFO;             \
}

/***************************************************************************
 * Types
 ***************************************************************************/

typedef struct urrRbcPgCbDrxCycleLenParamsTag
{/* DRX cycle lengths all stored as true values, not the k value sent over air */
    Boolean                             csSignallingConnectionIsEstab;
    Boolean                             psSignallingConnectionIsEstab;
    Int16                               psNasNegotiatedDrxCycleLen;
    Int16                               utranDrxCycleLen;
    Int16                               utranDrxCycleLen2;
    Int16                               psDrxCycleLenFromUtranMobInfo;
    Int16                               csDrxCycleLenFromUtranMobInfo;
    /* CS CN domain attached state from MM either AM_NORMAL or AM_EMERGENCY */
    ActivateMode                        csMode;
    /* PS CN domain attached state from MM either AM_NORMAL or AM_EMERGENCY */
    ActivateMode                        psMode;
    Int16                               t319;
}
urrRbcPgCbDrxCycleLenParams;


/* this data is not re-initialised when RRC is deactivated */
typedef struct urrRbcPgCbPersistentDataTag
{
    /* all details for DRX calculations */
    urrRbcPgCbDrxCycleLenParams   drxCycleLenParams;
    Boolean                       cbsEnabledByBmc;
}
UrrRbcPgCbPersistentData;

typedef struct UrrRbcSmCipherConfigurationTag
{
    UCipheringModeCommand_latest    modeCommand;
    USecurityKey                    ck;
    KeySeqId                        ksi;
    Boolean                         isValid;
    Boolean                         ckIsNew;
/*********** add begin*/
    URLC_SequenceNumber     dlRlcSequenceNumber[maxRB];
/*********** add end*/
}
UrrRbcSmCipherConfiguration;

//ICAT EXPORTED ENUM
typedef enum UrrRbcSmCipheringStartedStatusTag
{
    CIPHERING_NOT_STARTED,
    CIPHERING_STARTED,
    CIPHERING_STARTED_INVALID
}
UrrRbcSmCipheringStartedStatus;

typedef enum UrrRbcSmSecurityAffectedTag
{
    SECURITY_AFFECTED,
    SECURITY_NOT_AFFECTED,
    SECURITY_AFFECTED_INVALID
}
UrrRbcSmSecurityAffected;

typedef struct UrrRbcAirSignalInfoTag
{
    Int16                           messageIdentifier;
    void                            *outMsg_p;
    UrrSirAllocatedMemList          outMemList;
    Int8                            numOfRabIds;
    Int8                            rabIdList[RABMRRC_MAX_RABS];
    Int8                            numOfCsRabIds;
    Int8                            csRabIdList[RABMRRC_MAX_RABS];
    Boolean                         rrcSyncInd;
    SyncCause                       cause;
    UCN_DomainIdentity              cn_DomainIdentity;
}
UrrRbcAirSignalInfo;

//ICAT EXPORTED ENUM
typedef enum UrrRbcAwaitedInputTag
{
    RBC_INPUT_AIS_END_STATUS,
    RBC_INPUT_PDCP_CONFIG_CNF,
    RBC_INPUT_PHY_CHAN_EST_SYNC,
    RBC_INPUT_ACTIVATION_TIME,
    RBC_INPUT_UL_CONFIG_CHANGE_IND,
    RBC_INPUT_DL_CONFIG_CHANGE_IND,
    RBC_INPUT_CCTRCH_CONFIG_CNF,
    RBC_INPUT_PHY_TO_IDLE,
    RBC_INPUT_CRLC_SUSPEND_CNF,
    RBC_INPUT_CRLC_PREPARE_CIPHER_CFG_CHANGE_CNF,
    RBC_INPUT_CMAC_HFN_CONFIG_CNF,
    RBC_INPUT_NEW_UE_STATE,
    RBC_INPUT_CHANGE_STATE,
    RBC_INPUT_PHY_DEACTIVATE_CNF,
    RBC_INPUT_CELL_RESYNC_CNF,
    RBC_INPUT_CELL_CHANGE_COMPLETE,
    RBC_INPUT_SIB_7_RECIEVED,
    RBC_INPUT_NEW_SIBS_RECIEVED,
    RBC_INPUT_PHY_CONFIG_FINISH,
    RBC_INPUT_CONTINUE_UNREC_ERROR,
    RBC_INPUT_HO_TO_GSM_CNF,
    RBC_INPUT_RESELECT_TO_GSM_COMPLETE,
    RBC_INPUT_CPHY_SUSPEND_IND,
    RBC_INPUT_UMPH_HO_FAIL_CNF,
    RBC_INPUT_CELL_SELECT_IND,
    RBC_INPUT_SWITCH_FROM_GSM_PLMN_SEARCH,
    RBC_INPUT_RESELECT_GSM_DURING_END_STATUS,
    RBC_INPUT_WAIT_FGPS_END,
    RBC_INPUT_CPHY_TO_LTE_CNF,
    RBC_INPUT_IRAT_HANDOVER_ACK,
    RBC_INPUT_CPHY_TO_LTE_FAIL_CNF,
#if !defined (L1_SUPPORT_GSM_FG_PLMN_SEARCH) && !defined UPGRADE_PLMS /*********** add*/
/* ********** begin */
    RBC_INPUT_SWITCH_FROM_LTE_PLMN_SEARCH,
/* ********** end */
#endif/*********** add*/
#if defined (UPGRADE_DSDS)
    RBC_INPUT_WAIT_RESUME_FINISH, /* CQ00013886  CQ00015480 modify */
    RBC_INPUT_ANOTHER_CARD_POWER_OFF_COMPLETE, /* CQ00025299*/
#endif
    RBC_INPUT_WAIT_OTHER_CARD_RESUME_PCH_FINISH_IND, //********** added     
/*CQ00070208 add begin*/
#if defined (UPGRADE_PLMS)
	RBC_INPUT_ABORT_PLMN_SEARCH_CNF,
#endif /*(UPGRADE_PLMS)*/
/*CQ00070208 add end*/
	RBC_INPUT_DS_ABORT_SEARCH_CNF,
#if defined (ENABLE_WAIT_ABORT_SEARCH_CNF)
/* ********** added begin */	
	RBC_INPUT_DS_PHY_CONFIG_FINISH,
/* ********** added end */	
#endif /* ENABLE_WAIT_ABORT_SEARCH_CNF */

    RBC_INPUT_NONE
}
UrrRbcAwaitedInput;

typedef enum UrrRbcReconfigMsgTypeTag
{
    RBC_RECONFIG_MSG_RB_SETUP,
    RBC_RECONFIG_MSG_RB_RELEASE,
    RBC_RECONFIG_MSG_RB_RECONFIG,
    RBC_RECONFIG_MSG_PHY_CH_RECONFIG,
    RBC_RECONFIG_MSG_TR_CH_RECONFIG,
    RBC_NUMBER_OF_RECONFIG_MSGS
}
UrrRbcReconfigMsgType;
/*********** add begin*/
#if defined (RRC_DC_HSDPA)
typedef enum UrrRbcReconfigurationMsgTypeTag
{
    RBC_RADIO_BEARER_RECONFIG,
    RBC_RRC_CONNECTION_SETUP,
    RBC_ACTIVE_SET_UPDATE,
    RBC_RADIO_BEARER_SETUP,
    RBC_CELL_UPDATE_CONFIRM_CCCH,
    RBC_CELL_UPDATE_CONFIRM_DCCH,
    RBC_RADIO_BEARER_RELEASE,
    RBC_TRANSPORT_CHANNEL_RECONFIG,
    RBC_PHYSICAL_CHANNEL_RECONFIG
}
UrrRbcReconfigurationMsgType;
#endif //RRC_DC_HSDPA
/*********** add end*/
/* Note: if any structures added to this union require */
/* memory to be released besides releasing it during   */
/* normal processing it also must be added to          */
/* ClearAwaitedInputs in urrrbcmg.c                    */
typedef union UrrRbcAwaitedInputInfoTag
{
    /* Extra info for RBC_INPUT_AIS_END_STATUS */
    UrrRbcAirSignalInfo aisEndStatusInfo;
    /* Extra info for RBC_INPUT_CONFIG_CHANGE_IND */
    UActivationTime     cfn;
    /* Extra info for RBC_INPUT_CCTRCH_CONFIG_CNF */
    Int8                configIdentifier;
}
UrrRbcAwaitedInputInfo;

typedef struct RbcPhyChanEstSyncInfoTag
{
    Boolean                         successful;
}
RbcPhyChanEstSyncInfo;

typedef struct ReselectToGsmResultTag
{
    Boolean                         success;
    UrrSmcModeAndState              previousModeAndState; /* only valid when reselectToGsmSuccess is FALSE */
}
ReselectToGsmResult;

#if !defined (UPGRADE_EXCLUDE_2G)
#if !defined (L1_SUPPORT_GSM_FG_PLMN_SEARCH) && !defined UPGRADE_PLMS /*********** add UPGRADE_PLMS*/
typedef struct SwitchFromGsmPlmnSearchTag
{
    UrrSmcModeAndState              previousModeAndState;
}
SwitchFromGsmPlmnSearch;
#endif
#endif

typedef struct MsgSendStatusTag
{
    UrrAisSendStatus            sendStatus;
    Int16                       msgId;
}
MsgSendStatus;

typedef union UrrRbcReceivedDataTag
{
    /* Pointer to awaited input */
    MsgSendStatus                   msgSendStatus;
    CpdcpRelocCnf                   *pdcpRelocCnf_p;
    RbcPhyChanEstSyncInfo           phyChanEstSyncInfo;
    CmacActivationTimeCnf           *activationTimeCnf_p;
    Int8                            configIdentifier;
    UrrInternalSignalCampedOnCell   *campedOnCell_p;
    CrlcSuspendCnf                  *crlcSuspendCnf_p;
    CrlcPrepareCipherCfgChangeCnf   *crlcPrepareCipherCfgChangeCnf_p;
    CmacHfnConfigCnf                *cmacHfnConfigCnf_p;
    CphyCcTrChConfigCnf             *cphyCcTrChConfigCnf_p;
#if !defined (UPGRADE_EXCLUDE_2G)
    GrrRcErrorCause                 grrRcErrorCause;
    ReselectToGsmResult             reselectToGsmResult;
#if !defined (L1_SUPPORT_GSM_FG_PLMN_SEARCH) && !defined UPGRADE_PLMS /*********** add UPGRADE_PLMS*/
    SwitchFromGsmPlmnSearch switchFromGsmPlmnSearch;
#endif

#endif
    IratHandoverAck                 iratHandoverAck;
    CphyHandoverToLteCnf            cphyHandoverToLteCnf;
    CphyHandoverToUmtsFailCnf       cphyHandoverToUmtsFailCnf;
    Boolean                         success;
    UmacTmCountcInfo                hfnCurrent;
}
UrrRbcReceivedData;

typedef struct UrrRbcReceivedInputTag
{
    UrrRbcAwaitedInput      type;
    UrrRbcReceivedData      *data_p;
}
UrrRbcReceivedInput;

typedef struct TmStartInfoTag
{
    Boolean                             isPresent;
    CmacStartValues                     tmStartValues;
}
TmStartInfo;

typedef struct UrrRbcConnectedModeRachTag
{
    UTransmissionTimeInterval   ttiSelected;
    Int32                       tfSize;
    UrrRbcPrachFachSetupResult  result;
}
UrrRbcConnectedModeRach;

//ICAT EXPORTED STRUCT
typedef struct UrrRbcTransactionTag
{
    /* Air signal of message */
    UrrInternalSignalProcessRxAirSignal *sig_p;

    /* Only messages received by other sub processes first */
    UHandoverToUTRANCommand    *handoverToUtranCommand_p;

    /* rrc_TransactionIdentifier for CELL/URA UPDATE CONFIRM*/
    URRC_TransactionIdentifier  updateRrcTransId;

    /* type of the received message */
    UReceivedMessageType        msgType;

    /* version of received message */
    UrrRxMsgVersion             rxMsgVersion;

    /* type of the message that is sent in response */
    T_UUL_DCCH_MessageType      responseMsgType;

    /* mode and state when message was received */
    UrrSmcModeAndState          modeState;

    /* new state received in msg */
    URRC_StateIndicator         newState;

    /* indicates that MAC,RLC, and RRC configuration is synchronised at an
     * activation time determined by MAC */
    Boolean                     syncOnActivationTime;
    /* new CRNTI details */
    Boolean                     newCrntiPresent;
    UC_RNTI                     newCrnti;
    /* the CRNTI when the transaction message arrived */
    Boolean                     previousCrntiValid;
    UC_RNTI                     previousCrnti;
    /* new URNTI details */
    Boolean                     newUrntiPresent;
    UU_RNTI                     newUrnti;
    /* the URNTI when the transaction message arrived */
    UU_RNTI                     previousUrnti;
    /* stores air signal end status info (to wait for rlc ack) */
    UrrRbcAwaitedInputInfo      endStatus;
    /* error information */
    Boolean                     success;
    Boolean                     sendFailure;
    T_UFailureCauseWithProtErr  failureCause;
#if !defined (UPGRADE_EXCLUDE_2G)
    T_UInterRAT_HO_FailureCause interRatHoFailureCause;
    /* Note: certain causes will have additional information in errorCause */
#endif
    UProtocolErrorCause         errorCause;
    Boolean                     abortRrcConnection;

    /* success/failure send methods. Must only be accessed by SetHandlerFunc */
    void (*handlerFunc_p)       (UrrRbcAwaitedInputInfo *, UrrRbcReceivedInput, struct UrrRbcTransactionTag *);

    /* outstanding signals left before sending response */
    Int8                        numSignalsLeft;

    /* ciphering or intergity protection reconfiguration has been aborted,
     * but signals related to the transaction may still be expected */
    Boolean                     securityReconfigurationAborted;

    /* set to TRUE when the SECURITY MODE COMPLETE is submitted to AIS */
    Boolean                     securityModeCompleteHasBeenSent;

    /* a "cipher mode info" IE is present in this transaction     */
    Boolean                     cipherModeIeIsPresent;

    /* a "integrity protection mode info" IE is present in this transaction */
    Boolean                     integrityProtectionModeIeIsPresent;

    /* a "Downlink counter synchronisation info" IE is present*/
    Boolean                     dlCounterSyncPresent;

    /* indicates SRNS relocation is in progress 25.331 - ******* */
    Boolean                     srnsRelocation;

    /* transparent mode RB START values - indexed by domain id */
    TmStartInfo                 tmStartValues [maxCNdomains];

    /* START values that has been calculated during this transaction not
     * including START VALUE TO TRANSMIT - indexed by domain id */
    USTART_Value                calculatedStartValues [maxCNdomains];

    Boolean                     incompatSimulatneousRecfgInCellUpdate;

/* Note: variables below here should only be changed under ORDERED_RECONFIGURATION */

    /* frequency change--> to determine hard handover */
    Boolean                     hardHandoverRequired;

    /* CmacHandoverReq required */
    Boolean                     csRabsWillExistAfterTrans;

    /* details of how frequency is being changed */
    UrrRbcCommonRlInfoFrequency frequencyIsChanged;
    Boolean                     frequencyInfoPresent;
    Boolean                     timingMaintainedSyncInd;
    UUARFCN                     uarfcn_dl;
    UUARFCN                     uarfcn_ul;

    /* details of primaryCPICH_Info */
    Boolean                     primaryScramblingCodePresent;
    UPrimaryScramblingCode      primaryScramblingCode;

    ActivationTime              activationTime;

#if defined (UPGRADE_3G_RELEASE_6)
    /* RP-050320 / 2566 */
    Boolean                     delayRestrictionFlag;
#endif // UPGRADE_3G_RELEASE_6

    /* if prior to this procedure there exists no transparent mode*/
    /* radio bearer for the CN domain included in the IE "CN      */
    /* domain identity" in the IE "RAB info" in the variable      */
    /* ESTABLISHED_RABS and at least one transparent mode radio   */
    /* bearer is included in the IE "RB information to setup":    */
    Boolean                     tmRbIsFirst [maxCNdomains];

    /* if at least one RLC-AM or RLC-UM radio bearer is included  */
    /* in the IE "RB information to setup"                        */
    Boolean                     amOrUmRbToSetup;

    /* TM CountC activation time provided by UE                   */
    Boolean                     tmCountcActTimePresent;
    UActivationTime             tmCountcActTime;

  /********** - add*/  
    /* Number of frames between now and the Count-C Activation time,
         * as received from UMAC task */
        Int8                        frameGapReceivedFromUmac;
/********** - end*/
    /* 25.331 ******** ciphered TM radio bearers are having a timing resync */
    Boolean                     tmCipheredRbCfnResync;

    /* the type of MAC HFN configuration in progress */
    CmacTmCountcStartActTimeUsage  tmCountcStartActTimeUsage;

    /* HFN information at the point of applying an activation time must be
     * kept in case of a config failure */
    UmacTmCountcInfo            ulHfnWhenNewCfgApplied;
    UmacTmCountcInfo            dlHfnWhenNewCfgApplied;


    /* for comparison with DOFF */
    Boolean                     dpchFrameOffsetOfFirstRlPresent;
    UDL_InformationPerRL_latest     *dlInformationPerRLFirstRl_p;

    /* failed to establish DCH channels 25.331 - ******* */
    Boolean                     phyChanEstabHasFailed;
    /* the radio link action of the configuration that failed */
    URadioLinkAction            failedConfigRadioLinkAction;
    /* while restoring the previous configuration after a reconfiguration
     * failed the previous reconfiguration has failed */
    Boolean                     failedToApplyOldConfiguration;

#if !defined (UPGRADE_EXCLUDE_2G)
    /* following elements only apply in HANDOVER TO UTRAN COMMAND */
    Boolean                     cipheringIsOn;

    /* following elements only apply in HANDOVER FROM UTRAN COMMAND */
    ActivationTime              toGsmActivationTime;

    /* error cause saved from any of the GrrRc messages */
    GrrRcErrorCause             grrRcErrorCause;

    /* indicates that Layer 1 has suspended during inter RAT transfer */
    Boolean                     layerOneIsSuspended;

    NasInfoForRrcUponHandoverToUmts  nasInfoForRrcUponHoToUmts;

    /* indicates whether the grr sent ue capability to the air before handover to umts */
    Boolean                     grrSentCm3;

    /* RAB Identity and hand-over type */
    URAB_Identity               rabIdentity;
    CmacHandoverType            handoverType;
#endif /* UPGRADE_EXCLUDE_2G */
    IratHandoverResult                  iratHandoverResult;
    /* following elements only apply in HANDOVER FROM UTRAN COMMAND */
    ActivationTime                      toLteActivationTime;
    CmacHandoverType                    handoverTypeLte;
    URAB_Identity                       rabIdentityLte;
    T_UInterRAT_HO_FailureCause         interRatHoFailureCauseLte;
    	//Flag indicates whether nasSecurityFromEutra is valid
	Boolean 			  nasSecurityFromEutraPresent;
    //Used to deliver the key synchronisation and key freshness for the E-UTRAN to UTRAN handovers as specified in TS 33.401
	//the content of the parameter is defined in TS24.301
	Int8				  nasSecurityFromEutra;
    /* to store the cotents of IE "RB stop/continue" until actioned */
    Int8                        numberOfStopBearers;
    BearerIdentity              stopBearerIdentity [maxRB];
    Int8                        numberOfContinueBearers;
    BearerIdentity              continueBearerIdentity [maxRB];

    /* ptr to the element in the Transaction list that this transaction
     * belongs to. This is void so that it can be included in the
     * type def. N.B. must be cast when used*/
    void                       *transEle_p;

    /* Indicator if we need to reestablish bearers */
    Boolean                     reestablishBearers;

    /* indicates the bearer identity of an RLC unrecoverable error */
    BearerIdentity              bearerIdentity;

    /* indicates that the lower layer configuration was restricited to
     * SRBs only at the beginning of this transaction.
     * If data RBs mappings exist they are not configured */
    Boolean                     onlySrbsConfigured;

    /* indicates that an exisitng transport channel,
     * has had a change to the MAC header size.
     * It is assumed this applies to UL and DL.
     * If = RRC_INVALID_TRANSPORT_CHANNEL, no change applies */
    UTransportChannelIdentity   trChWithMacHeaderSizeChanged[2];

    /* stores the amount of change to MAC header size in bits */
    SignedInt8                  macHeaderSizeChangeInBits[2];

    /* Did the current transaction change the value of the UE variables HS_DSCH_RECEPTION and E_DCH_TRANSMISSION ? */
    Boolean                     hsDschReceptionChanged;
    Boolean                     eDchTransmissionChanged;
/*********** add begin*/
#if defined (RRC_DC_HSDPA)
    Boolean                     secondaryCellhsDschReceptionChanged;
	    /* Indicates whether SCC of the DC occurred */
	Boolean                     dcServingCellChangeOccurred;
#endif //RRC_DC_HSDPA
/*********** add end*/
#if defined (UPGRADE_3G_HSDPA)
    Boolean                     macHsReset;    /* Indicates MAC-HS reset request */
    Boolean                     hrntiPresent;    /* Did the message include the H_RNTI ? */
    Boolean                     hsDschServingCellChange;  /* Indicates HS-DSCH Serving Cell change */
    Boolean                     hsDschServingCellPresent;  /* Indicates existence of HS-DSCH Serving Cell */
    UPrimaryScramblingCode      hsDschServingCell;
#endif /* UPGRADE_3G_HSDPA */

#if defined (UPGRADE_3G_RELEASE_6)
    Boolean                     macHsConfigChanged;  /* Indicates received MAC-HS configuration different from backup */
    Boolean                     hsPhyConfigChanged;  /* Indicates received HS Phy configuration different from backup */
    Boolean                     macModified;
    Boolean                     edchModified;
    Boolean                     macEReset;    /* Indicates MAC-E reset request */
    Boolean                     primaryErntiPresent;
    Boolean                     secondaryErntiPresent;
    Boolean                     eDchServingCellChange;  /* Indicates E-DCH Serving Cell change */
    Boolean                     eDchServingCellPresent;  /* Indicates existence of E-DCH Serving Cell */
    UPrimaryScramblingCode      eDchServingCell;
    Boolean                     edchASU; /* Indicates Serving Cell Change, Adding Cell, Removing Cell in E-DCH Active Set */
#if defined (UPGRADE_3G_FDPCH)
    URL_AdditionInformationList_latestElement  *fdpchFirstElement;
#endif /* UPGRADE_3G_FDPCH */
#endif /* UPGRADE_3G_RELEASE_6 */
    Boolean                     secondaryCPICHconfigured;

    //CPC related
    Boolean                     dtxDrxTimingInfoPresent;
    Boolean                     dtxDrxStatusChanged;
    Boolean                     hsScchLessStatusChanged;
    Boolean                     isHsScchLessInfoIeIncluded;
    UE_DCH_TTI                  edchTti;/**<2ms or 10ms  */

    // Check if RLC PDU Size has been changed
    Boolean rlcPduSizeHasChanged;
    Boolean                     delayedRlcRb2Configuration;
    /* for CQ00020620 BEGIN PTK_CQ00227845 */
    /* A flag indicating whether this initial FACH RACH is requested for
       sending RRC CONNECTION REQUEST */
    Boolean                     initFachRachForRrcConnReq;
    /* for CQ00020620 END PTK_CQ00227845 */

#if defined(UPGRADE_UL_ECF) // ********** begin	
    // used for indication to HAW to skip sync procedure A in case of ECF UL -> CELL_DCH with EDCH reconfiguration
    Boolean                     skipSyncProcedureA;
#endif /* UPGRADE_UL_ECF  */ // ********** end
/* CQ00088662 add begin */
	Boolean						stopPlmsAfterPhyConfigFinish;
/* CQ00088662 add end */	
}
UrrRbcTransaction;

#if defined (UPGRADE_3G_HSDPA)
typedef struct UrrRbcHsdpaInfoTransactionStateTag
{
    URRC_StateIndicator rrcState;
    UrrRbcHsdpaInfo *hsdpaInfo_p;
    UrrRbcTransaction *trans_pp;
}
UrrRbcHsdpaInfoTransactionState;
#endif /* UPGRADE_3G_HSDPA */

typedef struct UrrRbcAwaitedInputsElementTag
{
    UrrRbcAwaitedInput              expectedInput;
    /* function called with input is received */
    void                          (*processingProc)(UrrRbcAwaitedInputInfo *,
                                                    UrrRbcReceivedInput,
                                                    UrrRbcTransaction *);
    UrrRbcAwaitedInputInfo          inputSpecificInfo;
    Boolean                         decrementNumSignalsLeft;
    UrrRbcTransaction               *trans_p;
    Boolean                         transDeleted; /**< TRUE when the transaction was deleted. */
}
UrrRbcAwaitedInputsElement;

/* Awaited inputs list */
UT_DOUBLE_LINK_LIST_DECLARE (UrrRbcAwaitedInputsList, UrrRbcAwaitedInputsElement);

typedef struct UrrRbcLatestConfiguredCnDomainTag
{
    Boolean                     present;
    UCN_DomainIdentity          cnDomainIdentity;
    USTART_Value                startValue;
}
UrrRbcLatestConfiguredCnDomainTag;

typedef struct UrrRbcAirSignalMessageElementTag
{
    Int16   messageIdentifier;
    void   *message_p;
}
UrrRbcAirSignalMessageElement;

/* Air Signal list */
UT_DOUBLE_LINK_LIST_DECLARE (UrrRbcAirSignalMessageList, UrrRbcAirSignalMessageElement);

typedef struct UrrRbcConfigSignalElementTag
{
    TaskId              destTask;
    SignalBuffer        configSignal;
    Int8               *noOfSignalSetMessages_p;
    Int8               *signalSetMessageNo_p;
}
UrrRbcConfigSignalElement;

/* Config signal list */
UT_DOUBLE_LINK_LIST_DECLARE (UrrRbcConfigSignalList, UrrRbcConfigSignalElement);

typedef struct UrrRbcConfigElementTag
{
    UrrRbcConfigSignalList  configSignalList;
    Int8                    configId;
    Boolean                 pendingCnf;
    Int8                    noOfSignalSetMessages;
    Boolean                 syncRequired;
    UrrRbcTransaction      *trans_p;
    SignalGroupId           group;
}
UrrRbcConfigElement;

/* Config lists */
UT_DOUBLE_LINK_LIST_DECLARE (UrrRbcConfigLists, UrrRbcConfigElement);

typedef struct UrrRbcConfigQueueTag
{
    /* denotes a configuration is being built */
    Boolean                     configStarted;
    /* configuration waiting to be sent */
    UrrRbcConfigLists           configLists;
}
UrrRbcConfigQueue;

typedef enum UrrRbcL123ConfigStatusTag
{
    URRRBC_L123_HAS_BEEN_CFG,
    URRRBC_L123_HAS_NOT_BEEN_CFG
}
UrrRbcL123ConfigStatus;

/***************************************************************************
 * Typed Constants
 ***************************************************************************/

/***************************************************************************
 * Function Prototypes
 ***************************************************************************/

/* From file urrrbc.c */

void UrrRbcProcessTBSizeTable(
    UrrRbcTransaction *trans_p,
    UHS_DSCH_TBSizeTable TBSizeTable,
    Boolean TBSizeTablePresent);

#if defined(UPGRADE_3G_HSDPA)
Boolean UrrRbcVerifyHsdpaRbMapping (Boolean dchState);

Int32 UrrRbcGetTotalSoftChannelBits (void);
#endif //UPGRADE_3G_HSDPA 
/*********** add begin*/
#if defined (RRC_DC_HSDPA)
void UrrRbcProcessSecondaryCellTBSizeTable(void);

Int32 UrrRbcGetTotalSoftChannelBitsForDualCell (void);

void UrrRbcProcessSecondaryCellInfoFdd (UDL_SecondaryCellInfoFDD_r9 *info_p,
                                                UrrRbcTransaction *trans_p,
                                                Boolean differentTxDiversityMode);

Boolean UrrRbcGetDifferentTxDiversityModeFromNCExt (UrrRbcReconfigurationMsgType reconigMsgType, void* ext_p);

Boolean UrrRbcCheckValidConfigurationOfSecondaryCellFreq (UUARFCN primaryCellUarfcn, UUARFCN secondaryCellUarfcn);

#endif // RRC_DC_HSDPA
/*********** add end*/
void UrrRbcSendHsdpaConfigToPhy (Int8 configId,
                                          ActivationTime *actTime_p,
                                          URRC_StateIndicator rrcState,
                                          Boolean macHsReset,
                                          Boolean configEcf,
                                          UrrRbcTransaction *trans_p);

#if defined (UPGRADE_3G_HSDPA)
void UrrRbcPrepareInitialEcfInfo(UrrRbcHsdpaInfo *hsdpaInfo_p, UrrRbcTransaction *trans_p);
#endif //UPGRADE_3G_HSDPA 


void UrrRbcActivateData (void);

Int8 UrrRbcFindUlTrch(
    UTransportChannelType     trChType,
    UTransportChannelIdentity trChId,
    UT_DOUBLE_LINK_LIST_NODE_TYPE(UrrRbcULTrChList) **ulTrch_p);

void UrrRbcConfigureDlRbMac (URRC_StateIndicator rrcState, UrrRbcTransaction *trans_p, Boolean configEcf);

Int8 UrrRbcConfigureCellDch (UrrRbcTransaction *trans_p);

#if defined(UPGRADE_UL_ECF) // ********** begin	
Int8 UrrRbcConfigureCellFach (UrrRbcTransaction *trans_p, Boolean configEcfDl, Boolean configEcfUl);
#else /* UPGRADE_UL_ECF */
Int8 UrrRbcConfigureCellFach (UrrRbcTransaction *trans_p, Boolean configEcf);
#endif /* UPGRADE_UL_ECF  */ // ********** end  

void UrrRbcConfigureCellUraPch (UrrRbcTransaction      *trans_p);

void UrrRbcConfigureCellDchActiveSetUpdate (UrrRbcTransaction *trans_p);

void UrrRbcConfigureRlcForRb0 (void);

void UrrRbcConfigureRlcForBcch (void);

void UrrRbcConfigureRlcForPcch (void);

void UrrRbcConfigureRbRlc (UrrRbcTransaction *trans_p);

void UrrRbcClearPdcpSnInfo (void);

TaskId UrrRbcDetermineRbOwnerTask(
    BearerIdentity     rbId,
    UCN_DomainIdentity cnDomain,
    Boolean            isPdcpEntityPresent);

Boolean UrrRbcPdcpSnInfoEmpty (void);

USTART_Value UrrRbcGetCalculatedStartForDomain(
    UCN_DomainIdentity  cnDomain,
    UrrRbcTransaction *trans_p);

Boolean UrrRbcGetLoopbackIsActive (void);

URadioLinkAction UrrRbcGetRadioLinkAction (void);

UrrRbcRadioBearerDatabase *UrrRbcGetBackupRbcDatabase (void);

void UrrRbcGetPdcpSnInfo(
    UrrSirAllocatedMemList  *destMemList_p,
    Boolean                 *destPresentFlag_p,
    URB_WithPDCP_InfoList  **destList_p);
/*********** add begin*/
#if !defined (RRC_DC_HSDPA)
/*********** add end*/
void UrrRbcProcessActiveSetUpdate(
    UActiveSetUpdate_latest_IEs  *activeSetUpdate_p,
    UrrRbcTransaction *trans_p);
/*********** add begin*/	
#else
void UrrRbcProcessActiveSetUpdate (
    UActiveSetUpdate_latest_IEs  *activeSetUpdate_p,
    UrrRbcTransaction *trans_p,
    UActiveSetUpdate_latest *asu_p );

#endif
/*********** add end*/
void UrrRbcProcessPhysChanReconfig (UrrRbcTransaction      *trans_p);

void UrrRbcProcessRadioBearerReconfig (UrrRbcTransaction      *trans_p);

void UrrRbcProcessRadioBearerRelease (UrrRbcTransaction      *trans_p);

void UrrRbcProcessRadioBearerSetup (UrrRbcTransaction      *trans_p);

Boolean UrrRbcProcessRrcConnectionSetup(
    URRCConnectionSetup_latest *rrcConnSetup_p,
    UrrRbcTransaction          *trans_p);

void UrrRbcProcessRrcConnectionRelease (void);

void UrrRbcProcessTransChanReconfig (UrrRbcTransaction      *trans_p);

void UrrRbcCopyR5toR7DrxCycleLenCoeff (UUTRAN_DRX_CycleLengthCoefficient r5DrxCycleCoeff, UUTRAN_DRX_CycleLengthCoefficient_latest *r7_p);


void UrrRbcProcessCcchUraUpdateConfig(
    UURAUpdateConfirm_CCCH_latest_IEs *msgIEs_p,
    UrrRbcTransaction *trans_p);

void UrrRbcProcessDcchUraUpdateConfig(
    UURAUpdateConfirm_DCCH_latest_IEs *msgIEs_p,
    UrrRbcTransaction *trans_p);

Boolean UrrRbcProcessUtranMobilityInfo(
    UUTRANMobilityInformation_latest *utranMobilityInfo_p,
    UrrRbcTransaction      *trans_p);

Boolean UrrRbcProcessSib16(
    USysInfoType16              *sibIEs_p,
    Boolean                     rbToSetupIePresent,
    UrrRbcTransaction           *trans_p);

void UrrRbcProcessHsDschCellInformation(
    UServing_HSDSCH_CellInformation_latest *info_p,
    UTX_DiversityMode txDiversityMode,
    UrrRbcTransaction *trans_p);

void UrrRbcRestartRlcEntities (void);

void UrrRbcStopRlcEntities (void);

void UrrRbcSendRrcSyncInd (SyncCause  cause, UCN_DomainIdentity cnDomainId, Boolean securityFinished,
    Int8 numOfCsRabIds, Int8 csRabIdList[RABMRRC_MAX_RABS]);

void UrrRbcSendCmacActTimeReqTrans (ActivationTime *activationTime_p,
                                    Boolean delayRestrictionFlag,
                                    UTransmissionTimeInterval maxTti,
                                    UrrRbcTransaction *trans_p);

void UrrRbcSendLeavingCellDch (void);

void UrrRbcSetCalculatedStartForDomain(
    USTART_Value startValue,
    UCN_DomainIdentity  cnDomain,
    UrrRbcTransaction *trans_p);

UrrRbcCommonPhysChSysInfo *UrrRbcGetIdleCommonPhysChSysInfoPtr (void);

UrrRbcCommonPhysChSysInfo *UrrRbcGetConnCommonPhysChSysInfoPtr (void);

void UrrRbcSetRadioLinkAction (URadioLinkAction radioLinkAction);

void UrrRbcSaveDlRbRlcSizes(
    UT_SINGLE_LINK_LIST_NODE_TYPE (UrrRbcRbList) *rbList_p,
    URRC_StateIndicator rrcState,
    UrrRbcTransaction   *trans_p);

void UrrRbcSaveUlRbRlcSizes(
    UT_SINGLE_LINK_LIST_NODE_TYPE (UrrRbcRbList) *rbList_p,
    URRC_StateIndicator rrcState,
    UrrRbcTransaction   *trans_p);

UTransmissionTimeInterval UrrRbcFlattenDynamicTfData(
    Int8      macHeaderSize,
    DynamicTfData *flat_p,
    UTransportFormatSet *sib_p);

void UrrRbcSendCphyUlTfcConfigReqs(
    Int8  configId,
    Int8  ccTrchIndex,
    ActivationTime *actTime_p,
    UrrRbcUlTfcsTable *ulDchTfcsTable_p);

void UrrRbcFlattenUlPhyTfcsList (UrrRbcRbUlPhyTfcsList *flatTfcsList_p,
                               UTFCS *tfcs_p);

void UrrRbcFillCmacUlTrchConfigReq(
    UTransportChannelIdentity chId,
    UTransportChannelType     chType,
    Int8                      macHeaderSize,
    UTransportFormatSet      *tfs_p,
    CmacUlTrChConfigReq      *msg_p);

void UrrRbcFlattenLogChanLists(
    UTransmissionTimeInterval   tti,
    UTransportFormatSet        *ttiTfs_p,
    MacLogicalChannelList       macTfs [],
    UrrRbcTransaction           *trans_p);

void UrrRbcClearRbDbForIdleMode (void);

void UrrRbcLeavingCellDch (Boolean goingToIdle, Boolean reconfiguration, UrrRbcTransaction *trans_p);

void UrrRbcSetTgpsIdentity (UrrRbcTgpsIdentity *tpgsId_p);

void UrrRbcValidateTgmpMode (UTGMP_r8 tgmp, T_UUL_DL_Mode mode);

void UrrRbcConvertUrntiTo64Bit(
    UU_RNTI *uRnti_p,
    UrrIdentityValue64Bit *uRnti64Bit_p);

void UrrRbcDeleteRbRlc (void);

void UrrRbcInformRabmReleaseInd(
    Int8 numOfPsRabIds,
    Int8 psRabIdList[RABMRRC_MAX_RABS]);

void UrrRbcPdcpRabmDeletions (Boolean fromRlf); /********** - modify*/

void ScanForTmRbsPerDomain (Boolean *tmRbPresentList_p);

void UrrRbcSetTmRbPresent(
    UCN_DomainIdentity cnDomain,
    Boolean tmRbIsPresent);

Boolean UrrRbcIsTmRbPresent (UCN_DomainIdentity cnDomain);

#if !defined (UPGRADE_EXCLUDE_2G)
void UrrRbcProcessHoToUtranPreconfigFddSpecificInfo(
    UHandoverToUTRANCommand_latest_IEs_fdd *msgIEs_p,
    UrrRbcTransaction *trans_p);

void UrrRbcProcessRabInfoPost (URAB_Info_Post *rab_Info_p);

void UrrRbcSendCmacHandoverReqToGsm(
    ActivationTime  *activationTime_p,
    CmacHandoverType handoverType,
    URAB_Identity    *rab_Identity_p);
#endif

void UrrRbcResetDataBaseIsValidBit (void);

void UrrRbcCreateBackupRbDataBase (void);

void UrrRbcRestoreBackupRbDataBase (UrrRbcTransaction *trans_p);

void UrrRbcDestroyBackupRbDataBase (void);

void UrrRbcConsiderRlcPdcpDiffsInBackupRbDatabase (UrrRbcTransaction *trans_p);

void UrrRbcRevertTransactionVariables (UrrRbcTransaction *trans_p);

Int8 UrrRbcGetTfisFromCtfc(
    UrrRbcULTrChList *ulTrChList_p,
    Int8 tfis[],
    Int32 ctfc,
    Boolean *validCtfc);

void UrrRbcGetMacGainFactor(
    MacGainFactor **macGainFactor_pp,
    TfcsAdd **tfcsAdd_pp,
    MacTFCS *cmacTfcs_p);

#if !defined (UPGRADE_EXCLUDE_2G)
void UrrRbcMgSendGrrRcHandoverToUmtsCnf (GrrRcErrorCause     cause);
void UrrRbcMgSendGrrRcHandoverToGsmReq(GrrRcHandoverToGsmReq *grrRcHandoverToGsmReq_p);
void UrrRbcMgSendRrcNasTxQueueInfoInd (void);
void UrrRbcInternalSignalCellSelectInd (Boolean success);
#endif

void UrrRbcSendRadioLinkFailure (UrrSubProcessId destinationSubProcess);

void UrrRbcSendCphyRlCommonSetupReq(
    Int8                  configId,
    CphyRlCommonSetupReq  *msg_p,
    ActivationTime        *activationTime_p,
#if defined(UPGRADE_UL_ECF) // ********** begin		
    Boolean                isForEcfUl,
#endif /* UPGRADE_UL_ECF  */ // ********** end  	
    UrrRbcTransaction     *trans_p);

void UrrRbcProcessDoffVsDpchOffsetValue(
    UrrRbcTransaction * trans_p);

Boolean UrrRbcCheckDoffVsDpchOffsetValue (UrrRbcTransaction *trans_p);

#if !defined (UPGRADE_EXCLUDE_2G)
UDefaultDPCH_OffsetValueFDD UrrRbcCalculateDoffForHoToUtranCommand (void);

UDPCH_FrameOffset UrrRbcCalculateDpchFrameOffsetForHoToUtranCommand (void);

void UrrRbcSetupCurrentCipherConfig (
    UCN_DomainIdentity cnDomain,
    UCipheringModeCommand_latest   *modeCommand_p);
#endif /* UPGRADE_EXCLUDE_2G */

void UrrRbcSetCcchSduSize (Int32 ccchSduSize);

#if defined(UPGRADE_3G_HSDPA)
UrrRbcEhsData* UrrRbcGetEhsDb (void);
#endif //UPGRADE_3G_HSDPA 
#if defined(UPGRADE_UL_ECF) // ********** begin	
UrrRbcCommonEdchData* UrrRbcGetCommonEdchDb (void);
#endif /* UPGRADE_UL_ECF  */ // ********** end  

/* List of Radio Bearers whose RLC configuration type is 'same as', plus the */
/* RB Id which they are the same as                                          */
/* The DL mapping info is used in case a DL RB mapping is defined 'same as   */
/* UL' for a RB which is mapped to a 'same as RB' RB                         */
typedef struct UrrRbcRbRlcSameAsTag
{
    URB_Identity                       rbId;
    URB_Identity                       rbIdToMatch;
    Boolean                           *dlMappingPresentFlag_p [maxRBMuxOptions];
    UDL_LogicalChannelMappingList_latest  *dlMapList_p [maxRBMuxOptions];
}
UrrRbcRbRlcSameAs;

UT_SINGLE_LINK_LIST_DECLARE (UrrRbcRbRlcSameAsList, UrrRbcRbRlcSameAs);

UT_SINGLE_LINK_LIST_NODE_TYPE (UrrRbcRbList) *ProcessRbMappingInfo(
    URB_Identity       rbId,
    URB_MappingInfo_latest *mappingInfo_p,
    URLC_Info_latest       *rlcInfo_p,
    UT_SINGLE_LINK_LIST_NODE_TYPE (UrrRbcRbRlcSameAsList) *sameAs_p);

void InitValidateMuxOptions (void);

void UrrRbcProcessRbMappingsToTrChLists (UrrRbcRadioBearerDatabase   *rbDp_p);

void UrrRbcProcessHrnti(
    UH_RNTI hrnti,
    UrrRbcTransaction *trans_p);

void UrrRbcProcessDlHspdschInfo (
    UDL_HSPDSCH_Information_latest *dlHspdschInfo_p,
    UrrRbcTransaction *trans_p);
/*********** add begin*/
#if defined (RRC_DC_HSDPA)
void UrrRbcSetHarqInfo (
    UHARQ_Info_latest *harqInfo_p,
    UrrRbcTransaction *trans_p);
void UrrRbcProcessHarqInfo(UrrRbcTransaction *trans_p);
#else
/*********** add end*/
void UrrRbcProcessHarqInfo (
    UHARQ_Info_latest *harqInfo_p,
    UrrRbcTransaction *trans_p);
/*********** add begin*/
#endif // RRC_DC_HSDPA
/*********** add end*/
#if defined(UPGRADE_3G_HSDPA)
void UrrRbcProcessAddOrReconfMACdFlow (UAddOrReconfMAC_dFlow *flow_p);
void UrrRbcProcessAddOrReconfMacEhsQueue (UAddOrReconfMAC_ehs_ReordQ_latest *addOrReconfig_p);
void UrrRbcProcessMacHsReset (
    Boolean reset,
    UrrRbcTransaction *trans_p);

void UrrRbcProcessHsDschServingRadioLinkInd (
    Boolean                servingIndicator,
    UPrimaryScramblingCode rl,
    UrrRbcTransaction     *trans_p,
    UTX_DiversityMode      txDiversityMode,
    Boolean                cltdPresent);
#endif //UPGRADE_3G_HSDPA 

UT_DOUBLE_LINK_LIST_NODE_TYPE (UrrRbcConfigLists)*UrrRbcGetTailOfConfigLists (void);

void UrrRbcProcessUlDpchPowerControlInfoForHsdpa (UUL_DPCH_PowerControlInfo_latest_fdd *ulDpchInfo_p);

void UrrRbcProcessPowerControlInfoForHsdpa(UServing_HSDSCH_CellInformation_latest *info_p);

Boolean UrrRbcNewSysInfoRead (void);
void UrrRbcSetNewSysInfoRead (Boolean value); 

void UrrRbcFreeRadioLinkAdditionsAndRemovals (void);

#if defined(UPGRADE_UL_ECF) // ********** begin	
Boolean UrrRbcAreRntiParamsSet(Boolean checkHrnti, Boolean checkCrnti, Boolean checkErnti);
void UrrRbcProcessHspaValues (UrrRbcTransaction *trans_p, Boolean revertDchHsupaInfoFromFachDb);
#else /* UPGRADE_UL_ECF */
Boolean UrrRbcAreRntiParamsSet(Boolean checkHrnti, Boolean checkCrnti);
#if defined(UPGRADE_3G_HSDPA)
void UrrRbcProcessHspaValues (UrrRbcTransaction *trans_p);
#endif //UPGRADE_3G_HSDPA 
#endif /* UPGRADE_UL_ECF  */ // ********** end  

void UrrRbcResolveHhoFailureFlagSet (UrrRbcTransaction *trans_p);

void UrrRbcSelectRbMappings (URRC_StateIndicator rrcState);

Boolean AnyDlRadioLinks (void);

void UrrRbcReportUnspecifiedBehaviour (UrrRbcTransaction *trans_p);

void RbcValidate64QamVsMacQueueType (UrrRbcTransaction *trans_p);

UT_SINGLE_LINK_LIST_NODE_TYPE (UrrRbcRlRemovalList) *UrrRbcCreateNewRlRemoval (void);

UT_SINGLE_LINK_LIST_NODE_TYPE (UrrRbcRadioLinkList) *UrrRbcCreateNewRlAddition (void);

#if defined (DEVELOPMENT_VERSION)
void UrrRbcSetTrans_pSuccessSubstituted (UrrRbcTransaction *trans_p,
                                                Boolean success,
                                                const char *const file_p,
                                                const Int32 line);

#else
void UrrRbcSetTrans_pSuccessSubstituted (UrrRbcTransaction *trans_p, Boolean success);
#endif

void UrrRbcMgSendPhysChanReconfigFailure (
    Boolean              transactionIdPresent,
    URRC_TransactionIdentifier  transactionId,
    T_UFailureCauseWithProtErr  failureCause,
    UProtocolErrorCause         errorCause,
    UTGPSI                      tgpsi,
    UrrRbcAwaitedInputInfo      *info_p);

void UrrRbcMgInitCurrentTransaction (
    UrrRbcTransaction           **trans_pp,
    UrrInternalSignalProcessRxAirSignal *sig_p);

UT_DOUBLE_LINK_LIST_NODE_TYPE (UrrRbcAwaitedInputsList) *UrrRbcMgAwaitInput (
    UrrRbcAwaitedInput      input,
    UrrRbcAwaitedInputInfo  *info_p,
    void (*proc_p)          (UrrRbcAwaitedInputInfo *,
                            UrrRbcReceivedInput,
                            UrrRbcTransaction *));

UT_DOUBLE_LINK_LIST_NODE_TYPE (UrrRbcAwaitedInputsList) *UrrRbcMgAwaitTransactionInput (
    UrrRbcAwaitedInput input,
    UrrRbcAwaitedInputInfo  *info_p,
    UrrRbcTransaction       *trans_p);

UT_DOUBLE_LINK_LIST_NODE_TYPE (UrrRbcAwaitedInputsList) *UrrRbcMgAwaitOptionalTransactionInput (
    UrrRbcAwaitedInput input,
    UrrRbcAwaitedInputInfo  *info_p,
    UrrRbcTransaction       *trans_p);

UT_DOUBLE_LINK_LIST_NODE_TYPE (UrrRbcAwaitedInputsList) *UrrRbcMgAwaitInputNoWait (
    UrrRbcAwaitedInput      input,
    UrrRbcAwaitedInputInfo  *info_p);

UT_DOUBLE_LINK_LIST_NODE_TYPE (UrrRbcAwaitedInputsList) *UrrRbcMgAwaitTransactionCompleteMessage (
    UrrRbcAwaitedInputInfo  *info_p,
    UrrRbcTransaction       *trans_p);

void UrrRbcMgRemoveAwaitedTransactionInput (
    UrrRbcAwaitedInput      input,
    UrrRbcTransaction       *trans_p);

void UrrRbcMgRemoveInput (UrrRbcAwaitedInput      input, UrrRbcTransaction  *trans_p);/*CQ00029414: add trans_p*/

void UrrRbcMgRemoveInputFromList (UrrRbcAwaitedInput input);/*********** add*/
void ClearExistAisAndStatus (void); /*********** add*/


Boolean UrrRbcMgInputIsPending (UrrRbcAwaitedInput input, UrrRbcTransaction  **trans_pp);

void UrrRbcDeactivate (void);

UrrConnectedState UrrRbcRrcStateTransform(URRC_StateIndicator stateInd);

void UrrRbcMgSendRabmRrcReleaseInd (void);

Boolean UrrRbcMgProcessInput (
    UrrRbcAwaitedInput input,
    UrrRbcReceivedData *data_p);

void UrrRbcMgInit (Boolean firstInitialisation);

void UrrRbcMgHandleTfcControl (UrrInternalSignalProcessRxAirSignal *sig_p);

void UrrRbcMgHandleTfcControlInitWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input);

void UrrRbcProcessTfcControl (UTransportFormatCombinationControl *tfccMsg_p);

void UrrRbcProcessCpdcpRelocCnf (UrrRbcReceivedData *data_p);

Boolean UrrRbcProcessCmacActivationTimeCnf (UrrRbcReceivedData *data_p);

void UrrRbcSendRrcConnSetupProcessed (RrcConnSetupSuccess errorCode);

/* processing an ORDERED RECONFIGURATION message */
void UrrRbcMgHandleReconfigMessage (
    UrrInternalSignalProcessRxAirSignal *sig_p,
    URRC_StateIndicator rrcState,
    Boolean integrityProtectionModeInfoPresent,
    UrrRbcReconfigMsgType reconfigMsgType,
    Boolean returnUnsupportedReconfig); /* ********** */

void UrrRbcMgHandleReconfigMessageIeProcessingWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction *trans_p);

void UrrRbcMgHandleReconfigMessageCellSelection (UrrRbcTransaction *trans_p);

void UrrRbcMgHandleReconfigMessageCellSelectionWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction      *trans_p);

void UrrRbcMgHandleReconfigMessageStateProcessing (UrrRbcTransaction *trans_p);

void UrrRbcMgHandleReconfigMessageStateProcessingWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction *trans_p);

void UrrRbcMgHandleReconfigMessageSelectCellComplete (Boolean successful);

void UrrRbcMgReconfigMessageComplete (UrrRbcTransaction *trans_p);

void UrrRbcMgReconfigMessageCompleteWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction *trans_p);

void UrrRbcMgSendReconfigMessageComplete (UrrRbcTransaction *trans_p);

Boolean UrrRbcMgGetCsRabsWillExistAfterTrans (UrrRbcTransaction           *trans_p);

void UrrRbcMgCsRabsWillExistAfterTrans (
    UrrRbcEstablishedRabsList *rabList_p,
    UrrRbcTransaction         *trans_p);

Boolean UrrRbcMgHandleRadioLinkFailureDuringOnGoingProcedure (void);

void UrrRbcHandleRlfDuringReconfigMessageOldDch (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction      *trans_p);

/* Security Mode Command */
void UrrRbcMgHandleSecurityModeCommand (UrrInternalSignalProcessRxAirSignal *sig_p);

void UrrRbcMgHandleSecurityModeCommandIeProcessingWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction    *trans_p);

void UrrRbcMgHandleSecurityModeCommandPrepareRb2 (UrrRbcTransaction    *trans_p);

void UrrRbcMgHandleSecurityModeCommandPrepareRb2Wait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction    *trans_p);

void UrrRbcMgHandleSecurityModeCommandComplete (UrrRbcTransaction    *trans_p);

void UrrRbcMgCompleteSecurityModeCommandComplete (
    UrrRbcAwaitedInputInfo *info_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction    *trans_p);

/* Utran Mobility Information */
void UrrRbcMgHandleUtranMobilityInfo (UrrInternalSignalProcessRxAirSignal *sig_p);

void UrrRbcMgHandleUtranMobilityInfoIeProcessingWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction    *trans_p);

void UrrRbcMgUtranMobilityInfoComplete (UrrRbcTransaction    *trans_p);

void UrrRbcMgUtranMobilityInfoCompleteWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction      *trans_p);

void UrrRbcMgSendUtranMobilityInfoComplete (UrrRbcTransaction *trans_p);

/* Active Set Update */
void UrrRbcMgHandleActiveSetUpdate (UrrInternalSignalProcessRxAirSignal *sig_p);
void UrrRbcMgHandleActiveSetUpdateIeProcessingWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction    *trans_p);
void UrrRbcMgActiveSetUpdateComplete (UrrRbcTransaction    *trans_p);
void UrrRbcMgHandleActiveSetUpdateIgnore (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input);

/* RRC Connection Setup DCH */
void UrrRbcMgHandleRrcConnSetupCellDch (UrrInternalSignalRrcConnectionSetup *intSig_p);
void UrrRbcMgHandleRrcConnSetupConfigChangeCellDch (UrrRbcTransaction *trans_p);
void UrrRbcMgHandleRrcConnSetupConfigChangeWaitCellDch (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput input,
    UrrRbcTransaction *trans_p);

void UrrRbcMgRrcConnSetupConfigCompleteCellDch (UrrRbcTransaction *trans_p);
void UrrRbcMgHandleRrcConnSetupConfigCompleteWaitCellDch (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction *trans_p);

/* RRC Connection Setup FACH */
void UrrRbcMgHandleRrcConnSetupCellFach (UrrInternalSignalRrcConnectionSetup *intSig_p);

void UrrRbcRrcConnSetupCellFachStateProcessingWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction      *trans_p);

void UrrRbcRrcConnSetupCellFachStateProcessing (UrrRbcTransaction *trans_p);

void UrrRbcMgHandleRrcConnSetupSelectCellWaitCellFach (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput input,
    UrrRbcTransaction *trans_p);
void UrrRbcMgHandleRrcConnSetupSibReceivedWaitCellFach (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput input,
    UrrRbcTransaction *trans_p);
void UrrRbcMgHandleRrcConnSetupCellFachConfig(URRCConnectionSetup_latest *rrcConnSetup_p);
void UrrRbcMgHandleRrcConnSetupActivationTimeWaitCellFach (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput input,
    UrrRbcTransaction *trans_p);
void UrrRbcMgHandleRrcConnSetupConfigChangeWaitCellFach (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput input,
    UrrRbcTransaction *trans_p);

/* Handle initial fach and rach selection invoked by AIS*/
void UrrRbcMgProcessSelectInitialFachRach (UrrInternalSignalSelectInitFachRach *sig_p);

void UrrRbMgConfigureInitialFachRach (UrrRbcTransaction  *trans_p);

void UrrRbMgConfigureInitialFachRachWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput     input,
    UrrRbcTransaction      *trans_p);

void UrrRbcMgGetSib7SelectInitialFachRachWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction      *trans_p);

void UrrRbcMgInitialFachRachConfigured (Boolean selected, UrrSubProcessId subProcess);

/* handle RrcConnection Release CELL_DCH */
void UrrRbcMgProcessRrcConnectionRelease (void);

void UrrRbcMgProcessRrcConnectionReleaseWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput input,
    UrrRbcTransaction *trans_p);

void UrrRbcMgProcessRrcConnectionReleaseContinued (UrrRbcTransaction *trans_p);

void UrrRbcMgProcessRrcConnectionReleaseCompleted (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput input,
    UrrRbcTransaction *trans_p);

/* handle RrcConnection Release CELL_FACH */
void UrrRbcMgProcessRrcConnectionReleaseCellFach (void);

/* handle CellUpdateConfirm */
void UrrRbcMgProcessCellUpdateConfirm (
    UCellUpdateConfirm_latest_IEs  *msgIEs_p,
    UrrInternalSignalProcessRxAirSignal *sig_p);
void UrrRbcMgProcessCellUpdateConfig (
    UrrRbcTransaction *trans_p,
    UCellUpdateConfirm_latest_IEs  *msgIEs_p,
    UrrInternalSignalProcessRxAirSignal *sig_p);

void UrrRbcProcessCellUpdateConfig (
    UCellUpdateConfirm_latest_IEs *msgIEs_p,
    UrrRbcTransaction *trans_p,
    UrrInternalSignalProcessRxAirSignal *sig_p);

void UrrRbcMgProcessCellUpdateConfigWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput input,
    UrrRbcTransaction *trans_p);

void UrrRbcMgProcessCellUpdateConfirmStateProcessing (UrrRbcTransaction *trans_p);

void UrrRbcMgProcessCellUpdateConfirmStateWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction *trans_p);

void UrrRbcMgCompleteCellUpdateConfirm (UrrRbcTransaction *trans_p);

void UrrRbcMgCompleteCellUpdateConfirmStateWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction *trans_p);

void UrrRbcMgSendCellUpdateConfirmComplete (UrrRbcTransaction *trans_p);


/* handle UraUpdateConfirm */
void UrrRbcMgProcessUraUpdateConfirm(
    void  *msgIEs_p,
    UraUpdateConfirmType uraUpdateConfirmType,
    UrrInternalSignalProcessRxAirSignal *sig_p);

void UrrRbcMgProcessUraUpdateConfigWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput input,
    UrrRbcTransaction *trans_p);

void UrrRbcMgProcessUraUpdateConfirmStateProcessing (UrrRbcTransaction *trans_p);

void UrrRbcMgProcessUraUpdateConfirmStateWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput input,
    UrrRbcTransaction *trans_p);

void UrrRbcMgCompleteUraUpdateConfirm (UrrRbcTransaction *trans_p);

void UrrRbcMgCompleteUraUpdateConfirmWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput input,
    UrrRbcTransaction *trans_p);

void UrrRbcMgSendUraUpdateConfirmComplete (UrrRbcTransaction *trans_p);

void CompleteUraUpdateConfirmComplete (
    UrrRbcAwaitedInputInfo *info_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction       *trans_p);

void UrrRbcProcessUraUpdateConfigBeforeCellUraPch (
    UURAUpdateConfirm_DCCH_latest_IEs *msgIEs_p,
    UrrRbcTransaction *trans_p);

void UrrRbcProcessUraUpdateConfigCellUraPch(UURAUpdateConfirm_DCCH_latest_IEs *msgIEs_p);

/* handle RESET UE TO IDLE MODE internal signal CELL DCH */
void UrrRbcMgProcessResetUeToIdleMode (Boolean causeIsRrcConnRelease);

void UrrRbcMgProcessResetUeToIdleModeWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput input,
    UrrRbcTransaction *trans_p);

void UrrRbcMgProcessResetUeToIdleModeGetActTime (UrrRbcTransaction *trans_p);

void UrrRbcMgProcessResetUeToIdleModeGetActTimeWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction      *trans_p);


void UrrRbcMgProcessResetUeToIdleModeContinued (UrrRbcTransaction *trans_p);

void UrrRbcMgProcessResetUeToIdleModeContinuedWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction      *trans_p);

void UrrRbcMgProcessResetUeToIdleModeCompleted (UrrRbcTransaction *trans_p);

/* handle RESET UE TO IDLE MODE internal signal CELL FACH */
void UrrRbcMgProcessResetUeToIdleModeCellFach (void);

/* handle RESET UE TO IDLE MODE internal signal CELL/URA_PCH */
void UrrRbcMgProcessResetUeToIdleModeCellUraPch (void);

/* handle UrrInternalSignalChangeState */
void UrrRbcMgHandleChangeState (UrrInternalSignalChangeState *sig_p);

void UrrRbcMgHandleChangeStateWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction      *trans_p);
Boolean UrrRbcMgHandleIratAbortSearchCnf(void);
#if defined (ENABLE_WAIT_ABORT_SEARCH_CNF)
void UrrRbcMgHandleChangeStateWaitDsAbortSearchCnf(
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction      *trans_p);
void UrrRbcMgProcessCellChangeOrderFromUtranWaitAbortSearchCnf (
                                            UrrRbcAwaitedInputInfo *input_p,
                                            UrrRbcReceivedInput    input,
                                            UrrRbcTransaction      *trans_p);
void UrrRbcMgProcessCellChangeOrderFromUtranAbortSearchCnfSuspended (UrrRbcTransaction *trans_p);
/* ********** added begin */	
void UrrRbcMgHandleChangeStateWaitDsPhyConfigFinishInd (UrrRbcAwaitedInputInfo *input_p,
                                                UrrRbcReceivedInput input,
                                                UrrRbcTransaction *trans_p);
/* ********** added end */	
#endif /* ENABLE_WAIT_ABORT_SEARCH_CNF */
//********** added begin
void UrrRbcMgHandleChangeStateWaitResumePchFinishInd (UrrRbcAwaitedInputInfo *input_p,
                                                UrrRbcReceivedInput input,
                                                UrrRbcTransaction *trans_p);
void UrrRbcMgHandleChangeStateWaitResumePchFinishIndHandler (void);
//********** added end

void UrrRbcMgSelectAndConfigureCellFach (void);

UReceivedMessageType UrrRbcMgGetMessageType (void);

UTransmissionTimeInterval UrrRbcDetermineMaxTti (void);

void UrrRbcSetDoff (UDefaultDPCH_OffsetValueFDD newDoff);

void UrrRbcSendCpdcpConfigReq (CpdcpConfigReq  *pdcpConfig_p);

Boolean UrrRbcReleaseRborSrb (URB_Identity rbId);

void UrrRbcReleaseRb (URB_Identity rbId);

void UrrRbcMgDeleteTransactionSignal (UrrRbcTransaction *trans_p);

void UrrRbcMgHandleCampedOnCell (SignalBuffer *signal_p);

void UrrRbcMgHandleCampedOnCellWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction      *trans_p);

void UrrRbcMgHandleAllScellSibsDistributed (void);

void UrrRbcMgClearCurrentTransaction (UrrRbcTransaction *trans_p);

void UrrRbcMgStoreAirSignalMessage (Int16   messageIdentifier,
                                    void   *message_p);

void UrrRbcMgSetUpdatePdcpSnInfoList (Boolean present);

void UrrRbcMgIncrementUpdatePdcpSnInfoCount (void);

void UrrRbcMgDecrementUpdatePdcpSnInfoCount (void);

void UrrRbcMgClearUpdatePdcpSnInfoCount (void);

void UrrRbcMgSelectCell (
    Boolean                freqPresent,
    UUARFCN                frequency,
    Boolean                primaryScramblingCodePresent,
    UPrimaryScramblingCode primaryScramblingCode,
    ActivationTime         *activationTime_p,
    Boolean                reconfiguration,
    UrrRbcCellUpdateState  state,
    Boolean                radioLinkFailure);

void UrrRbcMgHandleRlcUnrecoverableError (BearerIdentity      bearerIdentity);

void UrrRbcMgHandleRlcUnrecoverableErrorWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction      *trans_p);

void UrrRbcNewConfig (SignalGroupId group);

void UrrRbcEnqueueConfigSignal (
    TaskId destTask,
    SignalBuffer *signal_p,
    Int8 *noOfSignalSetMessages_p,
    Int8 *signalSetMessageNo_p);

void UrrRbcSendConfig (Int8 configId);

void UrrRbcConfigCnf (Int8 configId);

void UrrRbcConfigureForPdcpSnInfo (UrrRbcTransaction *trans_p);

void UrrRbcSendExplicitConfiguredStopContinue (UrrRbcTransaction *trans_p);

UrrRbcULTrChListElement *UrrRbcGetUlTrCh (URB_Identity rbId);


Int16 UrrRbcMgAllocateMessageIdentifier (void);

void UrrRbcMgCompleteCounterCheckProcedureComplete (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction      *trans_p);

CmacStartValues *UrrRbcMgGetTmStartValues (
    UCN_DomainIdentity cnDomain,
    UrrRbcTransaction  *trans_p);

void UrrRbcMgSetTmStartDataForRadioBearerSetup (
    UCN_DomainIdentity cnDomain,
    UrrRbcTransaction      *trans_p);

Boolean UrrRbcMgGetAmOrUmRbToSetup (UrrRbcTransaction  *trans_p);

void UrrRbcMgSetAmOrUmRbToSetup (
    Boolean amOrUmRbToSetup,
    UrrRbcTransaction  *trans_p);

void UrrRbcMgSetFirstTmRb (
    UCN_DomainIdentity cnDomain,
    Boolean  tmRbIsFirst,
    UrrRbcTransaction  *trans_p);

Boolean UrrRbcMgIsFirstTmRb (
    UCN_DomainIdentity cnDomain,
    UrrRbcTransaction *trans_p);

Boolean UrrRbcMgGetTmCipheredRbCfnResync (UrrRbcTransaction *trans_p);

void UrrRbcMgSetupTmStartValuesForCfnResync (
    UCN_DomainIdentity cnDomain,
    UrrRbcTransaction *trans_p,
    Boolean initialHfnValuePresent,
    UMAC_d_HFN_initial_value initialHfnValue);

#if !defined (UPGRADE_EXCLUDE_2G)
/* handle HandoverToUtranCommand */
void UrrRbcMgHandleHandoverToUtranCommand (
    UHandoverToUTRANCommand *handoverToUtranCommand_p,
    GrrRcHandoverToUmtsReq  *grrRcHandoverToUmtsReq_p,
    UrrRxMsgVersion          rxMsgVersion);

void UrrRbcMgHandleHandoverToUtranCommandWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction      *trans_p);

void UrrRbcMgHandoverToUtranCommandWaitForCellDch (UrrRbcTransaction      *trans_p);

void UrrRbcMgHandoverToUtranCommandWaitForCellDchWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction      *trans_p);

void UrrRbcMgHandoverToUtranCommandProcessing (UrrRbcTransaction      *trans_p);

void UrrRbcMgHandoverToUtranCommandIeProcessingWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction      *trans_p);

void UrrRbcMgHandoverToUtranCommandCheckSync (UrrRbcTransaction      *trans_p, Boolean l1Affected);/* ********** added l1Affected */

void UrrRbcMgHandoverToUtranCommandCheckSyncWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction      *trans_p);

void UrrRbcMgHandoverToUtranCommandSendComplete (UrrRbcTransaction      *trans_p, Boolean l1Affected);/* ********** added l1Affected */
void UrrRbcMgHandoverToUtranCommandCompleteDeactivation (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction      *trans_p);

/* handle HandoverFromUtranCommand */
void UrrRbcMgHandleHandoverFromUtranCommand (UrrInternalSignalProcessRxAirSignal *sig_p);

void UrrRbcMgHandoverFromUtranCommandSuspendRrc (UrrRbcTransaction *trans_p);

void UrrRbcMgHandoverFromUtranCommandSuspendRrcWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction      *trans_p);

void UrrRbcMgHandoverFromUtranCommandIeProcessing (UrrRbcTransaction *trans_p);

void UrrRbcMgHandoverFromUtranCommandIeProcessingWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction      *trans_p);

void UrrRbcMgHandoverFromUtranCommandDeactivatePhy (UrrRbcTransaction *trans_p);

void UrrRbcMgHandoverFromUtranCommandDeactivatePhyWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction      *trans_p);

void UrrRbcMgHandoverFromUtranCommandDeactivate (UrrRbcTransaction *trans_p);

void UrrRbcMgHandoverFromUtranCommandDeactivateWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction      *trans_p);

void UrrRbcMgHandoverFromUtranCommandComplete (UrrRbcTransaction *trans_p);

void UrrRbcMgHandoverFromUtranCommandGetSync (UrrRbcTransaction *trans_p);

void UrrRbcMgHandoverFromUtranCommandFailure (UrrRbcTransaction *trans_p);

/* URR_INTERNAL_SIGNAL_RESELECT_TO_GSM_OOS */
void UrrRbcMgHandleReselectToGsmOos (void);

#if !defined (L1_SUPPORT_GSM_FG_PLMN_SEARCH) && !defined UPGRADE_PLMS /*********** add UPGRADE_PLMS*/
void UrrRbcMgHandleSwitchToGsmPlmnSearch (void);

void UrrRbcMgHandleSwitchFromGsmPlmnSearch (void);

void UrrRbcMgHandleSwitchToGsmPlmnSearchWait(
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction      *trans_p);

void UrrRbcMgSwitchFromGsmPlmnSearchWait(
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction      *trans_p);
#endif

void UrrRbcMgHandleReselectToGsmOosWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction      *trans_p);

/* URR_INTERNAL_SIGNAL_RESELECT_TO_GSM_REQ */
void UrrRbcMgHandleReselectToGsmReq (UrrInternalSignalReselectToGsmReq  *sig_p);

void UrrRbcMgProcessCellChangeOrderFromUtranWait(
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction      *trans_p);
void UrrRbcMgProcessCellChangeOrderFromUtranSuspended (UrrRbcTransaction *trans_p);
void UrrRbcMgProcessCellChangeOrderFromUtranSuspendedWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction      *trans_p);
void UrrRbcMgProcessCellChangeOrderFromUtranConfirmed (UrrRbcTransaction *trans_p);
void UrrRbcMgProcessCellChangeOrderFromUtranConfirmedWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction      *trans_p);
void UrrRbcMgProcessCellChangeOrderFromUtranFailureComplete (UrrRbcTransaction *trans_p);

/* URR_INTERNAL_SIGNAL_RESELECT_TO_GSM */
void UrrRbcMgHandleReselectToGsm (void);

void UrrRbcMgReselectToGsmComplete (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction      *trans_p);

void UrrRbcMgReselectToGsmDeactivate (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction      *trans_p);

/* URR_INTERNAL_SIGNAL_RESELECT_TO_UMTS */
void UrrRbcMgHandleReselectToUmts (void);

/* URR_INTERNAL_SIGNAL_RESELECT_TO_UMTS_FAILURE */
void UrrRbcMgHandleReselectToUmtsFailure (void);
#endif

#if !defined UPGRADE_PLMS /*********** add*/
/* ********** begin */
void UrrRbcMgSwitchFromLTEPlmnSearchWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction      *trans_p);


void UrrRbcMgHandleSwitchToLTEPlmnSearchWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction      *trans_p);

void UrrRbcMgHandleSwitchToLTEPlmnSearch (void);
/* ********** end */
#endif/*********** add*/

void UrrRbcMgHandleHandoverToUtranCommandEutra (
                             UHandoverToUTRANCommand *handoverToUtranCommand_p,
                             IratHandoverRequest  *iratHandoverRequest_p,
                             UrrRxMsgVersion          rxMsgVersion);
void UrrRbcMgHandleHandoverToUtranCommandEutraWait (UrrRbcAwaitedInputInfo *input_p,
                                               UrrRbcReceivedInput    input,
                                               UrrRbcTransaction      *trans_p);

void UrrRbcMgHandoverToUtranCommandWaitForCellDchEutra (
                                               UrrRbcTransaction      *trans_p);

void UrrRbcMgHandoverToUtranCommandWaitForCellDchEutraWait (
                                               UrrRbcAwaitedInputInfo *input_p,
                                               UrrRbcReceivedInput    input,
                                               UrrRbcTransaction      *trans_p);

void UrrRbcMgHandoverToUtranCommandIeProcessingEutra (
                                               UrrRbcTransaction      *trans_p);

void UrrRbcMgHandoverToUtranCommandIeProcessingEutraWait (
                                               UrrRbcAwaitedInputInfo *input_p,
                                               UrrRbcReceivedInput    input,
                                               UrrRbcTransaction      *trans_p);
void UrrRbcMgHandoverToUtranCommandCheckSyncEutra (UrrRbcTransaction      *trans_p, Boolean 
l1Affected);/*CQ00058643 add l1Affected*/

void UrrRbcMgHandoverToUtranCommandCheckSyncEutraWait (
                                               UrrRbcAwaitedInputInfo *input_p,
                                               UrrRbcReceivedInput    input,
                                               UrrRbcTransaction      *trans_p);

void UrrRbcMgHandoverToUtranCommandSendCompleteEutra (
                                               UrrRbcTransaction      *trans_p);

#if 0/*********** remove*/    
void UrrRbcMgHandoverToUtranCommandCompleteDeactivationEutra (
                                               UrrRbcAwaitedInputInfo *input_p,
                                               UrrRbcReceivedInput    input,
                                               UrrRbcTransaction      *trans_p);
#endif

void UrrRbcMgSendIratHandoverCnf (IratHandoverResult     result);


void UrrRbcMgHandleHandoverFromUtranCommand_Eutra (
                                    UrrInternalSignalProcessRxAirSignal *sig_p);
void UrrRbcMgHandoverFromUtranCommandEutraActTime (UrrRbcTransaction *trans_p);
void UrrRbcMgHandoverFromUtranCommandEutraActTimeWait (
                                               UrrRbcAwaitedInputInfo *input_p,
                                               UrrRbcReceivedInput    input,
                                               UrrRbcTransaction      *trans_p);
void UrrRbcMgHandoverFromUtranCommandEutraIeToLte (UrrRbcTransaction *trans_p);
void UrrRbcMgHandoverFromUtranCommandEutraToLteWait (
                                               UrrRbcAwaitedInputInfo *input_p,
                                               UrrRbcReceivedInput    input,
                                               UrrRbcTransaction      *trans_p);
void  UrrRbcMgHandoverFromUtranCommandEutraIeProcessing (UrrRbcTransaction *trans_p);
void  UrrRbcMgHandoverFromUtranCommandEutraIeProcessingWait (
                                               UrrRbcAwaitedInputInfo *input_p,
                                               UrrRbcReceivedInput    input,
                                               UrrRbcTransaction      *trans_p);
void UrrRbcMgHandoverFromUtranCommandEutraDeactivate (UrrRbcTransaction *trans_p);
void UrrRbcMgHandoverFromUtranCommandEutraDeactivateWait (
                                               UrrRbcAwaitedInputInfo *input_p,
                                               UrrRbcReceivedInput    input,
                                               UrrRbcTransaction      *trans_p);
void UrrRbcMgHandoverFromUtranCommandEutraComplete (UrrRbcTransaction *trans_p);

void UrrRbcMgSendIratHandoverRequire (
                                IratHandoverRequire *iratHandoverRequire_p);

void UrrRbcMgHandleHandoverFromUtranCommandSelectCellComplete (Boolean successful);

void UrrRbcMgFreeResources (void);

void UrrRbcIeProcessNewUrnti (UU_RNTI newUrnti, UrrRbcTransaction *trans_p);

void UrrRbcIeProcessNewCrnti (UC_RNTI newCrnti, UrrRbcTransaction *trans_p);

void UrrRbcMgHandleRntiUpdates (UrrRbcTransaction *trans_p);

void UrrRbcMgSavePrimaryCpichInfo (
    UPrimaryCPICH_Info *primaryCPICH_Info_p,
    UrrRbcTransaction *trans_p);

void UrrRbcMgEndOfCellUpdateProcedure (UrrRbcTransaction  *trans_p);

void UrrRbcMgHandlePhyChanEstabFailure (UrrRbcTransaction *trans_p);

void UrrRbcMgHandlePhyChanEstabFailureWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction      *trans_p);

void UrrRbcMgHandlePhyChanEstabFailureCheckOldConfig (UrrRbcTransaction *trans_p);

void UrrRbcMgHandlePhyChanEstabFailureCheckOldConfigWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction      *trans_p);

void UrrRbcMgHandleRrcDeactReqWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction      *trans_p);

void UrrRbcMgHandleSendPhyDeactReq (UrrRbcTransaction      *trans_p);

void  UrrRbcMgHandleSendPhyDeactReqWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction      *trans_p);

void UrrRbcMgBuildRadioBearerSetupComplete (
    URRC_TransactionIdentifier transactionId,
    UrrRbcAwaitedInputInfo *info_p,
    UrrRbcTransaction *trans_p);

void UrrRbcMgBuildRadioBearerReleaseComplete (
    URRC_TransactionIdentifier transactionId,
    UrrRbcAwaitedInputInfo *info_p,
    UrrRbcTransaction *trans_p);

void UrrRbcMgSendRadioBearerReleaseComplete (UrrRbcTransaction       *trans_p);

void UrrRbcMgSendRadioBearerReleaseFailure (
    Boolean              transactionIdPresent,
    URRC_TransactionIdentifier  transactionId,
    T_UFailureCauseWithProtErr  failureCause,
    UProtocolErrorCause         errorCause,
    UTGPSI                      tgpsi,
    UrrRbcAwaitedInputInfo      *info_p);
void UrrRbcMgSendRadioBearerSetupComplete (UrrRbcTransaction       *trans_p);

void UrrRbcMgSendRadioBearerSetupFailure (
    Boolean              transactionIdPresent,
    URRC_TransactionIdentifier  transactionId,
    T_UFailureCauseWithProtErr  failureCause,
    UProtocolErrorCause         errorCause,
    UTGPSI                      tgpsi,
    UrrRbcAwaitedInputInfo      *info_p);
void UrrRbcMgBuildRadioBearerReconfigComplete (
    URRC_TransactionIdentifier transactionId,
    UrrRbcAwaitedInputInfo *info_p,
    UrrRbcTransaction    *trans_p);

void UrrRbcMgSendRadioBearerReconfigComplete (UrrRbcTransaction       *trans_p);

void UrrRbcMgSendRadioBearerReconfigFailure (
    Boolean              transactionIdPresent,
    URRC_TransactionIdentifier  transactionId,
    T_UFailureCauseWithProtErr  failureCause,
    UProtocolErrorCause         errorCause,
    UTGPSI                      tgpsi,
    UrrRbcAwaitedInputInfo      *info_p);

void UrrRbcMgBuildPhysChanReconfigComplete (
    URRC_TransactionIdentifier transactionId,
    UrrRbcAwaitedInputInfo *info_p,
    UrrRbcTransaction    *trans_p);

void UrrRbcMgSendPhysChanReconfigComplete (UrrRbcTransaction       *trans_p);

void UrrRbcMgBuildTransChanReconfigComplete (
    URRC_TransactionIdentifier transactionId,
    UrrRbcAwaitedInputInfo *info_p,
    UrrRbcTransaction    *trans_p);

void UrrRbcMgSendTransChanReconfigComplete (UrrRbcTransaction       *trans_p);

void UrrRbcMgSendTransChanReconfigFailure (
    Boolean              transactionIdPresent,
    URRC_TransactionIdentifier  transactionId,
    T_UFailureCauseWithProtErr  failureCause,
    UProtocolErrorCause         errorCause,
    UTGPSI                      tgpsi,
    UrrRbcAwaitedInputInfo      *info_p);

void UrrRbcMgSetCipherModeIePresent (
    UrrRbcTransaction *trans_p,
    Boolean isPresent);

void UrrRbcMgHandleDataBaseReversion (
    UrrRbcL123ConfigStatus l123ConfigStatus,
    UrrRbcTransaction *trans_p);

/* From file urrrbcfa.c (FACH) */

/* From file urrrbcpg.c (Paging) */
void UrrRbcPgCbFreeResources (void);

void UrrRbcPgCbNewServingCell (
    UUARFCN  freq,
    UPrimaryScramblingCode prScrCode);

void UrrRbcPgCbSib1Info (UCN_DomainSysInfoList  *list_p);

void UrrRbcPgCbHandleRrcUpdateReq (UrrInternalSignalRrcUpdateReq *sig_p);

void UrrRbcPgCbStoreUtranDrxCycleLength (UUTRAN_DRX_CycleLengthCoefficient_latest *utranDrxCycleLenCoeff_p);

void UrrRbcPgCbStoreCnDomainDrxCycleLength_UMI (UCN_DomainInformationListFull *list_p);

void UrrRbcPgCbUpdateDrxCycleLength (void);

void UrrRbcPgCbConfigPagingCbs (void);

void UrrRbcPgCbPagingCbsImplictlyRemoved (void);

Boolean UrrRbcPgCbStartT319Timer (void);
void UrrRbcPgCbStopT319Timer (void);

Int8 UrrRbcIeRlRemovalsLength (void);

void UrrRbcIeProcessPdcpSnInfoList (
    URB_WithPDCP_InfoList *pdcpInfoList_p,
    UrrRbcTransaction *trans_p);

void UrrRbcIeProcessPdcpSnInfoListCellUraUpdate (URB_WithPDCP_InfoList *pdcpInfoList_p);

void UrrRbcIeProcessRbInfoReconfigList(
    URB_InformationReconfigList_latest *rbReconfigList_p,
    UrrRbcTransaction *trans_p);

UCN_DomainIdentity UrrRbcIeProcessRabInfoToSetupList (
    URAB_InformationSetupList_latest *rabList_p,
    UrrRbcTransaction *trans_p);

void UrrRbcIeProcessRbInfoToSetupList (
    URB_InformationSetupList_latest   *rbList_p,
    UT_SINGLE_LINK_LIST_NODE_TYPE (UrrRbcEstablishedRabsList)  *rabListElement_p,
    UrrRbcTransaction *trans_p);

void UrrRbcIeProcessDLCommonInfoForAllRadioLinks (
    CphyRlCommonSetupReq      *commonRlInfo_p,
    UDL_CommonInformation_latest  *commonAllInfo_p,
    UrrRbcTransaction		  *trans_p);

void UrrRbcIeProcessFrequencyInfo (UFrequencyInfo        *freqInfo_p,
                                   UrrRbcTransaction     *trans_p);

void UrrRbcIeProcessDlInfoPerRadioLinkList (
    URRC_StateIndicator          toState,
    UrrRbcRadioLinkList          *dbList_p,
    UrrRbcRadioLinkList          *delList_p,
    UDL_InformationPerRL_List_latest *ieList_p,
    UrrRbcTransaction			 *trans_p,
    T_UDL_DPCH_InfoCommon_latest_cfnHandling cfnHandling,
    UTX_DiversityMode            txDiversityMode);

void UrrRbcIeProcessUlDpchInfoPredef (
    UUL_DPCH_InfoPredef    *ulDpchInfo_p,
    UUL_DPCH_InfoPredef_v770ext *ulDpchInfo_v770ext_p,
    CphyRlCommonSetupReq   *cphyRlSetupReq_p);

#if !defined (UPGRADE_EXCLUDE_2G)
Boolean UrrRbcIeProcessComplete (
    UHandoverToUTRANCommand_latest_IEs_complete *complete_p,
    UrrRbcTransaction *trans_p);

Boolean UrrRbcIeProcessPreconfiguration (
    UHandoverToUTRANCommand_latest_IEs_preconfiguration *preconfiguration_p,
    UrrRbcTransaction *trans_p);
void UrrRbcIeProcessDlDpchInfoCommonPost (
    UDL_CommonInformationPost *dl_CommonInformationPost_p,
    CphyRlCommonSetupReq      *cphyRlSetupReq_p);

void UrrRbcIeProcessUlDpchInfoPostFdd (
    UUL_DPCH_InfoPostFDD      *ul_DPCH_Info_p,
    CphyRlCommonSetupReq      *cphyRlSetupReq_p);

void UrrRbcIeProcessGsmMessage (
    UHandoverFromUTRANCommand_GSM_latest_IEs_gsm_message *ie_p,
    GrrRcHandoverToGsmReq *grrRcMsg_p);

void UrrRbcIeProcessFrequencyInfoFdd (
    UFrequencyInfoFDD     *freqInfo_p,
    UrrRbcTransaction     *trans_p);

void UrrRbcIeProcessDlInfoPerRadioLinkListPostFdd (
    UrrRbcRadioLinkList       *dbList_p,
    UDL_InformationPerRL_ListPostFDD *ieList_p);
#endif /* UPGRADE_EXCLUDE_2G */

void UrrRbcIeProcessDlCounterSync (
    UDL_CounterSynchronisationInfo_latest *dl_CounterSyncInfo_p,
    UrrRbcTransaction *trans_p);

void UrrRbcIeCreateProtocolErrorInformation (
    UrrSirAllocatedMemList  *memList_p,
    UProtocolErrorCause       cause,
    UProtocolErrorInformation **info_p);

void UrrRbcIeProcessUlTrChInfoForAllTrChs (
    UrrRbcCommonTrChInfo  *dbInfo_p,
    UUL_CommonTransChInfo_latest *commonInfo_p);

void UrrRbcIeProcessDlTrChInfoForAllTrChs (
    UrrRbcCommonTrChInfo     *list_p,
    UDL_CommonTransChInfo_latest *commonInfo_p,
    Boolean                   ulPres,
    UUL_CommonTransChInfo_latest *commonUlInfo_p);

void UrrRbcIeProcessSignallingRbInfoToSetupList2 (
    USRB_InformationSetupList2_latest  *rbList_p,
    UrrRbcTransaction *trans_p);

void UrrRbcIeProcessSignallingRbInfoToSetupList (
    USRB_InformationSetupList_latest *rbList_p,
    Boolean                      isHoToUtran,
    UrrRbcTransaction            *trans_p);

void UrrRbcIeProcessUtranDrxCycleLenCoeff (
    URRC_StateIndicator                       rrcState,
    UUTRAN_DRX_CycleLengthCoefficient_latest *coeff_p);

void UrrRbcIeProcessULDpchInfo (
    Boolean                iePresent,
    CphyRlCommonSetupReq  *commonRlInfo_p,
    UUL_DPCH_Info_latest  *ulDpchInfo_p,
    URRC_StateIndicator    rrcState);

#if defined(UPGRADE_3G_HSDPA)
void UrrRbcIeProcessCpc(Boolean dtx_drx_TimingInfoPresent,
                                UDTX_DRX_TimingInfo_r7 *dtx_drx_TimingInfo,
                                Boolean dtx_drx_InfoPresent,
                                UDTX_DRX_Info_r7 *dtx_drx_Info,
                                Boolean hs_scch_LessInfoPresent,
                                UHS_SCCH_LessInfo_r7 *hs_scch_LessInfo,
                                UrrRbcTransaction *trans_p,
                                UE_DCH_TTI edchTti,
                                Boolean activeSetUpdate);

void UrrRbcIeProcessDtxDrxTimingInfo (
    UDTX_DRX_TimingInfo_latest *dtx_drx_TimingInfo,
    UrrRbcTransaction *trans_p);

void UrrRbcIeProcessDtxDrxInfoCheckInvalidConfiguration (
    UDTX_DRX_Info_latest *dtx_drx_Info,
    UrrRbcTransaction *trans_p);

void UrrRbcIeProcessDtxDrxInfo (
    UDTX_DRX_Info_latest *dtx_drx_Info,
    UrrRbcTransaction *trans_p);

void UrrRbcIeProcessHsScchLessInfo (
    UHS_SCCH_LessInfo_latest *hs_scch_LessInfo,
    UrrRbcTransaction *trans_p);
#endif //UPGRADE_3G_HSDPA 

void UrrRbcIeProcessRadioLinkAdditionInfo (
    URL_AdditionInformationList_latest *additions_p,
/* ********** add begin */
#if defined (UPGRADE_ESCC)
    UServingCellChangeParameters *servingCellChangeParameters_p,
#endif //UPGRADE_ESCC
/* ********** add end */
    UrrRbcTransaction *trans_p);

void UrrRbcIeProcessRadioLinkRemovalInfo (
    URL_RemovalInformationList *removals_p,
    UrrRbcRadioLinkList *rbList_p);

void UrrRbcIeProcessDlAddReconfTrChInfoList (
    UDL_AddReconfTransChInfoList_latest   ieDlTrChList,
    UrrRbcTransaction                 *trans_p);

void UrrRbcIeProcessUlAddReconfTrChInfoList_latest (
    UUL_AddReconfTransChInfoList_latest    ieUlTrChList,
    UrrRbcTransaction *trans_p);

void UrrRbcIeProcessUlAddReconfTrChInfoList (UUL_AddReconfTransChInfoList          ieUlTrChList);

void UrrRbcIeProcessRbInfoAffectedList (URB_InformationAffectedList_latest *rbAffectedList_p);

void UrrRbcIeProcessCnDomainInfo (
    UCN_InformationInfo_latest *cnInfo_p,
    UrrRbcTransaction  *trans_p);

void UrrRbcIeProcessCnDomainInfoFull (UCN_InformationInfoFull *cnInfo_p,
                                                  Boolean primary_plmn_IdentityPresent,
                                                  UPLMN_Identity primary_plmn_Identity);

void UrrRbcIeFreeResources (void);

void UrrRbcInsertSignallingRbEstRabs (
    URB_Identity     rbIdentity,
    UrrRbcRbStarted  rbStarted);

void UrrRbcInsertRbEstRabs (
    URB_Identity     rbIdentity,
    UrrRbcRbStarted  rbStarted,
    UrrRbcRabRbList  *rbList_p);

void UrrRbcIeProcessRabInfoReconfigList (URAB_InformationReconfigList_latest *rabInfoReconfigList_p);

void UrrRbcIeProcessRbInfoReleaseList (URB_InformationReleaseList *rbReleaseList);

void UrrRbcIeProcessUlDeletedTrChInfoList (
    UUL_DeletedTransChInfoList_latest *ieUlTrChList_p,
    UrrRbcRadioBearerDatabase  *rbDb_p,
    UrrRbcTransaction *trans_p);

void UrrRbcIeProcessDlDeletedTrChInfoList (
    UDL_DeletedTransChInfoList_latest *ieDlTrChList_p,
    UrrRbcRadioBearerDatabase     *rbDb_p);

void UrrRbcIeProcessSigConnRelInd (UCN_DomainIdentity sigConnRelIndDomainId,
                                   UrrRbcEstablishedRabsList *estabRabsList_p,
                                   Boolean storeTillActivationTime);

void UrrRbcIeProcessTfcSubset  (UrrRbcRbMacTfcSubsetList *flatSubsetList_p,
                                UTFC_Subset              *tfcSubset_p);

void UrrRbcIeProcessTfcControlDuration (UrrRbcCommonTrChInfo     *commonInfo_p,
                              UTransportFormatCombinationControl *tfccMsg_p);

UCN_DomainIdentity UrrRbcGetSavedSignallingDomainToRelease(Boolean *present);
void UrrRbcInitSavedSignallingDomainToRelease(void);

/* From file urrrbcra.c (RACH) */
void UrrRbcRachFreeResources (void);

void UrrRbcRachRemoveIdleRach (void);

Int8 UrrRbcRachCountRequiredCphyMessages (void);

void UrrRbcRachConfigureCphyMessages (Int8 configId);

void UrrRbcRachConfigureCmac (Int8 configId);

void UrrRbcRachGetRachUlMacTrchInfo (CmacUlTrChConfigReq **ulMacTrchInfo_pp);

UTransmissionTimeInterval UrrRbcRachSelectTti (void);

#if defined(UPGRADE_UL_ECF) // ********** begin	
UrrRbcPrachFachSetupResult UrrRbcConfigureEcfUl(void);
#endif /* UPGRADE_UL_ECF  */ // ********** end  

UrrRbcPrachFachSetupResult UrrRbcRachSelectPrach (
    Boolean initialRach,
    UrrRbcCommonPhysChSysInfo *commonPhysChSysInfo_p);

void UrrRbcRachSavePrachSysInfoList (
    UPRACH_SystemInformationList *prachList_p,
    UrrSirAllocatedMemList *storedPrachMemList_p,
    UPRACH_SystemInformationList *storedPrachList_p,
    UAdditionalPRACH_TF_and_TFCS_CCCH_List *additionalList_p);

void UrrRbcSaveAdditionalPrachConfiguration (UAdditionalPRACH_TF_and_TFCS_CCCH_IEs *configuration);

void UrrRbcFlattenAdditionalTfData (DynamicTfData *msg_p);

Int8 UrrRbcGetAllAmUmRbsFromEstablishedRabs (URB_Identity   *rbIdArray_p);

Int8 UrrRbcGetNumberOfAmUmRbsFromEstablishedRabs (UCN_DomainIdentity cnDomain);

#if defined(UPGRADE_UL_ECF) // ********** begin	
UrrRbcPrachFachSetupResult UrrRbcSelectSccpchAndPrach (UrrRbcTransaction *trans_p, Boolean configEcfDl, Boolean configEcfUl);
#else /* UPGRADE_UL_ECF */
UrrRbcPrachFachSetupResult UrrRbcSelectSccpchAndPrach (UrrRbcTransaction *trans_p, Boolean configEcfDl);
#endif /* UPGRADE_UL_ECF  */ // ********** end  

UrrRbcUlTfcsTable *UrrRbcRachGetTfcs (void);

/* From file urrrbcsm.c (SECURITY MODULE) */
void UrrRbcSmAbortCipheringConfigForCnDomain (UCN_DomainIdentity  lccd);
void UrrRbcSmAbortCipheringConfigForAllCnDomains (UrrRbcTransaction   *trans_p);

void UrrRbcSmAddRb2ToUlCipherActivationTime (URLC_SequenceNumber rlc_SequenceNumber);

void UrrRbcSmAmUmRbCipheringConfig (UrrRbcTransaction *trans_p);

void UrrRbcSmBuildCmacCipherConfigReqForRbSetup (
    UCN_DomainIdentity cnDomain,
    CmacCipheringConfigReq *cmacCipheringConfigReq_p);

Boolean UrrRbcSmBuildCount_C_ActivationTime (
    UActivationTime *countcActTime_p,
    UrrRbcTransaction *trans_p);

Boolean UrrRbcSmBuildRb_UL_CiphActivationTimeInfo (
    URB_ActivationTimeInfoList *ulCiphActTime_p,
    UrrRbcTransaction    *trans_p);

Boolean UrrRbcSmBuildUlIntegProtActivationInfo (
    UrrRbcAwaitedInputInfo       **info_pp,
    UIntegrityProtActivationInfo **UIntegrityProtActivationInfo_pp,
    UrrRbcTransaction            *trans_p);

Boolean UrrRbcMgCipherModeIePresent (UrrRbcTransaction    *trans_p);

Boolean UrrRbcSmIntegrityProtectionModeIePresent (UrrRbcTransaction *trans_p);

void UrrRbcSmCipherReconfigurationComplete (void);

void UrrRbcSmCipherReconfigurationFailure (void);

void UrrRbcSmClearCipherStatus (void);

void UrrRbcSmClearLatestConfiguredCnDomain (void);

void UrrRbcSmClearStartValueToTransmit (void);

KeySeqId UrrRbcSmConvertKeySetIdentifierToKsi (
    RrcKeySequence keySetIdentifier,
    UCN_DomainIdentity cnDomain);

void UrrRbcSmCopyInRbUlCipherActivationTime (URB_ActivationTimeInfoList *activationTimeList_p);

void UrrRbcSmCopyOutRbUlCipherActivationTime (URB_ActivationTimeInfoList *activationTimeList_p);

void UrrRbcSmDeletePendingCkFromNas (UCN_DomainIdentity cnDomain);

Boolean UrrRbcSmFillCipherInfo (
    BearerIdentity rbId,
    CrlcCipherInfo *cipherInfo_p,
    UrrRbcTransaction    *trans_p);

Boolean UrrRbcSmGetCipheringActTime (UActivationTime *tmCipheringActTime_p);

UrrRbcSmCipherConfiguration *UrrRbcSmGetCipherConfigToUse (
                                                UCN_DomainIdentity cnDomain,
                                                UrrRbcTransaction *trans_p);

UrrRbcSmCipheringStartedStatus UrrRbcSmGetCipherStatus (UCN_DomainIdentity cn_DomainIdentity);

USTART_Value UrrRbcStGetMaxRb2Hfn (void);

UrrRbcSmSecurityAffected UrrRbcSmGetSecurityModification (UCN_DomainIdentity cn_DomainIdentity);

USTART_Value UrrRbcSmGetStartValueToTransmit (void);

void UrrRbcSmInit (Boolean firstInitialisation);

void UrrRbcSmIntegrityProtectionReconfigurationComplete (UrrRbcTransaction *trans_p);

Boolean UrrRbcSmRb2IsSuspended (void);

Boolean UrrRbcSmIsNewCkInConfig (UCN_DomainIdentity cnDomain);

void UrrRbcSmNotifyKsiChange (UCN_DomainIdentity cnDomain);

void UrrRbcSmPrepareForCipheringResponse (UrrRbcTransaction *trans_p,
                                          USTART_Value startValue);

Boolean UrrRbcSmProcessCipherModeInfo (UCipheringModeInfo_latest *cipherInfo_p,
                                       UrrRbcTransaction *trans_p);

void UrrRbcSmProcessCrlcPrepareCipherCfgChangeCnf (UrrRbcReceivedData *data_p);

void UrrRbcSmProcessCrlcSuspendCnf (UrrRbcReceivedData *data_p);

Boolean UrrRbcSmProcessIntegrityProtectionModeInfo (UIntegrityProtectionModeInfo *integProtModeInfo_p);

Boolean UrrRbcSmProcessSecurityModeCommand (
    USecurityModeCommand_latest_IEs *msgIEs_p,
    UrrRbcTransaction    *trans_p);

void UrrRbcSmResumeAllSuspendedRbs (void);

void UrrRbcSmRrcConnIsReleased (void);

void UrrRbcSmSendCmacCipheringConfigReq (
    UCN_DomainIdentity cnDomain,
    Boolean cipheringActivationTimePresent,
    UActivationTime cipheringActivationTime,
    USTART_Value startValue,
    UrrRbcTransaction *trans_p);

void UrrRbcSmSendCrlcPrepareCipherCfgChangeReq (
    Int16        cipherModeResponseBitLen,
    USTART_Value startValue);

Boolean UrrRbcSmSendIntegrityProtectionActivationInfo (UIntegrityProtActivationInfo **activationInfo_p_p);

void UrrRbcSmSetCipherStatusStarted (
    UCN_DomainIdentity cn_DomainIdentity,
    UrrRbcSmCipheringStartedStatus status);

void UrrRbcSmSetIntegrityProtectionModeIePresent (
    UrrRbcTransaction *trans_p,
    Boolean isPresent);

void UrrRbcSmSetPendingCkFromNas (
    UCN_DomainIdentity cnDomain,
    USecurityKey *ck_p,
    RrcKeySequence keySetIdentifier,
    Boolean   unusedKey);

void UrrRbcSmSetPendingIkFromNas (
    UCN_DomainIdentity cnDomain,
    USecurityKey *ik_p,
    RrcKeySequence keySetIdentifier,
    Boolean newKey);

void UrrRbcSmSetSecurityModificationForAllCnDomains (UrrRbcSmSecurityAffected status);

void UrrRbcSmSetSecurityModificationForSecurityModeCommand (UCN_DomainIdentity cnDomain);

void UrrRbcSmSetStartValueForThisMsg (
    UCN_DomainIdentity cnDomain,
    USTART_Value startValue);

void UrrRbcSmSetStartValueToTransmit (USTART_Value svtt);

void UrrRbcSmSetTmRbPresent (
    UCN_DomainIdentity cnDomain,
    Boolean tmRbIsPresent);

UrrRbcTransaction *UrrRbcSmGetCipherReconfigTransPtr (void);

void UrrRbcSmStartIntegrityProtectionForSignallingRb (
    UCN_DomainIdentity cnDomain,
    USTART_Value startValue,
    URB_Identity rbIdentity,
    UrrRbcTransaction *trans_p);

void UrrRbcSmSuspendRb2 (void);

void UrrRbcSmUpdateIkFromNas (UCN_DomainIdentity cnDomain);

/* From file urrrbcst.c (START CALCULATION) */
void UrrRbcStAllTmRbReleased (void);

void UrrRbcStClearCounterCheckData (void);

USTART_Value UrrRbcStGetUsimStart (UCN_DomainIdentity  cnDomain);

void UrrRbcStInit (Boolean firstInitialisation);

void UrrRbcStInitTmCnDomainHfn (
    UCN_DomainIdentity cnDomain,
    USTART_Value    startValue,
    KeySeqId        ksi);

void UrrRbcStInitAmUmRbHfn (
    URB_Identity    rbId,
    USTART_Value    startValue,
    KeySeqId        ksi);

UCN_DomainIdentity UrrRbcStGetCnDomain (BearerIdentity     rbId);

void UrrRbcStHandleStartValueTransmitted (UrrInternalSignalStartValueTransmitted *startValueTxed_p);

void UrrRbcStLatestConfiguredDomainChange (UCN_DomainIdentity  lccd);

void UrrRbcStNewKsi (
    UCN_DomainIdentity cnDomain,
    KeySeqId   ksi,
    Boolean isUnusedKey);

void UrrRbcStResetTmHfnData (
    CmacStartValues *csTmStartValues_p,
    CmacStartValues *psTmStartValues_p,
    KeySeqId csKsi,
    KeySeqId psKsi,
    UActivationTime     activationTime);

void UrrRbcStRrcConnIsReleased (void);

void UrrRbcStSetSimStart (
    UCN_DomainIdentity cnDomain,
    USTART_Value newStartValue);

void UrrRbcStWriteStartInfoArray (
    Int8 *numberOfStartInfo_p,
    RrcStartInformation *startInfo_p);

void UrrRbcStStoreSecModeCmdStartValue (Boolean startValueExist, USTART_Value startValue);
Boolean UrrRbcStGetSecModeCmdStartValue (USTART_Value *startValue);
/* From file urrrbcdc.c (Default Configurations) */
Boolean UrrRbcDcGetDefaultConfig (
    UDefaultConfigIdentity_latest     defaultConfigId,
    UrrSirEncodedPredefinedConfig *predefinedData_p);

/* From file urrrbcfa.c (FACH configuration) */
void UrrRbcFachFreeResources (void);

UrrRbcDlTfcsTable *UrrRbcFachGetFachTfcs (void);

void UrrRbcValidateDpchFrameOffset (
    UT_SINGLE_LINK_LIST_NODE_TYPE (UrrRbcRadioLinkList) *dbEle_p,
    UDL_InformationPerRL_latest_dl_dpchInfo     *ieEle_p,
    T_UDL_DPCH_InfoCommon_latest_cfnHandling    cfnHandling,
    UrrRbcTransaction		*trans_p);

void UrrRbcReestablishAmUmRbsAfterSrnsReloc (UrrRbcTransaction *trans_p);

void UrrRbcSendCrlcStopReqAllRbsNotRb2 (void);

void UrrRbcStopRb134 (void);

void UrrRbcReestablishRb2 (UrrRbcTransaction *trans_p);

Boolean UrrRbcDchEstablishmentFailed (void);

void UrrRbcUpdateTfcs (
    UTFCS *recievedTfcs_p,
    Int32 ctfcList[],
    Int16 maxNumberOfTFCs,
    Int16 *numberOfTFCs_p,
    UrrRbcReferenceGainFactor refGains[],
    UrrRbcPowerOfset powerOffsets[]);

void UrrRbcSendActiveSetToMcrOnLeavingCellDch (void);

UrrRbcTransaction *UrrRbcSmGetSecurityModeCommandTransaction (void);

void UrrRbcSmSetSecurityModeCommandTransaction (UrrRbcTransaction *trans_p);

void UrrRbcSmIncrementCountIForSmcAbortIfRequired (UrrRbcTransaction *trans_p);

void UrrRbcProcessTmRbsAfterRbRelease (void);

void UrrRbcReconfigureFachOccasions (UrrRbcTransaction *trans_p);

void UrrRbcSetPagingCbsCurrentlyAllowed (Boolean pagingCbsCurrentlyAllowed);

Boolean UrrRbcGetCellHasChanged(void);

void UrrRbcSetInitialFachRachConfigured (Boolean initialFachRachConfigured);

void UrrRbcSetFachRachSRBsConfigured (Boolean fachRachSRBsConfigured);

void UrrRbcSetFachRachSRBsMapped (Boolean fachRachSRBsMapped);

Boolean UrrRbcUSysInfoType16Decode (
    const Int8     *encodedData_p,
    Int32          encodedDataLengthInBits,
    PerError       *perError,
    USysInfoType16 **sib16_pp);

void UrrRbcMgHandleCphyDeactivateReq (UrrInternalSignalCphyDeactivateReq   *sig_p);


void UrrRbcMgHandleCphyDeactivateReqWait (
    UrrRbcAwaitedInputInfo *input_p,
    UrrRbcReceivedInput    input,
    UrrRbcTransaction      *trans_p);

void UrrRbcCheckCsNasIndicator (UNAS_Synchronisation_Indicator nasSyncInd);

void UrrRbcSendHsdpaReleaseToMac (
    Int8 configId,
    ActivationTime *actTime_p,
    Boolean currentQueue);
void UrrRbcSendHsdpaReleaseToPhy (
    Int8 configId,
    ActivationTime *actTime_p);

#if defined (UPGRADE_3G_HSDPA)
void UrrRbcProcessMacHsDelQueue (
    UMAC_hs_DelQueue *del_p,
    Boolean isFromMacdFlow);
Boolean UrrRbcHsQueueConfigDiffFromBackup (void);
Boolean UrrRbcHsPhyConfigDiffFromBackup (void);
/*********** add begin*/
#if defined (RRC_DC_HSDPA)
Boolean UrrRbcSecondaryCellHsPhyConfigDiffFromBackup (void);
#endif //RRC_DC_HSDPA
/*********** add end*/
#endif /* UPGRADE_3G_HSDPA */

#if defined (UPGRADE_3G_EDCH)
void UrrRbcProcessPrimaryErnti(
    UE_RNTI ernti,
    UrrRbcTransaction *trans_p);
void UrrRbcProcessSecondaryErnti (
    UE_RNTI ernti,
    UrrRbcTransaction *trans_p);
void UrrRbcProcessEdchInformation (
    UUL_EDCH_Information_latest *edchInfo,
    UrrRbcTransaction *trans_p);
void UrrRbcProcessEdchServingCellInd (
    URRC_StateIndicator toState,
    Boolean servingInd,
    UPrimaryScramblingCode rl,
    UrrRbcTransaction *trans_p);
void UrrRbcRemoveCellFromEdchActiveSet (UPrimaryScramblingCode sc);
void UrrRbcProcessEdchReconfigurationInfo(
    UE_DCH_ReconfigurationInfo_latest *element_p,
    Boolean primaryErntiPresent,
    Boolean secondaryErntiPresent,
    UrrRbcTransaction *trans_p);
void UrrRbcProcessEdchAddReconfUlTrChInfo(
    UUL_AddReconfTransChInformation_latest_e_dch *trChInfo,
    UrrRbcTransaction *trans_p);
void UrrRbcProcessEdchDeletedTransChInfo (
    UE_DCH_MAC_d_FlowIdentity macdFlowId,
    UrrRbcTransaction *trans_p);
void UrrRbcSendHsupaReleaseToMac (
    Int8 configId,
    ActivationTime *actTime_p);
#endif /* UPGRADE_3G_EDCH */

#if defined (UPGRADE_3G_FDPCH)
void UrrRbcProcessActiveSetAdditionInformation(
    UActiveSetUpdate_latest *activeSetUpdate_p,
    UrrRbcTransaction   *trans_p);

Boolean UrrRbcCheckDoffVsFdpchOffsetValue (UrrRbcTransaction *trans_p, Boolean addStep);
#endif /* UPGRADE_3G_FDPCH */

/* CQ00104672 added begin */
#if defined (UPGRADE_RRC_NO_CM_ON_ADJ_FREQ) || defined (UPGRADE_RRC_NO_CM_ON_INTER_BAND_FREQ)
void UrrRbcUpdateMeasurementOnSecondaryFreq (void);
#endif
/* CQ00104672 added end */
Boolean UrrRbcGetAssignCountC (void);

void UrrRbcPgCbSelectPagingCbsChannelsForEcf(UrrRbcTransaction *trans_p);

#if defined(UPGRADE_3G_HSDPA)
void UrrRbcSendRrcHsStatusInd (
    Boolean hsDschReception,
    Boolean hsDschReceptionChanged,
    Boolean edchTransmission,
    Boolean edchTransmissionChanged);

void UrrRbcSendCellHsCapInd (void);
#endif //UPGRADE_3G_HSDPA 

UrrRbcSmCipheringStartedStatus UrrRbcSmGetPrevious3GCipherStatus(UCN_DomainIdentity cn_DomainIdentity);

void UrrRbcSmClearPersistentCipherStatus(void);


/* From file urrrbcfa.c (Fach) */
UrrRbcPrachFachSetupResult UrrRbcFachSelectFach (
                                 UrrIdentityValue64Bit  *identity_p,
                                 Boolean   initialFach,
                                 USCCPCH_SystemInformationList *sccpchList_p,
                                 Int8                          *configId_p,
                                 UrrRbcTransaction *trans_p,
// ********** begin									 
                                 Boolean configEcfDl
#if defined(UPGRADE_UL_ECF)								 
                                 ,Boolean configEcfUl
#endif /* UPGRADE_UL_ECF */ 							 
								 );
// ********** end  		 

PerBuffer *UrrRbcIeGetSib16PerBuffer(void);
#if defined (VOID_SPECIAL_VALUE_OF_HE) //********** start
Boolean UrrRbcIeIsMacHesSupport(void);
#endif//VOID_SPECIAL_VALUE_OF_HE //********** ens

USTART_Value CalculateStartForRlcReestab (UCN_DomainIdentity  cnDomain,
                                                 UrrRbcTransaction   *trans_p);/*********** remove static*/
/* ********** added begin */
void UrrRbcCheckBestRbMappingValid(void);
/* ********** added end */

/* **********,added begin */
Int8 UrrRbcFindRealUlTrch (UTransportChannelType     trChType,
                       UTransportChannelIdentity trChId,
                       UT_DOUBLE_LINK_LIST_NODE_TYPE (UrrRbcULTrChList) **ulTrch_p);
void UrrRbcCheckUlRlcSizeList (void);
/* **********,added end */

#if !defined(UPGRADE_COMMON_STORE)
#if defined (ON_PC) && !defined (PC_TTCN_INT_TEST )  
Boolean GrrDsIsSimPowerOffOngoing(Int8 simId);
#endif
#endif /*UPGRADE_COMMON_STORE*/


void RbcMgSetIsWaitForAmDataCnfWhenReceivingKeySetCfg(UCN_DomainIdentity cnDomain, Boolean val);    /* CQ00042812, added */
UrrRbcTransaction *UrrRbcSmGetSmcTrans (void);    /* CQ00042812, added */

void ClearAwaitedInputs (void);      /* CQ00048560 adedd */
Boolean UrrRbcMgCheckIfWaitingforAckForSmcCompletemessage (void);/*********** add*/
Boolean UrrRbcGetDeactivateByMMpending (void);  /*********** added*/

void ClearHsDschInFachAndPchCapability (void);/*********** add */
Int8 UrrRbcRealUlTrchNum (void);/************/
Boolean UrrRbcCheckTmRbExist (void);/*********** added*/

#if defined(UPGRADE_UL_ECF) // ********** begin	
void UrrRbcDetermineAndProcessUlEcfParams (UrrRbcTransaction  *trans_p);
#endif /* UPGRADE_UL_ECF  */ // ********** end  

#if defined (URR_MRAT_ICS_SEARCH)//********** add
void UrrRbcMgHandleIcsEvalFinished(UrrInternalSignalIcsEvalFinished* icsEvalFinished_p);
#endif

void UrrRbcClearHspaPlusCapCellInd (void);/*********** add*/
UrrRbcSmCipherConfiguration *UrrRbcSmGetCipherConfig (UCN_DomainIdentity cnDomain);    /* ********** added */


#endif

#if defined (UPGRADE_DSDS)
#if !defined(UPGRADE_COMMON_STORE)
void UrrSendIratDsAbortCellSelectionReq (void);/*********** add*/
#endif
#endif

/* END OF FILE */

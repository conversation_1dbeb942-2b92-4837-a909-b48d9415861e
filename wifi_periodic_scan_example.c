/**
 * 定时WiFi扫描功能使用示例
 * 
 * 本文件演示如何使用新添加的定时WiFi扫描功能
 */

#include "gui/lv_watch/ke/ke_location_flow.h"

/**
 * 示例：在系统启动时启动定时WiFi扫描
 */
void example_start_periodic_wifi_scan(void)
{
    // 启动定时WiFi扫描
    // 这将每隔60秒自动触发一次WiFi扫描
    Flw_WiFi_Periodic_Scan_Start();
    
    printf("定时WiFi扫描已启动，每60秒扫描一次\n");
}

/**
 * 示例：在系统关闭或特定条件下停止定时WiFi扫描
 */
void example_stop_periodic_wifi_scan(void)
{
    // 停止定时WiFi扫描
    Flw_WiFi_Periodic_Scan_Stop();
    
    printf("定时WiFi扫描已停止\n");
}

/**
 * 示例：在应用初始化时同时启动测试扫描和定时扫描
 */
void example_init_wifi_scanning(void)
{
    printf("初始化WiFi扫描功能...\n");
    
    // 首先执行一次完整的WiFi测试扫描
    Flw_WiFi_Scan_Test_Interface();
    
    // 等待测试扫描完成后，启动定时扫描
    // 注意：实际使用中可能需要在测试扫描完成回调中启动定时扫描
    // 这里仅作为示例，实际部署时需要根据具体需求调整
    
    // 延迟启动定时扫描（避免与测试扫描冲突）
    // 在实际代码中，建议在测试扫描完成后再启动定时扫描
    printf("将在测试扫描完成后启动定时扫描...\n");
}

/**
 * 示例：条件性启动定时扫描
 * 可以根据系统状态、用户设置等条件决定是否启动定时扫描
 */
void example_conditional_start_periodic_scan(bool enable_periodic_scan)
{
    if (enable_periodic_scan) {
        printf("用户设置启用定时WiFi扫描\n");
        Flw_WiFi_Periodic_Scan_Start();
    } else {
        printf("用户设置禁用定时WiFi扫描\n");
        Flw_WiFi_Periodic_Scan_Stop();
    }
}

/**
 * 示例：在低电量模式下停止定时扫描以节省电量
 */
void example_power_management(int battery_level)
{
    if (battery_level < 20) {  // 电量低于20%
        printf("电量不足，停止定时WiFi扫描以节省电量\n");
        Flw_WiFi_Periodic_Scan_Stop();
    } else if (battery_level > 30) {  // 电量恢复到30%以上
        printf("电量充足，重新启动定时WiFi扫描\n");
        Flw_WiFi_Periodic_Scan_Start();
    }
}

/**
 * 使用说明和注意事项
 */
void example_usage_notes(void)
{
    printf("=== 定时WiFi扫描功能使用说明 ===\n");
    printf("1. 调用 Flw_WiFi_Periodic_Scan_Start() 启动定时扫描\n");
    printf("2. 调用 Flw_WiFi_Periodic_Scan_Stop() 停止定时扫描\n");
    printf("3. 扫描间隔：60秒（可在代码中修改 WIFI_PERIODIC_SCAN_INTERVAL_MS）\n");
    printf("4. 单次扫描超时：30秒（可在代码中修改 WIFI_PERIODIC_SCAN_TIMEOUT_MS）\n");
    printf("5. 定时扫描会自动避免与现有WIFI_TEST扫描冲突\n");
    printf("6. 扫描结果会输出到调试日志，格式与WIFI_TEST保持一致\n");
    printf("7. 建议在系统启动完成后启动定时扫描\n");
    printf("8. 在低电量或特殊模式下可以停止定时扫描以节省电量\n");
    printf("================================\n");
}

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/******************************************************************************
**
**  COPYRIGHT (C) 2000 Intel Corporation
**
**  FILENAME:      XsCp15.h
**
**  PURPOSE:       Function prototypes for iXSC Coprocessor 15 access functions.
**                 Functions defined in XsCp15.s.
**
**  Requires systypes.h
**
**  LAST MODIFIED: 12/07/2000
******************************************************************************/

/*
*******************************************************************************
    Functions in alphabetical order.
*******************************************************************************
*/
#ifndef _XSCP15_H_
#define _XSCP15_H_

#include "global_types.h"

/*  Generic Names */
void    CPUInvalidateDCacheLine        (UINT32);
void    CPUCleanDCacheLine             (UINT32);

/*  Normal Names */
void    XsAllocateDCacheLine          (UINT32);
void    XsCleanDCache                 (void);
void    XsCleanDCacheLine             (UINT32);
void    XsCpWait                      (void);
void    XsCreateDataRAM               (UINT32, UINT32);
void    XsDisableDCache               (void);
void    XsDisableDCacheLockMode       (void);
void    XsDrainBuffers                (void);
void    XsEnableDCache                (void);
void    XsEnableDCacheLockMode        (void);
void    XsEnableMMU                   (int enableDCache); // Anton
void    XsEnableMMU_NoCaches          (void);  // Anton
void    XsDisableMMU                  (void);

void    XsFetchAndLockICacheLine      (UINT32);
UINT32  XsGetARMControl               (void);
UINT32  XsGetAuxControl               (void);
UINT32  XsGetCacheType                (void);
UINT32  XsGetCoprocessorAccess        (void);
UINT32  XsGetDomainAccessControl      (void);
UINT32  XsGetFaultAddress             (void);
UINT32  XsGetFaultStatus              (void);
UINT32  XsGetProcessID                (void);
UINT32  XsGetProcessorVersion         (void);
UINT32  XsGetTransTableBase           (void);
void    XsInvalidateAllCaches         (void);
void    XsInvalidateBTB               (void);
void    XsInvalidateDCache            (void);
void    XsInvalidateDCacheLine        (UINT32);
void    XsInvalidateDTLB              (void);
void    XsInvalidateDTLBEntry         (UINT32);
void    XsInvalidateIandDTLBs         (void);
void    XsInvalidateICacheAndBTB      (void);
void    XsInvalidateICacheLine        (UINT32);
void    XsInvalidateITLB              (void);
void    XsInvalidateITLBEntry         (UINT32);
void    XsInvalidIAndDCachesAndBTB    (void);
void    XsLockDataIntoCache           (UINT32, UINT32);
void    XsSetARMControl               (UINT32);
void    XsSetAuxControl               (UINT32);
void    XsSetCoprocessorAccess        (UINT32);
void    XsSetDomainAccessControl      (UINT32);
void    XsSetProcessID                (UINT32);
void    XsSetTransTableBase           (UINT32);
void    XsTransAndLockDTLB_Entry      (UINT32);
void    XsTranslateAndLockITLB_Entry  (UINT32);
void    XsUnlockDataCache             (void);
void    XsUnlockDataTLB               (void);
void    XsUnlockInstructionCache      (void);
void    XsUnlockInstructionTLB        (void);
//#if defined (_TAVOR_HARBELL_) || defined(SILICON_PV2)
#if defined(SILICON_PV2)
void    XsFlushDCacheLine             (UINT32 line);
#endif
#if defined (L2_CACHE_ENABLE)
void    L2CacheEnable                 (void);
void    L2CacheCleanInvalidate        (void);
#endif

#endif /* _XSCP15_H_ */


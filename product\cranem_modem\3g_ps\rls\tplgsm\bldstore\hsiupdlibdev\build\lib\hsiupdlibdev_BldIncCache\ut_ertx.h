/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/tplgsm/utinc/ut_ertx.h#3 $
 *   $Revision: #3 $
 *   $DateTime: 2006/02/09 12:25:17 $
 **************************************************************************
 * File Description
 * ----------------
 *
 *
 * utility library for working with audio formats
 *   ERTX <-> iMelody
 **************************************************************************/

#ifndef UT_ERTX_H
#define UT_ERTX_H

/***************************************************************************
 * Include Files
 ***************************************************************************/
#include <ut_vob.h>

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/

/***************************************************************************
 * Type Definitions
 ***************************************************************************/

/***************************************************************************
 * Variables
 ***************************************************************************/

/***************************************************************************
 * Macros
 ***************************************************************************/

/***************************************************************************
 *  Function Prototypes
 **************************************************************************/


Int8* utErtxFromImelody( UtVobjCharCode *imelody, 
                         Int16          imelLen, 
                         Int16          *ertxLen_p );

UtVobjCharCode* utImelodyFromErtx( Int8  *ertx_p, 
                                   Int16 ertxLen, 
                                   Int16 *imelLen_p );

#endif /* AFSM_LC4_H */
/* END OF FILE */






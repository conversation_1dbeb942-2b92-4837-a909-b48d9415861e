/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/*******************************************************************************
 * TTPCom Software Copyright (c) TTPCom Limited 1997-2004
 *******************************************************************************
 * $Id: //central/releases/delivery/Branch_release_14/tplgsm/l1inc/umph_sig.h#2 $
 * $Revision: #2 $
 * $DateTime: 2005/06/08 12:53:33 $
 *******************************************************************************
 * Group            : Dual Mode, Signalling Interface
 * File Description :
 ******************************************************************************/
#if !defined (UMPH_SIG_H)
#define       UMPH_SIG_H

#if defined (INTEL_UPGRADE_DUAL_RAT)
/***************************************************************************
 * Nested Include Files
 **************************************************************************/
#include <kernel.h>
#include <l123_typ.h>
#include <l13_typ.h>
#include "umph_typ.h"
#if defined (HL_LTG)
#include <pl_td_globs.h>
#elif defined HL_LWG
#include <pl_w_globs.h>
#endif
#include <pl_d_globs.h>
#include <umph_typ.h>


#ifndef PLG_BCH_DATA_LEN
#define PLG_BCH_DATA_LEN 31
#endif
#define UMAX_NUM_GSM_CELLS 6	/* No of Cells in a UTRAN to GSM Cell Reselection Request */
#define UMAX_NUM_FDD_NCELLS	32	/* No of UTRAN Cells to Measure */

/***************************************************************************
 * Manifest Constants
 **************************************************************************/
/***************************************************************************
 * Type Definitions
 **************************************************************************/

/** \addtogroup 2G_GRR_L1_DRAT
 * This interface supports all signalling to support Inter-RAT measurements
 * and mode change.
 * 
 *
 * @{
 */


/** Inter-RAT measurement abort
 * This signal is sent from the GRR (in any GSM state) to GSM L1 to abort the
 * 3G operation(s) indicated by fddAbortCtrl, so that the PS can
 * initiate a new (GSM or 3G) operation
 */

typedef struct UmphAbortFddOperationReqTag
{
    /** Abort Inter-RAT measurements
     * Options: FDD_ABORT_ALL, FDD_ABORT_RSSI_SCAN or FDD_ABORT_FIND_CELL
     */
    FddAbortCtrl    fddAbortCtrl;
}
UmphAbortFddOperationReq;

/** MIB, SIB Decoding
 * These signals are sent to perform MIB and SIB
 * decoding
 *
 */
/** SIB Decode Request
 *
 * This signal is sent by GRR to GSM L1 to request the Master
 * Information Block, Scheduling Blocks or System Information
 * Blocks from the BCCH of a UMTS neighbour cell.
 *
 * Specifically, GRR requires only the System Information Block
 * type 16 that contains the predefined configuration of the
 * neighbour cell that the network may reference during GSM
 * to UMTS handover.
 *
 * The Master Information Block and the Scheduling Block contains
 * references and scheduling information to a number of System
 * Information blocks in a cell.
 *
 * If sibControl is set to USIB_ALL, then L1 should return all
 * SIBs to GRR
 *
 * If sibControl is set to USIB_OFF, then L1 should stop decoding
 * SIBs on the UMTS cell.
 *
 * If sibControl is set to USIB_SKIP, then L1 will skip over all
 * SIBs until the System Frame Number (SFN) indicated in skipToSfn
 * is reached
 */

typedef struct UmphFddSibDecodeReqTag
{
    /** uArfcn
     * uArfcn of cell to decode data on
     */
    UArfcn                  uArfcn;

    /** Primary Scrambling Code
     * Primary Scrambling Code of cell to decode data on
     */

    UPrimaryScramblingCode  primaryScramblingCode;

    /** Skip SIB decode control
     * Primary Scrambling Code of cell to decode data on
     */

    USysInfoBlockControl    sibControl;

    /** Skip to System Frame Number
     * Frame Number to skip too
     */

    Int16                   skipToSfn;

    /** Tx Diversity control
     * on or off
     */
    Boolean                 useTxDiversity;
}
UmphFddSibDecodeReq;


/** Inter-RAT Neigbour Cell measurements
 * These signals are used for 3G nieghbour cell monitoring in
 * Idle, Dedicated and PTM.
 *
 *
 */
/** Neighbour Cell Measurement List
 * This signal is sent by GRR to GSM L1 to indicate which UMTS
 * cells L1 should use for neighbour cell monitoring.
 *
 * The cells to perform measurements on are communicated to the
 * MS in MEASUREMENT INFORMATION messages (dedicated mode),
 * SI2quater messages (idle mode), or PSI3quater messages (PTM).
 */


typedef struct UmphFddNcellMeasReqTag
{
    Int8                    seqNum;
    FddNcellMeasCtrl        fddNcellMeasCtrl;
    Boolean                 fddCellsHavePriority;
    Int8                    numFddUtrans;
    UFddUtranElement        fddUtranElement [UMAX_NUM_MEAS_FDD_UTRANS];
}
UmphFddNcellMeasReq;


/** Neighbour Cell Measurement Control
 * This signal is sent from GRR to GSM L1 to control the reporting of
 * FDD measurements to the PS (i.e. perform measurements, stop measurements etc.)
 */

typedef struct UmphFddNcellMeasCtrlReqTag
{
    FddNcellMeasCtrl    fddNcellMeasCtrl;
    Boolean             fddCellsHavePriority;
}
UmphFddNcellMeasCtrlReq;

/** Find Next Cell Request
 * This signal is sent from GRR to request GSM L1 to synchronise to
 * and camp onto the next strongest UTRAN cell for the requested
 * uArfcn in the #UmphFddFindCellReq
 */

typedef EmptySignal UmphFddFindNextCellReq;

/** Reselection
 * These signals a used for reselection and
 * cell change orders
 *
 */
/** Relesect to GSM Request
 * This signal is sent from GRR to GSM L1 to initiate a
 * reselection from a UMTS to a GSM cell. This signal
 * also activates L1 for GSM services.
 * GRR will provide L1 with the ARFCN and BSIC of up to
 * the strongest 6 GSM cells in descending order of
 * strength.
 *
 * The ARFCN and BSIC lists are in priority order so the
 * cell in index [0] is first to try.
 *
 * If the bsic in the signal != bsic in L1 info then the
 * next cell in the list is attempted, and so on.
 *
 * If L1 has no sync then it has to perform FB/SB and
 * pass the bsic to GRR.
 */

typedef struct UmphReselectToGsmReqTag
{
    /** Band Mode
     * Band Mode to configure layer 1 too
     */
    BandMode    bandMode;

    /** Activate Now
     * Reselect to GSM immediately, ignore activation time
     */
    Boolean     now;

    /** Activate Time for reselection
     * The time to deactivate the 3G L1/PS
     */
    Int8        activationTime;

#if defined(ENABLE_OOS_HANDLING)
    Boolean     utranInOos;
#endif
#if defined(HL_LWG)
	Boolean 	isCellChangeOrder;
#endif

    /** Number of cells
     * THe number of valid cells contained
     * within the arfcn and bsic arrays.
     */

    Int8        numCells;
    /** Arfcn List
     * Arfcn list to reselect too
     */
#if defined(ENABLE_OOS_HANDLING)
    Arfcn       arfcn [MAX_BA_CHANNELS];
#else
    Arfcn       arfcn [UMAX_NUM_GSM_CELLS];
#endif
    /** bsic List
     * Bsic to be found on associated arfcn
     */
    Int8        bsic  [UMAX_NUM_GSM_CELLS];

	Boolean 			needSearchFullBand; 	   /* **********  When RRC OOS and start IRAT reselection to GSM, sometimes only need to search  carried GSM arfcns*/ 

}
UmphReselectToGsmReq;

/** Relesect to GSM Confirmation
 * This signal is sent from GSM L1 to GRR to indicate that
 * UmphReselectToGsmReq has been processed.
 *
 * If l1Error = SUCCESS, then this indicates that GSM L1
 * has been successfully activated and is currently performing
 * cell reselection.
 *
 * Otherwise, l1Error indicates an error in the reselection procedure
 */

typedef struct UmphReselectToGsmCnfTag
{
    L1Error     l1Error;
}
UmphReselectToGsmCnf;

/** Relesect to GSM Failure Request
 * This signal has no parameters and is sent by GRR
 * to GSM L1 so as to trigger off reactivation of 3G L1.
 * This signal is sent after the GSM stack has failed to
 * carry out a UMTS to GSM reselection or a Cell Change
 * Order To GSM.
 *
 * 3G L1 replies by sending signal UmphReselectToGsmFailCnf
 * to RRC once reactivation of 3G L1 has been completed
 */
typedef EmptySignal UmphReselectToGsmFailReq;

/** Relesect to GSM Failure Confirmation
 * This signal has no parameters and is sent by 3G L1 to
 * RRC in response to a UmphReselectToGsmFailReq once
 * reactivation of 3G L1 has been completed.
 */

typedef EmptySignal UmphReselectToGsmFailCnf;

 /** Relesect to UMTS Failure Request
 * This signal has no parameters and is sent by RRC to
 * 3G L1 so as to trigger off reactivation of GSM L1.
 *
 * This signal is sent after the 3G stack has failed to
 * carry out a GSM to UMTS reselection or a Cell Change
 * Order To UTRAN.
 *
 * GSM L1 replies by sending signal UmphReselectToUmtsFailCnf
 * to RRC once reactivation of GSM L1 has been completed
 */

typedef EmptySignal UmphReselectToUmtsFailReq;

/** Relesect to UMTS Failure Confirmation
 * This signal has no parameters and is sent by GSM L1 to RRC
 * in response to a UmphReselectToUmtsFailReq once reactivation
 * of GSM L1 has been completed
 */

typedef EmptySignal UmphReselectToUmtsFailCnf;

/** Switch RAT
 * These signals a used for Basic mode change
 * 
 *
 */
/** Switch RAT to UMTS Request
 * This signal is sent from GRR to GSM L1 to switch
 * control of L1 from GSM to 3G when GSM is currently
 * in control but is in NULL state.
 * GSM L1 replies by sending signal UmphSwitchRatCnf
 * to GRR once control is handed over
 */
typedef EmptySignal UmphSwitchRatReq;

/** Switch RAT to UMTS Confirmation
 * This signal is sent from L1 to GRR in response to
 * a UmphSwitchRatReq to confirm that control of L1
 * has been switched over to 3G from GSM
 */
typedef EmptySignal UmphSwitchRatCnf;

/**
 * These signals a used for Handover
 * and associated failure cases
 *
 */
/** Handover to GSM Request
 * This signal is sent from GRR to the GSM L1 to initiate a
 * handover from a UMTS cell to a GSM cell.  It contains the
 * information required to allow channel assignment and handover
 * messaging. This signal performs the combined tasks of MphImmAssignmentReq
 * (which allocates a channel and moves the mobile to Dedicated state) and
 * MphHandoverReq (which changes the allocated channel and initiates
 * handover signalling). When the startingTime of the handover has elapsed
 * and the mobile is handed over to the GSM dedicated channel,
 * L1 will return an MphHandoverStartInd.
 *
 * If the handover fails, L1 will return an MphHandoverCnf or
 * UmphHandoverToGsmCnf indicating the failure to the PS.
 *
 * If the handover succeeds, L1 will return an MphHandoverCnf indicating a success.
 *
 */
typedef struct UmphHandoverToGsmReqTag
{
    /** Indicates the bandmode associtated to the ARFCN and
     * network switching too for Layer 1 to configure itself
     */
    BandMode                    bandMode;
    /** Activate Now
     * Handover to GSM immediately, ignore activation time
     */
    Boolean                     now;
    /** The time to deactivate the 3G L1/PS */
    Int8                        activationTime;
    /** The Handover command itself, channel config etc */
    UHandoverFromUtranCommand   hoCommand;
}
UmphHandoverToGsmReq;

/** Handover to GSM Confirmation
 * This signal is sent from the GSM L1 to GRR to indicate that
 * the handover has been processed.
 *
 * If l1Error = SUCCESS, then this indicates that GSM L1 has
 * successfully started the handover process. This does not mean
 * the handover has been completed only that it has started.
 *
 * The signal MphHandoverCnf provides this information.
 *
 */
typedef struct UmphHandoverToGsmCnfTag
{
    /** Indicates if signal processed correctly or not and handover
     * proceeding */
    L1Error l1Error;
}
UmphHandoverToGsmCnf;

/** Handover to GSM Failure Indication
 * This signal is sent from GRR to GSM L1 to abort a UMTS to
 * GSM handover. On reception of this signal, L1 must attempt
 * to resume activity on the previous UMTS channel.
 *
 * This signal contains no parameters.
 */
typedef EmptySignal UmphHandoverToGsmFailReq;

/** Handover to GSM Failure Confirmation
 * This signal is sent from L1 to GRR in response to the
 * UmphHandoverToGsmFailReq. It indicates that L1 is attempting
 * to resume activities on the UMTS channel.
 *
 * This signal contains no parameters.
 */
typedef EmptySignal UmphHandoverToGsmFailCnf;

/** Handover to UMTS Failure Request
 * This signal is sent from RRC to the 3G L1 during the handover
 * to UMTS procedure. This signal is used to indicate that a handover
 * to UMTS has failed and that the MS should resume dedicated activities
 * on the previous GSM channel configuration. L1 must attempt to reinstate
 * all previous channel configurations.
 *
 * This signal contains no parameters.
 */

typedef EmptySignal UmphHandoverToUmtsFailReq;

/** Handover to UMTS Failure Confirmation
 *  This signal is sent from L1 to the RRC to indicate that the
 * UmphHandoverToUmtsFailReq has been processed.
 *
 * This signal contains no parameters
 */

typedef EmptySignal UmphHandoverToUmtsFailCnf;

#if !defined (L1_SUPPORT_GSM_FG_PLMN_SEARCH)
typedef EmptySignal UmphSwitchToGsmReq;
typedef EmptySignal UmphSwitchToGsmCnf;
typedef EmptySignal UmphSwitchToUmtsReq;
typedef EmptySignal UmphSwitchToUmtsCnf;
#endif
/** @} */

typedef struct UmphMeasReqTag
{
	Int8	searchSeqNum;
	Int8	numCells;
	Int16	uArfcn[UMAX_NUM_FDD_NCELLS];
	Int16	primaryScramblingCode[UMAX_NUM_FDD_NCELLS];
}
UmphMeasReq;


/*
** SIGNAL UmphMeasInd
**
** This signal is sent to return Utran Measurements in GSM mode
*/
typedef struct UmphMeasIndTag
{
	Int8		searchSeqNum;
	Int8		index;
	Boolean		lastMeas;
	Int8		numMeas;
	SignedInt16	cpich_Ec_N0[UMAX_NUM_FDD_NCELLS];
	SignedInt16	cpich_RSCP[UMAX_NUM_FDD_NCELLS];
}
UmphMeasInd;


#if defined (HL_LTG)

typedef struct UmphTdBchDecodeReqTag
{
	UINT16	uarfcn;
    UINT8 	sibControl;
    UINT8   cellParameter;
    Bool    sctd;
    Bool    tstd;
	UINT16  skipSfn;
}
UmphTdBchDecodeReq;


typedef struct UtranBchDecodeIndTag
{
	UINT16 	sfn;							/* SFN the block was received in (the last frame in the TTI)    */
	UINT16 	uarfcn;                         /* 	UARFCN                                          */
	UINT8 	cellParameter;                  /* MA              */
    FddDlPduStatus	status;			/* Good CRC or Bad CRC											*/
	UINT8 	frame[PLG_BCH_DATA_LEN];        /* BCH Block, PLG_BCCH_DATA_LEN = 31                            */
} UtranBchDecodeInd;

typedef struct UtranBchDecodeErrIndTag
{
	UINT16	uarfcn;                         /* 	UARFCN                                          */
	UINT8 	cellParameter;                 /*  MA                  */
	L1Error errorCode;                     	 /* L1_CANT_DECODE_SIB - error indicating that phisical
											  *	parameters are not valid or DRX period is too small  */
} UtranBchDecodeErrInd;


#elif defined HL_LWG
/** SIGNAL UmphWbBchDecodeReq
**
** This signal is sent to decode WB BCH in GSM IDLE mode
*/
typedef struct UmphWbBchDecodeReqTag
{
	UINT16	 uarfcn;
    UINT16 	scramblingCode;
    UINT8   sibControl;
    sttdInd_te   txDiversityOn;
    UINT16  skipSfn;
}
UmphWbBchDecodeReq;

typedef struct UtranBchDecodeIndTag
{
	UINT16 	sfn;							/* SFN the block was received in (the last frame in the TTI)    */
	UINT16 	uarfcn;                         /* (0..16383)	UARFCN                                          */
	UINT16 	scramblingCode;                 /* The Range is from 0 to 8176 with step of 16                  */
	#if defined (ENABLE_NETWORK_SCAN_REPORT)
	// [New_Feature]add by wpan for add EcNo and Rscp in bchRpt
	INT16  cpichEcNo;						/* add by wpan to rpt ecno of detect wb cell */
	INT16  cpichRscp;						/* add by wpan to rpt rscp of detect wb cell */
	#endif
	FddDlPduStatus          status;         /* Good or Bad CRC results */
	UINT8 	frame[PLG_BCH_DATA_LEN];        /* BCH Block, PLG_BCCH_DATA_LEN = 31                            */
} UtranBchDecodeInd;

typedef struct UtranBchDecodeErrIndTag
{
	UINT16	uarfcn;                         /* (0..16383)	UARFCN                                          */
	UINT16	scramblingCode;                  /* The Range is from 0 to 8176 with step of 16                  */
	L1Error errorCode;                     	 /* L1_CANT_DECODE_SIB - error indicating that phisical
											  *	parameters are not valid or DRX period is too small  */
} UtranBchDecodeErrInd;
#endif

#endif /* INTEL_UPGRADE_DUAL_RAT */

#endif
/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/3g.mod/lib/src/ubmcdec.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/26 20:17:00 $
 ***************************************************************************
 * File Description:
 **************************************************************************/
#if !defined(DS3_LTE_ONLY)

#if !defined (UBMCDEC_H)
#define       UBMCDEC_H

/****************************************************************************
 * Nested Include Files
 ****************************************************************************/

#if !defined (SYSTEM_H)
#   include  <system.h>
#endif

/***************************************************************************
 * Manifest Constants
 **************************************************************************/


/* constants which are used to define array sizes */

/****************************************************************************
 * Macros
 ****************************************************************************/


/***************************************************************************
 * Type Definitions
 **************************************************************************/

typedef enum ubmcMessageTypeTag
{
    CBS_MESSAGE = 1,
    SCHEDULE_MESSAGE = 2,
    CBS41_MESSAGE = 3,
    UNKNOWN_MESSAGE
} ubmcMessageType   ;

#define UBMC_HEADER_LENGTH 6

/***************************************************************************
 *  Prototypes
 **************************************************************************/

void UbmcSchdStateMachine (CbData *cbData);
Err CbStoreOneBlock (const CbUnpackedMessage       *cbUnpackedMessage,
                     const Int16                    element,
                     CbData                        *cbData);
void CbFreeAllMemory (CbData *cbData);
void CbFreeSpecifiedMemory (CbData *cbData, CbGeoScope scope);
Err CbMessageIsWanted (
    const CbUnpackedMessage    *header,
    CbData                     *cbData,
    Int16                      *elementReturned);

// start CQ00076406
void CbFreePrimaryEtwsSpecifiedMemory (CbData *cbData, CbGeoScope scope);

void CbFreeSecondaryEtwsSpecifiedMemory (CbData *cbData, CbGeoScope scope);
// end CQ00076406
#endif
/* END OF FILE */
#endif

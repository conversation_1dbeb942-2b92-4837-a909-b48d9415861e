#ifndef UT_ARRAY_LIST_H
#define UT_ARRAY_LIST_H

#include <system.h>

#define UT_ARRAY_LIST_NULL 0xff
#define UT_ARRAY_LIST_CONTAINS(arr_list_p, item) ((arr_list_p->next[item] != UT_ARRAY_LIST_NULL) || \
								(arr_list_p->tail == item))

//ICAT EXPORTED STRUCT
typedef struct UtArrayListTag
{
	Int8 head;
	Int8 tail;
	Int8 capacity;		/**< maximal possble size */
	Int8 size;			/**< actual size*/
	Int8 *next;
}UtArrayList;

Boolean UtArrayListCreate(UtArrayList *sIntList, Int8 size);
void UtArrayListDestroy(UtArrayList *sIntList);
Boolean UtArrayListAppendTail(UtArrayList *sIntList,Int8 newItem);
Boolean UtArrayListAppendHead(UtArrayList *sIntList,Int8 newItem);
Int8 UtArrayListRemoveHead(UtArrayList *sIntList);
void UtArrayListClone(UtArrayList *sNewIntList, UtArrayList *sExistedIntList);
Boolean UtArrayListRemoveItem(UtArrayList *sIntList,Int8 requestedItem);


#endif	//UT_ARRAY_LIST_H



/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/sys/gki.typ/api/cfg/testsigset.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/17 10:20:49 $
 **************************************************************************
 * File Description : Signal Base definitions for the  set.
 **************************************************************************/

/*
** This file deliberately does not have multiple inclusion protection
** as it can be legitimately included twice in the same file.
**
** The KI_BASE_DEF() macro adds _SIGNAL_BASE to the end of the parameter
** given as the base name. This is done to enforce the GKI naming convention.
**
** This must never be included directly by any file outside of the kernel.
**
** WARNING WARNING WARNING WARNING WARNING WARNING WARNING WARNING WARNING
**
** THE BASES WITHIN A SIGNAL SET SHOULD NOT BE CONDITIONALLY COMPILED
** IF POSSIBLE AS THIS CAN CAUSE SIGNAL ID'S TO MOVE AROUND. NEW BASES MUST
** BE ADDED TO THE END OF THE SET (OR USE A REDUNDANT ENTRY) TO MAINTAIN
** THE BASE NUMBERING FOR THE EXISTING BASES. IF A BASE IS CONDITIONALLY
** COMPILED THE ELSE CONDITION MUST HAVE RESERVED BASES SO THAT THE
** NUMBERING IS CONSTANT WHETHER THE CONDITIONAL IS ON OR NOT.
**
** FILES SHOULD NOT BE INCLUDED INTO THIS FILE AS ANY ADDITIONS TO THE BASES
** WITHIN THE INCLUDED FILE WILL AFFECT THE NUMBERING OF ALL BASES DEFINED
** AFTER THE INCLUSION IN THIS FILE.
**
** WARNING WARNING WARNING WARNING WARNING WARNING WARNING WARNING WARNING
*/

/***************************************************************************
** Test Signal Set
***************************************************************************/
KI_BASE_DEF (CA)      /* Coolart Signals exchanged between MCT and any MST/PST tasks */
KI_BASE_DEF (MST_MCT) /* Coolart Signals exchanged between MST and MCT */
KI_BASE_DEF (PST_MST) /* Coolart Signals exchanged between PST and MST */
KI_BASE_DEF (GPIB_NI) /* Coolart Signals to communicate with a test instrument using a National Instrument USB to GPIB converter.
                         Coolart Signals exchanged between GPIB Task and MST */
KI_BASE_DEF (ATI)     /* Coolart Signals exchanged between MST and ATI task. */
KI_BASE_DEF (BAMM)    /* Coolart Signals exchanged between MST and BAMM task */
KI_BASE_DEF (PWRCTRL) /* Coolart Signals exchanged between MST and PWRCTRL task */
KI_BASE_DEF (CA_UTIL) /* Signals exchanged between MST and CoolART Utility passthru task */
KI_BASE_DEF (LPT)     /* CoolArt Signals exchanged between MST and LPT task */



/*------------------------------------------------------------
(C) Copyright [2006-2010] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/******************************************************************************
 * File Name:	umaecpc.h
 *
 *
 * File Description: UMAC-E CPC header file
 *
 *****************************************************************************/

#if !defined (UMAECPC_H)
#define       UMAECPC_H

#if defined (UPGRADE_3G_EDCH)
/******************************************************************************
 * Include Files
 *****************************************************************************/
#include <stdio.h>
#include <string.h>
#include "gbl_types.h"
#include "pl_w_globs.h"
#include "pl_w_shared_memory.h"
#include "umc_sig.h"
#include "umac_sig.h"
#include "umautl.h"


/******************************************************************************
 * Constants
 *****************************************************************************/


/******************************************************************************
* Macros
*****************************************************************************/


/******************************************************************************
* Type Definitions
*****************************************************************************/

// CPC states of MAC-e
typedef enum  UmaECpcDtxCycleStateEnumTag
{
	UMACE_CPC_CYCLE_1_NOT_IN_DTX = 0,
	UMACE_CPC_CYCLE_2_NOT_IN_DTX,
	UMACE_CPC_CYCLE_1_IN_DTX,
	UMACE_CPC_CYCLE_2_IN_DTX,
	UMACE_CPC_NUM_STATES
} UmaECpcDtxCycleStateEnum;

/******************************************************************************
 * Function Prototypes
 *****************************************************************************/

Boolean UmaEdchCpcScheduleTx(UmacEConfiguration* config_p,Boolean isCmGapDetected);
Boolean UmaEdchCpcIsRetransmissionDisabled(UmacEConfiguration* config_p);
void UmaEdchCpcSetSharedMemoryOutputs(UmacEConfiguration* config_p);
void UmaEdchCpcReadSharedMemoryInputs(UmacEConfiguration* config_p);
void UmaEdchCpcReset(UmacEConfiguration* config_p,Boolean isNewConfiguration);
void UmaEdchCpcInit(void);
#if !defined (PS_L2_R8_API)
Boolean UmaEdchCpcIsErrorIndicatorSet(void);
#endif

#endif /* UPGRADE_3G_EDCH */
#endif /* UMAECPC_H */

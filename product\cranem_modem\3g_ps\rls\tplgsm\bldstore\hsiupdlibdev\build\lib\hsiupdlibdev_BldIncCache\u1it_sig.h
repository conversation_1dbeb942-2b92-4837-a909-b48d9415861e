/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/l1/3g.mod/pub/src/u1it_sig.h#6 $
 *   $Revision: #6 $
 *   $DateTime: 2007/03/30 13:53:25 $
 **************************************************************************
 * Group            : U1, Signals
 * File Description : Signal structures for the U1IT base (Layer 1 internal
 *                   signals).
 **************************************************************************/

#ifndef U1IT_SIG_H
#define U1IT_SIG_H

/*******************************************************************************
** Includes
*******************************************************************************/

#include <system.h>
#include <u1sytype.h>
#include <cphy_sig.h>
#include <uas_asn.h>
#include <u1nc_typ.h>
#include <u1mc_typ.h>
#include <u1sqtime.h>
#include <uphsystem.h>
#if !defined (UPGRADE_EXCLUDE_2G)
# if defined (DEVELOPMENT_VERSION)
#include <l1dlm_typ.h>
# endif
#endif
#include <uphpthctrlpam.h>
#include <u1_ads.h>
#include <u1sqpschafc.h>
#include <u1sqpschdet.h>

/* Get the headers required for logging of general structures */
#define U1IT_DEBUG_GEN_STRUCT_INC
#include <u1itgdbg.h>
#undef  U1IT_DEBUG_GEN_STRUCT_INC

/*******************************************************************************
** Constants
*******************************************************************************/

/*******************************************************************************
* Define      : U1IT_INT_TRACE_LIST_SIZE
* Group       : U1, Signals, IT
* Description : This gives the maximum number of entries in the traceEntries
*                   array member of the U1itIntTraceInd signal.
*******************************************************************************/
#define U1IT_INT_TRACE_LIST_SIZE  (65)

/*******************************************************************************
** Typedefs
*******************************************************************************/

/*******************************************************************************
* Typedef     : U1itPschAfcIndTag
* Group       : U1, Signals, IT
* Type        : structure
* Description : U1 P-SCH AFC convergence signal giving details of how the AFC
* convergence is proceeding.
*******************************************************************************/
typedef struct U1itPschAfcIndTag
{
    UUARFCN                arfcn;
    U1SqPschAfcMethod      method;
    U1SqPschAfcMethodData  methodData;

}U1itPschAfcInd;

/*******************************************************************************
* Typedef     : U1itPschDetectInd
* Group       : U1, Signals, IT
* Type        : structure
* Description : U1 P-SCH detect signal giving the number of paths detected
* and their powers/timings.
*******************************************************************************/
typedef struct U1itPschDetectIndTag
{
    UUARFCN                arfcn;
    Boolean                success;
    UphSysTimingSubChip2x  syTimeTriggerChip2x;
    Int16                  pathCount;
    Int16                  exponent;

    /** Count of paths in the following arrays. */
    Int16                  loggedPathCount;

    /** Timing for each path. */
    Int16                  pathTiming2x[UPH_CPS_PSC_NUM_BEST_PATHS];

    /** Power for each path.
     * \note This should be at the end of the structure to allow truncation
     *        according to the loggedPathCount.
     */
    Int16                  pathPowers[UPH_CPS_PSC_NUM_BEST_PATHS];

}U1itPschDetectInd;

typedef struct U1itCodeGroupDetectIndTag
{
    UUARFCN                arfcn;
    UphCpsCodeGroup        codeGroup;
    UphSysTimingChip       uphPathChipTiming;
    UphSysTimingSubChip2x  uphPathSubChipTiming2x;

}U1itCodeGroupDetectInd;

typedef struct U1itScramblingCodeIdentifyIndTag
{
    UUARFCN                arfcn;
    Boolean                codeIdentified;
    U1PathSubChipTiming2x  u1PathSubChipTiming2x;
    UPrimaryScramblingCode code;

}U1itScramblingCodeIdentifyInd;

typedef struct U1itScramblingCodeVerifyIndTag
{
    UUARFCN                arfcn;
    U1PathSubChipTiming2x  u1PathSubChipTiming2x;
    UPrimaryScramblingCode code;

}U1itScramblingCodeVerifyInd;

typedef struct U1itScramblingCodeVerifyFailIndTag
{
    UUARFCN                arfcn;
}U1itScramblingCodeVerifyFailInd;

typedef enum U1itRakePathInfoReasonTag
{
    U1IT_RAKE_PATH_INFO_REASON_ADD,
    U1IT_RAKE_PATH_INFO_REASON_DROP,
    U1IT_RAKE_PATH_INFO_REASON_ADVANCE,
    U1IT_RAKE_PATH_INFO_REASON_RETARD

} U1itRakePathInfoReason;

typedef struct U1itRakePathInfoIndTag
{
    U1itRakePathInfoReason u1itRakePathInfoReason;
    UphPthPathSetId  uphRakePathSetId;
    UphPthPathId     uphRakePathId;
    UphPthPathInfo   uphRakePathInfo;

}U1itRakePathInfoInd;

typedef struct U1itTimebaseAlignIndTag
{
    Int16               chipOffset;
    Int8                slotOffset;

}U1itTimebaseAlignInd;

typedef struct U1itTimebaseDlTrackIndTag
{
    U1SqTimebaseTrackCommand  command;
    Int32                     slotChipOffset;

}U1itTimebaseDlTrackInd;

typedef struct U1itTimebaseUlTrackIndTag
{
    U1SqTimebaseTrackCommand  command;

}U1itTimebaseUlTrackInd;

typedef struct U1itTimebasePathTrackIndTag
{
    SignedInt16         chipOffset;

}U1itTimebasePathTrackInd;

typedef struct U1itDlTfcsIndTag
{
    Int16               sfn;
    Int8                ccTrChIndex;
    U1TcDlTfcs          dlTfcs;

} U1itDlTfcsInd;

typedef struct U1itUlTrchIndTag
{
    Int16               sfn;
    Int8                numberOfTrchs;
    Int32               activeTrChs;
    U1TcUlTfs           tfs[UPS_MAX_UL_NO_OF_TRCH];

} U1itUlTrchInd;

typedef struct U1itDlTrchInfoTag
{
    UTransportChannelType               transportChannelType;
    UTransportChannelIdentity           transportChannelIdentity;
    SemiStaticTfData                    semiStaticTfData;
    UBLER_QualityValue                  blerQualityValue;

} U1itDlTrchInfo;

typedef struct U1itDlTrchIndTag
{
    Int16               sfn;
    Int8                ccTrChIndex;
    Int8                numberOfTrchs;
    Int32               activeTrChs;
    U1itDlTrchInfo      dlTrchInfo[UPS_MAX_DL_NO_OF_TRCH];

} U1itDlTrchInd;

typedef struct U1itIntraFreqCellInfoIndTag
{
    Int8                numberOfCells;
    Int32               cellIndices;
    U1NcUmtsCellData    cellData[U1_NC_MAX_NCELL_LIST_ENTRIES];

} U1itIntraFreqCellInfoInd;

typedef struct U1itInterFreqCellInfoIndTag
{
    Int8                numberOfCells;
    Int32               cellIndices;
    U1NcUmtsCellData    cellData[U1_NC_MAX_NCELL_LIST_ENTRIES];

} U1itInterFreqCellInfoInd;

#if !defined (UPGRADE_EXCLUDE_2G)
typedef struct U1itGsmCellInfoIndTag
{
    Int8                numberOfCells;
    Int32               cellIndices;
    U1NcGsmCellData     cellData[U1_NC_MAX_NCELL_LIST_ENTRIES];

} U1itGsmCellInfoInd;
#endif

typedef struct U1itBasicCellDataTag
{
    Int8 cellIndex;
    UPrimaryScramblingCode primaryScramblingCode;

} U1itBasicCellData;

typedef struct U1itCellPtrListIndTag
{
    UUARFCN             arfcn;
    U1McListType        listType;
    Int8                numberOfCells;
    U1itBasicCellData   cellData[U1_NC_MAX_NUM_MEAS_INTRA_FREQ_CELLS];

} U1itCellPtrListInd;

typedef struct U1itPhChReconfigIndTag
{
    Int8                cfn;
    Int8                reconfigType;

} U1itPhChReconfigInd;

typedef struct U1itTrChReconfigIndTag
{
    Int8                cfn;
    Int16               reconfigType;

} U1itTrChReconfigInd;

typedef struct U1itMeasOccasionIndTag
{
    Int16               currMeasOccnSfn;
    Int16               nextMeasOccnSfn;

} U1itMeasOccasionInd;

typedef struct U1itCbsOccasionIndTag
{
    Int16               cbsOccnSfn;

} U1itCbsOccasionInd;

typedef struct U1itTgpsInfoTag
{
    UTGPSI              tgpsi;
    Boolean             tgpsStatus;

}  U1itTgpsInfo;

typedef struct U1itTgpsIndTag
{
    U1itTgpsInfo        tgpsInfo[CPHY_MAX_ACTIVE_TGPS];

} U1itTgpsInd;

typedef struct U1itBasicTgInfoTag
{
    Int8                tgCfn;
    UTGPSI              tgpsi;
    Int16               tgCount;
    UTGSN               tgStartSlot;
    UTGL                tgLength;

} U1itBasicTgInfo;

typedef struct U1itTgSchedIndTag
{
    Int8                tgEventCfn;
    Int8                ttiStartCfn;
    Int8                ttiLength;
    Int8                numUlTgsInTti;
    U1itBasicTgInfo     basicUlTgInfo[U1_PHCH_MAX_NUM_TG_IN_TTI];
    Int8                numDlTgsInTti;
    U1itBasicTgInfo     basicDlTgInfo[U1_PHCH_MAX_NUM_TG_IN_TTI];

} U1itTgSchedInd;

typedef struct U1itDebug1IndTag
{
    Int16         value;
} U1itDebug1Ind;

typedef struct U1itDebug2IndTag
{
    Int16         value;
} U1itDebug2Ind;

typedef struct U1itDebug3IndTag
{
    Int16         value;
} U1itDebug3Ind;

typedef struct U1itDebug4IndTag
{
    Int16         value;
} U1itDebug4Ind;


typedef enum U1itDebugGenStructTypeTag
{
    #define U1IT_DEBUG_GEN_STRUCT_DEF(x) x##U1itDebugType,
    #include <u1itgdbg.h>
    #undef  U1IT_DEBUG_GEN_STRUCT_DEF

    NUM_U1IT_DEBUG_GEN_STRUCT_TYPE
}
U1itDebugGenStructType;

typedef union U1itDebugGenStructTag
{
    #define U1IT_DEBUG_GEN_STRUCT_DEF(x) x x##Val;
    #include <u1itgdbg.h>
    #undef  U1IT_DEBUG_GEN_STRUCT_DEF
}
U1itDebugGenStruct;

/** Message used to log general structures for debugging.
 * This structure contains an enumerated type together with a union that
 * allows one of a number of different structures to be logged.  The
 * unions can be trivially extended by adding definitions to u1itgdbg.h.
 */
typedef struct U1itDebugGenStructIndTag
{
    U1itDebugGenStructType type;       /**< Name of the structure stored in \a s. */
    Int32                  totalSize;  /**< Total size of this message in bytes. */
    U1itDebugGenStruct     s;          /**< Union of structures that can be logged. */
}
U1itDebugGenStructInd;

typedef struct U1itPichIndTag
{
    Int16                  result;
    UUARFCN                arfcn;
    UPrimaryScramblingCode primaryScramblingCode;
}
U1itPichInd;

typedef U1itPichInd U1itAichInd;

typedef struct U1itPduListIndTag
{
    Int8                   pduData[20];
    UDlPduStatus           pduStatus;
    Int8                   numberOfTransportBlocks;
} U1itPduListInd;

typedef struct U1itMpsPamIndTag
{
    U1NcUmtsCellData                cellInfo;
    U1NcUmtsMeasCellData            measData;
    UphPthRakeTopLevelInfo          uphRakeTopLevelInfo;
    UphPthPamAds                    uphPamAds;
    UphPthCtrlPamExecuteComChCmdListData  pamCmdList;
} U1itMpsPamInd;

typedef struct U1itTrChOutIndTag
{
    UTransportChannelType           transportChannelType;
    UTransportChannelIdentity       transportChannelIdentity;
    Int16                           numberOfReceivedTrBlk;
    Int16                           numberOfReceivedTrBlkBadCrc;
    Int16                           crcBitMask;
    Int8                            cfn;
    Int16                           transportBlockLength[UPH_SYS_MAX_FRAMES_PER_TTI];
} U1itTrChOutInd;

typedef struct U1itTpcIndTag
{
    Int16   tpcBitCountDown;
    Int16   tpcBitCountUp;
    Int16   tpcBitBuffer[UPH_SYS_MAX_FRAMES_PER_TTI];
} U1itTpcInd;

typedef struct U1itAfcIndTag
{
    Int16   dacValue;
    SignedInt16   currentPpb;
    Int32   variancePerSlot;
    Int16   numSlotsRun;
} U1itAfcInd;

typedef struct U1itSimSlowClockWakeupIndTag
{
    Int32       actualSlots;
    SignedInt32 errChips;
    SignedInt32 errFClks;

} U1itSimSlowClockWakeupInd;

/*******************************************************************************
* Typedef     : U1itPchSchedInd
* Group       : U1IT messages
* Type        : structure
* Description : This message structure is used to indicate the information used
*                   when scheduling a PICH/PCH reception.
*******************************************************************************/
typedef struct U1itPchSchedIndTag
{
    /* Member: Int8 piIndex = This is the paging indicator index within the
    **             PICH frame (called q in the specs). */
    Int8        piIndex;
    /* Member: Int16 pichSfn = This is the SFN of the PICH frame with our PI in
    **             it. */
    Int16       pichSfn;
    /* Member: SignedInt32 piChipOffset = This is the chip offset between the
    **             PCCPCH frame and the start of our PI within the PICH frame. */
    SignedInt32 piChipOffset;
    /* Member: Int16 pichStartSfn = This is the actual PCCPCH aligned SFN within
    **             which our PI falls. */
    Int16       pichStartSfn;
    /* Member: Int8 pichStartSlot = This is the actual PCCPCH aligned slot
    **             within which our PI starts. */
    Int8        pichStartSlot;
    /* Member: Int8 pichNumSlots = This is the number of PCCPCH aligned slots
    **             that our PI intersects. */
    Int8        pichNumSlots;
    /* Member: Int16 pchStartSfn = This is the actual PCCPCH aligned SFN within
    **             which the PCH falls. */
    Int16       pchStartSfn;
    /* Member: Int8 pchStartSlot = This is the actual PCCPCH aligned slot
    **             within which our PCH starts. */
    Int8        pchStartSlot;
    /* Member: Int8 pchNumSlots = This is the number of PCCPCH aligned slots
    **             that our PCH frame intersects. */
    Int8        pchNumSlots;

} U1itPchSchedInd;

/*******************************************************************************
* Typedef     : U1itRachSchedInd
* Group       : U1IT messages
* Type        : structure
* Description : This message structure is used to indicate the information used
*                   when scheduling a RACH transmission.
*******************************************************************************/
typedef struct U1itRachSchedIndTag
{
    /* Member: Int16 rachSfn = This is the SFN in which the RACH process starts*/
    Int16               accessSlotSetSfn;

    /* Member: Int8 ulAccessSlot*/
    Int8                ulAccessSlot;

    /* Member: Int16 preambleSfn = SFN in which the preamble occurs */
    Int16               preambleSfn;

    /* Member: Int8 preambleSslot = Slot in which the preamble occurs */
    Int8                preambleSlot;

    /* Member: Int8 preambleSignature */
    Int8                preambleSignature;

    /* Member: SignedInt16  preambleTxPowerdBm = Initial preamble power */
    SignedInt16         preambleTxPowerdBm;

} U1itRachSchedInd;

typedef struct U1itMpsSuccessIndTag
{
    Int8                    cellIndex;
    UUARFCN                 arfcn;
    UPrimaryScramblingCode  primaryScramblingCode;
    Int16                   lastMidPointChips;
    UphSysTimingChip        firstPathChipTiming;
    UphSysTimingSubChip2x   firstPathSubChipTiming2x;
    Int16                   delaySpread;

} U1itMpsSuccessInd;

typedef struct U1itMpsFailIndTag
{
    Int8                    cellIndex;
    UUARFCN                 arfcn;
    UPrimaryScramblingCode  primaryScramblingCode;
    Int16                   lastMidPointChips;
    Int16                   lastSuccessTimeStamp;

} U1itMpsFailInd;

typedef struct U1itSfnDetectSuccessIndTag
{
    Int8                    cellIndex;
    UUARFCN                 arfcn;
    UPrimaryScramblingCode  primaryScramblingCode;
    Int16                   sfnFrameOffset;

} U1itSfnDetectSuccessInd;

typedef struct U1itSfnDetectFailIndTag
{
    Int8                    cellIndex;
    UUARFCN                 arfcn;
    UPrimaryScramblingCode  primaryScramblingCode;
    Int8                    sfnRetriesCounter;

} U1itSfnDetectFailInd;

#if !defined (UPGRADE_EXCLUDE_2G)
# if defined (DEVELOPMENT_VERSION)
/*******************************************************************************
* Typedef     : U1itL1DlmGsmL1InitInd
* Group       : U1, GsmIn3g, L1Dlm Interface
* Type        : Structure
* Description : GSM Layer1 initialisation information.
*******************************************************************************/
typedef struct U1itL1DlmGsmL1InitIndTag
{
    /* Member: U1DlmUmtsState newState = Current 3G L1 state. */
    U1DlmUmtsState newState;

} U1itL1DlmGsmL1InitInd;
# endif
#endif

#if !defined (UPGRADE_EXCLUDE_2G)
# if defined (DEVELOPMENT_VERSION)
/*******************************************************************************
* Typedef     : U1itL1DlmGsmBaListDataInd
* Group       : U1, GsmIn3g, L1Dlm Interface
* Type        : Structure
* Description : GSM BA list information.
*******************************************************************************/
typedef U1DlmGsmBaListData U1itL1DlmGsmBaListDataInd;
# endif
#endif

#if !defined (UPGRADE_EXCLUDE_2G)
# if defined (DEVELOPMENT_VERSION)
/*******************************************************************************
* Typedef     : U1itL1DlmGsmBsicDecodeListInd
* Group       : U1, GsmIn3g, L1Dlm Interface
* Type        : Structure
* Description : GSM BSIC decode list information.
*******************************************************************************/
typedef U1DlmGsmBsicDecodeListData U1itL1DlmGsmBsicDecodeListInd;
# endif
#endif

#if !defined (UPGRADE_EXCLUDE_2G)
# if defined (DEVELOPMENT_VERSION)
/*******************************************************************************
* Typedef     : U1itL1DlmDchGsmMeasStartInd
* Group       : U1, GsmIn3g, L1Dlm Interface
* Type        : Structure
* Description : GSM measurement compressed mode gap pattern start information.
*******************************************************************************/
typedef struct U1itL1DlmDchGsmMeasStartIndTag
{
    /* Member: U1DlmGsmCellDchMonAct purpose = GSM Measurement type to be
     * performed within a CM gap allocated by 3G L1. */
    U1DlmGsmCellDchMonAct purpose;

} U1itL1DlmDchGsmMeasStartInd;
# endif
#endif

#if !defined (UPGRADE_EXCLUDE_2G)
# if defined (DEVELOPMENT_VERSION)
/*******************************************************************************
* Typedef     : U1itL1DlmDchGsmMeasStopInd
* Group       : U1, GsmIn3g, L1Dlm Interface
* Type        : Structure
* Description : GSM measurement compressed mode gap pattern stop information.
*******************************************************************************/
typedef struct U1itL1DlmDchGsmMeasStopIndTag
{
    /* Member: U1DlmGsmCellDchMonAct purpose = GSM Measurement type to be
     * performed within a CM gap allocated by 3G L1. */
    U1DlmGsmCellDchMonAct purpose;

} U1itL1DlmDchGsmMeasStopInd;
# endif
#endif

#if !defined (UPGRADE_EXCLUDE_2G)
# if defined (DEVELOPMENT_VERSION)
/*******************************************************************************
* Typedef     : U1itL1DlmDchGsmMeasPeriodSttInd
* Group       : U1, GsmIn3g, L1Dlm Interface
* Type        : Structure
* Description : GSM RSSI Measurement Period Information.
*******************************************************************************/
typedef U1DlmGsmRssiMeasPeriodInfo U1itL1DlmDchGsmMeasPeriodSttInd;
# endif
#endif

#if !defined (UPGRADE_EXCLUDE_2G)
# if defined (DEVELOPMENT_VERSION)
/*******************************************************************************
* Typedef     : U1itL1DlmCmGapInfoInd
* Group       : U1, GsmIn3g, L1Dlm Interface
* Type        : Structure
* Description : Compressed mode gap parameters for GSM.
*******************************************************************************/
typedef struct U1itL1DlmCmGapInfoIndTag
{
    /* Member: U1DlmGsmCellDchMonAct gapPurpose = GSM Measurement type to be
     * performed within a CM gap allocated by 3G L1. */
    U1DlmGsmCellDchMonAct gapPurpose;
    /* Member: Int8  gapUmtsStartCfn = 3G Connection Frame Number for the
     * frame in which the Compressed Mode gap starts. */
    Int8 gapUmtsStartCfn;
    /* Member: Int8 gapUmtsStartSlot = Number of start slots. */
    Int8 gapUmtsStartSlot;
    /* Member: Int8 gapLengthInUmtsSlots = Period available expressed in UMTS slots. */
    Int8 gapLengthInUmtsSlots;
    /* Member: L1Time gapStartTime = Gap start time according to the GSM arbitrary timebase */
    L1Time   gapStartTime;
    /* Member: Int32 gapLengthQbits = Gap length in Qbits */
    Int32    gapLengthQbits;

} U1itL1DlmCmGapInfoInd;
# endif
#endif

#if !defined (UPGRADE_EXCLUDE_2G)
# if defined (DEVELOPMENT_VERSION)
/*******************************************************************************
* Typedef     : U1itU1DlmMoGapInfoInd
* Group       : U1, GsmIn3g, L1Dlm Interface
* Type        : Structure
* Description : SFN for the 3G serving cell for the first frame in the
*               Measurement Occasion.
*******************************************************************************/
typedef Int16 U1itU1DlmMoGapInfoInd;
# endif
#endif

#if !defined (UPGRADE_EXCLUDE_2G)
# if defined (DEVELOPMENT_VERSION)
/*******************************************************************************
* Typedef     : U1itU1DlmFachGsmMeasSttInd
* Group       : U1, GsmIn3g, L1Dlm Interface
* Type        : Structure
* Description : Information for initiating GSM measurement activity in Cell_FACH.
*******************************************************************************/
typedef struct U1itU1DlmFachGsmMeasSttIndTag
{
    /* Member: Int8 tMeas = As defined in 25.133, *******. Expressed in number
       of 3G frames. Supported values as in tables 8.13 and 8.14 in 25.133.*/
    Int8 tMeas;
    /* Member: Int8 nTTI = nTTI is the number of frames in each measurement
       occasion, equal to the length of the largest TTI on the S-CCPCH
       monitored by the UE. Supported values: 1, 2, 4 and 8. */
    Int8 nTTI;
} U1itU1DlmFachGsmMeasSttInd;
# endif
#endif

#if !defined (UPGRADE_EXCLUDE_2G)
# if defined (DEVELOPMENT_VERSION)
/*******************************************************************************
* Typedef     : U1itU1DlmFachGsmMeasPeriodInd
* Group       : U1, GsmIn3g, L1Dlm Interface
* Type        : Structure
* Description : Number of MOs in the Cell_FACH measurement period.
*******************************************************************************/
typedef Int8 U1itU1DlmFachGsmMeasPeriodInd;
# endif
#endif

#if !defined (UPGRADE_EXCLUDE_2G)
# if defined (DEVELOPMENT_VERSION)
/*******************************************************************************
* Typedef     : U1itU1DlmGsmOptionalGapInd
* Group       : U1, GsmIn3g, L1Dlm Interface
* Type        : Structure
* Description : This structure contains data related to a time period over
* which for GSM monitoring may take place during 3G paging states.
*******************************************************************************/
typedef struct U1itU1DlmGsmOptionalGapIndTag
{
    /* Member: U1DlmOptionalGap optGap = Pointer to a structure containing information about
               upcoming radio gap that GSM L1 may decide to use. */
    U1DlmOptionalGap optGap;
    /* Member: L1DlmGapResponse response = Whether GSM L1 requires any of the radio gaps. */
    L1DlmGapResponse response;

} U1itU1DlmGsmOptionalGapInd;
# endif
#endif

#if !defined (UPGRADE_EXCLUDE_2G)
# if defined (DEVELOPMENT_VERSION)
/*******************************************************************************
* Typedef     : U1itU1DlmGsmOptionalGapStartInd
* Group       : U1, GsmIn3g, L1Dlm Interface
* Type        : Structure
* Description : This structure contains data related to a time period over
* which for GSM monitoring may take place during 3G paging states.
*******************************************************************************/
typedef U1DlmOptionalGap U1itU1DlmGsmOptionalGapStartInd;
# endif
#endif

#if !defined (UPGRADE_EXCLUDE_2G)
# if defined (DEVELOPMENT_VERSION)
/*******************************************************************************
* Typedef     : U1itU1DlmPagingGsmMeasStartInd
* Group       : U1, GsmIn3g, L1Dlm Interface
* Type        : Structure
* Description : drxCycleLength.
*******************************************************************************/
typedef Int16 U1itU1DlmPagingGsmMeasStartInd;
# endif
#endif

#if !defined (UPGRADE_EXCLUDE_2G)
# if defined (DEVELOPMENT_VERSION)
/*******************************************************************************
* Typedef     : U1itL1DlmGsmOptGapCompInd
* Group       : U1, GsmIn3g, L1Dlm Interface
* Type        : Structure
* Description : Gap ID.
*******************************************************************************/
typedef Int8 U1itL1DlmGsmOptGapCompInd;
# endif
#endif

#if !defined (UPGRADE_EXCLUDE_2G)
# if defined (DEVELOPMENT_VERSION)
/*******************************************************************************
* Typedef     : U1itL1TstGsmNcellDebugInd
* Group       : U1, GsmIn3g, Debug
* Type        : Structure
* Description : GSM Neighbour Cell debug.
*******************************************************************************/
typedef struct U1itL1TstGsmNcellDebugIndTag
{
  CellStatusUpdateReason  reason;
  CellInformation         cellInfo;
}
U1itL1TstGsmNcellDebugInd;
# endif
#endif

#if !defined (UPGRADE_EXCLUDE_2G)
# if defined (DEVELOPMENT_VERSION)
/*******************************************************************************
* Typedef     : U1itL1DlmGsmRssiDataInd
* Group       : U1, GsmIn3g, L1Dlm Interface
* Type        : Structure
* Description : GSM RSSI results.
*******************************************************************************/
typedef L1DlmGsmRssiData U1itL1DlmGsmRssiDataInd;
# endif
#endif

#if !defined (UPGRADE_EXCLUDE_2G)
# if defined (DEVELOPMENT_VERSION)
/*******************************************************************************
* Typedef     : U1itGsmFbSbSeqSttInd
* Group       : U1, GsmIn3g, L1Dlm Interface
* Type        : Structure
* Description : GSM FB/SB scheduler results structure.
*******************************************************************************/
typedef MmxBsicSeqResults U1itGsmFbSbSeqSttInd;
# endif
#endif

#if !defined (UPGRADE_EXCLUDE_2G)
# if defined (DEVELOPMENT_VERSION)
/*******************************************************************************
* Typedef     : U1itGsmFbSbSeqEndInd
* Group       : U1, GsmIn3g, L1Dlm Interface
* Type        : Structure
* Description : GSM FB/SB scheduler results structure.
*******************************************************************************/
typedef MmxBsicSeqResults U1itGsmFbSbSeqEndInd;
# endif
#endif

#if !defined (UPGRADE_EXCLUDE_2G)
# if defined (DEVELOPMENT_VERSION)
/*******************************************************************************
* Typedef     : U1itL1DlmGsmBsicDataInd
* Group       : U1, GsmIn3g, L1Dlm Interface
* Type        : Structure
* Description : GSM BSIC decode results.
*******************************************************************************/
typedef L1DlmGsmBsicData U1itL1DlmGsmBsicDataInd;
# endif

/*******************************************************************************
* Typedef     : U1itModeChangeState
* Group       : U1, Interface Handler
* Type        : Enum
* Description : Describes change mode request
*******************************************************************************/
typedef enum U1itModeChangeStateTag
{
    U1IT_NEW_MODE_GSM,
    U1IT_NEW_MODE_3G
} U1itModeChangeState;

/*******************************************************************************
* Typedef     : U1IhInternalState
* Group       : U1, Interface Handler
* Type        : Structure
* Description : Describes U1Ih Internal State
*******************************************************************************/
typedef struct U1itModeChangeIndTag
{
    U1itModeChangeState newMode;
} U1itModeChangeInd;
#endif

/*******************************************************************************
* Typedef     : U1itDebug3ParamInd
* Group       : U1, Signals, IT
* Type        : U1Debug3ParamAds
* Description : This signal structure is used to provide a simple
*                   mechanism for dumping 3 Int32 parameters from one processor
*                   to another. This is used by the M_U1SyDbg3Param macro.
*******************************************************************************/
typedef U1Debug3ParamAds U1itDebug3ParamInd;

/*******************************************************************************
* Typedef     : U1itDebug6ParamInd
* Group       : U1, Signals, IT
* Type        : U1Debug6ParamAds
* Description : This signal structure is used to provide a simple
*                   mechanism for dumping 6 Int32 parameters from one processor
*                   to another. This is used by the M_U1SyDbg3Param macro.
*******************************************************************************/
typedef U1Debug6ParamAds U1itDebug6ParamInd;

 /*******************************************************************************
* Typedef     : U1itBchSchedInd
* Group       : U1, Signals, IT
* Type        : structure
* Description : This signal structure is used to indicate the information used
*                   when scheduling BCH reception.
*******************************************************************************/
typedef struct U1itBchSchedIndTag
{
    UUARFCN                 arfcn;
    UPrimaryScramblingCode  primaryScramblingCode;
    Int16                   superSfn;
    Int32                   systemChips;
    UphSysTimingChip        firstPathChips;
    Int16                   offsetSlots;
    Int16                   sfnFrameOffset;
    Int16                   targetSfn;

} U1itBchSchedInd;

 /*******************************************************************************
* Typedef     : U1itBadCrcInd
* Group       : U1, Signals, IT
* Type        : structure
* Description : This signal structure is used to indicate that a bad CRC has been
*               received.
*******************************************************************************/
typedef struct U1itBadCrcIndTag
{
    UTransportChannelType           transportChannelType;
    UTransportChannelIdentity       transportChannelIdentity;
    Int16                           numberOfBadCrcs;

} U1itBadCrcInd;

/*******************************************************************************
* Typedef     : U1itTfciFailInd
* Group       : U1, Signals, IT
* Type        : structure
* Description : This signal structure is used to indicate that TFCI has failed
*******************************************************************************/
typedef struct U1itTfciFailIndTag
{
    Int16   cfn;

} U1itTfciFailInd;

/*******************************************************************************
* Typedef     : U1itTxOnInd
* Group       : U1, Signals, IT
* Type        : structure
* Description : This signal structure is used to indicate that TX is enabled
*******************************************************************************/
typedef struct U1itTxOnIndTag
{
    Int16   cfn;

} U1itTxOnInd;

/*******************************************************************************
* Typedef     : U1itTxOffInd
* Group       : U1, Signals, IT
* Type        : structure
* Description : This signal structure is used to indicate that TX is disabled
*******************************************************************************/
typedef struct U1itTxOffIndTag
{
    Int16   cfn;

} U1itTxOffInd;

/*******************************************************************************
* Typedef     : U1itIntTraceInd
* Group       : U1, Signals, IT
* Type        : structure
* Description : This signal is used to send Interrupt Trace debug information
*                   so that it can be logged.
*******************************************************************************/
typedef struct U1itIntTraceIndTag
{
    /* Member: Int16 sequenceNumber = This is value is incremented by one for
    **             every L1TstTraceInd signal that is sent. This is used to
    **             detect errors in logging and to be able to work around
    **             these missing signals to prevent misleading data. */
    Int16           sequenceNumber;
    /* Member: Int16 validEntries = This indicates the number of valid entries
    **             in the traceEntries array member. */
    Int16           validEntries;
    /* Member: Int32 traceEntries[U1IT_INT_TRACE_LIST_SIZE] = This is a list
    **             interrupt trace data. The format of the data may be platform
    **             specific. */
    Int32           traceEntries[U1IT_INT_TRACE_LIST_SIZE];
}
U1itIntTraceInd;

/** Logging of downlink PDU statuses.
 * This signal logs the statuses of PDUs sent in UphyDataInds.
 */
typedef struct U1itPhyDlDataStatusIndTag
{
    /** The number of elements in \a pduInfo[] that are populated. */
    Int8           numberOfTransportBlocks;

    /** Array giving PDU status information. */
    UDlPduListInfo pduListInfo[1];
}
U1itPhyDlDataStatusInd;

/** Logging of uplink PDU statuses.
 * This signal logs the statuses of PDUs sent in UphyDataReqs.
 */
typedef struct U1itPhyUlDataStatusIndTag
{
    /** The number of elements in \a pduInfo[] that are populated. */
    Int8           numberOfTransportBlocks;

    /** Array giving PDU status information. */
    UUlPduListInfo pduListInfo[1];
}
U1itPhyUlDataStatusInd;

/** Alarm cause identifiers.
 * Used with the \a U1itAlarmInd, this allows different alarm conditions
 * to be noted.
 */
typedef enum U1itAlarmCauseTag
{
    U1IT_ALARM_CPICH_VE_MAX_RESULTS_REACHED,
    U1IT_ALARM_CPICH_ID_CELL_LIST_FULL,

    NUM_U1IT_ALARM_CAUSES
}
U1itAlarmCause;

/** General alarm for non-fatal conditions.
 * This provides visibility for conditions or behaviours that may be
 * unexpected or require attention, but that are not necessarily indicative
 * of an outright error.  Because of this, the signal does not replace any of the
 * assertion macros and is not intended as an alternative.
 */
typedef struct U1itAlarmIndTag
{
    U1itAlarmCause  cause;  /**< Cause of the alarm. */
    Int32           v1;     /**< Alarm specific debug value 1. */
    Int32           v2;     /**< Alarm specific debug value 2. */
    Int32           v3;     /**< Alarm specific debug value 3. */
}
U1itAlarmInd;

/*******************************************************************************
** Global Data
*******************************************************************************/

/*******************************************************************************
** Global Function Prototypes
*******************************************************************************/

#endif
/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/3g.mod/lib/src/umm_sim.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2006/12/15 14:47:38 $
 **************************************************************************
 * File Description :
 *
 * Contains declarations for functions in umm_sim.c
 **************************************************************************/

#if !defined (UMM_SIM_H)
#define       UMM_SIM_H

/***************************************************************************
 * Nested Include Files
 **************************************************************************/


#include <system.h>

#if defined (UPGRADE_3G)
extern void MmSendMmsiStoreLocationInfoReq (MobilityManagerEntity *mm);
extern void MmGmmSimReadDataCnf (MobilityManagerEntity *mm);
extern void GmmSimWriteReq(MobilityManagerEntity *mm);
extern void MmCheckTinWithReadSimData(MobilityManagerEntity *mm);
#endif

#endif

/* END OF FILE */

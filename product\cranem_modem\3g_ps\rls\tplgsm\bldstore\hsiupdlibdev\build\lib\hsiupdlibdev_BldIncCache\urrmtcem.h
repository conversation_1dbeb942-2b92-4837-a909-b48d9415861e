/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/api/inc/urrmtcem.h#3 $
 *   $Revision: #3 $
 *   $DateTime: 2007/03/19 13:59:30 $
 **************************************************************************
 * File Description:
 *
 * Header file containing 3G engineering mode constants, macros,
 * structure/datatype definitions which can be exported outside the
 * RRC.
 *
 * Do not add datatypes, function declarations etc., that would result
 * in the export of RRC specific stuff outside the RRC.
 **************************************************************************/

#if !defined (URRMTCEM_H)
#define       URRMTCEM_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <sir_typ.h>        /* NAS CellId, Mcc, Mnc, Lai, Rai */
#include <l13_typ.h>        /* Band enum */
#include <grrmrtyp.h>       /* GprsNetworkMode */
#include <rrc_sig.h>        /* UEngModeInfo */

/***************************************************************************
*   Manifest Constants
***************************************************************************/

/* Engineering mode info */
extern struct UEngModeInfoTag gEngModeInfo;


/* Engineering mode control uses a 32-bit control field to turn
 * specific eng. mode information on and off.
 *
 * Bits 1 to 2 contain general engineering mode control ....
 * (based on equivalent NAS/GRR bit flags defined in
 * psinc\grrmtyp.h)
 */

#define UENG_INFO_DISABLED          0x00000000
#define UENG_INFO_CONTINUOUS        0x00000002
#define UENG_INFO_SINGLE_SHOT       0x00000004

#define UENG_INFO_ON_OFF_MASK       0x00000006


/* Bits 3 to 12 define a reporting period in seconds.
 * A zero value will indicate that the current reporting
 * period is to be left unchanged
 */

#define UENG_INFO_REPORTING_PERIOD_BIT_SHIFT    3
#define UENG_INFO_REPORTING_PERIOD_MASK         0x00001ff8


/* Bits 13 - 17 contain RRC mode/state specific control e.g
 * for which RRC mode/states do we want engineering mode
 * information (probably all)
 * IDLE, URA_PCH, CELL_PCH, CELL_FACH, CELL_DCH.
 *
 * Excludes RRC DEACTIVATED and SUSPENDED modes
 * (signals will be sent out, but will not contain
 * and valid information apart from the RRC mode/state)
 */
#define UENG_INFO_IDLE_MODE             0x00002000
#define UENG_INFO_CONNECTED_URA_PCH     0x00004000
#define UENG_INFO_CONNECTED_CELL_PCH    0x00008000
#define UENG_INFO_CONNECTED_CELL_FACH   0x00010000
#define UENG_INFO_CONNECTED_CELL_DCH    0x00020000

#define UENG_INFO_CONNECTED_STATES     (UENG_INFO_CONNECTED_URA_PCH   | \
                                        UENG_INFO_CONNECTED_CELL_PCH  | \
                                        UENG_INFO_CONNECTED_CELL_FACH | \
                                        UENG_INFO_CONNECTED_CELL_DCH)

#define UENG_INFO_ALL_MODE_STATES      (UENG_INFO_CONNECTED_STATES | \
                                        UENG_INFO_IDLE_MODE)

#define UENG_INFO_MODE_STATE_MASK       UENG_INFO_ALL_MODE_STATES


/* Bits 18 to 22 identify the information to be returned */
#define UENG_INFO_UE_OPERATION          0x00040000
#define UENG_INFO_SERVING_CELL          0x00080000
#define UENG_INFO_INTRA_FREQ_CELLS      0x00100000
#define UENG_INFO_INTER_FREQ_CELLS      0x00200000
#define UENG_INFO_INTER_RAT_GSM_CELLS   0x00400000
#define UENG_INFO_ACTIVE_SET_CELLS          0x00800000


#define UENG_INFO_ALL_DATA             (UENG_INFO_UE_OPERATION     | \
                                        UENG_INFO_SERVING_CELL     | \
                                        UENG_INFO_INTRA_FREQ_CELLS | \
                                        UENG_INFO_INTER_FREQ_CELLS | \
                                        UENG_INFO_ACTIVE_SET_CELLS | \
                                        UENG_INFO_INTER_RAT_GSM_CELLS)

#define UENG_INFO_DATA_MASK             UENG_INFO_ALL_DATA



#define UENG_DEFAULT_REPORTING_PERIOD_SECONDS      1


/* Constants used to flag invalid values
 * in the engineering mode structures */
#define UENG_INVALID_SERVING_CELL_ID                4000000000UL /* Int32 ID */
#define UENG_INVALID_CELL_ID                        0xff         /* Int8 ID  */

#define UENG_INVALID_MCC                            0xffff
#define UENG_INVALID_MNC                            0xffff
#define UENG_INVALID_LAC                            0xfffe
#define UENG_INVALID_RAC                            0x00u

#define UENG_INVALID_ARFCN                          0xffff
#define UENG_INVALID_PRIMARY_SCRAMBLING_CODE        0xffff
#define UENG_INVALID_URA_ID                         0xffff
#define UENG_INVALID_DRX_CYCLE_LENGTH               0xffff

#define UENG_INVALID_NUMBER_OF_RADIO_LINKS          0xff
#define UENG_INVALID_SRNC_IDENTITY                  0xffff
#define UENG_INVALID_S_RNTI                         0xffffffff

#define UENG_INVALID_TX_POWER                       ((SignedInt16) 0x8000)  /* Min SignedInt16 value */
#define UENG_INVALID_RSSI                           ((SignedInt16) 0x8000)

#define UENG_INVALID_REF_TIME_DIFFERENCE_TO_CELL    0xffff

#define UENG_INVALID_CPICH_RSCP                     ((SignedInt16) 0x8000)
#define UENG_INVALID_CPICH_EC_N0                    ((SignedInt16) 0x8000)
#define UENG_INVALID_UTRA_CARRIER_RSSI              ((SignedInt16) 0x8000)
#define UENG_INVALID_PATHLOSS                       ((SignedInt16) 0x8000)
#define UENG_INVALID_QUAL                           ((SignedInt16) 0x8000)
#define UENG_INVALID_RXLEV                          ((SignedInt16) 0x8000)

#define UENG_INVALID_GSM_CARRIER_RSSI               ((SignedInt16) 0x8000)
#define UENG_INVALID_BSIC_NCC                       0xff
#define UENG_INVALID_BSIC_BCC                       0xff

#define UENG_INVALID_H_CRITERIA                     ((SignedInt16) 0x8000)  /* Min SignedInt16 value */
#define UENG_INVALID_R_CRITERIA_RSCP                ((SignedInt16) 0x8000)
#define UENG_INVALID_R_CRITERIA_EC_NO               ((SignedInt16) 0x8000)



/***************************************************************************
*   Macro Functions
***************************************************************************/

#define UENG_ENCODE_REPORTING_PERIOD(cONFIGmASK, pERIOD) \
( ( (cONFIGmASK)                                         \
    & ~UENG_INFO_REPORTING_PERIOD_MASK )                 \
  |                                                      \
  ( ((pERIOD) << UENG_INFO_REPORTING_PERIOD_BIT_SHIFT)   \
     &  UENG_INFO_REPORTING_PERIOD_MASK )                \
)


#define UENG_EXTRACT_REPORTING_PERIOD(cONFIGmASK)   \
( ( (cONFIGmASK) & UENG_INFO_REPORTING_PERIOD_MASK) \
  >> UENG_INFO_REPORTING_PERIOD_BIT_SHIFT )


#define UENG_RRC_IS_CONNECTED_MODE(mODEsTATE)   \
((mODEsTATE) == UENG_RRC_CONNECTED_URA_PCH   || \
 (mODEsTATE) == UENG_RRC_CONNECTED_CELL_PCH  || \
 (mODEsTATE) == UENG_RRC_CONNECTED_CELL_FACH || \
 (mODEsTATE) == UENG_RRC_CONNECTED_CELL_DCH)


#define UENG_RRC_MODE_NAME(mODEsTATE)                    \
(UENG_RRC_IS_CONNECTED_MODE(mODEsTATE) ? "CONNECTED"   : \
 (mODEsTATE) == UENG_RRC_IDLE          ? "IDLE"        : \
 (mODEsTATE) == UENG_RRC_SUSPENDED     ? "SUSPENDED"   : \
 (mODEsTATE) == UENG_RRC_DEACTIVATED   ? "DEACTIVATED" : \
                                         "??")

#define UENG_RRC_STATE_NAME(mODEsTATE)                       \
((mODEsTATE) == UENG_RRC_CONNECTED_URA_PCH   ? "URA PCH"   : \
 (mODEsTATE) == UENG_RRC_CONNECTED_CELL_PCH  ? "CELL PCH"  : \
 (mODEsTATE) == UENG_RRC_CONNECTED_CELL_FACH ? "CELL FACH" : \
 (mODEsTATE) == UENG_RRC_CONNECTED_CELL_DCH  ? "CELL DCH"  : \
 ((mODEsTATE) == UENG_RRC_IDLE       ||                      \
  (mODEsTATE) == UENG_RRC_SUSPENDED  ||                      \
  (mODEsTATE) == UENG_RRC_DEACTIVATED)       ? "NA"        : \
                                               "??")

#define UENG_SCALE_TO_DB(vALUE) \
((vALUE) / RRC_SYS_INFO_DB_SCALING_FACTOR)

#define UENG_CIPHERING_ALGORITHM_NAME(aLGORITHMiD)    \
((aLGORITHMiD) == UCipheringAlgorithm_uea0 ? "UEA0" : \
 (aLGORITHMiD) == UCipheringAlgorithm_uea1 ? "UEA1" : \
                                             "??")

#define UENG_GSM_BAND_INDICATOR_NAME(bANDiD)               \
((bANDiD) == UFrequency_Band_dcs1800BandUsed ? "DCS1800" : \
 (bANDiD) == UFrequency_Band_pcs1900BandUsed ? "PCS1900" : \
                                               "??")

#define UENG_BARRED_STATUS_NAME(bARREDsTATUS)                      \
((bARREDsTATUS) == UENG_NOT_BARRED            ? "Not Barred"     : \
 (bARREDsTATUS) == UENG_BARRED_NO_SERVICE     ? "No Service"     : \
 (bARREDsTATUS) == UENG_BARRED_EMERGENCY_ONLY ? "Emergency Only" : \
                                                "??")

#define UENG_INTRA_FREQ_BARRING_TYPE_NAME(bARRINGtYPE)                              \
((bARRINGtYPE) == UENG_NO_INTRA_FREQ_BARRING                ? "Not Barred"        : \
 (bARRINGtYPE) == UENG_INTRA_FREQ_BARRING                   ? "Barred"            : \
 (bARRINGtYPE) == UENG_INTRA_FREQ_BARRING_MAX_TBARRED_TIMER ? "Max TBarred Timer" : \
                                                              "??")

#define UENG_ALLOWED_INDICATOR_NAME(aLLOWED)                 \
((aLLOWED) == UAllowedIndicator_allowed    ? "Allowed"     : \
 (aLLOWED) == UAllowedIndicator_notAllowed ? "Not Allowed" : \
                                             "??")

#define UENG_RESERVED_INDICATOR_NAME(rESERVED)                   \
((rESERVED) == UReservedIndicator_reserved    ? "Reserved"     : \
 (rESERVED) == UReservedIndicator_notReserved ? "Not Reserved" : \
                                                "??")

#define UENG_UT_BARRED_NAME(uTbARRED)     \
((uTbARRED) == UT_Barred_s10   ? "10"   : \
 (uTbARRED) == UT_Barred_s20   ? "20"   : \
 (uTbARRED) == UT_Barred_s40   ? "40"   : \
 (uTbARRED) == UT_Barred_s80   ? "80"   : \
 (uTbARRED) == UT_Barred_s160  ? "160"  : \
 (uTbARRED) == UT_Barred_s320  ? "320"  : \
 (uTbARRED) == UT_Barred_s640  ? "640"  : \
 (uTbARRED) == UT_Barred_s1280 ? "1280" : \
                                 "??")


/***************************************************************************
*   Types
***************************************************************************/

/**
 * Enumerated RRC mode and connected states.
 */

//ICAT EXPORTED ENUM
typedef enum UEngRrcModeStateTag
{
    UENG_RRC_DEACTIVATED,
    UENG_RRC_SUSPENDED,
    UENG_RRC_IDLE,
    UENG_RRC_CONNECTED_URA_PCH,
    UENG_RRC_CONNECTED_CELL_PCH,
    UENG_RRC_CONNECTED_CELL_FACH,
    UENG_RRC_CONNECTED_CELL_DCH
}
UEngRrcModeState;

//ICAT EXPORTED ENUM
typedef enum UEngBarredStatusTag
{
    UENG_NOT_BARRED,
    UENG_BARRED_NO_SERVICE,
    UENG_BARRED_EMERGENCY_ONLY
}
UEngBarredStatus;

//ICAT EXPORTED ENUM
typedef enum UEngIntraFreqBarringTypeTag
{
    UENG_NO_INTRA_FREQ_BARRING,
    UENG_INTRA_FREQ_BARRING,
    UENG_INTRA_FREQ_BARRING_MAX_TBARRED_TIMER
}
UEngIntraFreqBarringType;



/**
 * Cell access restriction information.
 */

//ICAT EXPORTED STRUCT
typedef struct UEngCellAccessRestrictionsTag
{
    UCellBarred                    cellBarred;
    UReservedIndicator             resForOpUse;
    UReservedIndicator             resExtension;
    Int16                          accessControlClass;
    UEngIntraFreqBarringType       freqBarring;
}
UEngCellAccessRestrictions;


/**
 * Serving Cell measurement information.
 */

//ICAT EXPORTED STRUCT
typedef struct UEngServingCellMeasTag
{
    SignedInt16     cpich_RSCP;
    SignedInt16     cpich_Ec_N0;
    SignedInt16     q_QualMin;
    SignedInt16     q_RxlevMin;
    SignedInt16     txPowerMax;
}
UEngServingCellMeas;

/**
 * Serving cell parameter information.
 */

//ICAT EXPORTED STRUCT
typedef struct UEngServingCellParamsTag
{
    Int16                   arfcn_dl;
    Int16                   primaryScramblingCode;
    Int16                   arfcn_ul;    /* ********** added */
    Int16                   uraId;
    Boolean                 hcsUsed;

    Int16                   csDrxCycleLength;
    Int16                   psDrxCycleLength;
    Int16                   utranDrxCycleLength;

    /* PS domain items  - also carried by
     * MM engineering mode signal: MmrInfoInd */
    Rac                     rac;
    GprsNetworkMode         gprsNetworkMode;
}
UEngServingCellParams;

/**
 * Engineering mode information about the H_Criterion and R_Criterion for UTRA
 */

//ICAT EXPORTED STRUCT
typedef struct UEngGsmCellReselectInfoTag
{
   SignedInt16     hValue;
   SignedInt16     rValueRscp;
   SignedInt16     s_rxlev;//********** added
}
UEngGsmCellReselectInfo;


/**
 * Engineering mode information about the H_Criterion and R_Criterion for GSM
 */

//ICAT EXPORTED STRUCT
typedef struct UEngUtraCellReselectInfoTag
{
   SignedInt16     hValue;
   SignedInt16     rValueRscp;
   SignedInt16     rValueEcNo;
   SignedInt16     s_rxlev;//********** added
}
UEngUtraCellReselectInfo;

/* CQ00091748 deleted begin */
#if 0
/**
 * Engineering mode information about the serving cell.
 */

//ICAT EXPORTED STRUCT
typedef struct UEngServingCellInfoTag
{
    UCellIdentity               cellId;
    Lai                         lai;
    UEngServingCellParams       params;
    UEngServingCellMeas         meas;
    UEngCellAccessRestrictions  accessRestrictions;
    UEngBarredStatus            barredStatus;
    UEngUtraCellReselectInfo	cellReselectInfo;
}
UEngServingCellInfo;
#endif
/* CQ00091748 deleted end */

/**
 * Intra-Frequency FDD cell measurement information.
 */

//ICAT EXPORTED STRUCT
typedef struct UEngIntraFreqCellMeasTag
{
    SignedInt16     cpich_RSCP;
    SignedInt16     cpich_Ec_N0;
    SignedInt16     pathloss;
    SignedInt16     q_QualMin;
    SignedInt16     q_RxlevMin;
    SignedInt16     txPowerMax;
}
UEngIntraFreqCellMeas;

/**
 * Intra-Frequency FDD cell parameter information.
 */

//ICAT EXPORTED STRUCT
typedef struct UEngIntraFreqCellParamsTag
{
    Int16       primaryScramblingCode;

    Boolean     isActive;
    Int16       referenceTimeDifferenceToCell;
}
UEngIntraFreqCellParams;

/**
 * Engineering mode information about
 * an Intra-Frequency FDD cell.
 */

//ICAT EXPORTED STRUCT
typedef struct UEngIntraFreqCellInfoTag
{
    UIntraFreqCellID            cellId;
    Lai                         lai;
    UEngIntraFreqCellParams     params;
    UEngIntraFreqCellMeas       meas;
    Boolean                     accessRestrictionsPresent;
    UEngCellAccessRestrictions  accessRestrictions;
    UEngUtraCellReselectInfo	cellReselectInfo;
}
UEngIntraFreqCellInfo;

/**
 * Engineering mode information about the
 * Intra-Frequency FDD cells.
 */

//ICAT EXPORTED STRUCT
typedef struct UEngIntraFreqCellListTag
{
    Int8                        numberOfCells;
    UEngIntraFreqCellInfo       cellInfo[maxCellMeas];
}
UEngIntraFreqCellList;

/**
 * Intra-Frequency FDD cell measurement information.
 */

//ICAT EXPORTED STRUCT
typedef struct UEngInterFreqCellMeasTag
{
    SignedInt16     cpich_RSCP;
    SignedInt16     cpich_Ec_N0;
    SignedInt16     utra_carrierRSSI;
    SignedInt16     pathloss;
    SignedInt16     q_QualMin;
    SignedInt16     q_RxlevMin;
    SignedInt16     txPowerMax;
}
UEngInterFreqCellMeas;

/**
 * Intra-Frequency FDD cell parameter information.
 */

//ICAT EXPORTED STRUCT
typedef struct UEngInterFreqCellParamsTag
{
    Int16       arfcn_dl;
    Int16       primaryScramblingCode;
    Int16       referenceTimeDifferenceToCell;
}
UEngInterFreqCellParams;

/**
 * Engineering mode information about
 * an Intra-Frequency FDD cell.
 */

//ICAT EXPORTED STRUCT
typedef struct UEngInterFreqCellInfoTag
{
    UInterFreqCellID            cellId;
    Lai                         lai;
    UEngInterFreqCellParams     params;
    UEngInterFreqCellMeas       meas;
    Boolean                     accessRestrictionsPresent;
    UEngCellAccessRestrictions  accessRestrictions;
    UEngUtraCellReselectInfo	cellReselectInfo;
}
UEngInterFreqCellInfo;

/**
 * Engineering mode information about the
 * Intra-Frequency FDD cells.
 */

//ICAT EXPORTED STRUCT
typedef struct UEngInterFreqCellListTag
{
    Int8                    numberOfCells;
    UEngInterFreqCellInfo   cellInfo[maxCellMeas];
}
UEngInterFreqCellList;

#if !defined (UPGRADE_EXCLUDE_2G)
/**
 * GSM cell measurement information.
 */

//ICAT EXPORTED STRUCT
typedef struct UEngGsmCellMeasTag
{
    SignedInt16     gsm_carrierRSSI;
    SignedInt16     q_RxlevMin;
}
UEngGsmCellMeas;

/**
 * GSM cell parameter information.
 */

//ICAT EXPORTED STRUCT
typedef struct UEngBsicTag
{
    Int8    ncc;
    Int8    bcc;
}
UEngBsic;

/**
 * GSM cell parameter information.
 */

//ICAT EXPORTED STRUCT
typedef struct UEngGsmCellEngParamsTag
{
    Int16             bcch_arfcn;
    UFrequency_Band   gsmBandIndicator;
    UEngBsic          bsic;
}
UEngGsmCellParams;

/**
 * Engineering mode information about a  GSM cell.
 */

//ICAT EXPORTED STRUCT
typedef struct UEngGsmCellEngInfoTag
{
    UInterRATCellID             cellId;
    Lai                         lai;
    UEngGsmCellParams           params;
    UEngGsmCellMeas             meas;
    UEngGsmCellReselectInfo		cellReselectInfo;
}
UEngGsmCellInfo;

/**
 * Engineering mode information about the GSM cells.
 */

//ICAT EXPORTED STRUCT
typedef struct UEngGsmCellListTag
{
    Int8                numberOfCells;
    UEngGsmCellInfo     cellInfo[maxCellMeas];
}
UEngGsmCellList;
#endif /* (UPGRADE_EXCLUDE_2G) */

//CQ00105479 added begin 
//ICAT EXPORTED STRUCT
typedef struct UEngLteCellInfoTag
{
    Earfcn               euarfcn;// CQ00107230 change Int32 to Earfcn
    Int16                pci; // Range is 0~512. Value 0xFFFF is used to indicate PCI is not present
    Int8                 rsrp; // Range is 0~97. Value 0xFF is used to indicate rsrp is not present
    Int8                 rsrq; // Range is 0~34. Value 0xFF is used to indicate rsrq is not present
    SignedInt16          s_rxlev; //********** added
}
UEngLteCellInfo;

//ICAT EXPORTED STRUCT
typedef struct UEngLteCellListTag
{
    Int8                numberOfCells;
    UEngLteCellInfo     cellInfo[maxCellMeas];
}
UEngLteCellList;
//CQ00105479 added end 

/**
 * GSM cell parameter information.
 */

//ICAT EXPORTED STRUCT
typedef struct UEngRntiTag
{
    Int16   srnc_Identity;
    Int32   s_RNTI;
}
UEngRnti;

/**
 * Ciphering Information
 */

//ICAT EXPORTED STRUCT
typedef struct UEngCiperingInfoTag
{
    UCipheringAlgorithm_r7  algorithm;
    Boolean                 algorithmPresent;
    Boolean                 started;
}
UEngCipheringInfo;

/**
 * HSDPA Information
 */

//ICAT EXPORTED STRUCT
typedef struct UEngHsdpaInfoTag
{
   Boolean    hsdpaIsSupported;
   Boolean    hsDschIsActive;
}
UEngHsdpaInfo;


/**
 * HSUPA Information
 */

//ICAT EXPORTED STRUCT
typedef struct UEngHsupaInfoTag
{
   Boolean    edchIsSupported;
   Boolean    edchIsActive;
}
UEngHsupaInfo;

/**
 * UE operation engineering mode information
 * (excluding RRC mode/state)
 */

//ICAT EXPORTED STRUCT
typedef struct UEngUeInfoTag
{
    UEngRnti            uRnti;
    Int8                numberOfRadioLinks;
    SignedInt16         txPower;
    SignedInt16         rssi;
    Int8                numberOfRabs;   /* ********** added */
    UEngCipheringInfo   csCipheringInfo;
    UEngCipheringInfo   psCipheringInfo;
    UEngHsdpaInfo       hsdpaInfo;
    UEngHsupaInfo       hsupaInfo;
    /* ********** added begin */
    Int8                phyChType;// 0 is DPCH, 1 is FDPCH. 0xFF is invalid
    Int16               sf;
    Int8                slotFormat;
    Boolean             compressMode;
    /* ********** added end */
}
UEngUeInfo;



/***************************************************************************
 * General Function Prototypes
 ***************************************************************************/

void UrrMtcEmPeriodicReporting (void);

#if defined (RRC_UPGRADE_ENG_MODE_REDESIGN) //CQ00068137 begin

#define UENG_INFO_GS34_HIGH		0x00010000
#define UENG_INFO_GS35_HIGH		0x00020000
#define UENG_INFO_GS67_HIGH		0x00040000
#define UENG_INFO_GS6F_HIGH		0x00080000
#define UENG_INFO_RF61_HIGH		0x00100000
#define UENG_INFO_RF62_HIGH		0x00200000
#define UENG_INFO_RF60_HIGH		0x00400000
#define UENG_INFO_GS57_HIGH		0x00000010
#define UENG_INFO_GS58_HIGH		0x00000020
#define UENG_INFO_GS54_HIGH		0x00000040
#define UENG_INFO_GS55_HIGH		0x00000080
#define UENG_MODE_INFO_ALL_DATA	0xFFFFFFFF

#define ENG_MODE_INVALID_PARAM 255
#define ENG_MODE_INVALID_PARAM_16BITS 0xFFFF
#define ENG_MODE_INVALID_ARFCN 0x7FFF
#define BITS_PER_BYTE           8




#define TGPS_STATUS_ACTIVE     		0x80 
#define TGPS_MEAS_TDD     	   		0x00
#define TGPS_MEAS_FDD     	   		0x01
#define TGPS_MEAS_GSM_RSSI 	  		0x02
#define TGPS_MEAS_GSM_BSIC_ID  		0x03
#define TGPS_MEAS_GSM_BSIC_RECFG	0x04	

#define EM_PERIOD_LOWER_BOUND_IN_MILLI_SECS_640 640 //0.62
#define EM_PERIOD_LOWER_BOUND_IN_MILLI_SECS_500 500 //0.48
#define EM_PERIOD_LOWER_BOUND_IN_MILLI_SECS_480 480 //0.46

void UrrMtcEngModePeriodicRepRf61 (void);

void UrrMtcEngModePeriodicRepRf62 (void);

void UrrMtcEngModePeriodicRepRf60 (void);


void UrrMtcEngModeSendMeas (void);

void UrrMtcEngModeSendInRatMeas (void);

void UrrMtcEngModeSendSCellMeas (void);

#endif //RRC_UPGRADE_ENG_MODE_REDESIGN :CQ00068137 end

#endif /* URRMTCEM_H */
/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urrsirty.h#3 $
 *   $Revision: #3 $
 *   $DateTime: 2007/03/19 13:59:30 $
 **************************************************************************
 * File Description:
 *
 *    Types for the System Information Reception sub-process.
 **************************************************************************/

#if !defined (URRSIRTY_H)
#define       URRSIRTY_H

/***************************************************************************
* Nested Include Files
***************************************************************************/
#if ! defined (UPGRADE_EXCLUDE_2G)
#include <grrrcsig.h>
#endif  /* UPGRADE_EXCLUDE_2G */
#include <kernel.h>
#include <urrtypes.h>
#include <urrintsig.h>

/* ********** begin */
/* Define the macros so that the Linked List utility functions use the URRC
 * local allocator and deallocator functions rather than calling KiAllocMemory
 * and KiFreeMemory. The local allocator/deallocator routines are much quicker
 * and reduce system loading. The definition of these macros must be done
 * before utllist.h is included, otherwise, utllist.h will use the default
 * allocator/deallocator routines. */
/* CQ00010790, modified begin */
#define UTLL_ALLOC_MEMORY(sIZE, nODE_PP) UrrLocalAllocZeroMemory (sIZE, nODE_PP)
/* CQ00010790, modified end */
#define UTLL_FREE_MEMORY(nODE_PP) UrrLocalFreeMemory (nODE_PP)

#include <utllist.h>

#if defined (RRC_SIR_OPTIMIZATION)
#if defined(UPGRADE_DSDSWB)
extern Boolean isDBMatch1[URRC_TASK_NUM]; /* CQ00066474 add */
#define isDBMatch isDBMatch1[URRC_DSDS_TASK]
#else/*UPGRADE_DSDSWB*/
extern Boolean isDBMatch; /* CQ00066474 add */
#endif/*UPGRADE_DSDSWB*/
#if defined (ON_PC)
#define URR_SIR_SI_VALIDITY_PERIOD_IN_MINUTES	2 // 2 minute for testing purposes on PC
#else
#define URR_SIR_SI_VALIDITY_PERIOD_IN_MINUTES	359 // 5hours and 59 minutes
#endif/* ON_PC */
#define URR_SECONDS_PER_MINUTE                      60
#define URR_SIR_SI_VALIDITY_PERIOD_IN_SECS	    URR_SIR_SI_VALIDITY_PERIOD_IN_MINUTES*URR_SECONDS_PER_MINUTE
#define URR_SIR_SI_VALIDITY_PERIOD_IN_TICKS		SECONDS_TO_TICKS(URR_SIR_SI_VALIDITY_PERIOD_IN_SECS)
#endif //RRC_SIR_OPTIMIZATION
/* ********** end */
//#if defined (UPGRADE_CSG1) || defined (UPGRADE_PLMS_SIB5) //CQ00072330 begin  //CQ00089215 Remove ifdef for 1826
/*CQ00084613 - modify begin*/
#define URR_SIR_SIB20_BITMAP	URR_SIR_EXT2_SYS_INFO_20_MASK 
#define URR_SIR_SIB5_BITMAP		URR_SIR_SYS_INFO_5_MASK // CQ0080536
#define URR_SIR_SIB3_BITMAP		URR_SIR_SYS_INFO_3_MASK   
#define URR_SIR_MIB_BITMAP		URR_SIR_SYS_INFO_MIB_MASK  
/*CQ00084613 - modify end*/
//#endif//CQ00070837 end 

/* CQ00085704 add begin */
#define URR_SIR_NUM_OF_DEFERRED_NORMAL_SIBS 3   // 11, 12 & 18
#define URR_SIR_NUM_OF_DEFERRED_EXT_SIBS 2      // 11bis & 19
/* CQ00085704 add end */

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/

#define MIB_SIB_POS             0
#define MIB_SIB_REP             8
#define MIB_SIB_OFF             2
#define URR_SIR_MINIMUM_SEGMENTS    1  /* CQ00043651 */

#define SEGMENT_INDEX_ZERO      0

#define URR_SIR_SYS_INFO_MIB_MASK       (Int32) 0x00000001
#define URR_SIR_SYS_INFO_SB1_MASK       (Int32) 0x00000002
#define URR_SIR_SYS_INFO_SB2_MASK       (Int32) 0x00000004
#define URR_SIR_SYS_INFO_1_MASK         (Int32) 0x00000008
#define URR_SIR_SYS_INFO_2_MASK         (Int32) 0x00000010
#define URR_SIR_SYS_INFO_3_MASK         (Int32) 0x00000020
#define URR_SIR_SYS_INFO_4_MASK         (Int32) 0x00000040
#define URR_SIR_SYS_INFO_5_MASK         (Int32) 0x00000080
#define URR_SIR_SYS_INFO_6_MASK         (Int32) 0x00000100
#define URR_SIR_SYS_INFO_7_MASK         (Int32) 0x00000200
#define URR_SIR_SYS_INFO_11_MASK        (Int32) 0x00000400
#define URR_SIR_SYS_INFO_12_MASK        (Int32) 0x00000800
#define URR_SIR_SYS_INFO_13_MASK        (Int32) 0x00001000
#define URR_SIR_SYS_INFO_13_1_MASK      (Int32) 0x00002000
#define URR_SIR_SYS_INFO_13_2_MASK      (Int32) 0x00004000
#define URR_SIR_SYS_INFO_13_3_MASK      (Int32) 0x00008000
#define URR_SIR_SYS_INFO_13_4_MASK      (Int32) 0x00010000
#define URR_SIR_SYS_INFO_14_MASK        (Int32) 0x00020000
#define URR_SIR_SYS_INFO_15_MASK        (Int32) 0x00040000
#define URR_SIR_SYS_INFO_15_1_MASK      (Int32) 0x00080000
#define URR_SIR_SYS_INFO_15_2_MASK      (Int32) 0x00100000
#define URR_SIR_SYS_INFO_15_3_MASK      (Int32) 0x00200000
#define URR_SIR_SYS_INFO_15_4_MASK      (Int32) 0x00400000
#define URR_SIR_SYS_INFO_15_5_MASK      (Int32) 0x00800000
#define URR_SIR_SYS_INFO_16_MASK        (Int32) 0x01000000
#define URR_SIR_SYS_INFO_17_MASK        (Int32) 0x02000000
#define URR_SIR_SYS_INFO_18_MASK        (Int32) 0x04000000
#define URR_SIR_SYS_INFO_SPARE_1_MASK   (Int32) 0x08000000
/*CQ00084613 - modify start*/
#define URR_SIR_EXT_SYS_INFO_11_BIS_MASK    (Int32) 0x10000000
#define URR_SIR_EXT2_SYS_INFO_19_MASK       (Int32) 0x20000000
/* CQ00066469 - add begin */
#if defined UPGRADE_CSG1
#define URR_SIR_EXT2_SYS_INFO_20_MASK    (Int32) 0x40000000
#endif //UPGRADE_CSG1
/* CQ00066469 - add end */
/*CQ00084613 - end */
#define PSC_NOT_REQUESTED               (Int16) 0xFFFF
#define UARFCN_NOT_REQUESTED            (Int16) 0xFFFF

/***************************************************************************
*   Macro Functions
***************************************************************************/

/******************************************************************************
 *
 * Function     : SET_BCH_ON_OFF_STATE
 *
 * Scope        : LOCAL
 *
 * Parameters   : sTATE - BCH on/off state
 *
 * Returns      : None
 *
 * Description  : Sets the BCH on/off state
 *
 *****************************************************************************/
#if defined (ON_PC)
#define SET_BCH_ON_OFF_STATE(sTATE, iNDEX)                                      \
    urrSir.bchOnOffState = sTATE;                                               \
    printf("RRC BCH State : %s, index %d", bchOnOffStateString[urrSir.bchOnOffState], iNDEX);	/* CQ00095497 modified */
#else
#define SET_BCH_ON_OFF_STATE(sTATE, iNDEX)                                      \
    urrSir.bchOnOffState = sTATE;                                               \
    RRC_TRACE_1(URRC_SIR, SET_BCH_ON_OFF_STATE_##iNDEX, URRC_SIR_DEFAULT,      \
        "RRC BCH State: %e{BchOnOffState}", urrSir.bchOnOffState);
#endif

/******************************************************************************
 *
 * Function     : URR_SIR_CREATE_PARAMETERS
 *
 * Scope        : GLOBAL
 *
 * Parameters   : UrrSirAllocatedMemList  *memList_p - list of allocated blocks
 *                dest_p        - pointer to destination field
 *                structureSize - size of structure
 *
 * Returns      : None
 *
 * Description  : Called to allocate memory for an IE
 *
 *                Memory is allocated and zeroed for the structure
 *
 * Notes:
 *      UrrLocalAllocMemory requires the destination pointer to be currently NULL
 *      UrrLocalAllocZeroMemory Allocates memory for structure
 *      AddToMemList saves the pointer allocated ready for deallocation utility
 *
 *****************************************************************************/

#if defined (DEBUG_MEMORY_LEAK_ON_PC)
#define URR_SIR_CREATE_PARAMETERS(memList_p, dest_pp, structureSize)                    \
{                                                                                       \
    *(dest_pp) = PNULL;                                                                 \
                                                                                        \
    UrrLocalAllocZeroMemory ((structureSize), (void **) (dest_pp));                     \
                                                                                        \
    AddToMemList (__FILE__,__LINE__,(memList_p), *(dest_pp));                                             \
}  
#else
#define URR_SIR_CREATE_PARAMETERS(memList_p, dest_pp, structureSize)                    \
{                                                                                       \
    *(dest_pp) = PNULL;                                                                 \
                                                                                        \
    UrrLocalAllocZeroMemory ((structureSize), (void **) (dest_pp));                     \
                                                                                        \
    AddToMemList ((memList_p), *(dest_pp));                                             \
}
#endif


/* CQ00038383, added begin */
#define URR_SIR_EXTRACT_PARAMETERS(memList_p, uSIB_p, dest_pp, structureSize)           \
{                                                                                       \
    URR_SIR_CREATE_PARAMETERS ((memList_p), (dest_pp), (structureSize));                \
    memcpy (*(dest_pp), (uSIB_p), (structureSize));                                     \
}
/* CQ00038383, added end */

/* ********** begin */
#if defined (RRC_SIR_OPTIMIZATION)
/******************************************************************************
 *
 * Function     : SET_WAIT_FOR_SIB3
 *
 * Scope        : LOCAL
 *
 * Parameters   : sTATE - urrSirVistidSibsDataBase.waitForSib3 value
 *
 * Returns      : None
 *
 * Description  : Sets urrSirVistidSibsDataBase.waitForSib3 flag
 *
 *****************************************************************************/
#define SET_WAIT_FOR_SIB3(sTATUS, iNDEX)                                      \
    RRC_TRACE_2(URRC_SIR, SET_WAIT_FOR_SIB3_##iNDEX, URRC_SIR_DEFAULT,      \
        "waitForSib3 set %d -> %d", urrSirVistidSibsDataBase.waitForSib3, sTATUS); \
    urrSirVistidSibsDataBase.waitForSib3 = sTATUS;

/******************************************************************************
 *
 * Function     : SET_MATCH_CELL
 *
 * Scope        : LOCAL
 *
 * Parameters   : sTATE - urrSirVistidSibsDataBase.matchCell value
 *
 * Returns      : None
 *
 * Description  : Sets urrSirVistidSibsDataBase.matchCell flag
 *
 *****************************************************************************/
#define SET_MATCH_CELL(sTATUS, iNDEX)                                      \
    RRC_TRACE_2(URRC_SIR, SET_MATCH_CELL_##iNDEX, URRC_SIR_DEFAULT,      \
        "matchCell set %d -> %d", urrSirVistidSibsDataBase.matchCell, sTATUS); \
    urrSirVistidSibsDataBase.matchCell = sTATUS;

#endif //RRC_SIR_OPTIMIZATION
/* ********** end */

/***************************************************************************
 * Types
 ***************************************************************************/

#define MAX_UMTS_CONFIG_ID                  16
#define MAX_UMTS_SIB_OFFSETS                15
#if ! defined (UPGRADE_EXCLUDE_2G)
/* types for UMTS system information acquisition in GSM/GPRS mode */
#define MAX_UMTS_SIB_SCHEDULING_INFO_SIZE   19

typedef struct UmtsSibSchedulingInfoTag
{
    USIB_Type                       sibType;
    Int8                            configId;
    Int16                           sibRep;
    Int16                           sibPos;
    USegCount                       segCount;
    Int8                            numSibOffs;
    USibOFF                         sibOffs [MAX_UMTS_SIB_OFFSETS];
    Int16                           segRxInfo;
}
UmtsSibSchedulingInfo;

typedef struct UmtsSibSchedulingArrayTag
{
    Int8                    numSibs;
    UmtsSibSchedulingInfo   schedInfo [MAX_UMTS_SIB_SCHEDULING_INFO_SIZE];
}
UmtsSibSchedulingArray;

typedef struct UMTS_SysInfoTag
{
    Int16                   sfn;
    UUARFCN                 uarfcn;
    UPrimaryScramblingCode  psc;
    Int8                    *sysInfoTti_p;
    Boolean                 plmnRequired;
    Boolean                 configIdRequired;
/* Shared NW: multiple number of PLMNs may be retrieved by MIB.
    plmn, plmnPresent will be deleted when RRC integrates Shared NW changes -
    RRC will start using plmnList[] and numPlmns */
    Int8                    numPlmns;
    UPLMN_Identity          plmnList[MAX_PLMN_IN_CELL];
    PredefConfigStatusInfo  predefConfigStatusInfo;
    UmtsSibSchedulingArray  sibScheduling;
}
UMTS_SysInfo;

//CQ00036244, Add ERRC-URRC interface for IRAT-CGI, start
typedef struct UMTS_CgiInfoTag
{
    Int16                               sfn;
    UUARFCN                          uarfcn;
    Int16                            physCellId;
    
    //WB-Ncell BCH data, ERRC is responsible for this memory
    Int8                                *sysInfoTti_p;

    //Flag to indicate if this is a new WB cell
    //if firstRead = TRUE, means this is the first FddNcellBchInd after the ecphyWcgiBchReq
    //otherwise, firstRead = FALSE
    Boolean                            firstRead;
    
    //Identifies the PLMN of the cell as given by the common PLMN broadcast in the MIB, as defined in TS 25.331
    //numPlmns = 0 means plmnPresent is FALSE
    //numPlmns >= 2 means multiple number of PLMNs may be retrieved by MIB
    Int8                       numPlmns;                              //from MIB
    UPLMN_Identity       plmnList[MAX_PLMN_IN_CELL];
    
    //UTRA Cell Identifier which is unique within the context of the identified PLMN as defined in TS 25.331
    Boolean                  cellIdentityPresent;
    Int32                     cellIdentity;                             //bit string 28, from sib3
    
    //A fixed length code identifying the location area within a PLMN, as defined in TS 23.003
    Boolean                  lacPresent;
    Int16                     lac;                                          //bit string 16
    
    //The RAC identity read from broadcast information, as defined in TS 23.003
    Boolean                  racPresent;
    Int8                       rac;                                         //bit string 8

    //If present, the cell is a CSG cell; If absent, is not a CSG cell. From MIB, Rel-8
    Boolean                   csgIndication;
    
    //The IE CSG-Identity is used to identify a Closed Subscriber Group
    Boolean                  csgIdentityPresent;
    Int32                     csgIdentity;                         //bit string 28, from sib3   
#if defined (UPGRADE_CSG1) //CQ00067091 start
	/*store for SIB20 */
	Boolean					hnbNamePresent;
	HnbNameStr				hnbName;
#endif// CQ00067091 end

// start CQ0080536
#if defined (UPGRADE_PLMS_SIB5)
	Boolean 						freqBandIndicatorPresent;
	FddBandsMask					bandIndicatorMask; /*CQ00084613 - modify */
#endif
// end CQ0080536
}
UMTS_CgiInfo;
//CQ00036244, Add ERRC-URRC interface for IRAT-CGI, end

#endif

/* types for storage of predefined configuration information */
typedef struct UrrSirEncodedPredefinedConfigTag
{
    const Int8  *encodedData_p;
    Int32       encodedDataLength;
}
UrrSirEncodedPredefinedConfig;

#if ! defined (UPGRADE_EXCLUDE_2G)
typedef struct PredefinedConfigInfoTag
{
    /* store for information about a single SIB16 */
    /* identity */
    UPredefinedConfigIdentity       id;
    /* value tag */
    UPredefinedConfigValueTag       vt;
}
PredefinedConfigInfo;
#endif

typedef struct UrrSirSib16StoredElementTag
{
    /* Pointer to the stored PER encoded data */
    Int8                                *data_p;
    /* Length (in bits) of the data */
    Int16                               sib16Length;
    /* The instance of the SIB 16 */
    Int16                               sib16Instance;
    /* Next element in list */
    struct UrrSirSib16StoredElementTag  *next_p;
}
UrrSirSib16StoredElement;

#if ! defined (UPGRADE_EXCLUDE_2G)
typedef struct ScheduledPredefConfigInfoTag
{
    Int8                        num;
    PredefinedConfigInfo        info [MAX_UMTS_CONFIG_ID];
}
ScheduledPredefConfigInfo;
#endif

typedef struct AcquiredPredefConfigStoreTag
{
    /* UARFCN and PSC of cell to which SIB16 info applies */
    UUARFCN                         uarfcn;
    UPrimaryScramblingCode          psc;
    /* valued of MIB value tag when the SIB16 info was read */
    UMIB_ValueTag                   mibValueTag;
    /* number of predefined configs */
    Int8                            num;
    /* store for predefined config information */
    UPredefinedConfigIdentity       id [MAX_UMTS_CONFIG_ID];
    /* stored SIB16s in encoded form */
    UrrSirSib16StoredElement        *storedSib16List_p;
}
AcquiredPredefConfigStore;

#if ! defined (UPGRADE_EXCLUDE_2G)
typedef enum Sib16AcquisitionStateTag
{
    SIB16_ACQUISITION_IDLE,
    SIB16_ACQUISITION_MIB,
    SIB16_ACQUISITION_SB1,
    SIB16_ACQUISITION_SB2,
    SIB16_ACQUISITION_SIB16,
    SIB16_ACQUISITION_MIB_REPEAT,
    SIB16_ACQUISITION_SB1_REPEAT,
    SIB16_ACQUISITION_SB2_REPEAT,
    SIB16_ACQUISITION_SIB16_REPEAT
}
Sib16AcquisitionState;
#endif  /* UPGRADE_EXCLUDE_2G */

typedef enum SirProcTag
{
    SIR_PROC_BCCH_MOD = 0,
    SIR_PROC_BCH_ON_REQ,
    MAX_NUM_SIR_PROC

}
SirProc;


typedef enum UrrSirSibModTypeTag
{
    SIR_MOD_TYPE_VALUE_TAG,
    SIR_MOD_TYPE_TIMER
}
UrrSirSibModType;

typedef enum UrrSirAreaScopeTag
{
    SIR_AREA_SCOPE_PLMN,
    SIR_AREA_SCOPE_CELL
}
UrrSirAreaScope;

//ICAT EXPORTED ENUM
typedef enum UrrSirCellTypeTag
{
    SIR_CELL_TYPE_SERVING = 1,
    SIR_CELL_TYPE_NEIGHBOUR
}
UrrSirCellType;

typedef struct UrrSirSegmentInfoTag
{
    USegmentIndex               segmentIndex;
    Int16                       segLength;
    Int8                        *segData_p;
    struct UrrSirSegmentInfoTag    *nextSegment_p;
}
UrrSirSegmentInfo;

typedef struct UrrSirBlockSegmentStoreInfoTag
{
    USIB_Type                           sib_Type;
    UrrSirSibTypeExtExt2                      sib_TypeExt;
    Int8                                instanceId;
    USegCount                           segCount;
    UrrSirSegmentInfo                   *segInfoFirst_p;
    UrrSirSegmentInfo                   *segInfoLast_p;
    struct UrrSirBlockSegmentStoreInfoTag  *nextBlock_p; /* CQ00076595 */
}
UrrSirBlockSegmentStoreInfo;

typedef struct UrrSirSysInfoRequiredBitmapsTag
{
    Int32    Mtc;   /* bitmap of sys info blocks required by MTC sub-process */
    Int32    Smc;   /* bitmap of sys info blocks required by SMC sub-process */
    Int32    Csr;   /* bitmap of sys info blocks required by CSR sub-process */
    Int32    Sir;   /* bitmap of sys info blocks required by SIR sub-process */
    Int32    Cer;   /* bitmap of sys info blocks required by CER sub-process */
    Int32    Rbc;   /* bitmap of sys info blocks required by RBC sub-process */
    Int32    Cmr;   /* bitmap of sys info blocks required by CMR sub-process */
    Int32    Ais;   /* bitmap of sys info blocks required by AIS sub-process */
    Int32    Mcr;   /* bitmap of sys info blocks required by MCR sub-process */
}
UrrSirSysInfoRequiredBitmaps;

typedef enum UrrSirSibPresenceIndicatorTag
{
    SIR_NOT_PRESENT  = FALSE,
    SIR_PRESENT      = TRUE,
    SIR_NOT_VALID
}
UrrSirSibPresenceIndicator;

typedef struct UrrSirSib4PresenceInfoTag
{
    UrrSirSibPresenceIndicator   sib4Present;  /* SIB is broadcast in cell */
}
UrrSirSib4PresenceInfo;

typedef struct UrrSirSib6PresenceInfoTag
{
    /* SIB is broadcast in cell */
    UrrSirSibPresenceIndicator   sib6Present;
    /* indicates if UPRACH_RACH_Info is present in SIB6 */
    UrrSirSibPresenceIndicator   UPRACH_RACH_Info_present;
    /* indicates if UAICH_Info is present in SIB6 */
    UrrSirSibPresenceIndicator   UAICH_Info_present;
    /* indicates if UPICH_Info is present in SIB6 */
    UrrSirSibPresenceIndicator   UPICH_Info_present;
    /* indicates if USCCPCH_Info is present in SIB6 */
    UrrSirSibPresenceIndicator   USCCPCH_Info_present;
}
UrrSirSib6PresenceInfo;

typedef struct UrrSirSib12PresenceInfoTag
{
    /* SIB is broadcast in cell */
    UrrSirSibPresenceIndicator   sib12Present;
    /* indicates if UIntraFreqMeasQuantity is present in SIB12 */
    UrrSirSibPresenceIndicator   UIntraFreqMeasQuantity_present;
    /* indicates if UIntraFreqReportingQuantityForRACH is present in SIB12 */
    UrrSirSibPresenceIndicator   UIntraFreqReportingQuantityForRACH_present;
    /* indicates if UMaxReportedCellsOnRACH is present in SIB12 */
    UrrSirSibPresenceIndicator   UMaxReportedCellsOnRACH_present;
    /* indicates if UReportingInfoForCellDCH is present in SIB12 */
    UrrSirSibPresenceIndicator   UReportingInfoForCellDCH_present;
    /* indicates if UIntraFreqReportingQuantity is present in SIB12 */
    UrrSirSibPresenceIndicator   UIntraFreqReportingQuantity_present;
    /* indicates if UIntraFreqReportingCriteria is present in SIB12 */
    UrrSirSibPresenceIndicator   UIntraFreqReportingCriteria_present;
    /* indicates if UPeriodicalReportingCriteria is present in SIB12 */
    UrrSirSibPresenceIndicator   UPeriodicalReportingCriteria_present;
}
UrrSirSib12PresenceInfo;

typedef struct UrrSirRemovedSibInfoTag
{
    Boolean     sb1Scheduled;         /* SB1 is scheduled in the MIB */
    Boolean     sb1Due;               /* SB1 is expected but not yet received */
    Boolean     sb1Updated;           /* SB1 has been updated */
    Boolean     sb2Scheduled;         /* SB2 is scheduled in the MIB */
    Boolean     sb2Due;               /* SB2 is expected but not yet received */
    Boolean     sb2Updated;           /* SB2 has been updated */
    Boolean     checkForRemovedSibs;  /* check if SIBs removed */
    Int32       scheduledSibsBitmap;  /* bitmap indicating which SIBs are */
                                      /* in the MIB and SBs */
    Int32       scheduledExtSibsBitmap;  /* bitmap indicating which EXT SIBs are */
                                      /* in the MIB and SBs */
}
UrrSirRemovedSibInfo;

typedef struct UrrSirDatabaseEntryTag
{
    /* type of system information block */
    USIB_Type                           sib_Type;
    /* type of exended system information block */
    UrrSirSibTypeExtExt2                      sib_TypeExt;
    /* identity for SIB 16 only (multiple instances) */
    Int8                                instanceId;
    /* indicates if first segment of SIB has been received */
//    Boolean                             firstSegmentReceived;
    /* number of last segment which was received */
//    Int8                                lastReceivedSegment;
//#if !defined (UPGRADE_EXCLUDE_2G)
    /* bitmap of received segments - used for out-of-sequence SIB16 reception */
    Int16                               receivedSegmentBitmap;
//#endif /* UPGRADE_EXCLUDE_2G */
    /* flag for SIB 16 only - value tag may not be present */
    Boolean                             valueTagPresent;
    /* value tag value */
    Int8                                valueTagValue;
    /* SEG_COUNT value for SIB */
    Int8                                segCount;
    /* SIB_REP value for SIB */
    Int16                               sibRep;
    /* SIB_POS value for SIB */
    Int16                               sibPos;
    /* number of SIB_OFF values in list */
    Int8                                sibOffCount;
    /* list of SIB_OFF values */
    Int8                                sibOffs [MAX_UMTS_SIB_OFFSETS];
    /* expiry time for timer update sys infos */
    Int32                               expirationTime;
    /* pointer to data for next system information block */
    struct UrrSirDatabaseEntry          *next_p;
}
UrrSirDatabaseEntry;

typedef struct UrrBchCrcCountTag
{
#if defined (URRC_DEBUG_SIR_SUB_PROCESS)
    /* goodCrcCount used for bebug info only */
    Int16 goodCrcCount;
#endif
    /* used to count bad BCH CRC packets */
    Int16 badCrcCount;
}
UrrBchCrcCount;

typedef struct UrrSirValueTagInfoTag
{
    Int8                                sib11ValueTag;
    Int8                                sib12ValueTag;
}
UrrSirValueTagInfo;


typedef struct UrrSirDatabaseTag
{
    /* scrambling code is valid */
    Boolean                             scramblingCodeValid;
    /* primary scrambling code of cell */
    UPrimaryScramblingCode              primaryScramblingCode;
    /* downlink ARFCN of cell */
    UUARFCN                             uarfcn_DL;
    /* cell's PLMN identity */
    UPLMN_Identity                      plmnIdentity;
    /* cell's cell identity */
    UCellIdentity                       cellIdentity;
    /* indicates if database initialised */
    Boolean                             awaitingFirstMib;
    /* indicates if MIB to be reacquired */
    Boolean                             reAcquireMib;
    /* indicates if part of a MIB has been received */
    Boolean                             partOfMibReceived;
    /* number of MIB segments */
    Int8                                numMibSegments;
    /* number of next MIB segment due */
    Int8                                nextMibSegmentDue;
//#if !defined (UPGRADE_EXCLUDE_2G)
    /* received MIB segments bitmap, used for out-of-sequence MIB reception*/
    Int16                               receivedMibSegmentBitmap;
//#endif /* UPGRADE_EXCLUDE_2G */
    /* value tag for MIB */
    Int8                                mibValueTag;
    /* last value of skip SFN sent to PHY */
    Int16                               lastSkipValueSent;
    /* bitmap of sys info blocks to be read */
    Int32                               readSysInfoBitmap;
    /* bitmap of EXT sys info blocks to be read */
    Int32                               readExtSysInfoBitmap;
    /* bitmap of SIB15_2 sys info blocks to be read */
    Int16                               readSib15_2InstanceBitmap;
    /* bitmap of SIB15_3 sys info blocks to be read */
    Int16                               readSib15_3InstanceBitmap;
    /* bitmap of SIB16 sys info blocks to be read */
    Int16                               readSib16InstanceBitmap;
    /* bitmaps of sys info required by other sub-processes */
    UrrSirSysInfoRequiredBitmaps        sysInfoToAcquireBitmaps;
    /* bitmaps of EXT sys info required by other sub-processes */
    UrrSirSysInfoRequiredBitmaps        extSysInfoToAcquireBitmaps;
    /* bitmaps of sys info to distribute to other sub-processes */
    UrrSirSysInfoRequiredBitmaps        sysInfoToDistributeBitmaps;
    /* bitmaps of sys info to distribute to other sub-processes */
    UrrSirSysInfoRequiredBitmaps        extSysInfoToDistributeBitmaps;
    /* indicates if SIB4 present */
    UrrSirSib4PresenceInfo              sib4PresenceInfo;
    /* indicates if SIB5bis is scheduled in the cell */
    Boolean                             sib5bisScheduled;
    /* indicates if SIB6 and optional IEs present */
    UrrSirSib6PresenceInfo              sib6PresenceInfo;
    /* indicates if SIB12 and optional IEs present */
    UrrSirSib12PresenceInfo             sib12PresenceInfo;
    /* info required to find out if SIB removed */
    UrrSirRemovedSibInfo                removedSibInfo;
    /* used to track whether BCH skipping or not */
    Boolean                             bchSkipping;
    /* used to track if CSR has been informed of sib per decode error */
    Boolean                             informedCsrSibPerDecodeError;
    /* used to track ratio of good to bad BCH CRCs */
    UrrBchCrcCount                      bchCrcCount;
    /* pointer to head of sys info data list */
    UrrSirDatabaseEntry                 *firstEntry_p;
    /* indicates that the SIB7 timer has expired */
    Boolean                             sib7timerExpired;
    /* indicates that the periodic SIB7 timer has expired in cell FACH*/
    Boolean                             sib7PeriodicTimerExpired;
    /* Inidcates value tag of sibs 11/12 */
    UrrSirValueTagInfo                  sib11_12ValueTag;
}
UrrSirDatabase;

/* ********** begin */
#if defined (RRC_SIR_OPTIMIZATION)
// define struct for sir optimization

//ICAT EXPORTED STRUCT
typedef struct UrrSirSiMsgsListElementTag
{
	USIB_Type        	 sib_Type;
	UrrSirSibTypeExtExt2 extSibType;
	Int16				 encodedSiSize;
	Int8				 *encodedSiString_p;
}
UrrSirSiMsgsListElement;

/** SI messages messages list.
The list contains SIB's received for a specific Cell*/
UT_SINGLE_LINK_LIST_DECLARE (UrrSirSiMsgsList, UrrSirSiMsgsListElement);

//ICAT EXPORTED STRUCT
typedef struct UrrTimeStampTag
{
	KernelTicks 		timeoutPeriod;
	KernelTicks 		timeStamp;
}
UrrTimeStamp;

//ICAT EXPORTED STRUCT
typedef struct UrrSirReadingDeltaTag
{
	Int32 				timeElapse;
}
UrrSirReadingDelta;

//ICAT EXPORTED STRUCT
typedef struct UrrSirCellInfoTag
{
	UUARFCN 				uarfcn;
	UPrimaryScramblingCode	psc;
	UPLMN_Identity          plmn_Identity;
	UMIB_ValueTag			mibValueTag;
	UCellIdentity			cellIdentity;
}
UrrSirCellInfo;

//ICAT EXPORTED STRUCT
typedef struct SirUrrCellDbTag
{
    UrrSirCellInfo			cellInfo;// for each cell in the list hold the cell parameters
	UrrTimeStamp			cellTimeStamp;// and time stamp so know if data is valid or not
	UrrSirSiMsgsList		siMessagesList;// and list of the sibs on that cell
	Int8					statisticReadingIndex;// the size of statisticReadingDelta is valid data
	UrrSirReadingDelta		statisticReadingDelta[10];// for statistics informetion, arry of the time take for as from reading sibs to all sibs recive,
													  // need to see imrove from the first time to other
}
SirUrrCellDb;

/** Cells list.
The list contains Cell's vistid */
UT_SINGLE_LINK_LIST_DECLARE (UrrSirVisitedCellsList, SirUrrCellDb);
#if defined (DUMMY_FLAG_MICHAEL)
// for PC only so the struct can be found in the source insight
typedef UrrSirVisitedCellsList UrrSirVisitedCellsList;
#endif

typedef UT_SINGLE_LINK_LIST_NODE_TYPE(UrrSirVisitedCellsList) UrrSirVisitedCellNode;

typedef UT_SINGLE_LINK_LIST_NODE_TYPE(UrrSirSiMsgsList) UrrSirSiMsgNode;

//ICAT EXPORTED STRUCT
typedef struct UrrSirVistidSibsDataBaseTag
{
	Boolean 							waitForSib3;// when reading MIB, if match data base ,set true else false
	Boolean								matchCell;	// when reading sib3 , if waitForSib3==true, and sib3 is match data base ,set to true , else false
	UrrSirCellType                  	matchCellType;// when match cell to data base(all parqameters) save the typr ,so when distribute we will know if it's for serving or neighbour
	UrrSirVisitedCellsList				visitedCells; // list of cell we camp on and save them sibs
#if defined (UPGRADE_SHARED_SYSINFO)
    Boolean                             fromOtherTask;  // indicator cellSelectedMatch_p is got from which DB(own task or other task)
#endif//UPGRADE_SHARED_SYSINFO    
	UrrSirVisitedCellNode				*cellSelectedMatch_p;// if MIB was match get pointer to that cell so when checking for sib3 start from that cell and not form the head
	UrrTimeStamp						timeStampStartRead;
	UrrTimeStamp						timeStampEndRead;
	Int32								tempElapesTime;// in case we in the first time in this cell , at the time we save the elapes time the cell data is not create yet, save in temp and store later
}
UrrSirVistidSibsDataBase;

#endif //RRC_SIR_OPTIMIZATION
/* ********** end */

typedef struct UrrSirBcchModInfoTag
{
    /* indicates BCH modification has been notified */
    Boolean                             awaitingModTime;

    /* Variables to store information from BCCH modification information */
    /* value tag from BCH modification */
    Int8                                mibValueTag;
    /* modification time is valid */
    Boolean                             modTimeValid;
    /* modification time */
    Int16                               modTime;

    /* Variables to store information to determine if the BCCH modification
     * time has been reached.
     * 25.331 8.1.1.7.2 indicates that BCCH modification should be actioned at
     * the SFN indicated by modTime, but in practice the UE may not receive
     * this frame and it still needs to act on the BCCH modification. Since the
     * SFN can wrap from 4095 to 0 it is not possible simply to say
     * 'if SFN >= modTime'. Variable notificationTime is set to the SFN of the
     * first BCCH frame received after the BCCH modification is notified to the
     * UE. For a given frame, if SFN >= modTime, modTimeReached is set TRUE. If
     * SFN < modTime and SFN < notificationTime, modTimeReached is set TRUE to
     * cope with the wrap. */
    /* notificationTime variable is valid */
    Boolean                             notificationTimeValid;
    /* notification time */
    Int16                               notificationTime;
}
UrrSirBcchModInfo;

/* type for storage of SIB in encoded form to save re-reading it */
typedef struct UrrSirEncodedSibStoreEntryTag {
    Int16                               dataLength;
    Int8                                *data_p;
}
UrrSirEncodedSibStoreEntry;

typedef struct UrrSirEncodedSibStoreTag {
    UrrSirEncodedSibStoreEntry          mib;
    UrrSirEncodedSibStoreEntry          sb1;
    UrrSirEncodedSibStoreEntry          sb2;
    UrrSirEncodedSibStoreEntry          sib1;
    UrrSirEncodedSibStoreEntry          sib2;
    UrrSirEncodedSibStoreEntry          sib3;
    UrrSirEncodedSibStoreEntry          sib4;
    UrrSirEncodedSibStoreEntry          sib5;
    UrrSirEncodedSibStoreEntry          sib6;
    UrrSirEncodedSibStoreEntry          sib7;
    UrrSirEncodedSibStoreEntry          sib11;
    UrrSirEncodedSibStoreEntry          sib11bis;
    UrrSirEncodedSibStoreEntry          sib12;
    UrrSirEncodedSibStoreEntry          sib15;
    UrrSirEncodedSibStoreEntry          sib15_1;
    UrrSirEncodedSibStoreEntry          sib15_4;
    UrrSirEncodedSibStoreEntry          sib15_5;
    UrrSirEncodedSibStoreEntry          sib18;
    UrrSirEncodedSibStoreEntry          sib19; 
}
UrrSirEncodedSibStore;

#if ! defined (UPGRADE_EXCLUDE_2G)
typedef struct UrrSirDualModeDataTag
{
    /* variables for dual mode UMTS system information acquisition in GSM */

    /* stores for data passed from and to the GSM stack */
    Boolean                         plmnRequired;
    Int8                            numberOfMultiplePlmnIdentities;
    UPLMN_Identity                  multiplePlmnIdentity[MAX_PLMN_IN_CELL];
    Boolean                         configIdRequired;

    Boolean                   cellIdentityPresent;
    Boolean                   lacPresent;
    Boolean                   racPresent;
    Boolean                   csgInd;
    Boolean                   csgIdentityPresent;
    Int32                     cellIdentity;  //bit string 28, from sib3
    Int16                     lac;           //bit string 16, from sib1
    Int8                      rac;           //bit string 8,   from sib1
    Int32                     csgIdentity;   //bit string 28, from sib3    

    /* flag to allow SIR to tell if sys info came from GSM stack */
    Boolean                         umtsInfoFromGsm;

    /* the GSM specs indicate that predef configs must be re-read every hour. */
    /* This flag is used to decide if starting to re-read                     */
    Boolean                         reReading;

    /* stores for SIB16 information */
    Sib16AcquisitionState           sib16AcquisitionState;
    Boolean                         sb1Scheduled;
    Boolean                         sb2Scheduled;
    Boolean                         sb1Acquired;
    Boolean                         sb2Acquired;
    ScheduledPredefConfigInfo       scheduledPredefConfigs;
    ScheduledPredefConfigInfo       savedPredefConfigScheduling;

// start CQ0080536
#if defined (UPGRADE_CSG1)  || defined (UPGRADE_PLMS_SIB5)// CQ00067091 start
	Boolean 				mibPresent; //CQ00072330  
#endif
#if defined (UPGRADE_CSG1) 
	Boolean					sib20Present; //CQ00072330  
	Boolean					sib3Present;//CQ00072330  
	/*store for SIB20 */
	Boolean					hnbNamePresent;
	HnbNameStr				hnbName;
#endif // end CQ00067091
#if defined (UPGRADE_PLMS_SIB5)
	Boolean							sib5Present;
	Boolean 						freqBandIndicatorPresent;
	FddBandsMask							freqBandIndicator; /*CQ00084613 - modify */
#endif // end CQ00067091
// end CQ0080536
	
}
UrrSirDualModeData;
#endif

typedef union PendedInfoTag
{
    UrrInternalSignalBcchModInfo    pendedBcchModInfo;
    Boolean                         pendedBchOn;
}
PendedInfo;

/* ********** begin */
typedef enum sibSegmentTag
{
    NOSEG,
    FIRSTSEG,
    FIRSTSHORTSEG,
    SUBSEQUENTSEG,
    LASTSEGSHORT,
    COMPLETELISTSEG,
    COMPLETESEG,
    LASTSEG
}
sibSegment;


typedef struct segNodeTag
{
   USIB_Type            sibType;
   sibSegment           segType;
   Int16                sfn;
   USegmentIndex        segIndex;
   USegCount            seg_Count;
   Int16                length;
   Int8                 *data_p;
   struct segNodeTag    *pre_p;
   struct segNodeTag    *next_p;
}
segNode;

/* ********** end */


typedef struct UrrSirDataTag
{
    /* pointer to storage for SIB segments */
    UrrSirBlockSegmentStoreInfo     *servSiBlockSegmentInfo_p;
    /* pointer to storage for SIB segments */
    UrrSirBlockSegmentStoreInfo     *neighSiBlockSegmentInfo_p;
    /* pointer to storage for reassembled segmented SIB */
    Int8                            *siBlockReassembledInfo_p;
    /* total bit-length of reassembled segmented SIB */
    Int16                           siBlockReassembledLength;
    /* segmented SIB ready to process */
    UrrSirBlockSegmentStoreInfo     *segmentedSibToProcess_p;
    /* single complete SIB to process */
    UCompleteSIB                    *singleCompleteSibToProcess_p;
    /* instance of SIB if multiple instance SIB */
    Int8                            singleCompleteSibInstance;
    /* number of complete SIBs in list */
    Int8                            multipleCompleteSibsToProcess;
    /* list of complete SIBs to process */
    UCompleteSIBshort               *multipleCompleteSibsToProcessArray_p[maxSIBperMsg];
    /* array of instances for multiple instance SIBs */
    Int8                            multipleCompleteSibInstanceArray[maxSIBperMsg];
    /* indicates current state of cell selection process */
    UrrSirCellSelectionState        cellSelectionState;
    /* points to system information for the serving cell */
    UrrSirDatabase                  *servingCellInfo_p;
    /* points to saved system information database */
    UrrSirDatabase                  *savedServingCellInfo_p;
    /* points to system information for neighbour cells */
    UrrSirDatabase                  *neighbourCellInfo_p;
    UrrSirBcchModInfo               bcchMod;
    PendedInfo                      pendedReqInfo;
    Boolean                         debugSysInfoInd;

    /* flag to disable sys info handling in CELL_DCH state */
    Boolean                         cellDchDisable;

#if ! defined (UPGRADE_EXCLUDE_2G)
    UrrSirDualModeData              *dualModeData_p;
#endif
    AcquiredPredefConfigStore       *acquiredPredefConfigs_p;

    /* storage for SIBs in encoded form to prevent them having to be re-read */
    SibStorageState                 sibStorageState;
    Boolean                         cellSelected;
    UrrSirEncodedSibStore           servEncodedSibStore;
    UrrSirEncodedSibStore           neighEncodedSibStore;

    /* flag for SIR to keep track of phys chan reconfigurations */
    Boolean                         reconfigCellDchToCellDch;

    /* flag for SIR to keep track of system information updates to allow it to
     * inform other sub-processes when system information is being re-read due
     * to a modification by the network and when this has finished. This is
     * only relevant to serving cell system information */
    Boolean                         bcchModReadingSibs;
    Boolean                         bcchModStartSent;

#if ! defined (UPGRADE_EXCLUDE_2G)
    Boolean                         intSigCellSelectIndReceived;
    Boolean                         intSigCellSelectIndSuccess;
#endif  /* UPGRADE_EXCLUDE_2G */

    /* BCH on/off state machine variables */
    BchOnOffState                   bchOnOffState;
    KiUnitQueue                     phyConfigIntQueue;
    KiUnitQueue                     phyConfigExtQueue;
    KiUnitQueue                     ncellReadQueue;
    Boolean                         urrSirBackOnCellCalled;





    /* flags to prevent decoding of system information after CSR has selected a
     * new cell but before SIR is told that it has been successfully selected */
    Boolean                         cphyFindCellReqSent;
    Boolean                         cphyNextCellReqSent;
    Boolean                         cphyDrxFindCellReqSent;

    /* flag to prevent SIB7 timer expiry from being handled after leaving
     * CELL_DCH and before a cell has been selected moving to CELL_FACH */
    Boolean                         dchToFachNotCampedOnCellYet;

    /* flag to prevent SIB7 timer expiry from being handled when in the NO_CELL
     * state and before a cell has been selected in CELL_FACH */
    Boolean                         noCellAndNotCampedOnCellYet;

    /* flag to keep track of the fact that the RRC is still in the NO_CELL window
     * even though the SIR state does not reflect this due to SIB acquisition.
     * Set when UrrSirNoCell is called, reset when UrrSirBackOnCell is called or
     * URR_INTERNAL_SIGNAL_CAMPED_ON_CELL is received */
    Boolean                         inNoCellWindow;

    /* Variables to protect RRC from handling bchCnf for unrequested PSC and UARFCN */
    UPrimaryScramblingCode          bchRequestedPsc;
    UUARFCN                         bchRequestedUarfcn;
    SirProc                         abortPlmnSource;
    UINT8                           numOfIgnoredBcchInfoSignals;

    /* Variables to hold the type of EXT SIB currently received by SIR */
    UrrSirSibTypeExtExt2            sib_TypeExt;
    Boolean                         bcchEnqueue;   /* **********, added */

    /* **********, BEGIN */
	/* ********** START
     indicates whether 4G->3G CSFB is ongoing and 
	 1. sys info container is N/A and AT&T flag is TRUE (need to skip SIB11, SIB11 bis and SIB12)
	 or
	 2. sys info container is available (R9 CSFB)
	 ********** END	 */
    Boolean                         csfbInProgress; 
    
    //********** begin
    Boolean                         csfbOccured; 
    /*indicates whether csfb had occured even after csfbInProgress is being cleared when SIR processes campedOnCell signal */
    //********** end
    
    /* indicates whether dummy SIB11 is used instead of real one from NW / container */
    Boolean                         dummySib11InUse;
    /* **********, END */
    
    /*flag to indicate if system information container exist */
    Boolean                         sysInfoContainerInd;
/* ********** begin */
    segNode                         *segChainEntry;
    Boolean                         chainLocked;
/* CQ00073451 begin */
    Int32                           chainLength;
/* CQ00073451 end */
/* ********** end*/
#if defined (UPGRADE_CSG1) || defined (UPGRADE_PLMS_SIB5)//CQ00072330 start
	Boolean						readBchFromOtherRat;
/*CQ00084613 - start */	
    Int32 umtsSibsRequested;
    Int32 umtsSibsReceivedForOtherRat;
    Boolean getUmtsSibsForPlms;
/*CQ00084613 - end */
#endif//CQ00072330 end

/*CQ00078728 begin*/
#if defined (RRC_SIR_OPTIMIZATION)
    Boolean						waitingforSib11Sib12DMCR;
#endif
/*CQ00078728 end*/

    /* CQ00085704 add begin */
    Int32    deferredSibs;      // bitmap of deferred SIBs
    Int32    deferredExtSibs;   // bitmap of deferred extension SIBs
    Boolean  sib19CanBeDeferred;   // indicates if it's allowed to defer SIB19
	/* CQ00085704 add end */
}
UrrSirData;

typedef struct UrrSirStateInfoTag
{
    USIB_Type           sib_Type;
    UrrSirAreaScope     areaScope;
    UrrModeState        blockValid;
}
UrrSirStateInfo;

typedef struct UrrSirStateExtInfoTag
{
    UrrSirSibTypeExtExt2      sib_TypeExt;
    UrrSirAreaScope     areaScope;
    UrrModeState        blockValid;
}
UrrSirStateExtInfo;


/*
    Auxilary table for the delayed SIBs mechanism:
    ==================================================
    For each SIB which should arrive after other SIBs
    Add a row in this table, containing:

    USIB_Type           sib_Type                    - SIB type
    UrrSirSibTypeExtExt2      sib_TypeExt;                - EXT SIB type
    Int32               dependencyBitmap;           - SIBs which should arrive before this SIB
    Int32               dependencyExtBitmap;        - EXT SIBs which should arrive before this SIB
    Int8                *data_p;                    - Leave as PNULL. Will be used for saving encoded data
    Int16               data_length;                - Leave as 0. Will be used for saving encoded data
    Boolean             delayed;                    - Leave as FALSE. Will be used when delaying SIB
    void                *sysInfo_p;                 - Leave as PNULL. Will be used when decoding SIB data
    Int16               sysInfoSize;                - sizeof SIB struct.
    PerDec_SysInfoGeneric sysInfoDecFunc;           - SIB decoding function
    RouteSysInfoTypeGeneric routeSysInfoFunc;       - SIB routing function

    Example for SIB12 which depends on SIB11 and SIB11bis:

    {USIB_Type_systemInformationBlockType12, (UrrSirSibTypeExtExt2)0, URR_SIR_SYS_INFO_11_MASK,
                                                                URR_SIR_EXT_SYS_INFO_11_BIS_MASK, PNULL, 0, FALSE,
        PNULL, sizeof (USysInfoType12), (PerDec_SysInfoGeneric) PerDec_USysInfoType12, (RouteSysInfoTypeGeneric) RouteSysInfoType12},

*/

typedef PerError (*PerDec_SysInfoGeneric)(PerBuffer *perBuffer, void *value_p);
typedef void (*RouteSysInfoTypeGeneric)(void *sysInfoType_p, UrrSirCellType cellType);

typedef struct UrrSirDependenciesInfoTag
{
    USIB_Type           sib_Type;
    UrrSirSibTypeExtExt2      sib_TypeExt;
    Int32               dependencyBitmap;
    Int32               dependencyExtBitmap;
    Int8                *data_p;
    Int16               data_length;
    Boolean             delayed;
    void                *sysInfo_p;
    Int16               sysInfoSize;
    PerDec_SysInfoGeneric sysInfoDecFunc;
    RouteSysInfoTypeGeneric routeSysInfoFunc;
}
UrrSirDependenciesInfo;


typedef enum UrrSirTimerIdentityTag
{
    /* N.B. these values are used to index table urrSirTimers so value should
     * not be changed arbitrarily */
    URRC_SIR_SERV_UPDATE_TIMER_SIB7,
    URRC_SIR_SERV_UPDATE_TIMER_SIB14,
    URRC_SIR_SERV_UPDATE_TIMER_SIB17,
    URRC_SIR_SERV_SIX_HOUR_TIMER_MIB,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SB1,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SB2,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB1,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB2,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB3,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB4,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB5,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB6,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB7,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB11,
//    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB11BIS,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB12,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB13,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB13_1,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB13_2,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB13_3,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB13_4,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB14,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB15,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB15_1,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB15_2_INSTANCE_0,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB15_2_INSTANCE_1,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB15_2_INSTANCE_2,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB15_2_INSTANCE_3,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB15_2_INSTANCE_4,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB15_2_INSTANCE_5,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB15_2_INSTANCE_6,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB15_2_INSTANCE_7,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB15_2_INSTANCE_8,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB15_2_INSTANCE_9,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB15_2_INSTANCE_10,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB15_2_INSTANCE_11,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB15_2_INSTANCE_12,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB15_2_INSTANCE_13,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB15_2_INSTANCE_14,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB15_2_INSTANCE_15,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB15_3_INSTANCE_0,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB15_3_INSTANCE_1,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB15_3_INSTANCE_2,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB15_3_INSTANCE_3,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB15_3_INSTANCE_4,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB15_3_INSTANCE_5,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB15_3_INSTANCE_6,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB15_3_INSTANCE_7,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB15_3_INSTANCE_8,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB15_3_INSTANCE_9,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB15_3_INSTANCE_10,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB15_3_INSTANCE_11,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB15_3_INSTANCE_12,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB15_3_INSTANCE_13,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB15_3_INSTANCE_14,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB15_3_INSTANCE_15,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB15_4,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB15_5,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB16_INSTANCE_0,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB16_INSTANCE_1,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB16_INSTANCE_2,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB16_INSTANCE_3,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB16_INSTANCE_4,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB16_INSTANCE_5,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB16_INSTANCE_6,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB16_INSTANCE_7,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB16_INSTANCE_8,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB16_INSTANCE_9,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB16_INSTANCE_10,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB16_INSTANCE_11,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB16_INSTANCE_12,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB16_INSTANCE_13,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB16_INSTANCE_14,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB16_INSTANCE_15,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB17,
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB18,

    URRC_SIR_SERV_MIB_TIMEOUT_TIMER,
    URRC_SIR_NEIGH_MIB_TIMEOUT_TIMER,

    URRC_SIR_SERV_SIB7_TIMEOUT_TIMER,

    URRC_SIR_SERV_BCCH_MOD_TIMEOUT_TIMER,

    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB11BIS, /* CQ00036126, modified */
    URRC_SIR_SERV_SIX_HOUR_TIMER_SIB19, 

    URRC_SIR_MAX_TIMERS
}
UrrSirTimerIdentity;
#endif
 /* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/api/inc/ucstypes.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2007/03/19 13:59:30 $
 **************************************************************************
 * File Description:
 *
 *    Type definitions for UCS (CSDI) which may be required in all UCS modules.
 **************************************************************************/

#if !defined (UCSTYPES_H)
#define       UCSTYPES_H
/***************************************************************************
* Nested Include Files
***************************************************************************/
#include <auc_ps_if.h>

/** \addtogroup 3G_CSDI_AMR
 * @{
 */

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/

/* See 26.101-320 for these numbers */

#define UCS_AMR_MAX_NUM_A_BITS API_MAX_CLASS_A_BITS //81u  //81, 72
#define UCS_AMR_MAX_NUM_B_BITS API_MAX_CLASS_B_BITS //405u//103, 405
#define UCS_AMR_MAX_NUM_C_BITS API_MAX_CLASS_C_BITS //60u  //60, 0


//#define UCS_AMR_NUM_SID_A_BITS 39u
#define UCS_AMR_MAX_NUM_A_BYTES BITS_TO_INT8S(UCS_AMR_MAX_NUM_A_BITS)
#define UCS_AMR_MAX_NUM_B_BYTES BITS_TO_INT8S(UCS_AMR_MAX_NUM_B_BITS)
#define UCS_AMR_MAX_NUM_C_BYTES BITS_TO_INT8S(UCS_AMR_MAX_NUM_C_BITS)
/* UCS_AMR_MAX_NUM_BYTES must be the largest of the three */
#define UCS_AMR_MAX_NUM_BYTES UCS_AMR_MAX_NUM_B_BYTES

#define UCS_RTFDP_MAX_NUM_BITS 640u
#define UCS_RTFDP_MAX_NUM_BYTES BITS_TO_INT8S(UCS_RTFDP_MAX_NUM_BITS)
#define UCS_MAX_URLC_SDU_OFFSET_BYTES 4

#define UCS_DATA_MAX_NUM_BYTES (UCS_RTFDP_MAX_NUM_BYTES > UCS_AMR_MAX_NUM_BYTES ? UCS_RTFDP_MAX_NUM_BYTES : UCS_AMR_MAX_NUM_BYTES)

#define UCS_MAX_CSD_SDUS_PER_TTI 4

/***************************************************************************
*   Macro Functions
***************************************************************************/

/***************************************************************************
 * Types
 ***************************************************************************/

/**\enum UcsAmrBitTypes
 * \brief Type of AMR bits 
*/
typedef enum UcsAmrBitTypesTag
{
    UCS_AMR_A_BITS = 0,
    UCS_AMR_B_BITS = 1,
    UCS_AMR_C_BITS = 2
}
UcsAmrBitTypes;

/* the ex-asn.1 type has ansi 41 clutter, which we don't want. */
/**\typedef UcsRabIdentity
* \brief Identity used between UMAC and CSDI to identify the circuit switched data flow.
*/
typedef Int8 UcsRabIdentity;

/**\enum UcsRabType
* \brief Type Rab contra part 
*/
typedef enum UcsRabTypeTag
{
    UCS_RAB_TYPE_AMR,
    UCS_RAB_TYPE_CSD
}
UcsRabType;

/**\enum UcsAmrDataStatus
 * \brief Frame status 
*/
typedef enum UcsAmrDataStatusTag
{
    UCS_AMR_DATA_STATUS_OK,
    UCS_AMR_DATA_STATUS_CRC_ERROR,
    UCS_AMR_DATA_STATUS_NO_FRAME,
    UCS_AMR_DATA_STATUS_UNKNOWN
}
UcsAmrDataStatus;

/**\enum ScenarioAmrState
 * \brief AMR current state
*/
typedef enum ScenarioAmrStateTag
{
    SAMR_IDLE, /**<no configured setup for amr*/
    SAMR_READY,/**<amr setup configured, ready to receive a voice call, ready to establish a voice call or now in a voice call*/
    SAMR_SUSPEND,/**<amr call is suspended (e.g: a handover scenario)*/
    SAMR_DISCONNECTED/**<amr call is disconnected*/
}
ScenarioAmrState;

/**<AMR global environment structure*/
typedef struct UcsAmrEntityTag
{
	/*
	Current AMR state = IDLE\READY\SUSPEND\DISCONNECT
	------------------------------------------------
	
	SAMR_IDLE - Initialized state. No RAB configured.
	--Can move to this state from:
			-SAMR_READY 		- upon AmrReleaseReq
			-SAMR_SUSPEND 		- upon AmrReleaseReq

	SAMR_READY - RAB is configured (might be active\connected).
	--Can move to this state from:
		-SAMR_IDLE 				- upon AmrConfigReq
		-SAMR_SUSPEND			- upon AmrConnectionEstablish
		-SAMR_DISCONNECTED	- upon AmrConnectionEstablish

	SAMR_SUSPEND - Active call, RAB is configured and connected. No data processing is possible before releasing suspend state.
	--Can move to this state from:
		-SAMR_READY 			- upon AmrSuspendReq

	SAMR_DISCONNECTED - RAB is configured, but not connected\active.
	--Can move to this state from:
		-SAMR_READY 			- upon AmrConnectionClose
		-SAMR_SUSPEND			- upon AmrConnectionClose
	*/
	ScenarioAmrState state; /**<Current AMR state = IDLE\READY\SUSPEND\DISCONNECT*/
	/*
	Current RAB ID for AMR speech.
	Configured in AMR Configuration (SIG_CCSD_CONFIG_REQ)
	*/
		
	UcsRabIdentity   rabIdentity; /**<Current RAB ID for AMR speech. rabIdentity is the identity used between UMAC and CSDI to identify the circuit switched data flow. It is the Radio Access Bearer identity, as specified by the network for this CS service.*/	

	/*
	AMR encoder parameters.
	Configured in AMR Configuration (SIG_CCSD_CONFIG_REQ).
	*/
	Int8	encoderRate;
	Int8	codecType;

	/*
	Uplink data for AMR (speech) must be synchronized with UMAC clock (synchronized by SIG_PHY_FRAME_IND 
	received from Phy\L1 level for Uplink transmission). This means that CSDI can't send TX data whenever it wills. 
	It must wait until there is a valid clock indication from UMAC (RTS) . The clock indication from UMAC is received 
	by the SIG_CCSD_UL_TTI_IND  signal (over the CCSD interface). This signal is sent every 20ms (same time 
	that the speech data is generated), so there is no loss of data. UMAC is generating this signal for every two 
	SIG_PHY_FRAME_IND (that is received every 10ms). 
	
	For this synchronization purposes, we hold a global parameter called UlReadyToSend (changing it's value is done 
	by the UcsChangeUlTtiIndValidity function in ucsamr.c). The one rule is that we send one 
	SIG_URLC_AMR_TM_DATA_REQ for every SIG_CCSD_UL_TTI_IND. This way, every 20ms there is only one TX 
	speech message in  L2 (URLC\UMAC). 
	*/
	Boolean 		ulReadyToSend;/**<RTS indication. If true, we call send uplink data to URLC. if false, delay data until valid RTS*/

	/*
	CSDI receives TX data by the SIG_CSDI_AMR_DATA from AMR directly. Upon reception of the data from AMR we have two options:
	--	If we have a valid UlTtiIndication (UlReadyToSend=TRUE) than we send the received data to URLC.
	--	Otherwise, we can't send this data yet, so we are saving it for the next SIG_CCSD_UL_TTI_IND. 
	In the second scenario, we save the AMR uplink signal's address in delayedAmrDataReqSignal_p. 
	*/
	SignalBuffer    delayedAmrDataReqSignal;/**<Internal buffer to hold delayed uplink codec voice data*/
	
} UcsAmrEntity;


/***************************************************************************
 * Typed Constants
 ***************************************************************************/
/** @} */ //3G_CSDI_AMR 

#endif /* UCSTYPES_H */
/* End of file */

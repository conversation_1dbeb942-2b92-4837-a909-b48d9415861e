/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/tplgsm/utinc/utip.h#5 $
 *   $Revision: #5 $
 *   $DateTime: 2006/02/09 12:25:17 $
 **************************************************************************
 * File Description:
 *
 * IP datagram header definition - this is a generic definition for any
 * Internet IP packet. This file is based on the file:
 *
 * ip.h ******* (University of California at Berkeley)  3/15/88
 **************************************************************************/

#ifndef UTIP_H
#define UTIP_H

#if !defined (UTENDIAN_H)
#  include <utendian.h>
#endif

/*
Definitions for internet protocol version 4.
Per RFC 791, September 1981.
*/
#define IPVERSION        4

/*
Structure of an internet header, naked of options.

We declare ip_len and ip_off to be short, rather than u_short
pragmatically since otherwise unsigned comparisons can result
against negative integers quite easily, and fail in subtle ways.
*/

/* ip_off bits */
#define IP_DF 0x4000            /* don't fragment flag */
#define IP_MF 0x2000            /* more fragments flag */
#define IP_MAXPACKET    65535   /* maximum packet size */

/* ip_pvhl bits */
#define IP_PV 0xF0              /* protocol version mask */
#define IP_HL 0x0F              /* header length mask */

struct iphdr
{
    Int8        ip_pvhl;        /* IP protocol version and header length */
    Int8        ip_tos;         /* type of service */
    SignedInt16 ip_len;         /* total length */
    Int16       ip_id;          /* identification */
    SignedInt16 ip_off;         /* fragment offset field */
    Int8        ip_ttl;         /* time to live */
    Int8        ip_p;           /* protocol */
    Int16       ip_sum;         /* checksum */
    Int32       ip_src;         /* source IP address */
    Int32       ip_dst;         /* dest IP address */
};

#endif

/* END OF FILE */

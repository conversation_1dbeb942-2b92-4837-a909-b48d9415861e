
/*------------------------------------------------------------
(C) Copyright [2006-2010] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/******************************************************************************
 * File Name:   urlalloc.h
 *
 *
 * File Description: URLC allocation implementation header file
 *
 *****************************************************************************/

#if !defined (URLALLOC_H)
#define       URLALLOC_H

/******************************************************************************
 * Include Files
 *****************************************************************************/

/* Define the macros so that the Linked List utility functions use the URLC
 * local allocator and deallocator functions rather than calling KiAllocMemory
 * and KiFreeMemory. The local allocator/deallocator routines are much quicker
 * and reduce system loading. The definition of these macros must be done
 * before utllist.h is included, otherwise, utllist.h will use the default
 * allocator/deallocator routines. */
#if !defined (ON_PC)
#ifdef UPGRADE_3G_HSDPA   
#define UTLL_ALLOC_MEMORY(sIZE, nODE_PP) UrlAllocMemory (sIZE, nODE_PP)
#define UTLL_ALLOC_ZERO_MEMORY(sIZE, nODE_PP)   \
    {UrlAllocMemory (sIZE, nODE_PP);            \
     DevAssert(*nODE_PP != PNULL);              \
     memset(*nODE_PP ,0, sIZE);                 \
    }
#define UTLL_FREE_MEMORY(nODE_PP) UrlFreeMemory (nODE_PP)
#else
#define UTLL_ALLOC_MEMORY(sIZE, nODE_PP) KiAllocMemory (sIZE, nODE_PP)
#define UTLL_ALLOC_ZERO_MEMORY(sIZE, nODE_PP)   KiAllocZeroMemory (sIZE, nODE_PP)
#define UTLL_FREE_MEMORY(nODE_PP) KiFreeMemory (nODE_PP)
#endif
#endif /* ON_PC */

#include <utllist.h>


/******************************************************************************
 * Macros
 *****************************************************************************/
//#define URL_ALLOC_DEBUG

/******************************************************************************
 * Function Prototypes
 *****************************************************************************/
void  UrlAllocInit(void);
void  UrlAllocMemory(Int32 size, void **mem_pp);
void  UrlFreeMemory(void **mem_pp);
#if defined URL_ALLOC_DEBUG
void  UrlAllocPrintStatistics(void);
#endif /* URL_ALLOC_DEBUG */

#endif /* URLALLOC_H */
    

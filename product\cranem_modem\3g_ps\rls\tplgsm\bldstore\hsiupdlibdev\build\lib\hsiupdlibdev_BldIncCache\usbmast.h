/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/devices/transports/usb.mod/pub/src/usbmast.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/24 10:16:13 $
 **************************************************************************
 *  File Description :
 *    Data types etc for USB Mass Storage Class interface application.
 **************************************************************************/
#if defined(UPGRADE_USB)
#if defined(USB_MASS_STORAGE_BULK_ONLY_INTERFACE)

#ifndef USBMAST_H
#define USBMAST_H

/****************************************************************************
 * Nested Include Files
 ****************************************************************************/
#include <usbappif.h> /* for dataQuantum type */

/****************************************************************************
 * Exported functions - Mast application (within USB_TARGET_TASK)
 ****************************************************************************/
extern void usbMastDevAppInitialise( void );
extern void usbMastConfigure( void );
extern void usbMastInitialise( void );
extern void usbMastGetMaxLunIndex( Int8* usbMastMaxLunIndex );

extern void usbMastDataReceived( UsbDataQuantum dataQuantum );
extern void usbMastStateMachineTxInd( void );

extern void usbMastRxFlushComplete( void );

extern void usbMastRxDataPending(Int8 logicalEndpoint, Int16 length);

extern void usbMastReceiveBufferFreeSpace(Int8 logicalEndpoint, Int16 freeSpace);

extern void usbMastResetApplication(TaskId taskId);

extern Int32 usbMastMaxOutTransfer(Int8 logicalEndpoint);

/****************************************************************************
 * Exported functions - Mast device (within USB_TASK)
 ****************************************************************************/
extern Boolean usbMastClassDeviceRequest( Int8 logicalEndpoint, Int8* setupPkt );


#endif /* USBMAST_H */
#endif /* USB_MASS_STORAGE_BULK_ONLY_INTERFACE */
#endif /* UPGRADE_USB */


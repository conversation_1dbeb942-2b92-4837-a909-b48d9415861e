/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/***************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 ***************************************************************************
 * $Id: //central/main/wsd/sys/gki.mod/pub/src/unwarminder.h#2 $
 * $Revision: #2 $
 * $DateTime: 2007/03/19 13:59:30 $
 **************************************************************************/
/** \file
 * Interface to the ARM stack unwinding module.
 **************************************************************************/

#ifndef UNWARMINDER_H
#define UNWARMINDER_H

/***************************************************************************
 * Nested Include Files
 **************************************************************************/

#include <system.h>

/***************************************************************************
 * Manifest Constants
 **************************************************************************/

/** \def ENABLE_ARM_STACK_UNWIND
 * \ingroup CfgGkiAssert
 * Enable unwinding of the call stack when the system stops at an assertion.
 * If this define is set, the call stack will be unwound and added to the
 * assertion report if the system is stopping at a failed assertion.
 *
 * \see \ref TechArmStackUnwind "ARM Stack Unwinding on Assertion Fail"
 */

#if defined(ENABLE_ARM_STACK_UNWIND)

/** \def UNW_DEBUG
 * If this define is set, additional information will be produced while
 * unwinding the stack to allow debug of the unwind module itself.
 */
/* #define UNW_DEBUG 1 */

/***************************************************************************
 * Type Definitions
 **************************************************************************/

/** Possible results for UnwindStart to return.
 */
typedef enum UnwResultTag
{
    /** Unwinding was successful and complete. */
    UNWIND_SUCCESS = 0,

    /** More than UNW_MAX_INSTR_COUNT instructions were interpreted. */
    UNWIND_EXHAUSTED,

    /** Unwinding stopped because the reporting func returned FALSE. */
    UNWIND_TRUNCATED,

    /** Read data was found to be inconsistent. */
    UNWIND_INCONSISTENT,

    /** Unsupported instruction or data found. */
    UNWIND_UNSUPPORTED,

    /** General failure. */
    UNWIND_FAILURE,

    /** Illegal instruction. */
    UNWIND_ILLEGAL_INSTR,

    /** Unwinding hit the reset vector. */
    UNWIND_RESET,

    /** Failed read for an instruction word. */
    UNWIND_IREAD_W_FAIL,

    /** Failed read for an instruction half-word. */
    UNWIND_IREAD_H_FAIL,

    /** Failed read for an instruction byte. */
    UNWIND_IREAD_B_FAIL,

    /** Failed read for a data word. */
    UNWIND_DREAD_W_FAIL,

    /** Failed read for a data half-word. */
    UNWIND_DREAD_H_FAIL,

    /** Failed read for a data byte. */
    UNWIND_DREAD_B_FAIL,

    /** Failed write for a data word. */
    UNWIND_DWRITE_W_FAIL
}
UnwResult;

/** Type for function pointer for result callback.
 * The function is passed two parameters, the first is a void * pointer,
 * and the second is the return address of the function.  The bottom bit
 * of the passed address indicates the execution mode; if it is set,
 * the execution mode at the return address is Thumb, otherwise it is
 * ARM.
 *
 * The return value of this function determines whether unwinding should
 * continue or not.  If TRUE is returned, unwinding will continue and the
 * report function maybe called again in future.  If FALSE is returned,
 * unwinding will stop with UnwindStart() returning UNWIND_TRUNCATED.
 */
typedef Boolean (*UnwindReportFunc)(void   *data,
                                    Int32   address);

/** Structure that holds memory callback function pointers.
 */
typedef struct UnwindCallbacksTag
{
    /** Report an unwind result. */
    UnwindReportFunc report;

    /** Read a 32 bit word from memory.
     * The memory address to be read is passed as \a address, and
     * \a *val is expected to be populated with the read value.
     * If the address cannot or should not be read, FALSE can be
     * returned to indicate that unwinding should stop.  If TRUE
     * is returned, \a *val is assumed to be valid and unwinding
     * will continue.
     */
    Boolean (*readW)(const Int32 address, Int32 *val);

    /** Read a 16 bit half-word from memory.
     * This function has the same usage as for readW, but is expected
     * to read only a 16 bit value.
     */
    Boolean (*readH)(const Int32 address, Int16 *val);

    /** Read a byte from memory.
     * This function has the same usage as for readW, but is expected
     * to read only an 8 bit value.
     */
    Boolean (*readB)(const Int32 address, Int8  *val);

#if defined(UNW_DEBUG)
    /** Print a formatted line for debug. */
    int (*printf)(const char *format, ...);
#endif

}
UnwindCallbacks;

/***************************************************************************
 *  Macros
 **************************************************************************/

/***************************************************************************
 *  Function Prototypes
 **************************************************************************/

/** Start unwinding the current stack.
 * This will unwind the stack starting at the PC value supplied to in the
 * link register (i.e. not a normal register) and the stack pointer value
 * supplied.
 */
UnwResult UnwindStart(Int32                  spValue,
                      const UnwindCallbacks *cb,
                      void                  *data);

#endif /* ENABLE_ARM_STACK_UNWIND */


/**
\page TechArmStackUnwind ARM Stack Unwinding on Assertion Fail
\section Intro Introduction

To help debug assertion failures, a call stack unwinder has been added to the
assertion handling routine such that when the system stops at an assert, more
information can given about the possible cause of the failure.

The unwinder requires no debug tables or special builds and adds only around 4k
of code and data to the flash image.  Because of this it is well suited to
debugging embedded systems, either in the lab or the field.

\note The call stack unwinder is specific to ARM architectures, and currently
      supports ARM and Thumb mode instructions.  It does not yet support
      Thumb2.

\section Config Configuration and Output

\subsection ConfigScreen Outputting Data to Screen

The enable the unwinder, ensure that you add #ENABLE_ARM_STACK_UNWIND to your
build if it is not already present.  The system must also be configured to
stop on assertion, that is, #CONTINUE_ON_ASSERTFAIL must not be defined.
Additionally the system must be configured to check assertions, i.e.
one of #KI_CHECK_ASSERTIONS, #CHECK_ASSERTIONS or #DEVELOPMENT_VERSION must be
defined, and #KI_DONT_CHECK_ASSERTIONS must not be defined.

Then a correctly configured system asserts, you should see an array of
addresses being dumped on the screen:

\code

  ABA_MAIN#561-0(1,2,3)
  0T:0x01181088
  1T:0x01181ff0
  2T:0x0106ed2e
  3T:0x0106eb9a
  4T:0x0106ef80
  4T:0x0106e4e4
  6T:0x01180cae

  F:0007 S:0001
\endcode

As well as the normal assertion message, this is a stack back trace giving the
addressed to which control would have returned if the assert handler were to
continue.  Each line is prefixed with a hex character which is simply the stack
depth to aid copying and reporting of the assert lines.  The second digit on
each line is either an \a T or an \a T to indicate whether the return would be
to Thumb or ARM mode respectively.  The value to the right of the colon is the
return address itself, indicating the instruction to which the processor
would return.

The bottom line contains two values, the \a F value and the \a S value.  The
\a F value is simply the number of stack frames that have been unwound.  The
\a S value gives a reason for unwinding to have terminated, and is generally
only of use in debugging cases where the stack unwinder has not successfully
unwound the entire call stack.  The \a S value is of the type #UnwResult.


\subsection Interpretation Interpreting the Stack Values

The hexadecimal stack values can be decoded using a small tool called \a
addr2sym which should be present in %TPLGSM%\tools.  This tool requires
the ELF image for the failed system, as well as the addresses displayed
after the assert.  With this information the tool can examine the symbol
table in the ELF image and dump the function names for each stack frame:

\code
  C:\>addr2sym ..\500egdev.elf 0x1181088 0x1181ff0 0x106ed2e 0x106eb9a \
  0x106ef80 0x106e4e4 0x1180cae
  0: 0x01181088: assertfail() + 0x50
  1: 0x01181ff0: checkDevParam() + 0x58
  2: 0x0106ed2e: AbaHandleKeyMenuSelect() + 0x112
  3: 0x0106eb9a: AbaHandleKeyMenuScroll() + 0x6e
  4: 0x0106ef80: AbaHandleKeyPress() + 0xbc
  5: 0x0106e4e4: AbaHandleDmKeypadMmiInd() + 0x1c
  5: 0x01180cae: afshTask() + 0x2de
\endcode

The output displays the function names against the addresses and stack depth
to help reduce the risk of error if copying values from a failed system's
screen.

\note To ensure that the function names that are listed are accurate, it is
      important to use the ELF file that corresponds to the image on the
      target.


\subsection ConfigRamLogging Unwinding with RAM logging

If RAM logging is enabled, stack unwinding information will additionally be
reported in the RAM log as well as any other configured reports.  If
\a rlggui.exe is used to collect the RAM log from the target, an ELF file
can be specified in the GUI such that the call stack is automatically decoded
with the rest of the RAM log.  This avoids the need to copy addresses from
the target's screen and simplifies interpretation.


\section Caveats Caveats

There are a couple of limitations and oddities that arise mainly from the
fact that the unwinder interprets the function return path as opposed to
working backwards to find how the functions were entered.  With a little
knowledge of what these are, it is usually possible to recognise when these
occur and interpret the stack unwind results correctly.


\subsection CaveatsTail Tail Call Optimisation

Sometimes functions that were on the function call path can be omitted from
the unwinding output due to optimisation of tail calling.  The following
functions show such a case where tail calling may cause \a FuncB() to be
omitted from the unwound stack report.

\code
  FuncA()
  {
    DevParam(1,2,3);
  }

  FuncB()
  {
     ....
     FuncA(); // Tail call
  }

  FuncC()
  {
    ...
    FuncB();
    ...
  }
\endcode

FuncB() tail calls FuncA(), and as such the compiler is able to speed up the
calling and returning to or from FuncA() by simply entering \a FuncA() with
the link register containing the return address to \a FuncC().  This means that
execution would pass directly from \a FuncA() to \a FuncC() if \a DevParam()
were to continue, and since the unwinder follows the return path, \a FuncB()
would be omitted from the output.

Tail calling can only be optimised by the compiler when the functions either
return nothing, \a void, or one the calling function directly returns the value
from the callee.  In the given example, this means that \a FuncA() either
returns nothing, or the call to \a FuncA() would be in the form of a return
statement:

\code
    int FuncB()
    {
        ....
        return FuncA(); // Tail call
    }
\endcode


\subsection CaveatsInline Inlined Functions

Inlined functions will disappear from the unwound stack output as they do not
generate a stack frame.  Since this scheme uses no debug tables, it is
impossible to detect when an inlined function is a part of the execution
history.


\subsection CaveatsCorrupt Corrupt Stack

There maybe a situation where the call stack is corrupt and therefore
impossible for the unwinder to interpret.  In such a case it maybe that the
stack is only partially unwound, or that unwinding stops before any of
the stack is reported.

The unwinder is written to be robust with respect to flagging erroneous stacks
or instructions.


*/

#endif /* UNWARMINDER_H */

/* END OF FILE */

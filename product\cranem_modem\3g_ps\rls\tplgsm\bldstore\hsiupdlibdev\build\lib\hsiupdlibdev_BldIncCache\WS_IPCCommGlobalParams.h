/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/************************************************************************/
/*                                                                      */
/* Title: IPC Comm. pacakge header file                                 */
/*                                                                      */
/* Filename:  IPCCommGlobalParams.h                                     */
/*                                                                      */
/* Authors: <AUTHORS>
/*                                                                      */
/* Target, subsystem: Common Platform, HAL                              */
/************************************************************************/
#ifndef _WS_IPCCommGlobalParams_H_

  #define _WS_IPCCommGlobalParams_H_
/*----------------------------------------------------------------------*/
#include "osa.h"
#include "osa_types.h"
#include "WS_IPCComm.h"
#include "WS_IPCCommConfig.h"
#include "WS_HwAcs.h"


#ifdef IPCCOMM_GLOBAL_PARAMS_TCM
	#define EXTERN
#else
	#define EXTERN extern
#endif


#define BLANK				0L //this defines a non-pointer parameter to be empty, blank

/***************************** Global Parameters for TCM ************************************/

EXTERN IPC_Status _packageStatus;           // IPC Comm package 'Status' parameter
EXTERN IPC_Config _packageConfiguration;	// IPC Comm package 'Configuration' parameter
#ifdef PLAT_TEST
EXTERN OSAFlagRef  IPCEvent;        // OS event used for Handshake
#else
EXTERN NU_EVENT_GROUP  IPCEvent;        // OS event used for Handshake
#endif


EXTERN UINT32 _CPU2DSPBufferCurrentPtr; //running index (current free address) in the TX command cyclic buffer (in shared memory)
EXTERN UINT8  _RDIntRequiredCounter;    //a counter for the next required RD_INT to be sent to the DSP
EXTERN UINT8  _RDIntRequiredTempCounter;//a temp counter for the next required RD_INT to be sent to the DSP - while TX comm paused
EXTERN BOOL   _RDIntIsAllowed;          //ACK INT had been received from the DSP, and therefore, the next interrupt could be sent

EXTERN UINT32 _DSP2CPUBufferCurrentPtr; //running index (next message address) in the RX message cyclic buffer (in shared memory)
EXTERN UINT32 _DSP2CPUBufferNextPtr;    //next potential message address in the cyclic buffer
EXTERN UINT32 _DSP2CPUPollValue;        //Image of the POLL register, which should be read only in the ACK interrupt

EXTERN UINT32 _DSP2CPUNumOfMsgsToPoll;  //when working in "Polling" mode, this counter represents the number of msgs waiting to be polled


#undef EXTERN

#ifdef IPCCOMM_GLOBAL_PARAMS
    #define EXTERN
#else
    #define EXTERN extern
#endif

/***************************** Global Parameters ************************************/
EXTERN UINT32 _DSP2CPUMsgsToPollPtrArray[MAX_NUM_OF_MSGS_TO_POLL];   //array of pointers to the messages to be polled (cyclic buffer - msgs may not be consecutive

EXTERN volatile BOOL  _dataChannelAvailable[IPC_MAX_DATA_CH_NUM];

EXTERN UINT8  _dataAckInterruptVectorSet[8];

EXTERN UINT8  _dataAckInterruptVectorClear[8];

// Database of the IPC interrupt sources
EXTERN UINT8  _IPCommInterruptSource[MAX_IPC_INT_SRC];
EXTERN UINT8  _IPCommInterruptCounter[MAX_IPC_INT_SRC];
EXTERN UINT8  _IPCommHisrCounter[MAX_IPC_INT_SRC];


/********* Interrutps Event Queue (for transferring parameters from the LISR to the HISR ******************/

#define IPC_QUEUE_DEPTH    10

#define QUEUE_CREATE_EXTERN(name,type,depth) \
EXTERN type  name##Queue[depth]; \
EXTERN UINT16 name##Queue##Depth; \
EXTERN UINT16 name##Queue##Rear, name##Queue##Front;

/*#define QUEUE_CREATE(name,type,depth) \
type  name##Queue[depth]; \
UINT16 name##Queue##Depth=depth;                \
UINT16 name##Queue##Rear=0, name##Queue##Front=0;       */

#define QUEUE_PUT(name,value)                   \
name##Queue[name##Queue##Rear]= value;          \
name##Queue##Rear++;                            \
if(name##Queue##Rear >= name##Queue##Depth)     \
    name##Queue##Rear=0;

#define QUEUE_GET(name)                         \
name##Queue[name##Queue##Front];                \
name##Queue##Front++;                           \
if(name##Queue##Front >= name##Queue##Depth)    \
    name##Queue##Front=0;

#define QUEUE_EMPTY(name)  ((name##Queue##Front==name##Queue##Rear) ? TRUE: FALSE)

#define QUEUE_INIT(name,depth)  name##Queue##Front=0; name##Queue##Rear=0; name##Queue##Depth=depth;

/****************************************************************************************/

// create the ISR event-queue to transfer parameters from the LISR to the HISR
QUEUE_CREATE_EXTERN(ipc,UINT32,IPC_QUEUE_DEPTH)

/******************************* CMD MSG DATA DEFINITONS ******************************************/

#define CMD_MSG_HEADER_SIZE     4         // bytes
// The longest message as of spec. 0.851 is 218 bytes; best to use 512 byte limit (shared memory buffer size)
#define CMD_MSG_PIPE_SIZE       0x400//0x200     // bytes

#define DATA_NO_COPY_HEADER_SIZE                12 //bytes
#define DATA_COPY_TO_KNOWN_DEST_HEADER_SIZE     16 //bytes
#define DATA_COPY_TO_UNKNOWN_DEST_HEADER_SIZE   12 //bytes

/*************************************************************************************/

typedef void (*IPCHandleRdIntToDSP)(void);

//Supply the aligned storage accessible via _msgTmpBuffer
EXTERN UINT8*               _msgTmpBuffer;
EXTERN UINT32               _msgTmpBufferArray[(CMD_MSG_HEADER_SIZE + CMD_MSG_PIPE_SIZE + 3)/sizeof(UINT32)];


/************************** OSA Objects for IPC package ********************************/

#define DATARX_STACK_SIZE 2048  //was 500
#define MAX_NUM_OF_RQST_IN_DATA_QUEUE    8
/* IPCDATARXTASK priority:
 *   must be higher than the L1 task (priority 2)
 *   otherwise L1 activities delay data RX transaction completion
 *   Note: data RX transactions are originated in IPCRDHISR. */
#define DATARX_TASK_PRIORITY   1  /* was 7 */


typedef struct
{
    OSPoolRef       msgRegDB_memPool;
#ifdef SPY_CMD
    OSPoolRef       spyCmdRegDB_memPool;
#endif
    OSSemaRef       register_sem;
    OSSemaRef       control_sem;
    OSSemaRef       appIDDB_sem;
    OSSemaRef       cmdBlocking_sem;
    OSSemaRef       dataBlocking_sem;
    OSSemaRef       commandWrite_sem;
    OSTaskRef       dataRxtaskRef;
    UINT8           dataRxTaskStack[DATARX_STACK_SIZE];
    OSMsgQRef       data_queue;
	OS_Hisr_t         rdHISR;
	OS_Hisr_t         dataHISR;
} OSAObjectsForIPC;

EXTERN OSAObjectsForIPC   _IPCOSAObjects;



/************************** Registration DataBases **********************************/

/************* Message Notification Registration DataBase ***************************/
/*																					*/
/* The 'Message Notification' database is according to SET ID's.				    */
/* Under each SET there is an array of required sub-opcodes.						*/
/* For each sub-opcode threre could be one or more notification callback functions.	*/
/*	adi - fix!!!																				*/
/* If for a certain sub-opcode there is only one registerred callback functiuon:	*/
/* The 'BASIC' database shall be used.												*/
/*																					*/
/* If for a certain sub-opcode there is several registerred callback functiuon:		*/
/* The 'BROADCAST' database shall be used.											*/
/*																					*/
/* Broadcasting: the pointer to a function in the 'BASIC' database shall point to	*/
/* a broadcasting routine.															*/
/* This routine shall use the 'BROADCASTING' database to broadcast the notification.*/
/* The 'BROADCASTING' database is a linked-list of all the callback function		*/
/* registerred for notification on this specific message (LINK ID + Sub-Opcode).	*/
/*																					*/
/************************************************************************************/

typedef struct stMsgRegDB
{
	ApplicationID registeredApp;
	IPCCommNotifyMessageReceived callbackMsgNotify;
	struct stMsgRegDB *nxtDBCell;
} MsgRegDBType;


EXTERN	MsgRegDBType	*_msgRegDB[HS_MAX_NUM_OF_SET_ID][HS_MAX_NUM_OF_SUB_OPCODE];



/************* Data Registration DataBase *******************************************/
/*																					*/
/* The 'Data' database is according to data channels numbers.					    */
/* Under each channel number threre is a structure of the registration parameters.	*/
/*																					*/
/* These parameters are updated during registration and channel openning.			*/
/*																					*/
/* The channel parameters are:														*/
/* 1) channel status - open ro close												*/
/* 2) ACP application ID (which opened the channel)									*/
/* 3) PLP application ID (which the channel was opened with)						*/
/* 4) Confirm Data Write - pointer to the callback binded function					*/
/* 5) Available For New Data - pointer to the callback binded function				*/
/* 6) Get Data Pointer - pointer to the callback binded function					*/
/* 7) Notify Data Received - pointer to the callback binded function				*/
/*																					*/
/************************************************************************************/
typedef struct
{
    ApplicationID               registeredApp;
    IPCCommNotifyDataReceived   callbackNotifyDataReceived;
} NotifyDataReceivedDBStruct;

typedef struct
{
    ApplicationID               registeredApp;
    IPCCommNotifyDataBufferFree callbackNotifyDataBufferFree;
} NotifyDataBufferFreeDBStruct;

typedef struct
{
    ApplicationID                   registeredApp;
    IPCCommNotifyDataChannelFree    callbackNotifyDataChannelFree;
} NotifyDataChannelFreeDBStruct;

typedef struct
{
    ApplicationID                   registeredApp;
    IPCCommGetDataPointer           callbackGetDataPtr;
} GetDataPointerDBStruct;


typedef struct
{
    NotifyDataReceivedDBStruct      notifyDataReceived;
    NotifyDataBufferFreeDBStruct    notifyDataBufferFree;
    NotifyDataChannelFreeDBStruct   notifyDataChannelFree;
    GetDataPointerDBStruct          getDataPointer;
} DataRegDBStruct;

EXTERN DataRegDBStruct _dataRegDB[IPC_MAX_DATA_CH_NUM];


#ifdef SPY_CMD
/********************** Spy Command Registration DataBase ***************************/
/*																					*/
/*																					*/
/************************************************************************************/

typedef struct stSpyCmdRegDB
{
	ApplicationID registeredApp;
    IPCCommSpyCommandNotification callbackNotifySpyCmd;
    struct stSpyCmdRegDB *nxtDBCell;
} SpyCmdRegDBType;


EXTERN  SpyCmdRegDBType    *_spyCmdRegDB[HS_MAX_NUM_OF_SET_ID][HS_MAX_NUM_OF_SUB_OPCODE];
EXTERN  SpyCmdRegDBType     _allSpyCmdsReg[MAX_NUM_OF_REGISTERS];

#endif



/*********************** Package Memory Pool ****************************/

#define SIZE_OF_MSG_REG_DB_CELL                     (sizeof(MsgRegDBType))    //remember also MemPool overhead 12 bytes
// THIS WORST CASE ALLOCATION IS UNACCEPTABLE - TOO MUCH MEMORY SPENT
// Allow configuration from target Ipc_gbl_config.h via MAX_NUM_OF_REGISTERS (default is 5)
// The number of registrations allowed is calculated below as: REG_DB_SPACE_UNIT*MAX_NUM_OF_REGISTERS
//#define NUM_OF_BASIC_MSG_REG_DB_CELLS				(HS_MAX_NUM_OF_SUB_OPCODE * HS_MAX_NUM_OF_SET_ID)
//#define NUM_OF_ADDITIONAL_MSG_REG_DB_CELLS			(MAX_NUM_OF_REGISTERS * NUM_OF_BASIC_MSG_REG_DB_CELLS)
//#define	SIZE_OF_ADDITIONAL_MSG_REG_DB_CELLS			(SIZE_OF_MSG_REG_DB_CELL * NUM_OF_ADDITIONAL_MSG_REG_DB_CELLS)
#define REG_DB_SPACE_UNIT 50

#define	SIZE_OF_ADDITIONAL_MSG_REG_DB_CELLS			(SIZE_OF_MSG_REG_DB_CELL*MAX_NUM_OF_REGISTERS*REG_DB_SPACE_UNIT)

EXTERN UINT8 _IPCCommMsgRegMemoryPool[SIZE_OF_ADDITIONAL_MSG_REG_DB_CELLS * 3 + 1000];


#ifdef SPY_CMD
/*********************** Package Memory Pool ****************************/

// SPY is a debug feature and is not massively used - allow 10% of message registration memory
#define SIZE_OF_ADDITIONAL_SPY_CMD_REG_DB_CELLS         (SIZE_OF_ADDITIONAL_MSG_REG_DB_CELLS/8)


EXTERN UINT8 _IPCCommSpyCmdRegMemoryPool[SIZE_OF_ADDITIONAL_SPY_CMD_REG_DB_CELLS];

#endif



/*************** Test Routines Prototypes *****************/
BOOL cmnSrvCheckAppIDValidity(ApplicationID app);


/*----------------------------------------------------------------------*/
#undef EXTERN

#endif  /* _WS_IPCCommGlobalParams_H_ */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/3g.mod/lib/src/ulbgrabmtimers.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2011/04/14 13:54:44 $
 **************************************************************************
 * File Description:
 *
 * RABM timer declarations
 **************************************************************************/

#if !defined (ULBGRABMTIMERS_H)
#define       ULBGRABMTIMERS_H

/**************************************************************************
 * Nested Include Files
 **************************************************************************/
#include <gpsystem.h>
#include <sntimers.h>
#include <xrabmdata.h>

/**************************************************************************
 * Manifest Constants
 **************************************************************************/
#define MIN_COUNTER_TIMEOUT_VAL_RABM    SECONDS_TO_TICKS(1)

#define RABM_DEACTIVATE_GUARD_TIMER_VALUE  SECONDS_TO_TICKS(60)

/* Modified by zuohuaxu for CQ00029836 20130307, begin */
//#define RABM_RAB_REESTAB_GUARD_TIMER_SECONDS	(30)
#define RABM_RAB_REESTAB_GUARD_TIMER_SECONDS	(15)
/* Modified by zuohuaxu for CQ00029836 20130307, end */

#define RABM_RAB_REESTAB_GUARD_TIMER_VALUE  SECONDS_TO_TICKS(RABM_RAB_REESTAB_GUARD_TIMER_SECONDS)

/**************************************************************************
 * Type Definitions
 **************************************************************************/
typedef enum RabmTimerIdTag
{
    RABM_DEACTIVATE_GUARD_TIMER = SN_MAX_TIMER_ID + 1,  /* Guard against no RAB release from NW */
    RABM_RAB_REESTAB_GUARD_TIMER						/* Guard against no RAB re-establishment from NW */
}
RabmTimerId;

/**************************************************************************
 * Macros
 **************************************************************************/

/**************************************************************************
 * Function Prototypes
 **************************************************************************/
extern Boolean UlbgRabmTimerExpiry (XRabmEntity *sn);
extern void    UlbgRabmStartDeactGuardTimer (XRabmPdpEntity *pdp);
extern void    UlbgRabmStopDeactGuardTimer (XRabmPdpEntity *pdp);
extern void    UlbgRabmCheckStartRabReestabGuardTimer (XRabmEntity *sn);
extern void	   UlbgRabmCheckStopRabReestabGuardTimer (XRabmEntity *sn);
extern void    UlbgRabmStartReestabRejTimer(XRabmEntity *sn);
extern void    UlbgRabmCheckStopReestabRejTimer(XRabmEntity *sn);
extern void    UlbgRabmCheckStopAllUmtsTimer(XRabmEntity *sn);

#endif

/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/******************************************************************************
 * File Name:   umaiseg.h
 *
 * File Description: Common definitions for segmentation entity clients.
 *
 *****************************************************************************/
#ifndef UMAISEG_H
#define UMAISEG_H
 
#define UMACI_CRC_LENGTH_BYTES              (1)

/******************************************************************************
 * Exported Macros
 *****************************************************************************/

#define UMACI_SEG_GET_TOTAL_PDU_SIZE_BITS(logChIndex)\
    umaEdchDb_g.segmentationDb.bufferInfo_p[logChIndex]->totalPduBits
    
#define UMACI_SEG_GET_NUM_UNTRANSMITTED_BITS(logChIndex)\
    umaEdchDb_g.segmentationDb.bufferInfo_p[logChIndex]->remainPduBits

#define UMACI_SEG_GET_FIRST_UNTRANSMITTED_BITOFFSET(logChIndex)\
    umaEdchDb_g.segmentationDb.bufferInfo_p[logChIndex]->nextPduBitOffset

#define UMACI_SEG_GET_BUF_ADDR(logChIndex)\
    &umaEdchDb_g.segmentationDb.bufferInfo_p[logChIndex]->pduBuffer_p[0]
    
#define UMACI_SEG_SET_NEW_PDU(logChIndex,totalPduSizeBits,transmittedPduSizeBits)\
    umaEdchDb_g.segmentationDb.bufferInfo_p[logChIndex]->totalPduBits = totalPduSizeBits;\
    umaEdchDb_g.segmentationDb.bufferInfo_p[logChIndex]->nextPduBitOffset = transmittedPduSizeBits;\
    if(totalPduSizeBits>=transmittedPduSizeBits)\
       umaEdchDb_g.segmentationDb.bufferInfo_p[logChIndex]->remainPduBits = totalPduSizeBits-transmittedPduSizeBits;\
    else\
	   umaEdchDb_g.segmentationDb.bufferInfo_p[logChIndex]->remainPduBits = 0

#define UMACI_SEG_UPDATE_EXIST_PDU(logChIndex,transmittedPduSizeBits)\
    umaEdchDb_g.segmentationDb.bufferInfo_p[logChIndex]->nextPduBitOffset += transmittedPduSizeBits;\
    if(umaEdchDb_g.segmentationDb.bufferInfo_p[logChIndex]->remainPduBits >= transmittedPduSizeBits)\
       umaEdchDb_g.segmentationDb.bufferInfo_p[logChIndex]->remainPduBits -= transmittedPduSizeBits;\
    else\
	   umaEdchDb_g.segmentationDb.bufferInfo_p[logChIndex]->remainPduBits = 0

#define UMACI_SEG_ADD_CRC_BYTE(logChIndex)\
    umaEdchDb_g.segmentationDb.bufferInfo_p[logChIndex]->totalPduBits += UMACI_CRC_LENGTH_BYTES*8;\
    umaEdchDb_g.segmentationDb.bufferInfo_p[logChIndex]->remainPduBits += UMACI_CRC_LENGTH_BYTES*8


/******************************************************************************
 * Exported Functions
 *****************************************************************************/
void UmaISegResetSegmentationEntities(UmacEdchControlConfig * ctrlConfig_p);
void UmaISegReleaseSegmentationEntities(void);
void UmaISegCalcAppendCrc(ULogicalChannelIdentity logicalChannelIdentity);
void UmaISegInit(void);

#endif //UMAISEG_H


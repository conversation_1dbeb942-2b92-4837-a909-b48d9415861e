# WiFi扫描8个设备限制问题修复

## 问题分析

从您的测试日志可以看到，虽然配置显示`max_hotspots=20`，但实际上仍然只扫描到8个设备。这说明限制来自更底层的地方。

### 根本原因

通过代码分析发现，问题出现在`gui/lv_watch/modem/mmi_modem_adaptor_nw.c`文件的第973行：

```c
if((NULL == pWifiCellInfo) || (APP_ADP_WIFI_AP_MAX_NUM == g_Wifi.WifiApList.count)) {
    // 停止扫描
}
```

这个硬编码的限制导致当扫描到的AP数量达到`APP_ADP_WIFI_AP_MAX_NUM`时，扫描就会停止。

### 数据流分析

1. **底层扫描**：可能发现了更多AP
2. **Modem适配器层**：在`Ril_MM_Response_Wifi_CellInfo`函数中被限制
3. **应用层**：只能收到被限制后的结果

### APP_ADP_WIFI_AP_MAX_NUM的定义

在`gui/lv_watch/include/app_adaptor_interface.h`中：

```c
#if USE_LV_WATCH_YIXIN !=0
#define APP_ADP_WIFI_AP_MAX_NUM        20  // YIXIN模式
#else
#define APP_ADP_WIFI_AP_MAX_NUM        8   // 普通模式 ← 这里是问题所在
#endif
```

## 解决方案

### 1. 增加APP_ADP_WIFI_AP_MAX_NUM限制

我已经修改了`app_adaptor_interface.h`中的定义：

```c
#if USE_LV_WATCH_YIXIN !=0
#define APP_ADP_WIFI_AP_MAX_NUM        30  // 从20增加到30
#else
#define APP_ADP_WIFI_AP_MAX_NUM        20  // 从8增加到20
#endif
```

### 2. 添加调试日志

在`mmi_modem_adaptor_nw.c`中添加了详细的调试日志：

```c
if(NULL == pWifiCellInfo) {
    printf("[WIFI_DEBUG] Scan stopped: received NULL WiFi info (scan end signal), count=%d\n", g_Wifi.WifiApList.count);
} else {
    printf("[WIFI_DEBUG] Scan stopped: reached max AP limit (%d), count=%d\n", APP_ADP_WIFI_AP_MAX_NUM, g_Wifi.WifiApList.count);
}
```

### 3. 更新测试配置显示

测试接口现在会显示系统级别的AP限制：

```
[WIFI_TEST] - Scan mode: Normal Enhanced (rounds=5, max_hotspots=20, max_results=20)
[WIFI_TEST] - System AP limit: 20 (increased from 8)
```

## 修改的文件

### 1. gui/lv_watch/include/app_adaptor_interface.h
- 将普通模式的`APP_ADP_WIFI_AP_MAX_NUM`从8增加到20
- 将YIXIN模式的`APP_ADP_WIFI_AP_MAX_NUM`从20增加到30

### 2. gui/lv_watch/modem/mmi_modem_adaptor_nw.c
- 添加扫描停止原因的调试日志
- 帮助诊断是否因为达到限制而停止扫描

### 3. gui/lv_watch/ke/ke_location_flow.c
- 更新测试配置显示信息
- 显示系统级别的AP限制

## 预期效果

修改后，WiFi扫描应该能够：

1. **发现更多AP**：从最多8个增加到20个（普通模式）
2. **突破原有限制**：不再被8个设备的硬限制约束
3. **提供详细日志**：明确显示扫描停止的原因
4. **保持兼容性**：不影响现有功能

## 测试验证

重新编译并运行测试后，您应该看到：

### 1. 更多的扫描结果
```
[WIFI_TEST] AP count this round: 15  // 而不是8
[WIFI_TEST] Round 1 SUCCESS - Found 15 APs  // 而不是8
```

### 2. 系统限制信息
```
[WIFI_TEST] - System AP limit: 20 (increased from 8)
```

### 3. 调试日志
```
[WIFI_DEBUG] Scan stopped: received NULL WiFi info (scan end signal), count=15
```
或者
```
[WIFI_DEBUG] Scan stopped: reached max AP limit (20), count=20
```

## 其他可能的限制

如果修改后仍然有限制，可能还需要检查：

### 1. 底层驱动限制
- WiFi芯片驱动可能有自己的限制
- 需要检查底层的扫描参数

### 2. 内存限制
- 确保有足够的内存来存储更多的AP信息
- 检查内存分配是否成功

### 3. 超时限制
- 扫描更多AP可能需要更长时间
- 可能需要调整超时参数

## 故障排除

### 1. 如果仍然只有8个设备
- 检查编译是否包含了修改的头文件
- 确认使用的是修改后的版本
- 查看调试日志确定停止原因

### 2. 如果出现内存问题
- 检查内存分配是否成功
- 考虑减少最大AP数量
- 监控内存使用情况

### 3. 如果扫描时间过长
- 调整扫描超时参数
- 优化扫描策略
- 考虑分批扫描

## 总结

这个修改解决了WiFi扫描被硬限制在8个设备的问题，通过：

1. **增加系统级限制**：从8个增加到20个
2. **添加诊断工具**：详细的调试日志
3. **保持向后兼容**：不影响现有功能
4. **提供扩展性**：为将来的改进预留空间

现在重新编译并测试，应该能够发现更多的WiFi设备，包括您关注的目标MAC地址。

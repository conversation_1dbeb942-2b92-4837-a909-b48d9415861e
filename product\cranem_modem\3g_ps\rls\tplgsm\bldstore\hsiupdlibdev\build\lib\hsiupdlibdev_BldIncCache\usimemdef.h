/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/3g.mod/lib/src/usimemdef.h#3 $
 *   $Revision: #3 $
 *   $DateTime: 2007/01/12 13:10:41 $
 **************************************************************************
 *  File Description :
 *
 *  File definition for the SIM emulator
 **************************************************************************/
  //t
#if !defined (USIMDEF_H)
#define USIMDEF_H

/***************************************************************************
 * Nested Include Files
 **************************************************************************/

#if !defined (ALSI_SIG_H)
#include "alsi_sig.h"
#endif

#if !defined (SIMDATA_H)
#include "simdata.h"
#endif

#if !defined (USIMEMU_H)
#include "usimemu.h"
#endif

/***************************************************************************
 * Manifest Constants
 **************************************************************************/
#define SIM_EMU_SIZE_USIM_TKCDF_FILE sizeof defTkcdfUsimData

/***************************************************************************
 * Typed Constants
 **************************************************************************/

#if defined (UPGRADE_3G)
SimMfDfData    defMfData =
{
    SIM_DIR_MF,
    SIM_DIR_INVALID,
    (2048),                                     /*total memory available*/
    SIM_FILETYPE_MF,                            /*type of file*/
    SIM_UICC_ACTIVATED_STATE,                   /*file State*/
    SIM_CLKSTOP_NOT_ALLOWED,                    /*clock mode*/
    SIM_MIN_AUTH_FREQ_13OVER8,                  /*min auth freq*/
    SIM_1_8_OR_3_VOLTS_CAPAB,                   /*SIM voltage capability*/
    {                                           /*Access data*/
       USIM_ACCESS_NEVER,   /* delete self */
       USIM_ACCESS_NEVER,   /* terminate */
       USIM_ACCESS_NEVER,   /* activate */
       USIM_ACCESS_NEVER,   /* deactivate */
       USIM_ACCESS_NEVER,   /* createEf */
       USIM_ACCESS_NEVER,   /* createDf */
       USIM_ACCESS_NEVER    /* deleteChild */
    },
    0,
    0,
    USIM_ACCESS_PIN1_APP1,                      /*Global application Pin*/
    USIM_ACCESS_PIN2_APP1,                      /*local PIN */
    {                                           /*local PIN status*/
        3,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x32, 0x32, 0x32, 0x32, 0xff, 0xff, 0xff,0xff}   /*local PIN value */

    },
    {                                           /*unblock PIN 2 status*/
        10,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32}  /*unblock PIN 2 value*/
     }

};


SimMfDfData    defGsmDfData =
{
    SIM_DIR_DF_GSM,
    SIM_DIR_MF,
    (2048),
    SIM_FILETYPE_DF,
    SIM_UICC_ACTIVATED_STATE,                   /*file State*/
    SIM_CLKSTOP_NOT_ALLOWED,                    /*clock mode*/
    SIM_MIN_AUTH_FREQ_13OVER8,                  /*min auth freq*/
    SIM_1_8_OR_3_VOLTS_CAPAB,                   /*SIM voltage capability*/
    {                                           /*Access data*/
       USIM_ACCESS_NEVER,   /* delete self */
       USIM_ACCESS_NEVER,   /* terminate */
       USIM_ACCESS_NEVER,   /* activate */
       USIM_ACCESS_NEVER,   /* deactivate */
       USIM_ACCESS_NEVER,   /* createEf */
       USIM_ACCESS_NEVER,   /* createDf */
       USIM_ACCESS_NEVER    /* deleteChild */
    },
    0,
    0,
    USIM_ACCESS_PIN1_APP1,                      /*Global application Pin*/
    USIM_ACCESS_PIN2_APP1,                      /*local PIN */
    {                                           /*local PIN status*/
        3,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x32, 0x32, 0x32, 0x32, 0xff, 0xff, 0xff,0xff}   /*local PIN value */

    },
    {                                           /*unblock PIN 2 status*/
        10,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32}  /*unblock PIN 2 value*/
    }


};

SimMfDfData    defEnsDfData =
{
    SIM_DIR_DF_ENS,
    SIM_DIR_MF,
    (2048),
    SIM_FILETYPE_DF,
    SIM_UICC_ACTIVATED_STATE,                   /*file State*/
    SIM_CLKSTOP_NOT_ALLOWED,                    /*clock mode*/
    SIM_MIN_AUTH_FREQ_13OVER8,                  /*min auth freq*/
    SIM_1_8_OR_3_VOLTS_CAPAB,                   /*SIM voltage capability*/
    {                                           /*Access data*/
       USIM_ACCESS_NEVER,   /* delete self */
       USIM_ACCESS_NEVER,   /* terminate */
       USIM_ACCESS_NEVER,   /* activate */
       USIM_ACCESS_NEVER,   /* deactivate */
       USIM_ACCESS_NEVER,   /* createEf */
       USIM_ACCESS_NEVER,   /* createDf */
       USIM_ACCESS_NEVER    /* deleteChild */
    },
    0,
    0,
    USIM_ACCESS_PIN1_APP1,                      /*Global application Pin*/
    USIM_ACCESS_PIN2_APP1,                      /*local PIN */
    {                                           /*local PIN status*/
        3,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x32, 0x32, 0x32, 0x32, 0xff, 0xff, 0xff,0xff}   /*local PIN value */
    },
    {                                           /*unblock PIN 2 status*/
        10,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32}  /*unblock PIN 2 value*/
    }
};

/* Directory located under the Enhanced Network Selection directory.
 * Note: Same ID as DF IRIDIUM. */
SimMfDfData    defEnsSubDfData =
{
    SIM_DIR_DF_IRIDIUM,
    SIM_DIR_DF_ENS,
    (2048),
    SIM_FILETYPE_DF,
    SIM_UICC_ACTIVATED_STATE,                   /*file State*/
    SIM_CLKSTOP_NOT_ALLOWED,                    /*clock mode*/
    SIM_MIN_AUTH_FREQ_13OVER8,                  /*min auth freq*/
    SIM_1_8_OR_3_VOLTS_CAPAB,                   /*SIM voltage capability*/
    {                                           /*Access data*/
       USIM_ACCESS_NEVER,   /* delete self */
       USIM_ACCESS_NEVER,   /* terminate */
       USIM_ACCESS_NEVER,   /* activate */
       USIM_ACCESS_NEVER,   /* deactivate */
       USIM_ACCESS_NEVER,   /* createEf */
       USIM_ACCESS_NEVER,   /* createDf */
       USIM_ACCESS_NEVER    /* deleteChild */
    },
    0,
    0,
    USIM_ACCESS_PIN1_APP1,                      /*Global application Pin*/
    USIM_ACCESS_PIN2_APP1,                      /*local PIN */
    {                                           /*local PIN status*/
        3,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x32, 0x32, 0x32, 0x32, 0xff, 0xff, 0xff,0xff}   /*local PIN value */

    },
    {                                           /*unblock PIN 2 status*/
        10,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32}  /*unblock PIN 2 value*/
    }
};

/*-----------------17/07/2003 16:13-----------------
 * local phonebook directory (phonebook under ADF USIM)
 * --------------------------------------------------*/

SimMfDfData    defPhoneBookUsimDfData =
{
    SIM_DIR_DF_PHONEBOOK,
    SIM_DIR_ADF_USIM,
    (2048),
    SIM_FILETYPE_DF,
    SIM_UICC_ACTIVATED_STATE,                   /*file State*/
    SIM_CLKSTOP_NOT_ALLOWED,                    /*clock mode*/
    SIM_MIN_AUTH_FREQ_13OVER8,                  /*min auth freq*/
    SIM_1_8_OR_3_VOLTS_CAPAB,                   /*SIM voltage capability*/
    {                                           /*Access data*/
       USIM_ACCESS_NEVER,   /* delete self */
       USIM_ACCESS_NEVER,   /* terminate */
       USIM_ACCESS_NEVER,   /* activate */
       USIM_ACCESS_NEVER,   /* deactivate */
       USIM_ACCESS_NEVER,   /* createEf */
       USIM_ACCESS_NEVER,   /* createDf */
       USIM_ACCESS_NEVER    /* deleteChild */
    },
    0,
    0,
    USIM_ACCESS_PIN1_APP1,                      /*Global application Pin*/
    USIM_ACCESS_PIN2_APP1,                      /*local PIN */
    {                                           /*local PIN status*/
        3,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x32, 0x32, 0x32, 0x32, 0xff, 0xff, 0xff,0xff}   /*local PIN value */
    },
    {                                           /*unblock PIN 2 status*/
        10,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32}  /*unblock PIN 2 value*/
    }


};

/*-----------------17/07/2003 16:13-------------------------
 * global phonebook directory (phonebook under DF TELECOM)
 * --------------------------------------------------------*/

SimMfDfData    defPhoneBookDfData =
{
    SIM_DIR_DF_PHONEBOOK,
    SIM_DIR_DF_TELECOM,
    (2048),
    SIM_FILETYPE_DF,
    SIM_UICC_ACTIVATED_STATE,                   /*file State*/
    SIM_CLKSTOP_NOT_ALLOWED,                    /*clock mode*/
    SIM_MIN_AUTH_FREQ_13OVER8,                  /*min auth freq*/
    SIM_1_8_OR_3_VOLTS_CAPAB,                   /*SIM voltage capability*/
    {                                           /*Access data*/
       USIM_ACCESS_NEVER,   /* delete self */
       USIM_ACCESS_NEVER,   /* terminate */
       USIM_ACCESS_NEVER,   /* activate */
       USIM_ACCESS_NEVER,   /* deactivate */
       USIM_ACCESS_NEVER,   /* createEf */
       USIM_ACCESS_NEVER,   /* createDf */
       USIM_ACCESS_NEVER    /* deleteChild */
    },
    0,
    0,
    USIM_ACCESS_PIN1_APP1,                      /*Global application Pin*/
    USIM_ACCESS_PIN2_APP1,                      /*local PIN */
    {                                           /*local PIN status*/
        3,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x32, 0x32, 0x32, 0x32, 0xff, 0xff, 0xff,0xff}   /*local PIN value */
    },
    {                                           /*unblock PIN 2 status*/
        10,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32}  /*unblock PIN 2 value*/
    }


};


SimMfDfData    defHnbUsimDfData =
{
    SIM_DIR_DF_HNB,
    SIM_DIR_ADF_USIM,
    (2048),
    SIM_FILETYPE_DF,
    SIM_UICC_ACTIVATED_STATE,                   /*file State*/
    SIM_CLKSTOP_NOT_ALLOWED,                    /*clock mode*/
    SIM_MIN_AUTH_FREQ_13OVER8,                  /*min auth freq*/
    SIM_1_8_OR_3_VOLTS_CAPAB,                   /*SIM voltage capability*/
    {                                           /*Access data*/
       USIM_ACCESS_NEVER,   /* delete self */
       USIM_ACCESS_NEVER,   /* terminate */
       USIM_ACCESS_NEVER,   /* activate */
       USIM_ACCESS_NEVER,   /* deactivate */
       USIM_ACCESS_NEVER,   /* createEf */
       USIM_ACCESS_NEVER,   /* createDf */
       USIM_ACCESS_NEVER    /* deleteChild */
    },
    0,
    0,
    USIM_ACCESS_PIN1_APP1,                      /*Global application Pin*/
    USIM_ACCESS_PIN2_APP1,                      /*local PIN */
    {                                           /*local PIN status*/
        3,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x32, 0x32, 0x32, 0x32, 0xff, 0xff, 0xff,0xff}   /*local PIN value */
    },
    {                                           /*unblock PIN 2 status*/
        10,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32}  /*unblock PIN 2 value*/
    }
};


/*-----------------17/07/2003 16:13-----------------
 * GSM ACCESS directory under ADF USIM Directory
 * --------------------------------------------------*/
SimMfDfData    defGsmAccessUsimDfData =
{
    SIM_DIR_DF_GSM_ACCESS,
    SIM_DIR_ADF_USIM,
    (2048),
    SIM_FILETYPE_DF,
    SIM_UICC_ACTIVATED_STATE,                   /*file State*/
    SIM_CLKSTOP_NOT_ALLOWED,                    /*clock mode*/
    SIM_MIN_AUTH_FREQ_13OVER8,                  /*min auth freq*/
    SIM_1_8_OR_3_VOLTS_CAPAB,                   /*SIM voltage capability*/
    {                                           /*Access data*/
       USIM_ACCESS_NEVER,   /* delete self */
       USIM_ACCESS_NEVER,   /* terminate */
       USIM_ACCESS_NEVER,   /* activate */
       USIM_ACCESS_NEVER,   /* deactivate */
       USIM_ACCESS_NEVER,   /* createEf */
       USIM_ACCESS_NEVER,   /* createDf */
       USIM_ACCESS_NEVER    /* deleteChild */
    },
    0,
    0,
    USIM_ACCESS_PIN1_APP1,                      /*Global application Pin*/
    USIM_ACCESS_PIN2_APP1,                      /*local PIN */
    {                                           /*local PIN status*/
        3,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x32, 0x32, 0x32, 0x32, 0xff, 0xff, 0xff,0xff}   /*local PIN value */
    },
    {                                           /*unblock PIN 2 status*/
        10,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32}  /*unblock PIN 2 value*/
    }


};
/*Added by zhjzhao for CQ00013463 Add a set of default USIM files value for LTE simulation on PC 10-10-11, Begin*/
/*Add by zhjzhao for LTE simulation for EFSlosa  5F70, 09-16-11, Begin*/
SimMfDfData    defSolsaUsimDfData =
{
    SIM_DIR_DF_SOLSA,
    SIM_DIR_ADF_USIM,
    (2048),
    SIM_FILETYPE_DF,
    SIM_UICC_ACTIVATED_STATE,                   /*file State*/
    SIM_CLKSTOP_NOT_ALLOWED,                    /*clock mode*/
    SIM_MIN_AUTH_FREQ_13OVER8,                  /*min auth freq*/
    SIM_1_8_OR_3_VOLTS_CAPAB,                   /*SIM voltage capability*/
    {                                           /*Access data*/
       USIM_ACCESS_NEVER,   /* delete self */
       USIM_ACCESS_NEVER,   /* terminate */
       USIM_ACCESS_NEVER,   /* activate */
       USIM_ACCESS_NEVER,   /* deactivate */
       USIM_ACCESS_NEVER,   /* createEf */
       USIM_ACCESS_NEVER,   /* createDf */
       USIM_ACCESS_NEVER    /* deleteChild */
    },
    0,
    0,
    USIM_ACCESS_PIN1_APP1,                      /*Global application Pin*/
    USIM_ACCESS_PIN2_APP1,                      /*local PIN */
    {                                           /*local PIN status*/
        3,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x32, 0x32, 0x32, 0x32, 0xff, 0xff, 0xff,0xff}   /*local PIN value */
    },
    {                                           /*unblock PIN 2 status*/
        10,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32}  /*unblock PIN 2 value*/
    }


};
/*Add by zhjzhao for LTE simulation for EFSlosa  5F70, 09-16-11, End*/
/*Added by zhjzhao for CQ00013463 Add a set of default USIM files value for LTE simulation on PC 10-10-11, End*/
SimMfDfData    defTelecomDfData =
{
    SIM_DIR_DF_TELECOM,
    SIM_DIR_MF,
    (2048),
    SIM_FILETYPE_DF,
    SIM_UICC_ACTIVATED_STATE,                   /*file State*/
    SIM_CLKSTOP_NOT_ALLOWED,                    /*clock mode*/
    SIM_MIN_AUTH_FREQ_13OVER8,                  /*min auth freq*/
    SIM_1_8_OR_3_VOLTS_CAPAB,                   /*SIM voltage capability*/
    {                                           /*Access data*/
       USIM_ACCESS_NEVER,                       /* delete self */
       USIM_ACCESS_NEVER,                       /* terminate */
       USIM_ACCESS_NEVER,                       /* activate */
       USIM_ACCESS_NEVER,                       /* deactivate */
       USIM_ACCESS_NEVER,                       /* createEf */
       USIM_ACCESS_NEVER,                       /* createDf */
       USIM_ACCESS_NEVER                        /* deleteChild */
    },
    0,
    0,
    USIM_ACCESS_PIN1_APP1,                      /*Global application Pin*/
    USIM_ACCESS_PIN2_APP1,                      /*local PIN */
    {                                           /*local PIN status*/
        3,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x32, 0x32, 0x32, 0x32, 0xff, 0xff, 0xff,0xff}   /*local PIN value */
    },
    {                                           /*unblock PIN 2 status*/
        10,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32}  /*unblock PIN 2 value*/
    }


};

/*CQ00135656, Cgliu, 2022-02-25,Begin*/
#if defined (ON_PC)
SimMfDfData    defGraphicsDfData =
{
    SIM_DIR_DF_GRAPHICS,
    SIM_DIR_DF_TELECOM,
    (2048),
    SIM_FILETYPE_DF,
    SIM_UICC_ACTIVATED_STATE,                   /*file State*/
    SIM_CLKSTOP_NOT_ALLOWED,                    /*clock mode*/
    SIM_MIN_AUTH_FREQ_13OVER8,                  /*min auth freq*/
    SIM_1_8_OR_3_VOLTS_CAPAB,                   /*SIM voltage capability*/
    {                                           /*Access data*/
       USIM_ACCESS_NEVER,                       /* delete self */
       USIM_ACCESS_NEVER,                       /* terminate */
       USIM_ACCESS_NEVER,                       /* activate */
       USIM_ACCESS_NEVER,                       /* deactivate */
       USIM_ACCESS_NEVER,                       /* createEf */
       USIM_ACCESS_NEVER,                       /* createDf */
       USIM_ACCESS_NEVER                        /* deleteChild */
    },
    0,
    0,
    USIM_ACCESS_PIN1_APP1,                      /*Global application Pin*/
    USIM_ACCESS_PIN2_APP1,                      /*local PIN */
    {                                           /*local PIN status*/
        3,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x32, 0x32, 0x32, 0x32, 0xff, 0xff, 0xff,0xff}   /*local PIN value */
    },
    {                                           /*unblock PIN 2 status*/
        10,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32}  /*unblock PIN 2 value*/
    }


};
#endif 
/*CQ00135656, Cgliu, 2022-02-25,End  */

/*-----------------17/07/2003 16:14-----------------
 * ADF USIM directory
 * --------------------------------------------------*/

SimMfDfData    defAdfUsimData =
{
    SIM_DIR_ADF_USIM,
    SIM_DIR_MF,
    (2048),
    SIM_FILETYPE_ADF,
    SIM_UICC_ACTIVATED_STATE,                   /*file State*/
    SIM_CLKSTOP_NOT_ALLOWED,                    /*clock mode*/
    SIM_MIN_AUTH_FREQ_13OVER8,                  /*min auth freq*/
    SIM_1_8_OR_3_VOLTS_CAPAB,                   /*SIM voltage capability*/
    {                                           /*Access data*/
       USIM_ACCESS_NEVER,                       /* delete self */
       USIM_ACCESS_NEVER,                       /* terminate */
       USIM_ACCESS_NEVER,                       /* activate */
       USIM_ACCESS_NEVER,                       /* deactivate */
       USIM_ACCESS_NEVER,                       /* createEf */
       USIM_ACCESS_NEVER,                       /* createDf */
       USIM_ACCESS_NEVER                        /* deleteChild */
    },
    0,
    0,
    USIM_ACCESS_PIN1_APP1,                      /*Global application Pin*/
    USIM_ACCESS_PIN2_APP1,                      /*local PIN */
    {                                           /*local PIN status*/
        3,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x32, 0x32, 0x32, 0x32, 0xff, 0xff, 0xff,0xff}   /*local PIN value */
    },
    {                                           /*unblock PIN 2 status*/
        10,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32}  /*unblock PIN 2 value*/
    },


    {                                             /* AID: Only used for ADF_USIM */
      {0xa0,0x00,0x00,0x00,0x87,
      /*3G app code */ 0x10, 0x01, /*PIX*/ 0x32, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff},  /* AID  value */
      0x10
    }
    };

/*-----------------17/07/2003 16:14-----------------
 * EF ICCID
 * --------------------------------------------------*/

Int8    defIccidData[] = {'I','C','C','I','D', 6, 7, 8, 9, 10};

SimEfData defIccidEfData =
{
    SIM_EMU_EF_ICCID,
    SIM_EFSTRUCT_T,
    SIM_DIR_MF,
    1,
    SIM_EMU_SIZE_ICCID_FILE,      /*record length*/
    (0),
    (0),
    { USIM_ACCESS_NEVER,          /* delete file */
      USIM_ACCESS_NEVER,          /* terminate */
      USIM_ACCESS_PIN,            /* activate */
      USIM_ACCESS_PIN,            /* deactivate */
      USIM_ACCESS_NEVER,          /* write */
      USIM_ACCESS_NEVER,          /* update */
      USIM_ACCESS_PIN             /* read */
    },
    SIM_UICC_ACTIVATED_STATE,     /* file State*/
    FALSE,                        /* sfi supported ?*/
    0,
    FALSE                         /* Under USIM*/
};
/*CQ00135656, Cgliu, 2022-02-25,Begin*/
#if defined (ON_PC)

Int8    defCspData[] = {   0x02, 0xF0,
                           0xC0, 0xFF,
                           0xD5, 0xFF,
                           0x06, 0x80,
                           0x09, 0xFC,
                           0x03, 0x78,
                           0x07, 0x00,
                           0x08, 0x63 };

SimEfData defCspEfData =
{
    SIM_EMU_EF_CSP,
    SIM_EFSTRUCT_T,
    SIM_DIR_ADF_USIM,
    1,
    SIM_EMU_SIZE_CSP_FILE,      /*record length*/
    (0),
    (0),
    { USIM_ACCESS_NEVER,          /* delete file */
      USIM_ACCESS_NEVER,          /* terminate */
      USIM_ACCESS_PIN,            /* activate */
      USIM_ACCESS_PIN,            /* deactivate */
      USIM_ACCESS_NEVER,          /* write */
      USIM_ACCESS_PIN,            /* update */
      USIM_ACCESS_PIN             /* read */
    },
    SIM_UICC_ACTIVATED_STATE,     /* file State*/
    FALSE,                        /* sfi supported ?*/
    0,
    TRUE                         /* Under USIM*/
};


/* ENS (Enhanced Network Selection) file*/
Int8    defTstData[] = {0x01, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 };

SimEfData    defTstEfData =
{

    SIM_EMU_EF_TST,           /* Id           */
    SIM_EFSTRUCT_T,         /* Type         */
    SIM_DIR_DF_ENS,
    1,                     /* Num recs          */
    SIM_EMU_SIZE_TST_FILE, /* Rec Len     */
    (0),                      /* curr Rec     */
    (0),
  { USIM_ACCESS_ALWAYS,     /* Access Up    */
    USIM_ACCESS_ALWAYS,     /* Access Rd    */
    USIM_ACCESS_ALWAYS,     /* Access Inc   */
    USIM_ACCESS_ALWAYS,     /* Access Inv   */
    USIM_ACCESS_ALWAYS,     /* Access Reh   */
  },
  SIM_UICC_ACTIVATED_STATE,     /* file State*/
    FALSE,
    0,
  TRUE
};

/* ENS (Enhanced Network Selection) file*/
Int8    defActingHplmnData[] = {0x00, 0xf1, 0x10 };

SimEfData    defActingHplmnEfData =
{

    SIM_EMU_EF_ACT_HPLMN,           /* Id           */
    SIM_EFSTRUCT_T,             /* Type         */
    SIM_DIR_DF_IRIDIUM,         /* Dir          */
    1,                          /* Num recs     */
    SIM_EMU_SIZE_ACTING_HPLMN_FILE, /* Rec len    */
    (0),                      /* curr Rec     */
    (0),
  { USIM_ACCESS_ADM,     /* Access Up    */
    USIM_ACCESS_ALWAYS,      /* Access Rd    */
    USIM_ACCESS_NEVER,       /* Access Inc   */
    USIM_ACCESS_ADM,     /* Access Inv   */
    USIM_ACCESS_ADM,     /* Access Reh   */
  },
   SIM_UICC_ACTIVATED_STATE, /*file State */
   FALSE,                    /*sfi supported  */
   0x00,                     /*sfi */
   TRUE                      /*Under USIM */
};



/* ENS (Enhanced Network Selection) file*/
Int8    defRatData[] = {0x00};

SimEfData    defRatEfData =
{

    SIM_EMU_EF_RAT,           /* Id           */
    SIM_EFSTRUCT_T,             /* Type         */
    SIM_DIR_DF_IRIDIUM,         /* Dir          */
    1,                          /* Num recs     */
    SIM_EMU_SIZE_RAT_FILE, /* Rec len    */
    (0),                      /* curr Rec     */
    (0),
  { USIM_ACCESS_ADM,     /* Access Up    */
    USIM_ACCESS_ALWAYS,      /* Access Rd    */
    USIM_ACCESS_NEVER,       /* Access Inc   */
    USIM_ACCESS_ADM,     /* Access Inv   */
    USIM_ACCESS_ADM,     /* Access Reh   */
  },
   SIM_UICC_ACTIVATED_STATE, /*file State */
   FALSE,                    /*sfi supported  */
   0x00,                     /*sfi */
   TRUE                      /*Under USIM */
};

#if defined (UPGRADE_CSG1)						

/* files under DF_HNB (HomeNodeB) related files */
Int8   defHnbAcsglData[] =
{
    /*-=rec #1 =-*/
    0xA0,           /* CSG List Tag*/
    0x1D,		 /*  CSG TLG length */

    0x80,           /* PLMN Tag*/
    0x03,           /* PLMN Obj Len*/
    0x00, 0xf1, 0x10,

    0x81,           /* CSG Information Tag*/
    0x06,           /* length*/
    0x01,           /* CSG Type Indication */
    0x01,           /* HNBH Type Indication */
    0x01,0x23,0x45,0x20,

    0x81,           /* CSG Information Tag*/
    0x06,           /* length*/
    0x01,           /* CSG Type Indication */
    0x01,           /* HNBH Type Indication */
    0x01,0x23,0x45,0x40,

    0x81,           /* CSG Information Tag*/
    0x06,           /* length*/
    0x00,           /* CSG Type Indication */
    0x00,           /* HNBH Type Indication */
    0x00,0x00,0x00,0x80,
    0xFF,

    /*-=rec #2 =-*/

    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,

    /*-=rec #3 =-*/

    0xA0,           /* CSG List Tag*/
    0x1E,		 /*  CSG TLG length */

    0x80,           /* PLMN Tag*/
    0x03,           /* PLMN Obj Len*/
    0x00, 0xf1, 0x10,

    0x81,           /* CSG Information Tag*/
    0x06,           /* length*/
    0x02,           /* CSG Type Indication */
    0x02,           /* HNBH Type Indication */
    0x02,0x46,0x8A,0xC0,

    0x81,           /* CSG Information Tag*/
    0x06,           /* length*/
    0x02,           /* CSG Type Indication */
    0x02,           /* HNBH Type Indication */
    0xca,0x86,0x42,0x00,

    0x81,           /* CSG Information Tag*/
    0x06,           /* length*/
    0x02,           /* CSG Type Indication */
    0x02,           /* HNBH Type Indication */
    0x02,0x46,0x8a,0xe0,
    0xFF,

    /*-=rec #4 =-*/

    0xA0,           /* CSG List Tag*/
    0x1E,		 /*  CSG TLG length */

    0x80,           /* PLMN Tag*/
    0x03,           /* PLMN Obj Len*/
    0x00, 0xf1, 0x10,

    0x81,           /* CSG Information Tag*/
    0x05,           /* length*/
    0x03,           /* CSG Type Indication */
    0x03,           /* HNBH Type Indication */
    0x01,0x23,0x45,0xFF,

    0x81,           /* CSG Information Tag*/
    0x04,           /* length*/
    0x03,           /* CSG Type Indication */
    0x03,           /* HNBH Type Indication */
    0x00,0x00,

    0x81,           /* CSG Information Tag*/
    0x03,           /* length*/
    0x03,           /* CSG Type Indication */
    0x03,           /* HNBH Type Indication */
    0x12,0x00,0x00,0x00,
    0xFF,0xFF,0xFF
 };

#define SIM_EMU_ASGL_RECS_NUM 4
#define SIM_EMU_ASGL_REC_SIZE (sizeof(defHnbAcsglData)/SIM_EMU_ASGL_RECS_NUM)
SimEfData defHnbAcsglEfData =
{
    SIM_EMU_EF_ACSGL,        /* Id           */
    SIM_EFSTRUCT_LF,        /* Type         */
    SIM_DIR_DF_HNB,         /* Dir          */
    SIM_EMU_ASGL_RECS_NUM,	 /* num Recs	 */
    SIM_EMU_ASGL_REC_SIZE,  /* Rec Len      */
    (0),                    /* curr Rec     */
    (0),
    { USIM_ACCESS_NEVER,    /* delete file*/
      USIM_ACCESS_NEVER,    /* terminate*/
      USIM_ACCESS_ADM,      /* activate */
      USIM_ACCESS_ADM,      /* deactivate */
      USIM_ACCESS_NEVER,    /* write */
      USIM_ACCESS_PIN,      /* update*/
      USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    TRUE,                    /*sfi supported  */
    0x01,                    /*sfi*/
    TRUE                     /*Under USIM*/
};

/* files under DF_HNB (HomeNodeB) related files */
Int8   defHnbCsgtData[] =
{
    /*-=rec #1 =-*/
    0x89,       /* Type */
    0x20,       /* Length*/

    'C', 'S', 'G', ' ', 't', 'y', 'p', 'e',
    '-', '1', 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,

    /*-=rec #2 =-*/
    0x89,       /* Type */
    0x20,       /* Length*/

    'C', 'S', 'G', ' ', 't', 'y', 'p', 'e',
    '-', '2', 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,

    /*-=rec #3 =-*/
    0x89,       /* Type */
    0x20,       /* Length*/

    'C', 'S', 'G', ' ', 't', 'y', 'p', 'e',
    '-', '3', 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,

    /*-=rec #4 =-*/
    0x89,       /* Type */
    0x20,       /* Length*/

    'C', 'S', 'G', ' ', 't', 'y', 'p', 'e',
    '-', '4', 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
};

#define SIM_EMU_CSGT_RECS_NUM 4
#define SIM_EMU_CSGT_REC_SIZE (sizeof(defHnbCsgtData)/SIM_EMU_CSGT_RECS_NUM)
SimEfData defHnbCsgtEfData =
{
    SIM_EMU_EF_CSGT,         /* Id           */
    SIM_EFSTRUCT_LF,        /* Type         */
    SIM_DIR_DF_HNB,         /* Dir          */
    SIM_EMU_CSGT_RECS_NUM,   /* num Recs     */
    SIM_EMU_CSGT_REC_SIZE,	/* Rec Len		*/
    (0),                    /* curr Rec     */
    (0),
    { USIM_ACCESS_NEVER,    /* delete file*/
      USIM_ACCESS_NEVER,    /* terminate*/
      USIM_ACCESS_ADM,      /* activate */
      USIM_ACCESS_ADM,      /* deactivate */
      USIM_ACCESS_NEVER,    /* write */
      USIM_ACCESS_PIN,      /* update*/
      USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    TRUE,                    /*sfi supported  */
    0x02,                    /*sfi*/
    TRUE                     /*Under USIM*/
};


/* files under DF_HNB (HomeNodeB) related files */
Int8   defHnbHnbnData[] =
{
    /*-=rec #1 =-*/
    0x80,       /* Type */
    0x20,       /* Length*/

    'C', 'S', 'G', ' ', 'n', 'a', 'm', 'e',
    '-', '1', 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,

    /*-=rec #2 =-*/
    0x80,       /* Type */
    0x20,       /* Length*/

    'C', 'S', 'G', ' ', 'n', 'a', 'm', 'e',
    '-', '2', 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,

    /*-=rec #3 =-*/
    0x80,       /* Type */
    0x20,       /* Length*/

    'C', 'S', 'G', ' ', 'n', 'a', 'm', 'e',
    '-', '3', 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,

    /*-=rec #4 =-*/
    0x80,       /* Type */
    0x20,       /* Length*/

    'C', 'S', 'G', ' ', 'n', 'a', 'm', 'e',
    '-', '4', 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,

};

#define SIM_EMU_HNBN_RECS_NUM 4
#define SIM_EMU_HNBN_REC_SIZE (sizeof(defHnbHnbnData)/SIM_EMU_HNBN_RECS_NUM)

SimEfData defHnbHnbnEfData =
{
    SIM_EMU_EF_HNBN,        /* Id           */
    SIM_EFSTRUCT_LF,        /* Type         */
    SIM_DIR_DF_HNB,         /* Dir          */
    SIM_EMU_HNBN_RECS_NUM,				/* num Recs 	*/
    SIM_EMU_HNBN_REC_SIZE,                      /* Rec Len      */
    (0),                    /* curr Rec     */
    (0),
    { USIM_ACCESS_NEVER,    /* delete file*/
      USIM_ACCESS_NEVER,    /* terminate*/
      USIM_ACCESS_ADM,      /* activate */
      USIM_ACCESS_ADM,      /* deactivate */
      USIM_ACCESS_NEVER,    /* write */
      USIM_ACCESS_PIN,      /* update*/
      USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    TRUE,                    /*sfi supported  */
    0x03,                    /*sfi*/
    TRUE                     /*Under USIM*/
};


/* files under DF_HNB (HomeNodeB) related files */
Int8   defHnbOCsglData[] =
{
   /*-=rec #1 =-*/
    0xA0,           /* CSG List Tag*/
    0x1D,		 /*  CSG TLG length */

    0x80,           /* PLMN Tag*/
    0x03,           /* PLMN Obj Len*/
    0x00, 0xf1, 0x10,

    0x81,           /* CSG Information Tag*/
    0x06,           /* length*/
    0x01,           /* CSG Type Indication */
    0x01,           /* HNBH Type Indication */
    0x01,0x23,0x45,0x20,

    0x81,           /* CSG Information Tag*/
    0x06,           /* length*/
    0x01,           /* CSG Type Indication */
    0x01,           /* HNBH Type Indication */
    0x01,0x23,0x45,0x40,

    0x81,           /* CSG Information Tag*/
    0x06,           /* length*/
    0x00,           /* CSG Type Indication */
    0x00,           /* HNBH Type Indication */
    0x00,0x00,0x00,0x80,
    0xFF,

    /*-=rec #2 =-*/

    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,

    /*-=rec #3 =-*/

    0xA0,           /* CSG List Tag*/
    0x1E,		 /*  CSG TLG length */

    0x80,           /* PLMN Tag*/
    0x03,           /* PLMN Obj Len*/
    0x00, 0xf1, 0x10,

    0x81,           /* CSG Information Tag*/
    0x06,           /* length*/
    0x02,           /* CSG Type Indication */
    0x02,           /* HNBH Type Indication */
    0x02,0x46,0x8A,0xC0,

    0x81,           /* CSG Information Tag*/
    0x06,           /* length*/
    0x02,           /* CSG Type Indication */
    0x02,           /* HNBH Type Indication */
    0xca,0x86,0x42,0x00,

    0x81,           /* CSG Information Tag*/
    0x06,           /* length*/
    0x02,           /* CSG Type Indication */
    0x02,           /* HNBH Type Indication */
    0x02,0x46,0x8a,0xe0,
    0xFF,

    /*-=rec #4 =-*/

    0xA0,           /* CSG List Tag*/
    0x1E,		 /*  CSG TLG length */

    0x80,           /* PLMN Tag*/
    0x03,           /* PLMN Obj Len*/
    0x00, 0xf1, 0x10,

    0x81,           /* CSG Information Tag*/
    0x05,           /* length*/
    0x03,           /* CSG Type Indication */
    0x03,           /* HNBH Type Indication */
    0x01,0x23,0x45,0xFF,

    0x81,           /* CSG Information Tag*/
    0x04,           /* length*/
    0x03,           /* CSG Type Indication */
    0x03,           /* HNBH Type Indication */
    0x00,0x00,

    0x81,           /* CSG Information Tag*/
    0x03,           /* length*/
    0x03,           /* CSG Type Indication */
    0x03,           /* HNBH Type Indication */
    0x12,0x00,0x00,0x00,
    0xFF,0xFF,0xFF
 };


#define SIM_EMU_OCSGL_RECS_NUM 4
#define SIM_EMU_OCSGL_REC_SIZE (sizeof(defHnbOCsglData)/SIM_EMU_OCSGL_RECS_NUM)


SimEfData defHnbOCsglEfData =
{
    SIM_EMU_EF_OCSGL,        /* Id           */
    SIM_EFSTRUCT_LF,        /* Type         */
    SIM_DIR_DF_HNB,         /* Dir          */
    SIM_EMU_OCSGL_RECS_NUM,   /* num Recs     */
    SIM_EMU_OCSGL_REC_SIZE, 	  /* Rec Len 	 */
    (0),                    /* curr Rec     */
    (0),
    { USIM_ACCESS_NEVER,    /* delete file*/
      USIM_ACCESS_NEVER,    /* terminate*/
      USIM_ACCESS_ADM,      /* activate */
      USIM_ACCESS_ADM,      /* deactivate */
      USIM_ACCESS_NEVER,    /* write */
      USIM_ACCESS_PIN,      /* update*/
      USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    TRUE,                    /*sfi supported  */
    0x04,                    /*sfi*/
    TRUE                     /*Under USIM*/
};


/* files under DF_HNB (HomeNodeB) related files */
Int8   defHnbOCsgtData[] =
{
    /*-=rec #1 =-*/
    0x89,       /* Type */
    0x20,       /* Length*/

    'C', 'S', 'G', ' ', 't', 'y', 'p', 'e',
    '-', '1',  ' ','O', 'p', 'e', 'r', 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,

    /*-=rec #2 =-*/
    0x89,       /* Type */
    0x20,       /* Length*/

    'C', 'S', 'G', ' ', 't', 'y', 'p', 'e',
    '-', '2',  ' ','O', 'p', 'e', 'r', 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,

    /*-=rec #3 =-*/
    0x89,       /* Type */
    0x20,       /* Length*/

    'C', 'S', 'G', ' ', 't', 'y', 'p', 'e',
    '-', '3',  ' ','O', 'p', 'e', 'r', 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,

    /*-=rec #4 =-*/
    0x89,       /* Type */
    0x20,       /* Length*/

    'C', 'S', 'G', ' ', 't', 'y', 'p', 'e',
    '-', '4',  ' ','O', 'p', 'e', 'r', 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
};
#define SIM_EMU_OCSGT_RECS_NUM 4
#define SIM_EMU_OCSGT_REC_SIZE (sizeof(defHnbOCsgtData)/SIM_EMU_OCSGT_RECS_NUM)


SimEfData defHnbOCsgtEfData =
{
    SIM_EMU_EF_OCSGT,        /* Id           */
    SIM_EFSTRUCT_LF,        /* Type         */
    SIM_DIR_DF_HNB,         /* Dir          */
    SIM_EMU_OCSGT_RECS_NUM,   /* num Recs     */
    SIM_EMU_OCSGT_REC_SIZE, 					 /* Rec Len 	 */
    (0),                    /* curr Rec     */
    (0),
    { USIM_ACCESS_NEVER,    /* delete file*/
      USIM_ACCESS_NEVER,    /* terminate*/
      USIM_ACCESS_ADM,      /* activate */
      USIM_ACCESS_ADM,      /* deactivate */
      USIM_ACCESS_NEVER,    /* write */
      USIM_ACCESS_PIN,      /* update*/
      USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    TRUE,                    /*sfi supported  */
    0x05,                    /*sfi*/
    TRUE                     /*Under USIM*/
};


/* files under DF_HNB (HomeNodeB) related files */
Int8   defHnbOHnbnData[] =
{
    /*-=rec #1 =-*/
    0x80,       /* Type */
    0x20,       /* Length*/

    'C', 'S', 'G', ' ', 'n', 'a', 'm', 'e',
    '-', '1', ' ','O', 'p', 'e', 'r', 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,

    /*-=rec #2 =-*/
    0x80,       /* Type */
    0x20,       /* Length*/

    'C', 'S', 'G', ' ', 'n', 'a', 'm', 'e',
    '-', '2', ' ','O', 'p', 'e', 'r', 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,

    /*-=rec #3 =-*/
    0x80,       /* Type */
    0x20,       /* Length*/

    'C', 'S', 'G', ' ', 'n', 'a', 'm', 'e',
    '-', '3',  ' ','O', 'p', 'e', 'r', 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,

    /*-=rec #4 =-*/
    0x80,       /* Type */
    0x20,       /* Length*/

    'C', 'S', 'G', ' ', 'n', 'a', 'm', 'e',
    '-', '4',  ' ','O', 'p', 'e', 'r', 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,


};

#define SIM_EMU_OHNBN_RECS_NUM 4
#define SIM_EMU_OHNBN_REC_SIZE (sizeof(defHnbOHnbnData)/SIM_EMU_OHNBN_RECS_NUM)

SimEfData defHnbOHnbnEfData =
{
    SIM_EMU_EF_OHNBN,       /* Id           */
    SIM_EFSTRUCT_LF,        /* Type         */
    SIM_DIR_DF_HNB,         /* Dir          */
    SIM_EMU_OHNBN_RECS_NUM,   /* num Recs     */
    SIM_EMU_OHNBN_REC_SIZE, 					 /* Rec Len 	 */
    (0),                    /* curr Rec     */
    (0),
    { USIM_ACCESS_NEVER,    /* delete file*/
      USIM_ACCESS_NEVER,     /* terminate*/
      USIM_ACCESS_ADM,       /* activate */
      USIM_ACCESS_ADM,       /* deactivate */
      USIM_ACCESS_NEVER,    /* write */
      USIM_ACCESS_PIN,        /* update*/
      USIM_ACCESS_PIN        /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    TRUE,                    /*sfi supported  */
    0x06,                    /*sfi*/
    TRUE                     /*Under USIM*/
};
#endif // (UPGRADE_CSG1)						
#endif 
/*CQ00135656, Cgliu, 2022-02-25,End  */


/*-----------------17/07/2003 16:15-----------------
 * EF KC file: this file is located under DF GSM ACCESS Dir
 * For coding see 131 102 section *******
 * --------------------------------------------------*/
Int8    defUsimKcData[] = { 1, 2, 3, 4, 5, 6, 7, 8, 1 };

SimEfData    defUsimKcEfData =
{
    SIM_EMU_EF_KC,             /* Id           */
    SIM_EFSTRUCT_T,         /* Type         */
    SIM_DIR_DF_GSM_ACCESS,  /* Dir          */
    1,                      /* Rec Len      */
    SIM_EMU_SIZE_KC_FILE,   /* num Recs     */
    (0),                    /* curr Rec     */
    (0),
    { USIM_ACCESS_NEVER,    /* delete file*/
      USIM_ACCESS_NEVER,    /* terminate*/
      USIM_ACCESS_ADM,      /* activate */
      USIM_ACCESS_ADM,      /* deactivate */
      USIM_ACCESS_NEVER,    /* write */
      USIM_ACCESS_PIN,      /* update*/
      USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    TRUE,                     /*sfi supported  */
    0x01,                   /*sfi*/
    TRUE                     /*Under USIM*/
};

/*-----------------17/07/2003 16:15-----------------
 * EF HPLMN: this file is located under DF GSM ACCESS Dir
 * For coding see 131 102 section 4.2.6
 * --------------------------------------------------*/

Int8    defHplmnUsimData[] = { 0 };

SimEfData    defHplmnUsimEfData =
{
    SIM_EMU_EF_HPLMN,           /* Id           */
    SIM_EFSTRUCT_T,         /* Type         */
    SIM_DIR_ADF_USIM,       /* Dir          */
    1,                      /* Num rec      */
    SIM_EMU_SIZE_USIM_HPLMN_FILE,/* rec length  */
    (0),                    /* curr Rec     */
    (0),
    { USIM_ACCESS_NEVER,    /* delete file*/
      USIM_ACCESS_NEVER,    /* terminate*/
      USIM_ACCESS_ADM,      /* activate */
      USIM_ACCESS_ADM,      /* deactivate */
      USIM_ACCESS_NEVER,    /* write */
      /*Modification for IT test, Chengguang Liu, Feb.16 09,Begin*/
      /*USIM_ACCESS_ADM,*/      /* update*/
      USIM_ACCESS_PIN,
      /*Modification forIT test, Chengguang Liu, Feb.16 09,Begin*/
      USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    TRUE,                     /*sfi supported  */
    0x12,                     /*sfi*/
    TRUE                     /*Under USIM*/
};


/*-----------------18/10/01 08:43---------------------------------------------
 * Ef DIR is used to store the AID of the different appliactions on the UICC
 * ---------------------------------------------------------------------------*/

Int8    defDirData[] =
{
#if defined (MULTI_USIM_APPLICATIONS)
     /*-----------------17/10/01 16:07-------------------
      * first application: UICC app
      * --------------------------------------------------*/
       0x61,                               /* app template tag */
       0x26,                              /* length of the app template */
       0x4f,                              /* application identifier tag */
       0x10,                              /* AID length , this is variable*/
/*RID*/0xa0,0x00,0x00,0x00,0x87, /*3G app code */ 0x10, 0x02, /*PIX*/ 0x32, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,  /* AID  value */
       0x50,                            /* application label tag  */
       0x10,                            /* application label value */
       'F','I','R','S','T',0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,  /* application label value  */
      /*-----------------17/10/01 16:07-------------------
      * second application: USIM app
      * --------------------------------------------------*/
       0x61,                               /* app template tag */
       0x26,                              /* length of the app template */
       0x4f,                              /* application identifier tag */
       0x10,                              /* AID length , this is variable*/
/*RID*/0xa0,0x00,0x00,0x00,0x87, /*3G app code */ 0x10, 0x02, /*PIX*/ 0x32, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,  /* AID  value */
       0x50,                            /* application label tag  */
       0x10,                            /* application label value */
       'S','E','C','O','N','D',0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,  /* application label value  */
       /*-----------------17/10/01 16:07-------------------
      * third application: USIM app
      * --------------------------------------------------*/
       0x61,                               /* app template tag */
       0x26,                              /* length of the app template */
       0x4f,                              /* application identifier tag */
       0x10,                              /* AID length , this is variable*/
/*RID*/0xa0,0x00,0x00,0x00,0x87, /*3G app code */ 0x10, 0x02, /*PIX*/ 0x32, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,  /* AID  value */
       0x50,                            /* application label tag  */
       0x10,                            /* application label value */
       'T','H','I','R','D',0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff  /* application label value  */
 #else
     /*-----------------17/10/01 16:07-------------------
      * first application: UICC app
      * --------------------------------------------------*/
       0x61,                               /* app template tag */
       0x26,                              /* length of the app template */
       0x4f,                              /* application identifier tag */
       0x10,                              /* AID length , this is variable*/
/*RID*/0xa0,0x00,0x00,0x00,0x87, /*3G app code */ 0x10, 0x02, /*PIX*/ 0x32, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,  /* AID  value */
       0x50,                            /* application label tag  */
       0x10,                            /* application label value */
       'F','I','R','S','T',0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,  /* application label value  */
#endif
};

/*-----------------17/10/01 15:41-------------------
 * EF DIR this EF points to USIM applications
 * --------------------------------------------------*/
SimEfData    defDirEfData =
{
    SIM_EMU_EF_DIR,              /* Id           */
    SIM_EFSTRUCT_LF,         /* Type         */
    SIM_DIR_MF,              /* Dir          */
    SIM_EMU_NUM_DIR_RECS,   /*  num Recs      */
    SIM_EMU_SIZE_DIR_FILE,   /* Rec Len     */
    (0),                     /* curr Rec     */
    (0),
  {   USIM_ACCESS_NEVER,    /* delete file*/
      USIM_ACCESS_NEVER,    /* terminate*/
      USIM_ACCESS_ADM,      /* activate */
      USIM_ACCESS_ADM,      /* deactivate */
      USIM_ACCESS_NEVER,    /* write */
      USIM_ACCESS_ADM,      /* update*/
      USIM_ACCESS_ALWAYS    /* read*/
  },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    TRUE,                     /*sfi supported  */
    0x00,                     /*sfi*/
    FALSE                      /*Under USIM*/
};
/*CQ00135656, Cgliu, 2022-02-25,Begin*/
#if defined (ON_PC)
/*-----------------18/10/01 10:39-------------------
 * Emergency codes: under ADF_USIM
 * For coding refer to 131 102 section 4.2.21
 * --------------------------------------------------*/
/* job 107906 - made the ECC record a more representative value */
Int8 defEccUsimData[] = { /* call code */ 0x11, 0xf2, 0xff,
                          /* alpha Id */ 'C','O','D','E','1',0xff,
                          /* service category */ 0x01,
                          /* call code */ 0x78, 0x90, 0x12,
                          /* alpha Id */ 'C','O','D','E','2',0xff,
                          /* service category */ 0x02,
                          /* call code */ 0x34, 0x56, 0x78,
                          /* alpha Id */ 'C','O','D','E','3',0xff,
                          /* service category */ 0x03
#if defined (USIM_EMU_USE_EXTENDED_ECC_CODES)
                          ,
                          /* call code */ 0x56, 0x78, 0x89, /* alpha Id */ 'C','O','D','E','4',0xff, /* service category */ 0x04,
                          /* call code */ 0x87, 0x09, 0x21, /* alpha Id */ 'C','O','D','E','5',0xff, /* service category */ 0x05,
                          /* call code */ 0x33, 0x44, 0x55, /* alpha Id */ 'C','O','D','E','6',0xff, /* service category */ 0x06,
                          /* call code */ 0x66, 0x77, 0x88, /* alpha Id */ 'C','O','D','E','7',0xff, /* service category */ 0x07,
                          /* call code */ 0xff, 0xff, 0xff, /* alpha Id */ 'C','O','D','E','8',0xff, /* service category */ 0x01,
                          /* call code */ 0x99, 0xaa, 0xbb, /* alpha Id */ 'C','O','D','E','9',0xff, /* service category */ 0x02,
                          /* call code */ 0xcc, 0xdd, 0xee, /* alpha Id */ 'C','O','D','E','A',0xff, /* service category */ 0x03,
                          /* call code */ 0x11, 0x22, 0x33, /* alpha Id */ 'C','O','D','E','B',0xff, /* service category */ 0x04
#endif
                           };


SimEfData defEccUsimEfData =
{
  SIM_EMU_EF_ECC,                 /* Id           */
  SIM_EFSTRUCT_LF,            /* Type         */
  SIM_DIR_ADF_USIM,           /* Dir          */
  SIM_EMU_NUM_USIM_ECC_RECS,  /* Num Recs      */
  SIM_EMU_SIZE_USIM_ECC_FILE, /* Rec length     */
  (0),                        /* curr Rec     */
  (0),
  {   USIM_ACCESS_NEVER,    /* delete file*/
      USIM_ACCESS_NEVER,    /* terminate*/
      USIM_ACCESS_ADM,      /* activate */
      USIM_ACCESS_ADM,      /* deactivate */
      USIM_ACCESS_NEVER,    /* write */
      USIM_ACCESS_ADM,      /* update*/
      USIM_ACCESS_ALWAYS    /* read*/
  },
  SIM_UICC_ACTIVATED_STATE, /*file State*/
  TRUE,                     /*sfi supported  */
  0x01,                     /*sfi*/
  TRUE                      /*Under USIM*/
};

#endif 
/*CQ00135656, Cgliu, 2022-02-25,End  */

/*-----------------18/10/01 08:40---------------------------
 * Ef PL:
 * Preferred languages
 * This file is located under the MF
 * For coding, see 131 102 section 4.1.3
 * ---------------------------------------------------------*/
Int8    defPlData[] = { 's','w','d','e','f','r','i','t','n','o'  };

SimEfData    defPlEfData =
{

    SIM_EMU_EF_PL,                /* Id           */
    SIM_EFSTRUCT_T,           /* Type         */
    SIM_DIR_MF,               /* Dir          */
    1,                        /* num Recs      */
    SIM_EMU_SIZE_PL_FILE,     /* file length    */
    (0),                      /* curr Rec     */
    (0),
    {   USIM_ACCESS_NEVER,    /* delete file*/
        USIM_ACCESS_NEVER,    /* terminate*/
        USIM_ACCESS_ADM,      /* activate */
        USIM_ACCESS_ADM,      /* deactivate */
        USIM_ACCESS_NEVER,    /* write */
        USIM_ACCESS_PIN,      /* update*/
        USIM_ACCESS_ALWAYS    /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    TRUE,                     /*sfi supported  */
    0x05,                     /*sfi*/
    FALSE                     /*Under USIM*/
};

/*-----------------18/10/01 08:40-------------------
 *  Ef LI:  Language indication
 *  For coding please refer to 131 102 section 4.2.1
 * --------------------------------------------------*/
Int8    defLiUsimData[] = { 'e','n','d','e','f','r','i','t', 'p','t',
                            'e','s','n','l','n','o','s','v','d','a','t','r' };

SimEfData    defLiUsimEfData =
{
    SIM_EMU_EF_LI,              /* Id           */
    SIM_EFSTRUCT_T,         /* Type         */
    SIM_DIR_ADF_USIM,       /* Dir          */
    1,                      /* num recs      */
    SIM_EMU_SIZE_USIM_LI_FILE,   /* file length     */
    (0),                    /* curr Rec     */
    (0),
  { USIM_ACCESS_NEVER,    /* delete file*/
    USIM_ACCESS_NEVER,    /* terminate*/
    USIM_ACCESS_ADM,      /* activate */
    USIM_ACCESS_ADM,      /* deactivate */
    USIM_ACCESS_NEVER,    /* write */
    USIM_ACCESS_PIN,      /* update*/
    USIM_ACCESS_ALWAYS    /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    TRUE,                     /*sfi supported  */
    0x02,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

/*Int8    defImsiUsimData[] = { 0x08, 0x7f, 0x59, 0x44, 0x62, 0x03, 0x10, 0x77, 0x02 }; */

/*-----------------18/10/01 08:40-------------------
 *  Ef IMSI:  Language indication
 *  For coding please refer to 131 102 section 4.2.2
 * --------------------------------------------------*/
#if defined (SIM_EMU_REGISTER_ON_HPLMN)
Int8    defImsiUsimData[] = { 0x08,    /*length of IMSI*/
                              0x09, 0x10, 0x00, 0x11, 0x32, 0x54, 0x76, 0x98 };    /*IMSI*/
#else
Int8    defImsiUsimData[] = { 0x08,    /*length of IMSI*/
                              0x09, 0x10, 0x10, 0x10, 0x32, 0x54, 0x76, 0x98 };    /*IMSI*/

/* mod for TTCN L+L B1 ********** by chenwenhao 2018 04 27 begin */
Int8    defImsiUsimData1[] = { 0x08,    /*length of IMSI*/
                              0x09, 0x10, 0x20, 0x10, 0x32, 0x54, 0x16, 0x32 };    /*IMSI*/
/* mod for TTCN L+L B1 ********** by chenwenhao 2018 04 27 end */

#endif

SimEfData    defImsiUsimEfData =
{
    SIM_EMU_EF_IMSI,        /* Id           */
    SIM_EFSTRUCT_T,         /* Type         */
    SIM_DIR_ADF_USIM,       /* Dir          */
    1,                      /* Num Rec      */
    SIM_EMU_SIZE_USIM_IMSI_FILE, /* Rec length     */
    (0),                    /* curr Rec     */
    (0),
    {
    USIM_ACCESS_NEVER,    /* delete file*/
    USIM_ACCESS_NEVER,    /* terminate*/
    USIM_ACCESS_ADM,      /* activate */
    USIM_ACCESS_ADM,      /* deactivate */
    USIM_ACCESS_NEVER,    /* write */
    /*Modification for IT test, Chengguang Liu, Feb.17 09,Begin*/
    /*USIM_ACCESS_ADM,*/      /* update*/
    USIM_ACCESS_PIN,
    /*Modification for IT test, Chengguang Liu, Feb.17 09,end  */
    USIM_ACCESS_PIN    /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    TRUE,                     /*sfi supported  */
    0x07,                     /*sfi*/
    TRUE                      /*Under USIM*/
 };

 /*-----------------17/07/2003 16:32-----------------
  * EF ACC :
  * Access control class
  * please refer to 131 102 section 4.2.15
  * --------------------------------------------------*/
 /****************************************************
 Zhiwei Add for TDD IT TEST Seting access tech is TYPE A OR B Started
 *****************************************************/
     /*CQ00135656, Cgliu, 2022-02-25,Begin*/
#if ON_PC

    Int8     defAccTypeAUsimData[] = { 0x03, 0xFF }; //TYPE A
    
    SimEfData    defAccTypeAUsimEfData =
    {
        SIM_EMU_EF_ACC,         /* Id           */
        SIM_EFSTRUCT_T,         /* Type         */
        SIM_DIR_ADF_USIM,       /* Dir          */
        1,                      /* num rec      */
        SIM_EMU_SIZE_USIM_ACC_FILE,  /* Rec length     */
        (0),                    /* curr Rec     */
        (0),
       {
        USIM_ACCESS_NEVER,    /* delete file*/
        USIM_ACCESS_NEVER,    /* terminate*/
        USIM_ACCESS_ADM,      /* activate */
        USIM_ACCESS_ADM,      /* deactivate */
        USIM_ACCESS_NEVER,    /* write */
        USIM_ACCESS_ALWAYS,   /* update*/
        USIM_ACCESS_PIN       /* read*/
        },
        SIM_UICC_ACTIVATED_STATE, /*file State*/
        TRUE,                     /*sfi supported  */
        0x06,                     /*sfi*/
        TRUE                      /*Under USIM*/
    };


    Int8     defAccTypeBUsimData[] = { 0xFB, 0xFF }; //TYPE B
    
    SimEfData    defAccTypeBUsimEfData =
    {
        SIM_EMU_EF_ACC,         /* Id           */
        SIM_EFSTRUCT_T,         /* Type         */
        SIM_DIR_ADF_USIM,       /* Dir          */
        1,                      /* num rec      */
        SIM_EMU_SIZE_USIM_ACC_FILE,  /* Rec length     */
        (0),                    /* curr Rec     */
        (0),
       {
        USIM_ACCESS_NEVER,    /* delete file*/
        USIM_ACCESS_NEVER,    /* terminate*/
        USIM_ACCESS_ADM,      /* activate */
        USIM_ACCESS_ADM,      /* deactivate */
        USIM_ACCESS_NEVER,    /* write */
        USIM_ACCESS_ALWAYS,   /* update*/
        USIM_ACCESS_PIN       /* read*/
        },
        SIM_UICC_ACTIVATED_STATE, /*file State*/
        TRUE,                     /*sfi supported  */
        0x06,                     /*sfi*/
        TRUE                      /*Under USIM*/
    };


/****************************************************
 Zhiwei Add for TDD IT TEST Seting access tech is TYPE A OR B End
*****************************************************/
#endif 
        /*CQ00135656, Cgliu, 2022-02-25,End  */

 Int8   defAccUsimData[] = { 0x02, 0x00 };//is default value

SimEfData    defAccUsimEfData =
{
    SIM_EMU_EF_ACC,         /* Id           */
    SIM_EFSTRUCT_T,         /* Type         */
    SIM_DIR_ADF_USIM,       /* Dir          */
    1,                      /* num rec      */
    SIM_EMU_SIZE_USIM_ACC_FILE,  /* Rec length     */
    (0),                    /* curr Rec     */
    (0),
   {
    USIM_ACCESS_NEVER,    /* delete file*/
    USIM_ACCESS_NEVER,    /* terminate*/
    USIM_ACCESS_ADM,      /* activate */
    USIM_ACCESS_ADM,      /* deactivate */
    USIM_ACCESS_NEVER,    /* write */
    USIM_ACCESS_ALWAYS,      /* update*/
    USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    TRUE,                     /*sfi supported  */
    0x06,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

/*-----------------17/07/2003 16:32-----------------
  * EF LOCI :
  * Location information
  * please refer to 131 102 section 4.2.17
  * --------------------------------------------------*/

Int8    defLociUsimData[] = { 0xff, 0xff, 0xff, 0xff,    /*TMSI*/
                              0x00, 0xF1, 0x10, 0x01, 0x00,  /* LAI*/
                              0xff, 0x00  };   /*RFU + location update status*/

SimEfData    defLociUsimEfData =
{
    SIM_EMU_EF_LOCI,            /* Id           */
    SIM_EFSTRUCT_T,         /* Type         */
    SIM_DIR_ADF_USIM,       /* Dir          */
    1,                      /* num recs     */
    SIM_EMU_SIZE_USIM_LOCI_FILE, /* Recs length  */
    (0),                    /* curr Rec     */
    (0),
   {
    USIM_ACCESS_NEVER,    /* delete file*/
    USIM_ACCESS_NEVER,    /* terminate*/
    USIM_ACCESS_ADM,      /* activate */
    USIM_ACCESS_ADM,      /* deactivate */
    USIM_ACCESS_NEVER,    /* write */
    USIM_ACCESS_ALWAYS,      /* update*/
    USIM_ACCESS_ALWAYS       /* read*/
  },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    TRUE,                     /*sfi supported  */
    0x0b,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

/*CQ00135656, Cgliu, 2022-02-25,Begin*/
#if defined (ON_PC)
/*------------------------------------------------------
 * EF CNL: Cooperative Network List
 *
 * --------------------------------------------------*/
Int8    defCnlUsimData[] = {   0x32, 0xf4, 0x10, 0x21, 0x43, 0x65,
                               0x32, 0xf4, 0x20, 0x21, 0x43, 0x65,
                               0x32, 0xf4, 0x30, 0x21, 0x43, 0x65,
                               0x32, 0xf4, 0x40, 0x21, 0x43, 0x65};

SimEfData    defCnlUsimEfData =
{
    SIM_EMU_EF_CNL,                 /* Id           */
    SIM_EFSTRUCT_T,                 /* Type         */
    SIM_DIR_ADF_USIM,               /* Dir          */
    1,                              /* Num Rec      */
    SIM_EMU_SIZE_USIM_CNL_FILE,     /* Rec length    */
    (0),                            /* curr Rec     */
    (0),
   {
    USIM_ACCESS_NEVER,    /* delete file*/
    USIM_ACCESS_NEVER,    /* terminate*/
    USIM_ACCESS_ADM,      /* activate */
    USIM_ACCESS_ADM,      /* deactivate */
    USIM_ACCESS_NEVER,    /* write */
    USIM_ACCESS_PIN,      /* update*/
    USIM_ACCESS_PIN       /* read*/
  },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                     /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};
#endif 
/*CQ00135656, Cgliu, 2022-02-25,End  */

/*-----------------17/07/2003 16:37-----------------
 * EF FPLMN: Forbidden PLMN
 *
 * see section 4.2.16  for coding (TS 131 102)
 * --------------------------------------------------*/
Int8    defFplmnUsimData[] = { 0x32, 0xf4, 0x51,      /*FPLMN 1.   (MCC = 234 MNC = 15) */
                               0x32, 0xf4, 0x03,      /*FPLMN 2 */
                               0x32, 0xf4, 0x33,      /*FPLMN 3 */
                               0x32, 0xf4, 0x19 };    /*FPLMN 4 */

/* mod for CQ000XXXXX by yunhai 2015.0424 begin */
Int8    defFplmnUsimDataPlmn4[] = {0x00, 0xf4, 0x13};      /*FPLMN 1   (MCC = 001 MNC = 31) */
/* mod for CQ000XXXXX by yunhai 2015.0424 end */


SimEfData    defFplmnUsimEfData =
{
    SIM_EMU_EF_FPLMN,                   /* Id           */
    SIM_EFSTRUCT_T,                 /* Type         */
    SIM_DIR_ADF_USIM,               /* Dir          */
    1,                              /* Num Rec      */
    SIM_EMU_SIZE_USIM_FPLMN_FILE,   /* Rec length    */
    (0),                            /* curr Rec     */
    (0),
   {
    USIM_ACCESS_NEVER,    /* delete file*/
    USIM_ACCESS_NEVER,    /* terminate*/
    USIM_ACCESS_ADM,      /* activate */
    USIM_ACCESS_ADM,      /* deactivate */
    USIM_ACCESS_NEVER,    /* write */
    USIM_ACCESS_PIN,      /* update*/
    USIM_ACCESS_PIN       /* read*/
  },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    TRUE,                     /*sfi supported  */
    0x0d,                     /*sfi*/
    TRUE                      /*Under USIM*/
};


/*-----------------17/07/2003 16:37-----------------*
 * EF PLMN SEL: Preferred PLMN                      *
 * -------------------------------------------------*/

Int8    defUserPlmnSelUsimData[] = { 0x02, 0xf8, 0x10, 0x00, 0x80,
                                     0x32, 0xf8, 0x10, 0x00, 0x80,
                                     0x42, 0xf0, 0x10, 0x00, 0x80,
                                     0x22, 0xf2, 0x10, 0x00, 0x80,
                                     0x22, 0xf8, 0x10, 0x00, 0x80,
                                     0x72, 0xf0, 0x10, 0x00, 0x80,
                                     0x62, 0xf2, 0x10, 0x00, 0x80,
                                     0x62, 0xf1, 0x80, 0x00, 0x80,
                                    /* 0x62, 0xf0, 0x10, 0x00, 0x80,
                                     0xff, 0xff, 0xff, 0x00, 0x80,
                                     0xff, 0xff, 0xff, 0x00, 0x80,
                                     0xff, 0xff, 0xff, 0x00, 0x80*/ };

SimEfData    defUserPlmnSelUsimEfData =
{
    SIM_EMU_EF_PLMNW_ACT,               /* Id           */
    SIM_EFSTRUCT_T,                 /* Type         */
    SIM_DIR_ADF_USIM,               /* Dir          */
    1,                              /* Num Rec      */
    SIM_EMU_SIZE_USER_PLMNWACT_FILE,   /* Rec length    */
    (0),                            /* curr Rec     */
    (0),
   {
    USIM_ACCESS_NEVER,    /* delete file*/
    USIM_ACCESS_NEVER,    /* terminate*/
    USIM_ACCESS_ADM,      /* activate */
    USIM_ACCESS_ADM,      /* deactivate */
    USIM_ACCESS_NEVER,    /* write */
    USIM_ACCESS_PIN,      /* update*/
    USIM_ACCESS_PIN       /* read*/
  },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    TRUE,                     /*sfi supported  */
    0x0d,                     /*sfi*/
    TRUE                      /*Under USIM*/
};


/*-----------------17/07/2003 16:37-----------------*
 * EF HPLMN SELECTOR WITH ACCESS TECHNOLOGY         *
 * See TS 131 102 section 4.2.54                    *
 * -------------------------------------------------*/

Int8    defHplmnSelUsimData[] = { 0x02, 0xf8, 0x10, 0x00, 0x80,
                                  0x32, 0xf8, 0x10, 0x00, 0x80,
                                  0x42, 0xf0, 0x10, 0x00, 0x80,
                                  0x22, 0xf2, 0x10, 0x00, 0x80,
                                  0x22, 0xf8, 0x10, 0x00, 0x80,
                                  0x72, 0xf0, 0x10, 0x00, 0x80,
                                  0x62, 0xf2, 0x10, 0x00, 0x80,
                                  0x62, 0xf1, 0x80, 0x00, 0x80 };

SimEfData    defHplmnSelUsimEfData =
{
    SIM_EMU_EF_HPLMNW_ACT,               /* Id           */
    SIM_EFSTRUCT_T,                 /* Type         */
    SIM_DIR_ADF_USIM,               /* Dir          */
    1,                              /* Num Rec      */
    SIM_EMU_SIZE_HPLMNWACT_FILE,   /* Rec length    */
    (0),                            /* curr Rec     */
    (0),
   {
    USIM_ACCESS_NEVER,    /* delete file*/
    USIM_ACCESS_NEVER,    /* terminate*/
    USIM_ACCESS_ADM,      /* activate */
    USIM_ACCESS_ADM,      /* deactivate */
    USIM_ACCESS_NEVER,    /* write */
    USIM_ACCESS_PIN,      /* update*/
    USIM_ACCESS_PIN       /* read*/
  },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    TRUE,                     /*sfi supported  */
    0x13,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

/*-----------------17/07/2003 16:37-----------------*
 * EF OPLMNwAct: Operator controlled PLMN selector  *
 * with access technology                           *
 * -------------------------------------------------*/

Int8    defOperatorPlmnSelUsimData[] = { 0x02, 0xf8, 0x10, 0x00, 0x80,
                                         0x32, 0xf8, 0x10, 0x00, 0x80,
                                         0x42, 0xf0, 0x10, 0x00, 0x80,
                                         0x22, 0xf2, 0x10, 0x00, 0x80,
                                         0x22, 0xf8, 0x10, 0x00, 0x80,
                                         0x72, 0xf0, 0x10, 0x00, 0x80,
                                         0x62, 0xf2, 0x10, 0x00, 0x80,
                                         0x62, 0xf1, 0x80, 0x00, 0x80
                                    };

SimEfData    defOperatorPlmnSelUsimEfData =
{
    SIM_EMU_EF_OPLMNW_ACT,               /* Id           */
    SIM_EFSTRUCT_T,                 /* Type         */
    SIM_DIR_ADF_USIM,               /* Dir          */
    1,                              /* Num Rec      */
    SIM_EMU_SIZE_OPERATOR_PLMNWACT_FILE,   /* Rec length    */
    (0),                            /* curr Rec     */
    (0),
   {
    USIM_ACCESS_NEVER,    /* delete file*/
    USIM_ACCESS_NEVER,    /* terminate*/
    USIM_ACCESS_ADM,      /* activate */
    USIM_ACCESS_ADM,      /* deactivate */
    USIM_ACCESS_NEVER,    /* write */
    /*Modification for IT test, Chengguang Liu, Feb.16 09,Begin*/
    /*USIM_ACCESS_ADM,*/      /* update*/
    USIM_ACCESS_PIN,
    /*Modification for IT test, Chengguang Liu, Feb.16 09,End  */
    USIM_ACCESS_PIN       /* read*/
  },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    TRUE,                     /*sfi supported  */
    0x11,                     /*sfi*/
    TRUE                      /*Under USIM*/
};



/*CQ00135656, Cgliu, 2022-02-25,Begin*/
#if defined (ON_PC)


/*-----------------17/07/2003 16:44-----------------
 * EF CBMID
 * for coding please refer to 131 102 section 4.2.20
 * --------------------------------------------------*/
Int8 defCbmidUsimData[] = {
      0x12, 0x34,
      0x56, 0x78,
      0x90, 0x12,
      0x34, 0x56,
      0xff, 0xff
};

SimEfData defCbmidUsimEfData =
{
  SIM_EMU_EF_CBMID,                  /* Id           */
  SIM_EFSTRUCT_T,                /* Type         */
  SIM_DIR_ADF_USIM,              /* Dir          */
  1,                             /* Num Rec      */
  SIM_EMU_SIZE_USIM_CBMID_FILE,  /* Rec length     */
  (0),                           /* curr Rec     */
  (0),
  {
    USIM_ACCESS_NEVER,           /* delete file*/
    USIM_ACCESS_NEVER,           /* terminate*/
    USIM_ACCESS_ADM,             /* activate */
    USIM_ACCESS_ADM,             /* deactivate */
    USIM_ACCESS_NEVER,           /* write */
    USIM_ACCESS_ADM,              /* update*/
    USIM_ACCESS_PIN               /* read*/
  },
    SIM_UICC_ACTIVATED_STATE,    /*file State*/
    TRUE,                        /*sfi supported  */
    0x0e,                        /*sfi*/
    TRUE                         /*Under USIM*/
};

/*-----------------17/07/2003 16:44-----------------
 * EF DCK
 * for coding please refer to 131 102 section 4.2.49
 * --------------------------------------------------*/

Int8 defDckUsimData[] = { 'D', 'C', 'K', '1',
                          'D', 'C', 'K', '2',
                          'D', 'C', 'K', '3',
                          'D', 'C', 'K', '4'};

SimEfData defDckUsimEfData =
{
  SIM_EMU_EF_DCK,                  /* Id           */
  SIM_EFSTRUCT_T,              /* Type         */
  SIM_DIR_ADF_USIM,            /* Dir          */
  1,                           /* Num Rec      */
  SIM_EMU_SIZE_USIM_DCK_FILE,  /* Rec Len     */
  (0),                         /* curr Rec     */
  (0),
  {
    USIM_ACCESS_NEVER,    /* delete file*/
    USIM_ACCESS_NEVER,    /* terminate*/
    USIM_ACCESS_ADM,      /* activate */
    USIM_ACCESS_ADM,      /* deactivate */
    USIM_ACCESS_NEVER,    /* write */
    USIM_ACCESS_PIN,      /* update*/
    USIM_ACCESS_PIN       /* read*/
  },
  SIM_UICC_ACTIVATED_STATE, /*file State*/
  FALSE,                    /*sfi supported  */
  0x00,                     /*sfi*/
  TRUE                      /*Under USIM*/
};


/*-----------------17/07/2003 16:44-----------------
 * EF BDN
 * for coding please refer to 131 102 section 4.2.44
 * --------------------------------------------------*/

Int8 defBdnUsimData[] = {


        'B', 'D', 'N', '1', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x03,
        0x81,
        0x78, 0x78, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0xff,
        0xff,


        'B', 'D', 'N', '2', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,
        0x81,
        0x11, 0x11, 0x11, 0x11, 0x11, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0xff,
        0xff,

        'B', 'D', 'N', '3', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,
        0xa8,
        0x07, 0x63, 0x26, 0x26, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0x01,
        0xff,

        'B', 'D', 'N', '4', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,
        0xa8,
        0x07, 0x63, 0x26, 0x26, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0xff,
        0xff,


        'B', 'D', 'N', '5', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,
        0xa8,
        0x07, 0x63, 0x26, 0x26, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0xff,
        0xff,

        'B', 'D', 'N', '6', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,
        0xa8,
        0x07, 0x63, 0x26, 0x26, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0xff,
        0xff,


        'B', 'D', 'N', '7', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,
        0xa8,
        0x07, 0x63, 0x26, 0x26, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0xff,
        0xff,

        'B', 'D', 'N', '8', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,
        0xa8,
        0x07, 0x63, 0x26, 0x26, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0xff,
        0xff,


        'B', 'D', 'N', '9', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,
        0xa8,
        0x07, 0x63, 0x26, 0x26, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0xff,
        0xff,

        'B', 'D', 'N', '1', '0', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x03,
        0x81,
        0x33, 0x33, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0xff,
        0xff,


        'B', 'D', 'N', '1', '1', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x03,
        0x81,
        0x78, 0x78, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0xff,
        0xff,

        'B', 'D', 'N', '1', '2', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,
        0x81,
        0x11, 0x11, 0x11, 0x11, 0x11, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0xff,
        0xff,


        'B', 'D', 'N', '1', '3', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,
        0xa8,
        0x07, 0x63, 0x26, 0x26, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0x01,
        0xff,


       'B', 'D', 'N', '1', '4', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,
        0xa8,
        0x07, 0x63, 0x26, 0x26, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0xff,
        0xff,


        'B', 'D', 'N', '1', '5', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,
        0xa8,
        0x07, 0x63, 0x26, 0x26, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0xff,
        0xff,

        'B', 'D', 'N', '1', '6', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,
        0xa8,
        0x07, 0x63, 0x26, 0x26, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0xff,
        0xff,


        'B', 'D', 'N', '1', '7', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,
        0xa8,
        0x07, 0x63, 0x26, 0x26, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0xff,
        0xff,

        'B', 'D', 'N', '1', '8',0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,
        0xa8,
        0x07, 0x63, 0x26, 0x26, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0xff,
        0xff,


        'B', 'D', 'N', '1', '9', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,
        0xa8,
        0x07, 0x63, 0x26, 0x26, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0xff,
        0xff,

        'B', 'D', 'N', '2', '0', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x03,
        0x81,
        0x33, 0x33, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0xff,
        0xff



};



SimEfData defBdnUsimEfData =
{
  SIM_EMU_EF_BDN,                  /* Id       */
  SIM_EFSTRUCT_LF,             /* Type     */
  SIM_DIR_ADF_USIM,          /* Dir      */
  SIM_EMU_NUM_USIM_BDN_RECS,   /* Num recs */
  SIM_EMU_SIZE_USIM_BDN_FILE,  /* Rec Len  */
  (0),                         /* curr Rec */
  (0),
  {
    USIM_ACCESS_NEVER,    /* delete file*/
    USIM_ACCESS_NEVER,    /* terminate*/
    USIM_ACCESS_ADM,      /* activate */
    USIM_ACCESS_ADM,      /* deactivate */
    USIM_ACCESS_NEVER,    /* write */
    USIM_ACCESS_PIN2,     /* update*/
    USIM_ACCESS_PIN       /* read*/
  },
  SIM_UICC_ACTIVATED_STATE, /*file State*/
  FALSE,                    /*sfi supported  */
  0x00,                     /*sfi*/
  TRUE                      /*Under USIM*/
};
#endif 
/*CQ00135656, Cgliu, 2022-02-25,End  */

/*-----------------17/07/2003 16:44-----------------
 * EF AD  Administrative Data
 * for coding please refer to 131 102 section 4.2.18
 * --------------------------------------------------*/

Int8    defAdUsimData[] =  { MS_OPERN_TA,  /*UE operation mode*/

#if defined (SIM_EMU_DISABLE_MNC_LENGTH)
                             'A', 'D'};  /*additional info*/
#else
                             'A', 'D',  /*additional info*/
                                 /*Zhiwei change for cq3731 change MNC Length is 2 started*/
                              2}; //3}; /*CQ00133810,Cgliu, 2021-11-04*/ /* length of MNC in the IMSI is now mandatory */ 
                              //2};/*Zhiwei change for cq3731 change MNC Length is 2 end*/
#endif
SimEfData    defAdUsimEfData =
{
    SIM_EMU_EF_AD,              /* Id           */
    SIM_EFSTRUCT_T,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,                          /* Num rec      */
    SIM_EMU_SIZE_USIM_AD_FILE,  /* rec Length     */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_ADM,      /* update*/
     USIM_ACCESS_ALWAYS    /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    TRUE,                     /*sfi supported  */
    0x03,                     /*sfi*/
    TRUE                      /*Under USIM*/
};
/*CQ00135656, Cgliu, 2022-02-25,Begin*/
#if defined (ON_PC)
Int8  defImgData[] =
{
 /* record number 1 */
  0x01, /* number of instances */
  0x08, 0x08, 0x11, 0x4F, 0x04, 0x00, 0x00, 0x00, 0x0A, /* record 1, instance 1 */
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,

  /* record number 2 */
  0x01, /* number of instances */
  0x08, 0x08, 0x21, 0x4F, 0x02, 0x00, 0x00, 0x00, 0x16, /* record 2, instance 1 */
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,

  /* record number 3 */
  0x01, /* number of instances */
  0x18, 0x10, 0x11, 0x4F, 0x03, 0x00, 0x00, 0x00, 0x32, /* record 3, instance 1 */
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,

  /* record number 4 */
  0x01, /* number of instances */
  0x2E, 0x28, 0x11, 0x4F, 0x01, 0x00, 0x00, 0x00, 0xE8, /* record 4, instance 1 */
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,

  /* record number 5 */
  0x01, /* number of instances */
  0x05, 0x05, 0x11, 0x4F, 0x05, 0x00, 0x00, 0x00, 0x08, /* record 5, instance 1 */
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
};

SimEfData  defImgEfData =
{
  SIM_EMU_EF_IMG,               /* Id           */
  SIM_EFSTRUCT_LF,              /* Type         */
  SIM_DIR_DF_GRAPHICS,          /* Dir          */
  SIM_EMU_NUM_IMG_RECS,         /* num Recs     */
  SIM_EMU_SIZE_IMG_FILE,        /* Rec Len      */
  (0),                          /* curr Rec     */
  (0),
  {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate   */
     USIM_ACCESS_ADM,      /* activate    */
     USIM_ACCESS_ADM,      /* deactivate  */
     USIM_ACCESS_NEVER,    /* write       */
     USIM_ACCESS_PIN,      /* update      */
     USIM_ACCESS_PIN       /* read        */
  },
    SIM_UICC_ACTIVATED_STATE, /*file State     */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi            */
    FALSE                     /*Under USIM     */
};

 Int8  const defImgInst1Data [] =
{
  0x2E, 0x28, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xFF, 0x80,
  0x00, 0x00, 0x00, 0x0F, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x77, 0xFE, 0x00,
  0x00, 0x00, 0x01, 0xBF, 0xF8, 0x00, 0x00, 0x00, 0x06, 0xFF, 0xE0, 0x00,
  0x00, 0x00, 0x1A, 0x03, 0x80, 0x00, 0x00, 0x00, 0x6B, 0xF6, 0xBC, 0x00,
  0x00, 0x01, 0xAF, 0xD8, 0x38, 0x00, 0x00, 0x06, 0xBF, 0x60, 0x20, 0x00,
  0x00, 0x1A, 0xFD, 0x80, 0x40, 0x00, 0x00, 0x6B, 0xF6, 0x00, 0x80, 0x00,
  0x01, 0xA0, 0x1F, 0x02, 0x00, 0x00, 0x06, 0xFF, 0xE4, 0x04, 0x00, 0x00,
  0x1B, 0xFF, 0x90, 0x10, 0x00, 0x00, 0x6D, 0xEE, 0x40, 0x40, 0x00, 0x01,
  0xBF, 0xF9, 0x01, 0x00, 0x00, 0x6F, 0xFF, 0xE4, 0x04, 0x00, 0x00, 0x1B,
  0xFF, 0x90, 0x10, 0x00, 0x00, 0x6F, 0xFE, 0x40, 0x40, 0x00, 0x01, 0xBF,
  0xF9, 0x01, 0x00, 0x00, 0x06, 0xFF, 0xE6, 0x04, 0x00, 0x00, 0x1B, 0xFF,
  0x88, 0x10, 0x00, 0x00, 0x6F, 0xFE, 0x20, 0x40, 0x00, 0x01, 0xBF, 0xF8,
  0x66, 0x00, 0x00, 0x06, 0xFF, 0xE0, 0xF0, 0x00, 0x00, 0x1B, 0xFF, 0x80,
  0x80, 0x00, 0x00, 0x7F, 0xFE, 0x00, 0x00, 0x00, 0x03, 0x00, 0x0C, 0x00,
  0x00, 0x00, 0x1F, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x1C, 0x21, 0x08, 0x44, 0xEE, 0x00, 0x48, 0xC4, 0x31, 0x92, 0x20, 0x01,
  0x25, 0x11, 0x45, 0x50, 0x80, 0x07, 0x14, 0x45, 0x15, 0x43, 0x80, 0x12,
  0x71, 0x1C, 0x4D, 0x08, 0x00, 0x4A, 0x24, 0x89, 0x32, 0x20, 0x01, 0xC8,
  0x9E, 0x24, 0x4E, 0xE0
};

#define SIM_EMU_SIZE_IMG_INST1_FILE sizeof defImgInst1Data

SimEfData defImgInst1EfData =
{
  SIM_EMU_EF_IMGINST1,          /* Id           */
  SIM_EFSTRUCT_T,               /* Type         */
  SIM_DIR_DF_GRAPHICS,          /* Dir          */
  1,                            /* rec len      */
  SIM_EMU_SIZE_IMG_INST1_FILE,  /* num recs     */
  (0),                          /* curr Rec     */
  (0),
  {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
  },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    FALSE                     /*Under USIM*/
};

 Int8  const defImgInst2Data [] =
{
  0x08, 0x08, 0x02, 0x03, 0x00, 0x16, 0xAA, 0xAA, 0x80, 0x02, 0x85, 0x42,
  0x81, 0x42, 0x81, 0x42, 0x81, 0x52, 0x80, 0x02, 0xAA, 0xAA, 0xFF, 0x00,
  0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0xFF
};

#define SIM_EMU_SIZE_IMG_INST2_FILE sizeof defImgInst2Data

SimEfData defImgInst2EfData =
{
  SIM_EMU_EF_IMGINST2,          /* Id           */
  SIM_EFSTRUCT_T,               /* Type         */
  SIM_DIR_DF_GRAPHICS,          /* Dir          */
  1,                            /* rec len      */
  SIM_EMU_SIZE_IMG_INST2_FILE,  /* num recs     */
  (0),                          /* curr Rec     */
  (0),
  {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
  },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    FALSE                     /*Under USIM*/
};

Int8 const defImgInst3Data[] =
{
  0x18, 0x10, 0xFF, 0xFF, 0xFF, 0x80, 0x00, 0x01, 0x80, 0x00, 0x01, 0x80,
  0x00, 0x01, 0x8F, 0x3C, 0xF1, 0x89, 0x20, 0x81, 0x89, 0x20, 0x81, 0x89,
  0x20, 0xF1, 0x89, 0x20, 0x11, 0x89, 0x20, 0x11, 0x89, 0x20, 0x11, 0x8F,
  0x3C, 0xF1, 0x80, 0x00, 0x01, 0x80, 0x00, 0x01, 0x80, 0x00, 0x01, 0xFF,
  0xFF, 0xFF
};
#define SIM_EMU_SIZE_IMG_INST3_FILE sizeof defImgInst3Data

SimEfData defImgInst3EfData =
{
  SIM_EMU_EF_IMGINST3,          /* Id           */
  SIM_EFSTRUCT_T,               /* Type         */
  SIM_DIR_DF_GRAPHICS,          /* Dir          */
  1,                            /* rec len      */
  SIM_EMU_SIZE_IMG_INST3_FILE,  /* num recs     */
  (0),                          /* curr Rec     */
  (0),
  {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
  },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    FALSE                     /*Under USIM*/
};

Int8 const defImgInst4Data[] =
{
  0x08, 0x08, 0xFF, 0x03, 0xA5, 0x99, 0x99, 0xA5, 0xC3, 0xFF
};

#define SIM_EMU_SIZE_IMG_INST4_FILE sizeof defImgInst4Data

SimEfData defImgInst4EfData =
{
  SIM_EMU_EF_IMGINST4,          /* Id           */
  SIM_EFSTRUCT_T,               /* Type         */
  SIM_DIR_DF_GRAPHICS,          /* Dir          */
  1,                            /* rec len      */
  SIM_EMU_SIZE_IMG_INST4_FILE,  /* num recs     */
  (0),                          /* curr Rec     */
  (0),
  {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
  },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    FALSE                     /*Under USIM*/
};

Int8 const defImgInst5Data[] =
{
  0x05, 0x05, 0xFE, 0xEB, 0xBF, 0xFF, 0xFF, 0xFF
};

#define SIM_EMU_SIZE_IMG_INST5_FILE sizeof defImgInst5Data

SimEfData defImgInst5EfData =
{
  SIM_EMU_EF_IMGINST5,          /* Id           */
  SIM_EFSTRUCT_T,               /* Type         */
  SIM_DIR_DF_GRAPHICS,          /* Dir          */
  1,                            /* rec len      */
  SIM_EMU_SIZE_IMG_INST5_FILE,  /* num recs     */
  (0),                          /* curr Rec     */
  (0),
  {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
  },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    FALSE                     /*Under USIM*/
};


/*-----------------17/07/2003 16:44-----------------
 * EF ACM  Accumulated Call Meter
 * for coding please refer to 131 102 section 4.2.9
 * --------------------------------------------------*/

Int8    defAcmUsimData[] =  { 0x00, 0x00, 0x00,
                              0x00, 0x00, 0x00};

SimEfData    defAcmUsimEfData =
{
    SIM_EMU_EF_ACM,                 /* Id           */
    SIM_EFSTRUCT_C,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    SIM_EMU_NUM_USIM_ACM_RECS,  /* Num rec      */
    SIM_EMU_SIZE_USIM_ACM_FILE, /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

/*-----------------17/07/2003 16:44-----------------
 * EF ACM  Accumulated Call Meter Max
 * for coding please refer to 131 102 section 4.2.7
 * --------------------------------------------------*/

Int8    defAcmMaxUsimData[] =  { 'M', 'A', 'X'};

SimEfData    defAcmMaxUsimEfData =
{
    SIM_EMU_EF_ACM_MAX,             /* Id           */
    SIM_EFSTRUCT_T,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,                          /* Num rec      */
    SIM_EMU_SIZE_USIM_ACM_MAX_FILE, /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_ALWAYS,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};
#endif 
/*CQ00135656, Cgliu, 2022-02-25,End  */




/*-----------------18/10/01 10:39-------------------
 * Ef UST: USIM service table
 * For coding please refer to 131 102 section 4.2.8
 * --------------------------------------------------*/
/*Modification for access SIM EMU data, Chengguang Liu, April.13 09,Begin*/
 #if 0
                                        /*   bit 8       bit7      bit6    bit5     bit4      bit3    bit2       bit1    */
Int8 defUstUsimData[] = { 0xff,         /*  OCI/OCT      EXT4      BDN     EXT3     SDN       EXT2    FDN        local PB */
                          0xef,         /*  CBMIR        CBMI      CCP     AoC      SMSP      SMSR    SMS        ICI    */
                          0xff,         /*  eMLPP        unused    IMG     MSISDN   UserPLMN  SPN     GID2       GID1*/
#if defined DEBUG_CALL_CONTROL_BY_USIM
                          0xff,         /*  run AT       mo-sms    cc      sms-cb   sms-pp    GSM a.  RFU        automatic answer*/
#else
                          0x1c,         /*  run AT       mo-sms    cc      sms-cb   sms-pp    GSM a.  RFU        automatic answer*/
#endif
                          0xff,         /*   inv. scan   CPBCCH   GSM sec.  CNL      dck       apn     est        1  */
#if defined (SIM_EMU_ENABLE_EONS)
#if defined (SIM_EMU_DISABLE_OPL)
                          0xde,         /*   MWIS     Mailboxdn OperatorPLMN PLMNname ext5   HPLMNwAct OPLMNwAct  MExE*/
#else
                          0xff,         /*   MWIS     Mailboxdn OperatorPLMN PLMNname ext5   HPLMNwAct OPLMNwAct  MExE*/
#endif
#else
                          0xcf,         /*   MWIS     Mailboxdn OperatorPLMN PLMNname ext5   HPLMNwAct OPLMNwAct  MExE*/
#endif
                          0x01          /*  N/A      MMS user c. cc on GPRS  ext8     MMS        SPD      RPLMN      CFIS*/
//csg only						  0x30,		  	/*service 88 ....................ASG(86)....EpsMmInformation(85).......................service 81*/
//csg only						  0x02,		  	/*								service 91 ................... ................service 89*/


		};
#endif /*end if 0*/
/*Modification for CQ00003401, cgLiu,Feb.21 2010, begin*/
/*Int8 defUstUsimData[] = { 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff};*/
/*CQ00135656, Cgliu, 2022-02-25,Begin*/
#if defined (ON_PC)
Int8 defUstUsimData[] = { 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff};
#else
Int8 defUstUsimData[] = { 0x00,0x00,0x08,0x00,0x01,0x26,0x00,0x00,0x40,0x01,0x10};

#endif 
/*CQ00135656, Cgliu, 2022-02-25,End  */

/*Modification for CQ00003401, cgLiu,Feb.21 2010, end  */

/*Modification for access SIM EMU datal, Chengguang Liu, April. 13 09,End  */

SimEfData defUstUsimEfData =
{
  SIM_EMU_EF_UST,              /* Id           */
  SIM_EFSTRUCT_T,              /* Type         */
  SIM_DIR_ADF_USIM,            /* Dir          */
  1,                           /* Num rec      */
  SIM_EMU_SIZE_USIM_UST_FILE,  /* num Recs     */
  (0),                         /* curr Rec     */
  (0),
  {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
  },
  SIM_UICC_ACTIVATED_STATE, /*file State*/
  TRUE,                    /*sfi supported  */
  0x04,                     /*sfi*/
  TRUE                      /*Under USIM*/
};

 /*-----------------18/10/01 10:39-------------------
 * Ef EST: Enabled Service table
  * for coding see section 4.2.47
 * --------------------------------------------------*/
#if defined (DEBUG_FDN_ENABLED)
Int8 defEstUsimData[] = { 0x01 };     /*FDN (bit1)    BDN  (bit2)  APN (bit3)*/
#else
#if defined (DEBUG_ACL_ENABLED)
Int8 defEstUsimData[] = { 0x04 };     /*FDN (bit1)    BDN  (bit2)  APN (bit3)*/
#else
Int8 defEstUsimData[] = { 0x00 };     /*FDN (bit1)    BDN  (bit2)  APN (bit3)*/
#endif
#endif

SimEfData defEstUsimEfData =
{
  SIM_EMU_EF_EST,              /* Id           */
  SIM_EFSTRUCT_T,              /* Type         */
  SIM_DIR_ADF_USIM,            /* Dir          */
  1,                           /* Num rec      */
  SIM_EMU_SIZE_USIM_EST_FILE,  /* num Recs     */
  (0),                         /* curr Rec     */
  (0),
  {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN2,     /* update*/
     USIM_ACCESS_PIN       /* read*/
  },
  SIM_UICC_ACTIVATED_STATE, /*file State*/
  TRUE,                     /*sfi supported  */
  0x05,                     /*sfi*/
  TRUE                      /*Under USIM*/
};

 /*CQ00135656, Cgliu, 2022-02-25,Begin*/
#if defined (ON_PC)

 /*-----------------18/10/01 10:39-------------------
 * Ef FDN: fixed dialled numbers
 * for coding see section 4.2.24
 * --------------------------------------------------*/

Int8    defFdnUsimData[] =
{
        'F', 'D', 'N', '1',0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x03,
        0x81,0x11,0x11,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,
        0xff,

        'F', 'D', 'N', '2',0x32,0x32,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x03,
        0x81,0x22,0x22,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,
        0xff,

        'F', 'D', 'N', '3',0x33,0x33,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x03,
        0x81,0x33,0x33,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,
        0xff

};

/*-----------------18/10/01 10:39-------------------
 * Ef FDN: fixed dialled numbers
 * for coding see section 4.2.24 Note: this file
 * is used after a REFRESH command
 * to simulate the new refreshed FDN file
 * --------------------------------------------------*/

Int8    defFdn2UsimData[] =
{
        'F', 'D', 'N', '4',0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x03,
        0x81,0x44,0x44,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,
        0xff,


        'F', 'D', 'N', '5',0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x03,
        0x81,0x55,0x55,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,
        0xff,

        'F', 'D', 'N', '6',0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x03,
        0x81,0x66,0x66,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,
        0xff

};



SimEfData    defFdnUsimEfData =
{
#if defined(SIM_EMU_PHASE1)
    SIM_EMU_EF_INVALID,
#else
    SIM_EMU_EF_FDN,                  /* Id           */
#endif
    SIM_EFSTRUCT_LF,             /* Type         */
    SIM_DIR_ADF_USIM,            /* Dir          */
    SIM_EMU_NUM_USIM_FDN_RECS,   /* num Recs     */
    SIM_EMU_SIZE_USIM_FDN_FILE,  /* Rec Len      */
    (0),                         /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN2,     /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};

/*-----------------17/07/2003 16:52-----------------
 * EF CBMI
 * se section 4.2.14
 * --------------------------------------------------*/
Int8    defCbmiUsimData[] = { 'C', 'B',       /* CB message identifier 1*/
                              'M', 'I',       /* CB message identifier 2*/
                              0x02, 0x02,     /* CB message identifier 3*/
                              0x03, 0x03,     /* CB message identifier 4*/
                              0x04, 0x04 };   /* CB message identifier 5*/

SimEfData    defCbmiUsimEfData =
{
    SIM_EMU_EF_CBMI,                    /* Id           */
    SIM_EFSTRUCT_T,                 /* Type         */
    SIM_DIR_ADF_USIM,               /* Dir          */
    1,                              /* Rec Len      */
    SIM_EMU_SIZE_USIM_CBMI_FILE,    /* num Recs     */
    (0),                            /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,     /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};

/*-----------------18/07/2003 09:19-----------------
 * EF SMSR    (short message status report)
 * see section 4.2.32
 * --------------------------------------------------*/
Int8 defSmsrUsimData[] = {
        0x02,
      0x87, 0x09, 0xc,  0x81, 0x87, 0x09, 0x21, 0x43, 0x65, 0x87, 0x09, 0x2,
        0x02, 0x0c, 0x21, 0x43, 0x65, 0x87, 0x09, 0x21, 0x43, 0x65, 0x87, 0x09,
      0x01, 0x02, 0x12, 0x21, 0x01,

      0x00,
      0x87, 0x09, 0xc, 0x81, 0x65, 0x87, 0x09, 0x05, 0x02, 0x02, 0x11, 0x22,
        0x02, 0x0c, 0x21, 0x43, 0x65, 0x87, 0x09, 0x21, 0x43, 0x65, 0x87, 0x09,
      0x01, 0x02, 0x12, 0x21, 0x02,

      0x01,
      0x87, 0x09, 0xc,  0x81, 0x65, 0x87, 0x09, 0x05, 0x02, 0x02, 0x11, 0x22,
        0x02, 0x0c, 0x21, 0x43, 0x65, 0x87, 0x09, 0x21, 0x43, 0x65, 0x87, 0x09,
      0x01, 0x02, 0x12, 0x21, 0x03,

      0x0,
      0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
      0xff, 0xff, 0xff, 0xff, 0xff,


      0x03,
      0x87, 0x09, 0xc,  0x81, 0x65, 0x87, 0x09, 0x05, 0x02, 0x02, 0x11, 0x22,
        0x02, 0x0c, 0x21, 0x43, 0x65, 0x87, 0x09, 0x21, 0x43, 0x65, 0x87, 0x09,
      0x01, 0x02, 0x12, 0x21, 0x05,

      0x04,
      0x87, 0x09, 0xc,  0x81, 0x65, 0x87, 0x09, 0x05, 0x02, 0x02, 0x11, 0x22,
        0x02, 0x0c, 0x21, 0x43, 0x65, 0x87, 0x09, 0x21, 0x43, 0x65, 0x87, 0x09,
      0x01, 0x02, 0x12, 0x21, 0x06,

      0x0c,
      0x87, 0x09, 0xc,  0x81, 0x65, 0x87, 0x09, 0x05, 0x02, 0x02, 0x11, 0x22,
        0x02, 0x0c, 0x21, 0x43, 0x65, 0x87, 0x09, 0x21, 0x43, 0x65, 0x87, 0x09,
      0x01, 0x02, 0x12, 0x21, 0x07,

      0x0f,
      0x87, 0x09, 0xc,  0x81, 0x65, 0x87, 0x09, 0x05, 0x02, 0x02, 0x11, 0x22,
        0x02, 0x0c, 0x21, 0x43, 0x65, 0x87, 0x09, 0x21, 0x43, 0x65, 0x87, 0x09,
      0x01, 0x02, 0x12, 0x21, 0x08,

      0x1f,
      0x87, 0x09, 0xc,  0x81, 0x65, 0x87, 0x09, 0x05, 0x02, 0x02, 0x11, 0x22,
        0x02, 0x0c, 0x21, 0x43, 0x65, 0x87, 0x09, 0x21, 0x43, 0x65, 0x87, 0x09,
      0x01, 0x02, 0x12, 0x21, 0x09,

      0x0,
      0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
      0xff, 0xff, 0xff, 0xff, 0xff
};

SimEfData    defSmsrUsimEfData =     /*job 100781*/
{

    SIM_EMU_EF_SMSR,            /* Id           */
    SIM_EFSTRUCT_LF,         /* Type         */
    SIM_DIR_ADF_USIM,       /* Dir          */
    SIM_EMU_NUM_USIM_SMSR_RECS,   /*num records*/
    SIM_EMU_SIZE_USIM_SMSR_FILE,   /*record length*/
    (0),                      /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};

/*-----------------18/07/2003 09:23-----------------
 * EF SMSS (SMS status)
 * see section 4.2.28
 * --------------------------------------------------*/

Int8    defSmssUsimData[] = { 0x8a, 0xff };

SimEfData    defSmssUsimEfData =
{

    SIM_EMU_EF_SMSS,            /* Id           */
    SIM_EFSTRUCT_T,         /* Type         */
    SIM_DIR_ADF_USIM,       /* Dir          */
    1,                      /* num Recs      */
    SIM_EMU_SIZE_USIM_SMSS_FILE,                      /* Recs  len   */
    (0),                      /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};



/*-----------------18/07/2003 09:23-----------------
 * EF SMSP (SMS parameters)
 * see section 4.2.27
 * --------------------------------------------------*/

Int8    defSmspUsimData[] = {
                          '1', 's', 't',
                          0xe0,
                          0x14, 0xa8, 0x11, 0x22, 0x33, 0x44,
                          0x55, 0x66, 0x77, 0x88, 0x99, 0x00,
                          0x0a, 0xa8, 0x11, 0x22, 0x33, 0x44,
                          0x55, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0x3f,
                          0xf6,
                          0x9c,

                          '2', 'n', 'd',
                          0xf3,
                          0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0x3f,
                          0xf6,
                          0x90,

                          '3', 'r', 'd',
                          0xff,
                          0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0xff,
                          0xff,
                          0xff,

                          0xff, 0xff, 0xff,
                          0xff,
                          0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0xff,
                          0xff,
                          0xff,

                          '5', 't', 'h',
                          0xef,
                          0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0xff,
                          0xff,
                          0x23
                  };
SimEfData    defSmspUsimEfData =
{

    SIM_EMU_EF_SMSP,            /* Id           */
    SIM_EFSTRUCT_LF,        /* Type         */
    SIM_DIR_ADF_USIM,       /* Dir          */
    SIM_EMU_NUM_USIM_SMSP_RECS,                      /* num Recs      */
    SIM_EMU_SIZE_USIM_SMSP_FILE,                      /* Recs  len   */
    (0),                      /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};

/*-----------------18/07/2003 09:23-----------------
 * EF PUCT (Price per unit and currency table)
 * see section 4.2.27
 * --------------------------------------------------*/


Int8    defPuctUsimData[] = { 0x12, 0x34, 0x56, 0x00, 0x2a };

SimEfData    defPuctUsimEfData =
{
    SIM_EMU_EF_PUCT,            /* Id           */
    SIM_EFSTRUCT_T,         /* Type         */
    SIM_DIR_ADF_USIM,       /* Dir          */
    1,                      /* Rec num      */
    SIM_EMU_SIZE_USIM_PUCT_FILE,   /* Rec len     */
    (0),                      /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};

/*-----------------27/03/02 09:28-------------------
 * SMS file
 * --------------------------------------------------*/

Int8    defSmsUsimData[][176] =
{
     {  0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
     },
     {  0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
     },
     {  0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
     },
     {  0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
     },
     {  0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
     }
};

SimEfData    defSmsUsimEfData =
{
    SIM_EMU_EF_SMS,                    /* Id           */
    SIM_EFSTRUCT_LF,               /* Type         */
    SIM_DIR_ADF_USIM,              /* Dir          */
    SIM_EMU_NUM_USIM_SMS_RECS,     /* Rec num      */
    SIM_EMU_SIZE_USIM_SMS_FILE,    /* Rec len     */
    (0),                           /* curr Rec     */
    (0),
  {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};






/*-----------------18/07/2003 13:03-----------------
 * EF SPN service provider name
 * --------------------------------------------------*/

Int8    defSpnUsimData[] = { 0x01,
                             'U','S', 'I', 'M', ' ', 'E', 'M', 'U', 'L','A', 'T', 'O', 'R', 0xff, 0xff, 0xff  };

SimEfData    defSpnUsimEfData =
{
    SIM_EMU_EF_SPN,         /* Id           */
    SIM_EFSTRUCT_T,         /* Type         */
    SIM_DIR_ADF_USIM,       /* Dir          */
    1,                      /* Rec Len      */
    SIM_EMU_SIZE_USIM_SPN_FILE,  /* num Recs     */
    (0),                    /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_ADM,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};

/*-----------------02/04/02 11:15-------------------
 * EF ICI : Incoming Call Information
 * --------------------------------------------------*/
Int8 defIciUsimData[] = {
        'I', 'C', 'I', '1', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,                                                             /*length of BCD number*/
        0xa8,                                                             /*TON and NPI*/
        0x07, 0x63, 0x26, 0x26, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0x0b,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x22,                                                 /*Incoming call duration*/
        0xf0,                                                             /*Incoming call status */
        0xff, 0xff, 0xff,                                                 /*link to phonebook entry*/

        'I', 'C', 'I', '2', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,
        0xa8,
        0x07, 0x63, 0x26, 0x26, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,                                                             /*CCP */
        0x0b,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0xff, 0xff, 0xff,                                                 /*Incoming call duration*/
        0xff,                                                             /*Icoming call status */
        0xff, 0xff, 0xff,                                                 /*link to phonebook entry*/

        'I', 'C', 'I', '3', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,
        0xa8,
        0x07, 0x63, 0x26, 0x26, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,                                                              /*CCP */
        0x0c,                                                              /*ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0xff, 0xff, 0xff,                                                 /*Incoming call duration*/
        0xff,                                                             /*Icoming call status */
        0xff, 0xff, 0xff,                                                 /*link to phonebook entry*/

        'I', 'C', 'I', '4', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,
        0xa8,
        0x07, 0x63, 0x26, 0x26, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,                                                             /*CCP */
        0x0b,                                                             /*ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0xff, 0xff, 0xff,                                                 /*Incoming call duration*/
        0xff,                                                             /*Icoming call status */
        0xff, 0xff, 0xff,                                                 /*link to phonebook entry*/

        'I', 'C', 'I', '5', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,
        0xa8,
        0x07, 0x63, 0x26, 0x26, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,                                                             /*CCP */
        0x0b,                                                             /*ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0xff, 0xff, 0xff,                                                 /*Incoming call duration*/
        0xff,                                                             /*Icoming call status */
        0xff, 0xff, 0xff,                                                 /*link to phonebook entry*/

        'I', 'C', 'I', '6', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,
        0xa8,
        0x07, 0x63, 0x26, 0x26, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,                                                             /*CCP */
        0x0b,                                                             /*ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0xff, 0xff, 0xff,                                                 /*Incoming call duration*/
        0xff,                                                             /*Icoming call status */
        0xff, 0xff, 0xff,                                                 /*link to phonebook entry*/

        'I', 'C', 'I', '7', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0x04,
        0x81,
        0x55, 0x55, 0x55, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,                                                             /*CCP */
        0xff,                                                             /*ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0xff, 0xff, 0xff,                                                 /*Incoming call duration*/
        0xff,                                                             /*Icoming call status */
        0xff, 0xff, 0xff                                                  /*link to phonebook entry*/


};

SimEfData defIciUsimEfData =
{
  SIM_EMU_EF_ICI,                 /* Id       */
  SIM_EFSTRUCT_C,                 /* Type     */
  SIM_DIR_ADF_USIM,               /* Dir      */
  SIM_EMU_NUM_USIM_ICI_RECS,      /* Num recs */
  SIM_EMU_SIZE_USIM_ICI_FILE,     /* Rec Len  */
  (0),                            /* curr Rec */
  (0),
  {
    USIM_ACCESS_NEVER,    /* delete file*/
    USIM_ACCESS_NEVER,    /* terminate*/
    USIM_ACCESS_ADM,      /* activate */
    USIM_ACCESS_ADM,      /* deactivate */
    USIM_ACCESS_NEVER,    /* write */
    USIM_ACCESS_PIN,      /* update*/
    USIM_ACCESS_PIN       /* read*/
  },
  SIM_UICC_ACTIVATED_STATE, /*file State*/
  TRUE,                    /*sfi supported  */
  0x14,                     /*sfi*/
  TRUE                      /*Under USIM*/
};

/*-----------------02/04/02 11:15-------------------
 * EF OCI: Outgoing Call Information
 * --------------------------------------------------*/
Int8 defOciUsimData[] = {
        'O', 'C', 'I', '1', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,                                                             /*length of BCD number*/
        0xa8,                                                             /*TON and NPI*/
        0x07, 0x63, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26, 0x26,       /*Dialling number */
        0xff,                                                             /*CCP */
        0x03,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0xff, 0xff, 0xff,                                                 /*Incoming call duration*/
        0xff, 0xff, 0xff,                                                 /*link to phonebook entry*/

        'O', 'C', 'I', '2', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x03,
        0x81,
        0x44, 0x44, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0xff, 0xff, 0xff,                                                 /*Incoming call duration*/
        0xff, 0xff, 0xff,                                                 /*link to phonebook entry*/

        'O', 'C', 'I', '3', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,
        0xa8,
        0x07, 0x63, 0x26, 0x26, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,                                                              /*CCP */
        0x0c,                                                              /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0xff, 0xff, 0xff,                                                 /*Incoming call duration*/
        0xff, 0xff, 0xff,                                                 /*link to phonebook entry*/

        'O', 'C', 'I', '4', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,
        0xa8,
        0x07, 0x63, 0x26, 0x26, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0x03,                                                              /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0xff, 0xff, 0xff,                                                 /*Incoming call duration*/
        0xff, 0xff, 0xff,                                                 /*link to phonebook entry*/

        'O', 'C', 'I', '5', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,
        0xa8,
        0x07, 0x63, 0x26, 0x26, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,                                                              /*CCP */
        0x0c,                                                              /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0xff, 0xff, 0xff,                                                 /*Incoming call duration*/
        0xff, 0xff, 0xff,                                                 /*link to phonebook entry*/

        'O', 'C', 'I', '6', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,
        0xa8,
        0x07, 0x63, 0x26, 0x26, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,                                                              /*CCP */
        0x09,                                                              /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0xff, 0xff, 0xff,                                                 /*Incoming call duration*/
        0xff, 0xff, 0xff,                                                 /*link to phonebook entry*/

};

SimEfData defOciUsimEfData =
{
  SIM_EMU_EF_OCI,                  /* Id       */
  SIM_EFSTRUCT_C,                  /* Type     */
  SIM_DIR_ADF_USIM,                /* Dir      */
  SIM_EMU_NUM_USIM_OCI_RECS,       /* Num recs */
  SIM_EMU_SIZE_USIM_OCI_FILE,      /* Rec Len  */
  (0),                             /* curr Rec */
  (0),
  {
    USIM_ACCESS_NEVER,    /* delete file*/
    USIM_ACCESS_NEVER,    /* terminate*/
    USIM_ACCESS_ADM,      /* activate */
    USIM_ACCESS_ADM,      /* deactivate */
    USIM_ACCESS_NEVER,    /* write */
    USIM_ACCESS_PIN,     /* update*/
    USIM_ACCESS_PIN       /* read*/
  },
  SIM_UICC_ACTIVATED_STATE, /*file State*/
  FALSE,                    /*sfi supported  */
  0x00,                     /*sfi*/
  TRUE                      /*Under USIM*/
};

/*-----------------18/07/2003 13:04-----------------
 * EF ICT: incoming call timer
 * --------------------------------------------------*/
Int8    defIctUsimData[] = { 'I','C', 'T'};

SimEfData    defIctUsimEfData =
{
    SIM_EMU_EF_ICT,         /* Id               */
    SIM_EFSTRUCT_C,         /* Type             */
    SIM_DIR_ADF_USIM,       /* Dir              */
    1,                      /* Num Recs         */
    SIM_EMU_SIZE_USIM_ICT_FILE,  /* Recs length */
    (0),                    /* curr Rec         */
    (1),
    {
     USIM_ACCESS_NEVER,    /* delete file  */
     USIM_ACCESS_NEVER,    /* terminate    */
     USIM_ACCESS_ADM,      /* activate     */
     USIM_ACCESS_ADM,      /* deactivate   */
     USIM_ACCESS_PIN,      /* increase     */
     USIM_ACCESS_PIN,      /* update       */
     USIM_ACCESS_PIN       /* read         */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi        */
    TRUE                      /*Under USIM */
};

/*-----------------18/07/2003 13:04-----------------
 * EF OCT: outgoing call timer
 * --------------------------------------------------*/
Int8    defOctUsimData[] = { 'O','C', 'T'};

SimEfData    defOctUsimEfData =
{
    SIM_EMU_EF_OCT,         /* Id               */
    SIM_EFSTRUCT_C,         /* Type             */
    SIM_DIR_ADF_USIM,       /* Dir              */
    1,                      /* NumRecs          */
    SIM_EMU_SIZE_USIM_OCT_FILE,  /* Rec length  */
    (0),                    /* curr Rec         */
    (1),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate   */
     USIM_ACCESS_ADM,      /* activate    */
     USIM_ACCESS_ADM,      /* deactivate  */
     USIM_ACCESS_PIN,      /* increase    */
     USIM_ACCESS_PIN,      /* update      */
     USIM_ACCESS_PIN       /* read        */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State     */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi            */
    TRUE                      /*Under USIM     */
};

/*-----------------18/07/2003 13:04-----------------
 * EF MSISDN
 * --------------------------------------------------*/
Int8    defMsisdnUsimData[] = {
        'M', 'S', 'I', 'S', 'D', 'N', '1', 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,
        0xa8,
        0x07, 0x63, 0x26, 0x26, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,                                                          /*CCP */
        0xff,                                                          /*EXT5 */

        'M', 'A', 'C', 'B', 'E', 'A', 'T', 'H', 'w', 'i', 't', 'h',
        'C', 'C', 'P', '2', 0xff, 0xff, 0xff, 0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x08,
        0x98,
        0x01, 0x01, 0x20, 0x44, 0x88, 0x83, 0x36, 0xff, 0xff, 0xff,
        0xff,                                                          /*CCP */
        0xff,                                                          /*EXT5 */

        'E', 'X', 'T', '.', 'R', 'E', 'Q', 'U', 'I', 'R', 'E', 'D',
        '3', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x0b,
        0x99,
        0x01, 0x01, 0x20, 0x44, 0x88, 0x83, 0x36, 0x01, 0x01, 0x20,
        0xff,                                                          /*CCP */
        0xff                                                           /*EXT5 */

};

SimEfData    defMsisdnUsimEfData =
{

    SIM_EMU_EF_MSISDN,          /* Id           */
    SIM_EFSTRUCT_LF,        /* Type         */
    SIM_DIR_ADF_USIM,       /* Dir          */
    SIM_EMU_NUM_USIM_MSISDN_RECS,                     /* num recs     */
    SIM_EMU_SIZE_USIM_MSISDN_FILE,                      /* Rec len     */
    (0),                      /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};

/*-----------------18/07/2003 13:05-----------------
 * EF EXT3 (used for SDN)
 * --------------------------------------------------*/
 Int8 defExt3UsimData[] = {
/*1*/   0x01,  /*CPS type*/
        0x16, 0x21, 0x43, 0x65, 0x87, 0x09, 0x21, 0x43, 0x65, 0x87, 0x09,
        0x2,

/*2*/   0xff,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*3*/   0x02,  /*additional data*/
        0x0a, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33,
        0x04,

/*4*/   0x02, /*additional data*/
        0x04, 0x44, 0x44, 0x44, 0x44, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0x06,

/*5*/   0xff,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*6*/   0x01, /*CPS type*/
        0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0x00, 0x11,
        0x01,

/*7*/   0x01,
        0x14, 0xa0, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0x00, 0x11, 0x22,
        0x09,

/*8*/   0x01,
        0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0x00, 0x11,
        0xff,

/*9*/   0x01,
        0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0x00, 0x11, 0x22,
        0xff,

/*a*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*b*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*c*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff
};

SimEfData defExt3UsimEfData =
{
  SIM_EMU_EF_EXT3,            /* Id           */
  SIM_EFSTRUCT_LF,        /* Type         */
  SIM_DIR_ADF_USIM,       /* Dir          */
  SIM_EMU_NUM_USIM_EXT3_RECS, /* num Rec       */
  SIM_EMU_SIZE_USIM_EXT3_FILE,  /* Recs len     */
  (0),                    /* curr Rec     */
  (0),
  {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_ADM,      /* update */
     USIM_ACCESS_PIN       /* read */
   },
   SIM_UICC_ACTIVATED_STATE, /*file State */
   FALSE,                    /*sfi supported  */
   0x00,                     /*sfi */
   TRUE                      /*Under USIM */
};

/*-----------------05/04/02 09:55-------------------
 * EF EXT4: used for BDN
 * --------------------------------------------------*/

Int8 defExt4UsimData[] = {
/*1*/   0x01,  /*CPS type*/
        0x03, 0x21, 0x43, 0x65, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*2*/   0xff,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*3*/   0x02,  /*additional data*/
        0x0a, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33,
        0x04,

/*4*/   0x02, /*additional data*/
        0x04, 0x44, 0x44, 0x44, 0x44, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0x06,

/*5*/   0xff,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*6*/   0x01, /*CPS type*/
        0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0x00, 0x11,
        0x01,

/*7*/   0x01,
        0x14, 0xa0, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0x00, 0x11, 0x22,
        0x09,

/*8*/   0x01,
        0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0x00, 0x11,
        0xff,

/*9*/   0x01,
        0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0x00, 0x11, 0x22,
        0xff,

/*a*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*b*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*c*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff
};


SimEfData defExt4UsimEfData =
{
  SIM_EMU_EF_EXT4,            /* Id           */
  SIM_EFSTRUCT_LF,        /* Type         */
  SIM_DIR_ADF_USIM,       /* Dir          */
  SIM_EMU_NUM_USIM_EXT4_RECS, /* Num Rec      */
  SIM_EMU_SIZE_USIM_EXT4_FILE, /*  Rec  len   */
  (0),                    /* curr Rec     */
  (0),
  {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN2,      /* update */
     USIM_ACCESS_PIN       /* read */
   },
   SIM_UICC_ACTIVATED_STATE, /*file State */
   FALSE,                    /*sfi supported  */
   0x00,                     /*sfi */
   TRUE                      /*Under USIM */
};
 /*-----------------05/04/02 09:55-------------------
 * EF EXT5: used for MSISDN OCI ICI
 * --------------------------------------------------*/

Int8 defExt5UsimData[] = {
/*1*/   0x01,  /*CPS type*/
        0x16, 0x21, 0x43, 0x65, 0x87, 0x09, 0x21, 0x43, 0x65, 0x87, 0x09,
        0x2,

/*2*/   0xff,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*3*/   0x02,  /*additional data*/
        0x0a, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33,
        0x04,

/*4*/   0x02, /*additional data*/
        0x04, 0x44, 0x44, 0x44, 0x44, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0x06,

/*5*/   0xff,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*6*/   0x01, /*CPS type*/
        0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0x00, 0x11,
        0x01,

/*7*/   0x01,
        0x14, 0xa0, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0x00, 0x11, 0x22,
        0x09,

/*8*/   0x01,
        0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0x00, 0x11,
        0xff,

/*9*/   0x01,
        0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0x00, 0x11, 0x22,
        0xff,

/*a*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*b*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*c*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff
};


SimEfData defExt5UsimEfData =
{
  SIM_EMU_EF_EXT5,            /* Id           */
  SIM_EFSTRUCT_LF,        /* Type         */
  SIM_DIR_ADF_USIM,       /* Dir          */
  SIM_EMU_NUM_USIM_EXT5_RECS, /* Num Rec      */
  SIM_EMU_SIZE_USIM_EXT5_FILE, /*  Rec  len   */
  (0),                    /* curr Rec     */
  (0),
  {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
  },
  SIM_UICC_ACTIVATED_STATE, /*file State */
  FALSE,                    /*sfi supported  */
  0x00,                     /*sfi */
  TRUE                      /*Under USIM */
};



/*---------------------------------------------------
 * EXT6: used for MBDN
 * --------------------------------------------------*/
Int8 defExt6UsimData[] = {
/*1*/   0x02,
        'E', 'X', 'T', '6', 0x87, 0x09, 0x21, 0x43, 0x65, 0x87, 0x09,
        0x2,

/*2*/   0x02,
        0x0c, 0x21, 0x43, 0x65, 0x87, 0x09, 0x21, 0x43, 0x65, 0x87, 0x09,
        0x04,

/*3*/   0x02,
        0x12, 0x21, 0x43, 0x65, 0x87, 0x09, 0x21, 0x43, 0x65, 0x87, 0x09,
        0x05,

/*4*/   0x02,
        0x02, 0x11, 0x22, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0x06,

/*5*/   0x02,
        0x08, 0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0xff, 0xff,
        0x07,

/*6*/   0x01,
        0x14, 0xa0, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0x00, 0x11,
        0x08,

/*7*/   0x01,
        0x14, 0xa0, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0x00, 0x11, 0x22,
        0x09,

/*8*/   0x01,
        0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0x00, 0x11,
        0xff,

/*9*/   0x01,
        0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0x00, 0x11, 0x22,
        0xff,

/*a*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

};

SimEfData defExt6UsimEfData =
{
  SIM_EMU_EF_EXT6,            /* Id           */
  SIM_EFSTRUCT_LF,        /* Type         */
  SIM_DIR_ADF_USIM,       /* Dir          */
  SIM_EMU_NUM_USIM_EXT6_RECS, /* Num Rec      */
  SIM_EMU_SIZE_USIM_EXT6_FILE, /*  Rec  len   */
  (0),                    /* curr Rec     */
  (0),
  {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
   },
   SIM_UICC_ACTIVATED_STATE, /*file State */
   FALSE,                    /*sfi supported  */
   0x00,                     /*sfi */
   TRUE                      /*Under USIM */
};


/*----------------------------------------------------
 * EXT7: used for CFIS
 * --------------------------------------------------*/
Int8 defExt7UsimData[] = {
/*1*/   0x02,
        'E', 'X', 'T', '7', 0x87, 0x09, 0x21, 0x43, 0x65, 0x87, 0x09,
        0x02,

/*2*/   0x02,
        0x0c, 0x21, 0x43, 0x65, 0x87, 0x09, 0x21, 0x43, 0x65, 0x87, 0x09,
        0x04,

/*3*/   0x02,
        0x12, 0x21, 0x43, 0x65, 0x87, 0x09, 0x21, 0x43, 0x65, 0x87, 0x09,
        0x05,

/*4*/   0x02,
        0x02, 0x11, 0x22, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0x06,

/*5*/   0x02,
        0x08, 0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0xff, 0xff,
        0x07,

/*6*/   0x02,
        0x08, 0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0xff, 0xff,
        0xff,

/*7*/   0x02,
        0x08, 0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0xff, 0xff,
        0xff,

/*8*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*9*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*a*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*b*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*c*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff
};

SimEfData defExt7UsimEfData =
{
  SIM_EMU_EF_EXT7,            /* Id           */
  SIM_EFSTRUCT_LF,        /* Type         */
  SIM_DIR_ADF_USIM,       /* Dir          */
  SIM_EMU_NUM_USIM_EXT7_RECS, /* Num Rec      */
  SIM_EMU_SIZE_USIM_EXT7_FILE, /*  Rec  len   */
  (0),                    /* curr Rec     */
  (0),
  {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
   },
   SIM_UICC_ACTIVATED_STATE, /*file State */
   FALSE,                    /*sfi supported  */
   0x00,                     /*sfi */
   TRUE                      /*Under USIM */
};


/*-----------------05/04/02 10:39-------------------
 * CFIS: Call Forwarding Status Information
 * --------------------------------------------------*/
Int8 defCfisUsimData[] = {
/*1*/   0x01, /*MSP number*/
        0x07, /*CFU status*/
        0x06, /*length of BCD number */
        0xa8, /*TON and NPI */
        0x11, 0x11, 0x11, 0x11, 0x11, 0xff, 0xff, 0xff, 0xff, 0xff, /*dialling number */
        0x03, /*CCP2*/
        0x01, /*EXT7 record*/


/*2*/   0x02, /*MSP number*/
        0x00, /*CFU status*/
        0x06, /*length of BCD number */
        0xa8, /*TON and NPI */
        0x22, 0x22, 0x22, 0x22, 0x22, 0xff, 0xff, 0xff, 0xff, 0xff, /*dialling number */
        0x02, /*CCP2*/
        0x02, /*EXT7 record*/


/*2*/   0x03, /*MSP number*/
        0x02, /*CFU status*/
        0x06, /*length of BCD number */
        0xa8, /*TON and NPI */
        0x33, 0x33, 0x33, 0x33, 0x33, 0xff, 0xff, 0xff, 0xff, 0xff, /*dialling number */
        0xff, /*CCP2*/
        0xff, /*EXT7 record*/



/*2*/   0xff, /*MSP number*/
        0xff, /*CFU status*/
        0xff, /*length of BCD number */
        0xff, /*TON and NPI */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, /*dialling number */
        0xff, /*CCP2*/
        0xff  /*EXT7 record*/


};

SimEfData defCfisUsimEfData =
{
  SIM_EMU_EF_CFIS,            /* Id           */
  SIM_EFSTRUCT_LF,        /* Type         */
  SIM_DIR_ADF_USIM,       /* Dir          */
  SIM_EMU_NUM_USIM_CFIS_RECS, /* Num Rec      */
  SIM_EMU_SIZE_USIM_CFIS_FILE, /*  Rec  len   */
  (0),                    /* curr Rec     */
  (0),
  {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
   },
   SIM_UICC_ACTIVATED_STATE, /*file State */
   FALSE,                    /*sfi supported  */
   0x00,                     /*sfi */
   TRUE                      /*Under USIM */
};



/*-----------------18/07/2003 13:09-----------------
 * EF MBDN
 * --------------------------------------------------*/

Int8 defMbdnUsimData[] = {
        'M', 'B', 'D', 'N', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,                                                             /*length of BCD number*/
        0xa8,                                                             /*TON and NPI*/
        0x07, 0x63, 0x26, 0x26, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP2 */
        0x01,                                                             /*Ext6 */


        'T', 'W', 'O', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,
        0xa8,
        0x07, 0x63, 0x26, 0x26, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0x0b,


        'T', 'H', 'R', 'E', 'E', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,
        0xa8,
        0x07, 0x63, 0x26, 0x26, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0x0c,


};

SimEfData defMbdnUsimEfData =
{
  SIM_EMU_EF_MBDN,                  /* Id       */
  SIM_EFSTRUCT_LF,             /* Type     */
  SIM_DIR_ADF_USIM,          /* Dir      */
  SIM_EMU_NUM_USIM_MBDN_RECS,   /* Num recs */
  SIM_EMU_SIZE_USIM_MBDN_FILE,  /* Rec Len  */
  (0),                         /* curr Rec */
  (0),
  {
    USIM_ACCESS_NEVER,    /* delete file*/
    USIM_ACCESS_NEVER,    /* terminate*/
    USIM_ACCESS_ADM,      /* activate */
    USIM_ACCESS_ADM,      /* deactivate */
    USIM_ACCESS_NEVER,    /* write */
    USIM_ACCESS_PIN,     /* update*/
    USIM_ACCESS_PIN       /* read*/
  },
  SIM_UICC_ACTIVATED_STATE, /*file State*/
  FALSE,                    /*sfi supported  */
  0x00,                     /*sfi*/
  TRUE                      /*Under USIM*/
};

/*-----------------28/09/2006      -----------------
 * EF MWIS
 * --------------------------------------------------*/
 Int8    defMWISUsimData[] = { 0x0f, 0x01, 0x02, 0x03, 0x04,
                               0x00, 0x00, 0x00, 0x00, 0x00  };
SimEfData    defMWISUsimEfData =
{
   SIM_EMU_EF_MWIS,            /* Id                  */
   SIM_EFSTRUCT_LF,            /* Type                */
   SIM_DIR_ADF_USIM,           /* Dir                 */
   SIM_EMU_NUM_USIM_MWIS_RECS, /* Num Recs            */
   SIM_EMU_SIZE_USIM_MWIS_FILE,/* Rec length          */
   (0),                        /* curr Rec            */
   (0),
   {
    USIM_ACCESS_NEVER,         /* delete file         */
    USIM_ACCESS_NEVER,         /* terminate           */
    USIM_ACCESS_ADM,           /* activate            */
    USIM_ACCESS_ADM,           /* deactivate          */
    USIM_ACCESS_NEVER,         /* write               */
    USIM_ACCESS_PIN,           /* update              */
    USIM_ACCESS_PIN            /* read                */
  },
  SIM_UICC_ACTIVATED_STATE,    /* file State          */
  FALSE,                       /* sfi supported       */
  0x00,                        /* sfi                 */
  TRUE                         /* Under USIM          */
};


/*-----------------18/07/2003 13:09-----------------
 * EF GID1
 * --------------------------------------------------*/
Int8    defGid1UsimData[] = {
           0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09,
           0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19  };

SimEfData    defGid1UsimEfData =
{

    SIM_EMU_EF_GID1,            /* Id           */
    SIM_EFSTRUCT_T,         /* Type         */
    SIM_DIR_ADF_USIM,       /* Dir          */
    1,                      /* Rec Len      */
    SIM_EMU_SIZE_GID1_FILE, /* num Recs     */
    (0),                    /* curr Rec     */
    (0),
 {
    USIM_ACCESS_NEVER,    /* delete file*/
    USIM_ACCESS_NEVER,    /* terminate*/
    USIM_ACCESS_ADM,      /* activate */
    USIM_ACCESS_ADM,      /* deactivate */
    USIM_ACCESS_NEVER,    /* write */
    USIM_ACCESS_PIN,     /* update*/
    USIM_ACCESS_PIN       /* read*/
  },
  SIM_UICC_ACTIVATED_STATE, /*file State*/
  FALSE,                    /*sfi supported  */
  0x00,                     /*sfi*/
  TRUE                      /*Under USIM*/
};


/*-----------------18/07/2003 13:09-----------------
 * EF GID2
 * --------------------------------------------------*/
Int8    defGid2UsimData[] = {
           0x19, 0x18, 0x17, 0x16, 0x15, 0x14, 0x13, 0x12, 0x11, 0x10,
           0x09, 0x08, 0x07, 0x06, 0x05, 0x04, 0x03, 0x02, 0x01, 0x00  };

SimEfData    defGid2UsimEfData =
{

    SIM_EMU_EF_GID2,            /* Id           */
    SIM_EFSTRUCT_T,         /* Type         */
    SIM_DIR_ADF_USIM,       /* Dir          */
    1,                      /* Rec Len      */
    SIM_EMU_SIZE_GID2_FILE, /* num Recs     */
    (0),                    /* curr Rec     */
    (0),
  {
    USIM_ACCESS_NEVER,    /* delete file*/
    USIM_ACCESS_NEVER,    /* terminate*/
    USIM_ACCESS_ADM,      /* activate */
    USIM_ACCESS_ADM,      /* deactivate */
    USIM_ACCESS_NEVER,    /* write */
    USIM_ACCESS_PIN,     /* update*/
    USIM_ACCESS_PIN       /* read*/
  },
  SIM_UICC_ACTIVATED_STATE, /*file State*/
  FALSE,                    /*sfi supported  */
  0x00,                     /*sfi*/
  TRUE                      /*Under USIM*/
};



/*-----------------05/04/02 16:13-------------------
 * EF EXT2:  used for FDN
 * --------------------------------------------------*/
Int8    defExt2UsimData[] = {

/*1*/   0x01,  /*CPS type*/
        0x16, 0x21, 0x43, 0x65, 0x87, 0x09, 0x21, 0x43, 0x65, 0x87, 0x09,
        0x2,

/*2*/   0xff,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*3*/   0x02,  /*additional data*/
        0x0a, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33,
        0x04,

/*4*/   0x02, /*additional data*/
        0x04, 0x44, 0x44, 0x44, 0x44, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0x06,

/*5*/   0xff,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*6*/   0x01, /*CPS type*/
        0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0x00, 0x11,
        0x01,

/*7*/   0xff,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*8*/   0xff,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*9*/   0xff,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*a*/   0xff,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*b*/   0xff,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

};

SimEfData    defExt2UsimEfData =
{

    SIM_EMU_EF_EXT2,                     /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    SIM_EMU_NUM_USIM_EXT2_RECS, /* Num Rec      */
    SIM_EMU_SIZE_USIM_EXT2_FILE,/*  Rec  len   */
  (0),                          /* curr Rec     */
  (0),
  {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN2,      /* update */
     USIM_ACCESS_PIN       /* read */
   },
   SIM_UICC_ACTIVATED_STATE, /*file State */
   FALSE,                     /*sfi supported  */
   0,                     /*sfi */
   TRUE                      /*Under USIM */
};

/*-----------------18/07/2003 13:10-----------------
 * EF SDN
 * --------------------------------------------------*/
Int8 defSdnUsimData[] = {
        'S', 'D', 'N', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x03,
        0x81,
        0x44, 0x44, 0x26, 0x26, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0x01,

        'S', 'E', 'C', 'O', 'N', 'D', 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,
        0xa8,
        0x07, 0x63, 0x26, 0x26, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0x02,

        'T', 'H', 'I', 'R', 'D', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,
        0xa8,
        0x07, 0x63, 0x26, 0x26, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0x03,

        'F', 'O', 'U', 'R', 'T', 'H', 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,
        0xa8,
        0x07, 0x63, 0x26, 0x26, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0x04,

        'F', 'I', 'F', 'T', 'H', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,
        0xa8,
        0x07, 0x63, 0x26, 0x26, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0x05,

        'S', 'I', 'X', 'T', 'H', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,
        0xa8,
        0x07, 0x63, 0x26, 0x26, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0x06,

        'S', 'E', 'V', 'E', 'N', 'T', 'H', 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,
        0xa8,
        0x07, 0x63, 0x26, 0x26, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0x07,

        'E', 'I', 'G', 'H', 'T', 'H', 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x03,
        0x81,
        0x33, 0x33, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0xff,

 };

SimEfData defSdnUsimEfData =
{
  SIM_EMU_EF_SDN,             /* Id           */
  SIM_EFSTRUCT_LF,        /* Type         */
  SIM_DIR_ADF_USIM,     /* Dir          */
  SIM_EMU_NUM_USIM_SDN_RECS, /* Num Rec      */
  SIM_EMU_SIZE_USIM_SDN_FILE,/*  Rec  len   */
  (0),                          /* curr Rec     */
  (0),
  {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_ADM,      /* update */
     USIM_ACCESS_PIN       /* read */
   },
   SIM_UICC_ACTIVATED_STATE, /*file State */
   FALSE,                    /*sfi supported  */
   0,                        /*sfi */
   TRUE                      /*Under USIM */

 };
#endif 
/*CQ00135656, Cgliu, 2022-02-25,End  */

/*-----------------18/07/2003 13:11-----------------
 * EF KC
 * This file is located under DF GSM ACCESS
 * See TS 131 102 section *******
 * --------------------------------------------------*/
Int8    defKcUsimData[] = { 1, 2, 3, 4, 5, 6, 7, 8,  /*ciphering key Kc*/
                            1 };                     /*ciphering key sequence number n */


SimEfData    defKcUsimEfData =
{
    SIM_EMU_EF_KC,              /* Id           */
    SIM_EFSTRUCT_T,          /* Type         */
    SIM_DIR_DF_GSM_ACCESS,   /* Dir          */
    1,                       /* Rec Len      */
    SIM_EMU_SIZE_USIM_KC_FILE, /* num Recs     */
    (0),                     /* curr Rec     */
    (0),
   {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
   },
   SIM_UICC_ACTIVATED_STATE, /*file State */
   TRUE,                     /*sfi supported  */
   0x01,                     /*sfi */
   TRUE                      /*Under USIM */
};


/*-----------------18/07/2003 13:11-----------------
 * EF KC GPRS
 * See TS 131 102 section *******
 * --------------------------------------------------*/

Int8    defKcGprsUsimData[] = { 1, 2, 3, 4, 5, 6, 7, 8, /*ciphering key KcGPRS*/
                                1 };                    /*ciphering key sequence number n for GPRS*/


SimEfData    defKcGprsUsimEfData =
{
    SIM_EMU_EF_KC_GPRS,         /* Id           */
    SIM_EFSTRUCT_T,          /* Type         */
    SIM_DIR_DF_GSM_ACCESS,   /* Dir          */
    1,                       /* Rec Len      */
    SIM_EMU_SIZE_USIM_KC_FILE, /* num Recs     */
    (0),                     /* curr Rec     */
    (0),
   {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
   },
   SIM_UICC_ACTIVATED_STATE, /*file State */
   TRUE,                     /*sfi supported  */
   0x02,                     /*sfi */
   TRUE                      /*Under USIM */
};

/*-----------------18/07/2003 13:15-----------------
 * EF KEYS
 * This file is located under ADF USIM directory
 * --------------------------------------------------*/

Int8    defKeysUsimData[] = { 0xff,    /* ( key set identifier refered to as "KSI") value 0xff indicate no key present */
                              'C','I','P','H','E','R','K','E','Y',0xff,0xff,0xff,0xff,0xff,0xff,0xff, /*Ciphering Key CK*/
                              'I','N','T','E','G','R','I','T','Y','K','E','Y',0xff,0xff,0xff,0xff  }; /*Integrity Key IK*/


SimEfData    defKeysUsimEfData =
{
    SIM_EMU_EF_KEYS,              /* Id           */
    SIM_EFSTRUCT_T,            /* Type         */
    SIM_DIR_ADF_USIM,          /* Dir          */
    1,                         /* Rec Len      */
    SIM_EMU_SIZE_USIM_KEYS_FILE,/* num Recs     */
    (0),                       /* curr Rec     */
    (0),
   {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
   },
   SIM_UICC_ACTIVATED_STATE, /*file State */
   TRUE,                     /*sfi supported  */
   0x08,                     /*sfi */
   TRUE                      /*Under USIM */
};

Int8    defKeysPsUsimData[] = { 1,   /* ( key set identifier refered to as "KSI") value 0xff indicate no key present */
                              'P','S','C','I','P','H','E','R','K','E','Y',0xff,0xff,0xff,0xff,0xff, /*Ciphering Key CK*/
                              'P','S','I','N','T','E','G','R','I','T','Y','K','E','Y',0xff,0xff }; /*Integrity Key IK*/


SimEfData    defKeysPsUsimEfData =
{
    SIM_EMU_EF_KEYS_PS,           /* Id           */
    SIM_EFSTRUCT_T,            /* Type         */
    SIM_DIR_ADF_USIM,          /* Dir          */
    1,                         /* Rec Len      */
    SIM_EMU_SIZE_USIM_KEYS_FILE,/* num Recs     */
    (0),                       /* curr Rec     */
    (0),
   {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_ALWAYS,      /* update */
     USIM_ACCESS_ALWAYS       /* read */
   },
   SIM_UICC_ACTIVATED_STATE, /*file State */
   TRUE,                     /*sfi supported  */
   0x09,                     /*sfi */
   TRUE                      /*Under USIM */
};


/*-----------------18/07/2003 13:17-----------------
 * EF PS LOCI (packet switched location information)
 * See TS 131 102, section 4.2.23
 * --------------------------------------------------*/
Int8    defPsLociUsimData[] = { 0xff, 0xff, 0xff, 0xff,
                                0x21, 0x43, 0x65,
                                0x32, 0xf4, 0x03, 0x12, 0x34, 0x10,
                                0x00 };

SimEfData    defPsLociUsimEfData =
{
    SIM_EMU_EF_PS_LOCI,                 /* Id           */
    SIM_EFSTRUCT_T,                  /* Type         */
    SIM_DIR_ADF_USIM,                /* Dir          */
    1,                               /* Rec Len      */
    SIM_EMU_SIZE_USIM_LOCI_PS_FILE,  /* num Recs     */
    (0),                             /* curr Rec     */
    (0),
   {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
   },
   SIM_UICC_ACTIVATED_STATE, /*file State */
   TRUE,                     /*sfi supported  */
   0x0c,                     /*sfi */
   TRUE                      /*Under USIM */
};

/*CQ00135656, Cgliu, 2022-02-25,Begin*/
#if defined (ON_PC)
/*-----------------18/07/2003 13:17-----------------
 * EF  HIDDEN KEY
 * See TS 131 102, section 4.2.42
 * --------------------------------------------------*/

Int8    defHiddenKeyUsimData[] = { 0x21, 0x43, 0x65, 0x87 };  /*hidden key value*/

SimEfData    defHiddenKeyUsimEfData =
{
    SIM_EMU_EF_HIDDEN_KEY,                 /* Id           */
    SIM_EFSTRUCT_T,                  /* Type         */
    SIM_DIR_ADF_USIM,                /* Dir          */
    1,                               /* Rec Len      */
    SIM_EMU_SIZE_USIM_HIDDEN_KEY_FILE,  /* num Recs     */
    (0),                             /* curr Rec     */
    (0),
   {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
   },
   SIM_UICC_ACTIVATED_STATE, /*file State */
   FALSE,                     /*sfi supported  */
   0x00,                     /*sfi */
   TRUE                      /*Under USIM */
};
#endif 
/*CQ00135656, Cgliu, 2022-02-25,End  */


/*-----------------18/07/2003 13:17-----------------
 * EF PS START HFN
 * See TS 131 102, section 4.2.51
 * --------------------------------------------------*/

Int8    defStartHfnUsimData[] = { 0x03, 0x33, 0x33,   /*START CS*/
                                  0x07, 0x77, 0x77 }; /*START PS*/

SimEfData    defStartHfnUsimEfData =
{
    SIM_EMU_EF_START_HFN,                 /* Id           */
    SIM_EFSTRUCT_T,                  /* Type         */
    SIM_DIR_ADF_USIM,                /* Dir          */
    1,                               /* Rec Len      */
    SIM_EMU_SIZE_USIM_START_HFN_FILE,  /* num Recs     */
    (0),                             /* curr Rec     */
    (0),
   {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
   },
   SIM_UICC_ACTIVATED_STATE, /*file State */
   TRUE,                     /*sfi supported  */
   0x0f,                     /*sfi */
   TRUE                      /*Under USIM */
};

/*CQ00135656, Cgliu, 2022-02-25,Begin*/
#if defined (ON_PC)
/*-----------------18/07/2003 13:17-----------------
 * EF PNN. See 51.011 for coding details, and 24.008 section 10.5.3.5a
 * First record decoded as an example:
 *
 *   0x43,   =>full network name IEI
 *   0x06,   =>length of following data
 *   0x80    => coding scheme (bit 5 to 7). bit 4 = whether to add Country initials or not
 *   0xec, 0xb7, 0xfb, 0x1c, 0x03,  => full name     (packed)
 *   0x45,   =>short network name IEI
 *   0x06,   => length of following data
 *   0x80,   => coding scheme (bit 5 to 7). bit 4 = whether to add Country initials or not
 *   0x73, 0xf4, 0x5b, 0x4e, 0x07,  =>short name   (packed)
 *   0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,   =>padding
 * --------------------------------------------------*/

Int8    defPnnUsimData[] = {
                             /* First record   */
                             0x43, 0x06, 0x80, 0xec, 0xb7, 0xfb, 0x1c, 0x03, 0x45, 0x06, 0x80, 0x73, 0xf4, 0x5b, 0x4e, 0x07,
                             0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                             /* record  2 */
                             0x43, 0x06, 0x80, 0xec, 0xb7, 0xfb, 0x2c, 0x03, 0x45, 0x06, 0x80, 0x73, 0xf4, 0x5b, 0x4e, 0x07,
                             0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                             /* record  3 */
                             0x43, 0x06, 0x80, 0xec, 0xb7, 0xfb, 0x3c, 0x03, 0x45, 0x06, 0x80, 0x73, 0xf4, 0x5b, 0x4e, 0x07,
                             0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                             /* record  4 */
                             0x43, 0x06, 0x80, 0xec, 0xb7, 0xfb, 0x4c, 0x03, 0x45, 0x06, 0x80, 0x73, 0xf4, 0x5b, 0x4e, 0x07,
                             0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                             /* record  5 */
                             0x43, 0x06, 0x80, 0xec, 0xb7, 0xfb, 0x5c, 0x03, 0x45, 0x06, 0x80, 0x73, 0xf4, 0x5b, 0x4e, 0x07,
                             0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                             /* record  6 */
                             0x43, 0x06, 0x80, 0xec, 0xb7, 0xfb, 0x6c, 0x03, 0x45, 0x06, 0x80, 0x73, 0xf4, 0x5b, 0x4e, 0x07,
                             0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                             /* record  7 */
                             0x43, 0x06, 0x80, 0xec, 0xb7, 0xfb, 0x7c, 0x03, 0x45, 0x06, 0x80, 0x73, 0xf4, 0x5b, 0x4e, 0x07,
                             0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                             /* record  8 */
                             0x43, 0x06, 0x80, 0xec, 0xb7, 0xfb, 0x8c, 0x03, 0x45, 0x06, 0x80, 0x73, 0xf4, 0x5b, 0x4e, 0x07,
                             0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                             /* record 9  */
                              0x43, 0x06, 0x80, 0xec, 0xb7, 0xfb, 0x9c, 0x03, 0x45, 0x06, 0x80, 0x73, 0xf4, 0x5b, 0x4e, 0x07,
                             0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                             /* record 10  */
                             0x43, 0x07, 0x80, 0xec, 0xb7, 0xfb, 0x1c, 0x83, 0x01, 0x45, 0x06, 0x80, 0x73, 0xf4, 0x5b, 0x4e,
                             0x07, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                             /* record 11  */
                             0x43, 0x07, 0x80, 0xec, 0xb7, 0xfb, 0x1c, 0x8b, 0x01, 0x45, 0x06, 0x80, 0x73, 0xf4, 0x5b, 0x4e,
                             0x07, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                             /* record 12  */
                             0x43, 0x07, 0x88, 0xec, 0xb7, 0xfb, 0x1c, 0x93, 0x01, 0x45, 0x06, 0x88, 0x73, 0xf4, 0x5b, 0x4e,
                             0x07, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff

                             };


SimEfData    defPnnUsimEfData =
{
    SIM_EMU_EF_PNN,                  /* Id           */
    SIM_EFSTRUCT_LF,                 /* Type         */
    SIM_DIR_ADF_USIM,                /* Dir          */
    SIM_EMU_NUM_USIM_PNN_RECS,       /* Rec Len      */
    SIM_EMU_SIZE_USIM_PNN_FILE,      /* num Recs     */
    (0),                             /* curr Rec     */
    (0),
   {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_NEVER,      /* update */
     USIM_ACCESS_PIN       /* read */
   },
   SIM_UICC_ACTIVATED_STATE, /*file State */
   TRUE,                     /*sfi supported  */
   0x1a,                     /*sfi */
   TRUE                      /*Under USIM */
};
/*EF OPL: this is part of EONS. see 51.011 for coding*/

Int8    defOplUsimData[] = {
                             /* First record   */
                             0x42, 0xf3, 0x53, 0x00, 0x00, 0xff, 0xfe, 0x01,
                             /* record  2 */
                             0x42, 0xf3, 0x43, 0x00, 0x01, 0x00, 0x10, 0x02,
                             /* record  3 */
                             0x42, 0xf3, 0x63, 0x00, 0x00, 0xff, 0xfe, 0x03,
                             /* record  4 */
                             0x42, 0xf3, 0x25, 0x00, 0x00, 0xff, 0xfe, 0x04,
                             /* record  5 */
                             0x42, 0xf3, 0x93, 0x00, 0x00, 0xff, 0xfe, 0x07,
                             /* record  6 */
                             0x42, 0xf3, 0x23, 0x00, 0x00, 0xff, 0xfe, 0x08,
                             /* record  7 */
                             0x42, 0xf3, 0x13, 0x00, 0x02, 0x00, 0x09, 0x08,
                             /* record  8 */
                             0x42, 0xf3, 0x03, 0x00, 0x00, 0xff, 0xfe, 0x03,
                             /* record 9  */
                             0x42, 0xf3, 0x21, 0x00, 0x00, 0xff, 0xfe, 0x05,
                             /* record 10  */
                             0x42, 0xf3, 0x22, 0x00, 0x00, 0xff, 0xfe, 0x06,
                             /* record 11  */
                             0x42, 0xf3, 0x24, 0x00, 0x00, 0xff, 0xfe, 0x09,
                             /* record 12  */
                             0x42, 0xf3, 0x26, 0x00, 0x00, 0xff, 0xfe, 0x0a,
                             /* record 13  */
                             0x00, 0xfd, 0x10, 0x00, 0x00, 0xff, 0xfe, 0x0c,
                             /* record 14  */
                             0x42, 0xf3, 0x27, 0x00, 0x00, 0xff, 0xfe, 0x0c,
                             /* record 15  */
                             0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
                             };
SimEfData    defOplUsimEfData =
{
    SIM_EMU_EF_OPL,                  /* Id           */
    SIM_EFSTRUCT_LF,                 /* Type         */
    SIM_DIR_ADF_USIM,                /* Dir          */
    SIM_EMU_NUM_USIM_OPL_RECS,       /* Rec Len      */
    SIM_EMU_SIZE_USIM_OPL_FILE,      /* num Recs     */
    (0),                             /* curr Rec     */
    (0),
   {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_NEVER,      /* update */
     USIM_ACCESS_PIN       /* read */
   },
   SIM_UICC_ACTIVATED_STATE, /*file State */
   TRUE,                     /*sfi supported  */
   0x1a,                     /*sfi */
   TRUE                      /*Under USIM */
};
#endif 
/*CQ00135656, Cgliu, 2022-02-25,End  */


/*-----------------18/07/2003 13:17-----------------
 * EF THRESHOLD
 * See TS 131 102, section 4.2.52
 * --------------------------------------------------*/

Int8    defThresholdUsimData[] = { 0xff, 0xff, 0xff };  /*max value of START PS and START CS*/


SimEfData    defThresholdUsimEfData =
{
    SIM_EMU_EF_THRESHOLD,                 /* Id           */
    SIM_EFSTRUCT_T,                  /* Type         */
    SIM_DIR_ADF_USIM,                /* Dir          */
    1,                               /* Rec Len      */
    SIM_EMU_SIZE_USIM_THRESHOLD_FILE,  /* num Recs     */
    (0),                             /* curr Rec     */
    (0),
   {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
   },
   SIM_UICC_ACTIVATED_STATE, /*file State */
   TRUE,                     /*sfi supported  */
   0x11,                     /*sfi */
   TRUE                      /*Under USIM */
};

/*-----------------18/07/2003 13:20-----------------
 * EF NETPAR:  Network parameters file
 * --------------------------------------------------*/

Int8    defNetParUsimData[] = { /*GSM object */
                                0xa0, /*GSM cell info tag*/
                                0x1a, /*length*/
                                0x80, /*current camped cell freq info TAG*/
                                0x02, /*length*/
                                0x29,
                                0x38,
                                0x81, /*Neighbour cell freq info*/
                                0x14,  /*length*/
                                0x29, /*neighbour cell freq 1*/
                                0x36,
                                0x29, /*neighbour cell freq 2*/
                                0x37,
                                0x29, /*neighbour cell freq 3*/
                                0x39,
                                0x29, /*neighbour cell freq 4*/
                                0x3a,
                                0x29, /*neighbour cell freq 5*/
                                0x3b,
                                0x29, /*neighbour cell freq 6*/
                                0x3c,
                                0x29, /*neighbour cell freq 7*/
                                0x3d,
                                0x29, /*neighbour cell freq 8*/
                                0x3e,
                                0x29, /*neighbour cell freq 9*/
                                0x3f,
                                0x29, /*neighbour cell freq 10*/
                                0x40,
                                
/*Zhiwei add for TDD IT test start*/
                                0xa2,  /*TDD cell info tag*/
                                0x20,  
                                0x80,  /*TDD intra freq info tag */
                                0x12,  /* m=8, length=18 */    
                                0x27, /* modification for LTE PC simulation by zhjzhao from 10079(0x27,0x5f) to 37525(0x9295), begin*/ /*This modified is returned 120717.*/
                                0x5f,/* modification for LTE PC simulation by zhjzhao from 10079 to 37525, end*/ 
                                0x00,       
                                0x00,
                                0x00,       
                                0x04,    
                                0x00,       
                                0x08,
                                0x00,       
                                0x7b,
                                0x00,       
                                0x01,
                                0x00,       
                                0x02,    
                                0x00,       
                                0x03,
                                0x00,       
                                0x04,
                       /*TDD inter frequency info (object 1)*/
                                0x81,       /*TDD inter freq info tag*/
                                0x0a,       /* n1=4 */
                                0x27,   /* modification for LTE PC simulation by zhjzhao from 10054(0x27,0x46) to 37250(0x9182), begin*/ /*This modified is returned 120717.*/
                                0x46,   /* modification for LTE PC simulation by zhjzhao from 10054 to 37250, end*/
                                0x00,       
                                0x12,
                                0x00,       
                                0x72,
                                0x00,       
                                0x73,
                                0x00,       
                                0x74,
                        /*TDD inter frequency info (object 2)*/
                                0x81,       /*TDD intra freq info tag*/
                                0x0a,       /* n2=4 */
                                0x27,      /* modification for LTE PC simulation by zhjzhao from10112(0x27,0x80) updated by zhanghao  to 36975(0x906f), begin*/ /*This modified is returned 120717.*/
                                0x80,      /* modification for LTE PC simulation by zhjzhao from10112 updated by zhanghao  to 36975, end*/
                                0x00,       
                                0x00,
                                0x00,       
                                0x01,
                                0x00,       
                                0x02,
                                0x00,       
                                0x03

#if 0
                                /* TDD cell information   */
                                0xa1,  /*TDD cell info tag*/
                                0x2c,  /*length*/
                                0x80, /*TDD intra freq info tag*/
                                0x12,        /*length*/
                                0x29,     /*intra frequency carrier freq*/
                                0x42,
                                0x00,       /*scrambling code 1: 100*/
                                0x64,
                                0x00,       /*scrambling code 2: 150*/
                                0x96,
                                0x00,       /*scrambling code 3: 200*/
                                0xc8,
                                0x00,       /*scrambling code 4: 250*/
                                0xfa,
                                0x01,       /*scrambling code 5: 450*/
                                0xc2,
                                0x01,       /*scrambling code 6: 450*/
                                0xf4,
                                0x02,       /*scrambling code 7: 450*/
                                0x26,
                                0x02,       /*scrambling code 8: 450*/
                                0x58,
                                 /*TDD inter frequency info (object 1)*/
                                0x81,       /*TDD intra freq info tag*/
                                0x0a,       /*length*/
                                0x2a,       /*inter frequency carrier freq*/
                                0x55,
                                0x00,       /*scrambling code 1: 250*/
                                0xfa,
                                0x01,       /*scrambling code 2: 300*/
                                0x2c,
                                0x01,       /*scrambling code 3: 350*/
                                0x5e,
                                0x01,       /*scrambling code 4: 400*/
                                0x90,
                                /*TDD inter frequency info (object 2)*/
                                0x81,       /*TDD intra freq info tag*/
                                0x0a,       /*length*/
                                0x2a,       /*inter frequency carrier freq*/
                                0x56,
                                0x02,       /*scrambling code 1: 800*/
                                0xbc,
                                0x02,       /*scrambling code 2: 750*/
                                0xee,
                                0x03,       /*scrambling code 3: 800*/
                                0x20,
                                0x01,       /*scrambling code 4: 400*/
                                0x90

#endif
/*Zhiwei add for TDD IT test start end */

};




SimEfData    defNetParUsimEfData =
{
    SIM_EMU_EF_NETPAR,                 /* Id           */
    SIM_EFSTRUCT_T,                  /* Type         */
    SIM_DIR_ADF_USIM,                /* Dir          */
    1,                               /* Rec Len      */
    SIM_EMU_SIZE_USIM_NETPAR_FILE,  /* num Recs     */
    (0),                             /* curr Rec     */
    (0),
   {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
   },
   SIM_UICC_ACTIVATED_STATE, /*file State */
   FALSE,                    /*sfi supported  */
   0x0,                      /*sfi */
   TRUE                      /*Under USIM */
};
/*CQ00135656, Cgliu, 2022-02-25,Begin*/
#if defined (ON_PC)
/* -----------------------------------------------
 *  EF SPDI: Service Provider Display Information
 *
 * ------------------------------------------------*/
Int8    defSpdiUsimData[] = { 0xa3, /*service provider display info tag*/
                              0x11, /*TLV object length*/
                              0x80, /*service provider list info tag*/
                              0x0f, /*length*/
                              0x32, 0xf4, 0x10, /*first PLMN entry: 234 01*/
                              0x32, 0xf4, 0x20,
                              0x32, 0xf4, 0x30,
                              0x32, 0xf4, 0x40,
                              0xff, 0xff, 0xff
                            };

SimEfData    defSpdiUsimEfData =
{
    SIM_EMU_EF_SPDI,                 /* Id           */
    SIM_EFSTRUCT_T,                  /* Type         */
    SIM_DIR_ADF_USIM,                /* Dir          */
    1,                               /* Rec Len      */
    SIM_EMU_SIZE_USIM_SPDI_FILE,     /* num Recs     */
    (0),                             /* curr Rec     */
    (0),
   {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_ADM,      /* update */
     USIM_ACCESS_PIN       /* read */
   },
   SIM_UICC_ACTIVATED_STATE, /*file State */
   FALSE,                     /*sfi supported  */
   0x00,                     /*sfi */
   TRUE                      /*Under USIM */
};

/*Access point names Control List, see 31.102, section 4.2.48 */
#if defined (SIM_EMU_BIG_ACL_FILE)

Int8    defAclUsimData[] = {
  0x0b,                                                       /*number of APNs*/
  /*First APN*/
  0xdd,                                                       /*first APN tag*/
  0x0a,                                                       /*length*/
  0x04, 0x74, 0x65, 0x73, 0x74, 0x04, 0x74, 0x65, 0x73, 0x74, /*value: "test.test" */
  /*second APN*/
  0xdd,                                                       /*second APN tag*/
  0x0a,                                                       /*length*/
  0x04, 0x33, 0x67, 0x70, 0x70, 0x04, 0x074, 0x65, 0x73, 0x74,/*value: "3gpp.test"*/
  /*Third APN*/
  0xdd,                                                       /*third APN tag*/
  0x0a,                                                       /*length*/
  0x04, 0x32, 0x67, 0x70, 0x70, 0x04, 0x74, 0x65, 0x73, 0x74, /*value: "2gpp.test"*/
  /*Fourth APN*/
  0xdd,                                                       /*fourth APN tag*/
  0x0a,                                                       /*length*/
  0x04, 0x41, 0x50, 0x4e, 0x34, 0x04, 0x74, 0x65, 0x73, 0x74, /*value: "APN4.test"*/
  /*Fifth APN*/
  0xdd,                                                       /*fifth APN tag*/
  0x0a,                                                       /*length*/
  0x04, 0x41, 0x50, 0x4e, 0x35, 0x04, 0x74, 0x65, 0x73, 0x74, /*value: "APN5.test"*/
  /*Sixth APN*/
  0xdd,                                                       /*sixth APN tag*/
  0x0a,                                                       /*length*/
  0x04, 0x41, 0x50, 0x4e, 0x36, 0x04, 0x74, 0x65, 0x73, 0x74, /*value: "APN6.test"*/
  /*Seventh APN*/
  0xdd,                                                       /*seventh APN tag*/
  0x0a,                                                       /*length*/
  0x04, 0x41, 0x50, 0x4e, 0x37, 0x04, 0x74, 0x65, 0x73, 0x74, /*value: "APN7.test"*/
  /*Eigth APN*/
  0xdd,                                                       /*eigth APN tag*/
  0x0a,                                                       /*length*/
  0x04, 0x41, 0x50, 0x4e, 0x38, 0x04, 0x74, 0x65, 0x73, 0x74, /*value: "APN8.test"*/
  /*ninth APN*/
  0xdd,                                                       /*ninth APN tag*/
  0x0a,                                                       /*length*/
  0x04, 0x41, 0x50, 0x4e, 0x39, 0x04, 0x74, 0x65, 0x73, 0x74, /*value: "APN9.test"*/
   /*tenth APN*/
  0xdd,                                                       /*tenth APN tag*/
  0x0b,                                                       /*length*/
  0x05, 0x41, 0x50, 0x4e, 0x31, 0x30, 0x04, 0x74, 0x65, 0x73, 0x74, /*value: "APN10.test"*/
  /*eleventh APN*/
  0xdd,                                                       /*eleventh APN tag*/
  0x0b,                                                       /*length*/
  0x05, 0x41, 0x50, 0x4e, 0x31, 0x31, 0x04, 0x74, 0x65, 0x73, 0x74, /*value: "APN11.test"*/

  0xff, 0xff,
  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, /*padding*/
  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
};
#else
#if defined (SIM_EMU_EMPTY_ACL)
/*this ACL file is as per the USIM test spec, 31.121, section 9.1.1.4.1*/
Int8    defAclUsimData[] = {
  0x00,                                                       /*number of APNs*/
  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff , 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
};

#else

/*this ACL file is as per the USIM test spec, 31.121, section 9.1.1.4.1*/ 
Int8    defAclUsimData[] = {                                    //ZHWIEI CHANGE FOR test case 31121911 
  0x03,                                                       /*number of APNs*/
  /*First APN*/
  0xdd,                                                       /*first APN tag*/
  0x0a,                                                       /*length*/
  0x04, 0x74, 0x65, 0x73, 0x74, 0x04, 0x74, 0x65, 0x73, 0x74, /*value: "test.test" */
  /*second APN*/
  0xdd,                                                       /*second APN tag*/
  0x0a,                                                       /*length*/
  0x04, 0x33, 0x67, 0x70, 0x70, 0x04, 0x074, 0x65, 0x73, 0x74,/*value: "3gpp.test"*/
  /*Third APN*/
  0xdd,                                                       /*third APN tag*/
  0x0a,                                                       /*length*/
  0x04, 0x32, 0x67, 0x70, 0x70, 0x04, 0x74, 0x65, 0x73, 0x74, /*value: "2gpp.test"*/

  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, /*padding*/

  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff , 0xff, 0xff, 0xff, 0xff, 0xff, 0xff

};
#endif
#endif

SimEfData    defAclUsimEfData =
{
    SIM_EMU_EF_ACL,                  /* Id           */
    SIM_EFSTRUCT_T,                  /* Type         */
    SIM_DIR_ADF_USIM,                /* Dir          */
    1,                               /* Rec Len      */
    SIM_EMU_SIZE_USIM_ACL_FILE,      /* num Recs     */
    (0),                             /* curr Rec     */
    (0),
   {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_ALWAYS,      /* update */
     USIM_ACCESS_PIN       /* read */
   },
   SIM_UICC_ACTIVATED_STATE, /*file State */
   FALSE,                    /*sfi supported  */
   0x0,                      /*sfi */
   TRUE                      /*Under USIM */
};
#endif 
/*CQ00135656, Cgliu, 2022-02-25,End  */

/*-----------------18/07/2003 13:20-----------------
 * EF ARR: access rule file
 * --------------------------------------------------*/

Int8 defArrUsimData[] = {
  /*-----------------25/04/2003 11:49---------------------
   *  FIRST RECORD : READ ALWAYS, other commands: NEVER
   * -----------------------------------------------------*/

  0x80 /*AM DO*/ , 0x01  /*length*/, 0x01 /*value: READ access condition */,
  0x90 /*SC DO CRT*/, 0x00 /*ALWAYS*/,

  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,


  /*-----------------25/04/2003 11:49-------------------------------------------------------------
   *  SECOND RECORD : READ ALWAYS, UPDATE: PIN 1 APP 1 or PIN 2 APP 1  DEACTIVATE/ACTIVATE: ADM
   * ----------------------------------------------------------------------------------------------*/

  /*READ*/
  0x80 /*AM DO*/ , 0x01  /*length*/, 0x01 /*value: READ access condition */,
  0x90 /*SC DO CRT*/, 0x00 /*ALWAYS*/,
  /*UPDATE*/
  0x80 /*AM DO*/ , 0x01  /*length*/, 0x02 /*value: UPDATE access condition */,
  0xa0 /*SC DO CRT : OR TEMPLATE */, 0x10 /*length*/,
  /*PIN 1 APP 1 (global)*/
  0xa4 /*SC DO CRT */, 0x06 /*length*/ , 0x83 /*key ref Tag*/ , 0x01 /*length of key ref TLV*/, 0x01 /*key ref*/,
  0x95 /*usage qual tag*/, 0x01 /*length*/ , 0x08 /* usage qual value*/,
  /*PIN 2 APP*/
  0xa4 /*SC DO CRT */, 0x06 /*length*/ , 0x83 /*key ref Tag*/ , 0x01 /*length of key ref TLV*/, 0x81 /*key ref*/,
  0x95 /*usage qual tag*/, 0x01 /*length*/ , 0x08 /* usage qual value*/,
  /*DEACTIVATE/ACTIVATE*/
   0x80 /*AM DO*/ , 0x01  /*length*/, 0x18 /*value: ACTIVATE/DEACTIVATE access condition */,
   0xa4 /*SC DO CRT */, 0x06 /*length*/,
   0x83 /*key ref Tag*/ , 0x01 /*length of key ref TLV*/, 0x0a /*key ref value*/,
   0x95 /*usage qual tag*/, 0x01 /*length*/ , 0x08 /* usage qual value*/,

  /*-----------------25/04/2003 11:49------------------------------------------
   *  THIRD RECORD : READ, UPDATE, ACTIVATE: PIN 1 APP 1, DEACTIVATE: ADM
   * --------------------------------------------------------------------------*/
   /* READ, UPDATE, ACTIVATE*/
   0x80 /*AM DO*/ , 0x01  /*length*/, 0x13 /*value: READ/UPDATE/ACTIVATE access conditions */,
   0xa4 /*SC DO CRT */, 0x06 /*length*/,
   0x83 /*key ref Tag*/ , 0x01 /*length of key ref TLV*/, 0x01 /*key ref value: PIN1 APP 1*/,
   0x95 /*usage qual tag*/, 0x01 /*length*/ , 0x08 /* usage qual value*/,
   /*DEACTIVATE*/
   0x80 /*AM DO*/ , 0x01  /*length*/, 0x08 /*value: DEACTIVATE access condition */,
   0xa4 /*SC DO CRT */, 0x06 /*length*/,
   0x83 /*key ref Tag*/ , 0x01 /*length of key ref TLV*/, 0x0a /*key ref value: ADM*/,
   0x95 /*usage qual tag*/, 0x01 /*length*/ , 0x08 /* usage qual value*/,

   0xff, 0xff, 0xff, 0xff, 0xff,
   0xff, 0xff, 0xff, 0xff, 0xff,
   0xff, 0xff, 0xff, 0xff, 0xff,

   /*-----------------25/04/2003 11:49---------------------------------------------------------------
   *  FOURTH RECORD : READ ALWAYS, UPDATE: PIN 1 APP 1 AND PIN 2 APP 1    DEACTIVATE/ACTIVATE = ADM
   * -----------------------------------------------------------------------------------------------*/

   /*READ*/
   0x80 /*AM DO*/ , 0x01  /*length*/, 0x01 /*value: READ access condition */,
   0x90 /*SC DO CRT*/, 0x00 /*ALWAYS*/,
   /*UPDATE*/
   0x80 /*AM DO*/ , 0x01  /*length*/, 0x02 /*value: UPDATE access condition */,
   0xaf /*SC DO CRT : AND TEMPLATE */, 0x10 /*length*/,
   /*PIN 1 APP 1 (global)*/
   0xa4 /*SC DO CRT */, 0x06 /*length*/ , 0x83 /*key ref Tag*/ , 0x01 /*length of key ref TLV*/, 0x01 /*key ref*/,
   0x95 /*usage qual tag*/, 0x01 /*length*/ , 0x08 /* usage qual value*/,
   /*PIN 2 APP*/
   0xa4 /*SC DO CRT */, 0x06 /*length*/ , 0x83 /*key ref Tag*/ , 0x01 /*length of key ref TLV*/, 0x81 /*key ref*/,
   0x95 /*usage qual tag*/, 0x01 /*length*/ , 0x08 /* usage qual value*/,
   /*DEACTIVATE/ACTIVATE*/
   0x80 /*AM DO*/ , 0x01  /*length*/, 0x18 /*value: ACTIVATE/DEACTIVATE access condition */,
   0xa4 /*SC DO CRT */, 0x06 /*length*/,
   0x83 /*key ref Tag*/ , 0x01 /*length of key ref TLV*/, 0x0a /*key ref value*/,
   0x95 /*usage qual tag*/, 0x01 /*length*/ , 0x08 /* usage qual value*/ ,

   /*-----------------25/04/2003 11:49---------------------------------------------------------------
   *  FIFTH RECORD : READ PIN1, UPDATE: PIN 2     DEACTIVATE/ACTIVATE = ADM
   * -----------------------------------------------------------------------------------------------*/

   /*READ needs PIN1*/
   0x80 /*AM DO*/ , 0x01  /*length*/, 0x01 /*value: READ access condition */,
   0xa4 /*SC DO CRT */, 0x06 /*length*/ ,
   0x83 /*key ref Tag*/ , 0x01 /*length of key ref TLV*/, 0x01 /*key ref*/,
   0x95 /*usage qual tag*/, 0x01 /*length*/ , 0x08 /* usage qual value*/,
   /*UPDATE*/
   0x80 /*AM DO*/ , 0x01  /*length*/, 0x02 /*value: UPDATE access condition */,
   /*PIN 2   (global)*/
   0xa4 /*SC DO CRT */, 0x06 /*length*/ ,
   0x83 /*key ref Tag*/ , 0x01 /*length of key ref TLV*/, 0x81 /*key ref*/,
   0x95 /*usage qual tag*/, 0x01 /*length*/ , 0x08 /* usage qual value*/,
   /*DEACTIVATE/ACTIVATE*/
   0x80 /*AM DO*/ , 0x01  /*length*/, 0x18 /*value: ACTIVATE/DEACTIVATE access condition */,
   0xa4 /*SC DO CRT */, 0x06 /*length*/,
   0x83 /*key ref Tag*/ , 0x01 /*length of key ref TLV*/, 0x0a /*key ref value*/,
   0x95 /*usage qual tag*/, 0x01 /*length*/ , 0x08 /* usage qual value*/ ,
   0xff,0xff,0xff,0xff,

   /*-----------------25/04/2003 11:49---------------------------------------------------------------
   *  SIXTH RECORD : READ PIN1, UPDATE: PIN1     DEACTIVATE/ACTIVATE = ADM
   * -----------------------------------------------------------------------------------------------*/

   /*READ needs PIN1*/
   0x80 /*AM DO*/ , 0x01  /*length*/, 0x03 /*value: READ/UPDATE access condition */,
   0xa4 /*SC DO CRT */, 0x06 /*length*/ ,
   0x83 /*key ref Tag*/ , 0x01 /*length of key ref TLV*/, 0x01 /*key ref*/,
   0x95 /*usage qual tag*/, 0x01 /*length*/ , 0x08 /* usage qual value*/,

   /*DEACTIVATE/ACTIVATE*/
   0x80 /*AM DO*/ , 0x01  /*length*/, 0x18 /*value: ACTIVATE/DEACTIVATE access condition */,
   0xa4 /*SC DO CRT */, 0x06 /*length*/,
   0x83 /*key ref Tag*/ , 0x01 /*length of key ref TLV*/, 0x0a /*key ref value*/,
   0x95 /*usage qual tag*/, 0x01 /*length*/ , 0x08 /* usage qual value*/ ,
   0xff,0xff,0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff, 0xff



};

SimEfData defArrUsimEfData =
{
  SIM_EMU_EF_ARR,             /* Id           */
  SIM_EFSTRUCT_LF,            /* Type         */
  SIM_DIR_ADF_USIM,           /* Dir          */
  SIM_EMU_NUM_USIM_ARR_RECS,  /* Num Rec      */
  SIM_EMU_SIZE_USIM_ARR_FILE, /*  Rec  len   */
  (0),                        /* curr Rec     */
  (0),
  {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_ADM,      /* update */
     USIM_ACCESS_PIN       /* read */
   },
   SIM_UICC_ACTIVATED_STATE, /*file State */
   TRUE,                     /*sfi supported  */
   0x17,                     /*sfi */
   TRUE                      /*Under USIM */

 };
/*CQ00135656, Cgliu, 2022-02-25,Begin*/
#if defined (ON_PC)


/*-----------------05/04/02 16:13-------------------
 * EF CCP2 (capability configuration parameters 2)
 *
 * --------------------------------------------------*/
Int8    defCcp2UsimData[] = {

    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x01, 0xa0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x07, 0xc2, 0xc8, 0x81, 0x21, 0x13, 0x63, 0xa3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x07, 0xc2, 0xc8, 0x81, 0x21, 0x13, 0x63, 0xa3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff };

SimEfData    defCcp2UsimEfData =
{

    SIM_EMU_EF_CCP2,            /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    SIM_EMU_NUM_USIM_CCP2_RECS, /* Num Rec      */
    SIM_EMU_SIZE_USIM_CCP2_FILE,/*  Rec  len   */
  (0),                          /* curr Rec     */
  (0),
  {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
   },
   SIM_UICC_ACTIVATED_STATE, /*file State */
   TRUE,                     /*sfi supported  */
   0x16,                     /*sfi */
   TRUE                      /*Under USIM */
};

/*Chengguang modify for unit test, 2008-8-22, begin*/
Int8 defCbmirUsimData[]={
     0x00,0x01,0x00,0x10,
     0x11,0x11,0x22,0x22,
     0x33,0x33,0x44,0x44};
     
SimEfData     defCbmirUsimEfData =
     {
     
         SIM_EMU_EF_CBMIR,           /* Id           */
         SIM_EFSTRUCT_T,             /* Type         */
         SIM_DIR_ADF_USIM,           /* Dir          */
         1, /* Num Rec   */
         SIM_EMU_SIZE_USIM_CBMIR_FILE,/*  Rec  len  */
       (0),                          /* curr Rec     */
       (0),
       {
          USIM_ACCESS_NEVER,    /* delete file */
          USIM_ACCESS_NEVER,    /* terminate */
          USIM_ACCESS_ADM,      /* activate */
          USIM_ACCESS_ADM,      /* deactivate */
          USIM_ACCESS_NEVER,    /* write */
          USIM_ACCESS_PIN,      /* update */
          USIM_ACCESS_PIN       /* read */
        },
        SIM_UICC_ACTIVATED_STATE, /*file State */
        TRUE,                     /*sfi supported  */
        0x16,                     /*sfi */
        TRUE                      /*Under USIM */
     };
Int8 defAaemUsimData[]={0x02};
     
SimEfData     defAaemUsimEfData =
     {
     
         SIM_EMU_EF_AAEM,            /* Id           */
         SIM_EFSTRUCT_T,             /* Type         */
         SIM_DIR_ADF_USIM,           /* Dir          */
         1, /* Num Rec   */
         SIM_EMU_SIZE_USIM_AAEM_FILE,/*  Rec  len   */
       (0),                          /* curr Rec     */
       (0),
       {
          USIM_ACCESS_NEVER,    /* delete file */
          USIM_ACCESS_NEVER,    /* terminate */
          USIM_ACCESS_ADM,      /* activate */
          USIM_ACCESS_ADM,      /* deactivate */
          USIM_ACCESS_NEVER,    /* write */
          USIM_ACCESS_PIN,      /* update */
          USIM_ACCESS_PIN       /* read */
        },
        SIM_UICC_ACTIVATED_STATE, /*file State */
        TRUE,                     /*sfi supported  */
        0x16,                     /*sfi */
        TRUE                      /*Under USIM */
     };
Int8 defEmlppUsimData[]={0x02,0x02};
     
SimEfData     defEmlppUsimEfData =
     {
     
         SIM_EMU_EF_EMLPP,           /* Id           */
         SIM_EFSTRUCT_T,             /* Type         */
         SIM_DIR_ADF_USIM,           /* Dir          */
         1, /* Num Rec   */
         SIM_EMU_SIZE_USIM_EMLPP_FILE,/*  Rec  len  */
       (0),                          /* curr Rec     */
       (0),
       {
          USIM_ACCESS_NEVER,    /* delete file */
          USIM_ACCESS_NEVER,    /* terminate */
          USIM_ACCESS_ADM,      /* activate */
          USIM_ACCESS_ADM,      /* deactivate */
          USIM_ACCESS_NEVER,    /* write */
          USIM_ACCESS_ADM,      /* update */
          USIM_ACCESS_PIN       /* read */
        },
        SIM_UICC_ACTIVATED_STATE, /*file State */
        TRUE,                     /*sfi supported  */
        0x16,                     /*sfi */
        TRUE                      /*Under USIM */
     };
#endif 
/*CQ00135656, Cgliu, 2022-02-25,End  */

/*Chengguang modify for unit test, 2008-8-22, end  */
/*Modification for USIM it test. CQ00001116, Chengguang Liu, June.09 09,Begin*/
/*sim data for CCSA*/
#ifdef ON_PC

Int8    defImsiUsimData_ccsa[] = { 0x06,/*length of IMSI*/0x41, 0x06, 0x10, 0x20, 0x43, 0xF5, 0xFF, 0xFF };    /*IMSI*/

Int8    defAdUsimData_ccsa[] =  { 0,/*UE operation mode*/0,0,3};  /* length of MNC in the IMSI is now mandatory */

Int8    defKeysUsimData_ccsa[] = { 0x01,    /* ( key set identifier refered to as "KSI") */
                                   'C','I','P','H','E','R','K','E','Y','C','I','P','H','E','R','K', /*Ciphering Key CK*/
                                   'I','N','T','E','G','R','I','T','Y','K','E','Y','I','N','T','E'}; /*Integrity Key IK*/
Int8    defKeysPsUsimData_ccsa[] = {0x01,   /* ( key set identifier refered to as "KSI") */
                                    'P','S','C','I','P','H','E','R','K','E','Y','P','S','C','I','P', /*Ciphering Key CK*/
                                    'P','S','I','N','T','E','G','R','I','T','Y','K','E','Y','P','S'}; /*Integrity Key IK*/
Int8   defAccUsimData_ccsa[] = { 0x02, 0x00 };//is default value
Int8   defFplmnUsimData_ccsa[] = { 0x64, 0x10, 0x10,  /*FPLMN 1.   460 011 */
                                   0x64, 0x20, 0x10,  /*FPLMN 2.   460 012 */
                                   0x64, 0x30, 0x10,  /*FPLMN 3.   460 013 */
                                   0x64, 0x40, 0x10,  /*FPLMN 4.   460 014 */
                                   0x64, 0x50, 0x10,  /*FPLMN 5.   460 015 */
                                   0x64, 0x60, 0x10   /*FPLMN 6.   460 016 */
                                  };
SimEfData    defFplmnUsimEfData_ccsa =
{
    SIM_EMU_EF_FPLMN,                   /* Id           */
    SIM_EFSTRUCT_T,                 /* Type         */
    SIM_DIR_ADF_USIM,               /* Dir          */
    1,                              /* Num Rec      */
    18,   /* Rec length    */
    (0),                            /* curr Rec     */
    (0),
   {
    USIM_ACCESS_NEVER,    /* delete file*/
    USIM_ACCESS_NEVER,    /* terminate*/
    USIM_ACCESS_ADM,      /* activate */
    USIM_ACCESS_ADM,      /* deactivate */
    USIM_ACCESS_NEVER,    /* write */
    USIM_ACCESS_PIN,      /* update*/
    USIM_ACCESS_PIN       /* read*/
  },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    TRUE,                     /*sfi supported  */
    0x0d,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

Int8 defUstUsimData_ccsa[] = { 0xff,0xff,0xfc,0xff,0xff,0,0};
Int8 defEstUsimData_ccsa[] = { 0x00 };     /*FDN (bit1)    BDN  (bit2)  APN (bit3)*/
Int8 defEstUsimData_fdn_ccsa[] = { 0x01 }; /*enable FDN*/
Int8 defEstUsimData_bdn_ccsa[] = { 0xB1 }; /*Enable BDN*/



Int8    defUserPlmnSelUsimData_ccsa[] = { 0x64, 0x10, 0x80, 0x80, 0x00,/*1: PLMN:460 081, ACT: UTRAN*/
                                          0x64, 0x10, 0x80, 0x00, 0x80,/*2: PLMN:460 081, ACT: GSM*/
                                          0x64, 0x20, 0x80, 0x80, 0x00,/*3: PLMN:460 082, ACT: UTRAN*/
                                          0x64, 0x20, 0x80, 0x00, 0x80,/*4: PLMN:460 082, ACT: GSM*/
                                          0x64, 0x31, 0x00, 0x80, 0x00,/*5: PLMN:461 003, ACT: UTRAN*/
                                          0x64, 0x41, 0x00, 0x80, 0x00,/*6: PLMN:461 004, ACT: UTRAN*/
                                          0x64, 0x51, 0x00, 0x80, 0x00,/*7: PLMN:461 005, ACT: UTRAN*/
                                          0x64, 0x61, 0x00, 0x80, 0x00,/*8: PLMN:461 006, ACT: UTRAN*/
                                          0x64, 0x71, 0x00, 0x80, 0x00,/*9: PLMN:461 007, ACT: UTRAN*/
                                          0x64, 0x81, 0x00, 0x80, 0x00,/*10: PLMN:461 008, ACT: UTRAN*/
                                          0x64, 0x91, 0x00, 0x80, 0x00,/*11: PLMN:461 009, ACT: UTRAN*/
                                          0x64, 0x01, 0x10, 0x80, 0x00};/*12: PLMN:461 010, ACT: UTRAN*/

SimEfData    defUserPlmnSelUsimEfData_ccsa =
{
    SIM_EMU_EF_PLMNW_ACT,               /* Id           */
    SIM_EFSTRUCT_T,                 /* Type         */
    SIM_DIR_ADF_USIM,               /* Dir          */
    1,                              /* Num Rec      */
    60,   /* Rec length    */
    (0),                            /* curr Rec     */
    (0),
   {
    USIM_ACCESS_NEVER,    /* delete file*/
    USIM_ACCESS_NEVER,    /* terminate*/
    USIM_ACCESS_ADM,      /* activate */
    USIM_ACCESS_ADM,      /* deactivate */
    USIM_ACCESS_NEVER,    /* write */
    USIM_ACCESS_PIN,      /* update*/
    USIM_ACCESS_PIN       /* read*/
  },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    TRUE,                     /*sfi supported  */
    0x0d,                     /*sfi*/
    TRUE                      /*Under USIM*/
};
Int8    defOperatorPlmnSelUsimData_ccsa[] = { 0x54, 0x14, 0x00, 0x80, 0x00,/*1: PLMN:454 001, ACT: UTRAN*/
                                              0x54, 0x14, 0x00, 0x00, 0x80,/*2: PLMN:454 001, ACT: GSM*/
                                              0x54, 0x24, 0x00, 0x80, 0x00,/*3: PLMN:454 002, ACT: UTRAN*/
                                              0x54, 0x34, 0x00, 0x80, 0x00,/*4: PLMN:454 003, ACT: GSM*/
                                              0x54, 0x44, 0x00, 0x80, 0x00,/*5: PLMN:454 004, ACT: UTRAN*/
                                              0x54, 0x54, 0x00, 0x80, 0x00,/*6: PLMN:454 005, ACT: UTRAN*/
                                              0x54, 0x64, 0x00, 0x80, 0x00,/*7: PLMN:454 006, ACT: UTRAN*/
                                              0x54, 0x74, 0x00, 0x80, 0x00};/*8: PLMN:454 007, ACT: UTRAN*/
Int8    defPsLociUsimData_ccsa[] = { 0xff, 0xff, 0xff, 0xff,
                                     0xff, 0xff, 0xff,
                                     0x64, 0x10, 0x80, 0x00, 0x01, 0x05,
                                     0x00};
Int8    defFdnUsimData_ccsa[] =
{
        /*FDN111, +1357924680*/
        'F', 'D', 'N', '1','1','1',
        0x06,
        0x91,0x31,0x75,0x29,0x64,0x08,0xff,0xff,0xff,0xff,0xff, 
        0xff,
        0xff,

        /*FDN222, 24680*/
        'F', 'D', 'N', '2','2','2',
        0x04,
        0x81,0x42,0x86,0xF0,0xff,0xff,0xff,0xff,0xff,0xff,0xff, 
        0xff,
        0xff,

        /*FDN333, +12345678901234567890*/
        'F', 'D', 'N', '3','3','3',
        0x0B,
        0x91,0x21,0x43,0x65,0x87,0x09,0x21,0x43,0x65,0x87,0x09, 
        0xff,
        0xff

};

SimEfData    defFdnUsimEfData_ccsa =
{
#if defined(SIM_EMU_PHASE1)
    SIM_EMU_EF_INVALID,
#else
    SIM_EMU_EF_FDN,                  /* Id           */
#endif
    SIM_EFSTRUCT_LF,             /* Type         */
    SIM_DIR_ADF_USIM,            /* Dir          */
    SIM_EMU_NUM_USIM_FDN_RECS,   /* num Recs     */
    20,  /* Rec Len      */
    (0),                         /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN2,     /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};

Int8 defEccUsimData_ccsa[] = { 0x11,0xF9,0xFF,0x54,0x45,0x53,0x54,0x04 };/*119 TEST,Fire*/


SimEfData defEccUsimEfData_ccsa =
{
  SIM_EMU_EF_ECC,                 /* Id           */
  SIM_EFSTRUCT_LF,            /* Type         */
  SIM_DIR_ADF_USIM,           /* Dir          */
  SIM_EMU_NUM_USIM_ECC_RECS,  /* Num Recs      */
  8, /* Rec length     */
  (0),                        /* curr Rec     */
  (0),
  {   USIM_ACCESS_NEVER,    /* delete file*/
      USIM_ACCESS_NEVER,    /* terminate*/
      USIM_ACCESS_ADM,      /* activate */
      USIM_ACCESS_ADM,      /* deactivate */
      USIM_ACCESS_NEVER,    /* write */
      USIM_ACCESS_ADM,      /* update*/
      USIM_ACCESS_ALWAYS    /* read*/
  },
  SIM_UICC_ACTIVATED_STATE, /*file State*/
  TRUE,                     /*sfi supported  */
  0x01,                     /*sfi*/
  TRUE                      /*Under USIM*/
};

Int8 defBdnUsimData_ccsa[] = {

        /*BDN111, +1357924680*/
        'B', 'D', 'N', '1', '1', '1',
        0x06,
        0x91,
        0x31, 0x75, 0x29, 0x64, 0x08, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0xff,
        0xff,

        /*BDN222, +1357924680*/
        'B', 'D', 'N', '2', '2', '2',
        0x03,
        0x81,
        0x11, 0xF9, 0xFF, 0xFF, 0xFF, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0xff,
        0xff,

        /*BDN333, +1357924680*/
        'B', 'D', 'N', '2', '2', '2',
        0x03,
        0x81,
        0x21, 0xF2, 0xFF, 0xFF, 0xFF, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0xff,
        0xff      
};



SimEfData defBdnUsimEfData_ccsa =
{
  SIM_EMU_EF_BDN,                  /* Id       */
  SIM_EFSTRUCT_LF,             /* Type     */
  SIM_DIR_ADF_USIM,          /* Dir      */
  3,   /* Num recs */
  20,  /* Rec Len  */
  (0),                         /* curr Rec */
  (0),
  {
    USIM_ACCESS_NEVER,    /* delete file*/
    USIM_ACCESS_NEVER,    /* terminate*/
    USIM_ACCESS_ADM,      /* activate */
    USIM_ACCESS_ADM,      /* deactivate */
    USIM_ACCESS_NEVER,    /* write */
    USIM_ACCESS_PIN2,     /* update*/
    USIM_ACCESS_PIN       /* read*/
  },
  SIM_UICC_ACTIVATED_STATE, /*file State*/
  FALSE,                    /*sfi supported  */
  0x00,                     /*sfi*/
  TRUE                      /*Under USIM*/
};

Int8    defLociUsimData_ccsa[] = { 0xff, 0xff, 0xff, 0xff,    /*TMSI*/
                              0x00, 0xF1, 0x10, 0x00, 0x00,  /* LAI*/
                              0xff, 0x01  };   /*RFU + location update status*/
Int8  defImgData_ccsa[] =
{
 /* record number 1 */
  0x00, /* number of instances */
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, /* record 1, instance 1 */
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF
};

SimEfData  defImgEfData_ccsa =
{
  SIM_EMU_EF_IMG,               /* Id           */
  SIM_EFSTRUCT_LF,              /* Type         */
  SIM_DIR_DF_GRAPHICS,          /* Dir          */
  1,         /* num Recs     */
  SIM_EMU_SIZE_IMG_FILE,        /* Rec Len      */
  (0),                          /* curr Rec     */
  (0),
  {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate   */
     USIM_ACCESS_ADM,      /* activate    */
     USIM_ACCESS_ADM,      /* deactivate  */
     USIM_ACCESS_NEVER,    /* write       */
     USIM_ACCESS_PIN,      /* update      */
     USIM_ACCESS_PIN       /* read        */
  },
    SIM_UICC_ACTIVATED_STATE, /*file State     */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi            */
    FALSE                     /*Under USIM     */
};

Int8    defAcmUsimData_ccsa[] =  { 0x00, 0x00, 0x00};

SimEfData    defAcmUsimEfData_ccsa =
{
    SIM_EMU_EF_ACM,                 /* Id           */
    SIM_EFSTRUCT_C,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_ACM_FILE, /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};
Int8    defPuctUsimData_ccsa[] = { 0xFF, 0xFF, 0xFF, 0x00, 0x00 };
Int8    defAcmMaxUsimData_ccsa[] =  { 0x00,0x00,0x00};
Int8    defSmssUsimData_ccsa[] = { 0xff, 0xff };
Int8 defSmsrUsimData_ccsa[] = {
        0x0,
      0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
      0xff, 0xff, 0xff, 0xff, 0xff,

       0x0,
      0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
      0xff, 0xff, 0xff, 0xff, 0xff,

      0x0,
      0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
      0xff, 0xff, 0xff, 0xff, 0xff,

      0x0,
      0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
      0xff, 0xff, 0xff, 0xff, 0xff,


      0x0,
      0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
      0xff, 0xff, 0xff, 0xff, 0xff,

      0x0,
      0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
      0xff, 0xff, 0xff, 0xff, 0xff,

      0x0,
      0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
      0xff, 0xff, 0xff, 0xff, 0xff,

      0x0,
      0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
      0xff, 0xff, 0xff, 0xff, 0xff,

      0x0,
      0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
      0xff, 0xff, 0xff, 0xff, 0xff,

      0x0,
      0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
      0xff, 0xff, 0xff, 0xff, 0xff
};

Int8 defIciUsimData_ccsa[] = {
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x00,                                                             /*Incoming call status */
        0x01, 0xff, 0xff,                                                 /*link to phonebook entry*/

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x00,                                                             /*Incoming call status */
        0x01, 0xff, 0xff,                                                 /*link to phonebook entry*/

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x00,                                                             /*Incoming call status */
        0x01, 0xff, 0xff,                                                 /*link to phonebook entry*/

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x00,                                                             /*Incoming call status */
        0x01, 0xff, 0xff,                                                 /*link to phonebook entry*/

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x00,                                                             /*Incoming call status */
        0x01, 0xff, 0xff,                                                 /*link to phonebook entry*/

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x00,                                                             /*Incoming call status */
        0x01, 0xff, 0xff,                                                 /*link to phonebook entry*/

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x00,                                                             /*Incoming call status */
        0x01, 0xff, 0xff                                                  /*link to phonebook entry*/


};

Int8 defOciUsimData_ccsa[] = {
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x01, 0xff, 0xff,                                                 /*link to phonebook entry*/

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x01, 0xff, 0xff,                                                 /*link to phonebook entry*/

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x01, 0xff, 0xff,                                                 /*link to phonebook entry*/

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x01, 0xff, 0xff,                                                 /*link to phonebook entry*/

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x01, 0xff, 0xff,                                                 /*link to phonebook entry*/

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x01, 0xff, 0xff,                                                 /*link to phonebook entry*/

};
Int8    defIctUsimData_ccsa[] = { 0,0,0};
Int8    defOctUsimData_ccsa[] = { 0,0,0};
Int8 defExt3UsimData_ccsa[] = {
/*1*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*2*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*3*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*4*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*5*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*6*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*7*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*8*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*9*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*a*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*b*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*c*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff
};

Int8 defExt5UsimData_ccsa[] = {
/*1*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*2*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*3*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*4*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*5*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*6*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*7*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*8*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*9*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*a*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*b*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*c*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff
};

Int8 defExt6UsimData_ccsa[] = {
/*1*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*2*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*3*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*4*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*5*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*6*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*7*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*8*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*9*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*a*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

};

Int8 defExt7UsimData_ccsa[] = {
/*1*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*2*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*3*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*4*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*5*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*6*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*7*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        
/*8*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*9*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*a*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*b*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*c*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff
};



Int8 defCfisUsimData_ccsa[] = {
/*1*/   0x11, /*MSP number*/
        0x00, /*CFU status*/
        0xff, /*length of BCD number */
        0xff, /*TON and NPI */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, /*dialling number */
        0xff, /*CCP2*/
        0xff,  /*EXT7 record*/


/*2*/   0x11, /*MSP number*/
        0x00, /*CFU status*/
        0xff, /*length of BCD number */
        0xff, /*TON and NPI */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, /*dialling number */
        0xff, /*CCP2*/
        0xff,  /*EXT7 record*/


/*2*/   0x11, /*MSP number*/
        0x00, /*CFU status*/
        0xff, /*length of BCD number */
        0xff, /*TON and NPI */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, /*dialling number */
        0xff, /*CCP2*/
        0xff,  /*EXT7 record*/



/*2*/   0x11, /*MSP number*/
        0x00, /*CFU status*/
        0xff, /*length of BCD number */
        0xff, /*TON and NPI */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, /*dialling number */
        0xff, /*CCP2*/
        0xff  /*EXT7 record*/

};

Int8    defExt2UsimData_ccsa[] = {

/*1*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*2*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*3*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*4*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*5*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*6*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*7*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*8*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*9*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*a*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*b*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

};
Int8    defKcGprsUsimData_ccsa[] = { 0xff,0xff, 0xff,0xff, 0xff, 0xff, 0xff, 0xff, /*ciphering key KcGPRS*/
                                0x07 };                    /*ciphering key sequence number n for GPRS*/

Int8    defHplmnSelUsimData_ccsa[] = { 0xff, 0xff, 0xff, 0x00, 0x00,
                                  0xff, 0xff, 0xff, 0x00, 0x00,
                                  0xff, 0xff, 0xff, 0x00, 0x00,
                                  0xff, 0xff, 0xff, 0x00, 0x00,
                                  0xff, 0xff, 0xff, 0x00, 0x00,
                                  0xff, 0xff, 0xff, 0x00, 0x00,
                                  0xff, 0xff, 0xff, 0x00, 0x00,
                                  0xff, 0xff, 0xff, 0x00, 0x00 };

Int8    defStartHfnUsimData_ccsa[] = { 0xf0, 0x00, 0x00,   /*START CS*/
                                  0xf0, 0x00, 0x00 }; /*START PS*/
Int8    defAclUsimData_ccsa[] = {
  0x00,                                                       /*number of APNs*/
  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff , 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
};

Int8    defCpbcchUsimData_ccsa[]={0xff,0xff,0xff,0xff};
SimEfData    defCpbcchUsimEfData_ccsa =
{
    SIM_EUM_EF_CPBCCH,                 /* Id           */
    SIM_EFSTRUCT_T,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_CPBCCH_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

Int8    defInvscanUsimData_ccsa[]={0x00};
SimEfData    defInvscanUsimEfData_ccsa =
{
    SIM_EMU_EF_INVSCAN,                 /* Id           */
    SIM_EFSTRUCT_T,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_INVSCAN_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_ADM,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

Int8    defSumeUsimData_ccsa[]={0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};
SimEfData    defSumeUsimEfData_ccsa =
{
    SIM_EMU_EF_SUME,                 /* Id           */
    SIM_EFSTRUCT_T,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_SUME_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_ADM,      /* update*/
     USIM_ACCESS_ADM       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

Int8    defCmiUsimData_ccsa[]={0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                               0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,0xff};
SimEfData    defCmiUsimEfData_ccsa =
{
    SIM_EMU_EF_CMI,                 /* Id           */
    SIM_EFSTRUCT_LF,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_CMI_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_ADM,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

Int8    defRplmnUsimData_ccsa[]={0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                               0x00, 0x00, 0x00, 0x00, 0x00};
SimEfData    defRplmnUsimEfData_ccsa =
{
    SIM_EMU_EF_RPLMN,                 /* Id           */
    SIM_EFSTRUCT_T,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_RPLMN_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};


Int8    defMbiUsimData_ccsa[]={0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};
SimEfData    defMbiUsimEfData_ccsa =
{
    SIM_EMU_EF_MBI,                 /* Id           */
    SIM_EFSTRUCT_LF,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    2,  /* Num rec      */
    SIM_EMU_SIZE_USIM_MBI_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

Int8    defMmsnUsimData_ccsa[]={0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};
SimEfData    defMmsnUsimEfData_ccsa =
{
    SIM_EMU_EF_MMSN,                 /* Id           */
    SIM_EFSTRUCT_LF,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_MMSN_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

Int8    defExt8UsimData_ccsa[]={0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};
SimEfData    defExt8UsimEfData_ccsa =
{
    SIM_EMU_EF_EXT8,                 /* Id           */
    SIM_EFSTRUCT_LF,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_EXT8_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

Int8    defMmsicpUsimData_ccsa[]={0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};
SimEfData    defMmsicpUsimEfData_ccsa =
{
    SIM_EMU_EF_MMSICP,                 /* Id           */
    SIM_EFSTRUCT_T,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_MMSICP_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};


Int8    defMmsupUsimData_ccsa[]={0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};
SimEfData    defMmsupUsimEfData_ccsa =
{
    SIM_EMU_EF_MMSUP,                 /* Id           */
    SIM_EFSTRUCT_LF,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_MMSUP_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};


Int8    defMmsucpUsimData_ccsa[]={0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};
SimEfData    defMmsucpUsimEfData_ccsa =
{
    SIM_EMU_EF_MMSUCP,                 /* Id           */
    SIM_EFSTRUCT_T,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_MMSUCP_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};
SimMfDfData    defMfData_ccsa =
{
    SIM_DIR_MF,
    SIM_DIR_INVALID,
    (2048),                                     /*total memory available*/
    SIM_FILETYPE_MF,                            /*type of file*/
    SIM_UICC_ACTIVATED_STATE,                   /*file State*/
    SIM_CLKSTOP_NOT_ALLOWED,                    /*clock mode*/
    SIM_MIN_AUTH_FREQ_13OVER8,                  /*min auth freq*/
    SIM_1_8_OR_3_VOLTS_CAPAB,                   /*SIM voltage capability*/
    {                                           /*Access data*/
       USIM_ACCESS_NEVER,   /* delete self */
       USIM_ACCESS_NEVER,   /* terminate */
       USIM_ACCESS_NEVER,   /* activate */
       USIM_ACCESS_NEVER,   /* deactivate */
       USIM_ACCESS_NEVER,   /* createEf */
       USIM_ACCESS_NEVER,   /* createDf */
       USIM_ACCESS_NEVER    /* deleteChild */
    },
    0,
    0,
    USIM_ACCESS_PIN1_APP1,                      /*Global application Pin*/
    USIM_ACCESS_PIN2_APP1,                      /*local PIN */
    {                                           /*local PIN status*/
        3,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x33, 0x35, 0x37, 0x39, 0xff, 0xff, 0xff,0xff}   /*local PIN value */

    },
    {                                           /*unblock PIN 2 status*/
        10,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x30, 0x38, 0x39, 0x37, 0x38, 0x36, 0x37, 0x35}  /*unblock PIN 2 value*/
     }

};
SimMfDfData    defPhoneBookUsimDfData_ccsa =
{
    SIM_DIR_DF_PHONEBOOK,
    SIM_DIR_ADF_USIM,
    (2048),
    SIM_FILETYPE_DF,
    SIM_UICC_ACTIVATED_STATE,                   /*file State*/
    SIM_CLKSTOP_NOT_ALLOWED,                    /*clock mode*/
    SIM_MIN_AUTH_FREQ_13OVER8,                  /*min auth freq*/
    SIM_1_8_OR_3_VOLTS_CAPAB,                   /*SIM voltage capability*/
    {                                           /*Access data*/
       USIM_ACCESS_NEVER,   /* delete self */
       USIM_ACCESS_NEVER,   /* terminate */
       USIM_ACCESS_NEVER,   /* activate */
       USIM_ACCESS_NEVER,   /* deactivate */
       USIM_ACCESS_NEVER,   /* createEf */
       USIM_ACCESS_NEVER,   /* createDf */
       USIM_ACCESS_NEVER    /* deleteChild */
    },
    0,
    0,
    USIM_ACCESS_PIN1_APP1,                      /*Global application Pin*/
    USIM_ACCESS_PIN2_APP1,                      /*local PIN */
    {                                           /*local PIN status*/
        3,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
         {0x33, 0x35, 0x37, 0x39, 0xff, 0xff, 0xff,0xff}   /*local PIN value */
    },
    {                                           /*unblock PIN 2 status*/
        10,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x30, 0x38, 0x39, 0x37, 0x38, 0x36, 0x37, 0x35}  /*unblock PIN 2 value*/
    }


};

/*-----------------17/07/2003 16:13-------------------------
 * global phonebook directory (phonebook under DF TELECOM)
 * --------------------------------------------------------*/

SimMfDfData    defPhoneBookDfData_ccsa =
{
    SIM_DIR_DF_PHONEBOOK,
    SIM_DIR_DF_TELECOM,
    (2048),
    SIM_FILETYPE_DF,
    SIM_UICC_ACTIVATED_STATE,                   /*file State*/
    SIM_CLKSTOP_NOT_ALLOWED,                    /*clock mode*/
    SIM_MIN_AUTH_FREQ_13OVER8,                  /*min auth freq*/
    SIM_1_8_OR_3_VOLTS_CAPAB,                   /*SIM voltage capability*/
    {                                           /*Access data*/
       USIM_ACCESS_NEVER,   /* delete self */
       USIM_ACCESS_NEVER,   /* terminate */
       USIM_ACCESS_NEVER,   /* activate */
       USIM_ACCESS_NEVER,   /* deactivate */
       USIM_ACCESS_NEVER,   /* createEf */
       USIM_ACCESS_NEVER,   /* createDf */
       USIM_ACCESS_NEVER    /* deleteChild */
    },
    0,
    0,
    USIM_ACCESS_PIN1_APP1,                      /*Global application Pin*/
    USIM_ACCESS_PIN2_APP1,                      /*local PIN */
    {                                           /*local PIN status*/
        3,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x33, 0x35, 0x37, 0x39, 0xff, 0xff, 0xff,0xff}   /*local PIN value */
    },
    {                                           /*unblock PIN 2 status*/
        10,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x30, 0x38, 0x39, 0x37, 0x38, 0x36, 0x37, 0x35}  /*unblock PIN 2 value*/
    }


};




/*-----------------17/07/2003 16:13-----------------
 * GSM ACCESS directory under ADF USIM Directory
 * --------------------------------------------------*/
SimMfDfData    defGsmAccessUsimDfData_ccsa =
{
    SIM_DIR_DF_GSM_ACCESS,
    SIM_DIR_ADF_USIM,
    (2048),
    SIM_FILETYPE_DF,
    SIM_UICC_ACTIVATED_STATE,                   /*file State*/
    SIM_CLKSTOP_NOT_ALLOWED,                    /*clock mode*/
    SIM_MIN_AUTH_FREQ_13OVER8,                  /*min auth freq*/
    SIM_1_8_OR_3_VOLTS_CAPAB,                   /*SIM voltage capability*/
    {                                           /*Access data*/
       USIM_ACCESS_NEVER,   /* delete self */
       USIM_ACCESS_NEVER,   /* terminate */
       USIM_ACCESS_NEVER,   /* activate */
       USIM_ACCESS_NEVER,   /* deactivate */
       USIM_ACCESS_NEVER,   /* createEf */
       USIM_ACCESS_NEVER,   /* createDf */
       USIM_ACCESS_NEVER    /* deleteChild */
    },
    0,
    0,
    USIM_ACCESS_PIN1_APP1,                      /*Global application Pin*/
    USIM_ACCESS_PIN2_APP1,                      /*local PIN */
    {                                           /*local PIN status*/
        3,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x33, 0x35, 0x37, 0x39, 0xff, 0xff, 0xff,0xff}  /*local PIN value */
    },
    {                                           /*unblock PIN 2 status*/
        10,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x30, 0x38, 0x39, 0x37, 0x38, 0x36, 0x37, 0x35}  /*unblock PIN 2 value*/
    }


};


SimMfDfData    defTelecomDfData_ccsa =
{
    SIM_DIR_DF_TELECOM,
    SIM_DIR_MF,
    (2048),
    SIM_FILETYPE_DF,
    SIM_UICC_ACTIVATED_STATE,                   /*file State*/
    SIM_CLKSTOP_NOT_ALLOWED,                    /*clock mode*/
    SIM_MIN_AUTH_FREQ_13OVER8,                  /*min auth freq*/
    SIM_1_8_OR_3_VOLTS_CAPAB,                   /*SIM voltage capability*/
    {                                           /*Access data*/
       USIM_ACCESS_NEVER,                       /* delete self */
       USIM_ACCESS_NEVER,                       /* terminate */
       USIM_ACCESS_NEVER,                       /* activate */
       USIM_ACCESS_NEVER,                       /* deactivate */
       USIM_ACCESS_NEVER,                       /* createEf */
       USIM_ACCESS_NEVER,                       /* createDf */
       USIM_ACCESS_NEVER                        /* deleteChild */
    },
    0,
    0,
    USIM_ACCESS_PIN1_APP1,                      /*Global application Pin*/
    USIM_ACCESS_PIN2_APP1,                      /*local PIN */
    {                                           /*local PIN status*/
        3,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x33, 0x35, 0x37, 0x39, 0xff, 0xff, 0xff,0xff}   /*local PIN value */
    },
    {                                           /*unblock PIN 2 status*/
        10,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x30, 0x38, 0x39, 0x37, 0x38, 0x36, 0x37, 0x35}  /*unblock PIN 2 value*/
    }


};


SimMfDfData    defGraphicsDfData_ccsa =
{
    SIM_DIR_DF_GRAPHICS,
    SIM_DIR_DF_TELECOM,
    (2048),
    SIM_FILETYPE_DF,
    SIM_UICC_ACTIVATED_STATE,                   /*file State*/
    SIM_CLKSTOP_NOT_ALLOWED,                    /*clock mode*/
    SIM_MIN_AUTH_FREQ_13OVER8,                  /*min auth freq*/
    SIM_1_8_OR_3_VOLTS_CAPAB,                   /*SIM voltage capability*/
    {                                           /*Access data*/
       USIM_ACCESS_NEVER,                       /* delete self */
       USIM_ACCESS_NEVER,                       /* terminate */
       USIM_ACCESS_NEVER,                       /* activate */
       USIM_ACCESS_NEVER,                       /* deactivate */
       USIM_ACCESS_NEVER,                       /* createEf */
       USIM_ACCESS_NEVER,                       /* createDf */
       USIM_ACCESS_NEVER                        /* deleteChild */
    },
    0,
    0,
    USIM_ACCESS_PIN1_APP1,                      /*Global application Pin*/
    USIM_ACCESS_PIN2_APP1,                      /*local PIN */
    {                                           /*local PIN status*/
        3,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x33, 0x35, 0x37, 0x39, 0xff, 0xff, 0xff,0xff}   /*local PIN value */
    },
    {                                           /*unblock PIN 2 status*/
        10,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x30, 0x38, 0x39, 0x37, 0x38, 0x36, 0x37, 0x35} /*unblock PIN 2 value*/
    }


};

/*-----------------17/07/2003 16:14-----------------
 * ADF USIM directory
 * --------------------------------------------------*/

SimMfDfData    defAdfUsimData_ccsa =
{
    SIM_DIR_ADF_USIM,
    SIM_DIR_MF,
    (2048),
    SIM_FILETYPE_ADF,
    SIM_UICC_ACTIVATED_STATE,                   /*file State*/
    SIM_CLKSTOP_NOT_ALLOWED,                    /*clock mode*/
    SIM_MIN_AUTH_FREQ_13OVER8,                  /*min auth freq*/
    SIM_1_8_OR_3_VOLTS_CAPAB,                   /*SIM voltage capability*/
    {                                           /*Access data*/
       USIM_ACCESS_NEVER,                       /* delete self */
       USIM_ACCESS_NEVER,                       /* terminate */
       USIM_ACCESS_NEVER,                       /* activate */
       USIM_ACCESS_NEVER,                       /* deactivate */
       USIM_ACCESS_NEVER,                       /* createEf */
       USIM_ACCESS_NEVER,                       /* createDf */
       USIM_ACCESS_NEVER                        /* deleteChild */
    },
    0,
    0,
    USIM_ACCESS_PIN1_APP1,                      /*Global application Pin*/
    USIM_ACCESS_PIN2_APP1,                      /*local PIN */
    {                                           /*local PIN status*/
        3,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x33, 0x35, 0x37, 0x39, 0xff, 0xff, 0xff,0xff}   /*local PIN value */
    },
    {                                           /*unblock PIN 2 status*/
        10,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x30, 0x38, 0x39, 0x37, 0x38, 0x36, 0x37, 0x35} /*unblock PIN 2 value*/
    },


    {                                             /* AID: Only used for ADF_USIM */
      {0xa0,0x00,0x00,0x00,0x87,
      /*3G app code */ 0x10, 0x01, /*PIX*/ 0x32, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff},  /* AID  value */
      0x10
    }
    };

Int8    defImsiUsimData_31121[] = { 0x06,/*length of IMSI*/0x21, 0x64, 0x80, 0x31, 0x75, 0xF9, 0xFF, 0xFF };    /*IMSI*/

Int8    defAdUsimData_31121[] =  { 0,/*UE operation mode*/0,0,3};  /* length of MNC in the IMSI is now mandatory */

Int8    defKeysUsimData_31121[] = { 0x01,    /* ( key set identifier refered to as "KSI") */
                                   'C','I','P','H','E','R','K','E','Y','C','I','P','H','E','R','K', /*Ciphering Key CK*/
                                   'I','N','T','E','G','R','I','T','Y','K','E','Y','I','N','T','E'}; /*Integrity Key IK*/
Int8    defKeysPsUsimData_31121[] = {0x01,   /* ( key set identifier refered to as "KSI") */
                                    'P','S','C','I','P','H','E','R','K','E','Y','P','S','C','I','P', /*Ciphering Key CK*/
                                    'P','S','I','N','T','E','G','R','I','T','Y','K','E','Y','P','S'}; /*Integrity Key IK*/
Int8   defAccUsimData_31121[] = { 0x02, 0x00 };//is default value
Int8   defFplmnUsimData_31121[] = { 0x32, 0x14, 0x00,  /*FPLMN 1.   234 001 */
                                    0x32, 0x24, 0x00,  /*FPLMN 2.   234 002 */
                                    0x32, 0x34, 0x00,  /*FPLMN 3.   234 003 */
                                    0x32, 0x44, 0x00,  /*FPLMN 4.   234 004 */
                                    0x32, 0x54, 0x00,  /*FPLMN 5.   234 005 */
                                    0x32, 0x64, 0x00   /*FPLMN 6.   234 006 */
                                  };
SimEfData    defFplmnUsimEfData_31121 =
{
    SIM_EMU_EF_FPLMN,                   /* Id           */
    SIM_EFSTRUCT_T,                 /* Type         */
    SIM_DIR_ADF_USIM,               /* Dir          */
    1,                              /* Num Rec      */
    18,   /* Rec length    */
    (0),                            /* curr Rec     */
    (0),
   {
    USIM_ACCESS_NEVER,    /* delete file*/
    USIM_ACCESS_NEVER,    /* terminate*/
    USIM_ACCESS_ADM,      /* activate */
    USIM_ACCESS_ADM,      /* deactivate */
    USIM_ACCESS_NEVER,    /* write */
    USIM_ACCESS_PIN,      /* update*/
    USIM_ACCESS_PIN       /* read*/
  },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    TRUE,                     /*sfi supported  */
    0x0d,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

Int8 defUstUsimData_31121[] = { 0xff,0xff,0xfc,0xff,0xff,0,0};
Int8 defEstUsimData_31121[] = { 0x00 };     /*FDN (bit1)    BDN  (bit2)  APN (bit3)*/
Int8 defEstUsimData_fdn_31121[] = { 0x01 }; /*enable FDN*/
Int8 defEstUsimData_bdn_31121[] = { 0xB1 }; /*Enable BDN*/



Int8    defUserPlmnSelUsimData_31121[] = { 0x42, 0x14, 0x80, 0x80, 0x00, /*1: PLMN:244 081, ACT: UTRAN*/
                                           0x42, 0x14, 0x80, 0x00, 0x80, /*2: PLMN:244 081, ACT: GSM*/
                                           0x42, 0x24, 0x80, 0x80, 0x00, /*3: PLMN:244 082, ACT: UTRAN*/
                                           0x42, 0x24, 0x80, 0x00, 0x80, /*4: PLMN:244 082, ACT: GSM*/
                                           0x42, 0x34, 0x00, 0x80, 0x00, /*5: PLMN:244 003, ACT: UTRAN*/
                                           0x42, 0x44, 0x00, 0x80, 0x00, /*6: PLMN:244 004, ACT: UTRAN*/
                                           0x42, 0x54, 0x00, 0x80, 0x00, /*7: PLMN:244 005, ACT: UTRAN*/
                                           0x42, 0x64, 0x00, 0x80, 0x00, /*8: PLMN:244 006, ACT: UTRAN*/
                                           0x42, 0x74, 0x00, 0x80, 0x00, /*9: PLMN:244 007, ACT: UTRAN*/
                                           0x42, 0x84, 0x00, 0x80, 0x00, /*10: PLMN:244 008, ACT: UTRAN*/
                                           0x42, 0x94, 0x00, 0x80, 0x00, /*11: PLMN:244 009, ACT: UTRAN*/
                                           0x42, 0x04, 0x10, 0x80, 0x00};/*12: PLMN:244 010, ACT: UTRAN*/

SimEfData    defUserPlmnSelUsimEfData_31121 =
{
    SIM_EMU_EF_PLMNW_ACT,               /* Id           */
    SIM_EFSTRUCT_T,                 /* Type         */
    SIM_DIR_ADF_USIM,               /* Dir          */
    1,                              /* Num Rec      */
    60,   /* Rec length    */
    (0),                            /* curr Rec     */
    (0),
   {
    USIM_ACCESS_NEVER,    /* delete file*/
    USIM_ACCESS_NEVER,    /* terminate*/
    USIM_ACCESS_ADM,      /* activate */
    USIM_ACCESS_ADM,      /* deactivate */
    USIM_ACCESS_NEVER,    /* write */
    USIM_ACCESS_PIN,      /* update*/
    USIM_ACCESS_PIN       /* read*/
  },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    TRUE,                     /*sfi supported  */
    0x0d,                     /*sfi*/
    TRUE                      /*Under USIM*/
};
Int8    defOperatorPlmnSelUsimData_31121[] = { 0x54, 0x14, 0x00, 0x80, 0x00,/*1: PLMN:454 001, ACT: UTRAN*/
                                              0x54, 0x14, 0x00, 0x00, 0x80,/*2: PLMN:454 001, ACT: GSM*/
                                              0x54, 0x24, 0x00, 0x80, 0x00,/*3: PLMN:454 002, ACT: UTRAN*/
                                              0x54, 0x34, 0x00, 0x80, 0x00,/*4: PLMN:454 003, ACT: GSM*/
                                              0x54, 0x44, 0x00, 0x80, 0x00,/*5: PLMN:454 004, ACT: UTRAN*/
                                              0x54, 0x54, 0x00, 0x80, 0x00,/*6: PLMN:454 005, ACT: UTRAN*/
                                              0x54, 0x64, 0x00, 0x80, 0x00,/*7: PLMN:454 006, ACT: UTRAN*/
                                              0x54, 0x74, 0x00, 0x80, 0x00};/*8: PLMN:454 007, ACT: UTRAN*/
Int8    defPsLociUsimData_31121[] = { 0xff, 0xff, 0xff, 0xff,
                                     0xff, 0xff, 0xff,
                                     0x42, 0x16, 0x80, 0x00, 0x01, 0x05,
                                     0x00};
Int8    defFdnUsimData_31121[] =
{
        /*FDN111, +1357924680*/
        'F', 'D', 'N', '1','1','1',
        0x06,
        0x91,0x31,0x75,0x29,0x64,0x08,0xff,0xff,0xff,0xff,0xff, 
        0xff,
        0xff,

        /*FDN222, 24680*/
        'F', 'D', 'N', '2','2','2',
        0x04,
        0x81,0x42,0x86,0xF0,0xff,0xff,0xff,0xff,0xff,0xff,0xff, 
        0xff,
        0xff,

        /*FDN333, +12345678901234567890*/
        'F', 'D', 'N', '3','3','3',
        0x0B,
        0x91,0x21,0x43,0x65,0x87,0x09,0x21,0x43,0x65,0x87,0x09, 
        0xff,
        0xff

};

SimEfData    defFdnUsimEfData_31121 =
{
#if defined(SIM_EMU_PHASE1)
    SIM_EMU_EF_INVALID,
#else
    SIM_EMU_EF_FDN,                  /* Id           */
#endif
    SIM_EFSTRUCT_LF,             /* Type         */
    SIM_DIR_ADF_USIM,            /* Dir          */
    SIM_EMU_NUM_USIM_FDN_RECS,   /* num Recs     */
    20,  /* Rec Len      */
    (0),                         /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN2,     /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};

Int8 defEccUsimData_31121[] = { 0x11,0xF9,0xFF,0x54,0x45,0x53,0x54,0x04 };/*119 TEST,Fire*/


SimEfData defEccUsimEfData_31121 =
{
  SIM_EMU_EF_ECC,                 /* Id           */
  SIM_EFSTRUCT_LF,            /* Type         */
  SIM_DIR_ADF_USIM,           /* Dir          */
  SIM_EMU_NUM_USIM_ECC_RECS,  /* Num Recs      */
  8, /* Rec length     */
  (0),                        /* curr Rec     */
  (0),
  {   USIM_ACCESS_NEVER,    /* delete file*/
      USIM_ACCESS_NEVER,    /* terminate*/
      USIM_ACCESS_ADM,      /* activate */
      USIM_ACCESS_ADM,      /* deactivate */
      USIM_ACCESS_NEVER,    /* write */
      USIM_ACCESS_ADM,      /* update*/
      USIM_ACCESS_ALWAYS    /* read*/
  },
  SIM_UICC_ACTIVATED_STATE, /*file State*/
  TRUE,                     /*sfi supported  */
  0x01,                     /*sfi*/
  TRUE                      /*Under USIM*/
};

Int8 defBdnUsimData_31121[] = {

        /*BDN111, +1357924680*/
        'B', 'D', 'N', '1', '1', '1',
        0x06,
        0x91,
        0x31, 0x75, 0x29, 0x64, 0x08, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0xff,
        0xff,

        /*BDN222, +1357924680*/
        'B', 'D', 'N', '2', '2', '2',
        0x03,
        0x81,
        0x11, 0xF9, 0xFF, 0xFF, 0xFF, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0xff,
        0xff,

        /*BDN333, +1357924680*/
        'B', 'D', 'N', '2', '2', '2',
        0x03,
        0x81,
        0x21, 0xF2, 0xFF, 0xFF, 0xFF, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0xff,
        0xff      
};



SimEfData defBdnUsimEfData_31121 =
{
  SIM_EMU_EF_BDN,                  /* Id       */
  SIM_EFSTRUCT_LF,             /* Type     */
  SIM_DIR_ADF_USIM,          /* Dir      */
  3,   /* Num recs */
  20,  /* Rec Len  */
  (0),                         /* curr Rec */
  (0),
  {
    USIM_ACCESS_NEVER,    /* delete file*/
    USIM_ACCESS_NEVER,    /* terminate*/
    USIM_ACCESS_ADM,      /* activate */
    USIM_ACCESS_ADM,      /* deactivate */
    USIM_ACCESS_NEVER,    /* write */
    USIM_ACCESS_PIN2,     /* update*/
    USIM_ACCESS_PIN       /* read*/
  },
  SIM_UICC_ACTIVATED_STATE, /*file State*/
  FALSE,                    /*sfi supported  */
  0x00,                     /*sfi*/
  TRUE                      /*Under USIM*/
};

Int8    defLociUsimData_31121[] = { 0xff, 0xff, 0xff, 0xff,    /*TMSI*/
                              0x42, 0x16, 0x80, 0x00, 0x01,  /* LAI*/
                              0xff, 0x00  };   /*RFU + location update status*/
Int8  defImgData_31121[] =
{
 /* record number 1 */
  0x00, /* number of instances */
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, /* record 1, instance 
1 */
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF
};

SimEfData  defImgEfData_31121 =
{
  SIM_EMU_EF_IMG,               /* Id           */
  SIM_EFSTRUCT_LF,              /* Type         */
  SIM_DIR_DF_GRAPHICS,          /* Dir          */
  1,         /* num Recs     */
  SIM_EMU_SIZE_IMG_FILE,        /* Rec Len      */
  (0),                          /* curr Rec     */
  (0),
  {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate   */
     USIM_ACCESS_ADM,      /* activate    */
     USIM_ACCESS_ADM,      /* deactivate  */
     USIM_ACCESS_NEVER,    /* write       */
     USIM_ACCESS_PIN,      /* update      */
     USIM_ACCESS_PIN       /* read        */
  },
    SIM_UICC_ACTIVATED_STATE, /*file State     */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi            */
    FALSE                     /*Under USIM     */
};

Int8    defAcmUsimData_31121[] =  { 0x00, 0x00, 0x00};

SimEfData    defAcmUsimEfData_31121 =
{
    SIM_EMU_EF_ACM,                 /* Id           */
    SIM_EFSTRUCT_C,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_ACM_FILE, /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};
Int8    defPuctUsimData_31121[] = { 0xFF, 0xFF, 0xFF, 0x00, 0x00 };
Int8    defAcmMaxUsimData_31121[] =  { 0x00,0x00,0x00};
Int8    defSmssUsimData_31121[] = { 0xff, 0xff };
Int8 defSmsrUsimData_31121[] = {
        0x0,
      0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
      0xff, 0xff, 0xff, 0xff, 0xff,

       0x0,
      0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
      0xff, 0xff, 0xff, 0xff, 0xff,

      0x0,
      0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
      0xff, 0xff, 0xff, 0xff, 0xff,

      0x0,
      0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
      0xff, 0xff, 0xff, 0xff, 0xff,


      0x0,
      0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
      0xff, 0xff, 0xff, 0xff, 0xff,

      0x0,
      0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
      0xff, 0xff, 0xff, 0xff, 0xff,

      0x0,
      0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
      0xff, 0xff, 0xff, 0xff, 0xff,

      0x0,
      0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
      0xff, 0xff, 0xff, 0xff, 0xff,

      0x0,
      0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
      0xff, 0xff, 0xff, 0xff, 0xff,

      0x0,
      0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
      0xff, 0xff, 0xff, 0xff, 0xff
};

Int8 defIciUsimData_31121[] = {
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x00,                                                             /*Incoming call status */
        0x01, 0xff, 0xff,                                                 /*link to phonebook entry*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x00,                                                             /*Incoming call status */
        0x01, 0xff, 0xff,                                                 /*link to phonebook entry*/

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,   /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x00,                                                             /*Incoming call status */
        0x01, 0xff, 0xff,                                                 /*link to phonebook entry*/

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,   /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x00,                                                             /*Incoming call status */
        0x01, 0xff, 0xff,                                                 /*link to phonebook entry*/

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x00,                                                             /*Incoming call status */
        0x01, 0xff, 0xff,                                                 /*link to phonebook entry*/

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */        
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x00,                                                             /*Incoming call status */
        0x01, 0xff, 0xff,                                                 /*link to phonebook entry*/

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x00,                                                             /*Incoming call status */
        0x01, 0xff, 0xff                                                  /*link to phonebook entry*/


};

Int8 defOciUsimData_31121[] = {
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x01, 0xff, 0xff,                                                 /*link to phonebook entry*/

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x01, 0xff, 0xff,                                                 /*link to phonebook entry*/

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x01, 0xff, 0xff,                                                 /*link to phonebook entry*/

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x01, 0xff, 0xff,                                                 /*link to phonebook entry*/

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,   /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x01, 0xff, 0xff,                                                 /*link to phonebook entry*/

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x01, 0xff, 0xff,                                                 /*link to phonebook entry*/

};
Int8    defIctUsimData_31121[] = { 0,0,0};
Int8    defOctUsimData_31121[] = { 0,0,0};
Int8 defExt3UsimData_31121[] = {
/*1*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*2*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*3*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*4*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*5*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*6*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*7*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*8*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*9*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*a*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*b*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*c*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff
};

Int8 defExt5UsimData_31121[] = {
/*1*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*2*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*3*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*4*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*5*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*6*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*7*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*8*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*9*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*a*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*b*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*c*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff
};

Int8 defExt6UsimData_31121[] = {
/*1*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*2*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*3*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*4*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*5*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*6*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*7*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*8*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*9*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*a*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

};

Int8 defExt7UsimData_31121[] = {
/*1*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*2*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*3*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*4*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*5*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*6*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*7*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        
/*8*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*9*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*a*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*b*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*c*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff
};



Int8 defCfisUsimData_31121[] = {
/*1*/   0x11, /*MSP number*/
        0x00, /*CFU status*/
        0xff, /*length of BCD number */
        0xff, /*TON and NPI */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, /*
dialling number */
        0xff, /*CCP2*/
        0xff,  /*EXT7 record*/


/*2*/   0x11, /*MSP number*/
        0x00, /*CFU status*/
        0xff, /*length of BCD number */
        0xff, /*TON and NPI */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, /*
dialling number */
        0xff, /*CCP2*/
        0xff,  /*EXT7 record*/


/*2*/   0x11, /*MSP number*/
        0x00, /*CFU status*/
        0xff, /*length of BCD number */
        0xff, /*TON and NPI */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, /*
dialling number */
        0xff, /*CCP2*/
        0xff,  /*EXT7 record*/



/*2*/   0x11, /*MSP number*/
        0x00, /*CFU status*/
        0xff, /*length of BCD number */
        0xff, /*TON and NPI */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, /*
dialling number */
        0xff, /*CCP2*/
        0xff  /*EXT7 record*/

};

Int8    defExt2UsimData_31121[] = {

/*1*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*2*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*3*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*4*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*5*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*6*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*7*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*8*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*9*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*a*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*b*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

};
Int8    defKcGprsUsimData_31121[] = { 0xff,0xff, 0xff,0xff, 0xff, 0xff, 0xff, 0xff, /*ciphering key KcGPRS*/
                                0x07 };                    /*ciphering key sequence number n for GPRS*/

Int8    defHplmnSelUsimData_31121[] = { 0xff, 0xff, 0xff, 0x00, 0x00,
                                  0xff, 0xff, 0xff, 0x00, 0x00,
                                  0xff, 0xff, 0xff, 0x00, 0x00,
                                  0xff, 0xff, 0xff, 0x00, 0x00,
                                  0xff, 0xff, 0xff, 0x00, 0x00,
                                  0xff, 0xff, 0xff, 0x00, 0x00,
                                  0xff, 0xff, 0xff, 0x00, 0x00,
                                  0xff, 0xff, 0xff, 0x00, 0x00 };

Int8    defStartHfnUsimData_31121[] = { 0xf0, 0x00, 0x00,   /*START CS*/
                                  0xf0, 0x00, 0x00 }; /*START PS*/
Int8    defAclUsimData_31121[] = {
  0x00,                                                       /*number of APNs*/
  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff , 0xff, 0xff, 0xff, 0xff, 
0xff, 0xff
};

Int8    defCpbcchUsimData_31121[]={0xff,0xff,0xff,0xff};
SimEfData    defCpbcchUsimEfData_31121 =
{
    SIM_EUM_EF_CPBCCH,                 /* Id           */
    SIM_EFSTRUCT_T,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_CPBCCH_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

Int8    defInvscanUsimData_31121[]={0x00};
SimEfData    defInvscanUsimEfData_31121 =
{
    SIM_EMU_EF_INVSCAN,                 /* Id           */
    SIM_EFSTRUCT_T,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_INVSCAN_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_ADM,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

Int8    defSumeUsimData_31121[]={0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xff, 0xff};
SimEfData    defSumeUsimEfData_31121 =
{
    SIM_EMU_EF_SUME,                 /* Id           */
    SIM_EFSTRUCT_T,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_SUME_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_ADM,      /* update*/
     USIM_ACCESS_ADM       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

Int8    defCmiUsimData_31121[]={0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                               0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,0xff};
SimEfData    defCmiUsimEfData_31121 =
{
    SIM_EMU_EF_CMI,                 /* Id           */
    SIM_EFSTRUCT_LF,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_CMI_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_ADM,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

Int8    defRplmnUsimData_31121[]={0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
0x00, 0x00, 0x00,
                               0x00, 0x00, 0x00, 0x00, 0x00};
SimEfData    defRplmnUsimEfData_31121 =
{
    SIM_EMU_EF_RPLMN,                 /* Id           */
    SIM_EFSTRUCT_T,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_RPLMN_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};


Int8    defMbiUsimData_31121[]={0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};
SimEfData    defMbiUsimEfData_31121 =
{
    SIM_EMU_EF_MBI,                 /* Id           */
    SIM_EFSTRUCT_LF,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    2,  /* Num rec      */
    SIM_EMU_SIZE_USIM_MBI_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

Int8    defMmsnUsimData_31121[]={0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};
SimEfData    defMmsnUsimEfData_31121 =
{
    SIM_EMU_EF_MMSN,                 /* Id           */
    SIM_EFSTRUCT_LF,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_MMSN_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

Int8    defExt8UsimData_31121[]={0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xff, 0xff};
SimEfData    defExt8UsimEfData_31121 =
{
    SIM_EMU_EF_EXT8,                 /* Id           */
    SIM_EFSTRUCT_LF,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_EXT8_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

Int8    defMmsicpUsimData_31121[]={0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xff, 0xff};
SimEfData    defMmsicpUsimEfData_31121 =
{
    SIM_EMU_EF_MMSICP,                 /* Id           */
    SIM_EFSTRUCT_T,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_MMSICP_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};


Int8    defMmsupUsimData_31121[]={0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xff, 0xff};
SimEfData    defMmsupUsimEfData_31121 =
{
    SIM_EMU_EF_MMSUP,                 /* Id           */
    SIM_EFSTRUCT_LF,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_MMSUP_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};


Int8    defMmsucpUsimData_31121[]={0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xff, 0xff};
SimEfData    defMmsucpUsimEfData_31121 =
{
    SIM_EMU_EF_MMSUCP,                 /* Id           */
    SIM_EFSTRUCT_T,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_MMSUCP_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};
SimMfDfData    defMfData_31121 =
{
    SIM_DIR_MF,
    SIM_DIR_INVALID,
    (2048),                                     /*total memory available*/
    SIM_FILETYPE_MF,                            /*type of file*/
    SIM_UICC_ACTIVATED_STATE,                   /*file State*/
    SIM_CLKSTOP_NOT_ALLOWED,                    /*clock mode*/
    SIM_MIN_AUTH_FREQ_13OVER8,                  /*min auth freq*/
    SIM_1_8_OR_3_VOLTS_CAPAB,                   /*SIM voltage capability*/
    {                                           /*Access data*/
       USIM_ACCESS_NEVER,   /* delete self */
       USIM_ACCESS_NEVER,   /* terminate */
       USIM_ACCESS_NEVER,   /* activate */
       USIM_ACCESS_NEVER,   /* deactivate */
       USIM_ACCESS_NEVER,   /* createEf */
       USIM_ACCESS_NEVER,   /* createDf */
       USIM_ACCESS_NEVER    /* deleteChild */
    },
    0,
    0,
    USIM_ACCESS_PIN1_APP1,                      /*Global application Pin*/
    USIM_ACCESS_PIN2_APP1,                      /*local PIN */
    {                                           /*local PIN status*/
        3,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x33, 0x35, 0x37, 0x39, 0xff, 0xff, 0xff,0xff}   /*local PIN value */

    },
    {                                           /*unblock PIN 2 status*/
        10,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x30, 0x38, 0x39, 0x37, 0x38, 0x36, 0x37, 0x35}  /*unblock PIN 2 
value*/
     }

};
SimMfDfData    defPhoneBookUsimDfData_31121 =
{
    SIM_DIR_DF_PHONEBOOK,
    SIM_DIR_ADF_USIM,
    (2048),
    SIM_FILETYPE_DF,
    SIM_UICC_ACTIVATED_STATE,                   /*file State*/
    SIM_CLKSTOP_NOT_ALLOWED,                    /*clock mode*/
    SIM_MIN_AUTH_FREQ_13OVER8,                  /*min auth freq*/
    SIM_1_8_OR_3_VOLTS_CAPAB,                   /*SIM voltage capability*/
    {                                           /*Access data*/
       USIM_ACCESS_NEVER,   /* delete self */
       USIM_ACCESS_NEVER,   /* terminate */
       USIM_ACCESS_NEVER,   /* activate */
       USIM_ACCESS_NEVER,   /* deactivate */
       USIM_ACCESS_NEVER,   /* createEf */
       USIM_ACCESS_NEVER,   /* createDf */
       USIM_ACCESS_NEVER    /* deleteChild */
    },
    0,
    0,
    USIM_ACCESS_PIN1_APP1,                      /*Global application Pin*/
    USIM_ACCESS_PIN2_APP1,                      /*local PIN */
    {                                           /*local PIN status*/
        3,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
         {0x33, 0x35, 0x37, 0x39, 0xff, 0xff, 0xff,0xff}   /*local PIN value */
    },
    {                                           /*unblock PIN 2 status*/
        10,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x30, 0x38, 0x39, 0x37, 0x38, 0x36, 0x37, 0x35}  /*unblock PIN 2 
value*/
    }


};

/*-----------------17/07/2003 16:13-------------------------
 * global phonebook directory (phonebook under DF TELECOM)
 * --------------------------------------------------------*/

SimMfDfData    defPhoneBookDfData_31121 =
{
    SIM_DIR_DF_PHONEBOOK,
    SIM_DIR_DF_TELECOM,
    (2048),
    SIM_FILETYPE_DF,
    SIM_UICC_ACTIVATED_STATE,                   /*file State*/
    SIM_CLKSTOP_NOT_ALLOWED,                    /*clock mode*/
    SIM_MIN_AUTH_FREQ_13OVER8,                  /*min auth freq*/
    SIM_1_8_OR_3_VOLTS_CAPAB,                   /*SIM voltage capability*/
    {                                           /*Access data*/
       USIM_ACCESS_NEVER,   /* delete self */
       USIM_ACCESS_NEVER,   /* terminate */
       USIM_ACCESS_NEVER,   /* activate */
       USIM_ACCESS_NEVER,   /* deactivate */
       USIM_ACCESS_NEVER,   /* createEf */
       USIM_ACCESS_NEVER,   /* createDf */
       USIM_ACCESS_NEVER    /* deleteChild */
    },
    0,
    0,
    USIM_ACCESS_PIN1_APP1,                      /*Global application Pin*/
    USIM_ACCESS_PIN2_APP1,                      /*local PIN */
    {                                           /*local PIN status*/
        3,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x33, 0x35, 0x37, 0x39, 0xff, 0xff, 0xff,0xff}   /*local PIN value */
    },
    {                                           /*unblock PIN 2 status*/
        10,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x30, 0x38, 0x39, 0x37, 0x38, 0x36, 0x37, 0x35}  /*unblock PIN 2 value*/
    }


};




/*-----------------17/07/2003 16:13-----------------
 * GSM ACCESS directory under ADF USIM Directory
 * --------------------------------------------------*/
SimMfDfData    defGsmAccessUsimDfData_31121 =
{
    SIM_DIR_DF_GSM_ACCESS,
    SIM_DIR_ADF_USIM,
    (2048),
    SIM_FILETYPE_DF,
    SIM_UICC_ACTIVATED_STATE,                   /*file State*/
    SIM_CLKSTOP_NOT_ALLOWED,                    /*clock mode*/
    SIM_MIN_AUTH_FREQ_13OVER8,                  /*min auth freq*/
    SIM_1_8_OR_3_VOLTS_CAPAB,                   /*SIM voltage capability*/
    {                                           /*Access data*/
       USIM_ACCESS_NEVER,   /* delete self */
       USIM_ACCESS_NEVER,   /* terminate */
       USIM_ACCESS_NEVER,   /* activate */
       USIM_ACCESS_NEVER,   /* deactivate */
       USIM_ACCESS_NEVER,   /* createEf */
       USIM_ACCESS_NEVER,   /* createDf */
       USIM_ACCESS_NEVER    /* deleteChild */
    },
    0,
    0,
    USIM_ACCESS_PIN1_APP1,                      /*Global application Pin*/
    USIM_ACCESS_PIN2_APP1,                      /*local PIN */
    {                                           /*local PIN status*/
        3,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x33, 0x35, 0x37, 0x39, 0xff, 0xff, 0xff,0xff}  /*local PIN value */
    },
    {                                           /*unblock PIN 2 status*/
        10,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x30, 0x38, 0x39, 0x37, 0x38, 0x36, 0x37, 0x35}  /*unblock PIN 2 value*/
    }


};


SimMfDfData    defTelecomDfData_31121 =
{
    SIM_DIR_DF_TELECOM,
    SIM_DIR_MF,
    (2048),
    SIM_FILETYPE_DF,
    SIM_UICC_ACTIVATED_STATE,                   /*file State*/
    SIM_CLKSTOP_NOT_ALLOWED,                    /*clock mode*/
    SIM_MIN_AUTH_FREQ_13OVER8,                  /*min auth freq*/
    SIM_1_8_OR_3_VOLTS_CAPAB,                   /*SIM voltage capability*/
    {                                           /*Access data*/
       USIM_ACCESS_NEVER,                       /* delete self */
       USIM_ACCESS_NEVER,                       /* terminate */
       USIM_ACCESS_NEVER,                       /* activate */
       USIM_ACCESS_NEVER,                       /* deactivate */
       USIM_ACCESS_NEVER,                       /* createEf */
       USIM_ACCESS_NEVER,                       /* createDf */
       USIM_ACCESS_NEVER                        /* deleteChild */
    },
    0,
    0,
    USIM_ACCESS_PIN1_APP1,                      /*Global application Pin*/
    USIM_ACCESS_PIN2_APP1,                      /*local PIN */
    {                                           /*local PIN status*/
        3,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x33, 0x35, 0x37, 0x39, 0xff, 0xff, 0xff,0xff}   /*local PIN value */
    },
    {                                           /*unblock PIN 2 status*/
        10,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x30, 0x38, 0x39, 0x37, 0x38, 0x36, 0x37, 0x35}  /*unblock PIN 2 value*/
    }


};


SimMfDfData    defGraphicsDfData_31121 =
{
    SIM_DIR_DF_GRAPHICS,
    SIM_DIR_DF_TELECOM,
    (2048),
    SIM_FILETYPE_DF,
    SIM_UICC_ACTIVATED_STATE,                   /*file State*/
    SIM_CLKSTOP_NOT_ALLOWED,                    /*clock mode*/
    SIM_MIN_AUTH_FREQ_13OVER8,                  /*min auth freq*/
    SIM_1_8_OR_3_VOLTS_CAPAB,                   /*SIM voltage capability*/
    {                                           /*Access data*/
       USIM_ACCESS_NEVER,                       /* delete self */
       USIM_ACCESS_NEVER,                       /* terminate */
       USIM_ACCESS_NEVER,                       /* activate */
       USIM_ACCESS_NEVER,                       /* deactivate */
       USIM_ACCESS_NEVER,                       /* createEf */
       USIM_ACCESS_NEVER,                       /* createDf */
       USIM_ACCESS_NEVER                        /* deleteChild */
    },
    0,
    0,
    USIM_ACCESS_PIN1_APP1,                      /*Global application Pin*/
    USIM_ACCESS_PIN2_APP1,                      /*local PIN */
    {                                           /*local PIN status*/
        3,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x33, 0x35, 0x37, 0x39, 0xff, 0xff, 0xff,0xff}   /*local PIN value */
    },
    {                                           /*unblock PIN 2 status*/
        10,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x30, 0x38, 0x39, 0x37, 0x38, 0x36, 0x37, 0x35} /*unblock PIN 2 value*/
    }


};

/*-----------------17/07/2003 16:14-----------------
 * ADF USIM directory
 * --------------------------------------------------*/

SimMfDfData    defAdfUsimData_31121 =
{
    SIM_DIR_ADF_USIM,
    SIM_DIR_MF,
    (2048),
    SIM_FILETYPE_ADF,
    SIM_UICC_ACTIVATED_STATE,                   /*file State*/
    SIM_CLKSTOP_NOT_ALLOWED,                    /*clock mode*/
    SIM_MIN_AUTH_FREQ_13OVER8,                  /*min auth freq*/
    SIM_1_8_OR_3_VOLTS_CAPAB,                   /*SIM voltage capability*/
    {                                           /*Access data*/
       USIM_ACCESS_NEVER,                       /* delete self */
       USIM_ACCESS_NEVER,                       /* terminate */
       USIM_ACCESS_NEVER,                       /* activate */
       USIM_ACCESS_NEVER,                       /* deactivate */
       USIM_ACCESS_NEVER,                       /* createEf */
       USIM_ACCESS_NEVER,                       /* createDf */
       USIM_ACCESS_NEVER                        /* deleteChild */
    },
    0,
    0,
    USIM_ACCESS_PIN1_APP1,                      /*Global application Pin*/
    USIM_ACCESS_PIN2_APP1,                      /*local PIN */
    {                                           /*local PIN status*/
        3,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x33, 0x35, 0x37, 0x39, 0xff, 0xff, 0xff,0xff}   /*local PIN value */
    },
    {                                           /*unblock PIN 2 status*/
        10,                                      /*number remaining retries*/
        TRUE,                                   /*secret code initialised*/
        TRUE,                                   /*enabled*/
        FALSE,                                  /* Has PIN been verified */
        TRUE,                                   /* usage qualifier specifies whether the PIN should be used or not */
        USIM_ACCESS_NO_PIN                      /* alternative key reference */
    },
    {
        {0x30, 0x38, 0x39, 0x37, 0x38, 0x36, 0x37, 0x35} /*unblock PIN 2 value
*/
    },


    {                                             /* AID: Only used for ADF_USIM */
      {0xa0,0x00,0x00,0x00,0x87,
      /*3G app code */ 0x10, 0x01, /*PIX*/ 0x32, 0xf4, 0xff, 0xff, 0xff, 0xff
, 0xff, 0xff, 0xff},  /* AID  value */
      0x10
    }
    };

#endif
/* Modification for USIM it test. CQ00001116, Zhanghao, 20090703 End   */

/*Modification for CQ00003401, cgLiu,Feb.21 2010, begin*/
/*CQ00133810, cgliu, 2021-11-04, begin*/
Int8    defEhplmnUsimData[]={0xff,0xff,0xff,0xff,0xff,0xff,
                             0xff,0xff,0xff,0xff,0xff,0xff,
                             0xff,0xff,0xff,0xff,0xff,0xff,
                             0xff,0xff,0xff,0xff,0xff,0xff,
                             0xff,0xff,0xff,0xff,0xff,0xff};
/*
                            {0x64,0xf0,0x10,0x64,0xf0,0x20,
                             0x64,0xf0,0x30,0x64,0xf0,0x40,
                             0x64,0xf0,0x50,0x64,0xf0,0x60,
                             0x64,0xf0,0x70,0x64,0xf0,0x80,
                             0x64,0xf0,0x90,0x64,0xf0,0x00};
*/
/*CQ00133810, cgliu, 2021-11-04, end  */



SimEfData    defEhplmnUsimEfData =
{
    SIM_EMU_EF_EHPLMN,                 /* Id           */
    SIM_EFSTRUCT_T,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_EHPLMN_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x1D,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

Int8    defEhplmnpiUsimData[]={0x00};
SimEfData    defEhplmnpiUsimEfData =
{
    SIM_EMU_EF_EHPLMNPI,                 /* Id           */
    SIM_EFSTRUCT_T,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_EHPLMNPI_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

/*Modification for CQ00003401, cgLiu,Feb.21 2010, end  */

#endif
/*Modfication by zhjzhao for CQ00013425 Merge from LTE_PS_DEV to NeZha_DEV_Daily 10-09-11, End*/
/*Modification of SIM emulator for CQ00004507, cgLiu,Jun.17 2010, begin*/
#if defined (UPGRADE_LTE)
Int8    defEpsncpipUsimData[]={ /*Record 1*/
                                0x53,0x12,0x21,0x7F,0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0a,0x0b,0x0c,0x0d,0x0e,0x0f,/*data destination address range*/
                                0x50,0x10,0x31,0x31,0x31,0x31,0x31,0x31,0x31,0x31,0x31,0x31,0x31,0x31,0x31,0x31,0x31,0x31,         /*access point name*/    
                                0x51,0x10,0x30,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39,0x30,0x31,0x32,0x33,0x34,0x35,          /*Login*/
                                0x52,0x10,0x30,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39,0x30,0x31,0x32,0x33,0x34,0x35,          /*password*/
                                0x54,0x14,0x30,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39,0x30,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39,          /*beardesc*/

                                /*Record 2*/
                                0x53,0x12,0x21,0x7F,0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0a,0x0b,0x0c,0x0d,0x0e,0x0f,/*data destination address range*/
                                0x50,0x10,0x32,0x32,0x32,0x32,0x32,0x32,0x32,0x32,0x32,0x32,0x32,0x32,0x32,0x32,0x32,0x32,        /*access point name*/    
                                0x51,0x10,0x30,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39,0x30,0x31,0x32,0x33,0x34,0x35,         /*Login*/
                                0x52,0x10,0x30,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39,0x30,0x31,0x32,0x33,0x34,0x35,         /*password*/
                                0x54,0x14,0x30,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39,0x30,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39,         /*beardesc*/

                                /*Record 3*/
                                0x53,0x12,0x21,0x7F,0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0a,0x0b,0x0c,0x0d,0x0e,0x0f,/*data destination address range*/
                                0x50,0x10,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,        /*access point name*/    
                                0x51,0x10,0x30,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39,0x30,0x31,0x32,0x33,0x34,0x35,         /*Login*/
                                0x52,0x10,0x30,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39,0x30,0x31,0x32,0x33,0x34,0x35,         /*password*/
                                0x54,0x14,0x30,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39,0x30,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39,         /*beardesc*/

                                /*Record 4*/
                                0x53,0x12,0x21,0x7F,0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0a,0x0b,0x0c,0x0d,0x0e,0x0f,/*data destination address range*/
                                0x50,0x10,0x34,0x34,0x34,0x34,0x34,0x34,0x34,0x34,0x34,0x34,0x34,0x34,0x34,0x34,0x34,0x34,        /*access point name*/    
                                0x51,0x10,0x30,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39,0x30,0x31,0x32,0x33,0x34,0x35,         /*Login*/
                                0x52,0x10,0x30,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39,0x30,0x31,0x32,0x33,0x34,0x35,         /*password*/
                                0x54,0x14,0x30,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39,0x30,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39,         /*beardesc*/

                                /*Record 5*/
                                0x53,0x12,0x21,0x7F,0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0a,0x0b,0x0c,0x0d,0x0e,0x0f,/*data destination address range*/
                                0x50,0x10,0x35,0x35,0x35,0x35,0x35,0x35,0x35,0x35,0x35,0x35,0x35,0x35,0x35,0x35,0x35,0x35,        /*access point name*/    
                                0x51,0x10,0x30,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39,0x30,0x31,0x32,0x33,0x34,0x35,         /*Login*/
                                0x52,0x10,0x30,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39,0x30,0x31,0x32,0x33,0x34,0x35,         /*password*/
                                0x54,0x14,0x30,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39,0x30,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39          /*beardesc*/

                               };
SimEfData    defEpsncpipUsimEfData =
{
    SIM_EMU_EF_NCPIP,                 /* Id           */
    SIM_EFSTRUCT_LF,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    SIM_EMU_SIZE_NCPIP_RECORD_NUMS,  /* Num rec      */
    SIM_EMU_SIZE_NCPIP_RECORD_LENGTH    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_ADM,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

//#if defined (UPGRADE_DUAL_LINK)
/*Modification for DL branch, Begin*/
Int8    defEpslociUsimData[]={0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff, /*guti*/
                             0x00,0xf1,0x10,0x00,0x00,/*tai*/
                             0x00 /*Eps update STATUS*/};
/* mod for TTCN L+L B1 ********** by chenwenhao 2018 04 27 begin */
Int8    defEpslociUsimData1[]={0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff, /*guti*/
                             0x00,0xf1,0x20,0x00,0x00,/*tai*/
                             0x00 /*Eps update STATUS*/};
/* mod for TTCN L+L B1 ********** by chenwenhao 2018 04 27 end */
/*Modification for DL branch, End*/
#if 0
//#else
Int8    defEpslociUsimData[]={0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff, /*guti*/
                             0x11,0xf1,0x22,0x00,0x00,/*tai*/
                             0x01 /*Eps update STATUS*/};

//#endif
#endif 
SimEfData    defEpslociUsimEfData =
{
    SIM_EMU_EF_EPSLOCI,                 /* Id           */
    SIM_EFSTRUCT_T,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_EPSLOCI_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

Int8    defEpsnscUsimData[]={0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
                             0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
                             0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
                             0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
                             0xff,0xff,0xff,0xff,0xff,0xff};
SimEfData    defEpsnscUsimEfData =
{
    SIM_EMU_EF_EPSNSC,                 /* Id           */
    SIM_EFSTRUCT_LF,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    SIM_EMU_SIZE_EPSNSC_RECORD_NUMS,  /* Num rec      */
    SIM_EMU_SIZE_EPSNSC_RECORD_LENGTH    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};
/*Modification of SIM emulator for CQ00004507, cgLiu,Jun.17 2010, end  */
/*Modfication by zhjzhao for CQ00013425 Merge from LTE_PS_DEV to NeZha_DEV_Daily 10-09-11, End*/
/*Added by zhjzhao for CQ00013463 Add a set of default USIM files value for LTE simulation on PC 10-10-11, Begin*/
#ifdef ON_PC
/*Contents of the EFs  under the MF level*/
/*EFdir 2F00*/
/*EFpl  2F05*/
/*EFarr 2F06*/
/*EFiccid 2FE2*/

/*Contents of files at the USIM ADF(Application DF) Level*/
/*EFli 6F05*/

/*EFarr 6F06*/

/*EFimsi  6F07 MNC+MCC+MSIN*/
Int8    defImsiUsimData_LTE[] = { 0x08,    /*length of IMSI*/
                              0x09, 0x10, 0x10, 0x10, 0x32, 0x54, 0x76, 0x98 };    /*IMSI*/
/* mod for CQ68641 by yunhai 2014.0821 begin */
/*EFimsi  6F07 MNC+MCC+MSIN*/
Int8    defImsiUsimData_LTE_normal[] = { 0x08,    /*length of IMSI*/
                              0x19, 0x10, 0x10, 0x10, 0x32, 0x54, 0x76, 0x98 };    /*IMSI*/
/* mod for CQ68641 by yunhai 2014.0821 end */


/*Modify EFimsi: PLMN 004 31 IMSI 004 31 0111111111*/
Int8    defImsiUsimData_LTE_6_1_1_1[] = { 0x08,                                     /*Length of IMSI*/             
                            0x09,0x40,0x13,0x10,0x11,0x11,0x11,0x11}; /*IMSI*/

/*Modify EFimsi 6f07 : PLMN 001 01 IMSI 001 01 0111111111*/
Int8    defImsiUsimData_LTE_6_1_1_4[] = { 0x08, /*Length of IMSI*/             
                            0x09,0x10,0x10,0x10,0x11,0x11,0x11,0x11 };    /*IMSI*/
/*Modify EFimsi 6f07 : PLMN 003 21  0111111111*/
Int8    defImsiUsimData_LTE_6_1_1_6[] = { 0x08, /*Length of IMSI*/             
                            0x09,0x30,0x12,0x10,0x11,0x11,0x11,0x11}; /*IMSI*/

/* mod for CQ000XXXXX by yunhai 2015.0330 begin */
/*EFimsi  6F07 MNC+MCC+MSIN*    PLMN 460 00 IMSI 460 01 0123456789*/
Int8    defImsiUsimData_LTE_13_7_3_6_1a_NS[] = { 0x08,    /*length of IMSI*/
                              0x49, 0x06, 0x10, 0x10, 0x32, 0x54, 0x76, 0x98 };    /*IMSI*/
/* mod for CQ000XXXXX by yunhai 2015.0330 end */


/*EFkeys 6F08 default in 34108*/
Int8    defKeysUsimData_LTE[] = { 0x07,    /* ( key set identifier refered to as "KSI") */
                                   0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff, /*Ciphering Key CK*/
                                   0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff}; /*Integrity Key IK*/
/*EFkeysps 6F09 default in 34108*/
Int8    defKeysPsUsimData_LTE[] = {0x01,   /* ( key set identifier refered to as "KSI") */
                                    0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff, /*Ciphering Key CK*/
                                    0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff}; /*Integrity Key IK*/
/*EFplmnwact 6F60*/
Int8    defUserPlmnSelUsimData_LTE[] = { 0x32, 0xf4, 0x10, 0xC0, 0xB0,
                                     0x32, 0xf4, 0x20, 0xC0, 0xB0,
                                     0x32, 0xf4, 0x30, 0xC0, 0xB0,
                                     0x32, 0xf4, 0x40, 0xC0, 0xB0,
                                     0x32, 0xf4, 0x50, 0xC0, 0xB0,
                                     0x32, 0xf4, 0x60, 0xC0, 0xB0,
                                     0x32, 0xf4, 0x70, 0xC0, 0xB0,
                                     0x32, 0xf4, 0x80, 0xC0, 0xB0
                                     };

/*Modify EFplmnwact: 6F60*/
Int8    defUserPlmnSelUsimData_LTE_6_1_1_1[]={0x32, 0xf4, 0x10, 0xc0, 0xb0,/*1: PLMN:234 01, ACT: default in 34108  */
                           0x00, 0xf3, 0x12, 0xc0, 0xf0,/*2: PLMN:003 21, ACT: All-specified*/
                           0x00, 0xf2, 0x11, 0x40, 0x00,/*3: PLMN:002 11, ACT: E-UTRAN*/
                           0x32, 0xf4, 0x10, 0xc0, 0xb0,/*4: PLMN:234 01, ACT: default in 34108  */
                           0x32, 0xf4, 0x10, 0xc0, 0xb0,/*5: PLMN:234 01, ACT: default in 34108  */
                           0x32, 0xf4, 0x10, 0xc0, 0xb0,/*6: PLMN:234 01, ACT: default in 34108  */
                           0x32, 0xf4, 0x10, 0xc0, 0xb0,/*7: PLMN:234 01, ACT: default in 34108  */
                           0x32, 0xf4, 0x10, 0xc0, 0xb0 /*8: PLMN:234 01, ACT: default in 34108  */
                           };


/*The first record is PLMN2 = 002 11*/
Int8    defUserPlmnSelUsimData_LTE_6_1_1_2[]={0x00, 0xf2, 0x11, 0x40, 0x00,/*1: PLMN:002 11, ACT: E-UTRAN*/
                           0x32, 0xf4, 0x20, 0xC0, 0xB0,
                           0x32, 0xf4, 0x30, 0xC0, 0xB0,
                           0x32, 0xf4, 0x40, 0xC0, 0xB0,
                           0x32, 0xf4, 0x50, 0xC0, 0xB0,
                           0x32, 0xf4, 0x60, 0xC0, 0xB0,
                           0x32, 0xf4, 0x70, 0xC0, 0xB0,
                           0x32, 0xf4, 0x80, 0xC0, 0xB0};
/*6_1_1_4, empty*/
Int8    defUserPlmnSelUsimData_LTE_6_1_1_4[]={0xff, 0xff, 0xff, 0x00, 0x00,/*1: PLMN:234 01, ACT: E-UTRAN*/
                           0xff, 0xff, 0xff, 0x00, 0x00,
                           0xff, 0xff, 0xff, 0x00, 0x00,
                           0xff, 0xff, 0xff, 0x00, 0x00,
                           0xff, 0xff, 0xff, 0x00, 0x00,
                           0xff, 0xff, 0xff, 0x00, 0x00,
                           0xff, 0xff, 0xff, 0x00, 0x00,
                           0xff, 0xff, 0xff, 0x00, 0x00};
/*Modify EFplmnwact: 6F60 The first PLMN is 001 01*/
Int8    defUserPlmnSelUsimData_LTE_6_1_1_6[]={0x00, 0xf1, 0x10, 0x40, 0x00,/*1: PLMN:001 01, ACT: E-UTRAN  */
                           0x32, 0xf4, 0x20, 0xC0, 0xB0, /*use the default value*/
                           0x32, 0xf4, 0x30, 0xC0, 0xB0,
                           0x32, 0xf4, 0x40, 0xC0, 0xB0,
                           0x32, 0xf4, 0x50, 0xC0, 0xB0,
                           0x32, 0xf4, 0x60, 0xC0, 0xB0,
                           0x32, 0xf4, 0x70, 0xC0, 0xB0,
                           0x32, 0xf4, 0x80, 0xC0, 0xB0};

/*6_2_1_1, empty*/
Int8    defUserPlmnSelUsimData_LTE_6_2_1_1[]={0xff, 0xff, 0xff, 0x00, 0x00,/*1: PLMN:234 01, ACT: E-UTRAN*/
                           0xff, 0xff, 0xff, 0x00, 0x00,
                           0xff, 0xff, 0xff, 0x00, 0x00,
                           0xff, 0xff, 0xff, 0x00, 0x00,
                           0xff, 0xff, 0xff, 0x00, 0x00,
                           0xff, 0xff, 0xff, 0x00, 0x00,
                           0xff, 0xff, 0xff, 0x00, 0x00,
                           0xff, 0xff, 0xff, 0x00, 0x00};
/*Modify EFplmnwact: 6F60 , first plmn 002 81, Second plmn 002 91*/
Int8    defUserPlmnSelUsimData_LTE_6_2_1_2[]={0x00, 0xf2, 0x18, 0x40, 0x00,/*1: PLMN:002 81, ACT: E-UTRAN*/
                           0x00, 0xf2, 0x19, 0x80, 0x00,/*2: PLMN:002 91, ACT: UTRAN*/
                           0xff, 0xff, 0xff, 0x00, 0x00,
                           0xff, 0xff, 0xff, 0x00, 0x00,
                           0xff, 0xff, 0xff, 0x00, 0x00,
                           0xff, 0xff, 0xff, 0x00, 0x00,
                           0xff, 0xff, 0xff, 0x00, 0x00,
                           0xff, 0xff, 0xff, 0x00, 0x00};

/*EFhpplmn  Higher Priority PLMN search period 6F31*/
Int8    defHpplmnUsimData_LTE[] = { 0xff };

Int8    defHpplmnUsimData_LTE_6_1_1_1[] = { 0x01 };

SimEfData    defHpplmnUsimEfData_LTE =
{
    SIM_EMU_EF_HPPLMN,           /* Id           */
    SIM_EFSTRUCT_T,         /* Type         */
    SIM_DIR_ADF_USIM,       /* Dir          */
    1,                      /* Num rec      */
    SIM_EMU_SIZE_USIM_HPLMN_FILE,/* rec length  */
    (0),                    /* curr Rec     */
    (0),
    { USIM_ACCESS_NEVER,    /* delete file*/
      USIM_ACCESS_NEVER,    /* terminate*/
      USIM_ACCESS_ADM,      /* activate */
      USIM_ACCESS_ADM,      /* deactivate */
      USIM_ACCESS_NEVER,    /* write */
      /*Modification for IT test, Chengguang Liu, Feb.16 09,Begin*/
      /*USIM_ACCESS_ADM,*/      /* update*/
      USIM_ACCESS_PIN,
      /*Modification forIT test, Chengguang Liu, Feb.16 09,Begin*/
      USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    TRUE,                     /*sfi supported  */
    0x12,                     /*sfi*/
    TRUE                     /*Under USIM*/
};

/*EFacmmax ACM maximum value 6F37*/
Int8    defAcmMaxUsimData_LTE[] =  { 0x00,0x00,0x00};

/*EFust Usim Service Table 6F38*/
#ifndef NAS_UNIT_TEST
Int8 defUstUsimData_LTE[] = { 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff};
#else
Int8 defUstUsimData_LTE[] = { 0xff,0xff,0xff,0x1c,0xff,0xff,0xff,0xff,0xff,0xff,0xff};
#endif
/*Service 71 not supporte*/
Int8    defUstUsimData_LTE_6_1_1_1[]={0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xBf,0xff,0xff};

/*Update EFust 6f38*/
/*Service 20, 42, 71 are supported*/
Int8    defUstUsimData_LTE_6_1_1_2[]={0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff};

/*EFacm Accumulated Call Meter 6F39*/
Int8    defAcmUsimData_LTE[] =  { 0x00, 0x00, 0x00};
SimEfData    defAcmUsimEfData_LTE =
{
    SIM_EMU_EF_ACM,                 /* Id           */
    SIM_EFSTRUCT_C,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_ACM_FILE, /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};
/*EFgid1 Group Identifier Level 1   6F3E Test house opition set as all 0xff*/
Int8    defGid1UsimData_LTE[] = {
           0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
           0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff  };

/*EFgid2  6F3F*/
Int8    defGid2UsimData_LTE[] = {
           0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
           0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff  };

/*EFspn 6F46*/
//use the default Value

/*EFpuct Price per Unit and Currency table 6F41*/
Int8    defPuctUsimData_LTE[] = { 0xFF, 0xFF, 0xFF, 0x00, 0x00 };

/*EFcbmi Cell Broadcast Message identifier selection 6F45*/
//Test house opition, use the default value
//added 20130822 wang tao for TTCN 14-1
#if defined(PC_TTCN_INT_TEST)  
Int8 defCbmiUsimData_LTE[] = {
      0x00, 0x01,
      0x02, 0x03,
      0x04, 0x05,
      0x06, 0x07,
      0x08, 0x09
};
#else
Int8    defCbmiUsimData_LTE[] = { 0xff, 0xff,       /* CB message identifier 1*/
                              0xff, 0xff,       /* CB message identifier 2*/
                              0xff, 0xff,     /* CB message identifier 3*/
                              0xff, 0xff,     /* CB message identifier 4*/
                              0xff, 0xff };   /* CB message identifier 5*/
#endif
/*EFacc access control Class 6F78*/
Int8     defAccTypeAUsimData_LTE[] = { 0x03, 0x00 }; //TYPE A
Int8     defAccTypeBUsimData_LTE[] = { 0xfb, 0x00 }; //TYPE B
Int8     defAccTypeCUsimData_LTE[] = { 0x8b, 0xFF }; //TYPE C
Int8     defAccTypeDUsimData_LTE[] = { 0x73, 0xFF }; //TYPE D

/*EFfplmn Forbidden PLMNs 6F7B default value in 31102 annex E*/
Int8    defFplmnUsimData_LTE[] = { 0xFF, 0xFF, 0xFF,      /*FPLMN 1.   (MCC = 234 MNC = 15) */
                               0xFF, 0xFF, 0xFF,      /*FPLMN 2 */
                               0xFF, 0xFF, 0xFF,      /*FPLMN 3 */
                               0xFF, 0xFF, 0xFF };    /*FPLMN 4 */
SimEfData    defFplmnUsimEfData_LTE =
{
    SIM_EMU_EF_FPLMN,                   /* Id           */
    SIM_EFSTRUCT_T,                 /* Type         */
    SIM_DIR_ADF_USIM,               /* Dir          */
    1,                              /* Num Rec      */
    SIM_EMU_SIZE_USIM_FPLMN_FILE,   /* Rec length    */
    (0),                            /* curr Rec     */
    (0),
   {
    USIM_ACCESS_NEVER,    /* delete file*/
    USIM_ACCESS_NEVER,    /* terminate*/
    USIM_ACCESS_ADM,      /* activate */
    USIM_ACCESS_ADM,      /* deactivate */
    USIM_ACCESS_NEVER,    /* write */
    USIM_ACCESS_PIN,      /* update*/
    USIM_ACCESS_PIN       /* read*/
  },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    TRUE,                     /*sfi supported  */
    0x0d,                     /*sfi*/
    TRUE                      /*Under USIM*/
};
/*EFloci Location Information 6F7E*/
Int8    defLociUsimData_LTE[] = { 0xff, 0xff, 0xff, 0xff,    /*TMSI*/
                              0x42, 0xF6, 0x18, 0xFF, 0xFE,  /* LAI*/
                              0xff, 0x01  };   /*RFU + location update status*/
/*6.1.1.1*/
/*Modify EFloci: MCC 001;MNC 01; LAC: FFFE; TMSI: FFFFFFFF*/
Int8    defLociUsimData_LTE_6_1_1_1[] = {0xff,0xff,0xff,0xff,  /* TMSI */
                                        0x00,0xf1,0x10,0xff,0xfe,/*LAI: MCC 001;MNC 01; LAC: ffff*/
                                        0xff,                    /*RFU, don't modify it*/
                                        0x00};                   /*Local Update status: updated*/       

/*Modify EFloci: MCC 001;MNC 31; LAC: FFFE; TMSI: FFFFFFFF*/
Int8    defLociUsimData_LTE_6_1_1_4[] = { 0xff, 0xff, 0xff, 0xff,    /*TMSI*/
                                          0x00,0xf1,0x13,0xff,0xfe,/*LAI: MCC 001;MNC 31; LAC: ffff*/
                                          0xff,                    /*RFU, don't modify it*/
                                          0x01};                   /*Local Update status: not updated*/
/*Modify EFloci 6f7e : MCC 001;MNC 01; LAC: FFFE; TMSI: FFFFFFFF*/
Int8    defLociUsimData_LTE_6_1_1_6[] = { 0xff, 0xff, 0xff, 0xff,    /*TMSI*/
                                         0x00,0xf1,0x10,0xff,0xfe,/*LAI: MCC 001;MNC 01; LAC: ffff*/
                                        0xff,                    /*RFU, don't modify it*/
                                        0x01};                   /*Local Update status: not updated*/

/*EFad Administrative Data 6FAD*/
Int8    defAdUsimData_LTE[] =  { 0x80,  /*type approval operations*/
                            0,
                            0,
                            0x02};  /* length of MNC in the IMSI is now mandatory */
/* mod for CQ68641 by yunhai 2014.0821 begin */
/*EFad Administrative Data 6FAD normal*/
Int8    defAdUsimData_LTE_normal[] =  { 0x00,  /*normal operations*/
                            0,
                            0,
                            0x02};  /* length of MNC in the IMSI is now mandatory */
/* mod for CQ68641 by yunhai 2014.0821 end */

/*EFcbmid Cell Broadcast Message Identifier for Data Download 6F48*/
Int8 defCbmidUsimData_LTE[] = {
      0xFF, 0xFF,
      0xFF, 0xFF,
      0xFF, 0xFF,
      0xFF, 0xFF,
      0xff, 0xff
};

SimEfData defCbmidUsimEfData_LTE =
{
  SIM_EMU_EF_CBMID,                  /* Id           */
  SIM_EFSTRUCT_T,                /* Type         */
  SIM_DIR_ADF_USIM,              /* Dir          */
  1,                             /* Num Rec      */
  SIM_EMU_SIZE_USIM_CBMID_FILE,  /* Rec length     */
  (0),                           /* curr Rec     */
  (0),
  {
    USIM_ACCESS_NEVER,           /* delete file*/
    USIM_ACCESS_NEVER,           /* terminate*/
    USIM_ACCESS_ADM,             /* activate */
    USIM_ACCESS_ADM,             /* deactivate */
    USIM_ACCESS_NEVER,           /* write */
    USIM_ACCESS_ADM,              /* update*/
    USIM_ACCESS_PIN               /* read*/
  },
    SIM_UICC_ACTIVATED_STATE,    /*file State*/
    TRUE,                        /*sfi supported  */
    0x0e,                        /*sfi*/
    TRUE                         /*Under USIM*/
};
/*EFecc  Emergence Call Codes 6FB7*/
//test house opition 
Int8 defEccUsimData_LTE[] = { /* call code */ 0x11, 0xf2, 0xff,
                          /* alpha Id */ 'C','O','D','E','1',0xff,
                          /* service category */ 0x01,
                          /* call code */ 0x78, 0x90, 0x12,
                          /* alpha Id */ 'C','O','D','E','2',0xff,
                          /* service category */ 0x02,
                          /* call code */ 0x34, 0x56, 0x78,
                          /* alpha Id */ 'C','O','D','E','3',0xff,
                          /* service category */ 0x03
                           };
SimEfData defEccUsimEfData_LTE =
{
  SIM_EMU_EF_ECC,                 /* Id           */
  SIM_EFSTRUCT_LF,            /* Type         */
  SIM_DIR_ADF_USIM,           /* Dir          */
  SIM_EMU_NUM_USIM_ECC_RECS,  /* Num Recs      */
  SIM_EMU_SIZE_USIM_ECC_FILE, /* Rec length     */
  (0),                        /* curr Rec     */
  (0),
  {   USIM_ACCESS_NEVER,    /* delete file*/
      USIM_ACCESS_NEVER,    /* terminate*/
      USIM_ACCESS_ADM,      /* activate */
      USIM_ACCESS_ADM,      /* deactivate */
      USIM_ACCESS_NEVER,    /* write */
      USIM_ACCESS_ADM,      /* update*/
      USIM_ACCESS_ALWAYS    /* read*/
  },
  SIM_UICC_ACTIVATED_STATE, /*file State*/
  TRUE,                     /*sfi supported  */
  0x01,                     /*sfi*/
  TRUE                      /*Under USIM*/
};

/*EFcbmir Cell Broadcast Message Identifier Range selection  6F50*/
Int8 defCbmirUsimData_LTE[]={
     0xff,0xff,0xff,0xff,
     0xff,0xff,0xff,0xff,
     0xff,0xff,0xff,0xff};
     
/*EFpsloci  Packet Switched location information*/
Int8    defPsLociUsimData_LTE[] = { 0xff, 0xff, 0xff, 0xff,/*P-TMSI*/
                                0xFF, 0xFF, 0xFF, /*P-TMSI signature value*/
                                0x42, 0xf6, 0x18, 0xFF, 0xFE, 0xFF, /*RAI*/
                                0x01/*Routing aera updating status*/
                                };
/*EFfdn Fixed Dialling Number 6F3B*/
// 31102 annex E, all 0xff
Int8    defFdnUsimData_LTE[] =
{
        0xff, 0xff, 0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,
        0xff,


        0xff, 0xff, 0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,
        0xff,

        0xff, 0xff, 0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,
        0xff

};

/*EFsms short messages 6F3C*/
// the same with default

/*EFmsisdn 6F40*/
// TS31102 annex E, all 0xff
Int8    defMsisdnUsimData_LTE[] = {
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,
        0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,                                                          /*CCP */
        0xff,                                                          /*EXT5 */

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,
        0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,                                                          /*CCP */
        0xff,                                                          /*EXT5 */
        
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,
        0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,                                                          /*CCP */
        0xff                                                          /*EXT5 */
};

/*EFsmsp Short message service parameters 6F42*/
//TS31102 annex E, all 0xff
Int8    defSmspUsimData_LTE[] = {
                          0xff, 0xff, 0xff,
                          0xff,
                          0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0xff,
                          0xff,
                          0xff,

                          0xff, 0xff, 0xff,
                          0xff,
                          0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0xff,
                          0xff,
                          0xff,
                          
                          0xff, 0xff, 0xff,
                          0xff,
                          0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0xff,
                          0xff,
                          0xff,
                          
                          0xff, 0xff, 0xff,
                          0xff,
                          0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0xff,
                          0xff,
                          0xff,

                          0xff, 0xff, 0xff,
                          0xff,
                          0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                          0xff,
                          0xff,
                          0xff 
                  };
//EFsmss Short message Service Status
Int8    defSmssUsimData_LTE[] = { 0xff, 0xff };

/*EFsdn Service Dialling Numbers 6F49*/
// use the value of all 0xff, TS 31102 annex E
Int8 defSdnUsimData_LTE[] = {
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,
        0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,
        0xff,

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,
        0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,
        0xff,

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,
        0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,
        0xff,
        
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,
        0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,
        0xff,
        
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,
        0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,
        0xff,
        
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,
        0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,
        0xff,
        
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,
        0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,
        0xff,
        
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,
        0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,
        0xff
 };


/*EFext2, 6F4B*/
Int8    defExt2UsimData_LTE[] = {
/*1*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*2*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*3*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*4*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*5*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*6*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*7*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*8*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*9*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*a*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*b*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff
};
/*EFext3 6F43*/
//the default value see to have the wrong record number
Int8 defExt3UsimData_LTE[] = {
/*1*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*2*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*3*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*4*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*5*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*6*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*7*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*8*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*9*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*a*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff
};

/*EFsmsr Short message status Reports 6f47*/
Int8 defSmsrUsimData_LTE[] = {
        0x0,
      0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
      0xff, 0xff, 0xff, 0xff, 0xff,

       0x0,
      0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
      0xff, 0xff, 0xff, 0xff, 0xff,

      0x0,
      0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
      0xff, 0xff, 0xff, 0xff, 0xff,

      0x0,
      0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
      0xff, 0xff, 0xff, 0xff, 0xff,


      0x0,
      0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
      0xff, 0xff, 0xff, 0xff, 0xff,

      0x0,
      0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
      0xff, 0xff, 0xff, 0xff, 0xff,

      0x0,
      0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
      0xff, 0xff, 0xff, 0xff, 0xff,

      0x0,
      0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
      0xff, 0xff, 0xff, 0xff, 0xff,

      0x0,
      0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
      0xff, 0xff, 0xff, 0xff, 0xff,

      0x0,
      0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
      0xff, 0xff, 0xff, 0xff, 0xff
};
/*EFici Incoming Call Information 6F80*/

Int8 defIciUsimData_LTE[] = {
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x00,                                                             /*Incoming call status */
        0x01, 0xff, 0xff,                                                 /*link to phonebook entry*/

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x00,                                                             /*Incoming call status */
        0x01, 0xff, 0xff,                                                 /*link to phonebook entry*/

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x00,                                                             /*Incoming call status */
        0x01, 0xff, 0xff,                                                 /*link to phonebook entry*/

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x00,                                                             /*Incoming call status */
        0x01, 0xff, 0xff,                                                 /*link to phonebook entry*/

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x00,                                                             /*Incoming call status */
        0x01, 0xff, 0xff,                                                 /*link to phonebook entry*/

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x00,                                                             /*Incoming call status */
        0x01, 0xff, 0xff,                                                 /*link to phonebook entry*/

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x00,                                                             /*Incoming call status */
        0x01, 0xff, 0xff                                                  /*link to phonebook entry*/
};
/*EFoci outcoming call information 6F81*/
Int8 defOciUsimData_LTE[] = {
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x01, 0xff, 0xff,                                                 /*link to phonebook entry*/

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x01, 0xff, 0xff,                                                 /*link to phonebook entry*/

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x01, 0xff, 0xff,                                                 /*link to phonebook entry*/

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x01, 0xff, 0xff,                                                 /*link to phonebook entry*/

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x01, 0xff, 0xff,                                                 /*link to phonebook entry*/

        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,    /*alpha Id*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,                                                             /*length of BCD number*/
        0xff,                                                             /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,       /*Dialling number */
        0xff,                                                             /*CCP */
        0xff,                                                             /*Ext5 */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,                         /*Incoming call date and time*/
        0x00, 0x00, 0x00,                                                 /*Incoming call duration*/
        0x01, 0xff, 0xff                                                 /*link to phonebook entry*/

};
/*EFict incoming call timer 6F82*/
Int8    defIctUsimData_LTE[] = { 0,0,0};
/*EFoct outcoming call timer 6F83*/
Int8    defOctUsimData_LTE[] = { 0,0,0};

/*EFext5 Extension5 6F4E*/
Int8 defExt5UsimData_LTE[] = {
/*1*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*2*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*3*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*4*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*5*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*6*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*7*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*8*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*9*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*a*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*b*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*c*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff
};
/*EFccp2 Capability Confifuration Parameters 2 6F4F*/
Int8    defCcp2UsimData_LTE[] = {
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff 
    };
/*EFemlpp enhanced Multi Level Precdence and Pre-emption 6FB5*/
Int8 defEmlppUsimData_LTE[]={0x1C,0x1C}; // priority 0, 1, 2
/*EFaaem Automatic Answer for eMLPP Service 6FB6*/
Int8 defAaemUsimData_LTE[]={0x00}; //annex E of TS31102
/*EFhiddenkey Key for hidden phonebook entries 6FC3*/
Int8    defHiddenKeyUsimData_LTE[] = { 0xff, 0xff, 0xff, 0xff };  /*annex E 31102*/

/*EFbdn Barred Dialling Numbers*/
/*Annex E TS31102. all 0xff*/
Int8 defBdnUsimData_LTE[] = {
        /*BDN111, +1357924680*/
        0xff, 0xff, 0xff, 0xff, 0xff, 
        0xff,
        0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0xff,
        0xff,

        /*BDN222, +1357924680*/
        0xff, 0xff, 0xff, 0xff, 0xff, 
        0xff,
        0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0xff,
        0xff,

        /*BDN333, +1357924680*/
        0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        0xff,
        0xff      
};



SimEfData defBdnUsimEfData_LTE =
{
  SIM_EMU_EF_BDN,                  /* Id       */
  SIM_EFSTRUCT_LF,             /* Type     */
  SIM_DIR_ADF_USIM,          /* Dir      */
  3,   /* Num recs */
  20,  /* Rec Len  */
  (0),                         /* curr Rec */
  (0),
  {
    USIM_ACCESS_NEVER,    /* delete file*/
    USIM_ACCESS_NEVER,    /* terminate*/
    USIM_ACCESS_ADM,      /* activate */
    USIM_ACCESS_ADM,      /* deactivate */
    USIM_ACCESS_NEVER,    /* write */
    USIM_ACCESS_PIN2,     /* update*/
    USIM_ACCESS_PIN       /* read*/
  },
  SIM_UICC_ACTIVATED_STATE, /*file State*/
  FALSE,                    /*sfi supported  */
  0x00,                     /*sfi*/
  TRUE                      /*Under USIM*/
};

/*EFext4 Extension4 6F55*/
Int8 defExt4UsimData_LTE[] = {
/*1*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*2*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*3*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*4*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*5*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*6*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*7*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*8*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*9*/   0x00,  /*invalid data*/
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*a*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*b*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*c*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff
};

/*EFcmi Comparison Method Information 6F58*/
/*Annex E TS31102. all 0xff*/
Int8    defCmiUsimData_LTE[]={
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff                            
                               };
SimEfData    defCmiUsimEfData_LTE =
{
    SIM_EMU_EF_CMI,                 /* Id           */
    SIM_EFSTRUCT_LF,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    7, /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_ADM,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};
/*EFest Enabled Services Table 6F56*/
Int8 defEstUsimData_LTE[] = { 0x00 }; 

/*EFacl Access Point Name Control List 6F57*/
Int8    defAclUsimData_LTE[] = {
  0x00,                                                       /*number of APNs*/
  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff , 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
};
/*EFdck Depersonalisation Control Keys 6F2C*/
Int8 defDckUsimData_LTE[] = { 
            0xff, 0xff, 0xff, 0xff,
                          0xff, 0xff, 0xff, 0xff,
                          0xff, 0xff, 0xff, 0xff,
                          0xff, 0xff, 0xff, 0xff
                          };
/*EFcnl Co-operatinve Network List 6F32*/
Int8    defCnlUsimData_LTE[] = {   
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                               0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                               0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                               0xff, 0xff, 0xff, 0xff, 0xff, 0xff
                               };
/*EFstart-hfn Initialisation values for HyperFrame number 6F5B*/
Int8    defStartHfnUsimData_LTE[] = { 0xf0, 0x00, 0x00,   /*START CS*/
                                  0xf0, 0x00, 0x00 }; /*START PS*/
/*EFthreshold Maximum value of start 6F5C*/
Int8    defThresholdUsimData_LTE[] = { 0xff, 0xff, 0xff };  /*max value of START PS and START CS*/
/*EFoplmnwact Operator Controlled PLMN selector with Access Technology 6F61*/
Int8    defOperatorPlmnSelUsimData_LTE[] = { 
                    0xff, 0xff, 0xff, 0x00, 0x00,
                                        0xff, 0xff, 0xff, 0x00, 0x00,
                                        0xff, 0xff, 0xff, 0x00, 0x00,
                                        0xff, 0xff, 0xff, 0x00, 0x00,
                                        0xff, 0xff, 0xff, 0x00, 0x00,
                                        0xff, 0xff, 0xff, 0x00, 0x00,
                                        0xff, 0xff, 0xff, 0x00, 0x00,
                                        0xff, 0xff, 0xff, 0x00, 0x00
                                    };

/*Modify EFoplmnwact: 6F61*/
Int8    defOperatorPlmnSelUsimData_LTE_6_1_1_1[]={0x00, 0xf1, 0x10, 0xc0, 0xf0,/*1: PLMN:001 01, ACT: UTRAN*/
                           0xff, 0xff, 0xff, 0x00, 0x00,/*2: Default in 34108*/
                           0xff, 0xff, 0xff, 0x00, 0x00,/*3: Default in 34108*/
                           0xff, 0xff, 0xff, 0x00, 0x00,/*4: Default in 34108*/
                           0xff, 0xff, 0xff, 0x00, 0x00,/*5: Default in 34108*/
                           0xff, 0xff, 0xff, 0x00, 0x00,/*6: Default in 34108*/
                           0xff, 0xff, 0xff, 0x00, 0x00,/*7: Default in 34108*/
                           0xff, 0xff, 0xff, 0x00, 0x00/*8: Default in 34108*/
                           };


/*Modify EFoplmnwact: 6F61, the first PLMN use PLMN3 = 003 21*/
Int8    defOperatorPlmnSelUsimData_LTE_6_1_1_2[]={0x00, 0xf3, 0x12, 0x40, 0x00,/*1: PLMN:003 21, ACT: E-UTRAN*/
                           0xff, 0xff, 0xff, 0x00, 0x00,/*2: Default in 34108*/
                           0xff, 0xff, 0xff, 0x00, 0x00,/*3: Default in 34108*/
                           0xff, 0xff, 0xff, 0x00, 0x00,/*4: Default in 34108*/
                           0xff, 0xff, 0xff, 0x00, 0x00,/*5: Default in 34108*/
                           0xff, 0xff, 0xff, 0x00, 0x00,/*6: Default in 34108*/
                           0xff, 0xff, 0xff, 0x00, 0x00,/*7: Default in 34108*/
                           0xff, 0xff, 0xff, 0x00, 0x00 /*8: Default in 34108*/};
/*Modify EFoplmnwact: 6F61*/
Int8    defOperatorPlmnSelUsimData_LTE_6_1_1_6[]={0x00, 0xf2, 0x11, 0x40, 0xf0,/*1: PLMN:002 11, ACT: E-UTRAN*/
                           0xff, 0xff, 0xff, 0x00, 0x00,/*1: PLMN:004 31, ACT: E-UTRAN*/
                           0xff, 0xff, 0xff, 0x00, 0x00,/*Use the default value*/
                           0xff, 0xff, 0xff, 0x00, 0x00,
                           0xff, 0xff, 0xff, 0x00, 0x00,
                           0xff, 0xff, 0xff, 0x00, 0x00,
                           0xff, 0xff, 0xff, 0x00, 0x00,
                           0xff, 0xff, 0xff, 0x00, 0x00};
/*Modify EFoplmnwact: 6F61, the first and third PLMN use 002 11*/
Int8    defOperatorPlmnSelUsimData_LTE_6_2_1_1[]={0x00, 0xf2, 0x11, 0x40, 0x00,/*1: PLMN:002 11, ACT: E-UTRAN*/
                           0x00, 0xf2, 0x18, 0x00, 0x80,/*2: PLMN:002 81, ACT: GERAN??*/
                           0x00, 0xf2, 0x11, 0x80, 0x00,/*3: PLMN:002 11, ACT: UTRAN*/
                           0x00, 0xf2, 0x18, 0x40, 0x00,/*4: PLMN:002 81, ACT: E-UTRAN*/
                           0xff, 0xff, 0xff, 0x00, 0x00,/*5: Default in 34108*/
                           0xff, 0xff, 0xff, 0x00, 0x00,/*6: Default in 34108*/
                           0xff, 0xff, 0xff, 0x00, 0x00,/*7: Default in 34108*/
                           0xff, 0xff, 0xff, 0x00, 0x00 /*8: Default in 34108*/
                           };

/*Modify EFoplmnwact: 6F61, first plmn: 002 11, second plmn: 002 91, third plmn: 002 81*/
Int8    defOperatorPlmnSelUsimData_LTE_6_2_1_2[]={0x00, 0xf2, 0x11, 0xff, 0xff,/*1: PLMN:002 11, ACT: ALL*/
                           0x00, 0xf2, 0x19, 0x80, 0x00,/*2: PLMN:002 91, ACT: UTRAN*/
                           0x00, 0xf2, 0x18, 0x40, 0x00,/*3: PLMN:002 81, ACT: E-UTRAN*/
                           0xff, 0xff, 0xff, 0x00, 0x00,/*4: Default in 34108*/
                           0xff, 0xff, 0xff, 0x00, 0x00,/*5: Default in 34108*/
                           0xff, 0xff, 0xff, 0x00, 0x00,/*6: Default in 34108*/
                           0xff, 0xff, 0xff, 0x00, 0x00,/*7: Default in 34108*/
                           0xff, 0xff, 0xff, 0x00, 0x00 /*8: Default in 34108*/
                           };

/*Modify EFoplmnwact: 6F61, first plmn: 002 11, second plmn: 002 11, third plmn: 002 91, forth plmn:002 81*/
Int8    defOperatorPlmnSelUsimData_LTE_6_2_1_3[]={0x00, 0xf2, 0x11, 0x40, 0x00,/*1: PLMN:002 11, ACT: E-UTRAN*/
                           0x00, 0xf2, 0x11, 0x80, 0x00,/*2: PLMN:002 11, ACT: UTRAN*/
                           0x00, 0xf2, 0x19, 0x80, 0x00,/*3: PLMN:002 91, ACT: UTRAN*/
                           0x00, 0xf2, 0x18, 0x40, 0x00,/*4: PLMN:002 81, ACT: E-UTRAN*/
                           0xff, 0xff, 0xff, 0x00, 0x00,/*5: Default in 34108*/
                           0xff, 0xff, 0xff, 0x00, 0x00,/*6: Default in 34108*/
                           0xff, 0xff, 0xff, 0x00, 0x00,/*7: Default in 34108*/
                           0xff, 0xff, 0xff, 0x00, 0x00 /*8: Default in 34108*/
                           };
/*Modify EFoplmnwact: 6F61, first plmn: 002 11, second plmn: 002 11, third plmn: 002 11, forth plmn:002 81*/
Int8    defOperatorPlmnSelUsimData_LTE_6_2_1_4[]={0x00, 0xf2, 0x11, 0x40, 0x00,/*1: PLMN:002 11, ACT: E-UTRAN*/
                           0x00, 0xf2, 0x11, 0x00, 0x80,/*2: PLMN:002 11, ACT: GERAN???*/
                           0x00, 0xf2, 0x11, 0x80, 0x00,/*3: PLMN:002 11, ACT: UTRAN*/
                           0x00, 0xf2, 0x18, 0x40, 0x00,/*4: PLMN:002 81, ACT: E-UTRAN*/
                           0xff, 0xff, 0xff, 0x00, 0x00,/*5: Default in 34108*/
                           0xff, 0xff, 0xff, 0x00, 0x00,/*6: Default in 34108*/
                           0xff, 0xff, 0xff, 0x00, 0x00,/*7: Default in 34108*/
                           0xff, 0xff, 0xff, 0x00, 0x00 /*8: Default in 34108*/
                           };


/*EFhplmnwact HPLMN selector with Access Technology 6f62*/
Int8    defHplmnSelUsimData_LTE[] = { 
                    0xff, 0xff, 0xff, 0x00, 0x00,
                                        0xff, 0xff, 0xff, 0x00, 0x00,
                                        0xff, 0xff, 0xff, 0x00, 0x00,
                                        0xff, 0xff, 0xff, 0x00, 0x00,
                                        0xff, 0xff, 0xff, 0x00, 0x00,
                                        0xff, 0xff, 0xff, 0x00, 0x00,
                                        0xff, 0xff, 0xff, 0x00, 0x00,
                                        0xff, 0xff, 0xff, 0x00, 0x00
                                        };
/*Update EFhplmnwact,A: mcc 004; mnc 31*/
Int8    defHplmnSelUsimData_LTE_6_1_1_1[] = { 
                             0x00, 0xf4, 0x13, 0x40, 0x00,/*1: PLMN:004 31, ACT: E-UTRAN*/
                            0xff, 0xff, 0xff, 0x00, 0x00,
                            0xff, 0xff, 0xff, 0x00, 0x00,
                            0xff, 0xff, 0xff, 0x00, 0x00,
                            0xff, 0xff, 0xff, 0x00, 0x00,
                            0xff, 0xff, 0xff, 0x00, 0x00,
                            0xff, 0xff, 0xff, 0x00, 0x00,
                            0xff, 0xff, 0xff, 0x00, 0x00
                                        };

/*Update EFhplmnwact,6f62 : mcc 003; mnc 21*/
Int8    defHplmnSelUsimData_LTE_6_1_1_6[] = { 
                            0x00, 0xf3, 0x12, 0x40, 0x00, /*1: PLMN:003 21, ACT: E-UTRAN*/
                            0xff, 0xff, 0xff, 0x00, 0x00,
                            0xff, 0xff, 0xff, 0x00, 0x00,
                            0xff, 0xff, 0xff, 0x00, 0x00,
                            0xff, 0xff, 0xff, 0x00, 0x00,
                            0xff, 0xff, 0xff, 0x00, 0x00,
                            0xff, 0xff, 0xff, 0x00, 0x00,
                            0xff, 0xff, 0xff, 0x00, 0x00
                                        };

/*EFarr Access Rule Reference 6F06*/
//use the default value

/*EFnetpar Network Parameters 6FC4*/
//use the default value
//Both the EFarr and EFnetpar need more consideration.

/*EFpnn PLMN Network Name 6FC5*/
//use the default value

/*EFopl Operator PLMN List 6FC6*/
//use the default value

/*EFmbdn MailBox Dialling Numbers 6FC7*/
//use the default value

/*EFext6 Extension6 6FC8 */
Int8 defExt6UsimData_LTE[] = {
/*1*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*2*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*3*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*4*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*5*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*6*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*7*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*8*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*9*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*a*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff
};

/*EFmbi Mailbox Identifier 6FC9*/

Int8    defMbiUsimData_LTE[]={0x00, 0x00, 0x00, 0x00, 0x00, 
                            0x00, 0x00, 0x00, 0x00, 0x00};
SimEfData    defMbiUsimEfData_LTE =
{
    SIM_EMU_EF_MBI,                 /* Id           */
    SIM_EFSTRUCT_LF,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    2,  /* Num rec      */
    SIM_EMU_SIZE_USIM_MBI_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};
/*EFmwis Message Waiting Indication Status 6FCA*/
 Int8    defMWISUsimData_LTE[] = { 0x00, 0x00, 0x00, 0x00, 0x00  };
SimEfData    defMWISUsimEfData_LTE =
{
   SIM_EMU_EF_MWIS,            /* Id                  */
   SIM_EFSTRUCT_LF,            /* Type                */
   SIM_DIR_ADF_USIM,           /* Dir                 */
   1, /* Num Recs            */
   SIM_EMU_SIZE_USIM_MWIS_FILE,/* Rec length          */
   (0),                        /* curr Rec            */
   (0),
   {
    USIM_ACCESS_NEVER,         /* delete file         */
    USIM_ACCESS_NEVER,         /* terminate           */
    USIM_ACCESS_ADM,           /* activate            */
    USIM_ACCESS_ADM,           /* deactivate          */
    USIM_ACCESS_NEVER,         /* write               */
    USIM_ACCESS_PIN,           /* update              */
    USIM_ACCESS_PIN            /* read                */
  },
  SIM_UICC_ACTIVATED_STATE,    /* file State          */
  FALSE,                       /* sfi supported       */
  0x00,                        /* sfi                 */
  TRUE                         /* Under USIM          */
};
/*EFcfis Call Forwarding Indication Status 6FCB*/
Int8 defCfisUsimData_LTE[] = {
/*1*/   0x11, /*MSP number*/
        0x00, /*CFU status*/
        0xff, /*length of BCD number */
        0xff, /*TON and NPI */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, /*dialling number */
        0xff, /*CCP2*/
        0xff,  /*EXT7 record*/


/*2*/   0x11, /*MSP number*/
        0x00, /*CFU status*/
        0xff, /*length of BCD number */
        0xff, /*TON and NPI */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, /*dialling number */
        0xff, /*CCP2*/
        0xff,  /*EXT7 record*/


/*2*/   0x11, /*MSP number*/
        0x00, /*CFU status*/
        0xff, /*length of BCD number */
        0xff, /*TON and NPI */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, /*dialling number */
        0xff, /*CCP2*/
        0xff,  /*EXT7 record*/



/*2*/   0x11, /*MSP number*/
        0x00, /*CFU status*/
        0xff, /*length of BCD number */
        0xff, /*TON and NPI */
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, /*dialling number */
        0xff, /*CCP2*/
        0xff  /*EXT7 record*/

};

/*EFext7 Extension7 6FCC*/
Int8 defExt7UsimData_LTE[] = {
/*1*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*2*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*3*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*4*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*5*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*6*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*7*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,
        
/*8*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*9*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*a*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*b*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*c*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff
};

/*EFspdi Service Provider Display Information   6FCD*/
//annex E is empty, use the default value

/*EFmmsn MMS Notification 6FCE*/
Int8    defMmsnUsimData_LTE[]={0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};
SimEfData    defMmsnUsimEfData_LTE=
{
    SIM_EMU_EF_MMSN,                 /* Id           */
    SIM_EFSTRUCT_LF,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_MMSN_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

/*EFext8 Extension8 6FCF*/
Int8    defExt8UsimData_LTE[]={0x00, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xff, 0xff};
SimEfData    defExt8UsimEfData_LTE =
{
    SIM_EMU_EF_EXT8,                 /* Id           */
    SIM_EFSTRUCT_LF,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_EXT8_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

/*EFmmsicp MMS Issuer Connectivity Parameters   6FD0*/
Int8    defMmsicpUsimData_LTE[]={0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xff, 0xff};
SimEfData    defMmsicpUsimEfData_LTE =
{
    SIM_EMU_EF_MMSICP,                 /* Id           */
    SIM_EFSTRUCT_T,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_MMSICP_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

/*EFmmsup MMS user preferences 6FD1*/
Int8    defMmsupUsimData_LTE[]={
0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
    };
SimEfData    defMmsupUsimEfData_LTE =
{
    SIM_EMU_EF_MMSUP,                 /* Id           */
    SIM_EFSTRUCT_LF,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_MMSUP_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};
/*EFmmsucp MMS User Connectivity Parameters 6FD2*/
Int8    defMmsucpUsimData_LTE[]={0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
0xff, 0xff, 0xff};
SimEfData    defMmsucpUsimEfData_LTE =
{
    SIM_EMU_EF_MMSUCP,                 /* Id           */
    SIM_EFSTRUCT_T,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_MMSUCP_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

/*EFnia Network's Indication of Alerting 6FD3*/
Int8    defNiaUsimData_LTE[]={
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
    };
SimEfData    defNiaUsimEfData_LTE =
{
    SIM_EMU_EF_NIA,                 /* Id           */
    SIM_EFSTRUCT_LF,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    SIM_EMU_NUM_USIM_NIA_RECS,  /* Num rec      */
    SIM_EMU_SIZE_USIM_NIA_FILE, /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};


/*EFvgcs Voice Group Call Service 6FB1*/
Int8    defVgcsUsimData_LTE[]={
    0x21, 0xff, 0xff, 0xff, 0x21, 0xf3, 0xff, 0xff,
    0x21, 0x43, 0xff, 0xff, 0x21, 0x43, 0xf8, 0xff,
    0x21, 0x43, 0x19, 0xff, 0x21, 0x53, 0x20, 0xf9,
    0x21, 0x53, 0xf1, 0xff, 0x21, 0x53, 0xf2, 0xff,
    0x21, 0x53, 0xf3, 0xff, 0x21, 0x53, 0xf4, 0xff,
    0x21, 0x53, 0xf5, 0xff, 0x21, 0x53, 0xf6, 0xff,
    0x21, 0x53, 0xf7, 0xff, 0x21, 0x53, 0xf8, 0xff,
    0x21, 0x53, 0xf9, 0xff, 0x02, 0x00, 0xf0, 0xff,
    0x02, 0x00, 0xf1, 0xff, 0x02, 0x00, 0xf2, 0xff, 
    0x02, 0x00, 0xf3, 0xff, 0x02, 0x00, 0xf4, 0xff, 
    0x02, 0x00, 0xf5, 0xff, 0x02, 0x00, 0xf6, 0xff, 
    0x02, 0x00, 0xf7, 0xff, 0x02, 0x00, 0xf8, 0xff, 
    0x02, 0x00, 0xf9, 0xff, 0x02, 0x10, 0xf0, 0xff, 
    0x66, 0x66, 0xf0, 0xff, 0x66, 0x66, 0xf1, 0xff, 
    0x66, 0x66, 0xf2, 0xff, 0x66, 0x66, 0xf3, 0xff, 
    0x66, 0x66, 0xf4, 0xff, 0x66, 0x66, 0xf5, 0xff, 
    0x66, 0x66, 0xf6, 0xff, 0x66, 0x66, 0xf7, 0xff, 
    0x66, 0x66, 0xf8, 0xff, 0x66, 0x66, 0xf9, 0xff, 
    0x66, 0x76, 0xf0, 0xff, 0x08, 0x21, 0xf0, 0xff, 
    0x08, 0x21, 0xf1, 0xff, 0x08, 0x21, 0xf2, 0xff, 
    0x08, 0x21, 0xf3, 0xff, 0x08, 0x21, 0xf4, 0xff, 
    0x08, 0x21, 0xf5, 0xff, 0x08, 0x21, 0xf6, 0xff, 
    0x08, 0x21, 0xf7, 0xff, 0x08, 0x21, 0xf8, 0xff, 
    0x08, 0x21, 0xf9, 0xff, 0x08, 0x31, 0xf0, 0xff, 
    0x99, 0x99, 0xf9, 0xff, 0x11, 0x11, 0x11, 0xf9
    };
SimEfData    defVgcsUsimEfData_LTE =
{
    SIM_EMU_EF_VGCS,                 /* Id           */
    SIM_EFSTRUCT_T,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    SIM_EMU_NUM_USIM_VGCS_RECS,  /* Num rec      */
    SIM_EMU_SIZE_USIM_VGCS_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

/*EFvgcss Voice Group Call Service Status 6FB2*/
Int8    defVgcssUsimData_LTE[]={
    0x09, 0x00, 0x08, 0x20, 0x00, 0x00, 0xfe
    };
SimEfData    defVgcssUsimEfData_LTE =
{
    SIM_EMU_EF_VGCSS,                 /* Id           */
    SIM_EFSTRUCT_T,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    SIM_EMU_NUM_USIM_VGCSS_RECS,  /* Num rec      */
    SIM_EMU_SIZE_USIM_VGCSS_FILE, /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

/*EFvbs Voice Broadcast Service 6FB3*/
Int8    defVbsUsimData_LTE[]={
    0x21, 0xff, 0xff, 0xff, 0x21, 0xf3, 0xff, 0xff,
    0x21, 0x43, 0xff, 0xff, 0x21, 0x43, 0xf8, 0xff,
    0x21, 0x43, 0x19, 0xff, 0x21, 0x53, 0x20, 0xf9,
    0x21, 0x53, 0xf1, 0xff, 0x21, 0x53, 0xf2, 0xff,
    0x21, 0x53, 0xf3, 0xff, 0x21, 0x53, 0xf4, 0xff,
    0x21, 0x53, 0xf5, 0xff, 0x21, 0x53, 0xf6, 0xff,
    0x21, 0x53, 0xf7, 0xff, 0x21, 0x53, 0xf8, 0xff,
    0x21, 0x53, 0xf9, 0xff, 0x02, 0x00, 0xf0, 0xff,
    0x02, 0x00, 0xf1, 0xff, 0x02, 0x00, 0xf2, 0xff, 
    0x02, 0x00, 0xf3, 0xff, 0x02, 0x00, 0xf4, 0xff, 
    0x02, 0x00, 0xf5, 0xff, 0x02, 0x00, 0xf6, 0xff, 
    0x02, 0x00, 0xf7, 0xff, 0x02, 0x00, 0xf8, 0xff, 
    0x02, 0x00, 0xf9, 0xff, 0x02, 0x10, 0xf0, 0xff, 
    0x66, 0x66, 0xf0, 0xff, 0x66, 0x66, 0xf1, 0xff, 
    0x66, 0x66, 0xf2, 0xff, 0x66, 0x66, 0xf3, 0xff, 
    0x66, 0x66, 0xf4, 0xff, 0x66, 0x66, 0xf5, 0xff, 
    0x66, 0x66, 0xf6, 0xff, 0x66, 0x66, 0xf7, 0xff, 
    0x66, 0x66, 0xf8, 0xff, 0x66, 0x66, 0xf9, 0xff, 
    0x66, 0x76, 0xf0, 0xff, 0x08, 0x21, 0xf0, 0xff, 
    0x08, 0x21, 0xf1, 0xff, 0x08, 0x21, 0xf2, 0xff, 
    0x08, 0x21, 0xf3, 0xff, 0x08, 0x21, 0xf4, 0xff, 
    0x08, 0x21, 0xf5, 0xff, 0x08, 0x21, 0xf6, 0xff, 
    0x08, 0x21, 0xf7, 0xff, 0x08, 0x21, 0xf8, 0xff, 
    0x08, 0x21, 0xf9, 0xff, 0x08, 0x31, 0xf0, 0xff, 
    0x99, 0x99, 0xf9, 0xff, 0x11, 0x11, 0x11, 0xf9
    };
SimEfData    defVbsUsimEfData_LTE =
{
    SIM_EMU_EF_VBS,                 /* Id           */
    SIM_EFSTRUCT_T,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    SIM_EMU_NUM_USIM_VBS_RECS,  /* Num rec      */
    SIM_EMU_SIZE_USIM_VBS_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

/*EFvbss Voice Broadcast Service Status 6FB4*/
Int8    defVbssUsimData_LTE[]={
    0x09, 0x00, 0x08, 0x20, 0x00, 0x00, 0xfe
    };
SimEfData    defVbssUsimEfData_LTE =
{
    SIM_EMU_EF_VBSS,                 /* Id           */
    SIM_EFSTRUCT_T,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_VBSS_FILE, /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

/*EFvgcsca Voice Group Call Service Ciphering Algorithm 6FD4*/
Int8    defVgcscaUsimData_LTE[]={
    0x01, 0x03
    };
SimEfData    defVgcscaUsimEfData_LTE =
{
    SIM_EMU_EF_VGCSCA,                 /* Id           */
    SIM_EFSTRUCT_T,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_VGCSCA_FILE, /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

/*EFvbcsca Voice Group Call Service Ciphering Algorithm 6FD5*/
Int8    defVbscaUsimData_LTE[]={
    0x01, 0x03
    };
SimEfData    defVbscaUsimEfData_LTE =
{
    SIM_EMU_EF_VBSCA,                 /* Id           */
    SIM_EFSTRUCT_T,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_VBSCA_FILE, /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};


/*EFgbabp GBA Bootstrapping parameters 6FD6*/
//TS31102 annex E, all 0xff
Int8    defGbabpUsimData_LTE[]={
    0xff, 
    0xff, 0xff, 0xff, 
    0xff, 
    0xff, 0xff, 0xff, 0xff, 
    0xff, 
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff
    };
SimEfData    defGbabpUsimEfData_LTE =
{
    SIM_EMU_EF_GBABP,                 /* Id           */
    SIM_EFSTRUCT_T,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_GBABP_FILE, /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

/*EFmsk MBMS Service Keys List 6FD7*/
//TS31102 Annex E, all 0xff
Int8    defMskUsimData_LTE[]={
    0xff, 0xff, 0xff, 
    0xff, 
    0xff, 0xff, 0xff, 0xff, 
    0xff, 0xff, 0xff, 0xff, 
    0xff, 0xff, 0xff, 0xff, 
    0xff, 0xff, 0xff, 0xff,

    0xff, 0xff, 0xff, 
    0xff, 
    0xff, 0xff, 0xff, 0xff, 
    0xff, 0xff, 0xff, 0xff, 
    0xff, 0xff, 0xff, 0xff, 
    0xff, 0xff, 0xff, 0xff
};
SimEfData    defMskUsimEfData_LTE =
{
    SIM_EMU_EF_MSK,                 /* Id           */
    SIM_EFSTRUCT_LF,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    2,  /* Num rec      */
    SIM_EMU_SIZE_USIM_MSK_FILE, /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

/*EFmuk MBMS User Key 6FD8*/
//TS31102 Annex E, all 0xff, record length is not specific.
Int8    defMukUsimData_LTE[]={
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,

    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
    };
SimEfData    defMukUsimEfData_LTE =
{
    SIM_EMU_EF_MUK,                 /* Id           */
    SIM_EFSTRUCT_LF,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    2,  /* Num rec      */
    SIM_EMU_SIZE_USIM_MUK_FILE, /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

/*EFgbanl GBA NAF List 6FDA*/
//TS31102 Annex E, all 0xff, record length is not specific.
Int8    defGbanlUsimData_LTE[]={
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,

    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
    };
SimEfData    defGbanlUsimEfData_LTE =
{
    SIM_EMU_EF_GBANL,                 /* Id           */
    SIM_EFSTRUCT_LF,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    2,  /* Num rec      */
    SIM_EMU_SIZE_USIM_GBANL_FILE, /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};


/*EFehplmn Equivalent HPLMN 6FD9*/
Int8    defEhplmnUsimData_LTE[]={0xff,0xff,0xff,0xff,0xff,0xff,
                             0xff,0xff,0xff,0xff,0xff,0xff,
                             0xff,0xff,0xff,0xff,0xff,0xff,
                             0xff,0xff,0xff,0xff,0xff,0xff,
                             0xff,0xff,0xff,0xff,0xff,0xff};

/*The first record is PLMN1 = 001 01*/
Int8    defEhplmnUsimData_LTE_6_1_1_2[]={0x00,0xf1,0x10,0xff,0xff,0xff,/*The first PLMN is 001 01*/
                             0xff,0xff,0xff,0xff,0xff,0xff,
                             0xff,0xff,0xff,0xff,0xff,0xff,
                             0xff,0xff,0xff,0xff,0xff,0xff,
                             0xff,0xff,0xff,0xff,0xff,0xff};

SimEfData    defEhplmnUsimEfData_LTE =
{
    SIM_EMU_EF_EHPLMN,                 /* Id           */
    SIM_EFSTRUCT_T,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_EHPLMN_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x1D,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

/*EFehplmnpi Equivalent HPLMN Presentation Indication 6FDB*/
//use the default value

/*EFlrplmnsi Last RPLMN Selection Indication 6FDC*/
Int8    defEhplmnsiUsimData_LTE[]={0x00};
SimEfData    defEhplmnsiUsimEfData_LTE=
{
    SIM_EMU_EF_LRPLMNSI,                 /* Id           */
    SIM_EFSTRUCT_T,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_LRPLMNSI_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

/*EFnafkca NAF key Centre Address 6FDD*/
//TS31102 Annex E, all 0xff, record length is not specific.
Int8    defNafkcaUsimData_LTE[]={
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,

    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
    };
SimEfData    defNafkcaUsimEfData_LTE =
{
    SIM_EMU_EF_NAFKCA,                 /* Id           */
    SIM_EFSTRUCT_LF,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    2,  /* Num rec      */
    SIM_EMU_SIZE_USIM_NAFKCA_FILE, /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

/*EFspni Service Provider Name Icon 6FDE*/
Int8    defSpniUsimData_LTE[]={
    0x00,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
    };
SimEfData    defSpniUsimEfData_LTE=
{
    SIM_EMU_EF_SPNI,                 /* Id           */
    SIM_EFSTRUCT_T,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_SPNI_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

/*EFpnni PLMN Network Name Icon 6FDF*/
Int8    defPnniUsimData_LTE[]={
    0x00,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,

    0x00,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
    };
SimEfData    defPnniUsimEfData_LTE=
{
    SIM_EMU_EF_PNNI,                 /* Id           */
    SIM_EFSTRUCT_LF,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    2,  /* Num rec      */
    SIM_EMU_SIZE_USIM_PNNI_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

/*EFncp-ip Network Connectivity Parameters for USIM IP connections 6FE2*/
/*EFepsloci EPS location Information 6FE3*/
/*EFepsnsc EPS NAS Security Context 6FE4*/
//EFncp-ip, epsloci, epsnsc have been done by Cgliu before.

/*DFs at the USIM ADF(application DF) Level */
/*DFphonebook 5F3A, DFgsm-access 5F3B, DFmexe 5F3C, DFwlan 5F40*/
/*DFhnb 5F50, DFsolsa 5F70, DFbcast 5F80*/

/*Contents of files at the DF SoLSA Level */
/*SoLSA----Support of Localised Service Area Service Number 23*/
/*EFsai solsa Access indicator 4F30*/
Int8    defSaiUsimData_LTE[]={
    0x00,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
    };
SimEfData    defSaiUsimEfData_LTE=
{
    SIM_EMU_EF_SAI,                 /* Id           */
    SIM_EFSTRUCT_T,             /* Type         */
    SIM_DIR_DF_SOLSA,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_SAI_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};
/*EFsll SoLSA LSA List 4F31*/
Int8    defSllUsimData_LTE[]={
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,0xff, 0xff, /*LSA name*/
    0xff, /*Configuration parameters*/  
    0xff, /*RFU*/
    0xff, /*Icon Identifier*/
    0xff, /*Priority*/
    0xff, 0xff, 0xff, /*PLMN code*/
    0xff, 0xff, /*LSA Descriptor File Identifier*/
    0xff,   /*LSA Descriptor Record Identifier*/

    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,0xff, 0xff, /*LSA name*/
    0xff, /*Configuration parameters*/  
    0xff, /*RFU*/
    0xff, /*Icon Identifier*/
    0xff, /*Priority*/
    0xff, 0xff, 0xff, /*PLMN code*/
    0xff, 0xff, /*LSA Descriptor File Identifier*/
    0xff,   /*LSA Descriptor Record Identifier*/

    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,0xff, 0xff, /*LSA name*/
    0xff, /*Configuration parameters*/  
    0xff, /*RFU*/
    0xff, /*Icon Identifier*/
    0xff, /*Priority*/
    0xff, 0xff, 0xff, /*PLMN code*/
    0xff, 0xff, /*LSA Descriptor File Identifier*/
    0xff    /*LSA Descriptor Record Identifier*/
    };
SimEfData    defSllUsimEfData_LTE=
{
    SIM_EMU_EF_SLL,                 /* Id           */
    SIM_EFSTRUCT_T,             /* Type         */
    SIM_DIR_DF_SOLSA,           /* Dir          */
    3,  /* Num rec      */
    SIM_EMU_SIZE_USIM_SLL_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};
/*LSA Descriptor files */
/*There may be several LSA Descriptor files, with four types :*/
/*LSA ID, LAC+CI, CI, LAC*/

/*Contents of files at the DF Phonebook Level*/
/*-----usimemgbpb.h, usimemlocpb.h-----*/


/*Contents of files at the DF gsm-access level*/
/*EFkc Gsm Ciphering Key Kc 4F20*/
Int8    defKcUsimData_LTE[] = { 
0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, /*ciphering key KcGPRS*/
                                0x07 };                    /*ciphering key sequence number n for GPRS*/
/*EFkcgprs GPRS Ciphering Key KcGPRS 4F52*/
Int8    defKcGprsUsimData_LTE[] = { 
0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, /*ciphering key KcGPRS*/
                                0x07 };                    /*ciphering key sequence number n for GPRS*/
/*EFcpbcch CPBCCH Information 4F63*/
/*The old code about the EFcpbcch's DF seems wrong*/
Int8    defCpbcchUsimData_LTE[]={0xff,0xff,0xff,0xff};
SimEfData    defCpbcchUsimEfData_LTE =
{
    SIM_EUM_EF_CPBCCH,                 /* Id           */
    SIM_EFSTRUCT_T,             /* Type         */
    SIM_DIR_DF_GSM_ACCESS,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_CPBCCH_FILE , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};
/*EFinvscan Investigation Scan 4F64*/
Int8    defInvscanUsimData_LTE[]={0x00};
SimEfData    defInvscanUsimEfData_LTE =
{
    SIM_EMU_EF_INVSCAN,                 /* Id           */
    SIM_EFSTRUCT_T,             /* Type         */
    SIM_DIR_DF_GSM_ACCESS,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_INVSCAN_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_ADM,      /* update*/
     USIM_ACCESS_PIN       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};
/*Contents of files at the MExE(Mobile Execute Environment) Level 4F40 */
/*EFmexe-st MExE Service Table 4F40*/
/*EFORPK Operator Root Public Key 4F41*/
/*EFarpk Adminstrator Root Public Key 4F42*/
/*EFtprpk Third Party Root Public Key 4F43 */
/*EFtkcdf Trusted Key/Certificates Data Files 4Fxx*/


/*Contents of files at the DF WLAN level*/
/*EFpseudo Pseudonym 4F41*/
/*EFuplmnwlan User controlled PLMN selector for I-WLAN Access 4F42*/
/*EFoplmnwlan Operator Controlled PLMN selector for I-WLAN Access 4F43*/
/*EFuwsidl User Controlled WLAN Specific Identifier List 4F44*/
/*EFowsidl Operator Controlled WLAN Specific Identifier List 4F45*/
/*EFwri WLAN Reauthentication Identity 4F46*/
/*EFhwsidl Home I-WLAN Specific Identifier List 4F47*/
/*EFwehplmnpi I-WLAN Equivalent HPLMN Resentation Indication 4F48*/
/*EFwhpi I-WLAN HPLMN Priority Indication 4F49*/
/*EFwlrplmn I-WLAN Last Registered PLMN 4F4A*/
/*EFhplmndai HPLMN Direct Access Indicator 4F4B*/
//need to be done later...

/*Contents of files at the DF HNB level */
/*EFacsgl Allowed CSG Lists 4F81*/
/*EFcsgt CSG type 4F82*/
/*EFHNBN Home NodeB Name 4F83*/
/*EFocsgl Operator CSG Lists 4F84*/
/*EFocsgt Operator CSG Type 4F85*/
/*EFohnbn Operator Home NodeB Name 4F86*/
//test house opinion, set as 0xff??

/*Contents of EFs at the TELECOM level*/
/*EFadn Abbrevidated Dialling Numbers */
/*EFext1 Extension1*/
/*EFeccp Extended Capability Configuration Parameters*/
/*EFsume SetUpMenu Elements 6F54 in TS102222*/
Int8    defSumeUsimData_LTE[]={0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};
SimEfData    defSumeUsimEfData_LTE =
{
    SIM_EMU_EF_SUME,                 /* Id           */
    SIM_EFSTRUCT_T,             /* Type         */
    SIM_DIR_ADF_USIM,           /* Dir          */
    1,  /* Num rec      */
    SIM_EMU_SIZE_USIM_SUME_FILE    , /* rec Length   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file*/
     USIM_ACCESS_NEVER,    /* terminate*/
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_ADM,      /* update*/
     USIM_ACCESS_ADM       /* read*/
    },
    SIM_UICC_ACTIVATED_STATE, /*file State*/
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi*/
    TRUE                      /*Under USIM*/
};

/*EFarr Access Rule Reference 6F06*/
/*EFICE_DN In Case of Emergency-Dialling Number 6FE0*/
/*EFICE_FF In Case of Emergency - Free Format 6FE1*/
/*EFrma Remote Management Actions 6F53 in TS1022222*/
/*EFpsismsc Public Service Identity of the SM-SC 6FE5*/


/*DFgraphics 5F50 DFphonebook 5F3A EFmultimedia 5F3B EFmmss 5F3C*/

/*Contents of files at the DFgraphics level*/
/*EFimg Image 4F20*/
Int8  defImgData_LTE[] =
{
 /* record number 1 */
  0x00, /* number of instances */
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, /* record 1, instance 
1 */
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF
};

SimEfData  defImgEfData_LTE =
{
  SIM_EMU_EF_IMG,               /* Id           */
  SIM_EFSTRUCT_LF,              /* Type         */
  SIM_DIR_DF_GRAPHICS,          /* Dir          */
  1,         /* num Recs     */
  SIM_EMU_SIZE_IMG_FILE,        /* Rec Len      */
  (0),                          /* curr Rec     */
  (0),
  {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate   */
     USIM_ACCESS_ADM,      /* activate    */
     USIM_ACCESS_ADM,      /* deactivate  */
     USIM_ACCESS_NEVER,    /* write       */
     USIM_ACCESS_PIN,      /* update      */
     USIM_ACCESS_PIN       /* read        */
  },
    SIM_UICC_ACTIVATED_STATE, /*file State     */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi            */
    FALSE                     /*Under USIM     */
};
/*EFiidf image instance Data Files 4FXX*/
//from 31102 annex E 

/*EFice-graphics In case of Emergency-Graphics */
//need to be done later and later

/*EFmml Multimedia Messages List 4F47*/
/*EFmmdf Multimedia Messages Data File 4F48*/
#endif

#endif /*end UPGRADE_LTE*/

/*Added by zhjzhao for CQ00013463 Add a set of default USIM files value for LTE simulation on PC 10-10-11, End*/

#endif




/* END OF FILE */

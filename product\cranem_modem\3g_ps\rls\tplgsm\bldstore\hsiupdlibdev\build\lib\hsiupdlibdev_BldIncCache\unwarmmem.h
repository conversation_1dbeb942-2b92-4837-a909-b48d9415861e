/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/***************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 ***************************************************************************
 * $Id: //central/main/wsd/sys/gki.mod/pub/src/unwarmmem.h#1 $
 * $Revision: #1 $
 * $DateTime: 2006/10/17 10:20:49 $
 ***************************************************************************
 * File Description: Interface to the memory tracking sub-system.
 **************************************************************************/

#ifndef UNWARMMEM_H
#define UNWARMMEM_H

/***************************************************************************
 * Nested Include Files
 **************************************************************************/

#include <system.h>
#if defined(ENABLE_ARM_STACK_UNWIND)
#include <unwarm.h>

/***************************************************************************
 * Manifest Constants
 **************************************************************************/


/***************************************************************************
 * Type Definitions
 **************************************************************************/


/***************************************************************************
 *  Macros
 **************************************************************************/


/***************************************************************************
 *  Function Prototypes
 **************************************************************************/

Boolean UnwMemHashRead  (MemData * const memData,
                         Int32           addr,
                         Int32   * const data,
                         Boolean * const tracked);

Boolean UnwMemHashWrite (MemData * const memData,
                         Int32           addr,
                         Int32           val,
                         Boolean         valValid);

void    UnwMemHashGC    (UnwState * const state);

#endif /* ENABLE_ARM_STACK_UNWIND */

#endif

/* END OF FILE */

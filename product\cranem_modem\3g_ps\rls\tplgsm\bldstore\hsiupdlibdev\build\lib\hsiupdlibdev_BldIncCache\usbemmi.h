/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/devices/transports/usb.mod/pub/src/usbemmi.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/24 10:16:13 $
 **************************************************************************
 *  File Description :
 *    Data types etc for USB EMMI interface application.
 **************************************************************************/

#if defined (UPGRADE_USB)
#if defined (USB_TTPCOM_EMMI_INTERFACE)

#if !defined (USBEMMI_H)
#define       USBEMMI_H

extern void usbEmmiDevAppInitialise (void);
extern void usbEmmiConfigure (void);
extern void usbEmmiProcessRxData (Int8 *data_p, Int16 bytesReceived);
extern void usbEmmiBlockTransmitComplete (void);
extern void usbEmmiResetApplication (TaskId taskId);
extern Int32 usbEmmiMaxOutTransfer (Int8 logicalEndpoint);


#endif /* USBEMMI_H */
#endif /* USB_TTPCOM_EMMI_INTERFACE */
#endif /* UPGRADE_USB */
/* END OF FILE */

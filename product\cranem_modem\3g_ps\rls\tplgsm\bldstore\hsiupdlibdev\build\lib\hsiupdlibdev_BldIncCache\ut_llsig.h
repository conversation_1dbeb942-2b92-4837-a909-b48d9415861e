/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/tplgsm/utinc/ut_llsig.h#6 $
 *   $Revision: #6 $
 *   $DateTime: 2006/02/09 12:25:17 $
 **************************************************************************
 *  File Description :
 *
 *      Utility for handling Linked List of Signals.
 **************************************************************************/

#ifndef UT_LLSIG_H
#define UT_LLSIG_H

/***************************************************************************
 * Nested Include Files
 **************************************************************************/
 
/***************************************************************************
 * Manifest Constants
 **************************************************************************/

/***************************************************************************
 * Types
 **************************************************************************/

typedef SignalBuffer 
UtSignalList;


/***************************************************************************
 * Typed Constants
 **************************************************************************/

extern const UtSignalList  utNullSignalList;


/***************************************************************************
 * Callback Function Types
 **************************************************************************/

/* Callback function for UtBrowseSignalList() */
typedef Boolean                     /* TRUE continue, FALSE stop */
(*UtBrowseCallback) (void *clientData, const SignalBuffer *sigBuf);

/* Callback function for UtSplitSignalList() */
typedef Boolean                     /* TRUE separate, FALSE keep */
(*UtSplitCallback)  (void *clientData, const SignalBuffer *sigBuf);

/* Callback function for UtFlushSignalList() */
typedef Boolean                     /* TRUE deleted,  FALSE left */
(*UtFlushCallback)  (void *clientData,       SignalBuffer *sigBuf);

/* Callback function for UtAddSignal() */
typedef SignedInt16                 /* = signal1 - signal2 */
(*UtCmpCallback)    (void *clientData, const SignalBuffer *sigBuf1,
                                       const SignalBuffer *sigBuf2);


/***************************************************************************
 * Macros (for stack implementation)
 **************************************************************************/

#define UtSignalStack  UtSignalList
#define UtPushSignal   UtInsertSignal
#define UtPopSignal    UtGetSignal


/***************************************************************************
 * Functions
 **************************************************************************/

/* Adding and getting a signal */
extern void    UtInsertSignal (UtSignalList *sigList, SignalBuffer *sigBuf);
extern void    UtAppendSignal (UtSignalList *sigList, SignalBuffer *sigBuf);
extern Boolean UtAddSignal    (UtSignalList *sigList, SignalBuffer *sigBuf, 
                               UtCmpCallback sigcmp,          void *clientData);
extern Boolean UtGetSignal    (UtSignalList *sigList, SignalBuffer *sigBuf);

/* Iterating trough a list */
extern const SignalBuffer * 
               UtFirstSignal  (UtSignalList *sigList);
extern const SignalBuffer * 
               UtNextSignal   (UtSignalList *sigList, 
                         const SignalBuffer *sigBuf);
extern const SignalBuffer * 
               UtLastSignal   (UtSignalList *sigList);
extern Boolean 
               UtIsLastSignal (UtSignalList *sigList, 
                         const SignalBuffer *sigBuf);

/* Information on a list */
extern Boolean UtBrowseSignalList (UtSignalList *sigList, 
                                   UtBrowseCallback browser,  void *clientData);
extern Boolean UtOnSignalList     (UtSignalList *sigList);

/* Splitting and splicing lists */
extern void    UtInsertSignalList (UtSignalList *sigList1, 
                                   UtSignalList *sigList2);
extern void    UtAppendSignalList (UtSignalList *sigList1, 
                                   UtSignalList *sigList2);
extern Int16   UtSplitSignalList  (UtSignalList *sigList1, 
                                   UtSignalList *sigList2,
                                   UtSplitCallback separator, void *clientData);

/* Flushing a list */
extern Int16   UtFlushSignalList  (UtSignalList *sigList, 
                                   UtFlushCallback destructor,void *clientData);


#endif

/* END OF FILE */

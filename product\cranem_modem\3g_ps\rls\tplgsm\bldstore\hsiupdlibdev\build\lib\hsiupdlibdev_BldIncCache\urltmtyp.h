/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/api/inc/urltmtyp.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2007/03/19 13:59:30 $
 **************************************************************************
 *  File Description :
 *
 * Types and constants required for the URLC TM module
 *
 **************************************************************************/

#if !defined (URLTMTYP_H)
#define       URLTMTYP_H

/***************************************************************************
 * Nested Include Files
 **************************************************************************/

#include <system.h>
#include <ki_typ.h>
#include <urlalloc.h>
#include <urltypes.h>

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/


/***************************************************************************
 * RX Types
 ***************************************************************************/

typedef enum UrlTmRxStateTag
{
    URLTMRX_STATE_NULL      ,
    URLTMRX_STATE_READY     ,
    URLTMRX_STATE_XOFF                          /* Above TMM high water mark */
} UrlTmRxState;

typedef struct UrlTmRxConfigTag
{
    Boolean                         segmentation;
    Boolean                         erroneousSduDelivery;
    Int8                            sduStartOctetOffset;
} UrlTmRxConfig;

/* Declare typedef for singly linked list carrying UrlTmRxSdu */
UT_SINGLE_LINK_LIST_DECLARE(UrlTmRxSduList, SignalBuffer)

typedef struct UrlTmRxEntityTag
{
    BearerIdentity                  bearerId;
    TaskId                          higherTaskId;
    Boolean                         loopback;       /* Configured for loopback */
    UrlTmRxConfig                   config;
    UrlTmRxState                    state;
    UrlTmRxSduList                  sduList;
    UrlLoopbackInfo               * loopbackInfo_p; /* PNULL unless test loop
                                                     * is closed
                                                     */
#if defined (ENABLE_UPLANE_STATISTICS)
    /* Statistic values for downlink channel
     */
    UrlDownlinkStatistics           dlStats;
#endif /* ENABLE_UPLANE_STATISTICS */
} UrlTmRxEntity;


/***************************************************************************
 * TX Types
 ***************************************************************************/

typedef enum UrlTmTxStateTag
{
    URLTMTX_STATE_NULL      ,
    URLTMTX_STATE_READY
} UrlTmTxState;

typedef struct UrlTmTxConfigTag
{
    UTransmissionRLC_Discard        discardMode;
    Boolean                         segmentation;
} UrlTmTxConfig;

typedef struct UrlTmTxSduListEntryTag
{
    Int16                           bitOffset;
    Int16                           sduBits;        /* Size of SDU */
    Int16                           bitLength;      /* Num untransmitted bits */
    KernelTicks                     discardTick;
    UrlcDataUsage                   dataUsage;
    Int8                           *data_p;
    struct UrlTmTxSduListEntryTag  *next_p;
} UrlTmTxSduListEntry;


typedef struct UrlTmTxSduTag
{
    Int16                           bitOffset;
    Int16                           sduBits;        /* Size of SDU */
    Int16                           bitLength;      /* Num untransmitted bits */
    KernelTicks                     discardTick;
    UrlcDataUsage                   dataUsage;
    Int8                           *data_p;
} UrlTmTxSdu;

/* Declare typedef for singly linked list carrying UrlUmTxSdu */
UT_SINGLE_LINK_LIST_DECLARE(UrlTmTxSduList, UrlTmTxSdu)

#if defined UPGRADE_3G_EDCH
typedef struct TmMappingRbTxReqIdTag
{
    Boolean                         IsTrafficIndConsumed;
    UrlTmTxSduList                  txSduList;
} TmMappingRbTxReqId;
#endif /* UPGRADE_3G_EDCH */

typedef struct UrlTmTxEntityTag
{
    BearerIdentity                  bearerId;
    TaskId                          higherTaskId;
    Boolean                         loopback;       /* Configured for loopback */
    UrlTmTxConfig                   config;
    UrlTmTxState                    state;
    UrlBufferInfo                   bufferInfo;
    UrlTmTxSduListEntry            *txSduList_p;
    Int8                           *amrData_p;      /* AMR data is always
                                                     * carried in signal
                                                     */
#if defined (ENABLE_UPLANE_STATISTICS)
    /* Statistic values for uplink channel
     */
    UrlUplinkStatistics             ulStats;
#endif /* ENABLE_UPLANE_STATISTICS */

#if defined UPGRADE_3G_EDCH
    TmMappingRbTxReqId              TmRbTxReqId [MAX_TX_REQ_ID];
#endif /* UPGRADE_3G_EDCH */

} UrlTmTxEntity;

typedef enum UrlTmTxResetCauseTag
{
    URLTMTX_RESET_CONFIGURATION     ,
    URLTMTX_RESET_RELEASE
} UrlTmTxResetCause;

#endif

/* END OF FILE */

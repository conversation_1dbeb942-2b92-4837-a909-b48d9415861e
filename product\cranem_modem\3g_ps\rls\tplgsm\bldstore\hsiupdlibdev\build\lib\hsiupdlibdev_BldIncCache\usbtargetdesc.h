/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/devices/transports/usb.mod/pub/src/usbtargetdesc.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/24 10:16:13 $
 **************************************************************************
 *  File Description :
 *    Device descriptors for the TTPCom Ltd target interfaces.
 **************************************************************************/
#if defined (UPGRADE_USB)

#ifndef USBTARGETDESC_H
#define USBTARGETDESC_H

#if defined (USB_LIBRARY_MODULE)
#error "Include file contains client-configurable build switches"
#endif

/*******************************************************************************
 * Nested Include Files
 ******************************************************************************/
#include <usbdescs.h>
#include <usblangid.h>     /* for language code used in descriptor */
#include <usbvendor.h>     /* for vendor-specific VID, PID's, protocols, etc. */
#include <usbendpoints.h>  /* for number of configurations/interfaces. */

/*******************************************************************************
 * Manifest Constants
 ******************************************************************************/

/* Assigned PID and release code. */
#define USB_BCDDEVICE_TEST_DEVICE  0x0000

/* Indexes for defined strings. */
#define USB_LANG_ID_STRING_INDEX           0
#define USB_MANUFACTURER_STRING_INDEX      1
#define USB_PRODUCT_STRING_INDEX           2
#define USB_SERIAL_NUMBER_STRING_INDEX     3

#define USB_NUMBER_OF_STRINGS    3

/* Determine the value of the configuration descripter bmAttribute from
 * the build file switches. If no build switches are defined, the default
 * bmAttribute is self-powered device and no remote wakeup capability.
 *
 * Note that the determined value of bmAttribute is applied to all
 * configuartion descriptors contained in this file.
 */
#if defined (USB_CONFIG_BUS_POWERED) && defined (USB_CONFIG_REMOTE_WAKEUP)
  /* Bus-powered and remote wakeup supported. */
# define USB_CONFIG_BM_ATTRIBUTE (USB_CONFIG_ATTR_REMOTE_WAKEUP)
#elif defined (USB_CONFIG_REMOTE_WAKEUP)
  /* Self-powered and remote wakeup supported. */
# define USB_CONFIG_BM_ATTRIBUTE (USB_CONFIG_ATTR_SELF_POWERED \
                                  | USB_CONFIG_ATTR_REMOTE_WAKEUP)
#elif defined (USB_CONFIG_BUS_POWERED)
  /* Bus-powered only. */
# define USB_CONFIG_BM_ATTRIBUTE (0x00)
#else
  /* Self-powered only. */
# define USB_CONFIG_BM_ATTRIBUTE (USB_CONFIG_ATTR_SELF_POWERED)
#endif

#if !defined ( USB_CONFIG_BUS_POWERED )
#  undef USB_CONFIG_B_MAX_POWER /* Override the value set in the build file. */
#    define USB_CONFIG_B_MAX_POWER USB_CONFIG_MAX_POWER_ZERO
#else
#  if !defined ( USB_CONFIG_B_MAX_POWER )
#    error "USB_CONFIG_B_MAX_POWER must be defined for a bus-powered device."
#  endif /* !defined ( USB_CONFIG_B_MAX_POWER ) */
#endif /* !defined ( USB_CONFIG_BUS_POWERED ) */

/* Define Union descriptor size */
#define  USB_UNION_FUNCTIONAL_DESCRIPTOR_SIZE   (5)

/* Comm interface - length of extra header */
# if defined(USB_COMMS_DEVICE_INTERFACE)
#define USB_FUNCTIONAL_DESCRIPTOR_TOTAL_LENGTH                    \
      + USB_CS_INTERFACE_FUNCTIONAL_DESCRIPTOR_LENGTH_HDR         \
        + USB_CS_INTERFACE_FUNCTIONAL_DESCRIPTOR_LENGTH_CMGMT  \
        + USB_CS_INTERFACE_FUNCTIONAL_DESCRIPTOR_LENGTH_ABCON  \
        + USB_UNION_FUNCTIONAL_DESCRIPTOR_SIZE
# else
#define USB_FUNCTIONAL_DESCRIPTOR_TOTAL_LENGTH  (0)
#endif

/* Mass Storage: 2 endpoints in 1 interface */
#define USB_MAST_NUM_INTERFACES             1
#define USB_MAST_NUM_INTERFACE_DESCRIPTORS  1
#define USB_MAST_NUM_ENDPOINT_DESCRIPTORS   2

/* EMMI class: 2 endpoints in 1 interface */
#define USB_EMMI_NUM_INTERFACES             1
#define USB_EMMI_NUM_INTERFACE_DESCRIPTORS  1
#define USB_EMMI_NUM_ENDPOINT_DESCRIPTORS   2

/* Modem class: 3 endpoints in 2 interfaces */
#define USB_COMM_NUM_INTERFACES             2
#define USB_COMM_NUM_INTERFACE_DESCRIPTORS  2
#define USB_COMM_NUM_ENDPOINT_DESCRIPTORS   3








      

/***************************************************************************
 * Convenience Macros
 ***************************************************************************/

#define USB_DEVICE_DESCRIPTOR_WITH( vID, cLASS, pID )                 \
    USB_DEVICE_DESCRIPTOR(                                            \
      USB_SPECIFICATION_1POINT1,        /* bcdUSB */                  \
      cLASS,                            /* bDeviceClass */            \
      USB_SUB_DEVICE_CLASS_NONE,        /* bDeviceSubClass */         \
      USB_DEVICE_PROTOCOL_NONE,         /* bDeviceProtocol */         \
      USB_MAX_PACKET_SIZE_EP0,          /* bMaxPacketSize0 */         \
      vID, pID,                         /* idVendor, idProduct */     \
      USB_BCDDEVICE_TEST_DEVICE,        /* bcdDevice */               \
      USB_MANUFACTURER_STRING_INDEX,    /* iManufacturer */           \
      USB_PRODUCT_STRING_INDEX,         /* iProduct */                \
      USB_SERIAL_NUMBER_STRING_INDEX,   /* iSerialNumber */           \
      USB_NUM_CONFIGURATIONS)           /* bNumConfigurations */


/***************************************************************************
 * Configuration
 ***************************************************************************/

#if defined(USB_TTPCOM_TEST_INTERFACE)
/* The USB Test Interface can only be defined on its own. */
# if defined(USB_MASS_STORAGE_BULK_ONLY_INTERFACE)
#   error USB Test Interface defined with USB Mass-Storage Interface
# endif /* defined(USB_MASS_STORAGE_BULK_ONLY_INTERFACE) */
# if defined(USB_COMMS_DEVICE_INTERFACE)
#   error USB Test Interface defined with USB Comms Device Interface
# endif /* defined(USB_COMMS_DEVICE_INTERFACE) */
# if defined(USB_TTPCOM_EMMI_INTERFACE)
#   error USB Test Interface defined with USB EMMI Interface
# endif /* defined(USB_TTPCOM_EMMI_INTERFACE) */
#endif /* defined(USB_TTPCOM_TEST_INTERFACE) */

#if !defined(USB_DYNAMIC_CONFIGURATION)
/* This code is for the single-configuration static model.  One set of
 * descriptors is defined with variation determined by the _INTERFACE
 * build switches.
 */

/* Define the Vendor ID. */
#define USB_VID     USB_VID_TTPCOM_LTD









      


/* The target descriptors. */
static Int8 pDescriptors[] =
{
  USB_DEVICE_DESCRIPTOR_WITH( USB_VID, USB_DEV_CLASS, USB_PID ),

  USB_CONFIGURATION_DESCRIPTOR(
    ( USB_CONFIGURATION_DESCRIPTOR_LENGTH
    + USB_NUM_INTERFACE_DESCRIPTORS * USB_INTERFACE_DESCRIPTOR_LENGTH
    + USB_NUM_ENDPOINT_DESCRIPTORS  * USB_ENDPOINT_DESCRIPTOR_LENGTH
      + USB_FUNCTIONAL_DESCRIPTOR_TOTAL_LENGTH ),   /* wTotalLength */
    USB_NUM_INTERFACES,              /* bNumInterfaces */
    USB_CONFIGURATION_VALUE,         /* bConfigurationValue */
    USB_STRING_NOT_DEFINED_INDEX,    /* iConfiguration */
    USB_CONFIG_BM_ATTRIBUTE,         /* bmAttributes */
    USB_CONFIG_B_MAX_POWER)          /* MaxPower */
    /* NO comma here */

    /* Add interfaces according to what is defined in the build. */

#if defined(USB_TTPCOM_TEST_INTERFACE)
    ,
#include <usbtestdesc.h>
#endif

#if defined(USB_COMMS_DEVICE_INTERFACE)
    ,
#include <usbcommdesc.h>
#endif

#if defined(USB_TTPCOM_EMMI_INTERFACE)
    ,
#include <usbemmidesc.h>
#endif

#if defined(USB_MASS_STORAGE_BULK_ONLY_INTERFACE)
    ,
#include <usbmastdesc.h>
#endif






      
};


/***************************************************************************/

#else /*(USB_DYNAMIC_CONFIGURATION)*/

/***************************************************************************/

/* This code is for the Dynamic USB Device model. Here a descriptor
 * is defined for each of the DUD options.  A change in configuration
 * results in the stack being pointed at a different descriptor set.
 *
 *
 * The target descriptors.
 *
 * We *would* use an array of descriptors but C doesn't allow 2D arrays to
 * be auto-sized, so rather than waste memory or risk overrunning some
 * arbitrary MAX length we use separate descriptor definitions for
 * the separate Device Configurations.
 *
 * NB! Every combination needs a structure - INCLUDING composites.
 */

#if defined(USB_MASS_STORAGE_BULK_ONLY_INTERFACE)

static Int8 pDescriptorsMastOnly[] =      /* USB_DYNAMIC_SELECTION_MAST */
{
    USB_DEVICE_DESCRIPTOR_WITH( USB_VID_TTPCOM_LTD,
                                USB_DEVICE_CLASS_NONE,
                                USB_TTPCOM_PID_STORAGE ),

    USB_CONFIGURATION_DESCRIPTOR(
      (USB_CONFIGURATION_DESCRIPTOR_LENGTH
      + USB_MAST_NUM_INTERFACE_DESCRIPTORS * USB_INTERFACE_DESCRIPTOR_LENGTH
      + USB_MAST_NUM_ENDPOINT_DESCRIPTORS  * USB_ENDPOINT_DESCRIPTOR_LENGTH),
                                       /* wTotalLength */
      USB_MAST_NUM_INTERFACES,         /* bNumInterfaces */
      USB_CONFIGURATION_VALUE,         /* bConfigurationValue */
      USB_STRING_NOT_DEFINED_INDEX,    /* iConfiguration */
      USB_CONFIG_BM_ATTRIBUTE,         /* bmAttributes */
      USB_CONFIG_B_MAX_POWER),         /* MaxPower */

#include <usbmastdesc.h>
};
#endif



#if defined(USB_TTPCOM_EMMI_INTERFACE)

static Int8 pDescriptorsEmmiOnly[] =      /* USB_DYNAMIC_SELECTION_EMMI */
{
    USB_DEVICE_DESCRIPTOR_WITH( USB_VID_TTPCOM_LTD,
                                USB_DEVICE_CLASS_NONE,
                                USB_TTPCOM_PID_TTPCOM ),

    USB_CONFIGURATION_DESCRIPTOR(
      (USB_CONFIGURATION_DESCRIPTOR_LENGTH
      + USB_EMMI_NUM_INTERFACE_DESCRIPTORS * USB_INTERFACE_DESCRIPTOR_LENGTH
      + USB_EMMI_NUM_ENDPOINT_DESCRIPTORS  * USB_ENDPOINT_DESCRIPTOR_LENGTH),
                                       /* wTotalLength */
      USB_EMMI_NUM_INTERFACES,         /* bNumInterfaces */
      USB_CONFIGURATION_VALUE,         /* bConfigurationValue */
      USB_STRING_NOT_DEFINED_INDEX,    /* iConfiguration */
      USB_CONFIG_BM_ATTRIBUTE,         /* bmAttributes */
      USB_CONFIG_B_MAX_POWER),         /* MaxPower */

#include <usbemmidesc.h>
};
#endif



#if defined(USB_COMMS_DEVICE_INTERFACE)

static Int8 pDescriptorsCommOnly[] =     /* USB_DYNAMIC_SELECTION_MODEM */
{
  USB_DEVICE_DESCRIPTOR_WITH( USB_VID_TTPCOM_LTD,
                              USB_DEVICE_CLASS_COMMS,
                              USB_TTPCOM_PID_MODEM ),

  USB_CONFIGURATION_DESCRIPTOR(
    ( USB_CONFIGURATION_DESCRIPTOR_LENGTH
      + USB_COMM_NUM_INTERFACE_DESCRIPTORS * USB_INTERFACE_DESCRIPTOR_LENGTH
      + USB_COMM_NUM_ENDPOINT_DESCRIPTORS  * USB_ENDPOINT_DESCRIPTOR_LENGTH
      + USB_FUNCTIONAL_DESCRIPTOR_TOTAL_LENGTH ),  /* wTotalLength */
    USB_COMM_NUM_INTERFACES,                       /* bNumInterfaces */
    USB_CONFIGURATION_VALUE,                       /* bConfigurationValue */
    USB_STRING_NOT_DEFINED_INDEX,                  /* iConfiguration */
    USB_CONFIG_BM_ATTRIBUTE,                       /* bmAttributes */
    USB_CONFIG_B_MAX_POWER),                       /* MaxPower */

#include <usbcommdesc.h>
};
#endif


























      































                                                  

/* USB_DYNAMIC_CONFIGURATION ends */
#endif
/* USBTARGETDESC_H ends */
#endif
/* UPGRADE_USB ends */
#endif
/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/umaulcm.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/27 15:10:43 $
 **************************************************************************
 * File Description:
 *
 *    Contains the external interface to the system for UMAC
 **************************************************************************/

#if !defined (UMAULCM_H)
#define       UMAULCM_H

/***************************************************************************
* Interface types
***************************************************************************/
typedef struct UmaUlCmUlTgTtiFrameInfoTag
{
    UUL_CompressedModeMethod  compressedModeMethod;
    Int8                      numberOfSlotGaps;
    Int8                      tgpsi;
} UmaUlCmUlTgTtiFrameInfo;

/***************************************************************************
 * Function Prototypes
 ***************************************************************************/
void UmaUlCmExitDch(void);
#if !defined (PC_TTCN_INT_TEST)  //2013.3.25 Jiantao removed according to xueyun's advice for LTE+W+G UE PC VS project
void UmaUlCmHandleCmacCompressedModeConfigReq(
                                CmacCompressedModeConfigReq *signal_p);        
#endif
Boolean UmaUlCmTgInNextTti(UmaUlCmUlTgTtiFrameInfo *ttiInfo_p, Int8 cfn,
                                Int8 longestCfgTti);

void UmaUlCmStartCompressedMode(Int8 cfn, Int8 longestCfgTti);
#endif

/* END OF FILE */

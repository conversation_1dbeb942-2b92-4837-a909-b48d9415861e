/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/3g.mod/lib/src/upde_isc.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/26 20:17:00 $
 **************************************************************************
 * File Description:
 *
 *    UPDCP Entity (UPDE) - Inter System Change (ISC)
 **************************************************************************/

#if !defined (UPDE_ISC_H)
#define UPDE_ISC_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <gpcntr.h>
#include <sn_sig.h>

/***************************************************************************
* Manifest Constants
***************************************************************************/

/***************************************************************************
* Macro Functions
***************************************************************************/

/***************************************************************************
* Types
***************************************************************************/

/***************************************************************************
* Typed Constants
***************************************************************************/

/***************************************************************************
* Function Prototypes
***************************************************************************/

Boolean UpdcpSetSn   (Nsapi nsapi, Int16  sendSn, Int16  receiveSn);
Boolean UpdcpGetSn   (Nsapi nsapi, Int16 *sendSn, Int16 *receiveSn);

Boolean UpdcpSetComp (Nsapi nsapi, SnXidReq *snXidReq);
Boolean UpdcpGetComp (Nsapi nsapi, SnXidReq *snXidReq);

#endif

/* END OF FILE */

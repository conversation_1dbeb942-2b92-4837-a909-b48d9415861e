/*------------------------------------------------------------
(C) Copyright [2006-2010] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/
/**\file umadebug.h
Contains the declarations for UMAC debug utility.*/


#if !defined (UMADEBUG_H)
#define       UMADEBUG_H
/***************************************************************************
* Nested Include Files
***************************************************************************/
#include <system.h>

/******************************************************************************
 * Constants
 *****************************************************************************/
/**Number of phyDataInd buffers. Duble buffering enable working in another buffer when the first is being printed.*/
///#define UMADEBUG_NUM_OF_DEBUG_BUFFERS						2		
/**Number of phyDataInd printed in a single print*/
#define UMADEBUG_NUM_OF_PHY_DATA_INDS_PRINTED				8
/**Number of phyDataInd printed in a single print. */
#define UMADEBUG_NUM_OF_PHY_DATA_INDS_STORED				((UMADEBUG_NUM_OF_PHY_DATA_INDS_PRINTED) * 2)
/**Number of plwPhyDataInds monitored in phyDataInd */
#define UMADEBUG_NUM_PLW_PHY_DATA_INDS_PER_PHY_DATA_IND		16		

/**Set on if phyDataInd delivery was delayed by phyFrameInd */
#define UMADEBUG_ADDITIONAL_INFO_MASK_DELAYED_PHY_DATA_IND			0x01	
/**Set on if received FACH for CBS on extended physical channel */
#define UMADEBUG_ADDITIONAL_INFO_MASK_CBS_ON_EXTENDED_PHYSICAL		0x02	
/**Set on if received more plwPhyDataInds than expected by configuration */
#define UMADEBUG_ADDITIONAL_INFO_MASK_UNEXPECTED_PLW_PHY_DATA_IND	0x04	
/**Set on if received plwPhyDataInd while state is disabled (as may happen in BCH) */
#define UMADEBUG_ADDITIONAL_INFO_MASK_STATE_INACTIVE                0x08	

/******************************************************************************
 * Type Definitions
 *****************************************************************************/
/**Information about phyDataInd */
//ICAT EXPORTED STRUCT
typedef struct PlwPhyDataIndDebugInfoTag
{
	Int8	trChId;				/**<Transport Channel ID*/
	Int8	trChType;			/**<Transport Channel Type*/
	Int8	tfi;				/**<Transport Format Indication*/
}
PlwPhyDataIndDebugInfo;


/**Information about phyDataInd */
//ICAT EXPORTED STRUCT
typedef struct PhyDataIndDebugInfoTag
{
	Int8	frameNumber;					/**<frame number of the phyDataInd*/
	Int8	expectedNumOfPlwPhyDataInds;	/**< Number of plwPhyDataInds received contained in PhyDataInd*/
	Int8	additionalInfoBitmap;			/**<Bitmap of special cases*/
	Int8	*pduList_p;						/**<Pointer to pduList. In common flow is the same for all PhyDataInds*/
	PlwPhyDataIndDebugInfo	plwPhyDataInd[UMADEBUG_NUM_PLW_PHY_DATA_INDS_PER_PHY_DATA_IND];
	/**< Information on plwPhyDataInds*/
}
PhyDataIndDebugInfo;

/**PhyDataInd debug buffer for a diag print. Pointer to PhyDataIndDebugInfo[] is casted to it before being printed.*/
//ICAT EXPORTED STRUCT
typedef struct PhyDataIndDebugPrintBufferTag
{
	/**Array of phyDataInds*/
	PhyDataIndDebugInfo	phyDataInd[UMADEBUG_NUM_OF_PHY_DATA_INDS_PRINTED];	
}
PhyDataIndDebugPrintBuffer;


/**UMAC debug info that printed every UMADEBUG_INTERVAL_IN_FRAMES frames */
typedef struct UmaDebugEntityTag
{
	/** index of current phyDataInd*/						
	Int8 phyDataIndIndex;
	/** index of phyDataInd waiting for dlTransferEnd. It is different from phyDataIndIndex only when received 
	plwPhyDataInd on the next FN before all dlTransferEnds were received.*/
	Int8 dlTransferEndIndex;
	/** index of current plwPhyDataInd*/
	Int8 plwPhyDataIndIndex;	
	/**Array of phyDataInds*/
	PhyDataIndDebugInfo	phyDataInd[UMADEBUG_NUM_OF_PHY_DATA_INDS_STORED];	
}
UmaDebugEntity;

/***************************************************************************
 * Extern Variables
 ***************************************************************************/
extern UmaDebugEntity umaDebug;

/******************************************************************************
 * Macros
 *****************************************************************************/
 /** Set bit in additionalInfoBitmap according to mask*/
#define UMADEBUG_SET_ADDITIONAL_INFO_BITMAP(mask) \
	umaDebug.phyDataInd[umaDebug.phyDataIndIndex].additionalInfoBitmap |= mask;

 /** Set pduList_p*/
#define UMADEBUG_SET_PDU_LIST_P(pduList_p) \
	umaDebug.phyDataInd[umaDebug.phyDataIndIndex].pduList_p = (Int8 *)pduList_p;


/******************************************************************************
 * Function Prototypes
 *****************************************************************************/
void UmaDebugInit(void);
void UmaDebugAddNewPhyDataInd(Int8 frameNum, Int8 expectedNumOfDataInds);
void UmaDebugAddNewPlwPhyDataInd(Int8 trChId, Int8 trChType, Int8 tfi);
void UmaDebugPrint(void);


 
#endif	//UMADEBUG

/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/tplgsm/utinc/utbitfnc.h#9 $
 *   $Revision: #9 $
 *   $DateTime: 2007/03/29 13:52:35 $
 **************************************************************************
 *  File Description :
 *
 *  This file contains the function prototypes of the bit manipulation routines
 *  available as utility functions to all TTPCom code.
 **************************************************************************/

#ifndef UTBITFNC_H
#define UTBITFNC_H

/***************************************************************************
 * Nested Include Files
 **************************************************************************/

/***************************************************************************
 * Manifest Constants
 **************************************************************************/

/***************************************************************************
 * Typed Constants
 **************************************************************************/

/***************************************************************************
 * Type Definitions
 **************************************************************************/

/***************************************************************************
 *  Macros
 **************************************************************************/

extern const   Int8    utilOffsetLengthMask [BITS_PER_INT8][BITS_PER_INT8+1];

/* Macro to get bits of a bitmap array, which rely on the fact that the start
 * of the bitmap is octet aligned
 */
#define UTIL_BITMAP_GET(bitmap,offset)                                      \
    (bitmap [offset/BITS_PER_INT8] & (0x80>>(offset%BITS_PER_INT8))) ? 1 : 0

/***************************************************************************
 *  Function Prototypes
 **************************************************************************/
Int16
UtEncode7BitPackedData (Int8 *buffer, Int16 msgLen, Int8 *msgData, Int16 maxChars);

Int16
UtDecode7BitPackedData (Int8 *destData, Int8 *sourceData, Int16 destLength, Int16 sourceLength);

void UtilEncodeBitField (
      Int8   *dest_p,
      Int32  *destBitOffset,
      Int8   numOfBits,
      Int32  value);

Int8 UtilExtract8BitField (
      Int8   * const source_p,
      Int32  * const sourceBitOffset,
      const Int8   numOfBits);

Int16 UtilExtract16BitField (
      Int8   * const source_p,
      Int32  * const sourceBitOffset,
      const Int8   numOfBits);

Int32 UtilExtract32BitField (
      Int8   * const source_p,
      Int32  * const sourceBitOffset,
      const Int8   numOfBits);

#if defined(ENABLE_UP_MIPS_TEST)










                                      
#else
void UtilBitCopy (
      Int8   *source_p,
      Int32  sourceBitOffset,
      Int32  sourceBitLength,
      Int8   *destination_p,
      Int32  destinationBitOffset);
#endif /* ENABLE_UP_MIPS_TEST */

void UtilBitmapSet (
     Int8       bitmap [],
     Int16      offset,
     Boolean    value);

#endif

/* END OF FILE */

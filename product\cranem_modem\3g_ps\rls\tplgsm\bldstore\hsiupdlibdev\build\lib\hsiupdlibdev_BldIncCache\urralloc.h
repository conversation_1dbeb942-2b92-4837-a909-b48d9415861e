/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2006 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urralloc.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2006/11/08 10:41:49 $
 **************************************************************************
 *  File Description :
 *  Interface to the URR local memory allocator
 **************************************************************************/
#if !defined (URRALLOC_H)
#define       URRALLOC_H

#if !defined (DS3_LTE_ONLY) && !defined(ENABLE_CAT1_LG)


/***************************************************************************
 * Nested Include Files
 **************************************************************************/
#include <system.h>

/*********** add begin*/
#if !defined(KI_RLG_ENABLE_URRC_TRACE_LOG)
#include <gkimem.h>
#endif
/*********** add end*/

#if !defined (URRC_DISABLE_LOCAL_MEMORY_MANAGER)
#include <string.h>


/***************************************************************************
 *  Types
 **************************************************************************/

/***************************************************************************
 *  Function Prototypes
 **************************************************************************/
/* Use the local memory manager to allocate and free memory */

/* Local memory manager interface */
/* CQ00010790, modified begin */
#if defined (DEBUG_MEMORY_LEAK_ON_PC)
extern void UrrLocalAllocMemory ( char *const file_p1, Int32 line1, char *const file_p, Int32 line,Int32 size, void **mem_pp);
#else
extern void UrrLocalAllocMemory (Int32 size, void **mem_pp);
#endif
/* CQ00010790, modified end */

/* CQ00010790, modified begin */
#if defined (DEBUG_MEMORY_LEAK_ON_PC)
#define UrrLocalAllocZeroMemory(sIZE, mEM_PP) \
{ \
    UrrLocalAllocMemory ( 0, 0,__FILE__,__LINE__,(sIZE), (mEM_PP)); \
    if (*(mEM_PP) != PNULL) \
    { \
        memset (*(mEM_PP), 0, sIZE); \
    } \
}
#else
#define UrrLocalAllocZeroMemory(sIZE, mEM_PP) \
{ \
    UrrLocalAllocMemory ((sIZE), (mEM_PP)); \
    if (*(mEM_PP) != PNULL) \
    { \
        memset (*(mEM_PP), 0, sIZE); \
    } \
}
#endif
/* CQ00010790, modified end */

extern void UrrLocalFreeMemory (void **mem_pp);

#if defined (DEVELOPMENT_VERSION) && defined (URRC_ALLOC_LIST_INTEGRITY_CHECK)
extern void UrrLocalAllocIntegrityCheck (void);
#else
#define UrrLocalAllocIntegrityCheck();
#endif


/* This section is used to prevent URRC modules calling the GKI memory
 * manager directly. All memory allocations must go via the URR local
 * allocator. */
#if defined (KiAllocMemory)
#undef KiAllocMemory
#define KiAllocMemory       Do_Not_Use_KiAllocMemory_Use_UrrLocalAllocMemory_Instead
#endif

#if defined (KiAllocZeroMemory)
#undef KiAllocZeroMemory
#define KiAllocZeroMemory   Do_Not_Use_KiAllocZeroMemory_Use_UrrLocalAllocZeroMemory_Instead
#endif



#else
/* If the local memory manager is not used, then the memory is allocated via
 * the GKI memory interface */
#define UrrLocalAllocMemory(sIZE, mEM_PP)     KiAllocMemory (sIZE, mEM_PP)
#define UrrLocalAllocZeroMemory(sIZE, mEM_PP) KiAllocZeroMemory (sIZE, mEM_PP)
#define UrrLocalFreeMemory(mEM_PP)            KiFreeMemory (mEM_PP)
#define UrrLocalAllocIntegrityCheck()

/*********** add begin*/
#define KiDestroyRrcInternalSignal(B)         KiOsDestroyRrcInternalSignal (B)
#define KiCreateRrcInternalSignal(S,Z,B)      KiOsCreateRrcInternalSignal (S,Z,B)
/*********** add end*/
#endif /* !URRC_DISABLE_LOCAL_MEMORY_MANAGER */

#endif
#endif

/* END OF FILE */

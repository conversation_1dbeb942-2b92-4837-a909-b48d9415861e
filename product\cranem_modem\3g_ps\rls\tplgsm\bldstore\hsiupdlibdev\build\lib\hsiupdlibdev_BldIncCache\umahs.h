/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/umahs.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/27 15:10:43 $
 **************************************************************************
 * File Description:
 *
 *    Functional interfaces to the upper MAC-hs module (lower MAC-hs in L1).
 **************************************************************************/

#if !defined (UMAHS_H)
#define       UMAHS_H

#if defined(UPGRADE_3G_HSDPA)
#include <pl_w_globs.h>
#include <u1_cfg.h>
#include <umaxhsphy.h>
#include <umautl.h>


/***************************************************************************
 * Function Prototypes
 ***************************************************************************/

/* Interface to umamain */
void UmaHsDebugMacHs (Boolean debugMacHs);
#if defined (PS_L2_R8_API)
void UmaHsHandlePhyHsDataInd(PhyHsDataInd *phyHsDataInd_p);
#else
void UmaHsHandlePhyHsDataInd(PhyHsDataIndHeader *phyHeader_p);
#endif
void UmaHsHandleUmacHsDataRsp(SignalBuffer *sigbuf_p);
void UmaHsHandleCmacHsQueueConfigReq(SignalBuffer *sigbuf_p);
void UmaHsHandleCmacHsQueueReleaseReq(SignalBuffer *sigbuf_p);
void UmaHsCheckT1Timers(void);
void UmaHsDeliverPendingMacHsPdus(void);
size_t UmaHsInit (UmacEntity *umac_p);
void UmaHsToIdle (void);

void UmaHsFillPduListInfoIndCreate( void );
void UmaHsFillPduListInfoInd(Int8 *data_p, dtcF8DescriptorInfo **hsCipherInfo_pp,
                            Int32 dataBitOffset, Int8 currentPdu,
                            Int8 numberPdus, Int16 pduSizeInBits,
                            Int8 macDflowId);
void UmaHsSendPduListInfoInd(Int8 numberMacDpdus);
void UmaHsHandleUmacHsPduListInfoRsp(SignalBuffer *sigbuf_p);
Boolean UmaHsDelayDlConfigChangeInd(void);
void UmaHsHandleUmacDlConfigChangeRsp(void);

#if defined ON_PC
void UmaHsTestDisassemblePdu(Int8 queueId, Int8 tsn);
void UmaHsTestProcessUmaHsDataRsp();
void UmaHsTestQueueConfigReq(Int8 queueId, Int8 lastTsnOfOldConfig );
void UmaHsTestMacHsReset(Int8 queueId, Int8 lastTsnOfOldConfig );

#endif //ON_PC

#endif /* UPGRADE_3G_HSDPA */
#endif
/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urrtp.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/27 15:10:43 $
 ***************************************************************************
 * File Description: Header file for the URRC function trace package
 **************************************************************************/

#if !defined (URRTP_H)
#define       URRTP_H

#if defined (DEVELOPMENT_VERSION)
#if defined (URRC_DEBUG_FUNCTION_CALLS)
/**************************************************************************
 * Nested Include Files
 **************************************************************************/
#include <urrtypes.h>
#include <stdio.h>
/**************************************************************************
 * Manifest Constants
 **************************************************************************/

/**************************************************************************
 * Type Definitions
 **************************************************************************/

typedef struct UrrFunctionTraceDataTag
{
    /* Call Stack information */
    UrrSubProcessId callStack [255];

    /* File handle in case of logging to external file */
    FILE* externalLogFileHandle;

    /* TRUE if logging to External file */
    Boolean externalLoggingEnabled;

    /* TRUE if logging to Genie */
    Boolean genieLoggingEnabled;

    /* current indentation level */
    Int16 indentationLevel;

    /* TRUE if logging to MSC */
    Boolean mscLoggingEnabled;

    /* TRUE if sub-process internal calls should be shown in the MSC */
    Boolean mscLoggingShowInternal;

    char printSpace[255];               /* used to improve the performace of
                                         * Genie logs. This variable is used to
                                         * create the indentation. It contains
                                         * only blanks ' ' */

    /* Global variable for subprocess function call tracing state,
     * MSB == PROCESS, LSB ==  Subprocess,
     * As it is only RRC that uses this package at the moment MSB == 0 is RRC.
     * When more protocol stack entities start to use the package the following
     *  should apply:
     * MSB == 0 RRC
     *     == 1 RLC
     *     == 2 MAC
     *     == 3 AL
    */
    Int32 subprocessTraceState;

}
UrrFunctionTraceData;

/**************************************************************************
 * Macros
 **************************************************************************/

/* Enable debugging of all subprocesses */
#if defined (URRC_DEBUG_ALL_SUB_PROCESSES)
#undef URRC_DEBUG_MTC_SUB_PROCESS
#undef URRC_DEBUG_SMC_SUB_PROCESS
#undef URRC_DEBUG_CSR_SUB_PROCESS
#undef URRC_DEBUG_SIR_SUB_PROCESS
#undef URRC_DEBUG_CER_SUB_PROCESS
#undef URRC_DEBUG_RBC_SUB_PROCESS
#undef URRC_DEBUG_CMR_SUB_PROCESS
#undef URRC_DEBUG_AIS_SUB_PROCESS
#undef URRC_DEBUG_MCR_SUB_PROCESS

#define URRC_DEBUG_MTC_SUB_PROCESS
#define URRC_DEBUG_SMC_SUB_PROCESS
#define URRC_DEBUG_CSR_SUB_PROCESS
#define URRC_DEBUG_SIR_SUB_PROCESS
#define URRC_DEBUG_CER_SUB_PROCESS
#define URRC_DEBUG_RBC_SUB_PROCESS
#define URRC_DEBUG_CMR_SUB_PROCESS
#define URRC_DEBUG_AIS_SUB_PROCESS
#define URRC_DEBUG_MCR_SUB_PROCESS
#endif

/* Log separator for external log file. Inserted to distinguish between multiple
 * logs in the log file */
#define URR_FUNCTION_CALL_LOG_SEPARATOR                                        \
  "\n========================================================================\n"

/*******************************************************************************
 *
 * Function     : XINDENT
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : lEVEL - the current stack depth and indentation level
  *
 * Returns      : None
 *
 * Description  : Creates the indentation for the sub-process printout. The
 *                code creates a null terminated string of blanks by putting
 *                the null termination in the right position and after
 *                printing the string, removes the null termination again.
 *
 ******************************************************************************/
#define XINDENT(lEVEL1)                                                        \
{                                                                              \
    /* set the end-of-string marker in the right position */                   \
    /* The multiplication by "2" is due to having two spaces per */            \
    /* indentation level */                                                    \
    urrFunctionTraceData.printSpace [ (lEVEL1) * 2] = '\0';                    \
                                                                               \
    if (urrFunctionTraceData.genieLoggingEnabled)                              \
    {                                                                          \
        printf (urrFunctionTraceData.printSpace);                              \
    }                                                                          \
                                                                               \
    if (urrFunctionTraceData.externalLoggingEnabled)                           \
    {                                                                          \
        fputs (urrFunctionTraceData.printSpace,                                \
               urrFunctionTraceData.externalLogFileHandle);                    \
    }                                                                          \
    /* remove the end-of-string marker */                                      \
    urrFunctionTraceData.printSpace [ (lEVEL1) * 2] = ' ';                     \
}

/*******************************************************************************
 *
 * Function     : URR_CHECK_IF_TRACE_IS_ENABLED
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : tRACE_ENABLED - set to TRUE if module tracing is enabled for
 *                                the current file
  *
 * Returns      : None
 *
 * Description  : Checks if the module tracing is enabled for the current file.
 *                Relies on the fact that MODULE_TRACE_CODE needs to be set
 *                at the top of every file that needs tracing.
 *
 ******************************************************************************/




      
#define URR_CHECK_IF_TRACE_IS_ENABLED(tRACE_ENABLED)                           \
{                                                                              \
    if ((urrFunctionTraceData.subprocessTraceState &                           \
         (Int32) (1 << (URR_TRACE_PROCESS - 1))) != 0)                         \
    {                                                                          \
        tRACE_ENABLED = TRUE;                                                  \
    }                                                                          \
    else                                                                       \
    {                                                                          \
        tRACE_ENABLED = FALSE;                                                 \
    }                                                                          \
}

/*******************************************************************************
 *
 * Function     : ENTER
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : None
 *
 * Returns      : None
 *
 * Description  : Prints the current function to stdout, external log file
 *                and MSC, if tracing is enabled.
 *
 ******************************************************************************/
#define ENTER                                                                  \
{                                                                              \
    Boolean traceEnabled = FALSE;                                              \
    UrrSubProcessId callingProcess;                                            \
                                                                               \
    URR_CHECK_IF_TRACE_IS_ENABLED (traceEnabled);                              \
    if (traceEnabled)                                                          \
    {                                                                          \
        /* remember where we are */                                            \
        urrFunctionTraceData.callStack [urrFunctionTraceData.indentationLevel] \
            = URR_TRACE_PROCESS;                                               \
        XINDENT (urrFunctionTraceData.indentationLevel);                       \
        if (urrFunctionTraceData.genieLoggingEnabled)                          \
        {                                                                      \
            printf ("ENTER  %s %s #%d\n", __FUNC__,  __FILE__, __LINE__);      \
        }                                                                      \
                                                                               \
        if (urrFunctionTraceData.externalLoggingEnabled)                       \
        {                                                                      \
            fprintf (urrFunctionTraceData.externalLogFileHandle,               \
                     "ENTER  %s %s #%d\n", __FUNC__, __FILE__, __LINE__);      \
        }                                                                      \
        urrFunctionTraceData.indentationLevel++;                               \
                                                                               \
        if (urrFunctionTraceData.mscLoggingEnabled)                            \
        {                                                                      \
            /* Determine the calling sub-process */                            \
            switch (urrFunctionTraceData.indentationLevel)                     \
            {                                                                  \
            case 0: /* fall-through */                                         \
            case 1:                                                            \
                callingProcess = URRC_MTC_SUB_PROCESS;                         \
                break;                                                         \
            default:                                                           \
                callingProcess = urrFunctionTraceData.callStack                \
                                   [urrFunctionTraceData.indentationLevel - 2];\
            }                                                                  \
                                                                               \
            /* Determine if sub-process internal function calls should be */   \
            /* shown. */                                                       \
            if (urrFunctionTraceData.mscLoggingShowInternal ||                 \
                (! urrFunctionTraceData.mscLoggingShowInternal &&              \
                 (callingProcess != URR_TRACE_PROCESS)))                       \
            {                                                                  \
                /* Output function call tracing information to MSC */          \
                UrrMtcOutputFuncDebugForMsc (callingProcess,                   \
                                             URR_TRACE_PROCESS,                \
                                             (Int8 *) __FUNC__##"");           \
            }                                                                  \
        }                                                                      \
    }                                                                          \
    else /* trace is not enabled for sub-process */                            \
    {                                                                          \
        /* remember where we are */                                            \
        urrFunctionTraceData.callStack [urrFunctionTraceData.indentationLevel] \
            = URR_TRACE_PROCESS;                                               \
        urrFunctionTraceData.indentationLevel++;                               \
    }                                                                          \
}

/*******************************************************************************
 *
 * Function     : RETURN
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : None
 *
 * Returns      : None
 *
 * Description  : Prints the current function to stdout and external log file,
 *                if tracing is enabled. Can be used as RETURN; or
 *                RETURN(value); and replaces the "normal" return;
 *
 ******************************************************************************/
#define RETURN                                                                 \
{                                                                              \
    Boolean traceEnabled = FALSE;                                              \
    UrrSubProcessId callingProcess;                                            \
                                                                               \
    URR_CHECK_IF_TRACE_IS_ENABLED (traceEnabled);                              \
    if (traceEnabled)                                                          \
    {                                                                          \
        if (urrFunctionTraceData.mscLoggingEnabled)                            \
        {                                                                      \
            /* Determine the calling sub-process */                            \
            switch (urrFunctionTraceData.indentationLevel)                     \
            {                                                                  \
            case 0: /* fall-through */                                         \
            case 1:                                                            \
                callingProcess = URRC_MTC_SUB_PROCESS;                         \
                break;                                                         \
            default:                                                           \
                callingProcess = urrFunctionTraceData.callStack                \
                                   [urrFunctionTraceData.indentationLevel - 2];\
            }                                                                  \
                                                                               \
            /* Determine if sub-process internal function calls should be */   \
            /* shown. RETURN is never shown for internal function calls */     \
            if (callingProcess != URR_TRACE_PROCESS)                           \
            {                                                                  \
                /* Output function call tracing information to MSC */          \
                UrrMtcOutputFuncDebugForMsc (URR_TRACE_PROCESS,                \
                                             callingProcess,                   \
                                             (Int8 *) __FUNC__##"_return");    \
            }                                                                  \
        }                                                                      \
                                                                               \
        if (urrFunctionTraceData.indentationLevel > 0)                         \
        {                                                                      \
            urrFunctionTraceData.indentationLevel--;                           \
        }                                                                      \
        XINDENT (urrFunctionTraceData.indentationLevel);                       \
        if (urrFunctionTraceData.genieLoggingEnabled)                          \
        {                                                                      \
            printf ("RETURN %s\n", __FUNC__);                                  \
        }                                                                      \
                                                                               \
        if (urrFunctionTraceData.externalLoggingEnabled)                       \
        {                                                                      \
            fprintf (urrFunctionTraceData.externalLogFileHandle,               \
                     "RETURN %s\n", __FUNC__);                                 \
        }                                                                      \
    }                                                                          \
    else /* trace not enabled for sub-process */                               \
    {                                                                          \
        if (urrFunctionTraceData.indentationLevel > 0)                         \
        {                                                                      \
            urrFunctionTraceData.indentationLevel--;                           \
        }                                                                      \
    }                                                                          \
}                                                                              \
return

/* if trace code is not defined for the current file, set default value */
#if ! defined (MODULE_TRACE_CODE)
#define MODULE_TRACE_CODE URRC_NULL_SUB_PROCESS
#endif

/**************************************************************************
 * Function Prototypes
 **************************************************************************/
extern void UrrInitSubprocessFunctionTrace (void);
extern void UrrEnableSubprocessFunctionTrace (UrrSubProcessId urrSubProcessId);
extern void UrrDisableSubprocessFunctionTrace (UrrSubProcessId urrSubProcessId);
extern void UrrEnableSubprocessFunctionLoggingExternal (char* fileName);
extern void UrrDisableSubprocessFunctionLoggingExternal (void);
extern void UrrEnableSubprocessFunctionLoggingGenie (void);
extern void UrrDisableSubprocessFunctionLoggingGenie (void);
extern void UrrEnableSubprocessFunctionLoggingMsc (Boolean showInternal);
extern void UrrDisableSubprocessFunctionLoggingMsc (void);

/* Global variable holding the trace data */
extern UrrFunctionTraceData urrFunctionTraceData;

#endif /* URRC_DEBUG_FUNCTION_CALLS */
#endif /* DEVELOPMENT_VERSION */

/* If not using URRC_DEBUG_FUNCTION_CALLS and DEVELOPMENT_VERSION */
#if ! defined (RETURN)
#define ENTER
#define RETURN return
#endif

#endif

/* END OF FILE */

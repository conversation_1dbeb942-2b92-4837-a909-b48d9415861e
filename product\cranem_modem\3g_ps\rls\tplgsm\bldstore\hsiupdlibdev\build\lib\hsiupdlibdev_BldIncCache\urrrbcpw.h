/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urrrbcpw.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2007/02/02 13:54:44 $
 **************************************************************************
 * File Description:
 *
 * URRC Radio Bearer Control Power calculations (Betas) and SF calculation
 **************************************************************************/

#if !defined (URR_RBC_PWR_H)
#define       URR_RBC_PWR_H

/***************************************************************************
* Nested Include Files
***************************************************************************/
#include <urrrbc.h>

/***************************************************************************
*   Manifest Constants
***************************************************************************/

/***************************************************************************
*   Macro Functions
***************************************************************************/

/***************************************************************************
*   Types
***************************************************************************/

/***************************************************************************
 * Typed Constants
 ***************************************************************************/

/***************************************************************************
 * Function Prototypes
 ***************************************************************************/
void UrrRbcPwrComputeMissingTfcsInfoDch(
                UrrRbcULTrChList          *ulTrChList_p,
                UrrRbcUlTfcsTable         *tfcTable_p,
                UUL_DPCH_Info_latest_fdd      *dpchInfo_p,
                UrrRbcReferenceGainFactor  refGains[]);

void UrrRbcPwrComputeMissingTfcsInfoRach(
                UrrRbcULTrChList          *ulTrChList_p,
                UrrRbcUlTfcsTable         *tfcTable_p,
                UrrRbcReferenceGainFactor  refGains[]);

Boolean UrrRbcPwrCalculateRachBetaCAndDFromRef (UPRACH_SystemInformation *prachData_p,
                                UReferenceTFC_ID computedGainFactors,
                                UGainFactor *betaC_p, UGainFactor *betaD_p,
                                Int8 tf, UrrRbcReferenceGainFactor refGains[]);

#endif

/* END OF FILE */

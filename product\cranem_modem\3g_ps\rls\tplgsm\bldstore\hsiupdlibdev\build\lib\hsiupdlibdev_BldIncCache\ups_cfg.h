/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/api/inc/ups_cfg.h#5 $
 *   $Revision: #5 $
 *   $DateTime: 2007/03/19 13:59:30 $
 *************************************************************************/
/** \file
 * 3G Protocol Stack Configuration Definitions
 *************************************************************************/

#if !defined (UPS_CFG_H)
#define       UPS_CFG_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <uas_asn.h>
#include <system.h>
#include <cphy_sig.h>

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/

/************* FDD RSSI scan thresholds.  (Start) **********************/

/* Whenever the UE performs an FDD RSSI scan on a UARFCN, the
   UARFCN won't be considered if the reported RSSI value is
   lower than the applicable threshold defined below.
   Threshold applicability is described below.

   NOTEs
   1) Due to internal scaling, the value "X" of the thresholds
      below must be such that
                  -4095 dBm <= X <= +4095 dBm.
   2) Setting these thresholds is at the discretion of individual
      manufacturers. In doing so, they accept sole responsibility. */

#define UPS_CFG_FDD_RSSI_SCAN_THRESHOLD 	(-120  *  MEASUREMENTS_TO_DB_SCALING_FACTOR)    /* value in 1/8dBm */

#define UPS_CFG_FDD_RSSI_PRIME_SCAN_THRESHOLD 	(-102  *  MEASUREMENTS_TO_DB_SCALING_FACTOR)    /* value in 1/8dBm */

/* If a UARFCN is scanned but no cells detected it will only be rescanned
   if its RSSI has increased by UPS_CFG_FDD_RSSI_SCAN_HYSTERESIS. */
#define UPS_CFG_FDD_RSSI_SCAN_HYSTERESIS 	( 2  *  MEASUREMENTS_TO_DB_SCALING_FACTOR)    /* value in 1/8dBm */

/* For Prime List search stage, the RSSI scan threshold is
   calculated dynamically to be the lowest RSSI value found
   plus UPS_CFG_FDD_NOISE_FLOOR_THRESHOLD. */
#define UPS_CFG_FDD_NOISE_FLOOR_THRESHOLD 	(3  *  MEASUREMENTS_TO_DB_SCALING_FACTOR)    /* value in 1/8dBm */


 /* For Whole Band search stage (exhaustive search) the RSSI threshold is slightly higher.
  * UARFCNs with RSSI level below this level are excluded from Whole Band search stage
  * (but not from Prime List search stage).
  * This reduces the time searching for noise in real
  * world vs searching for specific Uarfcns used by testers  */
#define UPS_CFG_FDD_WHOLE_BAND_RSSI_SCAN_THRESHOLD 	(-95  *  MEASUREMENTS_TO_DB_SCALING_FACTOR)    /* value in 1/8dBm */


/************* RSSI scan thresholds.  (End)  *********/


/************* UARFCN removal algorithm Parameters.  (Start) **********************/

/* These defines are used to speed up PLMN search - see CsrRemoveUarfcnsThatWillNotGiveService for details. */
#define FDD_FREQUENCY_SPACING       					((UUARFCN) 25)
#define FDD_FREQUENCY_WINDOW        					((UUARFCN) 3)

/* For each GSM cell found, remove (arfcn - FREQUENCY_SPACING , arfcn + FREQUENCY_SPACING) from UMTS search */
#define FREQUENCY_SPACING_FOR_REMOVING_GSM_CELLS_FROM_UMTS_SEARCH   ((UUARFCN) 10)
/************* UARFCN removal algorithm Parameters. (End) **********************/


/************* Dynamic Prime List Parameteres.  (Start) **********************/

/* The following constants define exactly how many UARFCNs will be scanned in the Prime List search stage (In Dynamic Prime List Mode)*/
#define FDD_PRIME_LIST_MAX_UARFCNS_TO_SCAN_PER_BAND       20
#define FDD_PRIME_LIST_MAX_ADDITIONAL_UARFCNS_TO_SCAN     4

/************* Dynamic Prime List Parameteres.  (End) **********************/


/************* RSSI scan ranking algorithm Parameters.  (Start) **********************/


/* UTRA Carrier match grade parameters:
     Correlation length in Units of 200KHz. Shouldn't exceed the maximum correlation length (currently UTRA_CARRIER_MAX_BANDWIDTH)! */
#define FDD_RSSI_SCAN_GRADE_BANDWIDTH      				20

#define FDD_RSSI_SCAN_GRADE_FACTOR						10000
#define FDD_RSSI_SCAN_GRADE_MAXIMUM    					(15 * FDD_RSSI_SCAN_GRADE_FACTOR)
#define FDD_RSSI_SCAN_GRADE_INVALID    					(-2147483647L)

/* RSSI Value grade parameters:
    UARFCNs will be given an offset proportional to their RSSI Value and to FDD_RSSI_SCAN_GRADE_POWER_OFFSET in order to prioritise strong UARFCNs. */
#define FDD_RSSI_SCAN_GRADE_POWER_OFFSET  				(2 * FDD_RSSI_SCAN_GRADE_FACTOR)

/* Static Prime List grade parameters:
    Static Prime UARFCNs with grade above FDD_RSSI_SCAN_GRADE_STATIC_PRIME_LIST_THRESHOLD
    will be given an offset of FDD_RSSI_SCAN_GRADE_STATIC_PRIME_LIST_OFFSET to prioritise good Static Prime UARFCNs. */
#define FDD_RSSI_SCAN_GRADE_STATIC_PRIME_LIST_THRESHOLD (-0.5 * FDD_RSSI_SCAN_GRADE_FACTOR)
#define FDD_RSSI_SCAN_GRADE_STATIC_PRIME_LIST_OFFSET  	(1 * FDD_RSSI_SCAN_GRADE_FACTOR)


#define FDD_RSSI_SCAN_GRADE_NUM_OF_PATTERNS  			(6)


/************* RSSI scan ranking algorithm Parameters.  (End) **********************/


/***************************************************************************
 * External Global Variables
 ***************************************************************************/

/** The following variable configures the Ec/No value that is deemed to give
 * enough confidence that the S criterion is satisfied for cells for which the
 * measured Ec/No is such that:
 *         measured Ec/No >= upsCfgSCriterionValidationLevel
 * during 3G PLMN searches.
 * This threshold is used in order to minimise the sib3 reading activity during
 * 3G PLMN searches in RRC IDLE. If the Ec/No for a cell satisfies the above
 * inequality, then its SIB3 is not read, beause it is very likely that this
 * cell satisfies S without having to read the relevant SIB3 parameters. This
 * speeds up the searches whilst giving confidence that the reported cells
 * still satisfy S.
 * \note Setting this threshold is at the discretion of individual
 * manufacturers. In doing so, they accept sole responsibility.
 */
 extern const SignedInt16 upsCfgSCriterionValidationLevel;

/** The minimum of the dynamic receiver range in dBm x 8 units for
 * Event 6E.
 */
 extern const SignedInt16 upsCfgGetMinDynamicReceiverRange;

/** The maximum of the dynamic receiver range in dBm x 8 units for
 * Event 6E.
 */
extern const SignedInt16 upsCfgGetMaxDynamicReceiverRange;

extern const SignedInt16 upsCfgRssiFullScanThreshold;

/* If there is a malfunction in the lower layers, and it becomes
 * impossible to send data then the RRC connection needs to be abandoned.
 * The best way of detecting this is if there are a large number of AM data
 * not acknowledged by the UTRAN. */
extern const SignedInt16 upsCfgMaxNumberOfAmDataReqQueuedInRlc;
extern const SignedInt16 numOfAmDataReq_HighWatermark;
extern const SignedInt16 numOfAmDataReq_LowWatermark;


/* The following static variable contains the UTRA carrier patterns table.
 These patterns are used in a correlation process to optimize the UMTS PLMN search.
 Every pattern consists of a central frequency RSSI value and an array of the relative RSSIs of surrounding frequencies. */
extern const UtraCarrierPattern utraCarrierPatterns[FDD_RSSI_SCAN_GRADE_NUM_OF_PATTERNS];


/***************************************************************************
 * Global Function Prototypes
 ***************************************************************************/
/* Function prototypes to allow a 3G platform to know when data received
 * by the PS in PhyDataInd has completed processing */


/** @} */ /* End of Cfg3gPs group */


/** \defgroup ApiPdu PDU Management API
 * \ingroup Prd3gDmProtoStack
 * \ingroup Api
 * This section defines API to allow a 3G platform to know if the data received
 * in #PhyDataInd primitive has been completely processed or not.
 *
 * @{
 */

/** Allows a 3G customer platform to define how large the data area should be
 * allocated for the UL PDU list. \note TMM memory should always be used
 * for these allocations for flow control to work correctly.
 */
Int8 *UpsCfgAllocUlPduList(void);

/** Allows a 3G customer platform to define a deallocation function for the
 * above allocation function. \note TMM memory should always be used for
 * these allocations for flow control to work correctly.
 */
void UpsCfgFreeUlPduList(Int8 *oldPduList_p);

Boolean UpsCfgCheckRssiToBeSent (SignedInt16 signalLevel, Int16 signalQuality, Boolean init);

#if defined(UPGRADE_3G_HSDPA)

/** Allows a 3G customer platform to define how large the data area should be
 * allocated for the MAC-hs PDU. \note TMM memory should always be used
 * for these allocations for flow control to work correctly.
 */
Int8 *UpsCfgAllocHsPdu(Int32 size);
#endif /* UPGRADE_3G_HSDPA */

/** @} */ /* End of ApiPdu group */

#endif /* UPS_CFG_H */
/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/******************************************************************************
 * File Name:   umaemuxcommon.h
 *
 * File Description: Common definitions for multiplexer clients.
 *
 *****************************************************************************/
 
#ifndef UMAEMUXCOMMON_H
#define UMAEMUXCOMMON_H

#ifdef UPGRADE_3G_EDCH
/******************************************************************************
 * Include Files
 *****************************************************************************/
#include <system.h>
#include <tmm.h>
#include <udtcif.h>
#include <umacebase.h>

#define UMACE_MAX_MUX_REQ_NUM               (2)
#define UMACE_MACE_HDR_SI_DDI0_VAL          (0x3F)  /* DDI0 = 111111 */
#define UMACE_SCHED_INFO_BUFFER_BYTELEN     (4)     /* SI buffer need 32 bits.
                                                     * Use 18 bits for SI content */

#if !defined(ON_PC)
//#define UMACE_DEBUG_MACE_TIME
#endif

/******************************************************************************
 * Constants
 *****************************************************************************/
/* Default DTC mode: don't retain cipher state, no cipher,
 * continous output mode, no cipher offset */
#define UMACE_DEFAULT_DTC_MODE_REG  \
    (UDTC_IF_DEFAULT_MODE_REG | UDTC_IF_CONTINUOUS_OUTPUT_MASK)

/* Maximum number of octets required to hold mac-e header calculated
 * as logical-channel info length (6 bits for DDI + 6 bits for PDUs number)
 * multiple with the maximum logical channels number + 6 bits for DDI0 */
#define UMACE_MAX_MACE_HEADER_OCTETS \
    (BITS_TO_INT8S((UMAC_EDCH_MAX_LOGICAL_CHANNELS *\
    (UMACE_MACE_HDR_DDI_BITLEN+UMACE_MACE_HDR_PDUS_NUM_BITLEN))+\
    UMACE_MACE_HDR_DDI_BITLEN))

#if defined (UPGRADE_3G_UL_EL2)
/* Maximum number of octets required to hold mac-i header calculated
 * as maximum PDUs number that can be transmitted in single TTI multiple
 * with each PDU header size (16 bits * max PDUs) */

/*The correct value of MAX_MAC_IS_SDUS_PER_TTI should be the maximum TB size divided by min rlc pdu size
    However, it results in a very big number which we cannot process (~625 PDus) so therefore we assume 
    248 corralted with the maximum number of DTC descriptor for EDCH though it is unlikly to result in such
    number as mac-i pdus should be large and only a very bad configuration of mac-i may cause us to reach
    this number */
#define MAX_MAC_IS_SDUS_PER_TTI		200 

#define UMACE_MAX_MACI_HEADER_OCTETS \
    (BITS_TO_INT8S(UMACI_MACI_HDR_PER_MAC_IS_SDU_BITLEN * MAX_MAC_IS_SDUS_PER_TTI))
    
#define UMACE_MAX_EDCH_HEADER_OCTETS    UMACE_MAX(UMACE_MAX_MACE_HEADER_OCTETS, UMACE_MAX_MACI_HEADER_OCTETS)

#else

#define UMACE_MAX_EDCH_HEADER_OCTETS    UMACE_MAX_MACE_HEADER_OCTETS

#endif


/******************************************************************************
 * Macros
 *****************************************************************************/
/* Increase TSN value modulus 64 */
#define UMACE_TSN_INCREMENT(a) (a = ((a + 1) & (0x3F)))

#define UMACE_GET_LAST_DTC_REQ_INFO_P \
    &(umaEMuxDB_p->muxReqInfo[umaEMuxDB_p->lastReqIndex].dtcRequestInfo)

/******************************************************************************
 * Type Definitions
 *****************************************************************************/
typedef enum UmacMuxReqStateTag
{
    UMACE_MUX_FREE      = 0, /* Free state */
    UMACE_MUX_ALLOC     = 1, /* Allocated state */
    UMACE_MUX_DTC_ENDED = 2  /* DTC transfer ended. resources can be released */
}
UmacMuxReqState;

typedef struct UmacMuxBufferInfoTag
{
    struct UmacMuxBufferInfoTag *next_p;
}
UmacMuxBufferInfo;

typedef struct UmacMuxDescChainInfoTag
{
    Int32                   totalDescNum;  /* Total allocated descriptors number */
    Int32                   freeDescNum;   /* Free descriptors number */
    dtcF8DescriptorInfo     *chainHead_p;
    dtcF8DescriptorInfo     *nextFreeDesc_p;
}
UmacMuxDescChainInfo;


typedef struct UmacMuxReqInfoTag
{
    UmacMuxReqState         reqState;
    Int32                   reqDescNum;
    Int8                    *schedInfoBuff_p;
    Int8                    *maceHeaderBuffer_p;
    UdtcRequestInfo         dtcRequestInfo;
    Int8                    rlcTxReqId;
    UmacMuxBufferInfo       *additionBuffList_p;
    UmacMuxBufferInfo       *lastBuffListElem_p; 
#if defined (UMACE_ENABLE_MACI_DEBUG)
    Int8                    *lastMaciBuffer_p;
    Int32                   lastMaciSize;
#endif
#if defined (UMACE_DEBUG_MACE_TIME)
    Int32                   dtcStartTime;
    Int32                   dtcEndTime;
#endif
}
UmacMuxReqInfo;

typedef struct UmaEMuxDBTag
{
    Int32                lastReqIndex;      /* Last request index */
    /* TSN counters array per logical channel. Indicates the sequence number of
     * the corresponding logical-channel transmission. Increased modulus 64 */
    Int32                tsnCounters[UMAC_EDCH_MAX_LOGICAL_CHANNELS];

    /* Pre-allocated descriptors chain info */
    UmacMuxDescChainInfo descriptorsChainInfo;

    /* Multiplexing requests array */
    UmacMuxReqInfo       muxReqInfo[UMACE_MAX_MUX_REQ_NUM];
}
UmaEMuxDB;

/******************************************************************************
 * Function Prototypes
 *****************************************************************************/
void UmaEMuxAllocDescWithSrcBuffer(Int32 bitLength, dtcF8DescriptorInfo *prevDesc_p);
void UmaEMuxAllocDescSrcBuffer(Int32 bitLength, dtcF8DescriptorInfo *descInfo_p);
void UmaEMuxDiscardDescriptorFromChain(dtcF8DescriptorInfo *prevDesc_p);
void UmaEMuxInitReqInfo(void (*callbackFunc_p)(dtcTransferEnd_ts *dtcTransferEndInd_p));
void UmaEMuxInitDescriptorsChain(void);
void UmaEMuxReleaseRequestResources(UmacMuxReqInfo *muxReq_p);
void UmaEMuxReleaseTerminatedRequests(void);
void UmaEMuxDisableLastChainDescContMode(void);
void UmaEMuxInit(void);
Int32 UmaEmuxGetTsnCounter(Int8 lcId);
#if defined (UMACE_ENABLE_MACI_DEBUG)
void UmaEMuxPrintDescriptorsChain(dtcF8DescriptorInfo *firstF8Desc_p);
#endif

/******************************************************************************
 * External Variables
 *****************************************************************************/
extern UmaEMuxDB *umaEMuxDB_p;

#endif //UPGRADE_3G_EDCH
#endif //UMAEMUXCOMMON_H


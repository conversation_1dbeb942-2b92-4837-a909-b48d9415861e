#include <urrtypes.h>
#include <urrintsig.h>
#include <urrmcria.h>
#include <urrmcrie.h>
#include <urrmcrir.h>

#if defined(UPGRADE_ENABLE_MCR_DEBUG)
/*****************************************************************************************
 *Type Definitions of the Debug Measurment ID struct - for logging and debugging purposes
 *****************************************************************************************/

#define maxIntraDebugDummys                 7
#define maxInterDebugDummys                 6
#define maxInterRATDebugDummys              5

//ICAT EXPORTED STRUCT
typedef struct DebugUPrimaryCPICH_InfoTag
{
    UPrimaryScramblingCode                             primaryScramblingCode;
}
DebugUPrimaryCPICH_Info;

//ICAT EXPORTED UNION : T_UPrimaryCCPCH_Info_syncCase
typedef union UPrimaryCCPCH_Info_syncCase_UnionTag
{
    /* 0 */
    UPrimaryCCPCH_Info_syncCase1                       syncCase1;
    /* 1 */
    UPrimaryCCPCH_Info_syncCase2                       syncCase2;
}
UPrimaryCCPCH_Info_syncCase_Union;

//ICAT EXPORTED STRUCT
typedef struct DebugUPrimaryCCPCH_Info_syncCaseTag
{
    T_UPrimaryCCPCH_Info_syncCase tag;

    UPrimaryCCPCH_Info_syncCase_Union Union;
}
DebugUPrimaryCCPCH_Info_syncCase;

//ICAT EXPORTED STRUCT
typedef struct DebugXr3UPrimaryCCPCH_Info_r4_tddTag
{
    Boolean                                            syncCasePresent;
    DebugUPrimaryCCPCH_Info_syncCase                   syncCase;
}
DebugXr3UPrimaryCCPCH_Info_r4_tdd;

//ICAT EXPORTED STRUCT
typedef struct DebugUPrimaryCCPCH_Info_r4_tddTag
{
    Boolean                                            cellParametersIDPresent;
    UCellParametersID                                  cellParametersID;
    Boolean                                            sctd_Indicator;
    DebugXr3UPrimaryCCPCH_Info_r4_tdd                  legacyR3_p;
}
DebugUPrimaryCCPCH_Info_r4_tdd;

//ICAT EXPORTED UNION : T_UPrimaryCCPCH_Info_r4
typedef union DebugUPrimaryCCPCH_Info_Union_r4Tag
{
     // 0 //
     UPrimaryCCPCH_Info_latest_fdd                          fdd;
     // 1 //
     DebugUPrimaryCCPCH_Info_r4_tdd                         tdd;
}
DebugUPrimaryCCPCH_Info_Union_r4;

//ICAT EXPORTED STRUCT
typedef struct DebugUPrimaryCCPCH_Info_r4Tag
{
    T_UPrimaryCCPCH_Info_latest                             tag;

    DebugUPrimaryCCPCH_Info_Union_r4                        Union; 
}
DebugUPrimaryCCPCH_Info_r4;

//ICAT EXPORTED UNION : T_UForbiddenAffectCell_r4
typedef union DebugUForbiddenAffectCell_Union_r4Tag
{
    UPrimaryCPICH_Info                                      fdd;

    DebugUPrimaryCCPCH_Info_r4                              tdd;
}
DebugUForbiddenAffectCell_Union_r4;

//ICAT EXPORTED STRUCT
typedef struct DebugUForbiddenAffectCell_r4Tag
{
    T_UForbiddenAffectCell_latest tag;

    DebugUForbiddenAffectCell_Union_r4 Union; 
}
DebugUForbiddenAffectCell_r4;

//ICAT EXPORTED STRUCT
typedef struct DebugUForbiddenAffectCellList_r4Tag
{
    DebugUForbiddenAffectCell_r4                            data[maxIntraDebugDummys];

}
DebugUForbiddenAffectCellList_r4;

//ICAT EXPORTED STRUCT
typedef struct DebugUEvent1a_r4Tag
{
    UTriggeringCondition2                                   triggeringCondition;
    UReportingRange                                         reportingRange;
    Boolean													forbiddenAffectCellListPresent;
    // DebugUForbiddenAffectCellList_r4                        forbiddenAffectCellList;
    UW														w;
    UReportDeactivationThreshold                            reportDeactivationThreshold;
    UReportingAmount                                        reportingAmount;
    UReportingInterval                                      reportingInterval;
}
DebugUEvent1a_r4;

//ICAT EXPORTED STRUCT
typedef struct DebugUEvent1b_r7Tag
{
    UTriggeringCondition1                                   triggeringCondition;
    UReportingRange                                         reportingRange;
    Boolean                                                 forbiddenAffectCellListPresent;
    // DebugUForbiddenAffectCellList_r4                        forbiddenAffectCellList;
    UW                                                      w;
    Boolean                                                 periodicReportingInfo_1bPresent;
    UPeriodicReportingInfo_1b                               periodicReportingInfo_1b;
}
DebugUEvent1b_r7;

//ICAT EXPORTED UNION : T_UIntraFreqEvent_r7
typedef union DebugUIntraFreqEvent_Union_r7Tag
{
    // Note: Event data for each
    /* 0 */
    DebugUEvent1a_r4                                        e1a;
    /* 1 */
    DebugUEvent1b_r7                                        e1b;
    /* 2 */
    UEvent1c                                                e1c;
    /* 3 */
    UEvent1d                                                e1d;
    /* 4 */
    UEvent1e_latest                                         e1e;
    /* 5 */
    UEvent1f_latest                                         e1f;
    /* 6 */
    Int8                                               _dummy7_;
    /* 7 */
    UThresholdUsedFrequency_r6                              e1h;
    /* 8 */
    UThresholdUsedFrequency_r6                              e1i;
    /* 9 */
    UEvent1j_latest                                         e1j;

}
DebugUIntraFreqEvent_Union_r7;

//ICAT EXPORTED STRUCT
typedef struct DebugUIntraFreqEvent_r7Tag
{
    T_UIntraFreqEvent_latest tag;

    DebugUIntraFreqEvent_Union_r7 Union; 
}
DebugUIntraFreqEvent_r7;    

//ICAT EXPORTED UNION : T_UReportingCellStatus
typedef union DebugUReportingCellStatus_UnionTag
{
    /* 0 */
    UMaxNumberOfReportingCellsType1                    withinActiveSet;
    /* 1 */
    UMaxNumberOfReportingCellsType1                    withinMonitoredSetUsedFreq;
    /* 2 */
    UMaxNumberOfReportingCellsType1                    withinActiveAndOrMonitoredUsedFreq;
    /* 3 */
    UMaxNumberOfReportingCellsType1                    withinDetectedSetUsedFreq;
    /* 4 */
    UMaxNumberOfReportingCellsType1                    withinMonitoredAndOrDetectedUsedFreq;
    /* 5 */
    UMaxNumberOfReportingCellsType3                    allActiveplusMonitoredSet;
    /* 6 */
    UMaxNumberOfReportingCellsType3                    allActivePlusDetectedSet;
    /* 7 */
    UMaxNumberOfReportingCellsType3                    allActivePlusMonitoredAndOrDetectedSet;
    /* 8 */
    UMaxNumberOfReportingCellsType1                    withinVirtualActSet;
    /* 9 */
    UMaxNumberOfReportingCellsType1                    withinMonitoredSetNonUsedFreq;
    /* 10 */
    UMaxNumberOfReportingCellsType1                    withinMonitoredAndOrVirtualActiveSetNonUsedFreq;
    /* 11 */
    UMaxNumberOfReportingCellsType3                    allVirtualActSetplusMonitoredSetNonUsedFreq;
    /* 12 */
    UMaxNumberOfReportingCellsType2                    withinActSetOrVirtualActSet_InterRATcells;
    /* 13 */
    UMaxNumberOfReportingCellsType2                    withinActSetAndOrMonitoredUsedFreqOrVirtualActSetAndOrMonitoredNonUsedFreq;
    
}
DebugUReportingCellStatus_Union;

//ICAT EXPORTED STRUCT
typedef struct DebugUReportingCellStatusTag
{
    T_UReportingCellStatus tag;

    DebugUReportingCellStatus_Union Union;
}
DebugUReportingCellStatus;

//ICAT EXPORTED STRUCT
typedef struct DebugUIntraFreqEventCriteria_r7Tag
{
    DebugUIntraFreqEvent_r7                                 event;
    UHysteresis                                             hysteresis;
    UTimeToTrigger                                          timeToTrigger;
    Boolean													reportingCellStatusPresent;
    DebugUReportingCellStatus                               reportingCellStatus;
}
DebugUIntraFreqEventCriteria_r7;

typedef Int16  /* 0 to 511 */                              UPrimaryScramblingCode;

//ICAT EXPORTED STRUCT
typedef struct DebugMcrPeriodicMeasDataTag
{
	Boolean                     IsFirstReport;
}
DebugMcrPeriodicMeasData;

//ICAT EXPORTED STRUCT
typedef struct DebugUReportingCellStatusOptTag
{
    Boolean                                                 reportingCellStatusPresent;
    DebugUReportingCellStatus                               reportingCellStatus;
}
DebugUReportingCellStatusOpt;

//ICAT EXPORTED STRUCT
typedef struct DebugUPeriodicalWithReportingCellStatusTag
{
    UPeriodicalReportingCriteria                            periodicalReportingCriteria;
    Boolean                                                 reportingCellStatusPresent;
    DebugUReportingCellStatus                               reportingCellStatus;
}
DebugUPeriodicalWithReportingCellStatus;

//ICAT EXPORTED STRUCT
typedef struct DebugUIntraFreqEventCriteriaList_r7Tag
{
    DebugUIntraFreqEventCriteria_r7                         data[maxIntraDebugDummys];
}
DebugUIntraFreqEventCriteriaList_r7;

//ICAT EXPORTED STRUCT
typedef struct DebugUIntraFreqReportingCriteria_r7Tag
{
    Boolean                                                 eventCriteriaListPresent;
    DebugUIntraFreqEventCriteriaList_r7                    eventCriteriaList;
}
DebugUIntraFreqReportingCriteria_r7;

//ICAT EXPORTED UNION : T_UIntraFreqReportCriteria_r7
typedef union DebugIntraFreqReportCriteria_Union_r7Tag
{
    /* 0 */
    DebugUIntraFreqReportingCriteria_r7                     intraFreqReportingCriteria;
    /* 1 */
    DebugUPeriodicalWithReportingCellStatus                 periodicalReportingCriteria;
    /* 2 */
    DebugUReportingCellStatusOpt                            noReporting;  
}
DebugIntraFreqReportCriteria_Union;

//ICAT EXPORTED STRUCT
typedef struct DebugUIntraFreqReportCriteria_r7Tag
{
    T_UIntraFreqReportCriteria_latest                       tag;

    DebugIntraFreqReportCriteria_Union                      Union;   

}
DebugUIntraFreqReportCriteria_r7;

//ICAT EXPORTED UNION : T_UCellReportingQuantities_modeInfo
typedef union DebugUCellReportingQuantities_modeInfo_UnionTag
{
    /* 0 */
    UCellReportingQuantities_fdd                       fdd;
    /* 1 */
    UCellReportingQuantities_tdd                       tdd;

}
DebugUCellReportingQuantities_modeInfo_Union;

//ICAT EXPORTED STRUCT
typedef struct DebugUCellReportingQuantities_modeInfoTag
{
    T_UCellReportingQuantities_modeInfo tag;

    DebugUCellReportingQuantities_modeInfo_Union Union;  // U

}
DebugUCellReportingQuantities_modeInfo;

//ICAT EXPORTED STRUCT
typedef struct DebugUCellReportingQuantitiesTag
{
    Boolean                                                 cellIdentity_reportingIndicator;
    Boolean                                                 cellSynchronisationInfoReportingIndicator;
    DebugUCellReportingQuantities_modeInfo                  modeSpecificInfo;
}
DebugUCellReportingQuantities;

//ICAT EXPORTED STRUCT
typedef struct DebugUIntraFreqReportingQuantityTag
{
    DebugUCellReportingQuantities                           activeSetReportingQuantities;
    DebugUCellReportingQuantities                           monitoredSetReportingQuantities;
    Boolean                                                 detectedSetReportingQuantitiesPresent;
    DebugUCellReportingQuantities                           detectedSetReportingQuantities;
}
DebugUIntraFreqReportingQuantity;

//ICAT EXPORTED STRUCT
typedef struct DebugUCellsForIntraFreqMeasListTag
{
    Int16 n; /* 1 to maxCellMeas */
    UIntraFreqCellID                                      data [maxCellMeas];
}
DebugUCellsForIntraFreqMeasList;

//ICAT EXPORTED STRUCT
typedef struct DebugPrintHeaderTag
{  
    UMeasurementReportingMode                   measReportingMode;
    Boolean										additionalMeasListPresent;
    UAdditionalMeasurementID_List               additionalMeasList;

    Boolean                                     measInProgress;
    Boolean                                     isSysInfo;
    Int8                                        MeasIdIndex;  

    T_UMeasurementType_latest                   tag;    
}
DebugPrintHeader;   

//ICAT EXPORTED ENUM
typedef enum DebugDiagPrintValidityCheckNumberTag
{
    IntraDiagPrintValidityCheckNumber = 8,
    InterDiagPrintValidityCheckNumber = 16,
    GsmDiagPrintValidityCheckNumber = 32
}
DebugDiagPrintValidityCheckNumber;

//ICAT EXPORTED STRUCT
typedef struct DebugMcrIntraFreqMeasTag
{   
    DebugPrintHeader                             printHeader;

    Boolean                                      cellsForIntraFreqMeasListPresent;
    UCellsForIntraFreqMeasList                   cellsForIntraFreqMeasList;
    McrMeasQuantity                              measQuantity;
    DebugUIntraFreqReportingQuantity             reportingQuantity;
    Boolean                                      measurementValidityPresent;
    UMeasurementValidity                         measurementValidity;
    DebugUIntraFreqReportCriteria_r7             reportCriteria;
    McrNonCriticalExtensions                     nonCriticalExtensions;
    T_UIntraFreqReportCriteria_latest            previousReportCriteriaBeforeModify;
    DebugDiagPrintValidityCheckNumber            diagPrintValidityCheckNumber;
}
DebugMcrIntraFreqMeas;

//ICAT EXPORTED STRUCT
typedef struct DebugMcrGsmEventsDataTag
{
    McrGsmCellEventList             trigg3aEvent_p;
    McrGsmCellEventList             trigg3bEvent_p;
    McrGsmCellEventList             trigg3cEvent_p;
    McrBestCell3dEvent              bestCell3dEvent;
}
DebugMcrGsmEventsData;

//ICAT EXPORTED STRUCT
typedef struct DebugMcrGsmReportDataTag
{
    DebugMcrPeriodicMeasData                 periodicMeasData;
    DebugMcrGsmEventsData                    eventsData;
}
DebugMcrGsmReportData;

//ICAT EXPORTED STRUCT
typedef struct DebugUEvent3aTag
{
    UThreshold_latest                                  thresholdOwnSystem;
    UW                                                 w;
    UThreshold_latest                                  thresholdOtherSystem;
    UHysteresis                                        hysteresis;
    UTimeToTrigger                                     timeToTrigger;
    Boolean                                            reportingCellStatusPresent;
    DebugUReportingCellStatus                          reportingCellStatus;
}
DebugUEvent3a;

//ICAT EXPORTED STRUCT
typedef struct DebugUEvent3bTag
{
    UThreshold_latest                                       thresholdOtherSystem;
    UHysteresis                                             hysteresis;
    UTimeToTrigger                                          timeToTrigger;
    Boolean                                                 reportingCellStatusPresent;
    DebugUReportingCellStatus                               reportingCellStatus;
}
DebugUEvent3b;

//ICAT EXPORTED STRUCT
typedef struct DebugUEvent3cTag
{
    UThreshold_latest                                       thresholdOtherSystem;
    UHysteresis                                             hysteresis;
    UTimeToTrigger                                          timeToTrigger;
    Boolean                                                 reportingCellStatusPresent;
    DebugUReportingCellStatus                               reportingCellStatus;
}
DebugUEvent3c;

//ICAT EXPORTED STRUCT
typedef struct DebugUEvent3dTag
{
    UHysteresis                                             hysteresis;
    UTimeToTrigger                                          timeToTrigger;
    Boolean                                                 reportingCellStatusPresent;
    DebugUReportingCellStatus                               reportingCellStatus;
}
DebugUEvent3d;

//ICAT EXPORTED UNION : T_UInterRATEvent
typedef union DebugUInterRATEvent_UnionTag
{
    /* 0 */
    DebugUEvent3a                                           event3a;
    /* 1 */
    DebugUEvent3b                                           event3b;
    /* 2 */
    DebugUEvent3c                                           event3c;
    /* 3 */
    DebugUEvent3d                                           event3d;    
}
DebugUInterRATEvent_Union;
    
//ICAT EXPORTED STRUCT
typedef struct DebugUInterRATEventTag
{
    T_UInterRATEvent tag;
    
    DebugUInterRATEvent_Union Union; 
}
DebugUInterRATEvent;

//ICAT EXPORTED STRUCT
typedef struct DebugUInterRATEventListTag
{
    DebugUInterRATEvent                                     data[maxInterRATDebugDummys];
}
DebugUInterRATEventList;

//ICAT EXPORTED STRUCT
typedef struct DebugUInterRATReportingCriteriaTag
{
    Boolean                                                 interRATEventListPresent;
    DebugUInterRATEventList                                 interRATEventList;
}
DebugUInterRATReportingCriteria;

//ICAT EXPORTED UNION : T_UInterRATReportCriteria
typedef union DebugUInterRATReportCriteriaUnionTag
{
    DebugUInterRATReportingCriteria                         interRATReportingCriteria;
    
    DebugUPeriodicalWithReportingCellStatus                 periodicalReportingCriteria;
    
    DebugUReportingCellStatusOpt                            noReporting;

}
DebugUInterRATReportCriteriaUnion;

//ICAT EXPORTED STRUCT
typedef struct DebugUInterRATReportCriteriaTag
{
    T_UInterRATReportCriteria tag;

    DebugUInterRATReportCriteriaUnion Union;  
}
DebugUInterRATReportCriteria;

//ICAT EXPORTED STRUCT
typedef struct DebugMcrGsmMeasTag
{  
    DebugPrintHeader                        printHeader;    
    
    Boolean                                 cellsForGsmMeasListPresent;
    UCellsForInterRATMeasList               cellsForGsmMeasList;
    Boolean                                 gsmMeasQuantityPresent;
    McrGsmMeasQuantity                      gsmMeasQuantity;
    Boolean                                 gsmReportingQuantityPresent;
    UInterRATReportingQuantity_gsm          gsmReportingQuantity;
    DebugUInterRATReportCriteria            reportCriteria;
    DebugMcrGsmReportData                   reportData;

    DebugDiagPrintValidityCheckNumber       diagPrintValidityCheckNumber;

}
DebugMcrGsmMeas;

//ICAT EXPORTED STRUCT
typedef struct DebugUCellsForInterFreqMeasListTag
{
    Int16 n; /* 1 to maxCellMeas */
    UInterFreqCellID                                   data [maxCellMeas];
}
DebugUCellsForInterFreqMeasList;

//ICAT EXPORTED STRUCT
typedef struct DebugUInterFreqReportingQuantityTag
{
    Boolean                                             utra_Carrier_RSSI;
    Boolean                                             frequencyQualityEstimate;
    DebugUCellReportingQuantities                       nonFreqRelatedQuantities;
}
DebugUInterFreqReportingQuantity;

//ICAT EXPORTED STRUCT
typedef struct DebugUNonUsedFreqParameterList_r6Tag
{
    UNonUsedFreqParameter_r6                           data[maxInterDebugDummys];
}
DebugUNonUsedFreqParameterList_r6;

//ICAT EXPORTED STRUCT
typedef struct DebugXr3UEvent2a_r6Tag
{
    Boolean                                            nonUsedFreqParameterListPresent;
    DebugUNonUsedFreqParameterList_r6                  nonUsedFreqParameterList;
}
DebugXr3UEvent2a_r6;

//ICAT EXPORTED STRUCT
typedef struct DebugUEvent2a_r6Tag
{
    UW                                                 usedFreqW;
    UHysteresisInterFreq                               hysteresis;
    UTimeToTrigger                                     timeToTrigger;
    Boolean                                            reportingCellStatusPresent;
    DebugUReportingCellStatus                          reportingCellStatus;
    Boolean                                            nonUsedFreqParameterListPresent;
    UNonUsedFreqWList_r6                               nonUsedFreqParameterList;
    DebugXr3UEvent2a_r6                                legacyR3_p;
}
DebugUEvent2a_r6;

//ICAT EXPORTED STRUCT
typedef struct DebugUEvent2b_r6Tag
{
    UThreshold_latest                                           usedFreqThreshold;
    UW                                                      usedFreqW;
    UHysteresisInterFreq                                    hysteresis;
    UTimeToTrigger                                          timeToTrigger;
    Boolean                                                 reportingCellStatusPresent;
    DebugUReportingCellStatus                               reportingCellStatus;
    Boolean                                                 nonUsedFreqParameterListPresent;
    DebugUNonUsedFreqParameterList_r6                       nonUsedFreqParameterList;
}
DebugUEvent2b_r6;

//ICAT EXPORTED STRUCT
typedef struct DebugUEvent2c_r6Tag
{
    UHysteresisInterFreq                                    hysteresis;
    UTimeToTrigger                                          timeToTrigger;
    Boolean                                                 reportingCellStatusPresent;
    DebugUReportingCellStatus                               reportingCellStatus;
    Boolean                                                 nonUsedFreqParameterListPresent;
    DebugUNonUsedFreqParameterList_r6                       nonUsedFreqParameterList;
}
DebugUEvent2c_r6;

//ICAT EXPORTED STRUCT
typedef struct DebugUEvent2d_r6Tag
{
    UThreshold_latest                                           usedFreqThreshold;
    UW                                                      usedFreqW;
    UHysteresisInterFreq                                    hysteresis;
    UTimeToTrigger                                          timeToTrigger;
    Boolean                                                 reportingCellStatusPresent;
    DebugUReportingCellStatus                               reportingCellStatus;
}
DebugUEvent2d_r6;

//ICAT EXPORTED STRUCT
typedef struct DebugUEvent2e_r6Tag
{
    UHysteresisInterFreq                                    hysteresis;
    UTimeToTrigger                                          timeToTrigger;
    Boolean                                                 reportingCellStatusPresent;
    DebugUReportingCellStatus                               reportingCellStatus;
    Boolean                                                 nonUsedFreqParameterListPresent;
    DebugUNonUsedFreqParameterList_r6                       nonUsedFreqParameterList;
}
DebugUEvent2e_r6;

//ICAT EXPORTED STRUCT
typedef struct DebugUEvent2f_r6Tag
{
    UThreshold_latest                                      usedFreqThreshold;
    UW                                                 usedFreqW;
    UHysteresisInterFreq                               hysteresis;
    UTimeToTrigger                                     timeToTrigger;
    Boolean                                            reportingCellStatusPresent;
    DebugUReportingCellStatus                          reportingCellStatus;
}
DebugUEvent2f_r6;

//ICAT EXPORTED UNION : T_UInterFreqEvent_r6
typedef union DebugUInterFreqEvent_Union_r6Tag
{
    /* 0 */
    DebugUEvent2a_r6                                        event2a;
    /* 1 */
    DebugUEvent2b_r6                                        event2b;
    /* 2 */
    DebugUEvent2c_r6                                        event2c;
    /* 3 */
    DebugUEvent2d_r6                                        event2d;
    /* 4 */
    DebugUEvent2e_r6                                        event2e;
    /* 5 */
    DebugUEvent2f_r6                                        event2f;
}
DebugUInterFreqEvent_Union_r6;

//ICAT EXPORTED STRUCT
typedef struct DebugUInterFreqEvent_r6Tag
{
    T_UInterFreqEvent_latest        tag;

    DebugUInterFreqEvent_Union_r6   Union; 
}
DebugUInterFreqEvent_r6;

//ICAT EXPORTED STRUCT
typedef struct DebugUInterFreqEventList_r6Tag
{
    DebugUInterFreqEvent_r6										data[maxInterDebugDummys];
}
DebugUInterFreqEventList_r6;

//ICAT EXPORTED STRUCT
typedef struct DebugUInterFreqReportingCriteria_r6Tag
{
    Boolean                                            interFreqEventListPresent;
    DebugUInterFreqEventList_r6                        interFreqEventList;
}
DebugUInterFreqReportingCriteria_r6;

//ICAT EXPORTED UNION : T_UInterFreqReportCriteria_r6
typedef union DebugUInterFreqReportCriteria_Union_r7Tag
{
    /* 0 */
    DebugUIntraFreqReportingCriteria_r7                     intraFreqReportingCriteria;
    /* 1 */
    DebugUInterFreqReportingCriteria_r6                     interFreqReportingCriteria;
    /* 2 */
    DebugUPeriodicalWithReportingCellStatus                 periodicalReportingCriteria;
    /* 3 */
    DebugUReportingCellStatusOpt                            noReporting;

}
DebugUInterFreqReportCriteria_Union_r7;

//ICAT EXPORTED STRUCT
typedef struct DebugUInterFreqReportCriteria_r7Tag
{
    T_UInterFreqReportCriteria_latest           tag;

    DebugUInterFreqReportCriteria_Union_r7      Union; 

}
DebugUInterFreqReportCriteria_r7;

//ICAT EXPORTED STRUCT
typedef struct DebugMcrInterFMEventsDataTag
{
    /* timeToTrigger timers list for inter-frequency events */
    UUARFCN                         bestFreq2aEvent;
    McrFrequencyList                trigg2bEvent;
    McrFrequencyList                trigg2cEvent;
    Boolean                         trigg2dEvent;
    McrFrequencyList                trigg2eEvent;
    Boolean                         trigg2fEvent;
}
DebugMcrInterFMEventsData;

//ICAT EXPORTED STRUCT
typedef struct DebugMcrInterFreqReportDataTag
{
    DebugMcrPeriodicMeasData                 periodicMeasData;
    DebugMcrInterFMEventsData                eventsData;
}
DebugMcrInterFreqReportData;

//ICAT EXPORTED STRUCT
typedef struct DebugMcrInterFreqMeasTag
{   
    DebugPrintHeader                             printHeader;    
    
    Boolean										 cellsForInterFreqMeasListPresent;
	// Note: list of cell to measure in Inter
    DebugUCellsForInterFreqMeasList              cellsForInterFreqMeasList;
	// Note: which quantity to measure (ECON...)
    McrMeasQuantity                              measQuantity;
    Int8										 activeCellFilterIndex;
	// Note: which quantitys to report 
    DebugUInterFreqReportingQuantity             reportingQuantity;
    Boolean										 measurementValidityPresent;
    UMeasurementValidity                         measurementValidity;
    Boolean										 setUpdatePresent;
    T_UUE_AutonomousUpdateMode                   updateMode;
    DebugUInterFreqReportCriteria_r7             reportCriteria;
	// Note: Events triggering parameters data
    DebugUIntraFreqEventCriteria_r7              event1a;
    DebugUIntraFreqEventCriteria_r7              event1b;
    DebugUIntraFreqEventCriteria_r7              event1c;
    /* virtual active sets for all non-used frequencies */
    VirtualActiveSetList                         virtualActiveSetList;
    DebugMcrInterFreqReportData                  reportData;
    Boolean 									 event2D2fExist;	

    DebugDiagPrintValidityCheckNumber            diagPrintValidityCheckNumber;

}
DebugMcrInterFreqMeas;

//ICAT EXPORTED UNION : T_UMeasurementType_r7
typedef union DebugMcrMeasType_UnionTag
{
    DebugMcrIntraFreqMeas                   intraFreqMeas;
    DebugMcrInterFreqMeas                   interFreqMeas;
    DebugMcrGsmMeas                         gsmMeas;
}
DebugMcrMeasType_Union;

//ICAT EXPORTED STRUCT
typedef struct DebugMcrMeasTypeTag
{
    T_UMeasurementType_latest                   tag;
    
    DebugMcrMeasType_Union                      u_McrMeasType; 
}
DebugMcrMeasType;

//ICAT EXPORTED STRUCT
typedef struct DebugUUE_InternalMeasQuantityTag
{
    UUE_MeasurementQuantity                                 measurementQuantity;
    Boolean													filterCoefficientPresent;
    UFilterCoefficient                                      filterCoefficient;
}
DebugUUE_InternalMeasQuantity;

//ICAT EXPORTED STRUCT
typedef struct DebugMcrUeInternalRxTxTimeDiffResultTag
{
    UPrimaryScramblingCode                   primaryScramblingCode ;
    UUE_RX_TX_TimeDifferenceType1            rxtxTimeDiff;
    Int32									 rxtxTimeDiffFiltered [MAX_FILTERS];
}
DebugMcrUeInternalRxTxTimeDiffResult;

//ICAT EXPORTED STRUCT
typedef struct DebugMcrUeInternalReportDataTag
{
    DebugMcrPeriodicMeasData                 periodicTimer;

    /* list of timetotrigger timers */
}
DebugMcrUeInternalTimerData; // converted from a union

//ICAT EXPORTED STRUCT
typedef struct DebugMcrUeInternalEventResultsTag
{
    DebugMcrUeInternalTimerData         timerData;
    Boolean                             trigg6aEvent;
    Boolean                             trigg6bEvent;
    Boolean                             trigg6cEvent;
    Boolean                             trigg6dEvent;
    Boolean                             trigg6eEvent;
}
DebugMcrUeInternalEventResults;

//ICAT EXPORTED STRUCT
typedef struct DebugUUE_InternalReportingCriteriaTag
{
    Boolean                                            ue_InternalEventParamListPresent;
    //DebugUUE_InternalEventParamList                         ue_InternalEventParamList;
	// Debug: removed due to childrens degeneracy
}
DebugUUE_InternalReportingCriteria;

//ICAT EXPORTED UNION : T_UUE_InternalReportCriteria
typedef union DebugUUE_InternalReportCriteria_UnionTag
{
    /* 0 */
    DebugUUE_InternalReportingCriteria                 ue_InternalReportingCriteria;
    /* 1 */
    UPeriodicalReportingCriteria                       periodicalReportingCriteria;
    /* 2 */
    Int8                                               _dummy3_;

}
DebugUUE_InternalReportCriteria_Union;

//ICAT EXPORTED STRUCT
typedef struct DebugUUE_InternalReportCriteriaTag
{
    T_UUE_InternalReportCriteria tag;

    DebugUUE_InternalReportCriteria_Union Union;    
}
DebugUUE_InternalReportCriteria;	

//ICAT EXPORTED STRUCT
typedef struct DebugMcrUeInternalReportingQuantityTag
{
    Boolean     ue_TransmittedPower;
    Boolean     ue_RX_TX_TimeDifference;
}
DebugMcrUeInternalReportingQuantity;

//ICAT EXPORTED UNION : T_UUL_TrCH_Identity
typedef union DebugUUL_TrCH_Identity_UnionTag
{
    /* 0 */
    UTransportChannelIdentity                               dch;
    /* 1 */
    Int8                                               _dummy2_;
    /* 2 */
    UTransportChannelIdentity                              usch;    
}
DebugUUL_TrCH_Identity_Union;

//ICAT EXPORTED STRUCT
typedef struct DebugUUL_TrCH_IdentityTag
{
    T_UUL_TrCH_Identity tag;

    DebugUUL_TrCH_Identity_Union Union;
}
DebugUUL_TrCH_Identity;

//ICAT EXPORTED STRUCT
typedef struct DebugUTransChCriteriaTag
{
    Boolean                                            ul_transportChannelIDPresent;
    DebugUUL_TrCH_Identity                             ul_transportChannelID;
    Boolean                                            eventSpecificParametersPresent;
    UTransChCriteria_eventSpecificParameters           eventSpecificParameters;
}
DebugUTransChCriteria;

//ICAT EXPORTED STRUCT
typedef struct DebugUAdditionalMeasurementID_ListTag
{
    Int16 n; /* 1 to maxAdditionalMeas */
    UMeasurementIdentity                                    data [maxAdditionalMeas];
}
DebugUAdditionalMeasurementID_List;

//ICAT EXPORTED STRUCT
typedef struct DebugMcrMeasControlMsgTag
{
	// Note: Inter, Intra or GSM measurment
    DebugMcrMeasType                             measType;
	// Note: Event or periodical measurment
    UMeasurementReportingMode                    measReportingMode;
    Boolean										 additionalMeasListPresent;
    UAdditionalMeasurementID_List                additionalMeasList;
}
DebugMcrMeasControlMsg;

//ICAT EXPORTED STRUCT
typedef struct DebugMcrMeasIdentityTag
{  
	// Note: defines the control meas. struct dicatated by the cell to the UE
    DebugMcrMeasControlMsg                  measControlMsg;

    /* Indicates whether particular measurement is in progress. */
    Boolean                                 measInProgress;

    /* Indicates if this measurement information was received in SIB11/SIB12
    or in measurementControl message. */
    Boolean                                 isSysInfo;

    Int8                                    MeasIdIndex;   
}
DebugMcrMeasIdentity;

//ICAT EXPORTED STRUCT
typedef struct DebugIaMeasDataTag
{
    CellIdMask                  occupied;
    IaCellInfo                  cellInfo [maxCellMeas];
    McrFilters                  filters;
    CellIdMask                  measResultsMask;
    McrMeasStatus               measStatus;
}
DebugIaMeasData;

//ICAT EXPORTED STRUCT
typedef struct DebugIeMeasDataTag
{
    CellIdMask                  occupied;
    IeCellInfo                  cellInfo [maxCellMeas];
    McrInterFreqList            freqList;
    McrFilters                  filters;
    CellIdMask                  measResultsMask;
    McrMeasStatus               measStatus;
}
DebugIeMeasData;

//ICAT EXPORTED STRUCT
typedef struct DebugIrMeasDataTag
{
    CellIdMask                  occupied;
    IrCellInfo                  cellInfo [maxCellMeas];
    McrFilters                  filters;
    CellIdMask                  measResultsMask;
    IrMeasStatus                measStatus;
}
DebugIrMeasData;

//ICAT EXPORTED STRUCT
typedef struct DebugCellMeasDataDebugTag
{
    DebugIaMeasData             MeasIaData;
    DebugIeMeasData             MeasIeData;
    DebugIrMeasData             MeasIrData;
} 
DebugCellMeasDataDebug; // Note: Unified struct for urrMcrMeasIaData, urrMcrMeasIeData , urrMcrMeasIrData
    

/***************************************************************************
 *  Macros
 **************************************************************************/

/***************************************************************************
 * Global Data declarations
***************************************************************************/


/***************************************************************************
 *  Function Prototypes
 **************************************************************************/

 /*****************************************************************************
  * Debug related functions
  */
 
 void    McrUpdateAndSendDebugMeasIdentityStruct ( UrrMcrData *UrrMcrData_p        );

#endif //UPGRADE_ENABLE_MCR_DEBUG

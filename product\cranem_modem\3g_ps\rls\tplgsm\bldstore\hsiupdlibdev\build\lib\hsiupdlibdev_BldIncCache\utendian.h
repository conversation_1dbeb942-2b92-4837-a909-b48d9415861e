/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/tplgsm/utinc/utendian.h#8 $
 *   $Revision: #8 $
 *   $DateTime: 2006/09/20 15:49:50 $
 **************************************************************************
 * File Description:
 *
 * GPRS Endianness definition. Change endianness to compile on different
 * platforms
 **************************************************************************/

#ifndef UTENDIAN_H
#define UTENDIAN_H

#if !defined BYTE_ORDER
/*
Definitions for byte order,
according to byte significance from low address to high.
*/
#define LITTLE_ENDIAN 1234    /* least-significant byte first */
#define BIG_ENDIAN    4321    /* most-significant byte first */
#define PDP_ENDIAN    3412    /* LSB first in word, MSW first in long (pdp) */

#define BYTE_ORDER    LITTLE_ENDIAN

#endif

#define __byte_swap_long(x) ((((x) >> 24) & 0xFF) + (((x) >> 8) & 0xFF00) + (((x) << 8) & 0xFF0000) + ((x)<<24) )

#define __byte_swap_word(x) (((((x) >> 8) & 0xFF) + ((x) << 8)) & 0xFFFF)

/*
 * Macros for network/external number representation conversion.
 */
#if BYTE_ORDER == BIG_ENDIAN
#if !(defined (LTE_W_PS) && defined (PC_TTCN_INT_TEST) )   //2013.3.27 Jiantao removed for LTE+W+G UE PC VS project
													//Not include these in case when both LTE_W_PS and PC_TTCN_INT_TEST are defined
													//Which impacts the LTE+W compilation on TTCN PC test
#define ntohl(x)    (x)
#define ntohs(x)    (x)
#define htonl(x)    (x)
#define	htons(x)	(x)
#endif
#define	NTOHL(x)	
#define	NTOHS(x)	
#define	HTONL(x)	
#define	HTONS(x)	

#elif BYTE_ORDER == LITTLE_ENDIAN



#if !(defined (LTE_W_PS) && defined (PC_TTCN_INT_TEST) )   //2013.3.27 Jiantao removed for LTE+W+G UE PC VS project
													//Not include these in case when both LTE_W_PS and PC_TTCN_INT_TEST are defined
													//Which impacts the LTE+W compilation on TTCN PC test
#define	ntohl	__byte_swap_long
#define	ntohs	__byte_swap_word
#define	htonl	__byte_swap_long
#define	htons	__byte_swap_word
#endif                         
#define NTOHL(x)        (x) = ntohl((Int32)x)
#define NTOHS(x)        (x) = ntohs((Int16)x)
#define HTONL(x)        (x) = htonl((Int32)x)
#define HTONS(x)        (x) = htons((Int16)x)
#else
#error  BYTE_ORDER must be defined
#endif



#endif

/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/devices/transports/usb.mod/api/inc/usbsigdef.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/24 10:16:13 $
 **************************************************************************
 *  File Description :
 *    Structure types used by GKI signals in the USB protocol stack.
 **************************************************************************/

/*******************************************************************************
 * Signal definitions
 ******************************************************************************/

/* Define the USB signal group base. */
SIG_DEF(SIG_USB_DUMMY = USB_SIGNAL_BASE,
        EmptySignal usbEmptySignal)

/*
 * Error signals.
 */

/* Notify that an error condition has occurred. */
SIG_DEF(SIG_USB_NOTIFY_ERROR_IND,
        UsbNotifyErrorInd usbNotifyErrorInd)

/*
 * Device request signals.
 */

/* Indicate if a device request has been received. */
SIG_DEF(SIG_USB_DEVICE_REQUEST_IND,
        UsbDeviceRequestInd usbDeviceRequestInd)

/* Indicate if a class device request has been received. */
SIG_DEF(SIG_USB_CLASS_DEVICE_REQUEST_IND,
        UsbDeviceRequestInd usbClassDeviceRequestInd)

/* Indicate if a vendor device request has been received. */
SIG_DEF(SIG_USB_VENDOR_DEVICE_REQUEST_IND,
        UsbDeviceRequestInd usbVendorDeviceRequestInd)

/*
 * Data transmission signals.
 */

/* Request to initialise transmit request queue. */
SIG_DEF(SIG_USB_INITIALISE_TRANSMIT_REQUEST_QUEUE_REQ,
        UsbInitialiseTransmitRequestQueueReq usbInitialiseTransmitRequestQueueReq)

/* Confirmation of transmit request queue initialisation. */
SIG_DEF(SIG_USB_INITIALISE_TRANSMIT_REQUEST_QUEUE_CNF,
        UsbInitialiseTransmitRequestQueueCnf usbInitialiseTransmitRequestQueueCnf)

/* Request to transmit data over USB. */
SIG_DEF(SIG_USB_TRANSMIT_DATA_REQ,
        UsbTransmitDataReq usbTransmitDataReq)

/* Confirmation of acceptance of transmit request. */
SIG_DEF(SIG_USB_TRANSMIT_DATA_CNF,
        UsbTransmitDataCnf usbTransmitDataCnf)

/* Indicates that a USB packet has been successfully transmitted. */
SIG_DEF(SIG_USB_TRANSMIT_PACKET_IND,
        UsbTransmitPacketInd usbTransmitPacketInd)

/* Issued when transmit request completes. */
SIG_DEF(SIG_USB_TRANSMIT_COMPLETE_IND,
        UsbTransmitCompleteInd usbTransmitCompleteInd)

/* Flush a transmit request queue. */
SIG_DEF(SIG_USB_FLUSH_TRANSMIT_REQUEST_QUEUE_REQ,
        UsbFlushTransmitRequestQueueReq usbFlushTransmitRequestQueueReq)

/* Confirmation of flush a transmit request queue. */
SIG_DEF(SIG_USB_FLUSH_TRANSMIT_REQUEST_QUEUE_CNF,
        UsbFlushTransmitRequestQueueCnf usbFlushTransmitRequestQueueCnf)

/*
 * Data reception signals.
 */

/* Initialisation request for Rx buffer. */
SIG_DEF(SIG_USB_INITIALISE_RECEIVE_BUFFER_REQ,
        UsbInitialiseReceiveBufferReq usbInitialiseReceiveBufferReq)

/* Confirmation of initialisation */
SIG_DEF(SIG_USB_INITIALISE_RECEIVE_DATA_CNF,
        UsbInitialiseReceiveBufferCnf usbInitialiseReceiveBufferCnf)

/* Issued when receive data arrives */
SIG_DEF(SIG_USB_RECEIVE_DATA_IND,
        UsbReceiveDataInd usbReceiveDataInd)

/* Response when received data has been handled. */
SIG_DEF(SIG_USB_RECEIVE_DATA_RSP,
        UsbReceiveDataRsp usbReceiveDataRsp)

/* Pause Rx Signals notifications */
SIG_DEF(SIG_USB_PAUSE_RECEIVE_DATA_REQ,
        UsbPauseReceiveDataReq usbPauseReceiveDataReq)

/* Request to flush the receive buffer for the specified endpoint */
SIG_DEF(SIG_USB_FLUSH_RECEIVE_BUFFER_REQ,
        UsbFlushReceiveBufferReq usbFlushReceiveBufferReq)

/* Confirmation of flush */
SIG_DEF(SIG_USB_FLUSH_RECEIVE_BUFFER_CNF,
        UsbFlushReceiveBufferCnf usbFlushReceiveBufferCnf)

/* Request to read received data (from the USBD or DMA buffers. */
SIG_DEF(SIG_USB_READ_DATA_REQ,
        UsbReadDataReq usbReadDataReq)

/* Generated when usbDclGetRxBufferWritePtr() cannot accept all of the received
   data. */
SIG_DEF(SIG_USB_RX_DATA_PENDING_IND,
        UsbRxDataPendingInd usbRxDataPendingInd)

/* Request to get the ammount of free space in an rx buffer. */
SIG_DEF(SIG_USB_RECEIVE_BUFFER_FREE_SPACE_REQ,
        UsbReceiveBufferFreeSpaceReq usbReceiveBufferFreeSpaceReq)

/* Confirmation of the ammount of free space in an rx buffer. */
SIG_DEF(SIG_USB_RECEIVE_BUFFER_FREE_SPACE_CNF,
        UsbReceiveBufferFreeSpaceCnf usbReceiveBufferFreeSpaceCnf)

/*
 * USB protocol stack context signals.
 */

/* Requests the device status. */
SIG_DEF(SIG_USB_GET_DEVICE_CONTROLLER_IS_PRESENT_REQ,
        UsbGetDeviceControllerIsPresentReq usbGetDeviceControllerIsPresentReq)

/* Returns the device status. */
SIG_DEF(SIG_USB_GET_DEVICE_CONTROLLER_IS_PRESENT_IND,
        UsbGetDeviceControllerIsPresentInd usbGetDeviceControllerIsPresentInd)

/* Requests the device status as defined in GetStatus(Device) requests. */
SIG_DEF(SIG_USB_GET_STATUS_DEVICE_REQ,
        UsbGetStatusDeviceReq usbGetStatusDeviceReq)

/* Returns the device status as defined in GetStatus(Device) requests. */
SIG_DEF(SIG_USB_GET_STATUS_DEVICE_IND,
        UsbGetStatusDeviceInd usbGetStatusDeviceInd)

/* Indicates that the device configuration has been successfully updated. */
SIG_DEF(SIG_USB_SET_CONFIGURATION_IND,
        UsbSetConfigurationInd usbSetConfigurationInd)

/* Indicates that a device interface has been successfully updated. */
SIG_DEF(SIG_USB_SET_INTERFACE_IND,
        UsbSetInterfaceInd usbSetInterfaceInd)

/*
 * Configuration signals
 */

/* The USBD has detected a USB bus reset signal. */
SIG_DEF(SIG_USB_BUS_RESET_IND,
        EmptySignal usbBusResetInd)

/* The USB stack request that the application resets (initailises) itself. */
SIG_DEF(SIG_USB_RESET_APPLICATION_REQ,
        UsbResetApplicationReq usbResetApplicationReq)

/* The application has reset (initailised) itself. */
SIG_DEF(SIG_USB_RESET_APPLICATION_CNF,
        UsbResetApplicationCnf usbResetApplicationCnf)

/* Request the device to connect to the USB. */
SIG_DEF(SIG_USB_CONNECT_REQ,
        UsbConnectReq usbConnectReq)

/* Status of a device connection request. */
SIG_DEF(SIG_USB_CONNECT_CNF,
        UsbConnectCnf usbConnectCnf)

/* Request to set the Device Application task ID where USB signals are to
   be sent. */
SIG_DEF(SIG_USB_SET_DEV_APP_TASK_ID_REQ,
        UsbSetDevAppTaskIDReq usbSetDevAppTaskIDReq)

/* Request the USBD controller to generate remote wakeup signalling. */
SIG_DEF(SIG_USB_ACTIVATE_REMOTE_WAKEUP_REQ,
        UsbActivateRemoteWakeupReq usbActivateRemoteWakeupReq)

/* Confirmation of remote wakeup. */
SIG_DEF(SIG_USB_ACTIVATE_REMOTE_WAKEUP_CNF,
        UsbActivateRemoteWakeupCnf usbActivateRemoteWakeupCnf)

/*
 * Dynamic Configuration signals
 */

/* Request the device to connect to the USB. */
SIG_DEF(SIG_USB_DYNAMIC_CONFIGURE_REQ,
        UsbDynamicConfigureReq usbDynamicConfigureReq)

/* Status of a device connection request. */
SIG_DEF(SIG_USB_DYNAMIC_CONFIGURE_CNF,
        UsbDynamicConfigureCnf usbDynamicConfigureCnf)

/* Configuration Query */
SIG_DEF(SIG_USB_CONFIG_QUERY_REQ,
        UsbConfigQueryReq usbConfigQueryReq)

/* Configuration Query response */
SIG_DEF(SIG_USB_CONFIG_QUERY_CNF,
        UsbConfigQueryCnf usbConfigQueryCnf)

/*
 * Other signals
 */

/* Used by the USBD controller driver to tell the target that it can,
   or cannot, enter power saving mode. */
SIG_DEF(SIG_USB_ALLOW_POWER_SAVING_IND,
        UsbAllowPowerSavingInd usbAllowPowerSavingInd)

/* Signal used in software development and debug. */
SIG_DEF(SIG_USB_DEV_AND_DEBUG,
        UsbDevAndDebug usbDevAndDebug)

/* Signals used to test the Genie USB DLL. */
SIG_DEF(SIG_USB_GENIE_DLL_TEST_START_REQ,
        UsbGenieDllTestStartReq usbGenieDllTestStartReq)

SIG_DEF(SIG_USB_GENIE_DLL_TEST_START_CNF,
        UsbGenieDllTestStartCnf usbGenieDllTestStartCnf)

SIG_DEF(SIG_USB_GENIE_DLL_TEST_STOP_REQ,
        EmptySignal usbGenieDllTestStopReq)

SIG_DEF(SIG_USB_GENIE_DLL_TEST_STOP_CNF,
        UsbGenieDllTestStopCnf usbGenieDllTestStopCnf)

SIG_DEF(SIG_USB_GENIE_DLL_TEST_DATA_32_IND,
        UsbGenieDllTestData32Ind usbGenieDllTestData32Ind)

SIG_DEF(SIG_USB_GENIE_DLL_TEST_DATA_64_IND,
        UsbGenieDllTestData64Ind usbGenieDllTestData64Ind)

SIG_DEF(SIG_USB_GENIE_DLL_TEST_DATA_128_IND,
        UsbGenieDllTestData128Ind usbGenieDllTestData128Ind)

SIG_DEF(SIG_USB_GENIE_DLL_TEST_DATA_256_IND,
        UsbGenieDllTestData256Ind usbGenieDllTestData256Ind)

SIG_DEF(SIG_USB_GENIE_DLL_TEST_DATA_512_IND,
        UsbGenieDllTestData512Ind usbGenieDllTestData512Ind)

SIG_DEF(SIG_USB_GENIE_DLL_TEST_DATA_1024_IND,
        UsbGenieDllTestData1024Ind usbGenieDllTestData1024Ind)

SIG_DEF(SIG_USB_GENIE_DLL_TEST_DATA_2048_IND,
        UsbGenieDllTestData2048Ind usbGenieDllTestData2048Ind)

SIG_DEF(SIG_USB_GENIE_DLL_TEST_DATA_RSP,
        UsbGenieDllTestDataRsp usbGenieDllTestDataRsp)

#if defined (DEVELOPMENT_VERSION)
SIG_DEF(SIG_USB_DEV_USB_REG_STATUS,
        UsbRegStatus usbRegStatus)
#endif

/* Cable detection signal. */
SIG_DEF(SIG_USB_CABLE_STATE_CHANGE_IND,
        UsbDclUsbCableStateChangeInd usbDclUsbCableStateChangeInd)

/* Force the usb stack to start a reset sequence. */
SIG_DEF(SIG_USB_TRIGGER_RESET_REQ,
        UsbTriggerResetReq usbTriggerResetReq)

SIG_DEF(SIG_USB_TRIGGER_RESET_CNF,
        UsbTriggerResetCnf usbTriggerResetCnf)

/* END OF FILE */

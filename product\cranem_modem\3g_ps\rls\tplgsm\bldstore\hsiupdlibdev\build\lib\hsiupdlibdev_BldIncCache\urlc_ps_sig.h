/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/api/inc/urlc_ps_sig.h#4 $
 *   $Revision: #4 $
 *   $DateTime: 2012/08/22  $
 **************************************************************************
 * File Description:
 *
 * 3G PS Signal Data Type definitions for the URLC Interface
 **************************************************************************/

#if !defined (URLC_PS_SIG_H)
#define       URLC_PS_SIG_H


#if defined(UMTS7_PRE_R8)
#include <bearer_sig.h>


/***************************************************************************
* Nested Include Files
***************************************************************************/
//typedef unsigned char Int8;
//typedef unsigned short Int16;
typedef unsigned char UINT8;
typedef unsigned short UINT16;
#if !defined (ENABLE_PS_L2_TOOL)
typedef unsigned long UINT32;
#endif

/**\enum UrlcDataUsage
 * \brief This parameter is used to indicate how the memory of the data of PDU
was created, and therefore how it should be handled and freed by the receiving task.
 */
typedef enum UrlcDataUsageTag
{
    /** Data points to const or static memory and must not be freed. */
    URLC_DATA_USAGE_PRESERVED               = 0,
    /** Memory was allocated from the kernel memory manager. */
    URLC_DATA_USAGE_KERNEL                  = 1,
    /** Memory was allocated from the traffic memory manager. */
    URLC_DATA_USAGE_TMM                     = 2,
    /* URLC_DATA_USAGE_CS obsolete */
    /** Memory was allocated from the AMR memory manager. */
    URLC_DATA_USAGE_AMR                     = 4
}
UrlcDataUsage;


#define MAX_PS_RB_NUM	3
#if defined(ENABLE_URLC_UNIT_TEST)
#define	MAX_ALLOCATED_SDU_BUFFER_NUM	15
#else
#define	MAX_ALLOCATED_SDU_BUFFER_NUM	100
#endif

/* Maximum number of SDUs which can be passed to upper layers in one signal */
#define URLC_MAX_SDUS_PER_IND               8 // 4


typedef struct UrlcPsSduSegmentTag
{
    /** Indicates the bit offset, from the most significant bit of the first
        octet of the data block, where the most significant bit of used data
        starts, in the range 0 to 65535. */
    Int16                                   bitOffset;
    /** Indicates the size of the used data, in bits, from bitOffset, which
        accepts a range 0 to 65535, however currently the maximum known SDU
        size is 1502 octets of data plus 3 octets of header. */
    Int16                                   bitLength;
    /** Indicates how memory was allocated for this SDU data. */
    //UrlcDataUsage                           dataUsage;
    /** A pointer to a TMM data block . The most significant bit of used data
        starts at bitOffset bits after the most significant bit of the first octet,
        and continues for bitLength bits. The size of the data may vary with the
        type of signal, but shall not be larger than 8192 octets (65535 bits). */
    Int8                                    *data_p;
	Int8									end_flag;
#if defined (DATA_IN_SIGNAL)
    /** Used by Genie to limit the amount of data displayed by Genie;
        it does not stop Genie from displaying data before the offset.
        Its value should be set to contain the value of
        ((bitOffset+bitLength-1)/8)+1. */
    Int16                                   byteLengthPlusOffset;
    /** An array of data within the signal, when compiled for DATA_IN_SIGNAL */
  //  Int8                                    data[UPS_MAX_RLC_SDU_OCTETS]; /*jianghaifang FOR GENIE UT TEST*/
#endif
} UrlcPsSduSegment;

/*The max segments of one rlc SDU*/
#if defined(ENABLE_URLC_UNIT_TEST)
#define MAX_SEGS_PER_SDU    60//jianghaifang modify for genie testOOS, old value is 20
#else
#define MAX_SEGS_PER_SDU    90//120 //50  Fixed CQ00048005 20131114
#endif
#define MAX_SDUS_DTCCOPY  16

typedef struct UrlcPsSduTag
{
		/*The number of segment of this SDU, because the UrlcPsSduTag structure is global array,
	which needn't to be allocated or deleted each time, so define the number of segment, then 
	when deletd the TMM allocation, delete numSegment data_p will be ok*/
	Int8								    numSegment;
      UrlSequenceNumber                             startSn;
      UrlSequenceNumber                             endSn;
	UrlcPsSduSegment						sduSegment[MAX_SEGS_PER_SDU];
}UrlcPsSdu;

typedef struct UrlcPsAmDataIndTag
{
    BearerIdentity                          bearerIdentity;
	/*The flow control will be realized when each RSP MSG received from platform by URLC , needn't the rspRequired flag*/
 // Boolean                                	rspRequired;
 	Int8									numberSdus;
    UrlcPsSdu                               *sdu[URLC_MAX_SDUS_PER_IND];  //these node of sdu segments belong to a whole sdu
} UrlcPsAmDataInd;


typedef struct UrlcPsDataRspTag
{
	Int8	rb_id;
	Int8	numberSdus;
	UrlcPsSdu  *sdu[MAX_SDUS_DTCCOPY];
}UrlcPsDataRsp;

typedef struct UrlcPsUmDataIndTag
{
    /** Identifies the radio bearer. */
    BearerIdentity                          bearerIdentity;
    /** Contains the octet aligned SDU data. */
    UrlcPsSdu                                 *rlcSdu;  //these node of sdu segments belong to a whole sdu
} UrlcPsUmDataInd;

/**\struct UrlcTmDataInd
 * \brief This signal is sent to the higher layers, except URRC from the URLC to indicate that
transparent mode data has been received on the specified radio bearer.
 */
typedef struct UrlcPsTmDataIndTag
{
    /** Identifies the radio bearer. */
    BearerIdentity                          bearerIdentity;
    /** Contains the bit aligned SDU data, which when the sduStatus indicates an error, may be empty. */
    UrlcPsSdu                               *rlcSdu;
} UrlcPsTmDataInd;

typedef struct SacDatRecvAmDataOptIndTag
{
  UINT32           id;
  UINT8            rb_id;
  UINT8            numberSdus;
  UrlcPsSdu        *sdu [URLC_MAX_SDUS_PER_IND];
}
SacDatRecvAmDataOptInd;

typedef struct SacDatRecvUmDataOptIndTag
{
  /** Identifies the radio bearer. */
  UINT32           id;
  UINT8            rb_id;
  UrlcPsSdu        *sdu;
}
SacDatRecvUmDataOptInd;

typedef struct SacDatRecvTmDataOptIndTag
{
  /** Identifies the radio bearer. */
  UINT32           id;
  UINT8            rb_id;
  UrlcPsSdu        *sdu;
}
SacDatRecvTmDataOptInd;

//function for platform

extern void UrlPsDataRspCB(UrlcPsDataRsp    *rxSignal_p);

#endif

/*Added end by xyzheng for opt 2012-07-04*/


#endif /* URLC_PS_SIG_H */


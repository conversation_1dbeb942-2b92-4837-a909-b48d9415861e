/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/******************************************************************************
 * File Name:	ut_qsort.h
 *
 *
 * File Description: binary search  header file
 *
 *****************************************************************************/

#if !defined (UT_QSORT_H)
#define       UT_QSORT_H
#include "system.h"
/******************************************************************************
 *
 * Function     : utQuickSort
 *
 * Scope        : Global
 *
 * Parameters   : pointer to long array,  the criteria array (input const) 
 *                pointer to long array , the index array output filled by this function 
 *                the array length 
 * Returns      : the index of the minimum element that is greater or equal than the search criteria in the table
 *                 -1 if not found
 *
 * Description  : Quick Sort algoritm
 *****************************************************************************/

void utQuickSort( SignedInt32 CriteriaArray[] ,  SignedInt32 IndexArray[] , SignedInt32 ArrayLength);

#endif /* UT_QSORT_H */

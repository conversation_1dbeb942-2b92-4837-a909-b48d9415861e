/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/gp.mod/lib/src/ulbgrasnmain2.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2013/12/16 20:18:06 $
 **************************************************************************
 * File Description:
 *
 * ULBG2 main module external functions
 **************************************************************************/

#ifndef ULBGRASNMAIN2_H
#define ULBGRASNMAIN2_H

/***************************************************************************
 * Nested Include Files
 **************************************************************************/
#if defined(UPGRADE_DSDS)&&!defined(UPGRADE_DSDSLTE)
#include <system.h>
#include <kernel.h>
#include <xrabmdata.h>
#include <ulbgdlbg_sig.h>


#ifdef PC_TTCN_INT_TEST
extern KI_ENTRY_POINT
UpUlbg2Task(SignalBuffer *sigBuf);
#else
extern KI_ENTRY_POINT
UpUlbg2Task (void);
#endif
#endif
#endif

/* END OF FILE */

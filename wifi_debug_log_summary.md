# WiFi扫描调试日志添加总结

## 概述
为了排查WiFi扫描不完整的问题，我们在GUI层的关键文件中添加了详细的调试日志。这些日志将帮助我们从应用层到HAL层全面追踪WiFi扫描的整个过程。

## 修改的文件和添加的调试日志

### 1. gui/lv_watch/ke/ke_location_flow.c
**主要功能**: 位置服务相关的WiFi扫描流程控制

**添加的调试日志**:
- **扫描参数日志**: 在`Flw_GetWifiInfo()`函数中添加扫描参数输出
  ```c
  WS_PRINTF("[WIFI_DEBUG] Starting WiFi scan (YIXIN): rounds=3, max_hotspots=20, timeout=%d, priority=0", WIFI_OVER_TIME);
  WS_PRINTF("[WIFI_DEBUG] Starting WiFi scan (Normal): rounds=2, max_hotspots=8, timeout=%d, priority=0", WIFI_OVER_TIME);
  ```

- **扫描结果详细日志**: 在`Flw_Wifi_Scan_Cb_Ext()`函数中添加详细的AP信息输出
  ```c
  WS_PRINTF("[WIFI_DEBUG] %s() result=%d, ap_count=%d, retry_count=%d", __FUNCTION__, result, ap_list ? ap_list->count : 0, wifi_scan_retry_count);
  // 输出前10个AP的详细信息（MAC地址和RSSI）
  ```

- **重试机制日志**: 在WiFi重试逻辑中添加重试状态追踪
  ```c
  WS_PRINTF("[WIFI_DEBUG] Flw_JudgeWifi() retry wifi scan, count=%d/%d", wifi_scan_retry_count, WIFI_SCAN_MAX_RETRY);
  WS_PRINTF("[WIFI_DEBUG] WiFi scan successful, resetting retry count (was %d)", wifi_scan_retry_count);
  ```

### 2. gui/lv_watch/ws/mmi_modem_adaptor_ws_location.c
**主要功能**: WiFi扫描适配器实现，包含扫描结果过滤逻辑

**添加的调试日志**:
- **过滤逻辑日志**: 在`CWatchService_Wifi_Scan_Cb_Ext()`函数中添加过滤条件检查
  ```c
  WS_PRINTF("[WIFI_DEBUG] %s() Adapter callback: result=%d, ap_count=%d, valid_min=%d", __FUNCTION__, result, ap_list ? ap_list->count : 0, valid_cnt_min);
  WS_PRINTF("[WIFI_DEBUG] Scan passed filter, processing %d APs", ap_list->count);
  WS_PRINTF("[WIFI_DEBUG] Scan failed filter: result=%d, count=%d, required_min=%d", result, ap_list ? ap_list->count : 0, valid_cnt_min);
  ```

### 3. gui/lv_watch/modem/mmi_modem_adaptor_nw.c
**主要功能**: 底层WiFi扫描适配器，处理超时和状态管理

**添加的调试日志**:
- **扫描参数传递日志**: 在`app_adaptor_wifi_scan()`函数中添加参数确认
  ```c
  printf("[WIFI_DEBUG] app_adaptor_wifi_scan: rounds=%d, max_hotspots=%d, timeout=%d, priority=%d\n", scan_rounds, max_hotspot_number, timeout_seconds, priority);
  printf("[WIFI_DEBUG] MMI_Modem_Wifi_Ctrl_Req returned: %d\n", ret);
  ```

- **超时回调日志**: 在`MMI_Modem_Wifi_Timeout_Cb()`函数中添加超时信息
  ```c
  printf("[WIFI_DEBUG] WiFi scan timeout! Found %d APs before timeout, timeout was %d seconds\n", g_Wifi.WifiApList.count, g_Wifi.TimeoutSeconds);
  ```

- **WiFi信息接收日志**: 在`Ril_MM_Response_Wifi_CellInfo()`函数中添加AP接收追踪
  ```c
  printf("[WIFI_DEBUG] %s: Received WiFi info - MAC:%s, RSSI:%d, current count:%d\n", __FUNCTION__, pWifiCellInfo->macAddr, pWifiCellInfo->rssi, count);
  printf("[WIFI_DEBUG] %s: Received NULL WiFi info (scan end signal), final count:%d\n", __FUNCTION__, count);
  ```

### 4. gui/lvgl/hal/hal_wlan.c
**主要功能**: WiFi扫描的HAL层实现，包含扫描启动和结果处理

**添加的调试日志**:
- **扫描启动日志**: 在`hal_wlan_start_scan()`函数中添加状态追踪
  ```c
  printf("[WIFI_DEBUG] hal_wlan_start_scan: starting scan, state=%d\n", wlan_info->state);
  printf("[WIFI_DEBUG] hal_wlan_start_scan: wlan_sta_start_scan returned %d\n", ret);
  printf("[WIFI_DEBUG] hal_wlan_start_scan: scan started successfully\n");
  ```

- **扫描保护超时日志**: 在`hal_wlan_scan_protect_timeout_cb()`函数中添加超时警告
  ```c
  printf("[WIFI_DEBUG] Scan protection timeout triggered after 2.5s! Forcing scan completion.\n");
  ```

- **扫描回调日志**: 在`hal_wlan_scan_cb()`函数中添加结果接收追踪
  ```c
  printf("[WIFI_DEBUG] hal_wlan_scan_cb: received scan result with %d APs\n", result->ap_num);
  printf("[WIFI_DEBUG] hal_wlan_scan_cb: received NULL result (scan failed or timeout)\n");
  ```

- **AP处理统计日志**: 在扫描结果处理过程中添加详细统计
  ```c
  printf("[WIFI_DEBUG] hal_wlan_scan_cb: AP processing summary - Total:%d, Processed:%d, Hidden:%d, Duplicates:%d, Final:%d\n", result->ap_num, processed_aps, hidden_aps, duplicate_aps, scan_rlt->ap_num);
  ```

## 预期的调试输出流程

当WiFi扫描执行时，您将看到以下顺序的调试日志：

1. **扫描启动阶段**:
   ```
   [WIFI_DEBUG] Starting WiFi scan (Normal): rounds=2, max_hotspots=8, timeout=10, priority=0
   [WIFI_DEBUG] app_adaptor_wifi_scan: rounds=2, max_hotspots=8, timeout=10, priority=0
   [WIFI_DEBUG] MMI_Modem_Wifi_Ctrl_Req returned: 0
   [WIFI_DEBUG] hal_wlan_start_scan: starting scan, state=X
   [WIFI_DEBUG] hal_wlan_start_scan: wlan_sta_start_scan returned 0
   [WIFI_DEBUG] hal_wlan_start_scan: scan started successfully
   ```

2. **扫描过程中**:
   ```
   [WIFI_DEBUG] Ril_MM_Response_Wifi_CellInfo: Received WiFi info - MAC:xx:xx:xx:xx:xx:xx, RSSI:-XX, current count:X
   ```

3. **扫描完成阶段**:
   ```
   [WIFI_DEBUG] hal_wlan_scan_cb: received scan result with X APs
   [WIFI_DEBUG] hal_wlan_scan_cb: AP processing summary - Total:X, Processed:X, Hidden:X, Duplicates:X, Final:X
   [WIFI_DEBUG] CWatchService_Wifi_Scan_Cb_Ext() Adapter callback: result=0, ap_count=X, valid_min=3
   [WIFI_DEBUG] Flw_Wifi_Scan_Cb_Ext() result=0, ap_count=X, retry_count=0
   [WIFI_DEBUG] Found X APs: [详细AP信息]
   ```

4. **异常情况**:
   ```
   [WIFI_DEBUG] WiFi scan timeout! Found X APs before timeout, timeout was 10 seconds
   [WIFI_DEBUG] Scan protection timeout triggered after 2.5s! Forcing scan completion.
   [WIFI_DEBUG] Scan failed filter: result=1, count=2, required_min=3
   [WIFI_DEBUG] Flw_JudgeWifi() retry wifi scan, count=1/3
   ```

## 编译和测试

1. 使用MSYS2环境编译：
   ```bash
   make cranem_modem_watch
   ```

2. 运行测试并观察日志输出，重点关注：
   - 扫描参数是否合理
   - 实际扫描到的AP数量
   - 过滤逻辑是否过于严格
   - 是否存在超时问题
   - 重试机制是否正常工作

## 问题排查指南

根据日志输出，可以判断问题所在：

- **如果扫描参数过于保守**: 调整rounds、max_hotspots参数
- **如果扫描保护超时频繁触发**: 检查底层扫描实现或增加超时时间
- **如果过滤条件过于严格**: 调整valid_cnt_min参数
- **如果重复AP过多**: 检查AP去重逻辑
- **如果底层扫描失败**: 检查硬件或驱动问题

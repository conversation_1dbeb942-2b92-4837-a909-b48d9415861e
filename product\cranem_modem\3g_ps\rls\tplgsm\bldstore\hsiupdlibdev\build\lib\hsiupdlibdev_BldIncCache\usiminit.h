/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/3g.mod/lib/src/usiminit.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2007/03/19 13:59:30 $
 **************************************************************************
 *  File Description :
 *
 *   Contains declarations for functions in usiminit.c
 **************************************************************************/

#if !defined (USIMINIT_H)
#define USIMINIT_H

#if defined (UPGRADE_3G)

/***************************************************************************
 * Nested Include Files
 **************************************************************************/

#if !defined(SIMDATA_H)
#include "simdata.h"
#endif

/***************************************************************************
 * Manifest Constants
 **************************************************************************/


#define ATR_T0_TA       0x10
#define ATR_T0_TB       0x20
#define ATR_T0_TC       0x40
#define ATR_T0_TD       0x80

#define SIM_CARD_DATA_SERVICES_TAG   0x31
#define SIM_CARD_CAPABILITIES_3_TAG  0x73
#define SIM_CARD_COUNTRY_CODE_TAG      0x10

/* Historical TAGs and Data */
#define SIM_CARD_ISSUER_IDENTIFICATION_TAG   0x20
#define SIM_CARD_INITIAL_ACCESS_TAG    0x40
#define SIM_CARD_CARD_ISSUER_TAG       0x50
#define SIM_CARD_PREISSUING_TAG        0x60
#define SIM_CARD_CAPABILITIES_TAG      0x70
#define SIM_CARD_SERVICE_DATA          0X01
#define SIM_CARD_CAPABILITY_DATA       0X02
#define SIM_CATEGORY_INDICATOR_BYTE    0x80


#define ATR_TB1_PI_MASK 0x1f

#define SIM_ATR_LEN     33

#define MAX_BAUD_RATE_SUPPORTED     16
#define MAX_CLOCK_RATE_SUPPORTED    512

/* MAX_ATR_TRIES defines the number of times an incorrect ATR can be */
/* recieved before the SIM is rejected.                              */

#define MAX_ATR_TRIES   3
#define MAX_PPS_TRIES   3
#define MAX_PTS_TRIES   1
#define WWT_13_BY_4      ((372.0 * 4.0 * 960.0)/(13.0 * 1000000.0))
#define WWT_13_BY_8      ((372.0 * 8.0 * 960.0)/(13.0 * 1000000.0))

/* mod for CQ73925 bt yunhail 2014 10 24 begin */
#define SIM_PPS_SIZE             4
/*The ratio F/D can only be one of these three values*/

/*Modification for CQ00011506 SIM is not recognized 453547 by zhjzhao, 07-05-11,begin */
#define SIM_UICC_MAX_BWI         0x0a
/*Modification for CQ00011506 SIM is not recognized 453547 by zhjzhao, 07-05-11,end */

#define SIM_INVERSE              0x3F
#define SIM_DIRECT               0x3B

#define SIM_DEFAULT_WWT          10
#define SIM_PTS_SIZE             3

/* Add by jungle for atrTB3 on 2012-11-07 Begin */
#define SIM_PPS_EXT_SIZE         5
/* Add by jungle for atrTB3 on 2012-11-07 End */

/* mod for CQ73925 bt yunhail 2014 10 24 end */



/***************************************************************************
 * Typed Constants
 **************************************************************************/

typedef enum atrOffsetsTag
{
    ATR_TS,
    ATR_T0,
    ATR_TA1,
    ATR_TB1,
    ATR_TC1,
    ATR_TD1,
    ATR_TA2,
    ATR_TB2,
    ATR_TC2,
    ATR_TD2
}
atrOffsets;


/***************************************************************************
 * Type Definitions
 **************************************************************************/


/***************************************************************************
 *  Macros
 **************************************************************************/


/***************************************************************************
 *  Function Prototypes
 **************************************************************************/

void SimInitialise ( SimManagerData *simData );

#endif
#endif




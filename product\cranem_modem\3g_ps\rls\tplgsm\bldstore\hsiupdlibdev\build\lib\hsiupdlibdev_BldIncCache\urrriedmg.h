/*------------------------------------------------------------
(C) Copyright [2006-2011] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/***************************************************************************
*
* File Description: urrriedmg.h
*   RRC RIE messsages handling functionality source file 
*   This is a manual revised file
*
***************************************************************************/

/***************************************************************************
* Include Files
***************************************************************************/
    
#if !defined (URRRIEDMG_H)
#define       URRRIEDMG_H

#include <uas_asn.h>
#include <utper.h>          /* For PerBuffer */
#include <urrtypes.h>       /* For UrrRxMsgVersion */
#include <urrrieut.h>

void Copy_UDDI_ddi_field_manual(UDDI *ddi, UUL_LogicalChannelMapping_r8_fixedSize *fixedSize);

void Copy_URLC_PDU_SizeList_rlc_PDU_SizeList_field_manual(URLC_PDU_SizeList *rlc_PDU_SizeList, UUL_LogicalChannelMapping_r8_fixedSize 
*fixedSize);

void Copy_Xr3UUL_LogicalChannelMapping_r6_legacyR3_p_field_manual(Xr3UUL_LogicalChannelMapping_r6 
*legacyR3_p);

void Copy_UUL_LogicalChannelMappingList_r6_ul_LogicalChMapping_ul_LogicalChannelMapping_field_manual( PerBuffer *perBuffer_p, UUL_LogicalChannelMappingList_r6 *source_p, UUL_LogicalChannelMappingList_r8 *target_p 
);

void Copy_Xr3UUL_LogicalChannelMappingList_r6_legacyR3_p_field_manual(Xr3UUL_LogicalChannelMappingList_r6 
*legacyR3_p);

void Copy_UTGMP_tgmp_enum_manual(
    UTGPS_ConfigurationParams *source_p,
    UTGPS_ConfigurationParams_r8 *target_p);

void Copy_UIntegrityProtectionAlgorithm_integrityProtectionAlgorithm_enum_manual(UIntegrityProtectionAlgorithm *integrityProtectionAlgorithm, UIntegrityProtectionModeInfo_r7 *target_p);

void Copy_UCipheringModeCommand_cipheringModeCommand_field_manual(UCipheringModeCommand *cipheringModeCommand, UCipheringModeInfo_r7 *target_p);

void Copy_UUTRAN_DRX_CycleLengthCoefficient_utran_DRX_CycleLengthCoeff_field_manual_1(UUTRAN_DRX_CycleLengthCoefficient *utran_DRX_CycleLengthCoeff, Int8 *drx_CycleLengthCoefficient);

void Copy_UOctetModeRLC_SizeInfoType1_dl_RLC_PDU_size_field_manual(UOctetModeRLC_SizeInfoType1 *dl_RLC_PDU_size, UDL_AM_RLC_Mode_r7 *target_p);

void Copy_URLC_Info_r6_altE_bitInterpretation_altE_bitInterpretation_enum_manual(URLC_Info_r6_altE_bitInterpretation *altE_bitInterpretation, URLC_Info_r7 *target_p);

void Copy_Int16_n_field_manual_1(Int16 *n);

void Copy_URB_MappingOption_r6_data_field_manual(PerBuffer *perBuffer_p, URB_MappingInfo_r6 *source_p, URB_MappingInfo_r8 *target_p);

void Copy_UE_DCH_TTI_tti_enum_manual(PerBuffer *perBuffer_p, UE_DCH_TTI *tti, UUL_AddReconfTransChInformation_r8_e_dch *target_p);

void Copy_UUL_AddReconfTransChInformation_r6_harq_Info_harq_Info_enum_manual(UUL_AddReconfTransChInformation_r6_harq_Info *harq_Info, UUL_AddReconfTransChInformation_r8_e_dch *target_p);

void Copy_Int16_maxMAC_e_PDUContents_field_manual(Int16 *maxMAC_e_PDUContents, UE_DCH_AddReconf_MAC_d_Flow_r7_non_ScheduledTransGrantInfo *target_p);

void Copy_Boolean_ms2_NonSchedTransmGrantHARQAllocPresent_field_manual(Boolean *ms2_NonSchedTransmGrantHARQAllocPresent, UE_DCH_AddReconf_MAC_d_Flow_r7_non_ScheduledTransGrantInfo *target_p);

void Copy_Int8_ms2_NonSchedTransmGrantHARQAlloc_field_manual(Int8 *ms2_NonSchedTransmGrantHARQAlloc, UE_DCH_AddReconf_MAC_d_Flow_r7_non_ScheduledTransGrantInfo *target_p);

void Copy_Xr3UDL_TransportChannelIdentity_r5_legacyR3_p_field_manual(Xr3UDL_TransportChannelIdentity_r5 *legacyR3_p);

void Copy_Int8_numberOfProcesses_field_manual(Int8 *numberOfProcesses, UHARQ_Info_r7 *target_p);

void Copy_Int16_n_field_manual_2(UHARQ_Info_explicit *source_p, UHARQ_Info_r7_explicit *target_p);

void Copy_UHARQMemorySize_data_enum_manual(UHARQ_Info_explicit *source_p, UHARQ_Info_r7_explicit *target_p);

void Copy_Boolean_addOrReconfMAC_dFlowPresent_field_manual(Boolean *addOrReconfMAC_dFlowPresent, UHSDSCH_Info_r9 *target_p);

void Copy_UAddOrReconfMAC_dFlow_addOrReconfMAC_dFlow_field_manual(PerBuffer *perBuffer_p, UAddOrReconfMAC_dFlow *addOrReconfMAC_dFlow, UHSDSCH_Info_r9 *target_p);

void Copy_Xr3UDL_AddReconfTransChInformation_r5_legacyR3_p_field_manual(Xr3UDL_AddReconfTransChInformation_r5 *legacyR3_p);

void Copy_Xr4UDL_AddReconfTransChInformation_r5_legacyR4_p_field_manual(Xr4UDL_AddReconfTransChInformation_r5 *legacyR4_p);

void Copy_Xr3UUL_DPCH_Info_r6_fdd_legacyR3_p_field_manual(Xr3UUL_DPCH_Info_r6_fdd *legacyR3_p);

void Copy_Xr4UUL_DPCH_Info_r6_fdd_legacyR4_p_field_manual(Xr4UUL_DPCH_Info_r6_fdd *legacyR4_p);

void Copy_Xr5UUL_DPCH_Info_r6_fdd_legacyR5_p_field_manual(Xr5UUL_DPCH_Info_r6_fdd *legacyR5_p);

void Copy_UUL_EDCH_Information_r6_mac_es_e_resetIndicator_mac_es_e_resetIndicator_enum_manual(UUL_EDCH_Information_r6_mac_es_e_resetIndicator *mac_es_e_resetIndicator, UUL_EDCH_Information_r9 *target_p);

void Copy_Boolean_e_DPCCH_InfoPresent_field_manual(PerBuffer *perBuffer_p, UUL_EDCH_Information_r6 *source_p, UUL_EDCH_Information_r9 *target_p);

void Copy_UE_DPCCH_Info_e_DPCCH_Info_field_manual(UE_DPCCH_Info *e_DPCCH_Info);

void Copy_Boolean_e_DPDCH_InfoPresent_field_manual(Boolean *e_DPDCH_InfoPresent);

void Copy_UE_DPDCH_Info_e_DPDCH_Info_field_manual(UE_DPDCH_Info *e_DPDCH_Info);

void Copy_Boolean_schedulingTransmConfigurationPresent_field_manual(Boolean *schedulingTransmConfigurationPresent);

void Copy_UE_DPDCH_SchedulingTransmConfiguration_schedulingTransmConfiguration_field_manual(UE_DPDCH_SchedulingTransmConfiguration *schedulingTransmConfiguration);

void Copy_UFeedback_cycle_feedback_cycle_enum_manual(UFeedback_cycle *feedback_cycle, UMeasurement_Feedback_Info_r7_fdd *target_p);

void Copy_UDL_HSPDSCH_Information_r6_modeInfo_modeSpecificInfo_field_manual(PerBuffer *perBuffer_p, UDL_HSPDSCH_Information_r6_modeInfo *modeSpecificInfo, UDL_HSPDSCH_Information_r8_modeSpecificInfo *target);

void Copy_UDL_CommonInformation_r6_modeInfo_modeSpecificInfo_field_manual(PerBuffer *perBuffer_p, UDL_CommonInformation_r6_modeInfo *modeSpecificInfo, UDL_CommonInformation_r8 *target_p);

void Copy_UDL_CommonInformation_r6_mac_hsResetIndicator_mac_hsResetIndicator_enum_manual(UDL_CommonInformation_r6_mac_hsResetIndicator *mac_hsResetIndicator, UDL_CommonInformation_r8 *target_p);

void Copy_UDL_CommonInformation_r6_postVerificationPeriod_postVerificationPeriod_enum_manual(UDL_CommonInformation_r6_postVerificationPeriod *postVerificationPeriod, UDL_CommonInformation_r8 *target_p);

void Copy_Xr3UDL_CommonInformation_r6_legacyR3_p_field_manual(Xr3UDL_CommonInformation_r6 *legacyR3_p);

void Copy_Xr4UDL_CommonInformation_r6_legacyR4_p_field_manual(Xr4UDL_CommonInformation_r6 *legacyR4_p);

void Copy_Xr5UDL_CommonInformation_r6_legacyR5_p_field_manual(Xr5UDL_CommonInformation_r6 *legacyR5_p);

void Copy_UDL_InformationPerRL_r6_modeInfo_modeSpecificInfo_field_manual(UDL_InformationPerRL_r6_modeInfo *modeSpecificInfo);

void Copy_UE_AGCH_ChannelisationCode_e_AGCH_ChannelisationCode_field_manual(PerBuffer *perBuffer_p, UE_AGCH_ChannelisationCode *e_AGCH_ChannelisationCode, UE_AGCH_Information_r8 *target_p);

void Copy_Boolean_e_HICH_InfoPresent_field_manual(PerBuffer *perBuffer_p, UDL_InformationPerRL_r6 *source_p, UDL_InformationPerRL_r8 *target_p);

void Copy_UDL_InformationPerRL_r6_e_HICH_Info_e_HICH_Info_field_manual(UDL_InformationPerRL_r6_e_HICH_Info *e_HICH_Info);

void Copy_Boolean_e_RGCH_InfoPresent_field_manual(Boolean *e_RGCH_InfoPresent);

void Copy_UDL_InformationPerRL_r6_e_RGCH_Info_e_RGCH_Info_field_manual(UDL_InformationPerRL_r6_e_RGCH_Info *e_RGCH_Info);

void Copy_Xr3UDL_InformationPerRL_r6_legacyR3_p_field_manual(Xr3UDL_InformationPerRL_r6 *legacyR3_p);

void Copy_Xr4UDL_InformationPerRL_r6_legacyR4_p_field_manual(Xr4UDL_InformationPerRL_r6 *legacyR4_p);

void Copy_Xr5UDL_InformationPerRL_r6_legacyR5_p_field_manual(Xr5UDL_InformationPerRL_r6 *legacyR5_p);

void Copy_Xr3UCellUpdateConfirm_r6_IEs_legacyR3_p_field_manual_1(Xr3UCellUpdateConfirm_r6_IEs *legacyR3_p);

void Copy_Xr4UCellUpdateConfirm_r6_IEs_legacyR4_p_field_manual_1(Xr4UCellUpdateConfirm_r6_IEs *legacyR4_p);

void Copy_Xr5UCellUpdateConfirm_r6_IEs_legacyR5_p_field_manual_1(Xr5UCellUpdateConfirm_r6_IEs *legacyR5_p);

void Copy_Int16_n_field_manual_3(Int16 *n);

void Copy_URB_MappingOption_r7_data_field_manual(PerBuffer *perBuffer_p, URB_MappingInfo_r7 *source_p, URB_MappingInfo_r8 *target_p);

#if !defined (ENABLE_UMTS_CAP_DOWNGRADE)
void Copy_UUL_AddReconfTransChInformation_r7_harq_Info_harq_Info_enum_manual(UUL_AddReconfTransChInformation_r7_harq_Info *harq_Info, UUL_AddReconfTransChInformation_r8_e_dch *target_p);

void Copy_UMAC_hs_WindowSize_mac_ehsWindowSize_enum_manual(UMAC_hs_WindowSize *mac_ehsWindowSize, UMAC_ehs_AddReconfReordQ_r9 *target_p);

void Copy_UUL_EDCH_Information_r7_mac_es_e_resetIndicator_mac_es_e_resetIndicator_enum_manual(UUL_EDCH_Information_r7_mac_es_e_resetIndicator *mac_es_e_resetIndicator, UUL_EDCH_Information_r9 *target_p);

void Copy_UDL_HSPDSCH_Information_r7_dl_64QAM_Configured_dl_64QAM_Configured_enum_manual(UDL_HSPDSCH_Information_r7_dl_64QAM_Configured *dl_64QAM_Configured, UDL_HSPDSCH_Information_r9_fdd *target_p);
#endif//ENABLE_UMTS_CAP_DOWNGRADE

void Copy_Int16_n_field_manual_4(Int16 *n);

void Copy_UTGP_Sequence_data_field_manual(PerBuffer *perBuffer_p, UTGP_SequenceList *source_p,  UTGP_SequenceList_r8 *target_p);

void Copy_UDL_CommonInformation_r7_mac_hsResetIndicator_mac_hsResetIndicator_enum_manual(UDL_CommonInformation_r7_mac_hsResetIndicator *mac_hsResetIndicator, UDL_CommonInformation_r8 *target_p);

void Copy_UDL_CommonInformation_r7_postVerificationPeriod_postVerificationPeriod_enum_manual(UDL_CommonInformation_r7_postVerificationPeriod *postVerificationPeriod, UDL_CommonInformation_r8 *target_p);

void Copy_UUL_EDCH_Information_r8_mac_es_e_resetIndicator_mac_es_e_resetIndicator_enum_manual(UUL_EDCH_Information_r8_mac_es_e_resetIndicator *mac_es_e_resetIndicator, UUL_EDCH_Information_r9 *target_p);

void Copy_UDL_HSPDSCH_Information_r8_dl_64QAM_Configured_dl_64QAM_Configured_enum_manual(UDL_HSPDSCH_Information_r8_dl_64QAM_Configured *dl_64QAM_Configured, UDL_HSPDSCH_Information_r9_fdd *target_p);
/*********** add begin*/
void Copy_UDL_SecondaryCellInfoFDD_dl_SecondaryCellInfoFDD_field_manual (PerBuffer *perBuffer_p, UDL_SecondaryCellInfoFDD *source_p, UDL_SecondaryCellInfoFDD_r9 *target_p);
/*********** add end*/
void Copy_USystemSpecificCapUpdateReq_r5_data_enum_manual(USystemSpecificCapUpdateReqList_r5 *source_p, USystemSpecificCapUpdateReqList_r8 *target_p);

void Copy_Xr3UCapabilityUpdateRequirement_r5_legacyR3_p_field_manual(Xr3UCapabilityUpdateRequirement_r5 *legacyR3_p);

void Copy_Xr4UCapabilityUpdateRequirement_r5_legacyR4_p_field_manual(Xr4UCapabilityUpdateRequirement_r5 *legacyR4_p);

void Copy_Xr5URRCConnectionSetup_r6_IEs_complete_legacyR5_p_field_manual(Xr5URRCConnectionSetup_r6_IEs_complete *legacyR5_p);

void Copy_Xr3URRCConnectionSetup_r6_IEs_legacyR3_p_field_manual(Xr3URRCConnectionSetup_r6_IEs *legacyR3_p);

void Copy_Xr4URRCConnectionSetup_r6_IEs_legacyR4_p_field_manual(Xr4URRCConnectionSetup_r6_IEs *legacyR4_p);

void Copy_Xr5URRCConnectionSetup_r6_IEs_legacyR5_p_field_manual(Xr5URRCConnectionSetup_r6_IEs *legacyR5_p);

void Copy_Xr3URL_AdditionInformation_r6_legacyR3_p_field_manual(Xr3URL_AdditionInformation_r6 *legacyR3_p);

void Copy_UServing_HSDSCH_CellInformation_mac_hsResetIndicator_mac_hsResetIndicator_enum_manual(UServing_HSDSCH_CellInformation_mac_hsResetIndicator *mac_hsResetIndicator, UServing_HSDSCH_CellInformation_r9 *target_p);

void Copy_UE_DCH_RL_InfoNewServingCell_prim_Sec_GrantSelector_primary_Secondary_GrantSelector_enum_manual(UE_DCH_RL_InfoNewServingCell_prim_Sec_GrantSelector *primary_Secondary_GrantSelector, UE_DCH_RL_InfoNewServingCell_r7_servingGrant *target_p);

void Copy_Xr3UActiveSetUpdate_r6_IEs_legacyR3_p_field_manual(Xr3UActiveSetUpdate_r6_IEs *legacyR3_p);

void Copy_UServing_HSDSCH_CellInformation_r7_mac_hsResetIndicator_mac_hsResetIndicator_enum_manual(UServing_HSDSCH_CellInformation_r7_mac_hsResetIndicator *mac_hsResetIndicator, UServing_HSDSCH_CellInformation_r9 *target_p);

void Copy_UServing_HSDSCH_CellInformation_r8_mac_hsResetIndicator_mac_hsResetIndicator_enum_manual(UServing_HSDSCH_CellInformation_r8_mac_hsResetIndicator *mac_hsResetIndicator, UServing_HSDSCH_CellInformation_r9 *target_p);

void Copy_UUTRAN_DRX_CycleLengthCoefficient_utran_DRX_CycleLengthCoeff_field_manual_3(UUTRAN_DRX_CycleLengthCoefficient *utran_DRX_CycleLengthCoeff);

void Copy_Xr3UCellUpdateConfirm_r6_IEs_legacyR3_p_field_manual_2(Xr3UCellUpdateConfirm_r6_IEs *legacyR3_p);

void Copy_Xr4UCellUpdateConfirm_r6_IEs_legacyR4_p_field_manual_2(Xr4UCellUpdateConfirm_r6_IEs *legacyR4_p);

void Copy_Xr5UCellUpdateConfirm_r6_IEs_legacyR5_p_field_manual_2(Xr5UCellUpdateConfirm_r6_IEs *legacyR5_p);

void Copy_Boolean_interRATCellInfoListPresent_field_manual(UInterRATMeasurement_r6 *source_p, UInterRATMeasurement_r9 *target_p);

void Copy_UInterRATCellInfoList_r6_interRATCellInfoList_field_manual(PerBuffer *perBuffer_p, UInterRATMeasurement_r6 *source_p, UInterRATMeasurement_r9 *target_p);

void Copy_UInterRATMeasQuantity_ratSpecificInfo_ratSpecificInfo_field_manual(UInterRATMeasQuantity_ratSpecificInfo *ratSpecificInfo);

void Copy_UInterRATReportingQuantity_ratSpecificInfo_ratSpecificInfo_field_manual(UInterRATReportingQuantity_ratSpecificInfo *ratSpecificInfo);

void Copy_Xr3UUE_Positioning_ReportingQuantity_r4_legacyR3_p_field_manual(Xr3UUE_Positioning_ReportingQuantity_r4 *legacyR3_p);
/* CQ00023389 begin */
void Copy_UUE_Positioning_GPS_Assistance_ue_positioning_GPS_AcquisitionAssistance_field_manual(UUE_Positioning_GPS_Assistance *ue_positioning_GPS_AcquisitionAssistance, UUE_Positioning_GPS_AcquisitionAssistance_r7 *target_p);
/* CQ00023389 end */
void Copy_Boolean_dummyPresent_field_manual(Boolean *dummyPresent);

void Copy_UMAC_ehs_DelReordQ_List_dummy_field_manual(UMAC_ehs_DelReordQ_List *dummy);

void Copy_Xr3UMeasurementControl_r6_IEs_legacyR3_p_field_manual(Xr3UMeasurementControl_r6_IEs *legacyR3_p);

void Copy_UUE_Positioning_ReportingQuantity_r7_velocityRequested_velocityRequested_enum_manual(UUE_Positioning_ReportingQuantity_r7_velocityRequested *velocityRequested, UUE_Positioning_ReportingQuantity_r8 *target_p);

void Copy_UUTRAN_DRX_CycleLengthCoefficient_utran_DRX_CycleLengthCoeff_field_manual_4(UUTRAN_DRX_CycleLengthCoefficient *utran_DRX_CycleLengthCoeff);

void Copy_Xr3UPhysicalChannelReconfiguration_r6_IEs_legacyR3_p_field_manual(Xr3UPhysicalChannelReconfiguration_r6_IEs *legacyR3_p);

void Copy_Xr4UPhysicalChannelReconfiguration_r6_IEs_legacyR4_p_field_manual(Xr4UPhysicalChannelReconfiguration_r6_IEs *legacyR4_p);

void Copy_Xr5UPhysicalChannelReconfiguration_r6_IEs_legacyR5_p_field_manual(Xr5UPhysicalChannelReconfiguration_r6_IEs *legacyR5_p);
/* CQ00023389 begin */
void Copy_UPhysicalChannelReconfiguration_r7_IEs_responseToChangeOfUE_Capability_responseToChangeOfUE_Capability_enum_manual(UPhysicalChannelReconfiguration_r7_IEs_responseToChangeOfUE_Capability *responseToChangeOfUE_Capability, UPhysicalChannelReconfiguration_r9_IEs *target_p);
void Copy_UPhysicalChannelReconfiguration_r8_IEs_responseToChangeOfUE_Capability_responseToChangeOfUE_Capability_enum_manual(UPhysicalChannelReconfiguration_r8_IEs_responseToChangeOfUE_Capability *responseToChangeOfUE_Capability, UPhysicalChannelReconfiguration_r9_IEs *target_p);
/* CQ00023389 end */
void Copy_UUTRAN_DRX_CycleLengthCoefficient_utran_DRX_CycleLengthCoeff_field_manual_5(UUTRAN_DRX_CycleLengthCoefficient *utran_DRX_CycleLengthCoeff);

void Copy_Xr5URadioBearerReconfiguration_r6_IEs_complete_legacyR5_p_field_manual(Xr5URadioBearerReconfiguration_r6_IEs_complete *legacyR5_p);

void Copy_Xr3URadioBearerReconfiguration_r6_IEs_legacyR3_p_field_manual(Xr3URadioBearerReconfiguration_r6_IEs *legacyR3_p);

void Copy_Xr4URadioBearerReconfiguration_r6_IEs_legacyR4_p_field_manual(Xr4URadioBearerReconfiguration_r6_IEs *legacyR4_p);

void Copy_Xr5URadioBearerReconfiguration_r6_IEs_legacyR5_p_field_manual(Xr5URadioBearerReconfiguration_r6_IEs *legacyR5_p);
/* CQ00023389 begin */
void Copy_URadioBearerReconfiguration_r7_IEs_responseToChangeOfUE_Capability_responseToChangeOfUE_Capability_enum_manual(URadioBearerReconfiguration_r7_IEs_responseToChangeOfUE_Capability *responseToChangeOfUE_Capability, URadioBearerReconfiguration_r9_IEs *target_p);
void Copy_URadioBearerReconfiguration_r8_IEs_responseToChangeOfUE_Capability_responseToChangeOfUE_Capability_enum_manual(URadioBearerReconfiguration_r8_IEs_responseToChangeOfUE_Capability *responseToChangeOfUE_Capability, URadioBearerReconfiguration_r9_IEs *target_p);
/* CQ00023389 end */
void Copy_UUTRAN_DRX_CycleLengthCoefficient_utran_DRX_CycleLengthCoeff_field_manual_6(UUTRAN_DRX_CycleLengthCoefficient *utran_DRX_CycleLengthCoeff);

void Copy_Xr3URadioBearerRelease_r6_IEs_legacyR3_p_field_manual(Xr3URadioBearerRelease_r6_IEs *legacyR3_p);

void Copy_Xr4URadioBearerRelease_r6_IEs_legacyR4_p_field_manual(Xr4URadioBearerRelease_r6_IEs *legacyR4_p);

void Copy_Xr5URadioBearerRelease_r6_IEs_legacyR5_p_field_manual(Xr5URadioBearerRelease_r6_IEs *legacyR5_p);

void Copy_UUTRAN_DRX_CycleLengthCoefficient_utran_DRX_CycleLengthCoeff_field_manual_7(UUTRAN_DRX_CycleLengthCoefficient *utran_DRX_CycleLengthCoeff);
/* CQ00023389 begin */
void Copy_URadioBearerSetup_r6_IEs_specificationMode_specificationMode_field_manual(PerBuffer *perBuffer_p, URadioBearerSetup_r6_IEs_specificationMode *specificationMode, URadioBearerSetup_r9_IEs *target_p);
/* CQ00023389 end */
void Copy_Xr3URadioBearerSetup_r6_IEs_legacyR3_p_field_manual(Xr3URadioBearerSetup_r6_IEs *legacyR3_p);

void Copy_Xr4URadioBearerSetup_r6_IEs_legacyR4_p_field_manual(Xr4URadioBearerSetup_r6_IEs *legacyR4_p);

void Copy_Xr5URadioBearerSetup_r6_IEs_legacyR5_p_field_manual(Xr5URadioBearerSetup_r6_IEs *legacyR5_p);
/* CQ00023389 begin */
void Copy_URadioBearerSetup_r7_IEs_specificationMode_specificationMode_field_manual(PerBuffer *perBuffer_p, URadioBearerSetup_r7_IEs_specificationMode *specificationMode, URadioBearerSetup_r9_IEs *target_p);
/* CQ00023389 end */
void Copy_UUTRAN_DRX_CycleLengthCoefficient_utran_DRX_CycleLengthCoeff_field_manual_8(UUTRAN_DRX_CycleLengthCoefficient *utran_DRX_CycleLengthCoeff);

void Copy_Xr3UTransportChannelReconfiguration_r6_IEs_legacyR3_p_field_manual(Xr3UTransportChannelReconfiguration_r6_IEs *legacyR3_p);

void Copy_Xr4UTransportChannelReconfiguration_r6_IEs_legacyR4_p_field_manual(Xr4UTransportChannelReconfiguration_r6_IEs *legacyR4_p);

void Copy_Xr5UTransportChannelReconfiguration_r6_IEs_legacyR5_p_field_manual(Xr5UTransportChannelReconfiguration_r6_IEs *legacyR5_p);
/* CQ00023389 begin */
void Copy_UTransportChannelReconfiguration_r7_IEs_responseToChangeOfUE_Capability_responseToChangeOfUE_Capability_enum_manual(UTransportChannelReconfiguration_r7_IEs_responseToChangeOfUE_Capability *responseToChangeOfUE_Capability, UTransportChannelReconfiguration_r9_IEs *target_p);
void Copy_UTransportChannelReconfiguration_r8_IEs_responseToChangeOfUE_Capability_responseToChangeOfUE_Capability_enum_manual(UTransportChannelReconfiguration_r8_IEs_responseToChangeOfUE_Capability *responseToChangeOfUE_Capability, UTransportChannelReconfiguration_r9_IEs *target_p);
/* CQ00023389 end */
void Copy_UUTRAN_DRX_CycleLengthCoefficient_utran_DRX_CycleLengthCoeff_field_manual_9(UUTRAN_DRX_CycleLengthCoefficient *utran_DRX_CycleLengthCoeff);

void Copy_Xr3UURAUpdateConfirm_r5_IEs_legacyR3_p_field_manual(Xr3UURAUpdateConfirm_r5_IEs *legacyR3_p);

void Copy_Xr3UUTRANMobilityInformation_r5_IEs_legacyR3_p_field_manual(Xr3UUTRANMobilityInformation_r5_IEs *legacyR3_p);

#if !defined (ENABLE_UMTS_CAP_DOWNGRADE)
void Copy_UMIMO_Parameters_r7_to_UMIMO_Parameters_r8_fdd (UMIMO_Parameters_r7 *source_p,  UMIMO_Parameters_r8_fdd *target_p);

void Copy_UMAC_d_FlowIdentity_to_UDL_TrCH_TypeId2_r7_hsdsch(PerBuffer *perBuffer_p, UDL_TrCH_TypeId2_r5 *source_p, UDL_TrCH_TypeId2_r7_A *target_p);

void Copy_UMIMO_Parameters_r7_to_UMIMO_Parameters_r9_fdd_M (PerBuffer *perBuffer_p, UMIMO_Parameters_r7 *source_p,  UMIMO_Parameters_r9_fdd *target_p);
#endif//ENABLE_UMTS_CAP_DOWNGRADE

void Copy_Boolean_dummy_field_manual(Boolean *dummy);

void Copy_Boolean_dummy2_field_manual(Boolean *dummy);

void Copy_URRC_TransactionIdentifier_rrc_TransactionIdentifier_field_manual(URRC_TransactionIdentifier *transactionID);

void Copy_UThresholdSFN_GPS_TOW_to_UThresholdSFN_GPS_TOW_us(  UUE_Positioning_EventSpecificInfo *source_p,  UUE_Positioning_EventSpecificInfo_r7 *target_p );

void Copy_URB_InformationSetupList_r6Element_to_URB_InformationSetupList_r8Element (  PerBuffer *perBuffer_p, URB_InformationSetupList_r6Element *source_p, URB_InformationSetupList_r8Element *target_p);

void Copy_UUTRAN_DRX_CycleLengthCoefficient_utran_DRX_CycleLengthCoeff_field_manual_2(UUTRAN_DRX_CycleLengthCoefficient *utran_DRX_CycleLengthCoeff, URRCConnectionSetup_r8_IEs *target_p);

void Copy_UDPCH_CompressedModeInfo_to_UDPCH_CompressedModeInfo_r8(  PerBuffer *perBuffer_p, UDPCH_CompressedModeInfo *source_p, UDPCH_CompressedModeInfo_r8 *target_p);

void Copy_UUL_AddReconfTransChInfoList_r7_to_UUL_AddReconfTransChInfoList_r8 (  PerBuffer *perBuffer_p, UUL_AddReconfTransChInfoList_r7 *source_p,  UUL_AddReconfTransChInfoList_r8 *target_p);

void Copy_URB_MappingInfo_r7_to_URB_MappingInfo_r8( PerBuffer *perBuffer_p, URB_MappingInfo_r7 *source_p, URB_MappingInfo_r8 *target_p);

void Copy_URB_InformationReconfigList_r7_to_URB_InformationReconfigList_r8 (PerBuffer *perBuffer_p, URB_InformationReconfigList_r7 *source_p, URB_InformationReconfigList_r8 *target_p);

void Copy_URB_InformationAffectedList_r7_to_URB_InformationAffectedList_r8( PerBuffer *perBuffer_p, URB_InformationAffectedList_r7 *source_p, URB_InformationAffectedList_r8 *target_p);

void Copy_USRB_InformationSetup_r6_to_USRB_InformationSetup_r8 (  PerBuffer *perBuffer_p, USRB_InformationSetup_r6 *source_p, USRB_InformationSetup_r8 *target_p);

void Copy_URAB_InformationReconfigList_to_URAB_InformationReconfigList_r8 ( PerBuffer *perBuffer_p, URAB_InformationReconfigList *source_p, URAB_InformationReconfigList_r8 *target_p);

void Copy_URB_InformationReconfigList_r6_to_URB_InformationReconfigList_r8 ( PerBuffer *perBuffer_p,URB_InformationReconfigList_r6 *source_p, URB_InformationReconfigList_r8 *target_p);

void Copy_URB_InformationAffectedList_r6_to_URB_InformationAffectedList_r8 (  PerBuffer *perBuffer_p, URB_InformationAffectedList_r6 *source_p, URB_InformationAffectedList_r8 *target_p);

void Copy_URLC_InfoChoice_r6_to_URLC_InfoChoice_r7 (  PerBuffer *perBuffer_p, URLC_InfoChoice_r6 *source_p, URLC_InfoChoice_r7 *target_p);

void Copy_URB_MappingInfo_r6_to_URB_MappingInfo_r8 (  PerBuffer *perBuffer_p, URB_MappingInfo_r6 *source_p, URB_MappingInfo_r8 *target_p);

void Copy_UUL_AddReconfTransChInfoList_r6_to_UUL_AddReconfTransChInfoList_r8 (  PerBuffer *perBuffer_p, UUL_AddReconfTransChInfoList_r6 *source_p,  UUL_AddReconfTransChInfoList_r8 *target_p);

void Copy_UDL_DeletedTransChInfoList_r5_to_UDL_DeletedTransChInfoList_r9 (  PerBuffer *perBuffer_p, UDL_DeletedTransChInfoList_r5 *source_p,  UDL_DeletedTransChInfoList_r7 *target_p);

void Copy_UDL_AddReconfTransChInfoList_r5_to_UDL_AddReconfTransChInfoList_r9 (  PerBuffer *perBuffer_p, UDL_AddReconfTransChInfoList_r5 *source_p,  UDL_AddReconfTransChInfoList_r9 *target_p);

///////////////////////////////////////////////////////////
// NonCriticalExtensions section
///////////////////////////////////////////////////////////

void Copy_UActiveSetUpdate_r6_v6b0NonCriticalExtensions(PerBuffer *perBuffer_p, UActiveSetUpdate_r6 *source_p, UActiveSetUpdate_r9* target_p);

void Copy_UCellUpdateConfirm_CCCH_r6_v6b0NonCriticalExtensions(PerBuffer *perBuffer_p, UCellUpdateConfirm_CCCH_r6 *source_p, UCellUpdateConfirm_CCCH_r9 *target_p);

void Copy_UCellUpdateConfirm_DCCH_r6_v6b0NonCriticalExtensions(PerBuffer *perBuffer_p, UCellUpdateConfirm_r6 *source_p, UCellUpdateConfirm_r9 *target_p);

void Copy_UMeasurementControl_r6_v6a0NonCriticalExtensions(PerBuffer *perBuffer_p, UMeasurementControl_r6 *source_p, UMeasurementControl_r9 *target_p);

void Copy_UPhysicalChannelReconfiguration_r6_v6b0NonCriticalExtensions(PerBuffer *perBuffer_p, UPhysicalChannelReconfiguration_r6 *source_p, UPhysicalChannelReconfiguration_r9 *target_p);

void Copy_URadioBearerRelease_r6_v6f0NonCriticalExtensions(PerBuffer *perBuffer_p, URadioBearerRelease_r6 *source_p, URadioBearerRelease_r9 *target_p);

void Copy_URRCConnectionSetup_r6_v6b0NonCriticalExtensions(PerBuffer *perBuffer_p, URRCConnectionSetup_r6 *source_p, URRCConnectionSetup_r9 *target_p);

void Copy_UTransportChannelReconfiguration_r6_v6b0NonCriticalExtensions(PerBuffer *perBuffer_p, UTransportChannelReconfiguration_r6 *source_p, UTransportChannelReconfiguration_r9 *target_p);

void Copy_URadioBearerSetup_r6_v6b0NonCriticalExtensions(PerBuffer *perBuffer_p, URadioBearerSetup_r6 *source_p, URadioBearerSetup_r9 *target_p);

#if !defined (ENABLE_UMTS_CAP_DOWNGRADE)
void Copy_UActiveSetUpdate_r7_v780NonCriticalExtensions(PerBuffer *perBuffer_p, UActiveSetUpdate_r7 *source_p, UActiveSetUpdate_r9* target_p);

void Copy_UActiveSetUpdate_r7_v7f0NonCriticalExtensions(PerBuffer *perBuffer_p, UActiveSetUpdate_r7 *source_p, UActiveSetUpdate_r9* target_p);

void Copy_UActiveSetUpdate_r7_v7g0NonCriticalExtensions(PerBuffer *perBuffer_p, UActiveSetUpdate_r7 *source_p, UActiveSetUpdate_r9* target_p);

void Copy_UCellUpdateConfirm_CCCH_r7_v780NonCriticalExtensions(PerBuffer *perBuffer_p, UCellUpdateConfirm_CCCH_r7 *source_p, UCellUpdateConfirm_CCCH_r9* target_p);

void Copy_UCellUpdateConfirm_DCCH_r7_v780NonCriticalExtensions(PerBuffer *perBuffer_p, UCellUpdateConfirm_r7 *source_p, UCellUpdateConfirm_r9* target_p);

void Copy_UPhysicalChannelReconfiguration_r7_v780NonCriticalExtensions(PerBuffer *perBuffer_p, UPhysicalChannelReconfiguration_r7 *source_p, UPhysicalChannelReconfiguration_r9 *target_p);

void Copy_URadioBearerReconfiguration_r7_v780NonCriticalExtensions(PerBuffer *perBuffer_p, URadioBearerReconfiguration_r7 *source_p, URadioBearerReconfiguration_r9 *target_p);

void Copy_URadioBearerRelease_r7_v780NonCriticalExtensions(PerBuffer *perBuffer_p, URadioBearerRelease_r7 *source_p, URadioBearerRelease_r9 *target_p);

void Copy_URadioBearerSetup_r7_v780NonCriticalExtensions(PerBuffer *perBuffer_p, URadioBearerSetup_r7 *source_p, URadioBearerSetup_r9 *target_p);

void Copy_URRCConnectionSetup_r7_v780NonCriticalExtensions(PerBuffer *perBuffer_p, URRCConnectionSetup_r7 *source_p, URRCConnectionSetup_r9 *target_p);

void Copy_URRCConnectionSetup_r8_v7dNonCriticalExtensions(PerBuffer *perBuffer_p, URRCConnectionSetup_r8 *source_p, URRCConnectionSetup_r9 *target_p);
#endif//ENABLE_UMTS_CAP_DOWNGRADE

void Copy_UTransportChannelReconfiguration_r7_v780NonCriticalExtensions(PerBuffer *perBuffer_p, UTransportChannelReconfiguration_r7 *source_p, UTransportChannelReconfiguration_r9 *target_p);

// void Copy_UMeasurementControl_r7_v7b0NonCriticalExtensions(PerBuffer *perBuffer_p, UMeasurementControl_r7 *source_p, UMeasurementControl_r8 *target_p);

void Copy_URadioBearerReconfiguration_r6_v6b0NonCriticalExtensions(PerBuffer *perBuffer_p, URadioBearerReconfiguration_r6 *source_p, URadioBearerReconfiguration_r9 *target_p);

void Copy_URadioBearerReconfiguration_r6_v6f0NonCriticalExtensions(PerBuffer *perBuffer_p, URadioBearerReconfiguration_r6 *source_p, URadioBearerReconfiguration_r9 *target_p);

void Copy_URadioBearerReconfiguration_r6_v770NonCriticalExtensions(PerBuffer *perBuffer_p, URadioBearerReconfiguration_r6 *source_p, URadioBearerReconfiguration_r9 *target_p);

#if !defined (ENABLE_UMTS_CAP_DOWNGRADE)
void Copy_URadioBearerReconfiguration_r7_v7f0NonCriticalExtensions(PerBuffer *perBuffer_p, URadioBearerReconfiguration_r7 *source_p, URadioBearerReconfiguration_r9* target_p);

void Copy_URadioBearerReconfiguration_r7_v7g0NonCriticalExtensions(PerBuffer *perBuffer_p, URadioBearerReconfiguration_r7 *source_p, URadioBearerReconfiguration_r9* target_p);

void Copy_URadioBearerReconfiguration_r8_v7f0NonCriticalExtensions(PerBuffer *perBuffer_p, URadioBearerReconfiguration_r8 *source_p, URadioBearerReconfiguration_r9* target_p);

void Copy_URadioBearerReconfiguration_r8_v890NonCriticalExtensions(PerBuffer *perBuffer_p, URadioBearerReconfiguration_r8 *source_p, URadioBearerReconfiguration_r9* target_p);

void Copy_URadioBearerReconfiguration_r8_v7g0NonCriticalExtensions(PerBuffer *perBuffer_p, URadioBearerReconfiguration_r8 *source_p, URadioBearerReconfiguration_r9* target_p);

#endif//ENABLE_UMTS_CAP_DOWNGRADE
void Copy_URadioBearerRelease_r6_v6b0NonCriticalExtensions(PerBuffer *perBuffer_p, URadioBearerRelease_r6 *source_p, URadioBearerRelease_r9 *target_p);

void Copy_URadioBearerRelease_r6_v770NonCriticalExtensions(PerBuffer *perBuffer_p, URadioBearerRelease_r6 *source_p, URadioBearerRelease_r9 *target_p);

#if !defined (ENABLE_UMTS_CAP_DOWNGRADE)
void Copy_URadioBearerRelease_r7_v7f0NonCriticalExtensions(PerBuffer *perBuffer_p, URadioBearerRelease_r7 *source_p, URadioBearerRelease_r9 *target_p);

void Copy_URadioBearerRelease_r7_v7g0NonCriticalExtensions(PerBuffer *perBuffer_p, URadioBearerRelease_r7 *source_p, URadioBearerRelease_r9 *target_p);

void Copy_URadioBearerSetup_r7_v7f0NonCriticalExtensions(PerBuffer *perBuffer_p, URadioBearerSetup_r7 *source_p, URadioBearerSetup_r9 *target_p);

void Copy_URadioBearerSetup_r7_v7g0NonCriticalExtensions(PerBuffer *perBuffer_p, URadioBearerSetup_r7 *source_p, URadioBearerSetup_r9 *target_p);

void Copy_URadioBearerSetup_r7_v820NonCriticalExtensions(PerBuffer *perBuffer_p, URadioBearerSetup_r7 *source_p, URadioBearerSetup_r9 *target_p);

void Copy_URadioBearerSetup_r8_v7f0NonCriticalExtensions (PerBuffer *perBuffer_p, URadioBearerSetup_r8 *source_p, URadioBearerSetup_r9 *target_p);

void Copy_UTransportChannelReconfiguration_r7_v7f0NonCriticalExtensions(PerBuffer *perBuffer_p, UTransportChannelReconfiguration_r7 *source_p, UTransportChannelReconfiguration_r9 *target_p);

void Copy_UTransportChannelReconfiguration_r7_v7g0NonCriticalExtensions(PerBuffer *perBuffer_p, UTransportChannelReconfiguration_r7 *source_p, UTransportChannelReconfiguration_r9 *target_p);

#endif//ENABLE_UMTS_CAP_DOWNGRADE
void Copy_UTransportChannelReconfiguration_r6_v770NonCriticalExtensions(PerBuffer *perBuffer_p, UTransportChannelReconfiguration_r6 *source_p, UTransportChannelReconfiguration_r9 *target_p);

#if !defined (ENABLE_UMTS_CAP_DOWNGRADE)
void Copy_UCellUpdateConfirm_DCCH_r7_v7f0NonCriticalExtensions(PerBuffer *perBuffer_p, UCellUpdateConfirm_r7 *source_p, UCellUpdateConfirm_r9 *target_p);

void Copy_UCellUpdateConfirm_DCCH_r7_v7g0NonCriticalExtensions(PerBuffer *perBuffer_p, UCellUpdateConfirm_r7 *source_p, UCellUpdateConfirm_r9 *target_p);

void Copy_UCellUpdateConfirm_CCCH_r7_v7f0NonCriticalExtensions(PerBuffer *perBuffer_p, UCellUpdateConfirm_CCCH_r7 *source_p, UCellUpdateConfirm_CCCH_r9 *target_p);

void Copy_UCellUpdateConfirm_CCCH_r7_v7g0NonCriticalExtensions(PerBuffer *perBuffer_p, UCellUpdateConfirm_CCCH_r7 *source_p, UCellUpdateConfirm_CCCH_r9 *target_p);

#endif//ENABLE_UMTS_CAP_DOWNGRADE
void Copy_UPhysicalChannelReconfiguration_r6_v770NonCriticalExtensions(PerBuffer *perBuffer_p, UPhysicalChannelReconfiguration_r6 *source_p, UPhysicalChannelReconfiguration_r9* target_p);

#if !defined (ENABLE_UMTS_CAP_DOWNGRADE)
void Copy_UPhysicalChannelReconfiguration_r7_v7f0NonCriticalExtensions(PerBuffer *perBuffer_p, UPhysicalChannelReconfiguration_r7 *source_p, UPhysicalChannelReconfiguration_r9* target_p);

void Copy_UPhysicalChannelReconfiguration_r7_v7g0NonCriticalExtensions(PerBuffer *perBuffer_p, UPhysicalChannelReconfiguration_r7 *source_p, UPhysicalChannelReconfiguration_r9* target_p);

void Copy_UPhysicalChannelReconfiguration_r8_v7f0NonCriticalExtensions(PerBuffer *perBuffer_p, UPhysicalChannelReconfiguration_r8 *source_p, UPhysicalChannelReconfiguration_r9 *target_p);

void Copy_UPhysicalChannelReconfiguration_r8_v890NonCriticalExtensions(PerBuffer *perBuffer_p, UPhysicalChannelReconfiguration_r8 *source_p, UPhysicalChannelReconfiguration_r9 *target_p);

void Copy_UPhysicalChannelReconfiguration_r8_v7g0NonCriticalExtensions(PerBuffer *perBuffer_p, UPhysicalChannelReconfiguration_r8 *source_p, UPhysicalChannelReconfiguration_r9 *target_p);

#endif//ENABLE_UMTS_CAP_DOWNGRADE
void Copy_UDL_AddReconfTransChInfoList_r7_to_UDL_AddReconfTransChInfoList_r9(PerBuffer *perBuffer_p, UDL_AddReconfTransChInfoList_r7 *source_p, UDL_AddReconfTransChInfoList_r9 *target_p);

void Copy_UDL_DeletedTransChInfoList_r5_to_UDL_DeletedTransChInfoList_r7 (PerBuffer *perBuffer_p, UDL_DeletedTransChInfoList_r5 *source_p, UDL_DeletedTransChInfoList_r7 *target_p);

void Copy_UUL_LogicalChannelMapping_r6_to_UUL_LogicalChannelMapping_r8 (PerBuffer *perBuffer_p,UUL_LogicalChannelMapping_r6 *source_p,UUL_LogicalChannelMapping_r8 *target_p);

void Copy_UUL_AddReconfTransChInfoList_r7_to_UUL_AddReconfTransChInfoList_r8 (PerBuffer *perBuffer_p, UUL_AddReconfTransChInfoList_r7 *source_p, UUL_AddReconfTransChInfoList_r8 *target_p);

void Copy_URB_InformationAffectedList_r7_to_URB_InformationAffectedList_r8 (PerBuffer *perBuffer_p, URB_InformationAffectedList_r7 *source_p, URB_InformationAffectedList_r8 *target_p);

void Copy_URB_InformationReconfigList_r7_to_URB_InformationReconfigList_r8 (PerBuffer *perBuffer_p, URB_InformationReconfigList_r7 *source_p, URB_InformationReconfigList_r8 *target_p);

void Copy_URB_MappingInfo_r7_to_URB_MappingInfo_r8 (PerBuffer *perBuffer_p, URB_MappingInfo_r7 *source_p, URB_MappingInfo_r8 *target_p);

#if !defined (ENABLE_UMTS_CAP_DOWNGRADE)
void Copy_UCellUpdateConfirm_CCCH_r8_v7f0NonCriticalExtensions(PerBuffer *perBuffer_p, UCellUpdateConfirm_CCCH_r8 *source_p, UCellUpdateConfirm_CCCH_r9 *target_p);
void Copy_UCellUpdateConfirm_CCCH_r8_v890NonCriticalExtensions(PerBuffer *perBuffer_p, UCellUpdateConfirm_CCCH_r8 *source_p, UCellUpdateConfirm_CCCH_r9 *target_p);
void Copy_UCellUpdateConfirm_CCCH_r8_v7g0NonCriticalExtensions(PerBuffer *perBuffer_p, UCellUpdateConfirm_CCCH_r8 *source_p, UCellUpdateConfirm_CCCH_r9 *target_p);
void Copy_UCellUpdateConfirm_DCCH_r8_v7f0NonCriticalExtensions(PerBuffer *perBuffer_p, UCellUpdateConfirm_r8 *source_p, UCellUpdateConfirm_r9 *target_p);
void Copy_UCellUpdateConfirm_DCCH_r8_v890NonCriticalExtensions(PerBuffer *perBuffer_p, UCellUpdateConfirm_r8 *source_p, UCellUpdateConfirm_r9 *target_p);
void Copy_UCellUpdateConfirm_DCCH_r8_v7g0NonCriticalExtensions(PerBuffer *perBuffer_p, UCellUpdateConfirm_r8 *source_p, UCellUpdateConfirm_r9 *target_p);

void Copy_URadioBearerSetup_r8_v7g0NonCriticalExtensions(PerBuffer *perBuffer_p, URadioBearerSetup_r8 *source_p, URadioBearerSetup_r9 *target_p);

void Copy_URadioBearerSetup_r8_v890NonCriticalExtensions (PerBuffer *perBuffer_p, URadioBearerSetup_r8 *source_p, URadioBearerSetup_r9 
*target_p);



void Copy_UActiveSetUpdate_r8_v890NonCriticalExtensions(PerBuffer *perBuffer_p, UActiveSetUpdate_r8 *source_p, UActiveSetUpdate_r9* target_p);
void Copy_UActiveSetUpdate_r8_v7g0NonCriticalExtensions(PerBuffer *perBuffer_p, UActiveSetUpdate_r8 *source_p, UActiveSetUpdate_r9* target_p);
void Copy_UTransportChannelReconfiguration_r8_v7f0NonCriticalExtensions(PerBuffer *perBuffer_p, UTransportChannelReconfiguration_r8 *source_p, UTransportChannelReconfiguration_r9* target_p);
void Copy_UTransportChannelReconfiguration_r8_v890NonCriticalExtensions(PerBuffer *perBuffer_p, UTransportChannelReconfiguration_r8 *source_p, UTransportChannelReconfiguration_r9* target_p);
void Copy_UTransportChannelReconfiguration_r8_v7g0NonCriticalExtensions(PerBuffer *perBuffer_p, UTransportChannelReconfiguration_r8 *source_p, UTransportChannelReconfiguration_r9* target_p);
void Copy_URadioBearerRelease_r8_v7f0NonCriticalExtensions(PerBuffer *perBuffer_p, URadioBearerRelease_r8 *source_p, URadioBearerRelease_r9* target_p);
void Copy_URadioBearerRelease_r8_v890NonCriticalExtensions(PerBuffer *perBuffer_p, URadioBearerRelease_r8 *source_p, URadioBearerRelease_r9* target_p);
void Copy_URadioBearerRelease_r8_v7g0NonCriticalExtensions(PerBuffer *perBuffer_p, URadioBearerRelease_r8 *source_p, URadioBearerRelease_r9* target_p);
#endif//ENABLE_UMTS_CAP_DOWNGRADE

void Copy_URAB_InformationSetupList_r6_to_URAB_InformationSetupList_r8 (
  PerBuffer *perBuffer_p,
  URAB_InformationSetupList_r6 *source_p,
  URAB_InformationSetupList_r8 *target_p);

void Copy_USRB_InformationSetupList_r7_to_USRB_InformationSetupList_r8 (
  PerBuffer *perBuffer_p,
  USRB_InformationSetupList_r7 *source_p,
  USRB_InformationSetupList_r8 *target_p);

void Copy_URAB_InformationReconfigList_to_URAB_InformationReconfigList_r8 (
  PerBuffer *perBuffer_p,
  URAB_InformationReconfigList *source_p,
  URAB_InformationReconfigList_r8 *target_p);

void Copy_URAB_InformationSetupList_r7_to_URAB_InformationSetupList_r8 (
  PerBuffer *perBuffer_p,
  URAB_InformationSetupList_r7 *source_p,
  URAB_InformationSetupList_r8 *target_p);


#endif

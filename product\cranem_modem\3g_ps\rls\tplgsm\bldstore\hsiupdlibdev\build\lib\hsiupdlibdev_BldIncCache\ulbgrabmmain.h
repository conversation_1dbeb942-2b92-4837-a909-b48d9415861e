/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/3g.mod/lib/src/ulbgrabmmain.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2011/03/06 20:17:00 $
 **************************************************************************
 * File Description:
 *
 * ULBG RABM main module external functions
 **************************************************************************/

#if !defined (ULBGRABMMAIN_H)
#define       ULBGRABMMAIN_H

/***************************************************************************
 * Nested Include Files
 **************************************************************************/


#include <system.h>
#include <xrabmdata.h>
#include <kernel.h>
#include <ulbgdlbg_sig.h>

extern void
UlbgRabmMain(XRabmEntity *sn,SignalBuffer *receivedSignal);

extern void
UlbgRabmProcessSignal(XRabmEntity *sn);

extern void
UlbgRabmDestroySignal(SignalBuffer *receivedSig);

extern void
UlbgRabmInit(XRabmEntity *sn);

extern Boolean 
UlbgRabmIsPdpInStateWaitForRabReestabInd(XRabmEntity *sn);


#endif /* ULBGRABMMAIN_H */

/* END OF FILE */

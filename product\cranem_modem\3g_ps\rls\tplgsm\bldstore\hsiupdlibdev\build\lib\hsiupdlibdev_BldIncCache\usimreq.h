/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/3g.mod/lib/src/usimreq.h#3 $
 *   $Revision: #3 $
 *   $DateTime: 2007/01/12 13:10:41 $
 **************************************************************************
 *  File Description :
 *
 *   Contains declarations for functions in usimreqp1.c/usimreqp2.c/usimrqp2p.c
 **************************************************************************/
#if defined (UPGRADE_3G)
#if !defined (USIMREQ_H)
#define USIMREQ_H

/***************************************************************************
 * Nested Include Files
 **************************************************************************/

#if !defined (SIMDEC_H)
#include    "simdec.h"
#endif

#if !defined (SIMATDEC_H)
#include    "simatdec.h"
#endif

#if !defined (USIMCMD_H)
#include    "usimcmd.h"
#endif

/***************************************************************************
 *  Types
 **************************************************************************/



/***************************************************************************
 *  Macros
 **************************************************************************/

#define USIM_SERVICE_ENABLED(s)    (Boolean) (simData->serviceTable.usimService.s)

#define SIM_SERVICE_AVAILABLE(s)  (Boolean) (simData->serviceTable.simService.s.allocated &&             \
                                   simData->serviceTable.simService.s.activated )
#define CPHS_SERVICE_AVAILABLE(s)  ( simData->cphsService.s.allocated &&    \
                                     simData->cphsService.s.activated )

#define USIM_SERVICE_DEACTVIATE(s)   (simData->serviceTable.usimService.s = (Boolean)FALSE)

#define SIM_SERVICE_DEACTVIATE(s)  ( simData->serviceTable.simService.s.activated = (Boolean)FALSE)

/***************************************************************************
 *  Function Prototypes
 **************************************************************************/
/* mod for CQ73925 bt yunhail 2014 10 24 begin */
#ifdef UPGRADE_BTSAP
void SimUiccChangeTargetTaskIdReq ( SimManagerData  *simData );
void SimUiccChangeTargetTaskIdCnf ( SimManagerData  *simData ); 
#endif
/* mod for CQ73925 bt yunhail 2014 10 24 end */

/* Add by jungle for CQ00027135,CQ00140921 on 2013-01-06 Begin */
Boolean SimUiccReadIccid (SimManagerData *simData);    //-+ CQ00176215 13-Sep-2011 +-
/* Add by jungle for CQ00027135 on 2013-01-06 End */
/*CQ00139879, Cgliu, 2022-11-12,Begin*/
void SimSendIccidInd(SimManagerData *simData);
/*CQ00139879, Cgliu, 2022-11-12,End  */ 
/* Add by jungle for CQ00032317 on 2013-04-18 Begin */ 
void SimUiccReadPhase (SimManagerData *simData);
/* Add by jungle for CQ00032317 on 2013-04-18 End */ 
void SimUiccRemovedInd          ( SimManagerData  *simData);
void SimUiccTerminateSessionReq ( SimManagerData  *simData);
void SimUiccReadPlmnSelReq (SimManagerData *simData);
void SimUiccWritePlmnSelReq (SimManagerData *simData);
void SimUiccInitialise ( SimManagerData *simData);
void SimUiccInitialiseReq ( SimManagerData *simData);

void SimUiccAddFplmnReq ( SimManagerData  *simData);
#if 0

void SimUiccReadAcmMaxReq ( SimManagerData  *simData);
void SimUiccWriteAcmMaxReq ( SimManagerData  *simData);
void SimUiccReadAcmReq ( SimManagerData  *simData);
void SimUiccWriteAcmReq ( SimManagerData  *simData);
void SimUiccIncreaseAcmReq ( SimManagerData  *simData);
void SimUiccReadPuctReq ( SimManagerData   *simData);
void SimUiccWritePuctReq ( SimManagerData  *simData);
void SimUiccReadCbmiReq ( SimManagerData   *simData);
void SimUiccWriteCbmiReq ( SimManagerData  *simData);


void SimUiccReadBcchReq ( SimManagerData  *simData);
void SimUiccWriteBcchReq ( SimManagerData  *simData);
#endif
void SimUiccGetFreeSmReq ( SimManagerData  *simData);
void SimUiccReadSmssReq ( SimManagerData   *simData);
void SimUiccWriteSmssReq ( SimManagerData   *simData);
/*Homezone signals */
void SimUiccReadHZCellCacheReq   ( SimManagerData  *simData );
void SimUiccWriteHZCellCacheReq  ( SimManagerData  *simData );
    /***********, Cgliu, 2022-11-09,Begin*/ 
#if 1
void SimUiccWriteEfTstReq  ( SimManagerData  *simData );
#endif 	
	/***********, Cgliu, 2022-11-09,End  */	

void SimUiccReadHZParamsReq      ( SimManagerData  *simData );
/* Added by flxing, for CQ00008853, 20110225, begin */
void simUiccReadHZLacCiReq(SimManagerData *simData);                           //-+ CQ00148041 10-Feb-2011 +-
void simUiccReadHZTagsReq(SimManagerData *simData);                            //-+ CQ00148041 10-Feb-2011 +-
void simUiccReadHZUeSettingsReq(SimManagerData *simData);                      //-+ CQ00148041 10-Feb-2011 +-
void simUiccWriteHZUeSettingsReq(SimManagerData *simData);                     //-+ CQ00148041 10-Feb-2011 +-
/* Added by flxing, for CQ00008853, 20110225, end */
	/***********, Cgliu, 2022-11-09,Begin*/ 
#if defined(SUPPORT_RPM)
//Rpm signals
void simUiccReadRpmEnabledFlagReq(SimManagerData *simData);
void simUiccReadRpmParamsReq(SimManagerData *simData);
void simUiccReadRpmOperLrCntrsReq(SimManagerData *simData);
void simUiccReadRpmOperCntrsReq(SimManagerData *simData);
void simUiccReadRpmVersionReq(SimManagerData *simData);
void simUiccWriteRpmOperCntrsReq(SimManagerData *simData);
void simUiccWriteRpmVersionReq(SimManagerData *simData);
#endif 	
	/***********, Cgliu, 2022-11-09,End  */	

void SimUiccReadEfStatusReq ( SimManagerData  *simData);
void SimUiccReadEfStatusReq ( SimManagerData  *simData);
void SimUiccReadSpnReq ( SimManagerData  *simData);
void SimUiccReadSpdiReq ( SimManagerData  *simData);
void SimUiccReadCfisReq ( SimManagerData  *simData );
#if defined(SUPPORT_SMS)
void SimUiccReadSmReq ( SimManagerData  *simData );
#endif
/*CQ00135815, Cgliu, 2022-03-04,Begin*/
#if defined (SUPPORT_PB)
void SimUiccDialNumStatusReq ( SimManagerData  *simData);
void SimUiccListDialNumExtReq ( SimManagerData  *simData);
#endif
/*CQ00135815, Cgliu, 2022-03-04,End  */

void SimUiccChvFunctionReq ( SimManagerData  *simData);
#if defined(SUPPORT_SMS)
void SimUiccReadSmspReq ( SimManagerData  *simData );
void SimUiccWriteSmspReq ( SimManagerData  *simData );
void SimUiccDeleteSmspReq ( SimManagerData  *simData );
void SimUiccAddSmspReq ( SimManagerData  *simData );
void SimUiccListSmspReq ( SimManagerData  *simData );
void SimUiccReadSmssReq ( SimManagerData  *simData );
void SimUiccWriteSmssReq ( SimManagerData   *simData );
void SimUiccDeleteSmReq ( SimManagerData  *simData );
void SimUiccWriteSmReq ( SimManagerData  *simData );
void SimUiccAddSmReq ( SimManagerData  *simData );
void SimUiccSetSmRecordStatReq ( SimManagerData  *simData );
#endif
void SimUiccReadLociReq ( SimManagerData  *simData );
void SimUiccDeleteFplmnReq ( SimManagerData  *simData );
void SimUiccAddFplmnReq ( SimManagerData  *simData );
void SimUiccReadDirStatusReq ( SimManagerData  *simData );
#if 0
void SimUiccReadAcmMaxReq ( SimManagerData *simData );

#endif
void SimUiccResetDckReq ( SimManagerData  *simData );
/***********, Cgliu, 2022-11-09,Begin*/ 
#if 0
void SimUiccReadCnlReq ( SimManagerData   *simData );
void SimUiccReadNiaReq ( SimManagerData   *simData );
#endif 	
	/***********, Cgliu, 2022-11-09,End  */	

void SimUiccReadSmsrReq ( SimManagerData  *simData );
void SimUiccWriteSmsrReq ( SimManagerData *simData );
void SimUiccListSmsrReq ( SimManagerData  *simData );
/*CQ0020210223,persewang*/
#if defined(SUPPORT_CB)

void SimUiccReadElpReq ( SimManagerData    *simData );
void SimUiccWriteElpReq ( SimManagerData   *simData );
void SimUiccReadCbmidReq ( SimManagerData *simData );
void SimUiccReadCbmirReq ( SimManagerData *simData );
void SimUiccWriteCbmirReq ( SimManagerData *simData );
void SimUiccReadPuctReq ( SimManagerData  *simData);
void SimUiccWritePuctReq ( SimManagerData  *simData);
void SimUiccReadLiReq ( SimManagerData    *simData);
void SimUiccWriteLiReq ( SimManagerData   *simData);
void SimUiccReadCbmiReq ( SimManagerData   *simData);
void SimUiccWriteCbmiReq ( SimManagerData  *simData);
#endif
void SimUiccListOplReq ( SimManagerData *simData );
void SimUiccListPnnReq ( SimManagerData *simData );
#if 0
void SimUiccReadVgcsVbsReq ( SimManagerData *simData );
void SimUiccReadEmlppReq ( SimManagerData *simData );
void SimUiccWriteAaemReq ( SimManagerData  *simData );

void SimUiccBarredDialReq ( SimManagerData  *simData );
void SimUiccReadVgcsVbsReq ( SimManagerData *simData );
void SimUiccReadVgcsVbsStatusReq ( SimManagerData *simData );
void SimUiccReadAaemReq ( SimManagerData *simData );
void SimUiccReadDckReq ( SimManagerData   *simData );
#endif
void SimUiccReadGidReq ( SimManagerData    *simData );
void SimUiccSimGenAccessReq ( SimManagerData  *simData );

/*Modification for access SIM EMU data, Chengguang Liu, Mar.6 09,Begin*/
#ifdef SIM_EMULATION_ON
void SimUiccSimEmuGenAccessReq ( SimManagerData  *simData );
/*Modification for USIM it test. CQ00001116, Chengguang Liu, June.09 09,Begin*/
#ifdef ON_PC
void SimUiccSimChgEmuReq ( SimManagerData  *simData );
#endif
/*Modification for USIM it test. CQ00001116, Chengguang Liu, June.09 09,End  */

/*Modification for SIM test envirement. CQ00007361, Chengguang Liu, Dec.07 2010,Begin*/
void SimUiccSimEmuErrInd( SimManagerData  *simData, SignalBuffer *signal);
/*Modification for SIM test envirement. CQ00007361, Chengguang Liu, Dec.07 2010,End  */
#endif /*end ((defined ON_PC) && (PC_INTEGRATION_TEST))*/
/*Modification for access SIM EMU datal, Chengguang Liu, Mar.6 09,End  */
void SimUiccReadDirReq ( SimManagerData  *simData );
/***********, Cgliu, 2022-11-09,Begin*/ 
#if 0
void SimUiccUsimApplicationStartReq ( SimManagerData  *simData );
#endif 	
	/***********, Cgliu, 2022-11-09,End  */	

void SimUiccReadCallTimerReq ( SimManagerData   *simData );
/*CQ00135815, Cgliu, 2022-03-04,Begin*/
#if defined (SUPPORT_PB)
void SimUiccAddDialnumReq ( SimManagerData  *simData );
void SimUiccWriteDialNumReq ( SimManagerData  *simData );
void SimUiccDeleteDialNumReq ( SimManagerData  *simData );
void SimUiccReadDialNumReq ( SimManagerData  *simData );

void SimUiccFixedDialReq ( SimManagerData  *simData );
#endif
/*CQ00135815, Cgliu, 2022-03-04,End  */
void SimUiccListDialNumReq ( SimManagerData  *simData );

void SimUiccReadEccReq ( SimManagerData  *simData );
void SimUiccReadExtEccReq (SimManagerData *simData);
void SimUiccPinFunctionReq ( SimManagerData  *simData);
void SimUiccReadNetParReq ( SimManagerData  *simData);
/*CQ00143191,add read CSP file  function,perse,begin */
void SimUiccReadCphsDataReq   ( SimManagerData  *simData );
void SimUiccCphsReadCspReq  ( SimManagerData  *simData );

/*CQ00143191,add read CSP file  function,perse,end */

/*CQ00135815, Cgliu, 2022-03-04,Begin*/
#if defined (SUPPORT_PB)
void SimUiccListPbcReq ( SimManagerData *simData );
void SimUiccReadEmailReq ( SimManagerData *simData );
void SimUiccReadAnrReq ( SimManagerData *simData );
#endif
/*CQ00135815, Cgliu, 2022-03-04,End  */
    /***********, Cgliu, 2022-11-09,Begin*/ 
#if 0
void SimUiccWriteNetParReq ( SimManagerData *simData);
#endif 	
	/***********, Cgliu, 2022-11-09,End  */	

void SimUiccReadMsgWaitInfoReq ( SimManagerData *simData);
void SimUiccWriteMsgWaitInfoReq ( SimManagerData *simData);

/*CQ00135815, Cgliu, 2022-03-04,Begin*/
#if defined (SUPPORT_PB)
void SimUiccGetPbidReq ( SimManagerData *simData );
void SimUiccReadCcpReq ( SimManagerData *simData );
void SimUiccReadPbrRecReq ( SimManagerData *simData );
void SimUiccReadGrpReq ( SimManagerData *simData );
void SimUiccReadGasReq ( SimManagerData *simData );
void SimUiccReadAasReq ( SimManagerData *simData );
void SimUiccWriteAasReq ( SimManagerData *simData );
void SimUiccDeleteAasReq ( SimManagerData *simData );
void SimUiccWriteGasReq (SimManagerData *simData );
void SimUiccDeleteGasReq ( SimManagerData *simData );
void SimUiccReadHiddenKeyReq ( SimManagerData *simData );
void SimUiccReadCcReq (SimManagerData *simData );
void SimUiccWriteGrpReq (SimManagerData *simData );
void SimUiccDeleteGrpReq ( SimManagerData *simData );
void SimUiccPhoneBookStatusReq ( SimManagerData *simData );
void SimUiccWriteSneReq ( SimManagerData *simData );
void SimUiccDeleteSneReq ( SimManagerData *simData );
void SimUiccListGasReq ( SimManagerData *simData );
void SimUiccListAasReq ( SimManagerData *simData );
void SimUiccWriteEmailReq ( SimManagerData *simData );
void SimUiccDeleteEmailReq ( SimManagerData *simData );
void SimUiccWriteAnrReq ( SimManagerData *simData );
void SimUiccDeleteAnrReq ( SimManagerData *simData );
void SimUiccReadPbDialNumReq ( SimManagerData *simData );
void SimUiccHiddenKeyFunctionReq ( SimManagerData *simData );
void SimUiccWritePbDialNumReq ( SimManagerData *simData );
void SimUiccDeletePbDialNumReq ( SimManagerData *simData );
void SimUiccListPbDialNumExtReq ( SimManagerData *simData );
void SimUiccListPbDialNumExtReqNonUicc (SimManagerData * simData );
void SimUiccReadDialNumReq ( SimManagerData  *simData );
void SimUiccReadPbCcpReq ( SimManagerData  *simData  );
void SimUiccReadPbUidReq ( SimManagerData  *simData );
#endif
/*CQ00135815, Cgliu, 2022-03-04,End  */
    /***********, Cgliu, 2022-11-09,Begin*/ 
#if 0
#if defined (UPGRADE_3G)
#if defined (UPGRADE_GPRS)
void SimUiccListAclReq (SimManagerData *simData);
void SimUiccWriteAclReq (SimManagerData *simData);
void SimUiccDeleteAclReq (SimManagerData *simData);
void SimUiccSetAclReq (SimManagerData *simData);
#endif
#endif
#endif 	
	/***********, Cgliu, 2022-11-09,End  */	

/*CPHS functions*/
#if 0
void SimUiccReadCphsDataReq   ( SimManagerData  *simData );
void SimUiccCphsReadCffReq  ( SimManagerData  *simData );
void SimUiccCphsWriteCffReq  ( SimManagerData  *simData );
void SimUiccCphsReadVmwfReq  ( SimManagerData  *simData );
void SimUiccCphsWriteVmwfReq  ( SimManagerData  *simData );

void SimUiccCphsWriteCspEntryReq  ( SimManagerData  *simData );
void SimUiccCphsInfoNumStatusReq  ( SimManagerData  *simData );
void SimUiccCphsListInfoNumReq  ( SimManagerData  *simData );
void SimUiccCphsReadInfoNumReq  ( SimManagerData  *simData );
void SimUiccCphsWriteInfoNumReq  ( SimManagerData  *simData );
#endif
void SimUiccReadEfHpplmnReq ( SimManagerData  *simData );
void SimUiccReadEfActingHplmnReq    ( SimManagerData  *simData );
void SimUiccReadEfRatReq    ( SimManagerData  *simData );
#if 0
/* Add by jungle for CQ00083026 on 2015-01-26 Begin */
void SimUiccCphsWriteCfisReq ( SimManagerData  *simData ); /*AT&T Smart Card -BT71*/
/* Add by jungle for CQ00083026 on 2015-01-26 End */
#endif
/*Modfication by zhjzhao for CQ00013425 Merge from LTE_PS_DEV to NeZha_DEV_Daily 10-09-11, Begin*/
/*Modification for CQ00004507, cgLiu,Jun.02 2010, begin*/
#if defined (UPGRADE_LTE)
void SimUiccReadNcpipReq ( SimManagerData  *simData );
#endif /*end UPGRADE_LTE*/
/*Modification for CQ00004507, cgLiu,Jun.02 2010, end  */
/*Modfication by zhjzhao for CQ00013425 Merge from LTE_PS_DEV to NeZha_DEV_Daily 10-09-11, End*/
#endif
#endif
#if defined (UPGRADE_SIM_APP_TOOLKIT)
/* Add by jungle for CQ00055532 on 2014-03-05 Begin */
void SimatStkRawDataRsp      ( SimManagerData  *simData );
/* Add by jungle for CQ00055532 on 2014-03-05 End */
void SimatInternalFetchReq    ( SimManagerData  *simData );
void SimatDisplayTextRsp      ( SimManagerData  *simData );
void SimatGetInkeyRsp         ( SimManagerData  *simData );
void SimatGetInputRsp         ( SimManagerData  *simData );
void SimatPlayToneRsp         ( SimManagerData  *simData );
void SimatSetUpMenuRsp        ( SimManagerData  *simData );
void SimatSelectItemRsp       ( SimManagerData  *simData );
void SimatSendSmRsp           ( SimManagerData  *simData );
#if 0
void SimatSendSsRsp           ( SimManagerData  *simData );
void SimatSetUpCallRsp        ( SimManagerData  *simData );
#endif
void SimatRefreshRsp          ( SimManagerData  *simData );
void SimatProvideLocalInfoRsp ( SimManagerData  *simData );
#if defined(SUPPORT_SMS)
void SimatSmsPpDownloadReq    ( SimManagerData  *simData );
#endif
#if 0
void SimatCbDownloadReq       ( SimManagerData  *simData );
#endif
void SimatMenuSelectionReq    ( SimManagerData  *simData );
#if 0
void SimatCcCallSetUpReq      ( SimManagerData  *simData );
void SimatCcSsOperationReq    ( SimManagerData  *simData );
#endif
# if defined (UPGRADE_SAT97)
void SimatSendUssdRsp         ( SimManagerData  *simData );
void SimatSetUpEventListRsp   ( SimManagerData  *simData );
void SimatMoSmControlReq      ( SimManagerData  *simData );
void SimatEventDownloadReq    ( SimManagerData  *simData );
void SimatSendSavedTermProfile  ( SimManagerData  *simData );
void SimatSendTermProfileReq  ( SimManagerData  *simData );
void SimatNetworkMeasReq      ( SimManagerData  *simData );
void SimatGmmLocalInfoRsp     ( SimManagerData  *simData );
# endif
# if defined (UPGRADE_SAT98)
void SimatTimerManagementRsp      ( SimManagerData  *simData );
void SimatSetUpIdleModeTextRsp    ( SimManagerData  *simData );
void SimatRunAtCommandRsp         ( SimManagerData  *simData );
void SimatSendDtmfRsp             ( SimManagerData  *simData );
void SimatLanguageNotificationRsp ( SimManagerData  *simData );
void SimatTimerExpirationReq      ( SimManagerData  *simData );
void SimatReadImageRecReq         ( SimManagerData  *simData );
void SimatReadImageInstDataReq    ( SimManagerData  *simData );
void SimatListImageRecReq         ( SimManagerData  *simData );
#  if defined (UPGRADE_SAT99)
void SimatLaunchBrowserRsp        ( SimManagerData  *simData );
void SimatOpenChannelRsp          ( SimManagerData  *simData );
void SimatCloseChannelRsp         ( SimManagerData  *simData );
void SimatReceiveDataRsp          ( SimManagerData  *simData );
void SimatSendDataRsp             ( SimManagerData  *simData );
void SimatGetChannelStatusRsp     ( SimManagerData  *simData );
#  endif
# endif
#if defined(UPGRADE_UICC_RECOVERY)
void UiccRecoveryPinStore ( SimManagerData        *simData,
                              SimUiccLogicalChannel      logicalChannel);
void      UiccRecoveryPinClean( SimManagerData        *simData, SimUiccLogicalChannel      logicalChannel);
void UiccRecoveryDataClean( SimManagerData        *simData);
Boolean UiccRecoveryCheckSavedPinValid( SimManagerData        *simData, SimUiccLogicalChannel      logicalChannel);
/* Mod by jungle for ********** on 2014-11-13 Begin */
void UiccRecoveryPinRead( SimManagerData        *simData, SimUiccLogicalChannel      logicalChannel,
                                 SimChvValue *dstValue, SimUiccKeyRefValue *key);
void UiccRecoveryPinSave( SimManagerData  *simData, SimUiccLogicalChannel  logicalChannel,
                                 SimChvValue *srcValue, SimUiccKeyRefValue key);
Int32 UiccSecsSinceStartup(void);
void UiccMgrSResetInit( SimManagerData        *simData);
void UiccMgrSResetIdle( SimManagerData        *simData);
/* Mod by jungle for ********** on 2014-11-13 End */
#endif
#endif
/***********, cgliu, 2012-06-04,begin*/
void SimAlsiOpenLogicalChannelReq( SimManagerData  *simData );
void SimAlsiCloseLogicalChannelReq( SimManagerData  *simData );
/***********, cgliu, 2012-06-04,end  */

void SimUiccAppPinReq( SimManagerData  *simData ); /* Add by jungle for ********** on 2014-04-02 */
/***********,cp reboot soft hotplug timer when sim process alsiTerminateSessionReq,perse,2020-9-22,begin*/
void SimUiccHotPlugSoftScanTimerstart (SimManagerData *simData,Int8 index);
/***********,cp reboot soft hotplug timer when sim process alsiTerminateSessionReq,perse,2020-9-22,end*/
#if defined (UPGRADE_CSG1)						
/*------------------ CSG  -------------------*/
void SimUicciReadACsgListReq ( SimManagerData  *simData );
void SimUiccWriteACsgReq ( SimManagerData  *simData );
void SimUiccReadOCsgListReq ( SimManagerData  *simData );
void SimUiccReadCsgTypeReq( SimManagerData  *simData );
void SimUiccWriteCsgTypeReq( SimManagerData  *simData );
void SimUiccReadHomeNodeBNameReq( SimManagerData  *simData );
void SimUiccWriteHomeNodeBNameReq ( SimManagerData  *simData );


void simUiccFreeACsgList( SimManagerData         *simData);
void SimUiccInitACsgList( SimManagerData         *simData);
#endif // (UPGRADE_CSG1)						
/* END OF FILE */

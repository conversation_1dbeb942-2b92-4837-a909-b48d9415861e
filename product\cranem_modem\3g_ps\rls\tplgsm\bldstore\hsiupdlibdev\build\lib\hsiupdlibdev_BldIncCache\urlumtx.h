/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urlumtx.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2007/03/19 13:59:30 $
 **************************************************************************
 * File Description:
 *
 *      Header file for UrlUmTx.c.
 *      Contains function call declarations, constants and types for use
 *      by other URLC modules.
 **************************************************************************/

#if !defined (URLUMTX_H)
#define       URLUMTX_H

/***************************************************************************
* Nested Include Files
***************************************************************************/
#include <urlumtyp.h>
#include <umacerlcif.h>
#include <urlDebugIf.h>

/***************************************************************************
 * Typed Constants
 ***************************************************************************/

/***************************************************************************
*  Macro Functions
***************************************************************************/

/* Macro to determine if a bearer is configured for loopback mode, either
 * active or pending
 */
#define URL_UMTX_LOOPBACK(entity_p)         ((entity_p)->loopback)

/* Macro to access the COUNT-C info */
#define URL_UMTX_UL_COUNTC_INFO(entity_p)   ((entity_p)->ulCountcInfo)

/* Macro to determine if UL data is available for transmission */
#define URL_UM_UL_DATA_AVAILABLE(entity_p)  (Boolean)                         \
    ((entity_p)->bufferInfo.tfcSelection.info.um.numBitsForTx > 0)

/***************************************************************************
 * Function Prototypes
 ***************************************************************************/

extern  void
UrlUmTxHandler
    (UrlUmTxEntity                                    * entityData_p,
     SignalBuffer                                     * rxSignal_p
    );
extern  void
UrlUmTxUrlcUmDataReq
    (UrlUmTxEntity                                    * entityData_p ,
     UrlcUmDataReq                                    * urlcUmDataReq_p
    );
extern  void
UrlUmTxGetTransportBlock
    (UrlUmTxEntity                                    * entityData_p,
     dtcF8DescriptorInfo                              * cipherInfo_p,
     UrlcDebugUlPduInfo                               * debugPduInfo_p,
     cipherType_te									  * cipherAlogithm_p
    );
extern  void
UrlUmTxUmacTxStatusInd
    (UrlUmTxEntity                                    * entityData_p
    );
extern  void
UrlUmTxUpdateBufferInfo
    (UrlUmTxEntity                                    * entityData_p
    );
extern  void
UrlUmTxDiagnosticsReq
    (UrlUmTxEntity                                    * entityData_p
    );
extern  void
UrlUmTxSduBufferShrinkage
    (UrlUmTxEntity                                    * entityData_p
    );

#if defined UPGRADE_3G_EDCH
extern  Int32
UrlUmTxGetTransportBlockEdch
    (UrlUmTxEntity                                    * entityData_p,
     dtcF8DescriptorInfo                              * cipherInfo_p,
#if defined (URL_DEBUG_EDCH_OVER_DTC)
     UrlDebugRlcPduInfo                               * debugPduInfo_p,
#else
	 UrlcDebugUlPduInfo    		                      * debugPduInfo_p,
#endif
     UmacEdchTxReqId_t                                  edchTxReqId,
     cipherType_te									  * cipherAlogithm_p
    );
extern  void
UrlUmTxUmacTxStatusIndEdch
    (UrlUmTxEntity                                    * entityData_p,
     UmacEdchTxStatusInd                              * umacEdchTxStatusInd_p
    );
#endif /*UPGRADE_3G_EDCH*/

#if defined(UMTS7_PRE_R8)&& !defined(ENABLE_URLC_UNIT_TEST)

extern void ulbgprocessPsUlSduDiscardInd(UrlcUlSduDiscardInd *urlcUlSduDiscardInd);
extern void ulbgprocessPsXonInd(UrlcXonInd *urlcXonInd);
extern void ulbgprocessPsXoffInd(UrlcXoffInd *urlcXoffInd);
#if defined ENABLE_OPT_DATA_FLOW_OVER_SHMEM
extern void ulbgprocessPsUmDataCnf(UrlcPsUmDataCnf *urlcPsUmDataCnf);

#endif
#endif
#endif /* URLUMTX_H */

/* END OF UrlUmTx.h */

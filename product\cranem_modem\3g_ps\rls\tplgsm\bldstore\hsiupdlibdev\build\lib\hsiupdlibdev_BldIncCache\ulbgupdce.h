/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/3g.mod/lib/src/ulbgupdce.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2011/03/27 20:17:00 $
 **************************************************************************
 * File Description:
 *
 *      UPDCP Control Entity
 **************************************************************************/

#if !defined (ULBGUPDCE_H)
#define ULBGUPDCE_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <ulbgupde.h>

/***************************************************************************
* Manifest Constants
***************************************************************************/

/***************************************************************************
* Macro Functions
***************************************************************************/

/***************************************************************************
* Types
***************************************************************************/

/***************************************************************************
* Typed Constants
***************************************************************************/

/***************************************************************************
* Function Prototypes
***************************************************************************/

Boolean UlbgUpdcpCSap(XRabmEntity *sn,SignalBuffer *sigBuf);

void UpdceSendStatusInd(XRabmEntity *sn,UpdcpStatus status);

#endif

/* END OF FILE */

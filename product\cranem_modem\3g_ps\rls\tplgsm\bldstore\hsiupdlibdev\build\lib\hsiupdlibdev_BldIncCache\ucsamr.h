/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/api/inc/ucsamr.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/27 15:10:43 $
 **************************************************************************
 * File Description:
 **************************************************************************/

#if !defined (UCSAMR_H)
#define       UCSAMR_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <uas_asn.h>
#include <ccsd_sig.h>
#include <ucstypes.h>
#include <urlc_sig.h>
#include <csdi_sig.h>
#include <ucsrabrb.h>


/***************************************************************************
*   Manifest Constants
***************************************************************************/

/***************************************************************************
*   Macros
***************************************************************************/
#define DETAILED_DEBUG_AMR

/***************************************************************************
*   Types
***************************************************************************/

/***************************************************************************
*   Function declarations
***************************************************************************/
#ifdef UPGRADE_DSDSWB_L2
extern Int8 UpActiveSimid;
#endif


/**
 *  Update a new amr state
 */
void UcsChangeAmrState (
	ScenarioAmrState state					/**<the amr state to parse*/
);

/**
 *  update a new RTS (Ready to send) value
 */
void UcsChangeUlReadyToSendValue(
	Boolean newUlReadyToSendValue 			/**<new RTS value*/
);

/**
 *  update a delayed amrDataReq (uplink voice data). 
 *			To indicate that there is no delay in uplink transmission, update with PNULL pointer
 * 			If pointer is valid, means that data arrived too late and needs to be synch'd upon TTI indication (RTS)
 */
void UcsUpdateDelayedAmrDataReqSignal (
	SignalBuffer *newDelayedAmrDataReqSignal_p	/**<an amrDataReq signal to hold in internal buffer for future sending (upon reception of RTS)*/
);

/**
 *  check if internal buffer contains a signal to send (uplink data) - 	AmrDataReq came too late
 */
Boolean UcsIsDelayedAmrDataReq (void);


/** Pass new configuration data to the AMR codec.
 *  If this is the first call for a give RAB ID, this is presumed to be a
 *  channel assignment.
 */
void
UcsAmrConfig (
  UcsRabIdentity        rabIdentity,     /**< [in] Radio access bearer identifier. */
  CsCodecType           codecType,       /**< [in] Codec type. Must be AMR. */
  Int8                  numberOfRates,   /**< [in] Number of valid uplink rates. */
  CcsdCsRate            activeCsRates[]  /**< [in] Valid uplink rate specifications. */
);


/** Tell the AMR codec to resume (e.g. after handover).
 */
void
UcsAmrResume (
  UcsRabInfo *rabInfo_p   /**< [in] Radio access bearer pointer. */
);


/** Tell the AMR codec to pause (e.g. during handover).
 */
void
UcsAmrSuspend (
  UcsRabIdentity  rabIdentity   /**< [in] Radio access bearer identifier. */
);


/** Tell the AMR codec that there's no more speech to/from the air interface.
 */
void UcsAmrRelease (
  UcsRabIdentity  rabIdentity   /**< [in] Radio access bearer identifier. */
);


/** Called by the stack to pass downlink speech to the AMR speech decoder.
 */
void UcsAmrDataInd (
  CsCodecType codecType,  /**< [in] Codec Type. */
  UrlcSduStatus   status,       /**< [in] Receiver status. */
  Int16           aBitLength,   /**< [in] Number of class A bits. */
  Int8            aDataBits [], /**< [in] Class A bits, packed. */
  Int16           bBitLength,   /**< [in] Number of class B bits. */
  Int8            bDataBits [], /**< [in] Class B bits, packed. */
  Int16           cBitLength,   /**< [in] Number of class C bits. */
  Int8            cDataBits []  /**< [in] Class C bits, packed. */
);

/**
*  Indicates that uplink speech is needed from the AMR codec
*/
void UcsAmrUlTtiInd (void);

/**
*  Pass downlink speech to the AMR codec
*/
void UcsSendUrlcAmrTmDataReq (
	CsdiAmrDataReq *amrTxFrame_p, 	/**<uplink data to send to URLC*/	
	Boolean isDelayedData 			/**<for debug printing only*/
);

/**
*  process connection establishment request from AMR
*/
void UcsAmrConnectionEstablish (
	SignalBuffer *rxSignal_p /**<pointer to SIG_AMR_CONNECTION_ESTABLISH signal*/
);

/**
*  UcsAmrConnectionClose - process connection close request from AMR
*/
void UcsAmrConnectionClose (void);

/**
 *  Handle an AmrDateReq (Uplink date from AMR)
*/

void UcsAmrDataReq (
	SignalBuffer *rxSignal_p /**<pointer to SIG_CSDI_AMR_DATA_REQ signal*/
);

#endif

/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/gp.mod/lib/src/xrabmdata.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2014/04/10 17:12:33 $
 **************************************************************************
 * File Description:
 *
 * 234G RABM entity definition
 **************************************************************************/

#ifndef XRABMDATA_H
#define XRABMDATA_H

/**** NESTED INCLUDE FILES *************************************************/

#if !defined (UPGRADE_EXCLUDE_2G)
#include <sndcdata.h>
#endif

#if defined (UPGRADE_3G)
#include <ulbgrabmdata.h>
#include <ulbgsndata.h>
#endif

#if defined(UPGRADE_LTE)
#include <lterabmdata.h>
//#include <lterabmpdp.h>
#endif

#if defined(UPGRADE_R13_CP_OPT)
#include <lterrcpdcpsig.h>
#endif

/**** CONSTANTS ************************************************************/
typedef struct XRabmPdpEntityCommonTag
{
    Nsapi                            nsapi;
    Int16                            pdpIndex;

    RadioPriorityLevel               rpl;
    QualityOfService                 negotiatedQos;

#if !defined (UPGRADE_EXCLUDE_2G) && !defined(DS3_LTE_ONLY)
    SndcpSapEntity			         *sap;
#endif

#if defined (UPGRADE_LTE)
	EpsQualityOfService              epsQos;
#endif
    KiUnitQueue                      uplinkQueue;      /*rabQueue*/
    KiUnitQueue                      downlinkQueue;    /*dlNpduQueue*/
}XRabmPdpEntityCommon;

typedef struct XRabmPdpEntityTag
{
    XRabmPdpEntityCommon             xRabmPdpEntityCommon;
#if !defined (UPGRADE_EXCLUDE_2G) && !defined(DS3_LTE_ONLY)
    SndcpPdpEntity                   snPdp;
#endif
#if defined (UPGRADE_3G) && !defined(DS3_LTE_ONLY)
    UlbgRabmPdpEntity                rabmPdp;
#endif
#if defined(UPGRADE_LTE)
    LteRabmPdpEntity                 lteRabmPdp;
#endif
}XRabmPdpEntity;

#if defined(UPGRADE_R13_CP_OPT)
typedef struct XRabmCloseTestLoopModeTag
{
    ELteUeTestLoopMode  testLoopMode;
    Int8                rlcLoopbackFlag;
    Int8                uplinkDataDelay; 
    Int8                repetitions;/*loop back mode G/H, rang 0-127*/
    Int8                bufferCpDataGH;
    KiTimer             tDelayModeGH;
	Int32               remainRepetitions;
}
XRabmCloseTestLoopMode;
#endif

typedef struct XRabmEntityTag
{
    UlbgSndcpEventData              event;
    SnRatMode                       snRatMode;
    SnRatMode                       snOldMode;
    SnRatMode                       suspendMode;
	KiTimer                         reestabRejTimer;
    KiTimer                         lteReestRejTimer;	
    KiTimer                         lteReestGuardTimer;

    XRabmPdpEntity                  *xRabmPdp[MAX_NUM_NSAPIS];
    SignedInt16                     lastAllocatedPdpIndex;      /** Highest index in the RABM PDP array representing an allocated PDP */
    Int32                           numOfAllocatedxRabmPdpEntities;

#if !defined (UPGRADE_EXCLUDE_2G) && !defined(DS3_LTE_ONLY)
    /* GSM - SNDCP only data */
    SndcpSapEntity                  sap[MAX_NUM_SAPIS];
    Boolean                         rfc1144HeaderCompAvailable;
    Boolean                         v42BisDataCompAvailable;
    SndcpVersionInfo                versionInfo;
    PendingSnSmModifyIndParams      pendingSnSmModifyIndParams;
    Int16                           dlNpduCount;                    /** Required in GSM */
    KiUnitQueue 		            txDelayQueue;	/* holds snsmActivateInd for later processing (Only used in 2G)*/
#endif

#if defined (UPGRADE_3G) && !defined(DS3_LTE_ONLY)
    /* 3G specific data */
    Boolean                         stateUpdate;                /** Signals whether some PDP context has changed after the retrieval of the last signal */
    Boolean                         FDisInitiated;
    KiTimer                         rabReestabGuardTimer;    
    Boolean                         rabEstablished[16];
#endif

#if defined (UPGRADE_LTE)
    Int16                           snDataReqCount;
    Boolean                         bReestablishOnGoing;
    Boolean                         drbEstablished[LTE_RABM_EPS_BEARER_NUM];
#endif

#if !defined (UPGRADE_EXCLUDE_2G)
    /* multi mode ISC data */
#if !defined(DS3_LTE_ONLY)
    IscPdpData                      iscPdpData[MAX_NUM_NSAPIS];  // delete this var later!!!!!!
#endif
    /* Flag to indicate if a reestablishReq has been sent to GMM for selective RAU */
    Boolean                         iscReestablishRequested;
	Boolean                         bIratDataTransferSent;
    Boolean                         iscSuspended;
	Boolean                         dsSuspended;
    Boolean                         pdpSyncFallback;
#endif
    Boolean                         iratDataTransferReqToMm;
	Boolean                         rabmDataEnqueueNotifyToMm;

	// add the global var to here for dual L+L
	Int8                            nReestGuardCount;  //timer guarder	
	Int16 							nLteRabmReestRetryTime;	
	
	Boolean                         erabmRrcConnRecoveryReest;
	Boolean 						THROTTLING_TIMER_RUNNING;
	
	LteRabmSnDataList 				lteRabmSnDataList;
	KiUnitQueue 					dataBeforeRabEstQueque;

	Boolean                         suspendByOtherSim;
	Int8                            umtsReestRetyrCount;

#if defined(UPGRADE_R13_CP_OPT)
    /* Prefer Mode */
    EpsOptPreferMode                epsOptPreferMode;
    /* Congestion falg indicated by back-off timer in CP OPtimization */
    Boolean                         congestionFlag;

    /* Sum the to-be cnf number from SM */
    Int16                           toBeCnfNum;
        
    /* Rate Control */
    KiTimer                         servingPlmnRcTimer;
    KiTimer                         apnRcTimer;
    ServingPlmnRateControl          servingPlmnRc;
    ApnRateControl                  apnRc;    
    Int16                           servingPlmnRcCounter;/* Reset when timer expire */
    Int16                           apnRcCounter;/* Reset when timer expire */

    /* This ROHC only for NAS IP Data */
    HdrCompConfigStatus             hdrCompConfigStatus;
    XRabmCloseTestLoopMode          tcModeCfg;
#endif
	#if 0
	TaskId                          dsMMTaskId;
	TaskId                          dsSMTaskId;
	TaskId                          dsERRCTaskId;
	TaskId                          dsRRCTaskId;
	TaskId                          dsDlbgTaskId;
	#endif
}XRabmEntity;

#endif

/* END OF FILE */

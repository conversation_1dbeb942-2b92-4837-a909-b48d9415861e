/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

#ifndef UTDOMAUTH_H
#define UTDOMAUTH_H

/*                COMPILE SWITCH ERROR HANDLING
   The following lines handle errors in compile switches definition. */

#if defined (UPGRADE_SYS_TOOLS)
#if defined (UT_DOM_USE_DEBUG_OUTPUT_MANAGER)

#if !defined (UT_DOM_CONFIGURATION)
#error DOM: UT_DOM_CONFIGURATION must be specified in build file
#elif !defined DOM_TX_PORT   /* defined in utdomgr.c */
#error Software does not currently support selected UT_DOM_CONFIGURATION
#endif

#if !defined (UT_DOM_PIN_FOR_TX)
#error DOM: UT_DOM_PIN_FOR_TX must be specified in build file
#elif !defined DOM_TX_PORT   /* defined in utdomgr.c */
#error Software does not currently support selected UT_DOM_PIN_FOR_TX
#endif

#if !defined (UT_DOM_PIN_FOR_RX)
#error DOM: UT_DOM_PIN_FOR_RX must be specified in build file
#endif

#endif   /* UT_DOM_USE_DEBUG_OUTPUT_MANAGER */
#endif   /* UPGRADE_SYS_TOOLS */

#endif   /* UTDOMAUTH_H */

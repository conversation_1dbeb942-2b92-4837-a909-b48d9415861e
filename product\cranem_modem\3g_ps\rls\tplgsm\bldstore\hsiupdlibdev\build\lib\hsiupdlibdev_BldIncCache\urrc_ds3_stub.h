/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urrcer.c#9 $
 *   $Revision: #9 $
 *   $DateTime: 2007/03/30 13:53:25 $
 **************************************************************************
 * File Description:
 *
 *    URRC Connection Establish and Release sub-process functionality.
 **************************************************************************/
#if !defined (URRC_DS3_STUB_H)
#define       URRC_DS3_STUB_H
//#if !defined(SUPPORT_LTE_IRAT)//**********

//#if defined (SUPPORT_LTE_IRAT)//**********
//#if defined UPGRADE_CSG1
#include <rrcsgtypes.h>
//#endif

#if defined (UPGRADE_DSDS) && defined (UPGRADE_COMMON_STORE)
Boolean UrrCheckForegroundSearchOngoing(void);
Boolean UrrDsIsEstReqToIratDsSuspendCnf (Int8 simId);
Boolean UrrRbcMgIsSimPowerOffOnGoing(void); /************/
#endif

#if defined UPGRADE_CSG1
void UrrCsgBuildAndSendRrcAutoCsgSelectCnf(RrCsgSearchResult result, Boolean plmnCsgIdPresent, PlmnCsgId *plmnCsgId_p);/*CQ00078449 - modify*/
void UrrCsgBuildAndSendRrcCsgListCnf(RrCsgSearchResult searchResult, Int8 numCsgs, RrCsgInfo *csgs_p);/*CQ00078449 - modify*/
void UrrCsgBuildAndSendCsgListReq(RrCsgSearchType searchType, FddBandsMask fddBands, NetworkMode mode, Boolean umtsSupported, Boolean LteSupported);
/*CQ0085671 - Deleted CQ0070940*/
#endif

#endif




/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/gp.mod/lib/src/ulbgsndata.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2011/03/06 15:44:33 $
 **************************************************************************
 * File Description:
 *
 * ULBG SNDCP data definition - this contains state information for each NSAPI
 * as well as global SNDCP variables
 **************************************************************************/

#ifndef ULBGSNDATA_H
#define ULBGSNDATA_H

/**** NESTED INCLUDE FILES *************************************************/

#if !defined (UPGRADE_EXCLUDE_2G)
#include <sndcdata.h>

#endif

#if defined (UPGRADE_3G)
#include <ulbgrabmdata.h> 
#endif

/**** CONSTANTS ************************************************************/

#define M_IsNsapiSet(nsapiBitmap, nsapi) (nsapiBitmap & (1 << nsapi))?TRUE:FALSE
#define M_SetNsapi(nsapiBitmap, nsapi) (nsapiBitmap |= (1 << nsapi))
/* Added by zuohuaxu for ********** 20120627, begin */
#define M_SetNsapiUlDataStatus(nsapiBitmap, nsapi) (nsapiBitmap |= (1 << nsapi))
/* Modified by zuohuaxu for ********** 20120627, end */
#define M_RemoveNsapi(nsapiBitmap, nsapi) (nsapiBitmap &= ~(1 << nsapi))

/* Should be <= PPP queue size */
#define MAX_DL_NPDU_COUNT 20

/* Added by Daniel for ********** 20101125, begin */
#define	SN_REESTAB_REJECT_GUARD_TIMER			10
#define SN_REESTAB_REJECT_GUARD_TIMER_VALUE  	SECONDS_TO_TICKS(5)
/* Added by Daniel for ********** 20101125, end */

#define ULBG_TASK_1                     1
#define ULBG_TASK_2                     2

//#define NAS_UPLANE_HWM                  500

/**** TYPEDEFS *************************************************************/

/**\struct SndcpEventDataTag
 * \brief
 */
typedef struct UlbgSndcpEventDataTag
{
    SignalBuffer        *receivedSig_p;       /* buffer received from KiReceive   */
}
UlbgSndcpEventData;

/**\var SnRatMode
 * \brief
 */
//ICAT EXPORTED ENUM
typedef enum SnRatModeTag
{
    SN_RAT_MODE_GSM,
    SN_RAT_MODE_UMTS
#if defined (UPGRADE_LTE)
    ,SN_RAT_MODE_LTE
#endif
    ,SN_RAT_MODE_NULL    
}
SnRatMode;

#if defined (UPGRADE_3G)
#if !defined (UPGRADE_EXCLUDE_2G)
/* This indicates the state of a PDP with respect to ISC */
/**\var PdpIscState
 * \brief
 */

//ICAT EXPORTED ENUM
typedef enum PdpIscStateTag
{
    PDP_STATE_GSM,
    PDP_STATE_UMTS
#if defined (UPGRADE_LTE)
    ,
    PDP_STATE_LTE
#endif
	,PDP_STATE_ISC
}
PdpIscState;


/* GSM/UMTS dual mode data for inter-system change */
/**\struct IscPdpDataTag
 * \brief
 */
typedef struct IscPdpDataTag
{
    PdpIscState   pdpIscState;
    /* Queue to store signals after suspend for ISHO occurs */
    KiUnitQueue   iscQueue1;
    /* Queue to store signals that will be transferred between RABM and SNDCP */
    KiUnitQueue   iscQueue2;
}
IscPdpData;
#endif
#endif

//ICAT EXPORTED ENUM
typedef enum snSnDataRetTag
{
	SN_SN_DATA_INVALID_STATE,
    SN_SN_DATA_RELEASE_STATE,
    SN_SN_DATA_ENQUEUE_STATE,    
    SN_SN_DATA_ENQUEUE_STATE_LLC,
    SN_SN_DATA_ISC_ENQUEUE_STATE,
    SN_SN_DATA_ISC_ENQUEUE_REESTAB_STATE,
    SN_SN_DATA_PASS_TO_LLC
}snSnDataRet;


//ICAT EXPORTED ENUM
typedef enum rabmDataRetTag
{
	RABM_DATA_INVALID_STATE,
    RABM_DATA_RELEASE_STATE,
    RABM_DATA_ENQUEUE_STATE,    
    RABM_DATA_ENQUEUE_ESTABLISH_STATE,
    RABM_DATA_ENQUEUE_INACTIVE_STATE,
    RABM_DATA_ISC_ENQUEUE_STATE,    
    RABM_DATA_ISC_ENQUEUE_REESTAB_STATE,
    RABM_DATA_PASS_TO_UPDCP,
    RABM_DATA_LOOPBACK,
    RABM_DATA_FAST_DORMANT
}rabmDataRet;


/**\struct UlbgSndcpEntityTag
 * \brief
 * A single UL DB both for UMTS and GSM .
 */
typedef struct UlbgSndcpEntityTag
{
    UlbgSndcpEventData  event;
    SnRatMode           snRatMode;
/* Added by zuohuaxu for ********** 20120627, begin */
    Boolean                FDisInitiated;
/* Added by zuohuaxu for ********** 20120627, end */
    /* Counter data */
    Int16               dlNpduCount;                    /** Required in GSM */
    
    /* Added by Daniel for ********** 20101125, begin */
	KiTimer             reestabRejTimer;
	/* Added by Daniel for ********** 20101125, end */

#if !defined (UPGRADE_EXCLUDE_2G)
    /* GSM - SNDCP only data */
    SndcpPdpEntity      *pdp_p[MAX_NUM_NSAPIS];
    SndcpSapEntity      sap[MAX_NUM_SAPIS];
    Boolean             rfc1144HeaderCompAvailable;
    Boolean             v42BisDataCompAvailable;
    SndcpVersionInfo    versionInfo;
    PendingSnSmModifyIndParams pendingSnSmModifyIndParams;
#endif

#if defined (UPGRADE_3G)
    /* 3G specific data */
    Boolean                 stateUpdate;                /** Signals whether some PDP context has changed after the retrieval of the last signal */
    SignedInt16             lastAllocatedPdpIndex;      /** Highest index in the RABM PDP array representing an allocated PDP */
    UlbgRabmPdpEntity       *rabmPdp_p[MAX_NUM_NSAPIS]; /** RABM PDP array */
    KiTimer                 rabReestabGuardTimer;
    Int32                   numOfAllocatedRabmPdpEntities;

#if !defined (UPGRADE_EXCLUDE_2G)
    /* 3G dual mode ISC data */
    IscPdpData          iscPdpData[MAX_NUM_NSAPIS];
    /* Flag to indicate if a reestablishReq has been sent to GMM for selective RAU */
    Boolean             iscReestablishRequested;
    Boolean             iscSuspended;
#endif
#endif 

	KiUnitQueue 		txDelayQueue;	/* holds snsmActivateInd for later processing (Only used in 2G)*/
}
SndcpEntity,
UlbgSndcpEntity;


/* debug print SnRatMode */
#define M_PrintSnRatMode(_currRatMode, _newRatMode, print_Index)\
        DIAG_FILTER(PS_2G, ISC, print_Index, DIAG_INFORMATION)  \
        diagPrintf("currentSnRatMode:%e{SnRatMode}, newSnRatMode:%e{SnRatMode}", _currRatMode, _newRatMode);

#endif

/* END OF FILE */

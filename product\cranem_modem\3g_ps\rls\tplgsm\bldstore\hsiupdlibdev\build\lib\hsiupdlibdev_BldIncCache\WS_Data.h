/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/


/************************************************************************/
/*                                                                      */
/* Title: IPC Comm. pacakge header file                                 */
/*                                                                      */
/* Filename:  Data.h                                                    */
/*                                                                      */
/* Authors: <AUTHORS>
/*                                                                      */
/* Target, subsystem: Common Platform, HAL                              */
/************************************************************************/
#ifndef _WS_Data_H_

  #define _WS_Data_H_
/*----------------------------------------------------------------------*/

#include "global_types.h"
#include "WS_IPCCommConfig.h"

/*----------------------------------------------------------------------*/

/********************* Sub-Package Definitions ********************/

#define DATA_CMD_HEADER_LENGTH  2       /* 2 long-words (32 bits): 8 bits data channel ID, 8 bits copy type, 16 bits data packet size,
                                           32 bits pointer to the data packet source location */

#define DATA_CMD_MSG_HEADER_DATA_CHAN_ID    0  // byte #1  (total 1 byte)
#define DATA_CMD_MSG_HEADER_DATA_COPY_MODE  1  // byte #2  (total 1 byte)
#define DATA_CMD_MSG_HEADER_DATA_SIZE       2  // byte #3  (total 2 bytes)
#define DATA_CMD_MSG_HEADER_DATA_SRC_PTR    4  // byte #5  (total 4 bytes)
#define DATA_CMD_MSG_HEADER_DATA_DST_PTR    8  // byte #6  (total 4 bytes)


/***************** macros ***********************/

#define FIND_MSG_HEADER_SIZE(sz) \
      { if (_msgTmpBuffer[CMD_MSG_HEADER_SIZE + DATA_CMD_MSG_HEADER_DATA_COPY_MODE] == IPC_CT_COPY_TO_KNOWN_DESTINATION) \
                sz = DATA_COPY_TO_KNOWN_DEST_HEADER_SIZE; \
        else if ( _msgTmpBuffer[CMD_MSG_HEADER_SIZE + DATA_CMD_MSG_HEADER_DATA_COPY_MODE] == IPC_CT_NO_COPY) \
                sz = DATA_NO_COPY_HEADER_SIZE; \
        else \
                sz = DATA_COPY_TO_UNKNOWN_DEST_HEADER_SIZE; }


/*********************** Sub-Package Parameters *************************/


/*************** Prototypes *****************/
void handleDataAllocationPointerResponseMsg(UINT16 msgOpCode, UINT16 msgLength, UINT8 *msgData);
void handleDataAttachedToMsg(void);
void dataCreateOSObjects(void);
void dataReturnOSObjects(void);







/*----------------------------------------------------------------------*/

#endif  /* _WS_Data_H_ */

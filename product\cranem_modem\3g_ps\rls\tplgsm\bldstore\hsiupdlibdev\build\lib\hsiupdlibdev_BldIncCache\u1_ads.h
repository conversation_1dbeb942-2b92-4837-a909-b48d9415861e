/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/l1/3g.mod/pub/src/u1_ads.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/11/09 16:03:40 $
 **************************************************************************
 * File Description: Action Data Structures for 3G Layer 1.
 **************************************************************************/

#ifndef U1_ADS_H
#define U1_ADS_H

/*******************************************************************************
** Includes
*******************************************************************************/

#include <system.h>

/*******************************************************************************
** Typedefs
*******************************************************************************/

/*******************************************************************************
* Typedef     : U1Debug3ParamAds
* Type        : structure
* Group       : U1, ADS
* Description : This Action Data Structure is used to provide a simple
*                   mechanism for dumping 3 Int32 parameters from one processor
*                   to another. This is used by the M_U1SyDbg3Param macro.
*******************************************************************************/
typedef struct U1Debug3ParamAdsTag
{
    /* Member: Int32 param1 = The first Int32 parameter. */
    Int32    param1;
    /* Member: Int32 param2 = The second Int32 parameter. */
    Int32    param2;
    /* Member: Int32 param3 = The third Int32 parameter. */
    Int32    param3;

} U1Debug3ParamAds;

/*******************************************************************************
* Typedef     : U1Debug6ParamAds
* Type        : structure
* Group       : U1, ADS
* Description : This Action Data Structure is used to provide a simple
*                   mechanism for dumping 6 Int32 parameters from one processor
*                   to another. This is used by the M_U1SyDbg6Param macro.
*******************************************************************************/
typedef struct U1Debug6ParamAdsTag
{
    /* Member: Int32 param1 = The first Int32 parameter. */
    Int32    param1;
    /* Member: Int32 param2 = The second Int32 parameter. */
    Int32    param2;
    /* Member: Int32 param3 = The third Int32 parameter. */
    Int32    param3;
    /* Member: Int32 param4 = The fourth Int32 parameter. */
    Int32    param4;
    /* Member: Int32 param5 = The fifth Int32 parameter. */
    Int32    param5;
    /* Member: Int32 param6 = The sixth Int32 parameter. */
    Int32    param6;

} U1Debug6ParamAds;

#endif
/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/tplgsm/utinc/uttcp.h#6 $
 *   $Revision: #6 $
 *   $DateTime: 2006/04/18 17:25:25 $
 **************************************************************************
 * File Description:
 *
 * TCP header definition - this is a generic definition for any
 * Internet TCP packet. This file is based on the file:
 *
 * tcp.h ******* (University of California at Berkeley) 2/7/88
 **************************************************************************/

#ifndef UTTCP_H
#define UTTCP_H

#include <system.h>
#include <utendian.h>

typedef Int32 tcp_seq;

/*
TCP header.
Per RFC 793, September, 1981.
*/
/*Protocol type in IP header */
#define IP_PROTO_TCP  6

/* components of th_flags below */
#define TH_FIN  0x01
#define TH_SYN  0x02
#define TH_RST  0x04
#define TH_PUSH 0x08
#define TH_ACK  0x10
#define TH_URG  0x20

struct tcphdr
{
    Int16      th_sport;        /* source port */
    Int16      th_dport;        /* destination port */
    Int32      th_seq;          /* sequence number */
    Int32      th_ack;          /* acknowledgement number */
    Int8       th_off;          /* data offset + unused low order nibble */
    Int8       th_flags;
    Int16      th_win;          /* window */
    Int16      th_sum;          /* checksum */
    Int16      th_urp;          /* urgent pointer */
};

#endif

/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/gp.mod/lib/src/ulbgrasnmain.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2011/03/06 20:18:06 $
 **************************************************************************
 * File Description:
 *
 * ULBG main module external functions
 **************************************************************************/

#ifndef ULBGRASNMAIN_H
#define ULBGRASNMAIN_H

/***************************************************************************
 * Nested Include Files
 **************************************************************************/

#include <system.h>
#include <kernel.h>
#include <xrabmdata.h>
#include <ulbgdlbg_sig.h>

#ifdef PC_TTCN_INT_TEST
extern KI_ENTRY_POINT
UpUlbgTask(SignalBuffer *sigBuf);
#else
extern KI_ENTRY_POINT
UpUlbgTask (void);
#endif

#endif

/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/******************************************************************************
 *
 *  File          : L1DspFnc_INT.h
 *  Project       :
 *  Unit          : Device Access Layer
 *  Status        : [designed | coded | tested | integrated]
 *  Designed by   :
 *  Coded by      :
 *  Tested by     :
 *  Creation Date : 23 Nov 1998
 *
 * Copyright (c) 2000 TTP Communications Ltd
 *
 ******************************************************************************
 *
 *  File Description :
 *
 *  DSP Task Interface definition
 *  This file defines the codes for each DSP task.
 *  The DSP run-time initialised a vector table with the task address in the offset
 *  defined for each task in this file.
 *
 * This file is also used directly in the MDF DSP logger class DLL to define
 * the matching set of Task Names, for decoding of the TID field in the
 * DSP Task Data Queue Entries. Hence the bottom section of the file
 * has to have the name strings in.
 *
 * The order is not important, only that the numbers are unique and that there
 * are not too many gaps as these waste DSP DM table space.
 *
 ******************************************************************************
 *
 */

/*This file is multiply included by MDF so avoid the double inclusion protectino*/
#ifndef L1DSPFNC_H
#define L1DSPFNC_H


/* Core and standard tasks ...
 * DO NOT change the order or names without rebuilding ALL platforms
 */

//ICAT EXPORTED ENUM
typedef enum{
 DSP_TID_NO_TASK                 	  =	0,
 DSP_TID_START                   	  =	DSP_TID_NO_TASK,
 DSP_TID_HSLCONFIG               	  =	1,
 DSP_TID_HSLSENDL1MSG            	  =	2,
 DSP_TID_HSLL1LOG                	  =	3,
 DSP_TID_GETBUF                  	  =	4,
 DSP_TID_FREEBUF                 	  =	5,
 DSP_TID_BUFFFILL                	  =	6,
 DSP_TID_RFINIT                  	  =	7,
 DSP_TID_RCSYNTH                 	  =	8,
 DSP_TID_CRLPRIMEAGC             	  =	9,
 DSP_TID_SETGAIN                 	  =	10,
 DSP_TID_STARTRX                 	  =	11,
 DSP_TID_STOPRX                  	  =	12,
 DSP_TID_CRLSNIFF			    	  =	13,
 DSP_TID_CRLFASTAGC              	  =	14,
 DSP_TID_CRLMONITOR			    	  =	15,
 DSP_TID_FBSEARCH                	  =	16,
 DSP_TID_ENCODEAB                	  =	17,
 DSP_TID_ENCODENB                	  =	18,
 DSP_TID_RACH                    	  =	DSP_TID_ENCODEAB,
 DSP_TID_STARTTX                 	  =	19,
 DSP_TID_TXPRIME                 	  =	DSP_TID_STARTTX,
 DSP_TID_SYNTHCONST              	  =	20,
 DSP_TID_AUULPROC                     = 21,
 DSP_TID_AUDLPROC                     = 22,
 DSP_TID_SPEECHENCODE            	  =	23,
 DSP_TID_SPEECHDECODE            	  =	24,
 DSP_TID_VBIFCONTROL             	  =	25,
 DSP_TID_CIPLD                   	  =	26,
 DSP_TID_CIPGO                   	  =	27,
 DSP_TID_CIPSTART                	  =	28,
 DSP_TID_CIPRESUME               	  =	29,
 DSP_TID_POWERRAMP               	  =	30,
 DSP_TID_AFE                     	  =	31,
 DSP_TID_FORMATNB                	  =	32,
 DSP_TID_STOPTX                  	  =	33,
 DSP_TID_AMRCFG                  	  =	34,
 DSP_TID_VOICE                   	  =	35,
 DSP_TID_WB_MEAS_RF_SWITCH_GSM_TO_WB  =	36,
 DSP_TID_WB_MEAS_RF_SWITCH_WB_TO_GSM  = 37,
 DSP_TID_WB_MEAS_AGC         		  =	38,
 DSP_TID_WB_MEAS_RSSI      			  =	39,
 DSP_TID_WB_MEAS_EBNORSCP_CPICH_START = 40,
 DSP_TID_WB_MEAS_EBNORSCP_CPICH_STOP  =	41,
 DSP_TID_GSM_POWER_MANAGEMENT         = 42,
 DSP_TID_CRLEQDECODEUSF_BEQ           =	43,
 DSP_TID_CRLENCODENB             	  =	44,
 DSP_TID_CRLFORMATNB             	  =	45
 ,
 DSP_TID_PERIPHERALSDBINIT     		  = 46
}DspTaskId_te;


#endif //  L1DSPFNC_INT_H

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urrsirut.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/27 15:10:43 $
 **************************************************************************
 * File Description:
 *
 *    Header file for URRSIRUT.C. Contains function call declarations
 *    for use by other URRC modules.
 **************************************************************************/

#if !defined (URRSIRUT_H)
#define       URRSIRUT_H

/***************************************************************************
* Nested Include Files
***************************************************************************/
#include <urrtypes.h>

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/

/***************************************************************************
*   Macro Functions
***************************************************************************/

/***************************************************************************
 * Types
 ***************************************************************************/
typedef struct LinkedListElementTag
{
    /* No data element */
    struct LinkedListElementTag *next;
}
LinkedListElement;

typedef struct LinkedListTag
{
    struct LinkedListElementTag *firstElement;
}
LinkedList;

typedef struct UrrSirAllocatedMemListElementTag
{
    void                                    *allocatedBlock_p;
    struct UrrSirAllocatedMemListElementTag *next;
}
UrrSirAllocatedMemListElement;

typedef struct UrrSirAllocatedMemListTag
{
    struct UrrSirAllocatedMemListElementTag *firstElement;
}
UrrSirAllocatedMemList;

/***************************************************************************
 * Typed Constants
 ***************************************************************************/

/***************************************************************************
 * Function Prototypes
 ***************************************************************************/

/* **********, modified begin */
#if defined (DEBUG_MEMORY_LEAK_ON_PC)
void UrrSirDeepCopyLinkedList ( char *const file_p, Int32 line,UrrSirAllocatedMemList  *memList_p,
#else
void UrrSirDeepCopyLinkedList (UrrSirAllocatedMemList  *memList_p,
#endif
/* **********, modified end */
                               const void *const listHeadSrc_p,
                               const void *listHeadDest_p,
                               Int16 sizeOfElement);

/* **********, deleted begin */
// void UrrSirExtractParameters (UrrSirAllocatedMemList  *memList_p,
//                              void *uSIB_p, void **dest_p, Int16 structureSize);
/* **********, deleted end */

void UrrSirCreateLinkedListElement (UrrSirAllocatedMemList  *memList_p,
                                    void **listHeadSrc_p,
                                    void *insertPos_p,
                                    void **element_p,
                                    Int16 sizeOfElement);
                                   


void UrrSirConcatenateMemLists (UrrSirAllocatedMemList *destList_p, 
    UrrSirAllocatedMemList *srcList_p);

void UrrSirInitAllocatedMemList (UrrSirAllocatedMemList  *memList_p);

void UrrSirExtractTFCS (UrrSirAllocatedMemList  *memList_p,
                        UTFCS *sibConfig_p, UTFCS **destConfig_p);

void UrrSirClearMemoryList (UrrSirAllocatedMemList *memList_p);

void UrrSirExtractCommonTransportFormatSet (UrrSirAllocatedMemList  *memList_p,
                                            UCommonTransChTFS *sibConfig_p,
                                            UCommonTransChTFS **destConfig_p);

void UrrSirExtractDedicatedTransportFormatSet (
                                        UrrSirAllocatedMemList  *memList_p,
                                        UDedicatedTransChTFS    *sibConfig_p,
                                        UDedicatedTransChTFS   **destConfig_p);

Int16 UrrSirGetNoOfTbs (UNumberOfTransportBlocks *noTbs_p);

Int16 UrrSirGetCommonRlcSize (UCommonDynamicTF_Info_rlc_Size  *rlcSize_p);

Int16 UrrSirGetDedicatedRlcSize (UDedicatedDynamicTF_Info_rlc_Size  *rlcSize_p);

/* **********, modified begin */
#if defined (DEBUG_MEMORY_LEAK_ON_PC)
void AddToMemList (  char *const file_p, Int32 line,UrrSirAllocatedMemList *memList_p, void *memPtr_p);
#else
void AddToMemList (UrrSirAllocatedMemList *memList_p, void *memPtr_p);
#endif
/* **********, modified end */

#endif  /* URRSIRUT_H */

/* END OF FILE */

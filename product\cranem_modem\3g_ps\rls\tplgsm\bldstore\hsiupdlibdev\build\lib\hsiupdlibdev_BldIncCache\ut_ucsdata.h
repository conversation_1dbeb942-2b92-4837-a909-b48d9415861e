/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/tplgsm/utinc/ut_ucsdata.h#11 $
 *   $Revision: #11 $
 *   $DateTime: 2006/12/12 19:03:51 $
 **************************************************************************
 * File Description
 * ----------------
 *   Include file for Unicode character data. Uses data downloaded
 *   from Unicode consortium. The Unicode consortium's terms of use
 *   for this data are reproduced below:
 *
 * UCD Terms of Use
 *
 * Disclaimer
 *
 * The Unicode Character Database is provided as is by Unicode,
 * Inc. No claims are made as to fitness for any particular
 * purpose. No warranties of any kind are expressed or implied. The
 * recipient agrees to determine applicability of information
 * provided. If this file has been purchased on magnetic or optical
 * media from Unicode, Inc., the sole remedy for any claim will be
 * exchange of defective media within 90 days of receipt.
 *
 * This disclaimer is applicable for all other data files accompanying
 * the Unicode Character Database, some of which have been compiled by
 * the Unicode Consortium, and some of which have been supplied by
 * other sources.
 * 
 * Limitations on Rights to Redistribute This Data
 * 
 * Recipient is granted the right to make copies in any form for
 * internal distribution and to freely use the information supplied in
 * the creation of products supporting the UnicodeTM Standard. The
 * files in the Unicode Character Database can be redistributed to
 * third parties or other organizations (whether for profit or not) as
 * long as this notice and the disclaimer notice are
 * retained. Information can be extracted from these files and used in
 * documentation or programs, as long as there is an accompanying
 * notice indicating the source.
 **************************************************************************/

#ifndef UT_UCSDATA_H
#define UT_UCSDATA_H


/***************************************************************************
 * Nested Include Files
 **************************************************************************/

#include <system.h>

/***************************************************************************
 * Manifest Constants
 **************************************************************************/

/***************************************************************************
 * Typed Constants
 **************************************************************************/
/***************************************************************************
 * Type Definitions
 **************************************************************************/

/* Defines the character types available */
typedef enum 
{
  E_Lu = 0, /* Letter, Uppercase */
  E_Ll,     /* Letter, Lowercase */
  E_Lt,     /* Letter, Titlecase */
  E_Lm,     /* Letter, Modifier */
  E_Lo,     /* Letter, Other */
  E_Mn,     /* Mark, Non-Spacing */
  E_Mc,     /* Mark, Spacing Combining */
  E_Me,     /* Mark, Enclosing */
  E_Nd,     /* Number, Decimal */
  E_Nl,     /* Number, Letter */
  E_No,     /* Number, Other */
  E_Pc,     /* Punctuation, Connector */
  E_Pd,     /* Punctuation, Dash */
  E_Ps,     /* Punctuation, Open */
  E_Pe,     /* Punctuation, Close */
  E_Pi,     /* Punctuation, Initial quote (may behave like Ps or Pe depending on usage) */
  E_Pf,     /* Punctuation, Final quote (may behave like Ps or Pe depending on usage) */
  E_Po,     /* Punctuation, Other */
  E_Sm,     /* Symbol, Math */
  E_Sc,     /* Symbol, Currency */
  E_Sk,     /* Symbol, Modifier */
  E_So,     /* Symbol, Other */
  E_Zs,     /* Separator, Space */
  E_Zl,     /* Separator, Line */
  E_Zp,     /* Separator, Paragraph */
  E_Cc,     /* Other, Control */
  E_Cf,     /* Other, Format */
  E_Cs,     /* Other, Surrogate */
  E_Co,     /* Other, Private Use */
  E_Cn      /* Other, Not Assigned (no characters in the file have this property) */
} E_UCSCategory;


/* Defines the bidirectional character types available */
typedef enum 
{
  E_L = 0,  /* Left-to-Right */
  E_LRE,    /* Left-to-Right Embedding */
  E_LRO,    /* Left-to-Right Override */
  E_R,      /* Right-to-Left */
  E_AL,     /* Right-to-Left Arabic */
  E_RLE,    /* Right-to-Left Embedding */
  E_RLO,    /* Right-to-Left Override */
  E_PDF,    /* Pop Directional Format */
  E_EN,     /* European Number */
  E_ES,     /* European Number Separator */
  E_ET,     /* European Number Terminator */
  E_AN,     /* Arabic Number */
  E_CS,     /* Common Number Separator */
  E_NSM,    /* Non-Spacing Mark */
  E_BN,     /* Boundary Neutral */
  E_B,      /* Paragraph Separator */
  E_S,      /* Segment Separator */
  E_WS,     /* Whitespace */
  E_ON,     /* Other Neutrals */  
  E_LIG,    /* not a Unicode type, used to indicate that a string sequence is
             * a ligature */
  E_COMBINE, /* not a Unicode type, used to indicate that a string sequence is
             * a series of combining characters */
  E_SUBSTITUTION, /*not a Unicode type, used to indicate that a string sequence is
                   * substitutued by a different string of characters*/
  E_INLINE_OBJECT,      /* Inline object */
  E_EMOTICON /*This is an emoticon object*/
} E_UCSBidiType;

/* Defines a character's numeric value */
typedef enum 
{
  E_0 = 0,
  E_1,
  E_2,
  E_3,
  E_4,
  E_5,
  E_6,
  E_7,
  E_8,
  E_9,
  E_X
} E_UCSNumeric;

/* Defines a character's mirror property */
typedef enum 
{
  E_N = 0,   /* Not mirrored */
  E_Y = 1    /* Mirrored */
} E_UCSMirrored;

/* Defines a structure containing data for a single Unicode
   character */
typedef struct
{
  Int16          codePoint;
  E_UCSCategory  category;
  Int8           combiningClass;
  E_UCSBidiType  bidiType;
  E_UCSNumeric   numberValue;
  E_UCSMirrored  mirrored;
} T_UCSCharData;

/* Defines groups of char data */
typedef struct
{
  Int16                page;
  const T_UCSCharData  *data;
  Int16                length;
  Int8                 start;
} T_UCSPageData;

/* Defines pairs of mirrored characters */
typedef struct
{
  Int16  ch;
  Int16  mirror;
} T_UCSMirrorChar;

/* Structure to define subsections of a string */
typedef struct
{
  Int16 *str;           /* Pointer to the section of the string */
  Int16 length;         /* The length of the section */
  Int8  level;          /* Embedding level of this section */
  E_UCSBidiType type;   /* Type of the chars in the section */
  union
  {
    /* this union contains data specific to 'special' items in the text
     * such as ligatures, combining sequences, etc. The contents of the 
     * union are defined by the value of the 'type' field, above.
     *
     * ligature: selected by type E_LIG.
     *
     */
    Int16 ligature;     /* If the section makes up a ligature, this is the 
                         * corresponding ligature character */
    TP_TChar* pSubstitutionSequence;
    Int8 emoticonIndex; /*This indicate the index of the emoticon represented by this section*/
  } auxData;
} T_UCSStrSection;
















                         
#endif /* UPGRADE_UCS_BIDI  */
/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/tplgsm/utinc/ut_vob.h#5 $
 *   $Revision: #5 $
 *   $DateTime: 2006/03/29 15:43:20 $
 **************************************************************************
 * File Description
 * ----------------
 *
 * utility library for working with VObjects: vcards, vcalendars, imelodies.
 * basic format and operation conforming to rfc2425
 **************************************************************************/

#ifndef UT_VOB_H
#define UT_VOB_H


/***************************************************************************
 * Include Files
 ***************************************************************************/
#include <kernel.h>

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/

#define VOBJ_BEGIN          "begin"
#define VOBJ_END            "end"

#define VOBJ_VCARD_ID       "vcard"
#define VOBJ_VCALENDAR_ID   "vcalendar"
#define VOBJ_VEVENT_ID      "vevent"
#define VOBJ_VTODO_ID       "vtodo"
#define VOBJ_IMELODY_ID     "imelody"

#define VOBJ_PARAM_ENC_ID      "encoding" 
#define VOBJ_PARAM_ENC_FILEREF "fileref"  /* non standard          */
#define VOBJ_PARAM_ENC_BASE64  "base64"   /* base64 as per rfc2045 */
#define VOBJ_PARAM_ENC_BASE64b "b"        /* base64 as per rfc2047 */

#define VOBJ_CAR_RET_CHAR   '\r'
#define VOBJ_LINEFEED_CHAR  '\n'
#define VOBJ_PERCENT_CHAR   '%'
#define VOBJ_PARAM_SEP      ';'
#define VOBJ_PARAM_EQUALS   '='
#define VOBJ_VALUE_SEP      ':'
#define VOBJ_CRLF           "\r\n" 

#define VOBJ_WRAP_INITIAL   25
#define VOBJ_WRAP_COLUMN    75
#define VOBJ_WRAP_EOL       "\r\n " 


/***************************************************************************
 * Type Definitions
 ***************************************************************************/

struct UtVobjectTag;
typedef struct UtVobjectTag UtVobject;

#if defined (USE_UCS2_VOBJECTS)
typedef Int16 UtVobjCharCode;
#else
typedef Char UtVobjCharCode;
#endif

typedef enum UtVobjModeTag
{
  VOBJ_READER,
  VOBJ_WRITER
} UtVobjMode;

typedef enum UtVobjDataEncTag
{
  VOBJ_ENC_NONE,
  VOBJ_ENC_BASE64,  
  VOBJ_ENC_FILEREF
} UtVobjDataEnc;

/***************************************************************************
 * Variables
 ***************************************************************************/


/***************************************************************************
 * Macros
 ***************************************************************************/

/*----------- utility funcs --------------*/

/* these cmp funcs assume that char string is already lowercase. */
int utVobjStrNCaseCmp( const UtVobjCharCode *uStr, 
                         const Char *cStr, 
                         Int16 n );

UtVobjCharCode* utVobjStrNCaseFind( const UtVobjCharCode *uStr, 
                                        const Char *cStr, 
                                        Int16 n );


/*----------- management funcs --------------*/

Boolean utVobjNewVobject( UtVobject       **vobj_pp,
                            UtVobjMode      mode,
                            const Char    *vobjId,
                            UtVobjCharCode  *buf, 
                            Int16         bufLength,
                            Boolean       takeBufOwnership );

UtVobjCharCode* utVobjAccessBuffer( UtVobject *vobj_p, Int16 *bufLen_p );

void utVobjSetBufOwnership( UtVobject *vobj_p, Boolean isBufOwner );

void utVobjCloseVobject( UtVobject *vobj_p );

void utVobjDestroyVobject( UtVobject **vobj_pp );


/*----------- reader funcs --------------*/

UtVobjCharCode* utVobjFirstField( UtVobject *vobj_p, 
                                      Int16 *length );

UtVobjCharCode* utVobjNextField( UtVobject *vobj_p, 
                                     Int16 *length );

UtVobjCharCode* utVobjFindFirstField( UtVobject *vobj_p, 
                                          const Char *fieldName, 
                                          Int16 *length );

UtVobjCharCode* utVobjFindNextField( UtVobject *vobj_p, 
                                         const Char *fieldName, 
                                         Int16 *length );

UtVobjCharCode* utVobjFieldName( UtVobjCharCode *field, 
                                     Int16 fieldLen, 
                                     Int16 *nameLen_p );

UtVobjCharCode* utVobjFieldValueString( UtVobjCharCode *field, 
                                            Int16 fieldLen, 
                                            Int16 *valueLen_p );

Boolean utVobjFieldValueInt( UtVobjCharCode *field, 
                               Int16      fieldLen,
                               Int32      *intValue_p );

UtVobjCharCode* utVobjFieldParams( UtVobjCharCode *field, 
                                       Int16 fieldLen, 
                                       Int16 *paramLen_p );

UtVobjCharCode* utVobjFieldParamStringByName( UtVobjCharCode *field, 
                                                  Int16      fieldLen, 
                                                  const Char *paramName,
                                                  Int16      *paramLen_p );

UtVobjCharCode* utVobjFindFieldValueString( UtVobject      *vobj_p, 
                                                const Char   *fieldName, 
                                                Int16        *valueLen_p );

Int8* utVobjFieldValueData( UtVobject      *vobj_p,
                              UtVobjCharCode *field,
                              Int16        fieldLen,
                              Int16        *dataLen_p );


/*----------- writer funcs --------------*/

void utVobjWriteChar( UtVobject      *vobj_p, 
                        const Char   c );

void utVobjWriteStringA( UtVobject      *vobj_p, 
                           const Char   *str );
                           
void utVobjWriteStringWLenA( UtVobject  *vobj_p,
                              const Char   *str,
                              Int16 len );

void utVobjWriteFieldName( UtVobject      *vobj_p, 
                             const Char   *fieldName );

void utVobjWriteFieldParam( UtVobject      *vobj_p, 
                              const Char   *paramName,
                              const Char   *paramValue );

void utVobjWriteFieldValue( UtVobject            *vobj_p, 
                              const UtVobjCharCode *value,
                              Int16              valueLen );

void utVobjWriteFieldValueA( UtVobject      *vobj_p, 
                               const Char   *value );

void utVobjWriteFieldValueInt( UtVobject      *vobj_p, 
                                 Int32         value );

Boolean utVobjWriteFieldValueData( UtVobject      *vobj_p, 
                                     const void   *data_p,
                                     Int16        dataLen,
                                     UtVobjDataEnc  encoding );

void utVobjWriteFieldEnd( UtVobject      *vobj_p );

void utVobjWriteField( UtVobject            *vobj_p, 
                         const Char         *fieldName, 
                         const UtVobjCharCode *value_p,
                         Int16              valueLen );

void utVobjDeleteField( UtVobject      *vobj_p, 
                          UtVobjCharCode *field_p, 
                          Int16        fieldLen );


#endif /* UT_VOB_H */
/* END OF FILE */






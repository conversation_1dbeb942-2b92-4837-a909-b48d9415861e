/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/tplgsm/utinc/uterr.h#12 $
 *   $Revision: #12 $
 *   $DateTime: 2006/04/20 12:15:54 $
 **************************************************************************
 * File Description:
 *
 * definition of UT error logging
 **************************************************************************/

#ifndef UTERR_H
#define UTERR_H

/**** NESTED INCLUDE FILES *************************************************/

#include <system.h>

#include <gkitask.h>


#define NoErrorBody Int8

#define MAX_UT_LOG_ERROR_SIZE 180

/**** TYPEDEFS *************************************************************/


#define ERROR_VAR ERROR_TYPE
#include <uterrdef.h>

typedef enum UtErrorTypeTag
{
#   include <uterrors.h>

    UTERR_LAST_ERROR
}
UtErrorType;


void
UtLogError (UtErrorType utErrType, void *errorData);
void utLogPdu (Boolean sending, const Int8 *pdu_p, Int16 dataLen,
               Int32 pduCounter, TaskId loggerTask);

#endif

/* END OF FILE */

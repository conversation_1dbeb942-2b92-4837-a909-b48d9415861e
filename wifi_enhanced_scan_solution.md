# WiFi增强扫描解决方案

## 问题分析

根据您提供的测试日志，当前WiFi扫描存在以下问题：
- 只扫描到8个AP，但目标MAC地址 `C6:99:0E:62:B8:66` 和 `2E:B0:52:85:24:38` 没有被发现
- 扫描参数相对保守：rounds=2, max_hotspots=8
- 单次扫描可能无法覆盖所有时间窗口内的AP

## 解决方案

### 1. 增强扫描参数
- **扫描轮数**：从2轮增加到5轮
- **最大AP数量**：从8个增加到20个（Normal模式）或30个（YIXIN模式）
- **测试超时**：从30秒增加到60秒

### 2. 多轮扫描机制
实现了3轮独立扫描，每轮间隔2秒：
- 第1轮：初始扫描
- 第2轮：补充扫描（2秒后）
- 第3轮：最终扫描（再2秒后）

### 3. 结果合并算法
- 自动去除重复AP（基于MAC地址）
- 保留信号强度最高的记录
- 累积所有轮次的发现结果

### 4. 目标MAC检测
专门检测您关注的两个MAC地址：
- `C6:99:0E:62:B8:66`
- `2E:B0:52:85:24:38`

## 技术实现

### 1. 数据结构增强
```c
#define WIFI_SCAN_TEST_MAX_ROUNDS  3      // 最大扫描轮次
static uint8_t wifi_scan_test_round = 0;  // 当前扫描轮次
static app_adp_wifi_ap_list* wifi_scan_all_results = NULL;  // 累积扫描结果
```

### 2. 结果合并函数
```c
static void Flw_WiFi_Merge_Scan_Results(app_adp_wifi_ap_list * new_results)
{
    // 检查重复MAC地址
    // 保留最强信号
    // 添加新发现的AP
}
```

### 3. 多轮扫描逻辑
```c
// 检查是否需要继续扫描
if(wifi_scan_test_round < WIFI_SCAN_TEST_MAX_ROUNDS) {
    // 延迟2秒后开始下一轮扫描
    uos_sleep(MS_TO_TICKS(2000));
    // 启动下一轮扫描
}
```

### 4. 目标检测算法
```c
// 检查目标MAC地址是否被发现
for(int i = 0; i < wifi_scan_all_results->count; i++) {
    // 检查 C6:99:0E:62:B8:66
    if(wifi_scan_all_results->item[i].mac[0] == 0xC6 && 
       wifi_scan_all_results->item[i].mac[1] == 0x99 && 
       // ... 其他字节检查
       ) {
        found_target1 = true;
        WS_PRINTF("[WIFI_TEST] ✓ Target MAC C6:99:0E:62:B8:66 FOUND");
    }
}
```

## 预期改进效果

### 1. 扫描覆盖率提升
- **时间覆盖**：3轮扫描覆盖更长时间窗口
- **数量覆盖**：最多可发现20-30个AP
- **信号覆盖**：多轮扫描提高弱信号AP的发现概率

### 2. 目标检测能力
- 专门检测指定的MAC地址
- 明确报告是否找到目标AP
- 提供目标AP的信号强度信息

### 3. 详细的分析报告
- 每轮扫描结果统计
- 最终合并结果分析
- 信号强度分布统计
- 目标MAC地址检测结果

## 新的日志输出格式

### 1. 测试启动
```
[WIFI_TEST] Enhanced Test configuration:
[WIFI_TEST] - Test timeout: 60000 ms
[WIFI_TEST] - Max scan rounds: 3
[WIFI_TEST] - Target MACs: C6:99:0E:62:B8:66, 2E:B0:52:85:24:38
[WIFI_TEST] - Scan mode: Normal Enhanced (rounds=5, max_hotspots=20)
```

### 2. 每轮扫描结果
```
[WIFI_TEST] ========== WiFi Scan Test Round 1 Result ==========
[WIFI_TEST] Round 1 SUCCESS - Found 8 APs
[WIFI_TEST] Added new AP MAC=46:31:27:d9:33:c5, RSSI=-50
[WIFI_TEST] Starting round 2 scan in 2 seconds...
```

### 3. 最终结果报告
```
[WIFI_TEST] ========== Final Scan Results After 3 Rounds ==========
[WIFI_TEST] Total unique APs found: 15
[WIFI_TEST] AP[0]: MAC=46:31:27:d9:33:c5, RSSI=-50
[WIFI_TEST] AP[1]: MAC=c6:b2:5b:5a:d0:e5, RSSI=-55
...
[WIFI_TEST] Final signal strength analysis:
[WIFI_TEST] - Strong signals (>-60dBm): 3
[WIFI_TEST] - Medium signals (-60~-80dBm): 8
[WIFI_TEST] - Weak signals (<-80dBm): 4
[WIFI_TEST] ✓ Target MAC C6:99:0E:62:B8:66 FOUND with RSSI=-65
[WIFI_TEST] ✗ Target MAC 2E:B0:52:85:24:38 NOT FOUND
```

## 使用方法

1. **编译更新的代码**：
   ```bash
   make cranem_modem_watch
   ```

2. **烧录并重启设备**

3. **观察增强的测试日志**：
   - 系统启动10秒后自动开始测试
   - 观察3轮扫描的详细过程
   - 查看最终的目标MAC检测结果

## 故障排除

### 1. 如果仍然找不到目标MAC
- 检查目标设备是否在扫描范围内
- 确认目标设备WiFi是否开启
- 检查目标设备是否设置为隐藏SSID
- 考虑增加扫描轮数或延长间隔时间

### 2. 如果扫描结果异常
- 检查WiFi硬件状态
- 确认扫描参数是否合理
- 查看是否有其他进程干扰WiFi扫描

### 3. 性能优化建议
- 根据实际环境调整扫描轮数
- 优化扫描间隔时间
- 考虑在不同时间段进行测试

这个增强的解决方案应该能够显著提高WiFi扫描的覆盖率，帮助您找到之前遗漏的AP设备。

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/2g.mod/api/inc/ts_sig.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/26 20:15:58 $
 **************************************************************************
 *  File Description :
 *      application layer  - Sms Tl task interface
 **************************************************************************/

#ifndef TS_SIG_H
#define TS_SIG_H

/****************************************************************************
 * Nested Include Files
 ****************************************************************************/

#if !defined (SITS_TYP_H)
#include     <sits_typ.h>
#endif

#if !defined (TL_TYP_H)
#include     <tl_typ.h>
#endif

/****************************************************************************
 * Macros
 ****************************************************************************/

/****************************************************************************
 * Defines
 ****************************************************************************/

#if defined (UPGRADE_SIM_APP_TOOLKIT)
#define  SMS_MAX_USER_DATA_LENGTH         140
#endif

/****************************************************************************
 * Types
 ****************************************************************************/

typedef enum  SmsStatusReportQualTag
{
   SMS_REPORT_FROM_SUBMIT,
   SMS_REPORT_FROM_COMMAND
}
SmsStatusReportQual;
//added by taow for SMS cp ACK begin

extern Int8     SmsCpDataCount;


//added by taow for SMS cp ACK begin

/****************************************************************************
 * Signal types
 ****************************************************************************/

typedef struct TsDeliverIndTag
{
   Int8                             shortMsgId;
   Boolean                          statusReportInd;
   SmsAddress                       smeAddr;
   SmsAddress                       scAddr;
   Boolean                          replyPath;
   SmsProtocolId                    smsProtocolId;
   SmsDataCoding                    smsDataCodingScheme;
   Boolean                          moreMsgsToSend;
   SmsTimeStamp                     scTimeStamp;
   Int8                             shortMsgLen;
   Int8                             shortMsgData[SMS_MAX_MSG_LEN];
   Boolean                          userDataHeaderPresent;
#if defined (UPGRADE_GPRS)
   Boolean                          smsCipher;  /* cipher status of SM */
#endif
#if defined (UPGRADE_SIM_APP_TOOLKIT)
   /* -----------------2/2/01 10:40AM-------------------
        SAT FTA - need to retain knowledge of the Raw
        value of the DCS byte as the test scripts expect
        us to send it to the SIM Simulator exactly as
        received in the TsDeliverInd. Networks don't
        care as they only process the lower nibble of
        the DCS containing the Class and Coding info.
    --------------------------------------------------*/
   Int8                             rawDcsValue;
#endif
   unsigned short                   LocalMsgRef; //add by taow ******** CQ87891
}
TsDeliverInd;


typedef struct TsDeliverReportReqTag
{
   Int8                             shortMsgId;
   SmsStatusOfReport                statusOfReport;
   TpFailureCause                   tpFailureCause;
#if defined (UPGRADE_SIM_APP_TOOLKIT)
   Boolean                          smsProtocolIdPresent;
   SmsProtocolId                    smsProtocolId;
   Boolean                          smsDataCodingSchemePresent;
   SmsDataCoding                    smsDataCodingScheme;
   /*
   ** SAT FTA - network requires PID and DCS fields to be the same as in the
   ** original TsDeliverInd, as per GSM 11.14, section 7.1.1
   */
   Int8                             rawDcsValue;
   Int8                             userDataLength;
   Int8                             userData[SMS_MAX_USER_DATA_LENGTH];
#endif
}
TsDeliverReportReq;


typedef struct TsSubmitReqTag
{
   Int8                             shortMsgId;
   /* msgRef to identify SM to SC */
   Int8                             msgRef;
   Boolean                          statusReportReq;
   SmsAddress                       smeAddr;
   SmsAddress                       scAddr;
   Boolean                          replyPath;
   SmsProtocolId                    smsProtocolId;
   SmsDataCoding                    smsDataCodingScheme;
#if defined (UPGRADE_SIM_APP_TOOLKIT) /*job 100087*/
   Boolean                          useRawDcs;  /*This will be used by STK application only*/
   Int8                             rawDcs;     /*This will be used by STK application only*/
#endif
   VpFormat                         validityPeriodFormat;
   Int8                             validityPeriodAsValue;
   SmsTimeStamp                     validityPeriodAsTime;
   Int8                             shortMsgLen;
   Int8                             shortMsgData[SMS_MAX_MSG_LEN];
   Boolean                          rejectDuplicates;
   Boolean                          userDataHeaderPresent;
#if defined (UPGRADE_GPRS)
   SmsRoute                         smsRoute;  /* sms routing type e.g. GPRS only, CS_preferred  */
#endif
}
TsSubmitReq;


typedef struct TsReportIndTag
{
   Int8                             shortMsgId;
   SmsStatusOfReport                statusOfReport;
   RpCauseElement                   rpCause;
   TpFailureCause                   tpFailureCause;
   RpUserData                       rpUserData;  /* FR 15954 */
#if defined (UPGRADE_GPRS)
   Boolean                          smsCipher;  /* cipher status of SM */
#endif
//add by taow for cq58182 501595 begin
//can use the rpUserData to trans the raw data, but in order to impact current project. define a new one.
   Int16                           tpduRawDataLen;
   Int8                            tpduRawData[MAX_TPDU_INFO_LENGTH];
//add by taow for cq58182 501595 end
}
TsReportInd;
/*CQ00134713,status report read from Efsms and write into EFsms,********,perse,begin*/
typedef struct TsStatusReportOptionFalgTag
{         
           Boolean                          smsPiFalg;
           Boolean                          smsPidFlag;
           Boolean                          smsDcsFalg;
           Boolean                          smsUserDataFlag;
}
TsStatusReportOptionFalg;

typedef struct TsStatusReportOptionTag
{         
             Int8                              smsOPid;
             SmsDataCoding              smsDataCodingScheme;
             Int8                             smsOrawDcsValue;
             Int16                           userDataLen;
             Int8                             userData[MAX_TPDU_INFO_LENGTH];
             TsStatusReportOptionFalg  smsStOptionFalg; 
}
TsStatusReportOption;

typedef struct TsStatusReportIndTag
{
   Int8                             shortMsgId;
   /* msg num of SM reported on by SC */
   Int8                             msgRef;
   Boolean                          moreMsgsToSend;
   SmsAddress                       scAddr;
   SmsAddress                       recipientAddr;
   SmsTimeStamp                     scTimeStamp;
   SmsTimeStamp                     receptionTime; /* discharge time */
   SmsStatus                        smsStatus;
   SmsStatusReportQual              statusReportQual;
#if defined (UPGRADE_GPRS)
   Boolean                          smsCipher;  /* cipher status of SM */
#endif
   /*CQ00146605,sms status report is abnorrnal,perse,2023.11.1,begin*/ 
   RawSmsAddress                    scRawAddr;
   RpUserDataElement                 tpdu;
   /*CQ00146605,sms status report is abnorrnal,perse,2023.11.1,end*/	
   Int8                              smsPi;

   TsStatusReportOption             smsStOption;
   Boolean                          userDataHeaderPresent;
}
TsStatusReportInd;
/*CQ00134713,status report read from Efsms and write into EFsms,********,perse,end*/

typedef struct TsCommandReqTag
{
   Int8                             shortMsgId;
   /* msg num to identify SM to SC */
   Int8                             msgRef;
   SmsAddress                       scAddr;
   SmsProtocolId                    smsProtocolId;
   SmsCommand                       commandType;
   /* msg num in SC to perform cmd on */
   Int8                             msgNum;
   Int8                             cmdDataLen;
   Int8                             cmdData[SMS_MAX_CMD_LEN];
   Boolean                          statusReportReq;
   SmsAddress                       smeAddr;
#if defined (UPGRADE_GPRS)
   SmsRoute                         smsRoute;  /* sms routing type e.g. GPRS only, CS_preferred  */
#endif
}
TsCommandReq;


typedef struct TsMemAvailReqTag
{
   Int8                             shortMsgId;
   Int8                             msgRef;
#if defined (UPGRADE_GPRS)
   SmsRoute                         smsRoute;  /* sms routing type e.g. GPRS only, CS_preferred */
#endif
}
TsMemAvailReq;

typedef struct TsRawDeliverIndTag
{
    Int8                            shortMsgId;
    RawSmsAddress                   scAddr;
    RpUserDataElement               tpdu;
#if defined (UPGRADE_GPRS)
    Boolean                         smsCipher;  /* cipher status of SM */
#endif
    unsigned short                  LocalMsgRef;//add by taow ******** CQ87891
}
TsRawDeliverInd;


/*job100559*/
/*job116776 introducing moreMessagesToSend functionality*/
typedef struct TsConfigReqTag
{
  Boolean                           isReady;      /*TRUE implies AL is ready to prcess received SMS's*/
  Boolean                           moreMsgsToSend;
}
TsConfigReq;

#endif
/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/umaulmap.h#4 $
 *   $Revision: #4 $
 *   $DateTime: 2007/02/02 13:54:44 $
 **************************************************************************
 * File Description:
 *
 *    Contains the internal declarations for UMAC
 **************************************************************************/

#if !defined (UMAULMAP_H)
#define       UMAULMAP_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <system.h>
#include <kernel.h>

#include <cmac_sig.h>
#include <umac_sig.h>
#include <uphy_sig.h>

#include <utllist.h>
#include <release_api.h>

#ifdef ENABLE_ULDATA_FUNCTION_CALL
#include <uf8chain.h>
#endif

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/


#define UMAC_TFI_BARRED                         ((UmacTfIndex)0xFF)
#define UMAC_MAX_UL_LOGICAL_CHANNELS_PER_TR_CH  15
#define UMAC_MAX_UL_TR_CH_TYPES                 2
                                                /* DCH, RACH, (CPCH, USCH) */
#define UMAC_MAX_UL_TR_CHS                      (UPS_MAX_UL_NO_OF_TRCH + 1)
                                    /* maxTrCh x DCH, + RACH, + (CPCH, + USCH) */
#define UMAC_TFI_INVALID                        ((UmacTfIndex)0xFF)
                                                    /* (maxTF) */
#define UMAC_TFCI_INVALID                       ((UmacTfcIndex)0xFFFF)
                                                    /* (maxTFC) */
#define UMAC_MIN_SCORE                          ((SignedInt32)0x80000000)
                                                    /* -2147483648 */
#define UMAC_ASC_INVALID                        ((Int8)0xFF)

/* This must be consistent with the value defined in RBC (done same way!) */
#define UMAC_RACH_TR_CH_IDENTITY                0xFE

#define UMAC_FDD_RACH_TCTF_CCCH_LENGTH          2
#define UMAC_FDD_RACH_TCTF_DXCH_LENGTH          UMAC_FDD_RACH_TCTF_CCCH_LENGTH
#define UMAC_FDD_RACH_TCTF_CCCH                 0x00
#define UMAC_FDD_RACH_TCTF_DXCH                 0x01
#define UMAC_FDD_RACH_DXCH_MAC_HDR_LENGTH   (UMAC_FDD_RACH_TCTF_CCCH_LENGTH + \
                                                UMAC_UE_ID_TYPE_LENGTH + \
                                                UMAC_C_RNTI_LENGTH)

#define UMAC_DXCH_NUM_MAPPINGS_NO_CT        1

#define UMAC_MAX_RLC_HEADER_LENGTH_IN_BITS 16


#define UMAC_TFC_NOT_USED                       0xFFFFFFFF
#define UMAC_CTFC_IN_ACTIVE_SUBSET(a)          (((a)& 0x80) != 0 ? TRUE : FALSE)
#define UMAC_CTFC_STATE(a)                      ((a) & 0x03)
#define UMAC_SET_CTFC_STATE(a,b)                ((a) = ((a) & ~0x03) | ((b) & 0x03))
#define UMAC_CTFC_SUBSET_ADD_CURRENT(a)         ((a) |= 0x80)
#define UMAC_CTFC_SUBSET_ADD_DEFAULT(a)         ((a) |= 0x40)
#define UMAC_CTFC_SUBSET_ADD_CURRENT_DEFAULT(a) ((a) |= 0xC0)
#define UMAC_CTFC_SUBSET_DEL_CURRENT(a)         ((a) &= ~0x80)
#define UMAC_CTFC_SUBSET_DEL_DEFAULT(a)         ((a) &= ~0x40)
#define UMAC_CTFC_SUBSET_DEL_CURRENT_DEFAULT(a) ((a) &= ~0xC0)
#define UMAC_CTFC_SUBSET_CURRENT_EQ_DEFAULT(a)  (((a) & 0x40) == ((a) & 0x80))
#define UMAC_CTFC_SUBSET_CURRENT_TO_DEFAULT(a)  (((a) & 0x40) == 0x40 ?        \
                                              UMAC_CTFC_SUBSET_ADD_CURRENT(a) :\
                                                UMAC_CTFC_SUBSET_DEL_CURRENT(a))
#define UMAC_CTFC_ADD_MINIMUM_SET(a)            ((a) |= 0x20)
#define UMAC_CTFC_DEL_MINIMUM_SET(a)            ((a) &= ~0x20)
#define UMAC_CTFC_NOT_IN_MINIMUM_SET(a)        ((((a) & 0x20) == 0) ? TRUE : FALSE)

/* always 2* whatever arrives in UTrafficVolumeMeasQuantity -
   see section 10.3.7.71 of TS 25.331 */
#define UMAUL_MAX_SAMPLES_FOR_AVG_VAR     26
#define UMAUL_NO_SAMPLES_PER_ASN_INTERVAL  2
#define UMAUL_INITIAL_SAMPLE_VALUE        ULONG_MAX
#define UMAUL_SAMPLE_INDEX(cur,index)     ((cur) > (index) ? ((cur)-(index) -1)\
                                          : (cur+UMAUL_MAX_SAMPLES_FOR_AVG_VAR)\
                                          - (index) - 1)
#define UMAUL_VAR_FACTOR                  16

/* #defines for changing CTFC state */
#define UMAC_NUMBER_SLOTS_ELIMINATION           UPS_SLOTS_PER_FRAME
#define UMAC_T_NOTIFY_PLUS_T_L1_PROC            3 /* 15ms + 15ms = 3 frames */
#define UMAC_MAX_POWER_LEFT                     33*8          //modify for CQ00052362 20140110
#define UMAC_T_ADAPT_AMR                        4 /* In Frames */
#define UMAC_T_ADAPT_AMR_2                      6 /* In Frames */

#define UMAUL_MIN_CS_RATES_NUM					3 /**< At least 3 rates should be configured for the codec:
													no data, convenient noise and speech.*/
/**number of frames to delay adaptiveModeNumberOfRates update.
    This avoid sending two ccsdConfigReq too close each other, thus lowering the rates too fast.*/
#define UMAUL_DELAY_ADAPTIVE_MODE_NUM_OF_RATES_UPDATE_IN_FRAMES 1

/***************************************************************************
 * Type Definitions
 ***************************************************************************/

typedef Int16 UmacTfcIndex;
typedef Int8 UmacTfIndex;
typedef Int32 TrChIdMap;

//ICAT EXPORTED ENUM
typedef enum UmacUlRachStateTag
{
    UMAUL_RACH_STATE_NULL = 0,                 /* no signals defined for null */
    UMAUL_RACH_STATE_SUPER_STATE = 1,          /* not in UL sub-state */
    UMAUL_RACH_STATE_RACH_IDLE = 2,            /* "substate" of COMMON & IDLE */
    UMAUL_RACH_STATE_RACH_CHECK_PERSISTENCY = 3,/* "substate" of COMMON & IDLE*/
    UMAUL_RACH_STATE_RACH_BACKOFF = 4,         /* "substate" of COMMON & IDLE */
    UMAUL_RACH_STATE_RACH_ACCESS = 5,          /* "substate" of COMMON & IDLE */
    UMAUL_RACH_STATE_NUMBER_OF_STATES
}
UmacUlRachState;

//ICAT EXPORTED ENUM
typedef enum UmacUlDchStateTag
{
    UMAUL_DCH_STATE_NULL = 0,                  /* no signals defined for null */
    UMAUL_DCH_STATE_SUPER_STATE = 1,           /* not in UL sub-state */
    UMAUL_DCH_STATE_TX = 2,                    /* "substate" of DEDICATED */
    UMAUL_DCH_STATE_NUMBER_OF_STATES
}
UmacUlDchState;

typedef enum UmaUlCsRabUpdateTag
{
    UMAUL_CS_RAB_NO_UPDATE,
    UMAUL_CS_RAB_UPDATE_ONLY,
    UMAUL_CS_RAB_UPDATE_AND_POPULATE
}
UmaUlCsRabUpdate;

typedef enum UmaUlDoTfcSelTag
{
    UMAUL_DO_TFC_SEL_NO,
    UMAUL_DO_TFC_SEL_TILL_END_LONGEST_TTI,
    UMAUL_DO_TFC_SEL_YES
}
UmaUlDoTfcSel;

/***************************************************************************
 *
 * Traffic Volume Measurement Structures
 *
 ***************************************************************************/

typedef struct UmaUlTrafficEventTag
{
    UMeasurementIdentity                      measIdentity;
    UTrafficVolumeThreshold                   reportingThreshold;

    Boolean                                   timeToTriggerPresent;
    Int16                                     timeToTriggerInFrames;
/* Timer as counter for timeToTrigger */
    Int16                                     timeLeftToTrigger;

    Boolean                                   pendingTimeAfterTriggerPresent;
    Int16                                     pendingTimeAfterTriggerInFrames;
/* Timer as counter for pendingTimeAfterTrigger */
    Int16                                     pendingTimeLeftAfterTrigger;
/* Whether Uplane data should be interrupted, and if so, for how long */
    Int16                                     noFramesToInterruptUplaneTx;
}
UmaUlTrafficEvent;

typedef struct UmaUlTrafficInfoTag
{
    Boolean                      criterion4aValid;
    UmaUlTrafficEvent            criterion4a;
    Boolean                      criterion4bValid;
    UmaUlTrafficEvent            criterion4b;
/* TrCh Traffic Volume measurement for event evaluation */
    Int32                        trChTrafficVol;
    Int32                        prevTrChTrafficVol;
}
UmaUlTrafficInfo;

typedef struct UmaUlTrafficResultsTag
{
/* measured results */
    Int8                   curSample;   /* index into samples array */
    UrlTrafficVolumeBits   samples[UMAUL_MAX_SAMPLES_FOR_AVG_VAR];
}
UmaUlTrafficResults;

/***************************************************************************
 *
 * Internal Transport Channel structures
 *
 ***************************************************************************/

typedef struct UmacPowerLeftTag
{
    SignedInt16                     avgPowerLeft;
    Boolean                         useTopHalf;
    SignedInt16                      powerLeft[UMAC_NUMBER_SLOTS_FOR_CTFC_STATE];  //modify for CQ00052362 20140110
}
UmacPowerLeft;

typedef struct UmacUlTFCinfoTag
{        /* Top 2 bits indicate whether TFC is in current or default subset */
         /* Bottom bits indicate current TFC state (defined by UmacCTFCstate) */
    Int8                            ctfcStateAndSubset;
    Int32                           ctfc;
    UGainFactor                     gainFactorBetaC;
    UGainFactor                     gainFactorBetaD;
    UPowerOffsetPp_m                powerOffsetPp_m;
    SignedInt16                      logPowerRelation; //modify for CQ00052362 20140110
    USpreadingFactor                spreadingFactor;
#if defined(UPS_CFG_UL_DATA_CLASS_768KBPS)
    Int8                            numberOfDpdchs;
#endif
    SignedInt16                      tfcPowerRequired;  //modify for CQ00052362 20140110
    Int8                            numHlSchedGapsForSf_2;
    Int8                            numberFramesExcessPower;
    Int8                            tfcTmodTnotTl1proc;
    Int8                            tfi[UPS_MAX_UL_NO_OF_TRCH];
}
UmacUlTFCinfo;

typedef struct UmacUlTFCinfoListTag
{
    SignedInt16                      logPowerForCM[CMAC_MAX_SLOT_GAPS_PER_FRAME];  //modify for CQ00052362 20140110
    USpreadingFactor                minimumSF;
    UmacTfcIndex                    numberOfCTFCs;
    UmacUlTFCinfo                   tfcInfo[UPS_MAX_UL_TFC];/*worst case: UPS_MAX_UL_TFC*/
}
UmacUlTFCinfoList;

typedef struct UmacTransportFormatDataTag
{
    DynamicTfInfo                   transportFormat;
    /* Used during TFC selection */
    Int8                            noPdus;
    SignedInt32                     score;
    Int32                           dataRate;
}
UmacTransportFormatData;

typedef struct UmacUlMappingTag
{
    struct UmacUlTransportChannelInfoTag *ulTrChInfo_p;
    struct UmacUlLogChInfoTag            *ulLogChInfo_p;
    Int8                                  macHeaderSizeInBits;
    T_UUL_LogicalChannelMapping_r6_rlc_SizeList    tfiListType;
    MacTfiList                           tfiList;
    /* Used during Tx */
    Int8                                 noPdus;
                                                       /* Index into TFS */
    Int16                                pduSizeInBits[UPS_MAX_UL_NO_OF_TB_PER_TTI];
}
UmacUlMapping;

UT_DOUBLE_LINK_LIST_DECLARE(UmacUlMappingList, UmacUlMapping);
/* Worst Case: maxRBMuxOptions per logical channel */

typedef struct UmacUlTransportChannelInfoTag
{
    /* These can only be T_UUL_TransportChannelType */
    UTransportChannelType           trChType;
    UTransportChannelIdentity       trChIdentity;
    Int8                            framesInTti;
    Boolean                         startOfTti;
    UmacTfIndex                     numberOfTFs;
    UmacTransportFormatData         tfData[maxTF];
    Int32                           p_i;
    Int8                            trChIndex; /* Used to index TF in TFI array in TFCinfo */
    Boolean                         ctFieldRequired;
    /*highPriority is the highest priority of logical channels mapped to specific
     * transport channel. this highPriority is assigned to a transport channel during
     * configuration time (does not take into account if data is sent or not in the channel)*/
    UMAC_LogicalChannelPriority     highPriority;
    UMAC_LogicalChannelPriority     highestPriority;
    UmacUlMappingList               ulMappingList;
    UmacConfigType                  configType;
      
    /* traffic volume event info on this trCH */
    UmaUlTrafficInfo                event;

/* Used during Tx procedure (note one Tx may straddle multiple frames */
    /* At the end of the TTI, this is set to UMAC_TFI_INVALID */
    UmacTfIndex                     selectedTFI;
    BearerIdentityWithCtrl          bearerIdentitiesWithCtrl[UPS_MAX_UL_NO_OF_TB_PER_TTI];
}
UmacUlTransportChannelInfo;

/* Umac stores Transport channels in assending order of TrChId */
UT_DOUBLE_LINK_LIST_DECLARE(UmacUlTrChInfoList, UmacUlTransportChannelInfo);
/* Worst Case: UMAC_MAX_UL_TR_CHS */

typedef struct UmacCsRabInfoTag
{
    URAB_Identity                 rabIdentity;
    CsCodecType                   codecType;
    Boolean                       configured;
    Boolean                       queuedForDeletion;
    UTransmissionTimeInterval     tti;
    Boolean                       ttiBoundary;
    Int8                          numberOfRBs;
    BearerIdentity                bearerIdentity[maxRBperRAB];
    Int8                          numberOfRates;
	Int8                          numberOfCsdiSuspendedRBs;		/**< When CSDI is suspended numberOfRBs set to 0. On CSDI resume the previous value is required so this variable saves it meanwhile.*/
    CcsdCsRate                    activeCsRates[UPS_MAX_UL_TFC];
	Int8						  adaptiveModeNumberOfRates;		/**< codec mode adaptation may reconfigure the codec to avoid using some rates due to excess power. */
	Int8                          maxNumRlcSdusPerTti;
    /**number of frames to delay adaptiveModeNumberOfRates update.
        This avoid sending two ccsdConfigReq too close each other, thus lowering the rates too fast.*/
    Int8                          remainingDelayAdaptiveModeUpdateInFrames;
/*CQ0093486 - start*/
    Boolean                             voiceRabValidityParamsPresent;
    CmacCsVoiceRabValiditityParams      voiceRabValidityParams;
/*CQ0093486 - end*/	
}
UmacCsRabInfo;

/***************************************************************************
 *
 * Internal RB mapping and Multiplexing Option structures
 *
 ***************************************************************************/

typedef struct UmacUlLogChInfoTag
{
    /* just as in UmacTrafficInd, Bit 7 indicates Control (rlcIndicator==TRUE)*/
    BearerIdentityWithCtrl              rbIdentityWithCtrl;
    ULogicalChannelIdentity             logicalChannelIdentity;
                                        /* Note: 1 RB can have >1 LogCh.
                                           In this case, there are 2 entries
                                           in logCh list. In >R99, it is up
                                           to RLC to determine whether data
                                           is sent on a channel which MAC
                                           allocates as control during TFC
                                           selection. (i.e. RLC should process
                                           rlc_LogicalChannelMappingIndicator */
    UMAC_LogicalChannelPriority         macLogicalChannelPriority;
    UmacDomainMode                      domainMode;
    UmacCsRabInfo                      *csRabInfo_p;
    Boolean                             segmentedTm;
    UrlBufferInfo                       rlcBufferInfo;
    UmaUlTrafficResults                 measResults;
#if defined(ENABLE_UPS_MAC_2_TRCH_PER_BEARER)
    Int8                                noPdusThisTfci;
#endif
    UmacConfigType                      configType;
}
UmacUlLogChInfo;

/* The Logical Channel Linked List is ordered by Logical Channel Priority */
UT_DOUBLE_LINK_LIST_DECLARE(UmacUlLogChInfoList, UmacUlLogChInfo);
/* Worst Case: maxRB*2? */

typedef struct UmacUlCCTrCHinfoTag
{
    UmacUlTFCinfoList                ulTFCinfoList;
    UmacUlTrChInfoList               ulTrChInfoList;
    /* Used during Tx procedure */
    UmacTfcIndex                     selectedTFCI;
}
UmacUlCCTrCHinfo;

typedef struct UmacDchInfoTag
{
    UmacUlDchState                  dchState;
    Boolean                         subsetDurationActive;
    Int16                           subsetCtrlDuration;
    Boolean                         hasZeroLengthTb;
    Int8                            numCmGaps;
    Int16                           cmGapsBitmap;
    UUL_CompressedModeMethod        cmMethod;
    UmacPowerLeft                   powerInfo;
} UmacDchInfo;

/***************************************************************************
 *
 * Internal RACH structures
 *
 ***************************************************************************/
typedef struct UmacRachInfoTag
{
/* Configuration data */
    /* conversion seems to be even spacing: 8 values 0..1 25.331 s10.3.6.35 */
    Int16                           dynamicPersistenceLevel;
    Int8                            numberOfAccessServiceClasses;
    /* 1st 2 scaling factors are 1.0, others may be defined by UTRAN in SIB */
    Int16                           persistenceScalingFactor [maxASC];
    /* The ASC to be used with RRC_Conn_Req */
    Boolean                         useSetupASC;
    Int8                            setupASC;
    URACH_TransmissionParameters    rachTransmissionParams;

/* Data used during transmission */
    Int8                            t2;
    Int8                            tb01;
    UmacUlRachState                 rachState;
    Int8                            preambleTxCounterM;
    Int8                            selectedASC;
    UmacPowerLeft                   powerInfo;
    BearerIdentityWithCtrl          bearerIdentitiesWithCtrl[UPS_MAX_UL_NO_OF_TB_PER_TTI];

#if defined(PS_L2_R8_API)
	Boolean							cmacRachAccessFinRequired;
#endif /*PS_L2_R8_API*/
}
UmacRachInfo;

typedef     UmacUlLogChInfo * UmacUlLogChInfoPtr;

//Recovery from multiple unsuccessful RACH accesses
typedef struct  UmacUlRachFailureRecoveryTag
{
    Int32       failuresCounter;					/**< number of RACH failures till now */
    Int32       numberOfFailuresForRrcReport;       /**< when  failuresCounter reach this value report to RRC  */
    Int32       timerValueInMs;						/**< number of ms in which RACH failures are counted*/
    KiTimer     timer;								/**< during this timer RACH failures are counted*/
    
}UmacUlRachFailureRecovery;

typedef struct UmaRestrictedTrChInfoTag
{
	Boolean isRestrictionReceved;
	SignalBuffer sigbuf;			/**< Contains the list of restricted tfi */											   
}UmaRestrictedTrChInfo;

typedef struct UmaUlEntityTag
{
    /* A pointer back to the Umac Entity */
    UmacEntity                     *umac_p;
    /* RACH "CCTrCH" - effectively MAC-c/sh */
    UmacRachInfo                    rachInfo;

    /* DCH CCTrCH(s) - effectively MAC-d */
    UmacDchInfo                     dchInfo;

    /* Note that this pointer is either:
     * - a pointer to the RACH TFCS and RACH TrChs in COMMON state
     * - a pointer to the DCH TFCS and DCH TrChs in DEDICATED state
     */
    UmacUlCCTrCHinfo                ulCCTrCHinfo;
    Int8                            minUlTtiInFrames;
    Int8                            maxUlTtiInFrames;
    SignalBuffer                    phyFrameIndSigBuf;
    UmaUlDoTfcSel                   doTfcSelection;
    Boolean                         trafficReqPresent;
    /* Options for going RB=>TrCH (UL) - logical channels of MAC-d plus CCCH */
    UmacUlLogChInfoList             ulLogChInfoList; /* Note - each LogCH is
                                                        allocated separately */
    /* Inhibit transmissions on U-plane following measurement event */
    Boolean                         inhibitUplaneTx;
    Int16                           inhibitUplaneTxTimeInFrames;
    /* CS-RAB information, required for rate adaptation and control */
    Boolean                         handoverActive;
    Int8                            noCsRabs;
    UmacCsRabInfo                   csRabInfo[maxRABsetup];
    UmaUlCsRabUpdate                updateCsRabs;
#if defined(DEVELOPMENT_VERSION)
    UmacDebugTfcSelection           tfcSelectionDebug;
#endif /* DEVELOPMENT_VERSION */

    /* UL COUNT-C, KSI and ciphering information */
    UmacCipheringInfo               csCipherInfo;
    UmacCipheringInfo               psCipherInfo;
    UdtcRequestInfo                 cipherReq;
#if defined (DTC_HW_VERSION_5)
    Int8                            dtcRestoreBuff[UPS_MAX_UL_NO_OF_TB_PER_TTI];
#endif
#if !defined(DATA_IN_SIGNAL)&& defined(ENABLE_ULDATA_FUNCTION_CALL)
    UCipherInfo                    *cipherInfo_p;
#endif /* !DATA_IN_SIGNAL */

/* Miscellaneous management */
    /* Reference to UL PDU list */
    UUlPduList                     *pduList_p;
/*CQ79461 modify by taoye begin 20141223*/
#if defined (PS_L2_R8_API)
		//Temporary workaround for DTC alignment bug
	UUlPduList					   *pduLists_temp[UPS_MAX_UL_PDULIST];
#endif
/*CQ79461 modify by taoye end 20141223*/

    UUlPduList                     *pduLists[UPS_MAX_UL_PDULIST];
    UUlPduList                      *abortedRachPduList_p; /* used for holding pduList_p until the
                                                            * aborted PhyAccessCnf is received */
    Int8                            curPduList;
    Boolean                         processingSavedSignals;
    KiUnitQueue                     signalStore;  /* this is a storage for
                                                   * received signals which
                                                   * have not been processed
                                                   * yet. */
    UmacTfcIndex                    validTFCIlist[UPS_MAX_UL_TFC];
    UmacTfcIndex                    validTFCIlistLength;
    Int8                            numberOfUlRadioBearers;

    USRB_delay                      srbDelay;     /* SPA bepstein in Sync proc A need to wait srbDelay frames
                                                   * after first ReadyToSend before sending data on SRBs */
    SrbDelayState                   srbFlag;      /* SPA bepstein */
	UmaRestrictedTrChInfo			restrictedTrChInfo;		/**< Info about restricted tfi on Transport Chanels.*/
	Int8                            maxFramesInCurrentTti;	/**< Number of frames in the longest tti starting on current cfn. */

    UmacUlRachFailureRecovery       rachFailureRecovery;   /**< In case of multiple rach failures, send indication to RRC to reselect cell.*/
}
UmaUlEntity;

/******************************************************************************
 * Global Variables
 *****************************************************************************/
extern UmaUlEntity umaUl;


/******************************************************************************
 * Function Prototypes
 *****************************************************************************/
void UmaUlMapDeleteLogChInfoList(
                UmacUlLogChInfo *ulLogChInfo_p);
void UmaUlMapDeleteMappingList(UmacUlTransportChannelInfo *ulTrCh_p);
void UmaUlMapDeleteTrChList(UmacUlTrChInfoList *ulTrChInfoList_p);
UmacUlCCTrCHinfo *UmaUlMapGetCCTrCHinfo(
                UTransportChannelType trChType);
UmacUlTransportChannelInfo *UmaUlMapGetUlTrChInfo(
                UTransportChannelType trChType,
                UTransportChannelIdentity trChIdentity);
UmacUlTransportChannelInfo *UmaUlMapNewUlTrChInfo(
                UTransportChannelType trChType,
                UTransportChannelIdentity trChIdentity);
UmacUlMapping *UmaUlMapGetUlMapping(UmacUlLogChInfo *logChInfo_p,
                UTransportChannelType trChType,
                UTransportChannelIdentity trChIdentity);
UmacUlMapping *UmaUlMapGetUlMappingAllTrChs(UmacUlLogChInfo *logChInfo_p,
                UmacUlTrChInfoList *trChList_p);
UT_DOUBLE_LINK_LIST_NODE_TYPE(UmacUlLogChInfoList)
        *UmaUlMapGetUmacUlLogChInfoListNode(
                BearerIdentityWithCtrl rbIdentityWithCtrl);
UmacUlLogChInfo *UmaUlMapGetUlLogChInfo(
                BearerIdentityWithCtrl rbIdentityWithCtrl);
void    UmaUlMapReorderLogChPriority(
                UT_DOUBLE_LINK_LIST_NODE_TYPE(UmacUlLogChInfoList)
                *logChInfoNode_p,
                UMAC_LogicalChannelPriority logChPriority);
void    UmaUlMapAddUlMapping(UmacUlLogChInfo *logChInfo_p,
            UUL_LogicalChannelMapping_latest *logChMapping_p,
            MacTfiList *tfiList_p);
UmacUlLogChInfo *UmaUlMapAddLogCh(BearerIdentityWithCtrl rbIdWithCtrl,
                        UUL_LogicalChannelMapping_latest *logChMapping_p);
Int8    UmaUlMapGetPdusToSend(BearerIdentityWithCtrl rbIdentityWithCtrl,
                UrlBufferInfo *bufferInfo_p, Int16 tbSizeInBits,
                Int8 macHeaderSize);
Boolean UmaUlMapLogChHasDataToSend(BearerIdentityWithCtrl rbIdentity,
                UrlBufferInfo *bufferInfo_p);
Int8    UmaUlMapGetMacHeaderSize(UmacUlTransportChannelInfo *trCh_p,
                BearerIdentity rbIdentity);

#endif

/* END OF FILE */

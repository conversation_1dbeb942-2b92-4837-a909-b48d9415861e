/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/api/inc/urlumtyp.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2007/03/22 15:29:52 $
 **************************************************************************
 *  File Description :
 *
 * Types and constants required for the URLC UM module
 *
 **************************************************************************/

#if !defined (URLUMTYP_H)
#define       URLUMTYP_H

/***************************************************************************
 * Nested Include Files
 **************************************************************************/

#include <system.h>
#include <ki_typ.h>
#include <urlalloc.h>
#include <urltypes.h>

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/


/***************************************************************************
 * TX Types
 ***************************************************************************/

typedef enum UrlUmTxStateTag
{
    URLUMTX_STATE_NULL              ,
    URLUMTX_STATE_READY             ,
    URLUMTX_STATE_SUSPEND
} UrlUmTxState;

typedef enum UrlUmTxUlStateTag
{
    URLUMTX_UL_ENABLED              ,
    URLUMTX_UL_STOPPED              ,
    URLUMTX_UL_HALTED
} UrlUmTxUlState;

typedef struct UrlUmTxConfigTag
{
    Int8                            liSize;         /* 7 or 15 bits */
    UTransmissionRLC_Discard        discardMode;
    Boolean                         useAlternatEBit;
    	/*CQ00067395 merged for FRBD 20140806 start*/
#if defined(UPGRADE_3G_UL_EL2)
    Boolean                         flexiblePduSize;
    Int32                           minPduBits;
    Int32                           maxPduBits;
#endif
/*CQ00067395 merged for FRBD 20140806 end*/
} UrlUmTxConfig;


typedef enum UrlUmTxResetTypeTag
{
    URLUMTX_RESET_CONFIGURATION     ,
    URLUMTX_RESET_RELEASE
} UrlUmTxResetType;

typedef struct UrlUmTxSduTag
{
    Int16                           startOctetOffset;
    Int16                           currentOctetOffset;

    /* octetLength must be signed, as can go to -1 with 15 bit LI's when
     * SDU was exactly one octet short of filling PDU
     */
    SignedInt16                     octetLength;

    /* Original length in bits
     */
    Int32                           bitLength;
    Boolean                         txStatusRequired;
    KernelTicks                     discardTick;
    UrlcDataUsage                   dataUsage;
    Boolean                         discardable;                               //-+ CQ00238599 11-Oct-2012 +-
#ifdef ENABLE_OPT_DATA_FLOW_OVER_SHMEM
    BearerIdentity                bearerId;
    TaskId                          higherTaskId;
#endif /* ENABLE_OPT_DATA_FLOW_OVER_SHMEM */
    Int8                             *data_p;
} UrlUmTxSdu;

/* Declare typedef for singly linked list carrying UrlUmTxSdu */
UT_SINGLE_LINK_LIST_DECLARE(UrlUmTxSduList, UrlUmTxSdu)

#if defined UPGRADE_3G_EDCH
typedef struct UmMappingRbTxReqIdTag
{
    Boolean                         IsTrafficIndConsumed;
    UrlUmTxSduList                  txSduList;
} UmMappingRbTxReqId;
#endif /* UPGRADE_3G_EDCH */

typedef struct UrlUmTxEntityTag
{
    BearerIdentity                  bearerId;
    TaskId                          higherTaskId;
    Boolean                         loopback;       /* Configured for loopback */
    UrlUmTxConfig                   config;
    UrlUmTxState                    state;
    UrlUmTxUlState                  ulState;
    UrlSequenceNumber               vtUS;
    Boolean                         sduDiscarded;
    Boolean                         liSizeReduction;
    Int16                           txStatusRequired;
    UrlBufferInfo                   bufferInfo;
    UrlUmTxSduList                  txSduList;

    Boolean                         sentDataPending; /* Flag control SIG_CRLC_DATA_PENDING_IND */

#if defined UPGRADE_3G_EDCH
    UmMappingRbTxReqId              UmRbTxReqId [MAX_TX_REQ_ID];
#endif /* UPGRADE_3G_EDCH */

    /* To enhance performance, it creates the PDUs (SN and LI's) in-situ,
     * however as it doesn't know how many LI's the PDU will contain until full,
     * it stores the data segments in an alloc'd buffer and appends it on
     * completeion; To avoid the need to alloc/free this data memory every time
     * a new PDU is created, it gets alloc'd once and never free'd, however its
     * size may grow, but never shrink.
     */
    Int16                           pduDataOctets;
    Int8                          * pduData_p;

    /* Ciphering */
    UrlCipherConfigList             cipherConfigList;
    UrlHyperFrameNumber             hfn;
    UrlCountcInfo                   ulCountcInfo;   /* COUNT-C of VR(US)-1 */

    /* RB throughput statistics */
    Int32                           totalDataSent;

#if defined (ENABLE_UPLANE_STATISTICS)
    /* Statistic values for uplink channel
     */
    UrlUplinkStatistics             ulStats;
#endif /* ENABLE_UPLANE_STATISTICS */

#if defined (ENABLE_UL_THROUGHPUT_IND)
    /* Statistic values for uplink PDCP channel
     */
    UrlUlTpStatistics               ulTpStats;
#endif /* ENABLE_UL_THROUGHPUT_IND */

#if defined (ENABLE_URLC_UNIT_TEST)
    /* Save PDU size for EDCH unit test stub function use */
    Int32                           maxPduBits;
#endif /* ENABLE_URLC_UNIT_TEST */

#if !defined(ON_PC)
	/* Engineering Mode PS domain UL statistics report */
	UrlcEngModeUlStatistics 		engModeUlStatistics; 
#endif /* !ON_PC */
} UrlUmTxEntity;


/***************************************************************************
 * RX Types
 ***************************************************************************/

typedef enum UrlUmRxStateTag
{
    URLUMRX_STATE_NULL      ,
    URLUMRX_STATE_READY     ,
    URLUMRX_STATE_XOFF                          /* Above TMM high water mark */
} UrlUmRxState;

typedef enum UrlUmRxRunStateTag
{
    URLUMRX_RUNNING         ,
    URLUMRX_STOPPED         ,
    URLUMRX_HALTED
} UrlUmRxRunState;

/* ==========================================================================
 * Bearer configuration data
 * ========================================================================== */
typedef struct UrlUmRxConfigTag
{
    Int8                            liSize;                 /* 7 or 15 bits */

    /* Number of octets to be reserved at start of DL SDU data to allow the
     * higher layers to perform in-situ header decompression
     */
    Int8                            sduStartOctetOffset;
    Boolean                         useAlternatEBit;
} UrlUmRxConfig;

/* Declare typedef for singly linked list carrying a SignalBuffer */
UT_SINGLE_LINK_LIST_DECLARE (UrlUmRxSduList, SignalBuffer)

/* ==========================================================================
 * Bearer entity type
 * ========================================================================== */
typedef struct UrlUmRxEntityTag
{
    BearerIdentity                  bearerId;
    TaskId                          higherTaskId;
    Boolean                         loopback;       /* Configured for loopback */
    UrlUmRxConfig                   config;
    UrlUmRxState                    state;
    UrlUmRxRunState                 runState;
    UrlSequenceNumber               vrUS;
    UrlUmRxSduList                  sduList;
    UrlcSduStatus                   status;
    UrlLoopbackInfo               * loopbackInfo_p; /* PNULL unless test loop
                                                     * is closed */
    /* Ciphering */
    UrlCipherConfigList             cipherConfigList;
    UrlHyperFrameNumber             hfn;
    UrlCountcInfo                   dlCountcInfo;   /* COUNT-C of VR(UR)-1 */

    /* RB throughput statistics */
    Int32                           totalDataReceived;

#if defined (ENABLE_URLC_UNIT_TEST)
    CrlcTestModeF8                  f8mode;         /* Normal or UTRAN */
    BearerIdentity                  f8bearer;       /* Bearer when in UTRAN mode */
#endif /* ENABLE_URLC_UNIT_TEST */
#if defined (ENABLE_UPLANE_STATISTICS)
    /* Statistic values for downlink channel
     */
    UrlDownlinkStatistics           dlStats;
#endif /* ENABLE_UPLANE_STATISTICS */
    /*modified ********** start */
	Boolean 						maintainSRBDataReceive;
    /*modified ********** end */

#if !defined(ON_PC)
	UrlcEngModeDlStatistics	engModeDlStatistics; /* Engineering Mode PS domain DL statistics report */
#endif /* !ON_PC */

} UrlUmRxEntity;

typedef void (UrlUmRxEntityHandler)( UrlUmRxEntity    * entityData_p,
                                     SignalBuffer     * rxSignal_p
                                   );

#endif

/* END OF FILE */

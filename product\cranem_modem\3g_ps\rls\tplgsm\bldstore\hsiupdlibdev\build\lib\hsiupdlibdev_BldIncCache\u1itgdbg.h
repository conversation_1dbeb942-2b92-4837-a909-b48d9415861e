/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/***************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 ***************************************************************************
 * $Id: //central/main/wsd/modem/l1/3g.mod/pub/src/u1itgdbg.h#1 $
 * $Revision: #1 $
 * $DateTime: 2006/11/09 16:03:40 $
 **************************************************************************/
/** \file
 * Definition of structs that maybe logged in the debug general struct messages.
 *
 * This file is used to build an enumeration and union that are contained in
 * MSG_U1IT_DEBUG_GEN_STRUCT_IND and SIG_U1IT_DEBUG_GEN_STRUCT_IND, and
 * allow structures in U1 to easily be logged for debugging purposes.  The
 * functions #U1FhSendU1itDebugGenStructIndMsg() and
 * #U1ShSendU1itDebugGenStructIndMsg() provide this logging functionality,
 * as do the helper macros M_U1FhSendU1itDebugGenStructIndMsg and
 * M_U1ShSendU1itDebugGenStructIndMsg.
 *
 * \see M_U1FhSendU1itDebugGenStructIndMsg
 * \see M_U1ShSendU1itDebugGenStructIndMsg
 * \see U1FhSendU1itDebugGenStructIndMsg()
 * \see U1ShSendU1itDebugGenStructIndMsg()
 **************************************************************************/

/***************************************************************************
 * Include files to define loggable structures
 **************************************************************************/

#if defined(U1IT_DEBUG_GEN_STRUCT_INC)

/* Add include files that define structures for logging here */
#endif

/***************************************************************************
 * Declaration of loggable structures
 **************************************************************************/

#if defined(U1IT_DEBUG_GEN_STRUCT_DEF)
U1IT_DEBUG_GEN_STRUCT_DEF(Int32)  /* Dummy entry */

/* Add additional structures here */
#endif

/* END OF FILE */

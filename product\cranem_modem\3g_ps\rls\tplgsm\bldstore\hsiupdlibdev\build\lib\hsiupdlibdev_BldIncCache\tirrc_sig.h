/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/api/inc/tirrc_sig.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/27 15:10:43 $
 **************************************************************************
 * File Description:
 *
 * 3G PS Signal Data Type definitions for the TIM/URRC Interface
 **************************************************************************/

#if !defined (TIRRC_SIG_H)
#define       TIRRC_SIG_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <system.h>
#include <ups_typ.h>
#include <l3_typ.h>

/***************************************************************************
*   Manifest Constants
***************************************************************************/

/***************************************************************************
*   Macro Functions
***************************************************************************/

/***************************************************************************
*   Types
***************************************************************************/

typedef TcUmtsCloseTestLoopModeCmd      TiRrcCloseTestLoopModeReq;

typedef struct TiRrcCloseTestLoopModeCnfTag
{
     /* 34.109 5.3.2.3: If no radio bearer is established, then the UE shall
      * ignore any CLOSE UE TEST LOOP message
      */
     Boolean                    success;
} TiRrcCloseTestLoopModeCnf;

typedef struct TiRrcOpenTestLoopModeCnfTag
{
     /* 34.109 5.3.3.3: If no radio bearer is established, then the UE shall
      * ignore any CLOSE UE TEST LOOP message.
      */
     Boolean                    success;
} TiRrcOpenTestLoopModeCnf;

#endif /* TIRRC_SIG_H */
/* END OF TiRrc_Sig.h */

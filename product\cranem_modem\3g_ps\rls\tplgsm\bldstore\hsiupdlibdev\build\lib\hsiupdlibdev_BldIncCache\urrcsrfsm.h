/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urrcsrty.h#8 $
 *   $Revision: #8 $
 *   $DateTime: 2007/03/30 13:53:25 $
 **************************************************************************
 * File Description:
 *
 *    Contains types, definitions and declarations of CSR needed for URRTYPES.
 **************************************************************************/

#if !defined (URRCSRFSM_H)
#define       URRCSRFSM_H


/***************************************************************************
 * Manifest Constants
 ***************************************************************************/
/* Modification of this enum also requires update of csrProcIdString in urrutm.c */
//ICAT EXPORTED ENUM
typedef enum CsrProcIdTag
{
    CSR_NO_PROC,
    CSR_PROC_CSRR,
    CSR_PROC_CSRP,
    CSR_PROC_CSRS,
    CSR_PROC_CSRC
//********** add begin
#if defined UPGRADE_CSG1
	,CSR_PROC_CSRCSGS
#endif	
//********** add end
}
CsrProcId;

/* Modification of this enum also requires update of csrcStateString in urrutm.c */
//ICAT EXPORTED ENUM
typedef enum CsrcStateTag
{
    CSRC_IDLE,
    CSRC_AWAIT_CPHY_FIND_CELL_CNF,
    CSRC_AWAIT_SYS_INFO,
    CSRC_AWAIT_CPHY_CELL_SELECT_CNF,
    CSRC_AWAIT_CPHY_NEXT_CELL_CNF,
    /* PTK_CQ00236868 begin */
    CSRC_SEARCH_ABORTING_WAIT_L1_CNF
    /* PTK_CQ00236868 end */
}
CsrcState;

/* Modification of this enum also requires update of csrrReselectorStateString in urrutm.c */
//ICAT EXPORTED ENUM
typedef enum CsrrReselectorStateTag
{
#if ! defined (UPGRADE_EXCLUDE_2G)
    CSRR_AWAIT_RESELECT_TO_UMTS_FAIL_CNF,
    CSRR_AWAIT_RESELECTION_TO_GSM,
    CSRR_SUSPENDED_DUE_TO_CCOFU,
#endif
    CSRR_IDLE,
    CSRR_AWAIT_MEASUREMENTS,
    CSRR_AWAIT_BCH_DECODING_CNF,
    CSRR_AWAIT_SYS_INFO,
    CSRR_AWAIT_L1_CAMPED_ON_CELL,
    CSRR_AWAIT_PHY_RESYNCH_TO_SERV_CELL,
    CSRR_RANKING_CSRP_ACTIVE,
    CSRR_ABORTING
    ,
    CSRR_AWAIT_RESELECTION_TO_LTE,
    CSRR_AWAIT_DEACTIVATE_CNF
}
CsrrReselectorState;

/* ********** modify begin */
#if !defined (UPGRADE_PLMS)
//ICAT EXPORTED ENUM
typedef enum CsrpLtePlmnSearchStatusTag
{
    CSRP_LTE_NOT_SEARCHING,
    CSRP_LTE_SEARCH_WAIT_FOR_CELL_CNF,
    CSRP_LTE_SEARCH_WAIT_FOR_BCH,
    CSRP_LTE_SEARCH_WAIT_FOR_STOP_BCH_CNF,
    CSRP_ABORTING_LTE_SEARCH,
    CSRP_SUSPEND_STOP_LTE_SEARCH
}
CsrpLtePlmnSearchStatus;
#endif/*UPGRADE_PLMS*/
/* ********** modify end */

//********** add begin
#if defined UPGRADE_CSG1
typedef enum CsrcsgsLteCsgSearchStatusTag
{
    CSRCSGS_LTE_NOT_SEARCHING,
    CSRCSGS_LTE_SEARCH_WAIT_FOR_CELL_CNF,
    CSRCSGS_LTE_SEARCH_WAIT_FOR_BCH,
    CSRCSGS_LTE_SEARCH_WAIT_FOR_STOP_BCH_CNF,
    CSRCSGS_ABORTING_LTE_SEARCH,
    CSRCSGS_SUSPEND_STOP_LTE_SEARCH
}
CsrcsgsLteCsgSearchStatus;
#endif //UPGRADE_CSG1
//********** add end

typedef enum CsrpLtePlmnSearchTypeTag
{
    CSRP_LTE_NO_TYPE,
    CSRP_LTE_FDD_TYPE,
    CSRP_LTE_TDD_TYPE,
    CSRP_LTE_FDD_TDD_TYPE
}
CsrpLtePlmnSearchType;

/* ********** modify begin */
#if !defined UPGRADE_PLMS
//ICAT EXPORTED ENUM
typedef enum CsrpStateTag
{
    CSRP_NOT_SEARCHING,
    CSRP_SEARCH_WAIT_RSSI_SCAN_CNF,
    CSRP_SEARCH_WAIT_FOR_CELL_CNF,
    CSRP_SEARCH_WAIT_FOR_BCH,
    CSRP_SEARCH_AWAIT_PHY_DEACTIVATE_CNF,
    CSRP_SUSPENDING_AWAIT_L1_CNF,
    CSRP_SUSPENDED,
    CSRP_ABORTING_UMTS_SEARCH
#if !defined (L1_SUPPORT_GSM_FG_PLMN_SEARCH) && !defined UPGRADE_PLMS /*********** add UPGRADE_PLMS*/
    ,
    CSRP_SWITCH_TO_GSM_FGPS     //Switching to GSM in order to perform FG PLMN search
#endif
#if !defined (L1_SUPPORT_GSM_FG_PLMN_SEARCH) && !defined UPGRADE_PLMS /*********** add*/
/* ********** begin */
    ,
    CSRP_SWITCH_TO_LTE_FGPS
/* ********** end */
#endif/*********** add*/
}
CsrpState;
#else
//ICAT EXPORTED ENUM
typedef enum CsrpPlmsStateTag
{
    CSRP_PLMS_NOT_SEARCHING,                /* idle */
    CSRP_PLMS_WAIT_PLMS_FREQ_SCAN,          /* RSSI scan is done by PLMS task */
    CSRP_PLMS_UMTS_WAIT_FOR_BCH_ESTABLISH,  /* Waiting for L1 to open BCH */
    CSRP_PLMS_UMTS_READING_SIBS,            /* Reading BCCH info */
    CSRP_PLMS_UMTS_WAIT_FOR_BCH_RELEASE,    /* Waiting for L1 to close BCH */
    CSRP_PLMS_GSM_READING_SIBS,             /* Reading BCCH info */
    CSRP_PLMS_LTE_READING_SIBS,             /* Reading BCCH info */
    CSRP_PLMS_LTE_WAIT_FOR_BCH_RELEASE      /* Waiting for L1 to close BCH */
    
}
CsrpPlmsState;

//ICAT EXPORTED ENUM
typedef enum CsrpActiveStateTag
{
    CSRP_STATE_NOT_SEARCHING,              /* idle */
    CSRP_STATE_ACTIVE,                     /* active */
    CSRP_STATE_ABORTING_AWAIT_PLMS,        /* Waiting for PLMS to trigger CSRP abort */
    CSRP_STATE_ABORTING_AWAIT_L1_CNF,      /* Waiting for L1 CNF so abort can take place */
    CSRP_STATE_SUSPENDING_AWAIT_PLMS,      /* Waiting for PLMS to trigger CSRP suspend */
    CSRP_STATE_SUSPENDING_AWAIT_L1_CNF,    /* Waiting for L1 CNF so suspend can take place */
    CSRP_STATE_SUSPENDED,                   /* PLMN search process is suspended */ 
    CSRP_STATE_COMPLETING_AWAIT_L1_CNF	   /*Waiting for L1 to deactivate before completing search*/
}
CsrpActiveState;

#endif /* UPGRADE_PLMS */
/* ********** modify end */

//********** add begin
#if defined UPGRADE_CSG1
//ICAT EXPORTED ENUM
typedef enum CsrcsgsStateTag
{
    CSRCSGS_NOT_SEARCHING,
    CSRCSGS_SEARCH_WAIT_RSSI_SCAN_CNF,
    CSRCSGS_SEARCH_WAIT_FOR_CELL_CNF,
    CSRCSGS_SEARCH_WAIT_FOR_BCH,
    CSRCSGS_SEARCH_AWAIT_PHY_DEACTIVATE_CNF,
    CSRCSGS_SUSPENDING_AWAIT_L1_CNF,
    CSRCSGS_SUSPENDED,
    CSRCSGS_ABORTING_UMTS_SEARCH
#if !defined (L1_SUPPORT_GSM_FG_PLMN_SEARCH) && !defined UPGRADE_PLMS /*********** add UPGRADE_PLMS*/
    ,
    CSRCSGS_SWITCH_TO_GSM_FGPS     //Switching to GSM in order to perform FG PLMN search
#endif
#if !defined (L1_SUPPORT_GSM_FG_PLMN_SEARCH) && !defined UPGRADE_PLMS /*********** add*/
/* ********** begin */
    ,
    CSRCSGS_SWITCH_TO_LTE_FGPS
/* ********** end */
#endif/*********** add*/
}
CsrcsgsState;
#endif //UPGRADE_CSG1
//********** add end

/* ********** - add begin */
#if defined UPGRADE_CSG1
typedef enum CsgScellCapmingTypeTag
{
    SCELL_CAMPED_AS_NON_CSG,       /* camped on Scell as Non CSg - i.e. non CSG cell/hybrid cell but camped on the non csg flavour */
    SCELL_CAMPED_AS_CSG,           /*camped on Scell as CSG - i.e. csg only cell and camping on it, hybrid cell - camping on the csg flavour  */
    SCELL_CAMPED_AS_CSG_EMERGENCY  /* camped on a csg cell w/o service - i.e this CSG cell is not in the white list*/
}
CsgScellCapmingType;

//ICAT EXPORTED ENUM
typedef enum CsrCsgSelectStatusTag
{
    CSRCSG_SELECTION_IDLE,  //No Csg Select request is being handled
    CSRCSG_SELECT_PREPARE,  //Csg select request started. prepare sorted list of csg cells from DB
    CSRCSG_TRY_RESELECT     //Csg cells were merged on top of the merged sorted list and reselection to the top cell is statred. will go back to IDLE if procedure finished. or to PREPARE if need to try another candidate
    
}CsrCsgSelectStatus;

#endif //UPGRADE_CSG1
/* ********** - add end */

//ICAT EXPORTED ENUM
typedef enum CsrsSearchStatusTag
{
    CSRS_NOT_SEARCHING,
    CSRS_SEARCH_WAIT_FOR_SCAN_CNF,
    CSRS_SEARCH_WAIT_FOR_SEARCH_CNF,
    CSRS_SEARCH_WAIT_FOR_SEARCH_BCH,
    CSRS_SEARCH_WAIT_FOR_CHECK_PLMN_REJ_CNF,
    CSRS_SEARCH_WAIT_FOR_CHECK_PLMN_REJ_BCH,
    CSRS_SEARCH_WAIT_PHY_DEACTIVATE_CNF,
    CSRS_SEARCH_ABORTING_WAIT_L1_CNF,
    CSRS_SEARCH_WAIT_FOR_PLMS,		//search is ongoing in PLMS
    CSRS_SEARCH_WAIT_FB_BCH_CNF,		//Wating for CphyFgBchCnf 
    CSRS_SEARCH_WAIT_PLMS_ABORT,	//Wating for PLMS to Abort the search
    CSRS_SEARCH_WAIT_FOR_IRAT_RESEL,//ICS started in another RAT waiting for reselection to URR
    CSRS_SEARCH_WAIT_HAW_INIT,
    CSRS_SEARCH_WAIT_FB_BCH_CNF_BEFORE_PLMS_ABORT //Wating for CphyFgBchCnf  Before aborting PLMS
}
CsrsSearchStatus;

//ICAT EXPORTED ENUM
typedef enum UrrCSRExitCodesTag
{
    // CSRC
    CSRC_CELL_SELECTION_FAILURE_OOS = 1,
    CSRC_CELL_CAMPED_ON_SUITABLE_CELL,
    CSRC_CSRTU_FAILURE,
    CSRC_CSRTU_T3174_EXPIRY,
    CSRC_REDIRECTION_FAILED,
    /* PTK_CQ00236868 begin */
    CSRC_ABORT_REQUEST,
    /* PTK_CQ00236868 end */
    // CSRS
    CSRS_ABORT_REQUEST,
    CSRS_INITIAL_CELL_SELECTION_SUCCESS,
    CSRS_FAIL_TO_SELECT_ANY_CELL,
    // CSRR
    CSRR_ABORT_REQUEST, 
//    CSRR_START_INTERNAL_PLMN_SEARCH,  /* ********** */
    CSRR_START_ICS_IDLE_OOS,
    CSRR_COMPLETE_RESELECTION,
    CSRR_OOS_DETECTED,
    CSRR_RESELECTION_REQUIRED,
    CSRR_ABORT_SUSPENDED_PLMN,
    CSRR_START_CSRR_PROC,
    // CSRP
    CSRP_START_CELL_RESELECTION,
    CSRP_ABORT_REQUEST,
    CSRP_ABORT_GSM_MODE_PLMN,
    CSRP_ABORT_LTE_MODE_PLMN,
    CSRP_SUSPENSION_COMPLETE,
    CSRP_FG_GSM_OOS_CONN_NON_DCH_COMPLETE
//********** add begin
#if defined UPGRADE_CSG1
	// CSRCSGS
	,CSRCSGS_START_CELL_RESELECTION,
    CSRCSGS_ABORT_REQUEST,
    CSRCSGS_ABORT_GSM_MODE_PLMN,
    CSRCSGS_ABORT_LTE_MODE_PLMN,
    CSRCSGS_SUSPENSION_COMPLETE,
    CSRCSGS_FG_GSM_OOS_CONN_NON_DCH_COMPLETE
#endif //UPGRADE_CSG1
//********** add end
}
UrrCSRExitCodes;

#endif


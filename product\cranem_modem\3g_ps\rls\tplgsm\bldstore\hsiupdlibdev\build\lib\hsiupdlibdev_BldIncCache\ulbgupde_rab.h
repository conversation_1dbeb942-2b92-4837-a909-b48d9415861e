/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/3g.mod/lib/src/ulbgupde_rab.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2011/04/04 20:17:00 $
 **************************************************************************
 * File Description:
 *
 *      The UPDCP Entity - RAB.
 **************************************************************************/

#if !defined (ULBGUPDE_RAB_H)
#define ULBGUPDE_RAB_H

#include <xrabmdata.h>
#include <diag.h>
/***************************************************************************
* Nested Include Files
***************************************************************************/

/***************************************************************************
* Manifest Constants
***************************************************************************/

/***************************************************************************
* Macro Functions
***************************************************************************/

/***************************************************************************
* Types
***************************************************************************/

/***************************************************************************
* Typed Constants
***************************************************************************/

/***************************************************************************
* Function Prototypes
***************************************************************************/

Boolean UlbgUpdcpSap(XRabmEntity *sn,SignalBuffer *sigBuf);

#endif

/* END OF FILE */

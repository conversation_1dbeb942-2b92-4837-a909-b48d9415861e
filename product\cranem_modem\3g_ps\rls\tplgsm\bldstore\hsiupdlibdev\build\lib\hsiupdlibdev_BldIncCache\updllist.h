/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/3g.mod/lib/src/updllist.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2007/03/19 13:59:30 $
 **************************************************************************
 * File Description:
 *
 *      UPDCP Linked list function definitions
 **************************************************************************/

#if !defined (UPDLLIST_H)
#define UPDLLIST_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <utllist.h>
#include <ulbgupde.h>
#include <updlltyp.h>
#include <kernel.h>
#include <psinterface.h>

/***************************************************************************
* Manifest Constants
***************************************************************************/

/***************************************************************************
* Macro Functions
***************************************************************************/

/***************************************************************************
* Types
***************************************************************************/

/***************************************************************************
* Typed Constants
***************************************************************************/

/***************************************************************************
* Function Prototypes
***************************************************************************/
extern void
UpdcpLListInit (UlbgUpdEntity *pde);

extern void
UpdcpLListAddNpdu (UlbgUpdEntity		*pde,
                   Npdu                         *npdu,
                   Boolean                      sentToRlc,
                   SnUlSduList                 *srcPlatNode);

extern void 
UpdcpDeleteNpdu(UlbgUpdEntity *pde,
				    UT_DOUBLE_LINK_LIST_NODE_TYPE (UpdcpNpduLList) *pListItem);


extern UpdcpLListRet
UpdcpLListDeleteNpdu (UlbgUpdEntity			*pde,
                      UrlcMessageUnitIdentifier    mui);

extern UpdcpNpduInfo *
UpdcpLListGetNpdu (UlbgUpdEntity		*pde,
                   UrlcMessageUnitIdentifier    mui);

extern Boolean
UpdcpLListProcessReloc (UlbgUpdEntity	*pde,
                        UPDCP_SN_Info   receiveSn);

extern void
UpdcpLListRetransmit (UlbgUpdEntity *pde);

extern void
UpdcpLListSendUntransmittedNpdus (UlbgUpdEntity *pde);

extern void 
UpdcpLListDestroy(UlbgUpdEntity *pde);

extern void
UpdcpLListFlush (UlbgUpdEntity *pde);

#if !defined (UPGRADE_EXCLUDE_2G)
extern void
UpdcpLlistPutNpdusInQueue (UlbgUpdEntity    *pde,
                           KiUnitQueue  *queue);
#endif

#endif

/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Intel Corporation
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/umautl.h#5 $
 *   $Revision: #5 $
 *   $DateTime: 2007/03/30 13:53:25 $
 **************************************************************************
 * File Description:
 *
 *    Contains the internal declarations for UMAC
 **************************************************************************/

#if !defined (UMAUTL_H)
#define       UMAUTL_H

//when creating scripts (from log for example) avoid reading the data in phyDataInds.
#if defined (UMAC_UNIT_TEST) && defined (DATA_IN_SIGNAL) && defined (CONTROL_ONLY_MAC_UNIT_TEST)
#error "Can not define CONTROL_ONLY_MAC_UNIT_TEST with DATA_IN_SIGNAL"
#endif

/***************************************************************************
* Nested Include Files
***************************************************************************/
#include <system.h>
#include <kernel.h>
#include <utllist.h>

#include <cmac_sig.h>
#include <umac_sig.h>
#include <uphy_sig.h>


/******************************************************************************
 * Constants
 *****************************************************************************/
#if defined(UPGRADE_3G_HSDPA)
#define PHY_MAX_SIGNALS ((SignalId)(SIG_PHY_HS_UNASSIGN_POINTER_IND - UPHY_SIGNAL_BASE + 1))
#else /* UPGRADE_3G_HSDPA */
#define PHY_MAX_SIGNALS ((SignalId) (SIG_PHY_STATE_IND - UPHY_SIGNAL_BASE + 1))
#endif /* UPGRADE_3G_HSDPA */

#define UMAC_MAX_SIGNALS ((SignalId) (SIG_UMAC_HS_DEBUG_PHY_HS_DATA_IND - UMAC_SIGNAL_BASE + 1))
#define CMAC_MAX_SIGNALS ((SignalId) (SIG_CMAC_HFN_ABORT_REQ - CMAC_SIGNAL_BASE + 1))

#define UMAC_DEBUG_LEVEL                        DBG_LEVEL_ERROR

#define UMAC_U_RNTI_SRNC_ID_LENGTH              12
#define UMAC_U_RNTI_S_RNTI_LENGTH               20
#define UMAC_MS_TO_FRAMES(a)                    ( (a)/10)

#define UMAC_MIN_RECONFIG_TIME_IN_FRAMES        2

#if defined (ON_PC) && !defined (CONTROL_ONLY_MAC_UNIT_TEST)
#define UMAC_UL_DL_CFN_DIFF                     (0) 
#else /* On Target */
#define UMAC_UL_DL_CFN_DIFF                     (3) // 3 frames difference between UL CFN and DL CFN.
#endif /* ON_PC */

#define UMAC_UL_DL_NO_TIMES_TO_PROCESS          2

#define UMAC_TR_CH_INVALID                      ((Int8)0xFF)
#define UMAC_LOG_CH_NOT_PRESENT                 ((Int8)0xFF)

#define UMAC_RLC_LOG_CH_DATA                    ((Int8)0)
#define UMAC_READ_RB_IDENTITY(rbId)             (rbId & \
                                                    (~UMAC_BEARER_CONTROL_MASK))
#define UMAC_READ_RB_IDENTITY_ENUM(rbId)        (BearerIdentity) \
                                                    (rbId & \
                                                    (~UMAC_BEARER_CONTROL_MASK))
#define UMAC_READ_RB_LOG_CH(rbId)               (rbId & \
                                                    UMAC_BEARER_CONTROL_MASK)
#define UMAC_WRITE_RB_LOG_CH(rbId, boLogChCtrl) (rbId = (boLogChCtrl==TRUE)  ? \
                                                    rbId |                     \
                                                    UMAC_BEARER_CONTROL_MASK : \
                                                    rbId &                     \
                                                    ~UMAC_BEARER_CONTROL_MASK)

#define UMAC_LOGICAL_CHANNEL_START_IN_CT        1

#define UMAC_MAX_TIMERS                         10

#define UMAC_UE_ID_TYPE_LENGTH                  2
#define UMAC_UE_ID_TYPE_C_RNTI                  0x01
#define UMAC_UE_ID_TYPE_U_RNTI                  0x00

#define UMAC_U_RNTI_LENGTH                      32
#define UMAC_C_RNTI_LENGTH                      16

#define UMAC_CT_FIELD_LENGTH                    4

#define UMAC_MASK_OFF_CFN                       0xFF
#define UMAC_CFN_BIT_LENGTH                     8
#define UMAC_HFN_LSB_LENGTH                     4
#define UMAC_COUNT_C_START_ACT_TIME_OFFSET      232 /* at least 200, see TS 25.331 section 8.2.2.3, 8.3.6.3, ******** */
#define UMAC_COUNT_C_START_ACT_TIME_FRAME_ALIGNMENT (8)
#if defined(DEVELOPMENT_VERSION)
#define UMAC_DEBUG_ACTIVATION_TIME_MAXIMUM      256
#endif

#define UMAC_PERSISTENCE_SCALING_FACTOR_FACTOR     ((Int16)(10))
#define UMAC_DYNAMIC_PERSISTENCE_LEVEL_FACTOR      ((Int16)(128))
#define UMAC_PERSISTENCE_SCALING_FACTOR_DEFAULT    UMAC_PERSISTENCE_SCALING_FACTOR_FACTOR

#define UMAC_COMPRESSED_MODE_INFO_ARRAY_SIZE       8    /* Must be power 0f 2 */

/********** - start*/
/* Hypotetic HFN, cipher sync problem recovery mechnism constants */
#define  UMAC_NUM_DOUBLE_HFN_HYP                   (2)     
#define  UMAC_NUM_HYP_HFN                          (UMAC_NUM_DOUBLE_HFN_HYP*2)
/********** - end*/

/******************************************************************************
 * Macros
 *****************************************************************************/
#if defined(UMAC_DEBUG)
#define UMAC_DBG_PRINTF(level,msg)              if(umacDbg(level) == TRUE) \
                                                    { printf##msg ; }
#else
#define UMAC_DBG_PRINTF(level,msg)
#endif

#if defined(UT_LINKED_LIST_AS_MODULE)
#undef UMAC_DBG_PRINTF
#define UMAC_DBG_PRINTF(level,msg)
#endif

#define UMAC_CFN_LATER_THAN(a,b,cfn)            ( ( (a) - (cfn) ) > \
                                                          ( (b) - (cfn) ) )
#define UMAC_CFN_EARLIER_THAN(a,b,cfn)          ( ( (a) - (cfn) ) < \
                                                          ( (b) - (cfn) ) )
#define UMAC_CFN_DIFF(a,b)                      ( (a)>(b) ? (a)-(b) : (b)-(a) )

#if defined (UMTS7_PRE_R8)

#define UMAEHS_GET_QUEUE_NUM(bitmap,queueNum_p)     \
{													\
	static Int16 bitMask16[16] = {0x0001,			\
									0x0002,			\
		                            0x0004,			\
		                            0x0008,			\
		                            0x0010,			\
		                            0x0020,			\
		                            0x0040,			\
		                            0x0080,			\
		                            0x0100,			\
		                            0x0200,			\
		                            0x0400,			\
		                            0x0800,			\
		                            0x1000,			\
		                            0x2000,			\
		                            0x4000,			\
		                            0x8000};		\
	Int8 i;											\
	*(queueNum_p) = 0;								\
	for(i = 0; i < BITS_PER_INT16; i++)							\
	{												\
		if (bitmap & bitMask16[i])					\
		(*queueNum_p)++;							\
	}												\
}													

#endif
/********** - start*/
#define SIZEOF_CACHE_LINE               (32)
#define CACHE_LINE_MASK                 (SIZEOF_CACHE_LINE-1)
/********** - end*/

/******************************************************************************
 * Type Definitions
 *****************************************************************************/
typedef Int8 UmacLogicalChannelIdentity;
typedef Int8 BearerIdentityWithCtrl;
typedef void (*UmacHandler)(SignalBuffer *);

typedef enum DbgLevelTag
{
    DBG_LEVEL_ERROR,
    DBG_LEVEL_WARN,
    DBG_LEVEL_VERBOSE
}
DbgLevel;

/* UMAC state machine states - ideally would be in umacmain.c,
 * but superstate needed in rachInfo */

 /* Note that the following states are not the same states as RRC states,
   although they have a close resemblance. */

//ICAT EXPORTED ENUM   
typedef enum UmacInternalStateTag {
    UMAC_STATE_NULL = 0,
    UMAC_STATE_IDLE = 1,
    UMAC_STATE_COMMON = 2,
    UMAC_STATE_DEDICATED = 3,
/*********** begin*/    
#if defined (UPGRADE_UL_ECF)
    UMAC_STATE_COMMON_ENHANCED=4,  
#endif	
/*********** endif*/
    UMAC_STATE_NUMBER_OF_STATES
} UmacInternalState;

//ICAT EXPORTED ENUM
typedef enum UmacEdchStateTag {
    UMAC_NO_EDCH_STATE       = 0,
    UMAC_EDCH_ONLY_STATE     = 1,
    UMAC_EDCH_WITH_DCH_STATE = 2
} UmacEdchState;

typedef enum UmacEnhancedDlTypeTag {
    UMAC_HS_IS_ACTIVE,
    UMAC_EHS_IS_ACTIVE 					
} UmacEnhancedDlType;

typedef enum UmacDomainModeTag
{
    UMAC_TM_CS_DOMAIN_IDENTITY = UCN_DomainIdentity_cs_domain,
    UMAC_TM_PS_DOMAIN_IDENTITY = UCN_DomainIdentity_ps_domain,
    UMAC_TM_NO_DOMAIN, /* e.g. RB_0! */
    UMAC_NOT_TM_DOMAIN_UM,
    UMAC_NOT_TM_DOMAIN_AM
}
UmacDomainMode;

typedef enum UmacConfigTypeTag
{
    UMAC_CONFIG_TYPE_NEW,
    UMAC_CONFIG_TYPE_RECONFIG_OLD,
    UMAC_CONFIG_TYPE_RECONFIG_MODIFY,
    UMAC_CONFIG_TYPE_FOR_DELETION
}
UmacConfigType;

typedef enum SrbDelayStateTag
{
    UMAC_SRB_DELAY_ALL_DONE = 0,
    UMAC_SRB_DELAY_MAC_READY_UMAC_WAIT = 1,
    UMAC_SRB_DELAY_DECREMENT = 2,
    UMAC_SRB_DELAY_NOT_START_DECREMENT = 3
}
SrbDelayState;

typedef enum DlConfigChangeStateTag
{
    UMAC_DL_CONFIG_CHANGE_NULL = 0,
    UMAC_DL_CONFIG_CHANGE_IND_PENDING = 1,
    UMAC_DL_CONFIG_CHANGE_WAIT_FOR_RSP = 2
}
DlConfigChangeState;

typedef struct UmacSignalSetTag
{
    Int8               configIdentifier;
    Boolean            activateNow;
    UActivationTime    activationTime;
    KiUnitQueue        signalStore;
}
UmacSignalSet;

typedef struct UmacCipherSignalSetTag
{
    Int8               configIdentifier;
    Boolean            ulActivateNow;
    UActivationTime    ulActivationTime;
    Boolean            dlActivateNow;
    UActivationTime    dlActivationTime;
    KiUnitQueue        signalStore;
}
UmacCipherSignalSet;

typedef struct UmacFrameCmInfoTag
{
    Int8                        cfn;
    Int8                        numCmGaps;
    Int16                       cmGapsBitmap;
    UUL_CompressedModeMethod    cmMethod;
}
UmacFrameCmInfo;

typedef struct UmacCompressedModeInfoTag
{
    Boolean             isNewCmInfo;
    Boolean             isCmActive;
    Int32               nextFrameIndex;
    UmacFrameCmInfo     ttiCmInfo[UMAC_COMPRESSED_MODE_INFO_ARRAY_SIZE]; 
}
UmacCompressedModeInfo;


/***************************************************************************
 * Internal Ciphering structures
 ***************************************************************************/
typedef struct UmacCipheringInfoTag
{
    Boolean                         hasTmBearers;
    Boolean                         hfnIsIncrementing;
    Boolean                         cipheringStarted;
    UMacHyperFrameNumber            macdHfn;
    UMacHyperFrameNumber            macdHfnRestartValue;
    Int32                           umaHfn; /* initial HFN before increment */
	Boolean                         useAbortRestartValue;
	UMacHyperFrameNumber            macdHfnAbortRestartValue;
    Int32                           countC;
    KeySeqId                        ksi;
    UCipheringAlgorithm_r7          algorithm;
    USecurityKey                   *ck_p;
}
UmacCipheringInfo;

typedef struct UmacF8ParamsTag
{
    UmacCipheringInfo              *cipherConfig_p;
    Int8                            bearerIdentity;
    Int16                           bitOffset;
    Int16                           bitLength;
}
UmacF8Params;

typedef struct UmacCipheringTimingTag
{
    Int8                            ulDlPending; /* counter: 1 for UL, 1 for DL */
    Int8                            awaitingHfnIncr;  /* counter: 1 for UL, 1 for DL */
    Boolean                         determineHfnIncr;
    UActivationTime                 cfnToIncrHfn;
}
UmacCipheringTiming;

/* Periodic timers - used exclusively for periodic measurements */
typedef struct UmaUtlTimerTag
{
    Int16                  periodInFrames;
    Int16                  framesRemaining;
    UMeasurementIdentity   measurementIdentity;
}
UmaUtlTimer;

UT_DOUBLE_LINK_LIST_DECLARE(UmaUtlTimerList, UmaUtlTimer)

/* List of signals - used by compressed mode configurations */
UT_DOUBLE_LINK_LIST_DECLARE(UmaUtlSignalList, SignalBuffer)

typedef enum UmacCipherChainIdTag
{
    UMAC_CIPHER_CHAIN_ID_UL,
    UMAC_CIPHER_CHAIN_ID_DL, /* DCH, FACH for simplicity */
    UMAC_CIPHER_CHAIN_ID_DL_BCH, /* BCH only */
#if defined(UPGRADE_3G_HSDPA)
    UMAC_CIPHER_CHAIN_ID_HS,
#endif /* UPGRADE_3G_HSDPA */
    UMAC_MAX_CIPHER_CHAIN_IDS
}
UmacCipherChainId;

/* Function pointer to what MAC should do on completion of a cipher operation, if anything */
typedef void (*UmacCipherComplete)(void);

typedef struct UmacCipherChainTag
{
    Boolean            inUse;
    dtcTransferReq_ts *dtcTransferReq_p;
    UmacCipherComplete cipherCompleteFn;
}
UmacCipherChain;
/********** - start*/
typedef enum UmacHypHfnPhaseTag
{
    UMAC_HYP_HFN_DETECTION_PHASE = 0,
    UMAC_HYP_HFN_CORRECTION_PHASE = 1
}UmacHypHfnPhase;

/* Buffer to hold the different hypothetic deciphered SIDs */
typedef struct  UmacHypHfnDecipheredSidOutputBufferTag
{
    Int8   data_p[SIZEOF_CACHE_LINE];     
}UmacHypHfnDecipheredSidOutputBuffer;


/* DB for Hypotetic HFN, cipher sync problem recovery mechnism */
typedef struct UmacHypHfnStoreTag
{
    /*  Indicate if the mechanism for hfn correction is enabled or not */
    Boolean             enabled;

    /* In case mechanism is enabled, indicated the
     * recovery phase (Detection phase / Correction phase)
     */
    UmacHypHfnPhase     recoveryPhase;
  
    /* In correction phase, indicates the number of remaining valid HFN hypotheses */
    Int8                numOfValidHypHfns;   

    /* In correction phase, for each hypothetic hfn, indicate if its still valid (TRUE) or not (FALSE) */
    Boolean             hypHfnValid[UMAC_NUM_HYP_HFN]; 

	/* Data buffer to hold temporary SID decipher results */
    UmacHypHfnDecipheredSidOutputBuffer    * decipheredSidOutputBuffer_p;
}UmacHypHfnStore;
/********** - end*/

typedef struct UmacEntityTag
{
#ifdef UPGRADE_DSDSWB_L2
    UmacInternalState               macState[SIM_NUM];
#else
    UmacInternalState               macState;
#endif
    UmacEdchState                   edchState;
    Boolean                         fdpchConfigured;

/* UE-wide elements */
    Boolean                         interruptTimerOn;
    Boolean                         validUrnti;
    UU_RNTI                         u_RNTI;
    Boolean                         validCrnti;
    UC_RNTI                         c_RNTI;
    UrrHrntiType                    h_RNTI_Status;
    UmacCipheringTiming             cipherTiming;
    Int8                            ulCfn;       /* As reported in the previous
                                                    PhyFrameInd */
    Int8                            dlCfn;       /* As reported in this
                                                    PhyDataInd */                                                   
    Int8                            noTrChRbResolves;
    SignalBuffer                    phyFrameIndSigBuf;

/* List of periodic measurement timers */
    UmaUtlTimerList                 timerList;

/* Miscellaneous management */                   /* as requested by RRC */
    Boolean                         nextUlHlReconfigValid;
    Boolean                         nextDlHlReconfigValid;
    Boolean                         fachToDchPending;
    Int8                            nextHlReconfiguration; /* as CFN */
    UmacSignalSet                   ulReconfiguration;
    UmacSignalSet                   dlReconfiguration;
    SignalBuffer                    dlHfnSignal;
    CmacHandoverType                handoverType;
    UmacCipherSignalSet             cipheringChange;
    Boolean                         receivedHfnAbort;
    Int8                            cfnAtHfnAbort;
#if defined(ENABLE_UF8_CHAIN_NON_BLOCKING)
    UmacCipherChainId               currentCipherChain;
    UmacCipherChain                 outstandingCipherChains[UMAC_MAX_CIPHER_CHAIN_IDS];
#endif /* ENABLE_UF8_CHAIN_NON_BLOCKING */
#if defined(DEVELOPMENT_VERSION)
    Int16                           debugActTimePhyFrameInd;
    Int16                           debugActTimePhyDataInd;
#endif
#ifdef ENABLE_ULDATA_FUNCTION_CALL
	UmacUpdateTrafficInd			umacupdatetrafficInd;
	UmacTrafficReq					umactrafficReq;
	UmacNoTrafficReq				umacnotrafficreq;
	UmacTrafficInd					umacdchtrafficInd;
	UmacDataReq 					umacdchdataReq;	
#ifdef ON_PC
	UmacDataReq   					umacdchdataReqOnPC;
#endif
#endif
    Boolean                         pendingNowReconfig;
    Boolean                         pendingUlActivations;   /**< true when pending PendingUlActivation since waiting for umacDataReq */
    Boolean                         dchReadyToSend;         /**< dchReadyToSend value received in previous phyFrameInd    */
    Boolean                         awaitingTrafficReq;
    Boolean                         awaitingDataReq;
    KiUnitQueue                     qFrameInd;
    KiUnitQueue                     qDeactivateReq;
#ifdef UPGRADE_DSDSWB_L2
    Boolean                         awaitingDlPduListInfoRsp[SIM_NUM];
#else
    Boolean                         awaitingDlPduListInfoRsp;
#endif
    Boolean                         awaitingUllastsignal;
#if defined(ON_PC)
    Boolean                         unitTest;
#endif
    Boolean                         debugPhyDataReq;
    Boolean                         debugPhyDataInd;
    Boolean                         sendingTrafficInd;
    Int8                            prevUlCfn;
#if defined (ENABLE_UPLANE_STATISTICS)
    Boolean                         startStatsLog;            /* start statistic logging */
    KernelTicks                     mSecUpdateTrafficInd;     /* log point for Update Traffic Ind */
    KernelTicks                     mSecUmacTrafficReq;       /* log point for Umac Traffic Req */
    KernelTicks                     mSecUmacDataReq;          /* log point for Umac Data Req */
    KernelTicks                     mSecSendPhyDataReq;       /* log point for SendPhyDataReq */
    KernelTicks                     maxqFrameIndLength;       /* Max number of PhyFrameInd Queue Length */
    KernelTicks                     meanUmacTxDataTime;       /* Mean MAC Data Tx Turnaround Time */
    KernelTicks                     maxUmacTxDataTime;        /* Max MAC Data Tx Turnaround Time */
#endif /* ENABLE_UPLANE_STATISTICS */
    Boolean                         newCmacHfnConfigReqReceived;
    UmacCompressedModeInfo          compressedModeInfo;
    UmacDlConfigChangeInd           umacDlConfigChangeInd;    /* UmacDlConfigChangeInd can be sent later than on cfn arival if mac-hs 
                                                               * has PDUs in its queues. Store the fields and send the signal when all
                                                               * mac-hs PDUs delivered to RLC.
                                                               */
    DlConfigChangeState             dlConfigChangeState;
	UmacEnhancedDlType 				enhancedDlType;
	Boolean							interRatTransition;
	dtcF8DescriptorInfo        		*hsdpaDescChainHead_p;   /* Hold a pointer the HS/EHS descriptor chain head*/

	/*CQ45146 Do NOT increment HFN if the activation time in new cipher configuration is zero, yhang add 2013-10-09*/
	Boolean 						dlHfnStopIncrement;
	Boolean 						ulHfnStopIncrement;

#if defined (ESCC)
    Int8                            activationTimeOffsetExpiryCfn;
    Boolean                         activationTimeOffsetExpiryRequired;
#endif

    SignalBuffer                    ulTfcSubsetMessage;    /* When a TFC subset message arrives while there is a pending
                                                            * reconfiguration, this signal buffer will hold the message
                                                            * and it will be activated immediately */
    UmacHypHfnStore                 hypHfnStore; /********** - add*/
} UmacEntity;


/******************************************************************************
 * Function Prototypes
 *****************************************************************************/
Boolean umacDbg(DbgLevel level);

/* Functions provided by umamain.c */
extern void UmaHandleRNTIConfigReq(SignalBuffer *sigbuf_p);
/*Fixed CQ23028 taoye modify begin 20121012*/
#if defined(UPGRADE_3G_HSDPA)            
extern void UmaHandleHRNTIConfigReq(SignalBuffer *sigbuf_p); 
#endif
/*Fixed CQ23028 taoye modify end 20121012*/

extern void UmaGetFrameCompressModeInfo(Int8 cfn);

/* Functions provided by umautl.c */
Int8 UmaUtlMapDynamicPersistenceLevel
                        (UDynamicPersistenceLevel dynamicPersistenceLevel);
Int8 UmaUtlMapPersistenceScalingFactor
                        (UPersistenceScalingFactor persistenceScalingFactor);
Boolean UmaUtlCheckPersistency
                        (Int8 selectedASC,
                         Int16 dynamicPersistenceLevel,
                         Int16 persistenceScalingFactor);
Int8 UmaUtlGetBackoffTimerFramesDuration(Int8 nb01Min, Int8 nb01Max);
UTransportChannelType UmaUtlConvertFromAsnUlTrChType
                        (T_UUL_TransportChannelType asnTrChType);
UTransportChannelType UmaUtlConvertFromAsnDlTrChType
                        (T_UDL_TransportChannelType_r7 asnTrChType);
Int8 UmaUtlFramesInTti  (UTransmissionTimeInterval tti);
UTransmissionTimeInterval UmaUtlFramesToTti(Int8 framesInTti);

/* Functions provided to UL / DL */
void UmaUtlDestroySignal(UmacInternalState macState, SignalBuffer * rxSignal_p);

/* Timer functions for Periodic Measurements */
void UmaUtlTickPeriodicMeasurementTimer
                        (UmaUtlTimerList *list_p);
void UmaUtlStartPeriodicMeasurementTimer
                        (UmaUtlTimerList *list_p,
                         UMeasurementIdentity measurementIdentity,
                         Int16 periodInFrames);
void UmaUtlStopPeriodicMeasurementTimer
                        (UmaUtlTimerList *list_p,
                         UMeasurementIdentity measurementIdentity);

URLC_BuffersPayload UmaUtlComputeTctv(UrlTrafficVolumeBits trafficVolumeBits);

#if defined(ENABLE_UF8_CHAIN_NON_BLOCKING)
/* Functions from umamain.c */
void Umacf8chain(dtcTransferReq_ts *dtcRequest, UmacCipherChainId chainId);
Boolean UmacF8chainInUse(Int8 chainId);
void UmacSetF8chainInUse(Int8 chainId);
void UmacClearF8chainInUse(Int8 chainId);
#endif /* ENABLE_UF8_CHAIN_NON_BLOCKING */

#endif

/* END OF FILE */

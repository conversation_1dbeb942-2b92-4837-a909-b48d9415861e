/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/3g.mod/pub/src/usimcfg.H#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/31 16:40:51 $
 **************************************************************************
 *  File Description :
 *
 *   USIM config file
 **************************************************************************/

#if !defined (USIMCFG_H)
#define USIMCFG_H

/***************************************************************************
 * Nested Include Files
 **************************************************************************/

#if !defined (SYSTEM_H)
#   include  <system.h>
#endif

#if !defined (KERNEL_H)
#   include  <kernel.h>
#endif

#if !defined (L1SI_SIG_H)
#include     <l1si_sig.h>
#endif

#if !defined (ALSI_SIG_H)
#include     <alsi_sig.h>
#endif

/***************************************************************************
 * Manifest Constants
 **************************************************************************/

/*these tables are used to determine the values for F and D in the PPS request*/
#define NUM_CR_TABLE_LINES 12
#define NUM_BR_TABLE_LINES  9  /* Mod by jungle for CQ00053584 on 2014-01-24 */

#if defined (SIM_UICC_USE_VALUES_FROM_ATR)
#define SIM_FREQ_DEFAULT     SIM_FREQ_4_MHZ      /*Frequency used by default*/
#else
#define SIM_FREQ_DEFAULT     SIM_FREQ_3_25_MHZ   /*Frequency used by default*/
#endif

extern const crFactorParam CLOCK_RATE_CONVERSION_TABLE [NUM_CR_TABLE_LINES];


extern const brFactorParam BAUD_RATE_CONVERSION_TABLE [NUM_BR_TABLE_LINES];

/***************************************************************************
 * Global functions
 **************************************************************************/
Int8
SimUiccGetPps1  (Int8                ta1,
                 SimClockRateFactor *clockRateFactor,
                 SimBaudRateFactor  *baudRateFactor,
                 SimClockFreq       *clockFreq,
                 SimVoltage          voltage,
                 Int8               numPpsTries,
                 Int8                simID );/*CQ00113421,CQ00125586*/



/***************************************************************************
 * Global functions
 **************************************************************************/

#endif



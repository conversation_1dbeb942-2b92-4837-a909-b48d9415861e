/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/***************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 ***************************************************************************
 *
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urrriedc.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/27 15:10:43 $
 *
 ***************************************************************************
 *
 * File Description:
 *
 *    Header file for the CCCH message handling functionality of the RIE
 *    RRC module.
 *
 ***************************************************************************
 *
 ***************************************************************************/

#if !defined (URRRIEDC_H)
#define       URRRIEDC_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <uas_asn.h>
#include <utper.h>          /* For PerBuffer */
#include <urrtypes.h>       /* For UrrRxMsgVersion */

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/

/***************************************************************************
 *  Macro Functions
 ***************************************************************************/

/***************************************************************************
 * Types
 ***************************************************************************/

 /***************************************************************************
 * Typed Constants
 ***************************************************************************/

/***************************************************************************
 * Global Data declarations
 ***************************************************************************/

/***************************************************************************
 * Global Function Prototypes
 ***************************************************************************/

UrrRxMsgVersion UrrRieDcUCellUpdateConfirm_CCCH (
    PerBuffer * perBuffer_p, 
    UCellUpdateConfirm_CCCH * msg_p);

UrrRxMsgVersion UrrRieDcURRCConnectionRelease_CCCH (
    PerBuffer * perBuffer_p, 
    URRCConnectionRelease_CCCH * msg_p);

UrrRxMsgVersion UrrRieDcURRCConnectionSetup (
    PerBuffer * perBuffer_p, 
    URRCConnectionSetup * msg_p);

void UrrRieDcURRCConnectionSetup_R7(
	PerBuffer * perBuffer_p, 
	URRCConnectionSetup * msg_p);

void UrrRieDcUCellUpdateConfirm_CCCH_r7 (
    PerBuffer * perBuffer_p,
    UCellUpdateConfirm_CCCH *msg_p);

#endif /* !defined (URRRIEDC_H) */

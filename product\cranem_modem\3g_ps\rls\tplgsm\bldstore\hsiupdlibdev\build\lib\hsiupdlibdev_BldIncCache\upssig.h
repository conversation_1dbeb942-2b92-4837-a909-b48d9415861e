/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/api/inc/upssig.h#6 $
 *   $Revision: #6 $
 *   $DateTime: 2007/03/30 13:53:25 $
 **************************************************************************
 * File Description:
 *
 * 3G PS Signal definitions
 **************************************************************************/
 /*******************************  Revision History ***********************
  CQ/Activity     SW Eng       Date              Description
  **********      efrats      07/08/08        AGPS re-design - update AGPS signals.
  **********      yanive      04/07/10        Internal KI queue re-design

  **************************************************************************/
#if defined (UPGRADE_GENIE_INTERNAL)
/* ********** : Upgrade to LTE+WCDMA feature : begin */
//#if defined (UPGRADE_LTE) ********** remove
#include<Iratduallinksig.h>
//#endif ********** remove
/* ********** : Upgrade to LTE+WCDMA feature : end */
#endif


#if !defined (EXCLUDE_CBMC)
#if !defined (SIGNAL_MAPPING) //2013.3.21 Jiantao modified for LTE+W+G UE PC VS project
SIG_DEF( SIG_CBMC_DUMMY = CBMC_SIGNAL_BASE,             EmptySignal                     cbmc_dummy )
#endif
SIG_DEF( SIG_CBMC_RX_IND,                               CbmcRxInd                       cbmcRxInd )
SIG_DEF( SIG_CBMC_CONFIG_IND,                           CbmcConfigInd                   cbmcConfigInd )
SIG_DEF( SIG_CBMC_DRX_LEVEL_1_IND,                      CbmcDrxLevel1Ind                cbmcDrxLevel1Ind )
SIG_DEF( SIG_CBMC_CELL_CHANGE_IND,                      CbmcCellChangeInd               cbmcCellChangeInd )
#endif /* EXCLUDE_CBMC */

#if !defined (EXCLUDE_CPDC)
#if !defined (SIGNAL_MAPPING) //2013.3.21 Jiantao modified for LTE+W+G UE PC VS project
SIG_DEF( SIG_CPDC_DUMMY = CPDC_SIGNAL_BASE,             EmptySignal                     cpdc_dummy )
#endif
SIG_DEF( SIG_CPDCP_CONFIG_REQ,                          CpdcpConfigReq                  cpdcpConfigReq )
SIG_DEF( SIG_CPDCP_RELEASE_REQ,                         CpdcpReleaseReq                 cpdcpReleaseReq )
SIG_DEF( SIG_CPDCP_RELEASE_REQ_CONT,                    CpdcpReleaseReqCont             cpdcpReleaseReqCont )/*CQ0094287 - add*/
SIG_DEF( SIG_CPDCP_RELEASE_REQ_CONT_IND,                CpdcpReleaseReqContInd          cpdcpReleaseReqContInd ) /*CQ0094287 - add*/

SIG_DEF( SIG_CPDCP_RELOC_REQ,                           CpdcpRelocReq                   cpdcpRelocReq )
SIG_DEF( SIG_CPDCP_RELOC_CNF,                           CpdcpRelocCnf                   cpdcpRelocCnf )
SIG_DEF( SIG_CPDCP_STATUS_IND,                          CpdcpStatusInd                  cpdcpStatusInd )
SIG_DEF( SIG_CPDCP_CLOSE_TEST_LOOP_MODE_REQ,            CpdcpCloseTestLoopReq           cpdcpCloseTestLoopReq )
SIG_DEF( SIG_CPDCP_OPEN_TEST_LOOP_MODE_REQ,             EmptySignal                     cpdcpOpenTestLoopReq )
SIG_DEF( SIG_CPDCP_SN_IND,                              CpdcpSnInd                      cpdcpSnInd )
#if defined (PDCP_TEST_SN)
SIG_DEF( SIG_CPDCP_SN_REQ,                              CpdcpSnReq                      cpdcpSnReq )
#endif /* PDCP_TEST_SN */
#endif /* EXCLUDE_CPDC */

#if !defined (EXCLUDE_ULBG)
#if !defined (SIGNAL_MAPPING) //2013.3.21 Jiantao modified for LTE+W+G UE PC VS project
SIG_DEF( SIG_ULBGDLBG_DUMMY = ULBGDLBG_SIGNAL_BASE,   EmptySignal                     ulbgdlbg_dummy )
#endif
SIG_DEF( SIG_ULBG_DLBG_DEACTIVATE_IND,                  UlbgDlbgDeactivateInd           ulbgDlbgDeactivateInd )
SIG_DEF( SIG_ULBG_DLBG_PDP_IND,             UlbgDlbgPdpInd                  ulbgDlbgPdpInd )
SIG_DEF( SIG_ULBG_DLBG_RESET_PDP_COUNTER,         UlbgDlbgResetPdpCounterInd      ulbgDlbgResetPdpCounterInd )
SIG_DEF( SIG_ULBG_DLBG_XID_REQ,             UlbgDlbgXidReq                  ulbgDlbgXidReq)
SIG_DEF( SIG_ULBG_DLBG_DATA_RSP,                        EmptySignal                     ulbgDlbgDataRsp)  
#endif /* EXCLUDE_ULBG */

#if !defined (EXCLUDE_CCSD)
#if !defined (SIGNAL_MAPPING) //2013.3.21 Jiantao modified for LTE+W+G UE PC VS project
SIG_DEF( SIG_CCSD_DUMMY = CCSD_SIGNAL_BASE,             EmptySignal                     ccsd_dummy )
#endif
SIG_DEF( SIG_CCSD_CONFIG_REQ,                           CcsdConfigReq                   ccsdConfigReq )
SIG_DEF( SIG_CCSD_RELEASE_REQ,                          CcsdReleaseReq                  ccsdReleaseReq )
SIG_DEF( SIG_CCSD_SUSPEND_REQ,                          CcsdSuspendReq                  ccsdSuspendReq )
SIG_DEF( SIG_CCSD_RESUME_REQ,                           CcsdResumeReq                   ccsdResumeReq )
SIG_DEF( SIG_CCSD_UL_TTI_IND,                           CcsdUlTtiInd                    ccsdUlTtiInd )
SIG_DEF( SIG_CCSD_DL_TTI_IND,                           CcsdDlTtiInd                    ccsdDlTtiInd )
#endif /* EXCLUDE_CCSD */

#if !defined (EXCLUDE_CSDI)
#if !defined (SIGNAL_MAPPING) //2013.3.21 Jiantao modified for LTE+W+G UE PC VS project
SIG_DEF( SIG_CSDI_DUMMY = CSDI_SIGNAL_BASE,             EmptySignal                     csdi_dummy )
#endif
SIG_DEF( SIG_CSDI_CONFIG_IND,                           CsdiConfigInd                   csdiConfigInd )
SIG_DEF( SIG_CSDI_RELEASE_IND,                          CsdiReleaseInd                  csdiReleaseInd )
SIG_DEF( SIG_CSDI_SUSPEND_IND,                          CsdiSuspendInd                  csdiSuspendInd )
SIG_DEF( SIG_CSDI_RESUME_IND,                           CsdiResumeInd                   csdiResumeInd )
SIG_DEF( SIG_CSDI_CONNECT_REQ,                          CsdiConnectReq                  csdiConnectReq )
SIG_DEF( SIG_CSDI_DISCONNECT_REQ,                       CsdiDisconnectReq               csdiDisconnectReq )
SIG_DEF( SIG_CSDI_CONNECT_CNF,                          CsdiConnectCnf                  csdiConnectCnf )
SIG_DEF( SIG_CSDI_DISCONNECT_CNF,                       CsdiDisconnectCnf               csdiDisconnectCnf )
SIG_DEF( SIG_CSDI_TTI_IND,                              CsdiTtiInd                      csdiTtiInd )
SIG_DEF( SIG_CSDI_AMR_DATA_REQ,                         CsdiAmrDataReq                  csdiAmrDataReq )
SIG_DEF( SIG_CSDI_CSD_DATA_REQ,                         CsdiCsdDataReq                  csdiCsdDataReq )
SIG_DEF( SIG_CSDI_AMR_DATA_IND,                         CsdiAmrDataInd                  csdiAmrDataInd )
SIG_DEF( SIG_CSDI_CSD_DATA_IND,                         CsdiCsdDataInd                  csdiCsdDataInd )
SIG_DEF( SIG_CSDI_ENG_INFO_IND,                         CsdiEngInfoInd                  csdiEngInfoInd)
#endif /* EXCLUDE_CSDI */

#if !defined (EXCLUDE_RRC)
#if !defined (SIGNAL_MAPPING) //2013.3.21 Jiantao modified for LTE+W+G UE PC VS project
SIG_DEF( SIG_RRC_DUMMY = RRC_SIGNAL_BASE,               EmptySignal                     rrc_dummy )
#else
SIG_DEF( SIG_RRC_DUMMY,               EmptySignal                     rrc_dummy )
#endif
SIG_DEF( SIG_RRC_ACT_REQ,                               RrcActReq                       rrcActReq )
SIG_DEF( SIG_RRC_ACT_CNF,                               RrcActCnf                       rrcActCnf )
SIG_DEF( SIG_RRC_ACT_IND,                               RrcActInd                       rrcActInd )
SIG_DEF( SIG_RRC_DEACT_REQ,                             RrcDeactReq                     rrcDeactReq )
SIG_DEF( SIG_RRC_DEACT_CNF,                             EmptySignal                     rrcDeactCnf )
SIG_DEF( SIG_RRC_PAGE_IND,                              RrcPageInd                      rrcPageInd )
SIG_DEF( SIG_RRC_ESTABLISH_REQ,                         RrcEstablishReq                 rrcEstablishReq )
SIG_DEF( SIG_RRC_ESTABLISH_CNF,                         RrcEstablishCnf                 rrcEstablishCnf )
SIG_DEF( SIG_RRC_CONN_REL_REQ,                          RrcConnRelReq                   rrcConnRelReq )
SIG_DEF( SIG_RRC_CONN_REL_CNF,                          RrcConnRelCnf                   rrcConnRelCnf )
SIG_DEF( SIG_RRC_CONN_REL_IND,                          RrcConnRelInd                   rrcConnRelInd )
SIG_DEF( SIG_RRC_DATA_REQ,                              RrcDataReq                      rrcDataReq )
SIG_DEF( SIG_RRC_DATA_IND,                              RrcDataInd                      rrcDataInd )
SIG_DEF( SIG_RRC_SMC_ABORT_IND,                         RrcSmcAbortInd                  rrcSmcAbortInd )/*CQ00048124*/
SIG_DEF( SIG_RRC_SYS_INFO_IND,                          RrcSysInfoInd                   rrcSysInfoInd )
SIG_DEF( SIG_RRC_ME_DATA_REQ,                           RrcMeDataReq                    rrcMeDataReq )
SIG_DEF( SIG_RRC_MS_DATA_REQ,                           RrcMsDataReq                    rrcMsDataReq )
SIG_DEF( SIG_RRC_SECURITY_KEY_SET_CONFIG_REQ,           RrcSecurityKeySetConfigReq      rrcSecurityKeySetConfigReq )
SIG_DEF( SIG_RRC_UPDATE_REQ,                            RrcUpdateReq                    rrcUpdateReq )
SIG_DEF( SIG_RRC_CODEC_IND,                             RrcCodecInd                     rrcCodecInd )   /* CQ29374 yhang add 2013-02-27 */
SIG_DEF( SIG_RRC_CELL_UPDATE_IND,                       RrcCellUpdateInd                rrcCellUpdateInd )
SIG_DEF( SIG_RRC_POWER_CLASS_REQ,                       RrcPowerClassReq                rrcPowerClassReq )
SIG_DEF( SIG_RRC_STATUS_IND,                            RrcStatusInd                    rrcStatusInd )
SIG_DEF( SIG_RRC_PLMN_LIST_REQ,                         RrcPlmnListReq                  rrcPlmnListReq )
SIG_DEF( SIG_RRC_PLMN_LIST_CNF,                         RrcPlmnListCnf                  rrcPlmnListCnf )
SIG_DEF( SIG_RRC_TEST_MODE_REQ,                         RrcTestModeReq                  rrcTestModeReq )
SIG_DEF( SIG_RRC_REQUEST_STOP_IRAT_REQ,                 RrcRequestStopIratReq           rrcRequestStopIratReq ) /* CQ00071555 */
#if defined(UPGRADE_DS_PHASE_II_WITH_NAS)
SIG_DEF( SIG_RRC_DS_PAGING_FAILURE_IND,                 RrcDsPagingFailureInd           rrcDsPagingFailureInd ) 
#endif/*UPGRADE_DS_PHASE_II_WITH_NAS*/

/*********************************************
 * PLEASE DON'T ADD ANY "RRC" SIGNALS BEFORE
 * THE RRC_DEBUG SIGNALS, AS IT MIGHT CAUSE
 * PROBLEMS WITH MESSAGE DECODING IN ACAT
 **********************************************/
SIG_DEF( SIG_RRC_DEBUG_SYSTEM_INFORMATION_IND,          RrcDebugSystemInformationInd    rrcDebugSystemInformationInd )
SIG_DEF( SIG_RRC_DEBUG_AIR_INTERFACE_RX_IND,            RrcDebugAirInterfaceRxInd       rrcDebugAirInterfaceRxInd )
SIG_DEF( SIG_RRC_DEBUG_AIR_INTERFACE_TX_IND,            RrcDebugAirInterfaceTxInd       rrcDebugAirInterfaceTxInd )
SIG_DEF( SIG_RRC_DEBUG_GSM_AIR_INTERFACE_RX_IND,        RrcDebugGsmAirInterfaceRxInd    rrcDebugGsmAirInterfaceRxInd )
SIG_DEF( SIG_RRC_DEBUG_INTEGRITY_PROTECT_FAIL_IND,      RrcDebugIntegrityProtectFailInd rrcDebugIntegrityProtectFailInd )
/* added for CQ00012101 start */
SIG_DEF( SIG_RRC_DEBUG_INTEGRITY_PROTECT_SUCC_IND,      RrcDebugIntegrityProtectSuccInd rrcDebugIntegrityProtectSuccInd )
/* added for CQ00012101 end */
SIG_DEF( SIG_RRC_DEBUG_TRANSACTION_LIST_IND,            RrcDebugTransListInd            rrcDebugTransListInd )
SIG_DEF( SIG_RRC_DEBUG_STATE_CHANGE_IND,                RrcDebugStateChangeInd          rrcDebugStateChangeInd )
SIG_DEF( SIG_RRC_SYNC_IND,                              RrcSyncInd                      rrcSyncInd )
#if defined (RRC_UPGRADE_ENG_MODE_REDESIGN) //********** begin
SIG_DEF( SIG_RRC_ENG_MODE_INFO_REQ,           RrcEngModeInfoReq       rrcEngModeInfoReq )
SIG_DEF( SIG_RRC_ENG_MODE_INFO_IND,           RrcEngModeInfoInd       rrcEngModeInfoInd )
#endif //RRC_UPGRADE_ENG_MODE_REDESIGN :********** end
/*CQ00065587 START */
#if defined UPGRADE_CSG1
SIG_DEF(SIG_RRC_CSG_LIST_REQ,                               RrcCsgListReq                   rrcCsgListReq)
SIG_DEF(SIG_RRC_CSG_LIST_CNF,                               RrcCsgListCnf                   rrcCsgListCnf)
SIG_DEF(SIG_RRC_AUTO_CSG_SELECT_REQ,                        RrcAutoCsgSelectReq             rrcAutoCsgSelectReq)
SIG_DEF(SIG_RRC_AUTO_CSG_SELECT_CNF,                        RrcAutoCsgSelectCnf             rrcAutoCsgSelectCnf)
SIG_DEF(SIG_RRC_CSG_SELECT_REQ,                             RrcCsgSelectReq                 rrcCsgSelectReq)
SIG_DEF(SIG_RRC_CSG_SELECT_CNF,                             RrcCsgSelectCnf                 rrcCsgSelectCnf)
/*CQ0085671 - Deleted CQ0070940*/


#endif //UPGRADE_CSG1
/*CQ00065587 END */
SIG_DEF( SIG_RRC_NAS_TX_QUEUE_INFO_IND,                 RrcNasTxQueueInfoInd            rrcNasTxQueueInfoInd )
SIG_DEF( SIG_RRC_ENG_INFO_REQ,                          RrcEngInfoReq                   rrcEngInfoReq )
SIG_DEF( SIG_RRC_ENG_INFO_IND,                          RrcEngInfoInd                   rrcEngInfoInd )
SIG_DEF( SIG_RRC_NETWORK_AUTH_FAIL_REQ,                 EmptySignal                     rrcNetworkAuthFailReq )
SIG_DEF( SIG_RRC_DEBUG_INFORMATION_IND,                 RrcDebugInformationInd          rrcDebugInformationInd )
/* CQ00012793 added end */
/*added for CQ00012063 start*/   
SIG_DEF( SIG_RRC_MONITOR_UE_IND,              RrcMonitorUeInd         rrcMonitorUeInd )
/*added for CQ00012063 end*/   

#if !defined UPGRADE_PLMS
SIG_DEF( SIG_RRC_SCAN_AND_DETECT_RESULT_REPORT_IND,              RrcScanAndDetectResultReportInd           rrcScanAndDetectResultReportInd )//CQ00118692 added
#endif

SIG_DEF( SIG_RRC_UNIT_TEST_REQ,                         EmptySignal                     rrcUnitTestReq )
SIG_DEF( SIG_RRC_DEBUG_SYS_INFO_WITH_ERROR_IND,         RrcDebugSysInfoWithErrorInd     rrcDebugSysInfoWithErrorInd )
SIG_DEF( SIG_RRC_DEBUG_MSC_IND,                         RrcDebugMscInd                  rrcDebugMscInd )
SIG_DEF( SIG_RRC_PLMN_LIST_IND,                         RrcPlmnListInd                  rrcPlmnListInd )
SIG_DEF( SIG_RRC_DEBUG_AIR_INTERFACE_STATUS_IND,        RrcDebugAirInterfaceStatusInd   rrcDebugAirInterfaceStatusInd )
SIG_DEF( SIG_RRC_PLMN_LIST_ABORT_REQ,                   EmptySignal                     rrcPlmnListAbortReq )
SIG_DEF( SIG_RRC_HS_STATUS_IND,                         RrcHsStatusInd                  rrcHsStatusInd )
SIG_DEF( SIG_RRC_CELL_HS_CAP_IND,                     RrcCellHsCapInd                 rrcCellHsCapInd )
SIG_DEF( SIG_RRC_FD_ENABLED_IND,            RrcFdEnabledInd         rrcFdEnabledInd )
SIG_DEF( SIG_RRC_FD_CONFIG_REQ,             RrcFdConfigReq          rrcFdConfigReq )

SIG_DEF( SIG_RRC_PS_DL_DATA_IND,                        RrcPsDlDataInd                  rrcPsDlDataInd)
SIG_DEF( SIG_RRC_PS_DL_DATA_RSP,                        RrcPsDlDataRsp                  rrcPsDlDataRsp)
SIG_DEF( SIG_RRC_PS_UL_DATA_REQ,                        RrcPsUlDataReq                  rrcPsUlDataReq)
SIG_DEF( SIG_RRC_PS_UL_DATA_CNF,                        RrcPsUlDataCnf                  rrcPsUlDataCnf)
#if defined(UPGRADE_DSDS)
SIG_DEF( SIG_DMM_RRC_RESUME_REQ,                            EmptySignal                     rrcResumeReq )
/* **********, added begin */
SIG_DEF( SIG_DMM_RRC_CANCEL_SERVICE_REQ,                    EmptySignal                     rrcCancelServiceReq )
/* **********, added end */
#endif
#if defined (UPGRADE_DUAL_LINK)
SIG_DEF( SIG_RRC_DL_PS_REL_IND,                 EmptySignal         rrcDlPsRelInd )/************/
#endif

SIG_DEF( SIG_RRC_DATA_STATUS_REQ,                     RrcDataStatusReq                rrcDataStatusReq)
SIG_DEF( SIG_RRC_DATA_STATUS_CNF,                     RrcDataStatusCnf                rrcDataStatusCnf)
SIG_DEF( SIG_RRC_DATA_STATUS_IND,           RrcDataStatusInd        rrcDataStatusInd)

#if defined (ENABLE_URRC_UNIT_TESTING)
SIG_DEF( SIG_RRC_UTM_TM_DATA_IND,             RrcUtmTmDataInd             rrcUtmTmDataInd)
SIG_DEF( SIG_RRC_UTM_UM_DATA_IND,           RrcUtmUmDataInd         rrcUtmUmDataInd)
SIG_DEF( SIG_RRC_UTM_AM_DATA_IND,             RrcUtmAmDataInd             rrcUtmAmDataInd)
#endif // ENABLE_URRC_UNIT_TESTING

SIG_DEF( SIG_RRC_CELL_LOCK_CNF,                         RrcCellLockCnf                  rrcCellLockCnf)
SIG_DEF( SIG_RRC_CELL_LOCK_REQ,                         RrcCellLockReq                  rrcCellLockReq)
SIG_DEF( SIG_RRC_SET_CELL_SELECT_CONFIG_REQ,            RrcSetCellSelectConfigReq       rrcSetCellSelectConfigReq)//CQ00115560 added

SIG_DEF( SIG_RRC_BARRED_CELL_UPDATE_REQ,                RrcBarredCellUpdateReq          rrcBarredCellUpdateReq)//CQ00116138 added

/* CQ00031717,added begin */
SIG_DEF( SIG_RRC_DIP_DATA_IND,                          RrcDipDataInd                   rrcDipDataInd)
/* CQ00031717,added end */
#if 0/*CQ00086477 remove*/
SIG_DEF( SIG_RRC_CONNECTION_STATE_IND,                  RrcConnectionStateInd           rrcConnectionStateInd)    /* CQ00082630 addeded */
#endif
/* CQ00035171,added begin */
SIG_DEF( SIG_RRC_CS_TEST_LOOP_MODE_IND,                 RrcCsTestLoopModeInd            rrcCsTestLoopModeInd)
/* CQ00035171,added end */
/* CQ00053559, added begin */
#if defined (UPGRADE_SRVCC)
SIG_DEF( SIG_RRC_SRVCC_IND,                             RrcSrvccInd                     rrcSrvccInd )
#endif
/* CQ00053559, added end */

/* CQ00054105, added begin *//*CQ00084302 change begin*/
SIG_DEF( SIG_RRC_SAC_COMMON_INFO_IND,                   RrcSacCommonInfoInd             rrcSacCommonInfoInd)
/* CQ00054105, added end *//*CQ00084302 change end*/
SIG_DEF( SIG_RRC_TEST_REQ,                              EmptySignal                     rrcTestReq )

SIG_DEF( SIG_RRC_DEBUG_TRACING_TRIGGER_IND,             EmptySignal                     rrcDebugTracingTriggerInd )
/*add by taow 20200612 CQ00121467 begin*/
SIG_DEF( SIG_URRC_WATCH_REPORT_IND,                     EmptySignal               rrcWatchReportInd)
/*add by taow 20200612 CQ00121467 end*/

//CQ00067244 begin
#if defined (UPGRADE_ECID)
SIG_DEF( SIG_RRC_ECID_MEAS_REQ,                         RrcEcidMeasReq                   rrcEcidMeasReq)
SIG_DEF( SIG_RRC_ECID_MEAS_CNF,                         RrcEcidMeasCnf                   rrcEcidMeasCnf)
#endif/*UPGRADE_ECID*/
//CQ00067244 end

// Internal signals section
#if !defined (SIGNAL_MAPPING) //2013.3.21 Jiantao modified for LTE+W+G UE PC VS project
SIG_DEF( SIG_RRC_INT_DUMMY = RRC_INT_SIGNAL_BASE,       EmptySignal                      rrcInt_dummy)
#endif
SIG_DEF( SIG_RRC_INT_BCCH_SYS_INFO,                     RrcIntBcchSysInfo                rrcIntBcchSysInfo)
/* CQ00065510 begin */
#if defined (RRC_SIR_OPTIMIZATION)
SIG_DEF( SIG_RRC_INT_CELL_MATCH_DB,                     EmptyIntSig                      rrcIntCellMatchDb)
#endif //RRC_SIR_OPTIMIZATION
/* CQ00065510 end */
SIG_DEF( SIG_RRC_INT_CHANGE_STATE,                      RrcIntChangeState                rrcIntChangeState)
SIG_DEF( SIG_RRC_INT_NEW_UE_STATE,                      RrcIntNewUeState                 rrcIntNewUeState)
SIG_DEF( SIG_RRC_INT_CHANGE_PHY_SM,                     RrcIntChangePhySm                rrcIntChangePhySm)
SIG_DEF( SIG_RRC_INT_RRC_MS_DATA_REQ,                   RrcIntRrcMsDataReq               rrcIntRrcMsDataReq)
SIG_DEF( SIG_RRC_INT_RRC_ME_DATA_REQ,                   RrcIntRrcMeDataReq               rrcIntRrcMeDataReq)
SIG_DEF( SIG_RRC_INT_RRC_UPDATE_REQ,                    RrcIntRrcUpdateReq               rrcIntRrcUpdateReq)
SIG_DEF( SIG_RRC_INT_CELL_SELECTED,                     RrcIntCellSelected               rrcIntCellSelected)
SIG_DEF( SIG_RRC_INT_SELECT_CELL,                       RrcIntSelectCell                 rrcIntSelectCell)
SIG_DEF( SIG_RRC_INT_CAMPED_ON_CELL,                    RrcIntCampedOnCell               rrcIntCampedOnCell)
SIG_DEF( SIG_RRC_INT_BCCH_MODIFICATION,                 RrcIntBcchModInfo                rrcIntBcchModificationInfo)
SIG_DEF( SIG_RRC_INT_CELL_UPDATE,                       RrcIntCellUpdate                 rrcIntCellUpdate)
SIG_DEF( SIG_RRC_INT_CELL_UPDATE_OCCURRED,              EmptyIntSig                      rrcIntCellUpdateOccured)
SIG_DEF( SIG_RRC_INT_CELL_UPDATE_CONFIRM,               RrcIntCellUpdateConfirm          rrcIntCellUpdateConfirm)
SIG_DEF( SIG_RRC_INT_URA_UPDATE_CONFIRM,                RrcIntUraUpdateConfirm           rrcIntUraUpdateConfirm)
SIG_DEF( SIG_RRC_INT_UPDATE_CONFIRM_PROCESSED,          RrcIntUpdateConfirmProcessed     rrcIntUpdateConfirmProcessed)
SIG_DEF( SIG_RRC_INT_INDICATE_SIB_SCHEDULING,           RrcIntIndicateSibScheduling      rrcIntIndicateSibScheduling)
SIG_DEF( SIG_RRC_INT_AIR_SIGNAL_SEND_STATUS,            RrcIntAirSignalSendStatus        rrcIntAirSignalSendStatus)
SIG_DEF( SIG_RRC_INT_PROCESS_RX_AIR_SIGNAL,             RrcIntProcessRxAirSignal         rrcIntProcessRxAirSignal)
SIG_DEF( SIG_RRC_INT_RELEASE_RX_AIR_SIGNAL,             EmptyIntSig                      rrcIntReleaseRxAirSignal)
SIG_DEF( SIG_RRC_INT_START_PERIODIC_UPDATE,             EmptyIntSig                      rrcIntStartPeriodicUpdate)
SIG_DEF( SIG_RRC_INT_DRX_CYCLE,                         RrcIntDrxCycle                   rrcIntDrxCycle)
SIG_DEF( SIG_RRC_INT_OUT_OF_SERVICE_AREA,               EmptyIntSig                      rrcIntOutOfServiceArea)
SIG_DEF( SIG_RRC_INT_RRC_CONNECTION_SETUP,              RrcIntRrcConnectionSetup         rrcIntRrcConnectionSetup)
SIG_DEF( SIG_RRC_INT_RRC_CONN_SETUP_PROCESSED,          RrcIntRrcConnSetupProcessed      rrcIntRrcConnSetupProcessed)
SIG_DEF( SIG_RRC_INT_RRC_CONNECTION_RELEASE,            EmptyIntSig                      rrcIntRrcConnectionRelease)
SIG_DEF( SIG_RRC_INT_RRC_CONN_REL_PROCESSED,            EmptyIntSig                      rrcIntRrcConnRelProcessed)
SIG_DEF( SIG_RRC_INT_ACTIVE_SET,                        RrcIntActiveSet                  rrcIntActiveSet)
SIG_DEF( SIG_RRC_INT_RACH_MEAS_CNF,                     RrcIntRachMeasCnf                rrcIntRachMeasCnf)          //-+ CQ00210397 29-Mar-2012 +-
SIG_DEF( SIG_RRC_INT_SIB_RECEIVED,                      RrcIntSibReceived                rrcIntSibReceived)
SIG_DEF( SIG_RRC_INT_LEAVING_CELL_DCH,                  EmptyIntSig                      rrcIntLeavingCellDch)
SIG_DEF( SIG_RRC_INT_PHY_SYNC_SUCCESS,                  EmptyIntSig                      rrcIntPhySyncSuccess)
SIG_DEF( SIG_RRC_INT_PHY_SYNC_FAIL,                     EmptyIntSig                      rrcIntPhySyncFail)
SIG_DEF( SIG_RRC_INT_RADIO_LINK_FAILURE,                EmptyIntSig                      rrcIntRadioLinkFailure)
SIG_DEF( SIG_RRC_INT_PHY_TO_IDLE_STATE,                 RrcIntPhyToIdleState             rrcIntPhyToIdleState)
SIG_DEF( SIG_RRC_INT_RESET_UE_TO_IDLE_MODE,             RrcIntResetUeToIdle              rrcIntResetUeToIdleMode)
SIG_DEF( SIG_RRC_INT_SELECT_INIT_FACH_RACH,             RrcIntSelectInitFachRach         rrcIntSelectInitFachRach)
SIG_DEF( SIG_RRC_INT_INIT_FACH_RACH_SELECTED,           RrcIntInitFachRachSelected       rrcIntInitFachRachSelected)
SIG_DEF( SIG_RRC_INT_START_VALUE_TRANSMITTED,           RrcIntStartValueTransmitted      rrcIntStartValueTransmitted)
SIG_DEF( SIG_RRC_INT_CFN_INVALID_IND,                   EmptyIntSig                      rrcIntCfnInvalidInd)
SIG_DEF( SIG_RRC_INT_TM_HFN_CONFIG_IND,                 RrcIntTmHfnCfgInd                rrcIntTmHfnCfgInd)
SIG_DEF( SIG_RRC_INT_TM_HFN_IND,                        RrcIntTmHfnInd                   rrcIntTmHfnInd)
SIG_DEF( SIG_RRC_INT_DISTRIB_NEXT_STORED_SIB,           EmptyIntSig                      rrcIntDistribNextStoredSib)
SIG_DEF( SIG_RRC_INT_ALL_SCELL_SIBS_RECEIVED,           EmptyIntSig                      rrcIntAllScellSibsReceived)
SIG_DEF( SIG_RRC_INT_ALL_NCELL_SIBS_RECEIVED,           EmptyIntSig                      rrcIntAllNcellSibsReceived)
SIG_DEF( SIG_RRC_INT_ALL_SCELL_SIBS_DISTRIBUTED,        EmptyIntSig                      rrcIntAllScellSibsDistributed)
SIG_DEF( SIG_RRC_INT_NCELL_BCH_REQ,                     EmptyIntSig                      rrcIntNcellBchReq)
SIG_DEF( SIG_RRC_INT_NCELL_BCH_CNF,                     RrcIntNcellBchCnf                rrcIntNcellBchCnf)
SIG_DEF( SIG_RRC_INT_RESYNC_CELL_REQ,                   EmptyIntSig                      rrcIntResyncCellReq)
SIG_DEF( SIG_RRC_INT_RESYNC_CELL_CNF,                   RrcIntResyncCellCnf              rrcIntResyncCellCnf)
SIG_DEF( SIG_RRC_INT_RELEASE_UPDATE_CONFIRM,            EmptyIntSig                      rrcIntReleaseUpdateConfirm)
SIG_DEF( SIG_RRC_INT_RLC_UNRECOVERABLE_ERROR,           RrcIntRlcUnrecoverableError      rrcIntRlcUnrecoverableError)
SIG_DEF( SIG_RRC_INT_DISABLE_CELL_SELECTION_ON_FREQ,    RrcIntDisableCellSelectionOnFreq rrcIntDisableCellSelectionOnFreq)
SIG_DEF( SIG_RRC_INT_ENABLE_CELL_SELECTION_ON_FREQ,     EmptyIntSig                      rrcIntEnableCellSelectionOnFreq)
SIG_DEF( SIG_RRC_INT_BCCH_MOD_READ_SIBS_START,          EmptyIntSig                      rrcIntBcchModReadSibsStart)
SIG_DEF( SIG_RRC_INT_BCCH_MOD_READ_SIBS_FINISH,         EmptyIntSig                      rrcIntBcchModReadSibsFinish)
SIG_DEF( SIG_RRC_INT_SCELL_BCCH_ERROR,                  RrcIntScellBcchError             rrcIntScellBcchError)
SIG_DEF( SIG_RRC_INT_NCELL_BCCH_ERROR,                  RrcIntNcellBcchError             rrcIntNcellBcchError)
SIG_DEF( SIG_RRC_INT_ABORT_PLMN_SEARCH_REQ,             RrcIntAbortPlmnSearchReq         rrcIntAbortPlmnSearchReq)/*CQ00098682 change to RrcIntAbortPlmnSearchReq*/
//-+ UID END  CQ00210397 29-Mar-2012  +-
SIG_DEF( SIG_RRC_INT_ABORT_PLMN_SEARCH_CNF,             EmptyIntSig                      rrcIntAbortPlmnSearchCnf)
SIG_DEF( SIG_RRC_INT_NETWORK_AUTH_FAIL,                 EmptyIntSig                      rrcIntNetworkAuthFail)

#if ! defined (UPGRADE_EXCLUDE_2G)
SIG_DEF( SIG_RRC_INT_RRC_CONN_ESTABLISHED,              EmptyIntSig                      rrcIntRrcConnEstablished)
SIG_DEF( SIG_RRC_INT_RESELECT_TO_GSM,                   EmptyIntSig                      rrcIntReselectToGsm)
SIG_DEF( SIG_RRC_INT_RESELECT_TO_GSM_REQ,               RrcIntReselectToGsmReq           rrcIntReselectToGsmReq)
SIG_DEF( SIG_RRC_INT_RESELECT_TO_GSM_CNF,               RrcIntReselectToGsmCnf           rrcIntReselectToGsmCnf)
SIG_DEF( SIG_RRC_INT_RESELECT_TO_GSM_SUCCESS,           EmptyIntSig                      rrcIntReselectToGsmSuccess)
SIG_DEF( SIG_RRC_INT_RESELECT_TO_GSM_FAILURE,           RrcIntReselectToGsmFailure       rrcIntReselectToGsmFailure)
SIG_DEF( SIG_RRC_INT_RESELECT_TO_UMTS,                  EmptyIntSig                      rrcIntReselectToUmts)
SIG_DEF( SIG_RRC_INT_RESELECT_TO_UMTS_FAILURE,          EmptyIntSig                      rrcIntReselectToUmtsFailure)
SIG_DEF( SIG_RRC_INT_CPHY_SUSPEND_IND,                  EmptyIntSig                      rrcIntCphySuspedInd)
SIG_DEF( SIG_RRC_INT_CELL_SELECT_IND,                   RrcIntCellSelectInd              rrcIntCellSelectInd)
SIG_DEF( SIG_RRC_INT_OUT_OF_SYNC,                       EmptyIntSig                      rrcIntOutOfSync)
#endif      /* UPGRADE_EXCLUDE_2G */

SIG_DEF( SIG_RRC_INT_RESELECT_TO_LTE,                   EmptyIntSig                      rrcIntReselectToLte)
SIG_DEF( SIG_RRC_INT_RESELECT_TO_LTE_SUCCESS,           EmptyIntSig                      rrcIntReselectToLteSuccess)
SIG_DEF( SIG_RRC_INT_RESELECT_TO_LTE_FAILURE,           RrcIntReselectToLteFailure       rrcIntReselectToLteFailure)

SIG_DEF( SIG_RRC_INT_NO_CELL,                           EmptyIntSig                      rrcIntNoCell)

SIG_DEF( SIG_RRC_INT_ALL_SCELL_SIBS_UP_TO_DATE,         EmptyIntSig                      rrcIntAllScellSibsUpToDate)
SIG_DEF( SIG_RRC_INT_PHY_DEACTIVATION_PENDING,          EmptyIntSig                      rrcIntPhyDeactivationPending)

SIG_DEF( SIG_RRC_INT_PHY_CONFIG_FINISH,                 EmptyIntSig                      rrcIntPhyConfigFinish)
SIG_DEF( SIG_RRC_INT_INDICATE_SCELL_SIBS_TO_ACQUIRE,    RrcIntIndicateScellSibsToAcquire rrcIntIndicateScellSibsToAcquire)
SIG_DEF( SIG_RRC_INT_INDICATE_NCELL_SIBS_TO_ACQUIRE,    RrcIntIndicateNcellSibsToAcquire rrcIntIndicateNcellSibsToAcquire)
SIG_DEF( SIG_RRC_INT_TURN_OFF_SCELL_BCH_IF_ON,          EmptyIntSig                      rrcIntTurnOffScellBchIfOn)
SIG_DEF( SIG_RRC_INT_SIB7_TIMEOUT,                      EmptyIntSig                      rrcIntSib7Timout)
SIG_DEF( SIG_RRC_INT_INTER_FREQUENCY_HARD_HANDOVER,     EmptyIntSig                      rrcIntInterFrequencyHardHandover)

#if ! defined (UPGRADE_EXCLUDE_2G)
SIG_DEF( SIG_RRC_INT_RESELECT_TO_GSM_OOS,               EmptyIntSig                      rrcIntReselectToGsmOos)
#if !defined (L1_SUPPORT_GSM_FG_PLMN_SEARCH) && !defined UPGRADE_PLMS /*CQ00104943 add UPGRADE_PLMS*/
SIG_DEF( SIG_RRC_INT_SWITCH_TO_GSM_PLMN_SEARCH,     EmptyIntSig                      rrcIntSwitchToGsmPlmnSearch)
SIG_DEF( SIG_RRC_INT_SWITCH_FROM_GSM_PLMN_SEARCH,       RrcIntSwitchFromGsmPlmnSearch    rrcIntSwitchFromGsmPlmnSearch)
#endif
#endif      /* UPGRADE_EXCLUDE_2G */

#if !defined (L1_SUPPORT_GSM_FG_PLMN_SEARCH) && !defined UPGRADE_PLMS /*CQ00104943 add*/
/* CQ00054100 begin */
SIG_DEF( SIG_RRC_INT_SWITCH_TO_LTE_PLMN_SEARCH,         EmptyIntSig                      rrcIntSwitchToLTEPlmnSearch)
SIG_DEF( SIG_RRC_INT_SWITCH_FROM_LTE_PLMN_SEARCH,       RrcIntSwitchFromGsmPlmnSearch    rrcIntSwitchFromLtePlmnSearch)
/* CQ00054100 end */
#endif /*CQ00104943 add*/

SIG_DEF( SIG_RRC_INT_CPHY_DEACTIVATE_REQ,               RrcIntCphyDeactivateReq          rrcIntCphyDeactivateReq)
#if defined (UPGRADE_PLMS) //CQ00078421 begin
SIG_DEF( SIG_RRC_INT_CPHY_DEACTIVATE_CNF,               EmptyIntSig                      rrcIntCphyDeactivateCnf)
#endif //UPGRADE_PLMS  //CQ00078421 end


SIG_DEF( SIG_RRC_INT_SCELL_SIB_PER_ERROR,               EmptyIntSig                      rrcIntScellSibPerError)
SIG_DEF( SIG_RRC_INT_NCELL_SIB_PER_ERROR,               EmptyIntSig                      rrcIntNcellSibPerError)
SIG_DEF( SIG_RRC_INT_BACK_ON_CELL,                      EmptyIntSig                      rrcIntBackOnCell)
SIG_DEF( SIG_RRC_INT_POWER_OFF_FROM_OTHER_RAT,          EmptyIntSig                      rrcIntPowerOffFromOtherRat)
SIG_DEF( SIG_RRC_INT_INITIATE_ECF_MEAS_REPORT,        EmptyIntSig                      rrcIntInitiateEcfMeasReport)
SIG_DEF( SIG_RRC_INT_ABORT_FG_GSM_SEARCH,               EmptyIntSig                      rrcIntAbortFgGsmSearch)
SIG_DEF( SIG_RRC_INT_ABORT_FG_LTE_SEARCH,               EmptyIntSig                      rrcIntAbortFgLteSearch)
SIG_DEF( SIG_RRC_INT_FG_GSM_OOS_CONN_NON_DCH_COMPLETE,  EmptyIntSig                      rrcIntFgGsmOosConnNonDchComplete)
#if defined(UPGRADE_DSDS)
SIG_DEF( SIG_RRC_INT_PHY_CONFIG_FAIL,                   EmptyIntSig                      rrcIntPhyConfigFail)
#endif
SIG_DEF( SIG_RRC_INT_DMCR_BCH_READ_FINISH,              EmptyIntSig                      rrcIntDmcrBchReadFinish)/*CQ00084399 add*/

SIG_DEF( SIG_RRC_INT_ICS_EVAL_FINISHED,         RrcIntIcsEvalFinished            rrcIntIcsEvalFinished)//CQ00087173 add

// internal signals section ends here

#endif /* EXCLUDE_RRC */

#if !defined (EXCLUDE_CRLC)
#if !defined (SIGNAL_MAPPING) //2013.3.21 Jiantao modified for LTE+W+G UE PC VS project
SIG_DEF( SIG_CRLC_DUMMY = CRLC_SIGNAL_BASE,             EmptySignal                     crlc_dummy )
#endif
SIG_DEF( SIG_CRLC_CONFIG_REQ,                           CrlcConfigReq                   crlcConfigReq )
SIG_DEF( SIG_CRLC_ACTIVATE_CONFIG_REQ,                  EmptySignal                     crlcActivateConfigReq )
SIG_DEF( SIG_CRLC_STOP_REQ,                             CrlcStopReq                     crlcStopReq )
SIG_DEF( SIG_CRLC_HALT_REQ,                             CrlcHaltReq                     crlcHaltReq )
SIG_DEF( SIG_CRLC_CONTINUE_REQ,                         CrlcContinueReq                 crlcContinueReq )
SIG_DEF( SIG_CRLC_RELEASE_REQ,                          CrlcReleaseReq                  crlcReleaseReq )
SIG_DEF( SIG_CRLC_STATUS_IND,                           CrlcStatusInd                   crlcStatusInd )
SIG_DEF( SIG_CRLC_DATA_PENDING_IND,                     CrlcDataPendingInd              crlcDataPendingInd )
SIG_DEF( SIG_CRLC_CLOSE_TEST_LOOP_MODE_REQ,             CrlcCloseTestLoopReq            crlcCloseTestLoopReq )
SIG_DEF( SIG_CRLC_OPEN_TEST_LOOP_MODE_REQ,              EmptySignal                     crlcOpenTestLoopReq )
SIG_DEF( SIG_CRLC_SUSPEND_REQ,                          CrlcSuspendReq                  crlcSuspendReq )
SIG_DEF( SIG_CRLC_SUSPEND_CNF,                          CrlcSuspendCnf                  crlcSuspendCnf )
SIG_DEF( SIG_CRLC_RESUME_REQ,                           CrlcResumeReq                   crlcResumeReq )
SIG_DEF( SIG_CRLC_COUNTC_REQ,                           CrlcCountcReq                   crlcCountcReq )
SIG_DEF( SIG_CRLC_COUNTC_CNF,                           CrlcCountcCnf                   crlcCountcCnf )
SIG_DEF( SIG_CRLC_COUNTC_IND,                           CrlcCountcInd                   crlcCountcInd )
SIG_DEF( SIG_CRLC_PREPARE_CIPHER_CFG_CHANGE_REQ,        CrlcPrepareCipherCfgChangeReq   crlcPrepareCipherCfgChangeReq )
SIG_DEF( SIG_CRLC_PREPARE_CIPHER_CFG_CHANGE_CNF,        CrlcPrepareCipherCfgChangeCnf   crlcPrepareCipherCfgChangeCnf )
SIG_DEF( SIG_CRLC_ABORT_CIPHER_CONFIG_REQ,              CrlcAbortCipherConfigReq        crlcAbortCipherConfigReq )
SIG_DEF( SIG_CRLC_TEST_MODE_REQ,                        CrlcTestModeReq                 crlcTestModeReq)
SIG_DEF( SIG_CRLC_TEST_MODE_DIAGNOSTICS_RSP,            CrlcTestModeDiagnosticsRsp      crlcTestModeDiagnosticsRsp)
SIG_DEF( SIG_CRLC_DEACTIVATE_REQ,                       EmptySignal                     crlcDeactivateReq )
SIG_DEF( SIG_CRLC_RB2_SN_ACTIVATION_TIME_IND,           EmptySignal                     CrlcRb2SnActivationTimeInd)
SIG_DEF( SIG_CRLC_ENG_INFO_REQ,                         CrlcEngInfoReq                  crlcEngInfoReq )
#if defined (ENABLE_URLC_UNIT_TEST)
SIG_DEF( SIG_CRLC_UNIT_TEST_LOOPBACK_MODE_REQ,          CrlcUnitTestLoopbackModeReq     crlcUnitTestLoopbackModeReq )
SIG_DEF( SIG_CRLC_TEST_MODE_UL_SDU_SEGMENTED,           CrlcUnitTestUlAmSduSegmented    crlcUnitTestUlAmSduSegmented )
SIG_DEF( SIG_CRLC_TEST_MODE_UL_SDU_EXPIRED,             CrlcUnitTestUlAmSduExpired      crlcUnitTestUlAmSduExpired )
SIG_DEF( SIG_CRLC_TEST_MODE_UL_SDU_DELETED,             CrlcUnitTestUlAmSduDeleted      crlcUnitTestUlAmSduDeleted )
SIG_DEF( SIG_CRLC_UNIT_TEST_UPDATE_TRAFFIC_IND,         CrlcUnitTestUpdateTrafficInd    crlcUnitTestUpdateTrafficInd )
SIG_DEF( SIG_CRLC_UNIT_TEST_UL_INFO_RSP,                CrlcUnitTestUlInfoRsp           crlcUnitTestUlInfoRsp )
SIG_DEF( SIG_URLC_UNIT_TEST_PDU_LIST,          UrlcUnitTestPduList      urlcUnitTestPduList )

#else
SIG_DEF( SIG_CRLC_UNIT_TEST_LOOPBACK_MODE_REQ,          EmptySignal                     crlcUnitTestLoopbackModeReq )
SIG_DEF( SIG_CRLC_TEST_MODE_UL_SDU_SEGMENTED,           EmptySignal                     crlcUnitTestUlAmSduSegmented )
#endif /* ENABLE_URLC_UNIT_TEST */
SIG_DEF( SIG_CRLC_DL_AM_PDU_SIZE_IND,                   CrlcDlAmPduSizeInd              crlcDlAmPduSizeInd )
/*********** added 20110907 start*/

SIG_DEF( SIG_CRLC_PS_MONITOR_CONTROL_REQ,               CrlcPsMonitorControlReq         crlcPsMonitorControlReq )
SIG_DEF(SIG_CRLC_NO_PS_DATA_IND,                        CrlcNoPsDataInd                 crlcNoPsDataInd     )

/*********** added 20110907 end*/
SIG_DEF( SIG_CRLC_DEBUG_TRACING_TRIGGER_REQ,      EmptySignal           crlcDebugTracingTriggerReq )
SIG_DEF( SIG_CRLC_NOTIFY_PS_DATA_REQ,             CrlcNotifyPsDataReq         crlcNotifyPsDataReq )
SIG_DEF( SIG_CRLC_NOTIFY_PS_DATA_IND,             CrlcNotifyPsDataInd       crlcNotifyPsDataInd )
SIG_DEF( SIG_CRLC_RESELECTION_IND,                  CrlcReselectionInd        crlcReselectionInd )/*********** add*/
#if defined(RRC_UPGRADE_ENG_MODE_REDESIGN) //********** begin
SIG_DEF( SIG_CRLC_ENG_MODE_INFO_REQ,          CrlcEngModeInfoReq        crlcEngModeInfoReq )
#endif /* RRC_UPGRADE_ENG_MODE_REDESIGN : ********** end */

#endif /* EXCLUDE_CRLC */


#if !defined (EXCLUDE_URLC)
#if !defined (SIGNAL_MAPPING) //2013.3.21 Jiantao modified for LTE+W+G UE PC VS project
SIG_DEF( SIG_URLC_DUMMY = URLC_SIGNAL_BASE,             EmptySignal                     urlc_dummy )
#endif
SIG_DEF( SIG_URLC_TM_DATA_REQ,                          UrlcTmDataReq                   urlcTmDataReq )
SIG_DEF( SIG_URLC_TM_DATA_IND,                          UrlcTmDataInd                   urlcTmDataInd )
SIG_DEF( SIG_URLC_UM_DATA_REQ,                          UrlcUmDataReq                   urlcUmDataReq )
#ifdef ENABLE_OPT_DATA_FLOW_OVER_SHMEM
SIG_DEF( SIG_URLC_UM_DATA_CNF,                          UrlcUmDataCnf                   urlcUmDataCnf )
#endif /* ENABLE_OPT_DATA_FLOW_OVER_SHMEM */
SIG_DEF( SIG_URLC_UM_DATA_TRANSMITTED_IND,              UrlcUmDataTransmittedInd        urlcUmDataTransmittedInd )
SIG_DEF( SIG_URLC_UM_DATA_IND,                          UrlcUmDataInd                   urlcUmDataInd )
SIG_DEF( SIG_URLC_AM_DATA_REQ,                          UrlcAmDataReq                   urlcAmDataReq )
SIG_DEF( SIG_URLC_AM_DATA_CNF,                          UrlcAmDataCnf                   urlcAmDataCnf )
SIG_DEF( SIG_URLC_AM_DATA_IND,                          UrlcAmDataInd                   urlcAmDataInd )
SIG_DEF( SIG_URLC_AM_DL_SDU_DISCARD_IND,                UrlcAmDlSduDiscardInd           urlcAmDlSduDiscardInd )
SIG_DEF( SIG_URLC_UL_SDU_DISCARD_IND,                   UrlcUlSduDiscardInd             urlcUlSduDiscardInd )
SIG_DEF( SIG_URLC_XOFF_IND,                             UrlcXoffInd                     urlcXoffInd )
SIG_DEF( SIG_URLC_XON_IND,                              UrlcXonInd                      urlcXonInd )
SIG_DEF( SIG_URLC_UL_CONFIG_CHANGE_IND,                 UrlcUlConfigChangeInd           urlcUlConfigChangeInd )
SIG_DEF( SIG_URLC_DEBUG_MAC_DATA_REQ,                   UrlcDebugMacDataReq             urlcDebugMacDataReq )
SIG_DEF( SIG_URLC_DEBUG_MAC_DATA_IND,                   UrlcDebugMacDataInd             urlcDebugMacDataInd )
SIG_DEF( SIG_URLC_AMR_TM_DATA_REQ,                      UrlcAmrTmDataReq                urlcAmrTmDataReq )
SIG_DEF( SIG_URLC_CSD_TM_DATA_REQ,                      UrlcCsdTmDataReq                urlcCsdTmDataReq )
SIG_DEF( SIG_URLC_DL_CONFIG_CHANGE_IND,                 UrlcDlConfigChangeInd           urlcDlConfigChangeInd )
SIG_DEF( SIG_URLC_AM_DATA_RSP,                          EmptySignal                     urlcAmDataRsp )
SIG_DEF( SIG_URLC_DEBUG_PARAM,                          UrlcDebugParam                  urlcDebugParam )
SIG_DEF( SIG_URLC_DEBUG_AM_TXRX_ENTITY_IND,             UrlcDebugAmTxRxEntityInd        urlcDebugAmTxRxEntityInd )
SIG_DEF( SIG_URLC_DEBUG_UM_TX_ENTITY_IND,               UrlcDebugUmTxEntityInd          urlcDebugUmTxEntityInd )
SIG_DEF( SIG_URLC_DEBUG_UM_RX_ENTITY_IND,               UrlcDebugUmRxEntityInd          urlcDebugUmRxEntityInd )
SIG_DEF( SIG_URLC_DEBUG_TM_TX_ENTITY_IND,               UrlcDebugTmTxEntityInd          urlcDebugTmTxEntityInd )
SIG_DEF( SIG_URLC_DEBUG_TM_RX_ENTITY_IND,               UrlcDebugTmRxEntityInd          urlcDebugTmRxEntityInd )
SIG_DEF( SIG_URLC_ENG_INFO_IND,                         UrlcEngInfoInd                  urlcEngInfoInd )
SIG_DEF( SIG_URLC_ENG_MODE_IND,             UrlcEngModeInd          urlcEngModeInd )
SIG_DEF( SIG_URLC_EDCH_TTI_TIMING_IND,                  EmptySignal                     urlcEdchTtiTimingInd )
#if defined(RLC_LOW_PRIOR_OPT)
SIG_DEF( SIG_URLC_BG_IND,                               EmptySignal                     urlcBackgroundInd )
#endif
#if defined (ENABLE_UL_THROUGHPUT_IND)
SIG_DEF( SIG_URLC_DATA_UL_THROUGHPUT_IND,               UrlcDataUlTpInd                 urlcDataUlTpInd )
#endif /* ENABLE_UL_THROUGHPUT_IND */
#if defined (URL_DEBUG_HSDPA_OVER_DTC)
SIG_DEF( SIG_URLC_DEBUG_HS_DIAG_IND,                    UrlcDebugHsDiagInd              urlcDebugHsDiagInd )
#endif // URL_DEBUG_HSDPA_OVER_DTC
#if defined (URL_DEBUG_HSDPA_OVER_DTC) || defined (URL_DEBUG_EDCH_OVER_DTC)
SIG_DEF( SIG_URLC_DEBUG_ENCODED_DATA,                   UrlcDebugEncodedData            urlcDebugEncodedData )
#endif /* URL_DEBUG_HSDPA_OVER_DTC || URL_DEBUG_EDCH_OVER_DTC */
#if defined(UMTS7_PRE_R8)
SIG_DEF( SIG_URLC_PS_AM_DATA_IND,                       UrlcPsAmDataInd                 urlcPsAmDataInd )
SIG_DEF( SIG_URLC_PS_UM_DATA_IND,           UrlcPsUmDataInd         urlcPsUmDataInd )
SIG_DEF( SIG_URLC_PS_TM_DATA_IND,           UrlcPsTmDataInd         urlcPsTmDataInd )
SIG_DEF( SIG_URLC_PS_DATA_RSP,              UrlcPsDataRsp         urlcPsDataRsp )
SIG_DEF( SIG_URLC_PS_AM_DATA_CNF,           UrlcPsAmDataCnf         urlcPsAmDataCnf )
#ifdef ENABLE_OPT_DATA_FLOW_OVER_SHMEM
SIG_DEF( SIG_URLC_PS_UM_DATA_CNF,             UrlcPsUmDataCnf         urlcPsUmDataCnf )
#endif /* ENABLE_OPT_DATA_FLOW_OVER_SHMEM */

#endif
#endif /* EXCLUDE_URLC */


#if !defined (EXCLUDE_CMAC)
#if !defined (SIGNAL_MAPPING) //2013.3.21 Jiantao modified for LTE+W+G UE PC VS project
SIG_DEF( SIG_CMAC_DUMMY = CMAC_SIGNAL_BASE,             EmptySignal                     cmac_dummy )
#endif
SIG_DEF( SIG_CMAC_RNTI_CONFIG_REQ,                      CmacRNTIConfigReq               cmacRNTIConfigReq )
SIG_DEF( SIG_CMAC_CIPHERING_CONFIG_REQ,                 CmacCipheringConfigReq          cmacCipheringConfigReq )
SIG_DEF( SIG_CMAC_RACH_CONFIG_REQ,                      CmacRachConfigReq               cmacRachConfigReq )
SIG_DEF( SIG_CMAC_RACH_TX_STATUS_IND,                   CmacRachTxStatusInd             cmacRachTxStatusInd )
// This signal was removed. Definition is only for backward compatibility
SIG_DEF( SIG_CMAC_RACH_ABORT_REQ,                       EmptySignal           cmacRachAbortReq )

SIG_DEF( SIG_CMAC_UL_DEDICATED_TFCS_CONFIG_REQ,         CmacUlDedicatedTfcsConfigReq    cmacUlDedicatedTfcsConfigReq )

#if defined(PS_L2_R8_API)
SIG_DEF( SIG_CMAC_RACH_ACCESS_REQ,            CmacRachAccessReq       cmacRachAccessReq )
SIG_DEF( SIG_CMAC_RACH_ACCESS_CNF,            CmacRachAccessCnf       cmacRachAccessCnf )
SIG_DEF( SIG_CMAC_RACH_ACCESS_FIN,            EmptySignal           cmacRachAccessFin )
#endif /*PS_L2_R8_API*/

SIG_DEF( SIG_CMAC_UL_TFC_SUBSET_CONFIG_REQ,             CmacUlTfcSubsetConfigReq        cmacUlTfcSubsetConfigReq )
SIG_DEF( SIG_CMAC_CS_RAB_CONFIG_REQ,                    CmacCsRabConfigReq              cmacCsRabConfigReq )
SIG_DEF( SIG_CMAC_HANDOVER_REQ,                         CmacHandoverReq                 cmacHandoverReq )
SIG_DEF( SIG_CMAC_DL_RB_MAPPING_CONFIG_REQ,             CmacDlRbMappingConfigReq        cmacDlRbMappingConfigReq )
SIG_DEF( SIG_CMAC_UL_RB_MAPPING_CONFIG_REQ,             CmacUlRbMappingConfigReq        cmacUlRbMappingConfigReq )
SIG_DEF( SIG_CMAC_UL_TRCH_CONFIG_REQ,                   CmacUlTrChConfigReq             cmacUlTrChConfigReq )
SIG_DEF( SIG_CMAC_UL_MIN_TFC_SET_CONFIG_REQ,            CmacUlMinTfcSetConfigReq        cmacUlMinTfcSetConfigReq )
SIG_DEF( SIG_CMAC_TRAFFIC_MEASUREMENT_REQ,              CmacTrafficMeasurementReq       cmacTrafficMeasurementReq )
SIG_DEF( SIG_CMAC_TRAFFIC_MEASUREMENT_IND,              CmacTrafficMeasurementInd       cmacTrafficMeasurementInd )
SIG_DEF( SIG_CMAC_MEASUREMENT_STOP_REQ,                 CmacMeasurementStopReq          cmacMeasurementStopReq )
SIG_DEF( SIG_CMAC_QUALITY_MEASUREMENT_REQ,              CmacQualityMeasurementReq       cmacQualityMeasurementReq )
SIG_DEF( SIG_CMAC_QUALITY_MEASUREMENT_IND,              CmacQualityMeasurementInd       cmacQualityMeasurementInd )
SIG_DEF( SIG_CMAC_GET_MEASUREMENT_REQ,                  CmacGetMeasurementReq           cmacGetMeasurementReq )
SIG_DEF( SIG_CMAC_GET_MEASUREMENT_CNF,                  CmacGetMeasurementCnf           cmacGetMeasurementCnf )
SIG_DEF( SIG_CMAC_ACTIVATION_TIME_REQ,                  CmacActivationTimeReq           cmacActivationTimeReq )
SIG_DEF( SIG_CMAC_ACTIVATION_TIME_CNF,                  CmacActivationTimeCnf           cmacActivationTimeCnf )
#if defined (UPGRADE_ESCC)
SIG_DEF( SIG_CMAC_ACTIVATION_TIME_OFFSET_REQ,           CmacActivationTimeOffsetReq     cmacActivationTimeOffsetReq )
SIG_DEF( SIG_CMAC_ACTIVATION_TIME_OFFSET_CNF,           CmacActivationTimeOffsetCnf     cmacActivationTimeOffsetCnf ) 
  SIG_DEF( SIG_CMAC_ACTIVATION_TIME_OFFSET_EXPIRY,  EmptySignal           cmacActivationTimeOffsetExpiry )  
#endif//UPGRADE_ESCC
SIG_DEF( SIG_CMAC_ACTIVATION_TIME_RESET_REQ,            EmptySignal                     cmacActivationTimeResetReq )
SIG_DEF( SIG_CMAC_HFN_CONFIG_REQ,                       CmacHfnConfigReq                cmacHfnConfigReq )
SIG_DEF( SIG_CMAC_HFN_CONFIG_CNF,                       CmacHfnConfigCnf                cmacHfnConfigCnf )
SIG_DEF( SIG_CMAC_HFN_MEASUREMENT_IND,                  CmacHfnMeasurementInd           cmacHfnMeasurementInd )
SIG_DEF( SIG_CMAC_DEACTIVATE_REQ,                       CmacDeactivateReq               cmacDeactivateReq )
SIG_DEF( SIG_CMAC_TEST_MODE_REQ,                        CmacTestModeReq                 cmacTestModeReq)
/* Internal UMAC control signals, not externally visible */
SIG_DEF( SIG_CMAC_INTERNAL_RESOLVE_RB_TRCH_CONFIG,      CmacInternalResolveRbTrChConfig cmacInternalResolveRbTrChConfig)
SIG_DEF( SIG_CMAC_INTERNAL_TIMER_EXPIRY,                CmacInternalTimerExpiry         cmacInternalTimerExpiry )
SIG_DEF( SIG_CMAC_INTERNAL_MEAS_EVENT,                  CmacInternalMeasEvent           cmacInternalMeasEvent )
SIG_DEF( SIG_CMAC_INTERNAL_RECONFIG,                    CmacInternalReconfig            cmacInternalReconfig )
SIG_DEF( SIG_CMAC_INTERNAL_UL_TM_BEARERS,               CmacInternalUlTmBearers         cmacInternalUlTmBearers )
#if defined(UPGRADE_3G_HSDPA)
SIG_DEF( SIG_CMAC_HS_QUEUE_CONFIG_REQ,                  CmacHsQueueConfigReq            cmacHsQueueConfigReq )
SIG_DEF( SIG_CMAC_HS_QUEUE_RELEASE_REQ,                 CmacHsQueueReleaseReq           cmacHsQueueReleaseReq )
SIG_DEF( SIG_CMAC_EHS_QUEUE_CONFIG_REQ,         CmacEhsQueueConfigReq     cmacEhsQueueConfigReq )
SIG_DEF( SIG_CMAC_EHS_QUEUE_RELEASE_REQ,        CmacEhsQueueReleaseReq      cmacEhsQueueReleaseReq )
/* added for CQ00023022 */
SIG_DEF( SIG_CMAC_H_RNTI_CONFIG_REQ,                    CmacHRNTIConfigReq              cmacHRNTIConfigReq )
#else /* UPGRADE_3G_HSDPA */
SIG_DEF( SIG_CMAC_HS_QUEUE_CONFIG_REQ,                  EmptySignal                     cmacHsQueueConfigReq )
SIG_DEF( SIG_CMAC_HS_QUEUE_RELEASE_REQ,                 EmptySignal                     cmacHsQueueReleaseReq )
SIG_DEF( SIG_CMAC_EHS_QUEUE_CONFIG_REQ,         EmptySignal           cmacEhsQueueConfigReq )
SIG_DEF( SIG_CMAC_EHS_QUEUE_RELEASE_REQ,        EmptySignal           cmacEhsQueueReleaseReq )
#endif /* UPGRADE_3G_HSDPA */
#if defined (DEVELOPMENT_VERSION) && defined(ON_PC)
/* Internal test signals used for unit testing */
SIG_DEF( SIG_CMAC_INTERNAL_TEST_RACH_REQ,               CmacInternalTestRachReq         cmacInternalTestRachReq)
SIG_DEF( SIG_CMAC_INTERNAL_TEST_RACH_CNF,               CmacInternalTestRachCnf         cmacInternalTestRachCnf)
SIG_DEF( SIG_CMAC_INTERNAL_TEST_CM,                     CmacInternalTestCm              cmacInternalTestCm)
#else /* DEVELOPMENT_VERSION && ON_PC */
SIG_DEF( SIG_CMAC_INTERNAL_TEST_RACH_REQ,               EmptySignal                     cmacInternalTestRachReq)
SIG_DEF( SIG_CMAC_INTERNAL_TEST_RACH_CNF,               EmptySignal                     cmacInternalTestRachCnf)
SIG_DEF( SIG_CMAC_INTERNAL_TEST_CM,                     EmptySignal                     cmacInternalTestCm)
#endif /* DEVELOPMENT_VERSION && ON_PC */
#if defined (DEVELOPMENT_VERSION)
SIG_DEF( SIG_CMAC_DEBUG_TFC,                            CmacDebugTfc                    cmacDebugTfc)
#else /* DEVELOPMENT_VERSION */
SIG_DEF( SIG_CMAC_DEBUG_TFC,                            EmptySignal                     cmacDebugTfc)
#endif /* DEVELOPMENT_VERSION */
SIG_DEF( SIG_CMAC_HFN_ABORT_REQ,                        CmacHfnAbortReq                 cmacHfnAbortReq )

#if defined (UPGRADE_3G_EDCH)
SIG_DEF( SIG_CMAC_EDCH_MACD_FLOW_CONFIG_REQ,            CmacEdchMacdFlowConfigReq       cmacEdchMacdFlowConfigReq )
SIG_DEF( SIG_CMAC_EDCH_TFCS_CONFIG_REQ,                 CmacEdchTfcsConfigReq           cmacEdchTfcsConfigReq )
SIG_DEF( SIG_CMAC_EDCH_RELEASE_REQ,                     CmacEdchReleaseReq              cmacEdchReleaseReq )
#if defined (UPGRADE_UL_ECF) // ********** begin
SIG_DEF( SIG_CMAC_EDCH_RACH_CONFIG_REQ,                 CmacEdchRachConfigReq           cmacEdchRachConfigReq )
SIG_DEF( SIG_CMAC_EDCH_COMMON_EDCH_RES_RELEASE_REQ,     EmptySignal                     cmacEdchCommonEdchResReleaseReq)
SIG_DEF( SIG_CMAC_EDCH_RACH_TX_STATUS_IND ,             CmacEdchRachTxStatusInd         cmacEdchRachTxStatusInd)
SIG_DEF( SIG_CMAC_EDCH_ACCESS_REQ, 	                    EmptySignal 					cmacEdchAccessReq)
SIG_DEF( SIG_CMAC_EDCH_ACCESS_FIN,						EmptySignal 					cmacEdchAccessFin)
SIG_DEF( SIG_CMAC_EDCH_ACCESS_CNF,                      CmacEdchAccessCnf                     cmacEdchAccessCnf)
#endif // ********** end
#endif /* UPGRADE_3G_EDCH */

SIG_DEF (SIG_CMAC_UL_TRCH_COMBINATION_CONFIG_REQ,       CmacUlTrChCombinationConfigReq  cmacUlTrChCombinationConfigReq)
SIG_DEF (SIG_CMAC_DL_TRCH_COMBINATION_CONFIG_REQ,       CmacDlTrChCombinationConfigReq  cmacDlTrChCombinationConfigReq)

#if defined ON_PC
SIG_DEF (SIG_CMAC_INTERNAL_TEST_REQ,          CmacInternalTestReq       cmacInternalTestReq)
#else /*  ON_PC */
SIG_DEF( SIG_CMAC_INTERNAL_TEST_REQ,          EmptySignal           cmacInternalTestReq)
#endif //ON_PC

SIG_DEF( SIG_CMAC_RACH_FAILURES_ALERT_IND,              CmacRachFailuresAlertInd        cmacRachFailuresAlertInd ) /* ********** added */
SIG_DEF( SIG_CMAC_ENABLE_BCH_REQ,           CmacEnableBchReq        cmacEnableBchReq)
SIG_DEF( SIG_CMAC_DISABLE_BCH_REQ,            CmacDisableBchReq       cmacDisableBchReq)
SIG_DEF( SIG_CMAC_RESET_PHY_DL_REQ,           CmacResetPhyDlReq       cmacResetPhyDlReq)


#if defined (UMACE_UNIT_TEST)
SIG_DEF (SIG_UNITEST_UMACE_SESSION_INIT_IND,            UnitestUmaceSessionInitInd      unitestUmaceSessionInitInd )
SIG_DEF (SIG_UNITEST_UMACE_SESSION_RELEASED_IND,        UnitestUmaceSessionReleasedInd  unitestUmaceSessionReleasedInd )
SIG_DEF (SIG_UNITEST_UMACE_DATA_IND,                    UnitestUmaceDataInd             unitestUmaceDataInd )
SIG_DEF (SIG_UNITEST_UMACE_CHECK_OUTPUT_IND,            UnitestUmaceCheckOutputInd      unitestUmaceCheckOutputInd )
#endif /* UMACE_UNIT_TEST */

#if defined(RRC_UPGRADE_ENG_MODE_REDESIGN) //********** begin
SIG_DEF( SIG_CMAC_EDCH_ENG_MODE_INFO_REQ,       CmacEngEdchModeInfoReq      cmacEngEdchModeInfoReq )
#endif /* RRC_UPGRADE_ENG_MODE_REDESIGN : ********** end*/
SIG_DEF( SIG_CMAC_HFN_SYNC_RECOVER_REQ,                 EmptySignal                     cmacHfnSyncRecoverReq)/********** - add*/
SIG_DEF( SIG_CMAC_CHANGE_CS_HFN_EXPORTED,               CmacChangeCsHfnParams           cmacChangeCsHfnExported ) /********** - add*/

#endif /* EXCLUDE_CMAC */

#if !defined (EXCLUDE_UMAC)
#if !defined (SIGNAL_MAPPING) //2013.3.21 Jiantao modified for LTE+W+G UE PC VS project
SIG_DEF( SIG_UMAC_DUMMY = UMAC_SIGNAL_BASE,             EmptySignal                     umac_dummy )
#endif
SIG_DEF( SIG_UMAC_TRAFFIC_REQ,                          UmacTrafficReq                  umacTrafficReq )
SIG_DEF( SIG_UMAC_NO_TRAFFIC_REQ,                       UmacNoTrafficReq                umacNoTrafficReq )
SIG_DEF( SIG_UMAC_UPDATE_TRAFFIC_IND,                   UmacUpdateTrafficInd            umacUpdateTrafficInd )

SIG_DEF( SIG_UMAC_TRAFFIC_IND,                          UmacTrafficInd                  umacTrafficInd )
SIG_DEF( SIG_UMAC_NO_TRAFFIC_IND,                       EmptySignal                     umacNoTrafficInd )
SIG_DEF( SIG_UMAC_DATA_REQ,                             UmacDataReq                     umacDataReq )
SIG_DEF( SIG_UMAC_DATA_IND,                             UmacDataInd                     umacDataInd )
SIG_DEF( SIG_UMAC_TX_STATUS_IND,                        UmacTxStatusInd                 umacTxStatusInd )
SIG_DEF( SIG_UMAC_UL_CONFIG_CHANGE_IND,                 UmacUlConfigChangeInd           umacUlConfigChangeInd )
SIG_DEF( SIG_UMAC_DL_CONFIG_CHANGE_IND,                 UmacDlConfigChangeInd           umacDlConfigChangeInd )
#if defined(DEVELOPMENT_VERSION)&&!defined(NAS_UNIT_TEST)/*Modified by qinglanwang, **********, 20180306*/
        /* Used for checking activation times */
SIG_DEF( SIG_UMAC_DEBUG_PHY_FRAME_IND,                  PhyFrameInd                     umacDebugPhyFrameInd )
SIG_DEF( SIG_UMAC_DEBUG_PHY_DATA_IND,                   PhyDataInd                      umacDebugPhyDataInd )
        /* Used for checking signal contents */
SIG_DEF( SIG_UMAC_DEBUG_PHY_DATA_INFO_IND,              PhyDebugDataInfoInd             umacDebugPhyDataInfoInd )
SIG_DEF( SIG_UMAC_DEBUG_PHY_DATA_INFO_REQ,              PhyDebugDataInfoReq             umacDebugPhyDataInfoReq )
#else /*DEVELOPMENT_VERSION */
SIG_DEF( SIG_UMAC_DEBUG_PHY_FRAME_IND,                  EmptySignal                     umacDebugPhyFrameInd )
SIG_DEF( SIG_UMAC_DEBUG_PHY_DATA_IND,                   EmptySignal                     umacDebugPhyDataInd )
        /* Used for checking signal contents */
SIG_DEF( SIG_UMAC_DEBUG_PHY_DATA_INFO_IND,              EmptySignal                     umacDebugPhyDataInfoInd )
SIG_DEF( SIG_UMAC_DEBUG_PHY_DATA_INFO_REQ,              EmptySignal                     umacDebugPhyDataInfoReq )
#endif /*DEVELOPMENT_VERSION */
SIG_DEF( SIG_UMAC_PDU_LIST_INFO_IND,                    UmacDlPduListInfoInd            umacDlPduListInfoInd )
SIG_DEF( SIG_UMAC_PDU_LIST_INFO_RSP,                    UmacDlPduListInfoRsp            umacDlPduListInfoRsp )
SIG_DEF( SIG_UMAC_F8_CHAIN_COMPLETE,                    UmacF8ChainComplete             umacF8ChainComplete )
#if defined(UPGRADE_3G_HSDPA)
SIG_DEF( SIG_UMAC_HS_DATA_IND,                          UmacHsDataInd                   umacHsDataInd )
SIG_DEF( SIG_UMAC_HS_DATA_RSP,                          EmptySignal                     umacHsDataRsp )
SIG_DEF( SIG_UMAC_HS_PDU_LIST_INFO_IND,                 UmacHsPduListInfoInd            umacHsPduListInfoInd )
SIG_DEF( SIG_UMAC_HS_PDU_LIST_INFO_RSP,                 UmacHsPduListInfoInd            umacHsPduListInfoRsp )
SIG_DEF( SIG_UMAC_HS_DEBUG_PHY_HS_DATA_IND,             UmacHsDebugPhyHsDataInd         umacHsDebugPhyHsDataInd )
SIG_DEF( SIG_UMAC_EHS_DATA_IND,             UmacEhsDataInd          umacEhsDataInd )
SIG_DEF( SIG_UMAC_EHS_DATA_RSP,             EmptySignal           umacEhsDataRsp )
SIG_DEF( SIG_UMAC_EHS_PDU_LIST_INFO_IND,        UmacEhsPduListInfoInd     umacEhsPduListInfoInd )
SIG_DEF( SIG_UMAC_EHS_PDU_LIST_INFO_RSP,        EmptySignal           umacEhsPduListInfoRsp )
SIG_DEF( SIG_UMAC_EHS_REQUEST_STATUS_IND,       EmptySignal           umacEhsRequestStatusInd )
SIG_DEF( SIG_UMAC_EHS_DEBUG_PHY_HS_DATA_IND,      EmptySignal           umacEhsDebugPhyHsDataInd )
SIG_DEF( SIG_UMAC_EHS_RECOVERY_IND,           UmacEhsRecoveryInd        umacEhsRecoveryInd )
#if defined (ENABLE_UMAC_UNIT_TEST)
SIG_DEF( SIG_UMAC_EHS_UNIT_TEST_DATA_IND,       UmacEhsUnitTestDataInd      umacEhsUnitTestDataInd)
SIG_DEF( SIG_UMAC_EHS_QUEUE_RESET_IND,          UmacEhsQueueResetInd      umacEhsQueueResetInd)
#endif /*ENABLE_UMAC_UNIT_TEST*/
#else /* UPGRADE_3G_HSDPA */
SIG_DEF( SIG_UMAC_HS_DATA_IND,                          EmptySignal                     umacHsDataInd )
SIG_DEF( SIG_UMAC_HS_DATA_RSP,                          EmptySignal                     umacHsDataRsp )
SIG_DEF( SIG_UMAC_HS_PDU_LIST_INFO_IND,                 EmptySignal                     umacHsPduListInfoInd )
SIG_DEF( SIG_UMAC_HS_PDU_LIST_INFO_RSP,                 EmptySignal                     umacHsPduListInfoRsp )
SIG_DEF( SIG_UMAC_HS_DEBUG_PHY_HS_DATA_IND,             EmptySignal                     umacHsDebugPhyHsDataInd )
SIG_DEF( SIG_UMAC_EHS_DATA_IND,             EmptySignal           umacEhsDataInd )
SIG_DEF( SIG_UMAC_EHS_DATA_RSP,             EmptySignal           umacEhsDataRsp )
SIG_DEF( SIG_UMAC_EHS_PDU_LIST_INFO_IND,        EmptySignal           umacEhsPduListInfoInd )
SIG_DEF( SIG_UMAC_EHS_PDU_LIST_INFO_RSP,        EmptySignal           umacEhsPduListInfoRsp )
SIG_DEF( SIG_UMAC_EHS_REQUEST_STATUS_IND,       EmptySignal           umacEhsRequestStatusInd )
SIG_DEF( SIG_UMAC_EHS_DEBUG_PHY_HS_DATA_IND,      EmptySignal           umacEhsDebugPhyHsDataInd )
SIG_DEF( SIG_UMAC_EHS_RECOVERY_IND,           EmptySignal        umacEhsRecoveryInd )
#endif /* UPGRADE_3G_HSDPA */

#if defined(DEVELOPMENT_VERSION)
SIG_DEF( SIG_UMAC_DEBUG_TFC_SELECTION,                  UmacDebugTfcSelection           umacDebugTfcSelection )
SIG_DEF( SIG_UMAC_DEBUG_PHY_FRAME_IND_QUEUE,            UmacDebugPhyFrameIndQueue       umacDebugPhyFrameIndQueue )
SIG_DEF( SIG_UMAC_DEBUG_PARAM,                          UmacDebugParam                  umacDebugParam )
#else /*DEVELOPMENT_VERSION */
SIG_DEF( SIG_UMAC_DEBUG_TFC_SELECTION,                  EmptySignal                     umacDebugTfcSelection )
SIG_DEF( SIG_UMAC_DEBUG_PHY_FRAME_IND_QUEUE,            EmptySignal                     umacDebugPhyFrameIndQueue )
SIG_DEF( SIG_UMAC_DEBUG_PARAM,                          EmptySignal                     umacDebugParam )
#endif /*DEVELOPMENT_VERSION */
SIG_DEF( SIG_UMAC_DL_CONFIG_CHANGE_RSP,         UmacDlConfigChangeInd       umacDlConfigChangeRsp )

#if defined (ENABLE_UL_THROUGHPUT_IND)
SIG_DEF( SIG_UMAC_MAX_PS_UL_TRANSPORT_TP_IND,           UmacMaxPsUlTranspTpInd          umacMaxPsUlTranspTpInd )
#endif /* ENABLE_UL_THROUGHPUT_IND */
SIG_DEF( SIG_UMAC_PERIODICAL_DL_DEBUG,                  EmptySignal                     umacPeriodicalDlDebug )
SIG_DEF( SIG_UMACE_ENG_MODE_IND,            UmacEdchEngModeInd        umacEdchEngModeInd )
/*********** merged for FRBD 20140806 start*/
#if defined (UPGRADE_UL_ECF)
SIG_DEF( SIG_UMAC_EDCH_REQ_UL_DATA_UPDATE_START_IND,    EmptySignal                     umacEdchReqUlDataUpdateStartInd )
SIG_DEF( SIG_UMAC_EDCH_REQ_UL_DATA_UPDATE_STOP_IND,     EmptySignal                     umacEdchReqUlDataUpdateStopInd )
SIG_DEF( SIG_UMAC_EDCH_TRAFFIC_REQ,                     EmptySignal                     umacEdchTrafficReq )
#endif
/*********** merged for FRBD 20140806 end*/
#endif /* EXCLUDE_UMAC */

#if !defined (EXCLUDE_RABMRRC)
#if !defined (SIGNAL_MAPPING) //2013.3.21 Jiantao modified for LTE+W+G UE PC VS project
SIG_DEF( SIG_RABMRRC_DUMMY = RABMRRC_SIGNAL_BASE,       EmptySignal                     rabmrrc_dummy )
#endif
SIG_DEF( SIG_RABMRRC_ESTABLISH_IND,                     RabmRrcEstablishInd             rabmRrcEstablishInd )
SIG_DEF( SIG_RABMRRC_ESTABLISH_RES,                     RabmRrcEstablishRes             rabmRrcEstablishRes )
/* Added by Daniel for HSD data throughput low issue, begin */
/*SIG_DEF( SIG_RABMRRC_ESTABLISH_REJ,                     EmptySignal                     rabmRrcEstablishRej )*/
SIG_DEF( SIG_RABMRRC_ESTABLISH_REJ,                     RabmRrcEstablishRej                     rabmRrcEstablishRej )
/* Added by Daniel for HSD data throughput low issue, end */
SIG_DEF( SIG_RABMRRC_RELEASE_IND,                       RabmRrcReleaseInd               rabmRrcReleaseInd )
SIG_DEF( SIG_RABMRRC_RELEASE_RES,                       RabmRrcReleaseRes               rabmRrcReleaseRes )
SIG_DEF( SIG_RABMRRC_STATUS_IND,                        RabmRrcStatusInd                rabmRrcStatusInd )
SIG_DEF( SIG_RABMRRC_PDP_CTX_STATUS_REQ,                RabmRrcPdpCtxStatusReq          rabmRrcPdpCtxStatusReq )
#endif /* EXCLUDE_RABMRRC */

#if !defined (EXCLUDE_GMMRABM)
#if !defined (SIGNAL_MAPPING) //2013.3.21 Jiantao modified for LTE+W+G UE PC VS project
SIG_DEF( SIG_GMMRABM_DUMMY = GMMRABM_SIGNAL_BASE,       EmptySignal                     gmmrabm_dummy )
#endif
/* Added by zuohuaxu for CQ00019713 20120627, begin */
SIG_DEF( SIG_GMMRABM_FAST_DORMANCY_REQ,         GmmRabmFastDormancyReq      gmmRabmFastDormancyReq )
SIG_DEF( SIG_GMMRABM_FAST_DORMANCY_RSP,         GmmRabmFastDormancyRsp      gmmRabmFastDormancyRsp )
SIG_DEF( SIG_GMMRABM_ABORT_FAST_DORMANCY_REQ,     GmmRabmAbortFastDormancyReq   gmmRabmAbortFastDormancyReq )
/* Added by zuohuaxu for CQ00019713 20120627, end */
/* Added by flxing, for CQ00041653, 20130816, begin */
/*CQ00092930 - modify start*/
SIG_DEF( SIG_GMMRABM_IRAT_DATA_TRANSFER_REQ,            GmmRabmIratDataTransferReq      gmmRabmIratDataTransferReq)
/*CQ00092930 - modify end*/
/* Added by flxing, for CQ00041653, 20130816, end */
SIG_DEF( SIG_GMMRABM_REESTABLISH_REQ,                   GmmRabmReestablishReq           gmmRabmReestablishReq )
SIG_DEF( SIG_GMMRABM_REESTABLISH_RSP,                   EmptySignal                     gmmRabmReestablishRsp )
SIG_DEF( SIG_GMMRABM_REESTABLISH_REJ,                   GmmRabmReestablishRej           gmmRabmReestablishRej )
SIG_DEF( SIG_GMMRABM_REESTABLISH_IND,                   EmptySignal                     gmmRabmReestablishInd )
SIG_DEF( SIG_GMMRABM_RELEASE_REQ,                       EmptySignal                     gmmRabmReleaseReq )
SIG_DEF( SIG_GMMRABM_STATUS_IND,                        GmmRabmStatusInd                gmmRabmStatusInd )
#if defined(FEATURE_DETECT_DEADLOCK)
SIG_DEF( SIG_RABM_WATCH_REPORT_IND,						RabmWatchReportInd				rabmWatchReportInd )
#endif
#endif /* EXCLUDE_GMMRABM */

#if !defined (EXCLUDE_PMMSMS)
#if !defined (SIGNAL_MAPPING) //2013.3.21 Jiantao modified for LTE+W+G UE PC VS project
SIG_DEF( SIG_PMMSMS_DUMMY = PMMSMS_SIGNAL_BASE,         EmptySignal                     pmmsms_dummy )
#endif
#if defined (SUPPORT_SMS)
SIG_DEF( SIG_PMMSMS_EST_REQ,                            PmmSmsEstReq                    pmmSmsEstReq )
SIG_DEF( SIG_PMMSMS_EST_CNF,                            PmmSmsEstCnf                    pmmSmsEstCnf )
SIG_DEF( SIG_PMMSMS_ERROR_IND,                          PmmSmsErrorInd                  pmmSmsErrorInd )
SIG_DEF( SIG_PMMSMS_RESUME_IND,                         EmptySignal                     pmmSmsResumeInd )   //add by sunny on 20141202 for pagingfailure+SMS
SIG_DEF( SIG_PMMSMS_REL_REQ,                            PmmSmsRelReq                    pmmSmsRelReq )
SIG_DEF( SIG_PMMSMS_UNITDATA_REQ,                       PmmSmsUnitDataReq               pmmSmsUnitDataReq )
SIG_DEF( SIG_PMMSMS_UNITDATA_IND,                       PmmSmsUnitDataInd               pmmSmsUnitDataInd )
#endif
#endif /* EXCLUDE_PMMSMS */

#if !defined (EXCLUDE_TIRRC)
#if !defined (SIGNAL_MAPPING) //2013.3.21 Jiantao modified for LTE+W+G UE PC VS project
SIG_DEF( SIG_TI_RRC_DUMMY = TIRRC_SIGNAL_BASE,          EmptySignal                     tirrc_dummy )
#endif
SIG_DEF( SIG_TI_RRC_ACTIVATE_RB_TEST_LOOP_MODE_REQ,     EmptySignal                     tiRrcActivateRbTestLoopModeReq )
SIG_DEF( SIG_TI_RRC_CLOSE_TEST_LOOP_MODE_REQ,           TiRrcCloseTestLoopModeReq       tiRrcCloseTestLoopModeReq )
SIG_DEF( SIG_TI_RRC_CLOSE_TEST_LOOP_MODE_CNF,           TiRrcCloseTestLoopModeCnf       tiRrcCloseTestLoopModeCnf )
SIG_DEF( SIG_TI_RRC_OPEN_TEST_LOOP_MODE_REQ,            EmptySignal                     tiRrcOpenTestLoopModeReq )
SIG_DEF( SIG_TI_RRC_OPEN_TEST_LOOP_MODE_CNF,            TiRrcOpenTestLoopModeCnf        tiRrcOpenTestLoopModeCnf )
SIG_DEF( SIG_TI_RRC_DEACTIVATE_RB_TEST_LOOP_MODE_REQ,   EmptySignal                     tiRrcDeactivateRbTestLoopModeReq )
#endif /* EXCLUDE_TIRRC */

#if !defined (EXCLUDE_ALSU)
#if !defined (SIGNAL_MAPPING) //2013.3.21 Jiantao modified for LTE+W+G UE PC VS project
SIG_DEF( SIG_ALSU_DUMMY = ALSU_SIGNAL_BASE,             EmptySignal                     alsu_dummy )
#endif
/*CQ00135815, Cgliu, 2022-03-04,Begin*/
#if defined (SUPPORT_PB)
SIG_DEF( SIG_ALSU_READ_PBR_REC_REQ,                     AlsuReadPbrRecReq               alsuReadPbrRecReq )
SIG_DEF( SIG_ALSU_READ_PBR_REC_CNF,                     AlsuReadPbrRecCnf               alsuReadPbrRecCnf )
SIG_DEF( SIG_ALSU_READ_PB_DIALNUM_REQ,                  AlsuReadPbDialNumReq            alsuReadPbDialNumReq )
SIG_DEF( SIG_ALSU_READ_PB_DIALNUM_CNF,                  AlsuReadPbDialNumCnf            alsuReadPbDialNumCnf )
SIG_DEF( SIG_ALSU_WRITE_PB_DIALNUM_REQ,                 AlsuWritePbDialNumReq           alsuWritePbDialNumReq )
SIG_DEF( SIG_ALSU_WRITE_PB_DIALNUM_CNF,                 AlsuWritePbDialNumCnf           alsuWritePbDialNumCnf )
SIG_DEF( SIG_ALSU_DELETE_PB_DIALNUM_REQ,                AlsuDeletePbDialNumReq          alsuDeletePbDialNumReq )
SIG_DEF( SIG_ALSU_DELETE_PB_DIALNUM_CNF,                AlsuDeletePbDialNumCnf          alsuDeletePbDialNumCnf )
SIG_DEF( SIG_ALSU_READ_PB_CCP_REQ,                      AlsuReadPbCcpReq                alsuReadPbCcpReq )
SIG_DEF( SIG_ALSU_READ_PB_CCP_CNF,                      AlsuReadPbCcpCnf                alsuReadPbCcpCnf )
SIG_DEF( SIG_ALSU_READ_PB_EMAIL_REQ,                    AlsuReadPbEmailReq              alsuReadPbEmailReq )
SIG_DEF( SIG_ALSU_READ_PB_EMAIL_CNF,                    AlsuReadPbEmailCnf              alsuReadPbEmailCnf )
SIG_DEF( SIG_ALSU_WRITE_PB_EMAIL_REQ,                   AlsuWritePbEmailReq             alsuWritePbEmailReq )
SIG_DEF( SIG_ALSU_WRITE_PB_EMAIL_CNF,                   AlsuWritePbEmailCnf             alsuWritePbEmailCnf )
SIG_DEF( SIG_ALSU_DELETE_PB_EMAIL_REQ,                  AlsuDeletePbEmailReq            alsuDeletePbEmailReq )
SIG_DEF( SIG_ALSU_DELETE_PB_EMAIL_CNF,                  AlsuDeletePbEmailCnf            alsuDeletePbEmailCnf )
SIG_DEF( SIG_ALSU_READ_PB_ANR_REQ,                      AlsuReadPbAnrReq                alsuReadPbAnrReq )
SIG_DEF( SIG_ALSU_READ_PB_ANR_CNF,                      AlsuReadPbAnrCnf                alsuReadPbAnrCnf )
SIG_DEF( SIG_ALSU_WRITE_PB_ANR_REQ,                     AlsuWritePbAnrReq               alsuWritePbAnrReq )
SIG_DEF( SIG_ALSU_WRITE_PB_ANR_CNF,                     AlsuWritePbAnrCnf               alsuWritePbAnrCnf )
SIG_DEF( SIG_ALSU_DELETE_PB_ANR_REQ,                    AlsuDeletePbAnrReq              alsuDeletePbAnrReq )
SIG_DEF( SIG_ALSU_DELETE_PB_ANR_CNF,                    AlsuDeletePbAnrCnf              alsuDeletePbAnrCnf )
SIG_DEF( SIG_ALSU_READ_PB_AAS_REQ,                      AlsuReadPbAasReq                alsuReadPbAasReq )
SIG_DEF( SIG_ALSU_READ_PB_AAS_CNF,                      AlsuReadPbAasCnf                alsuReadPbAasCnf )
SIG_DEF( SIG_ALSU_WRITE_PB_AAS_REQ,                     AlsuWritePbAasReq               alsuWritePbAasReq )
SIG_DEF( SIG_ALSU_WRITE_PB_AAS_CNF,                     AlsuWritePbAasCnf               alsuWritePbAasCnf )
SIG_DEF( SIG_ALSU_DELETE_PB_AAS_REQ,                    AlsuDeletePbAasReq              alsuDeletePbAasReq )
SIG_DEF( SIG_ALSU_DELETE_PB_AAS_CNF,                    AlsuDeletePbAasCnf              alsuDeletePbAasCnf )
SIG_DEF( SIG_ALSU_LIST_PB_PBC_REQ,                      AlsuListPbPbcReq                alsuListPbPbcReq )
SIG_DEF( SIG_ALSU_LIST_PB_PBC_CNF,                      AlsuListPbPbcCnf                alsuListPbPbcCnf )
SIG_DEF( SIG_ALSU_READ_PB_SNE_REQ,                      AlsuReadPbSneReq                alsuReadPbSneReq )
SIG_DEF( SIG_ALSU_READ_PB_SNE_CNF,                      AlsuReadPbSneCnf                alsuReadPbSneCnf )
SIG_DEF( SIG_ALSU_WRITE_PB_SNE_REQ,                     AlsuWritePbSneReq               alsuWritePbSneReq )
SIG_DEF( SIG_ALSU_WRITE_PB_SNE_CNF,                     AlsuWritePbSneCnf               alsuWritePbSneCnf )
SIG_DEF( SIG_ALSU_DELETE_PB_SNE_REQ,                    AlsuDeletePbSneReq              alsuDeletePbSneReq )
SIG_DEF( SIG_ALSU_DELETE_PB_SNE_CNF,                    AlsuDeletePbSneCnf              alsuDeletePbSneCnf )
SIG_DEF( SIG_ALSU_READ_PB_GAS_REQ,                      AlsuReadPbGasReq                alsuReadPbGasReq )
SIG_DEF( SIG_ALSU_READ_PB_GAS_CNF,                      AlsuReadPbGasCnf                alsuReadPbGasCnf )
SIG_DEF( SIG_ALSU_WRITE_PB_GAS_REQ,                     AlsuWritePbGasReq               alsuWritePbGasReq )
SIG_DEF( SIG_ALSU_WRITE_PB_GAS_CNF,                     AlsuWritePbGasCnf               alsuWritePbGasCnf )
SIG_DEF( SIG_ALSU_DELETE_PB_GAS_REQ,                    AlsuDeletePbGasReq              alsuDeletePbGasReq )
SIG_DEF( SIG_ALSU_DELETE_PB_GAS_CNF,                    AlsuDeletePbGasCnf              alsuDeletePbGasCnf )
SIG_DEF( SIG_ALSU_LIST_PB_GAS_REQ,                      AlsuListPbGasReq                alsuListPbGasReq )
SIG_DEF( SIG_ALSU_LIST_PB_GAS_CNF,                      AlsuListPbGasCnf                alsuListPbGasCnf )
SIG_DEF( SIG_ALSU_LIST_PB_AAS_REQ,                      AlsuListPbAasReq                alsuListPbAasReq )
SIG_DEF( SIG_ALSU_LIST_PB_AAS_CNF,                      AlsuListPbAasCnf                alsuListPbAasCnf )
SIG_DEF( SIG_ALSU_READ_PB_GRP_REQ,                      AlsuReadPbGrpReq                alsuReadPbGrpReq )
SIG_DEF( SIG_ALSU_READ_PB_GRP_CNF,                      AlsuReadPbGrpCnf                alsuReadPbGrpCnf )
SIG_DEF( SIG_ALSU_WRITE_PB_GRP_REQ,                     AlsuWritePbGrpReq               alsuWritePbGrpReq )
SIG_DEF( SIG_ALSU_WRITE_PB_GRP_CNF,                     AlsuWritePbGrpCnf               alsuWritePbGrpCnf )
SIG_DEF( SIG_ALSU_DELETE_PB_GRP_REQ,                    AlsuDeletePbGrpReq              alsuDeletePbGrpReq )
SIG_DEF( SIG_ALSU_DELETE_PB_GRP_CNF,                    AlsuDeletePbGrpCnf              alsuDeletePbGrpCnf )
SIG_DEF( SIG_ALSU_GET_PBID_REQ,                         AlsuGetPbidReq                  alsuGetPbidReq )
SIG_DEF( SIG_ALSU_GET_PBID_CNF,                         AlsuGetPbidCnf                  alsuGetPbidCnf )
SIG_DEF( SIG_ALSU_READ_PB_HIDDENKEY_REQ,                AlsuReadPbHiddenKeyReq          alsuReadPbHiddenKeyReq )
SIG_DEF( SIG_ALSU_READ_PB_HIDDENKEY_CNF,                AlsuReadPbHiddenKeyCnf          alsuReadPbHiddenKeyCnf )
SIG_DEF( SIG_ALSU_READ_PB_CC_REQ,                       AlsuReadPbCcReq                 alsuReadPbCcReq )
SIG_DEF( SIG_ALSU_READ_PB_CC_CNF,                       AlsuReadPbCcCnf                 alsuReadPbCcCnf )
SIG_DEF( SIG_ALSU_PHONEBOOK_STATUS_REQ,                 AlsuPhoneBookStatusReq          alsuPhoneBookStatusReq )
SIG_DEF( SIG_ALSU_PHONEBOOK_STATUS_CNF,                 AlsuPhoneBookStatusCnf          alsuPhoneBookStatusCnf )
SIG_DEF( SIG_ALSU_HIDDENKEY_FUNCTION_REQ,               AlsuHiddenKeyFunctionReq        alsuHiddenKeyFunctionReq )
SIG_DEF( SIG_ALSU_HIDDENKEY_FUNCTION_CNF,               AlsuHiddenKeyFunctionCnf        alsuHiddenKeyFunctionCnf )
SIG_DEF( SIG_ALSU_LIST_PB_DIALNUM_EXT_REQ,              AlsuListPbDialNumExtReq         alsuListPbDialNumExtReq )
SIG_DEF( SIG_ALSU_LIST_PB_DIALNUM_EXT_CNF,              AlsuListPbDialNumExtCnf         alsuListPbDialNumExtCnf )
SIG_DEF( SIG_ALSU_READ_PB_UID_REQ,                      AlsuReadPbUidReq                alsuReadPbUidReq )
SIG_DEF( SIG_ALSU_READ_PB_UID_CNF,                      AlsuReadPbUidCnf                alsuReadPbUidCnf )
SIG_DEF( SIG_ALSU_PB_CC_CHANGE_IND,                     AlsuPbCcChangeInd               alsuPbCcChangeInd )
SIG_DEF( SIG_ALSU_PBID_CHANGE_IND,                      AlsuPbIdChangeInd               alsuPbIdChangeInd )
#endif
    /*CQ00135815, Cgliu, 2022-03-04,End  */

#endif /* EXCLUDE_ALSU */

#if defined (NODEB_GKI_API)
#if !defined (EXCLUDE_NODEBSIG)
SIG_DEF( SIG_NODEB_DUMMY = NODEB_SIGNAL_BASE,           EmptySignal                     nodeb_dummy )
SIG_DEF( SIG_NODEB_INIT_TO_DEFAULT_STATE_REQ,           EmptySignal                     nodebInitToDefaultStateReq)
SIG_DEF( SIG_NODEB_REPORT_RRC_DCCH_MESSAGE_REQ,         NodebRrcReportDcchMessageReq    nodebRrcReportDcchMessageReq)
SIG_DEF( SIG_NODEB_REPORTED_RRC_MESSAGE_IND,            NodebRrcMessageInd              nodebRrcReportedMessageInd)
SIG_DEF( SIG_NODEB_UNREPORTED_RRC_MESSAGE_IND,          NodebRrcMessageInd              nodebRrcUnreportedMessageInd)
SIG_DEF( SIG_NODEB_MENU_SPECIAL_CELL_DCH_REQ,           NodebMenuSpecialCellDchReq      nodebMenuSpecialCellDchReq)
SIG_DEF( SIG_NODEB_PARAM_CELL_STRENGTH_REQ,             NodebParamCellStrengthReq       nodebParamCellStrengthReq)
SIG_DEF( SIG_NODEB_PARAM_ACTIVE_SET_REQ,                NodebParamActiveSetReq          nodebParamActiveSetReq)
SIG_DEF( SIG_NODEB_PARAM_GSM_CELL_STRENGTH_REQ,         NodebParamGsmCellStrengthReq    nodebParamGsmCellStrengthReq)
SIG_DEF( SIG_NODEB_MENU_SPECIAL_CELL_FACH_REQ,          NodebMenuSpecialCellFachReq     nodebMenuSpecialCellFachReq)
SIG_DEF( SIG_NODEB_PARAM_CELL_CONFIG_REQ,               NodebParamCellConfigReq         nodebParamCellConfigReq)
SIG_DEF( SIG_NODEB_PARAM_CELL_SELECT_DATA_REQ,          NodebParamCellSelectDataReq     nodebParamCellSelectDataReq)
SIG_DEF( SIG_NODEB_PARAM_CSR_QUALITY_HCS_REQ,           NodebParamCsrQualityHcsReq      nodebParamCsrQualityHcsReq)
SIG_DEF( SIG_NODEB_PARAM_CELL_ACCESS_INFO_REQ,          NodebParamCellAccessInfoReq     nodebParamCellAccessInfoReq)
SIG_DEF( SIG_NODEB_PARAM_HCS_DATA_REQ,                  NodebParamHcsDataReq            nodebParamHcsDataReq)
SIG_DEF( SIG_NODEB_DEBUG_IND,                           NodebDebugInd                   nodebDebugInd)
SIG_DEF( SIG_NODEB_ENVIRONMENT_FILE_REQ,                NodebEnvironmentFileReq         nodebEnvironmentFileReq)
SIG_DEF( SIG_NODEB_CHECK_RACH_REQ,                      NodebCheckRachReq               nodebCheckRachReq)
SIG_DEF( SIG_NODEB_CHECK_RACH_SUCCESS_IND,              NodebCheckRachSuccessInd        nodebCheckRachSuccessInd)
SIG_DEF( SIG_NODEB_CHECK_RACH_FAIL_IND,                 NodebCheckRachFailInd           nodebCheckRachFailInd)
SIG_DEF( SIG_NODEB_SIB12_INTRA_MEAS_ON_RACH_REQ,        NodebSib12IntraMeasOnRachReq    nodebSib12IntraMeasOnRachReq)
SIG_DEF( SIG_NODEB_MENU_SPECIAL_CELL_PCH_REQ,           NodebMenuSpecialCellPchReq      nodebMenuSpecialCellPchReq)
SIG_DEF( SIG_NODEB_TEST_LOG_IND,                        NodebTestLogInd                 nodebTestLogInd)
SIG_DEF( SIG_NODEB_IE_CHECK_INIT_REQ,                   NodebIECheckInitReq             nodebIECheckInitReq)
SIG_DEF( SIG_NODEB_IE_CHECK_REQ,                        NodebIECheckReq                 nodebIECheckReq)
SIG_DEF( SIG_NODEB_IE_CHECK_CNF,                        EmptySignal                     nodebIECheckCnf)
SIG_DEF( SIG_NODEB_REPORT_RRC_CCCH_MESSAGE_REQ,         NodebRrcReportCcchMessageReq    nodebRrcReportCcchMessageReq)

/* Signals to control Raven */
SIG_DEF( SIG_RAVEN_ENABLE_CH1_REQ,                      EmptySignal                     ravenEnableChannel1)
SIG_DEF( SIG_RAVEN_AT_COMMAND_REQ,                      RavenAtCommandReq               ravenAtCommandReq)

/* Signal to control cell band */
SIG_DEF( SIG_NODEB_CELL_BAND_REQ,                       NodebCellBandReq                nodebCellBandReq)

#endif
#endif

#if !defined (EXCLUDE_UPSUT)
#if !defined (SIGNAL_MAPPING) //2013.3.21 Jiantao modified for LTE+W+G UE PC VS project
SIG_DEF( SIG_UPSUT_DUMMY = UPSUT_SIGNAL_BASE,           EmptySignal                     upsut_dummy)
#endif
#if defined (ENABLE_UPLANE_STATISTICS)
SIG_DEF( SIG_UPSUT_UPLANE_STATISTICS,                   UplaneStatisticsReport          uplaneStatisticsReport)
#else /* !ENABLE_UPLANE_STATISTICS */
SIG_DEF( SIG_UPSUT_UPLANE_STATISTICS,                   EmptySignal                     uplaneStatisticsReport)
#endif /* ENABLE_UPLANE_STATISTICS */
#endif /* EXCLUDE_UPSUT */

#if defined (ENABLE_PS_L2_TOOL)
SIG_DEF( SIG_L2_TOOL_START,                             EmptySignal                     l2ToolStart)
SIG_DEF( SIG_L2_TOOL_TIMER_EXPIRY,                      EmptySignal                     l2ToolTimerExpiry)
SIG_DEF( SIG_L2_TOOL_ENABLE_CFG_DATA_SENDING,           EmptySignal                     l2ToolEnableCfgDataSending)
SIG_DEF( SIG_L2_TOOL_HS_PTR_ASSIGN_REQ,                 Int8*                           l2ToolHsPointerAssign)
SIG_DEF( SIG_L2_TOOL_RLC_DATA_IND,                      L2ToolRlcDataInd                l2ToolRlcDataInd)
SIG_DEF( SIG_L2_TOOL_PLW_PHY_DATA_IND,          L2ToolPlwPhyDataInd       l2ToolPlwPhyDataInd)
#endif

/* END OF FILE */

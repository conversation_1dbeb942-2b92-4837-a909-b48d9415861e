/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to TTPCom Development
 **************************************************************************
 *   $Id: //central/main/tplgsm/utinc/ut_ucs2.h#12 $
 *   $Revision: #12 $
 *   $DateTime: 2006/10/27 16:16:26 $

 **************************************************************************
 * File Description
 * ----------------
 *   Include file for General Purpose Handlers
 **************************************************************************/

#ifndef UT_UCS2_H
#define UT_UCS2_H


/***************************************************************************
 * Nested Include Files
 **************************************************************************/

#include <system.h>

/***************************************************************************
 * Manifest Constants
 **************************************************************************/



/***************************************************************************
 * Typed Constants
 **************************************************************************/


/***************************************************************************
 * Type Definitions
 **************************************************************************/

typedef enum FormatCharacterTag
{
    UNSUPPORTED_FORMAT = 0,
    FORMAT_SPACE,
    FORMAT_PLUS,
    FORMAT_MINUS,
    FORMAT_HASH,
    FORMAT_ZERO,
    WIDTH_ASTERICS,
    WIDTH,
    PRECISION_DOT,
    CONVERSION_HALF,
    CONVERSION_LONG,
    CONVERSION_LONGLONG,
    PERCENT,
    CONVERSION_INTEGER,
    CONVERSION_UNSIGNED,
    CONVERSION_OCTAL,
    CONVERSION_HEX,
    CONVERSION_FLOAT,
    CONVERSION_CHAR,
    CONVERSION_STRING,
    CONVERSION_POINTER,
    CONVERSION_NUMBER
} FormatCharacter;



/***************************************************************************
 *  Macros
 **************************************************************************/


/***************************************************************************
 *  Function Prototypes
 **************************************************************************/

extern Int16 *
utStrcatUcs2 (Int16 *str1_p, const Int16 * str2_p);

extern SignedInt16
utSprintfUcs2 (Int16 *strDst_p, const Char *formatStr_p, ...);

extern Int16
utStrlenUcs2(const Int16  *string_p);

extern Int16 *
utStrcpyUcs2(Int16  *strDst_p, const Int16  *strSrc_p);

extern Int16 *
utExpandStrcpy(Int16  *strDst_p, const Char  *strSrc_p);

extern Int16 *
utStrncpyUcs2(Int16  *strDst_p, const Int16  *strSrc_p, const Int32 len);

extern Int16 *
utExpandStrncpy (Int16 *strDst_p, const Char *strSrc_p, const Int32 len);

extern Int8 *
utCompressStrcpy (Int8 *strDst_p, const Int16 *strSrc_p);

extern Int8 *
utCompressStrncpy (Int8 *strDst_p, const Int16 *strSrc_p, const Int32 maxLen);

extern SignedInt32
utStrcmpUcs2(const Int16  *str1_p, const Int16  *str2_p);

extern SignedInt32
utStricmpUcs2(const Int16 *str1_p, const Int16 *str2_p);

extern SignedInt32
utStrncmpUcs2(const Int16  *str1_p, const Int16  *str2_p, const Int32 len);

extern Int16
utStrCpyToUCS2 (Int16 *strDst_p, const Char *strSrc_p);

extern Int16 *
utStrchrUcs2 (const Int16 * str1_p, Int16 findChar );

extern void *
utExpandMemcpy(void * strInt16Dst_p, const void * strCharSrc_p, Int16 len );

/*-----------------------------------------------
 * Converts a UTF8 encoded string into a UNICODE encoded string.
 * If strDst_p is NULL the dstLen will be ignored, and a buffer will be
 * allocated. Else if the dstLen is to short a NULL pointer is returned.
 * The caller is responsible for freeing memory allocated by the method
 * (the returned value)
 *
 * The returned string will be zero terminated
 *-----------------------------------------------*/

extern Int16 *
utUtf8ToUcs2(const Char* strSrc_p, const Int16 srcLen, Int16* strDst_p, const Int16 dstLen);

/*-------------------------------------------------
 * Converts a UNICODE encoded string to a UTF8 encoded string.
 * If strDst_p is NULL the dstLen will be ignored, and a buffer will be
 * allocated. Else if the dstLen is to short a NULL pointer is returned.
 * The caller is responsible for freeing memory allocated by the method
 * (the returned value)
 *
 * The returned string will be zero termiated
 *------------------------------------------------*/

extern Char *
utUcs2ToUtf8(const Int16* strSrc_p, const Int16 srcLen, Char* strDst_p, const Int16 dstLen);

/*-------------------------------------------------
 * Calculates the number of ucs2 characters in the UTF-8 encoded string
 *
 *  Returns the number of ucs2 characters in the string
 *------------------------------------------------*/
extern Int16
utUtf8CharCount(const Char* strSrc_p, const Int16 srcLen);

/*-----------------------------------------------
 * Converts a UTF8 encoded string into a UNICODE encoded string.
 * strDst_p must be allocated to contain the number of chars in the
 * UTF-8 string + 1 for zero termination
 *
 * The strDst_p is returned
 *-----------------------------------------------*/
extern Int16*
utUtf8ToUcs2Ex(const Char* strSrc_p, const Int16 srcLen, Int16* strDst_p, const Int16 maxDstLen);


#endif /* UT_UCS2_H */
/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/api/inc/urlc_sig.h#4 $
 *   $Revision: #4 $
 *   $DateTime: 2007/03/30 13:53:25 $
 **************************************************************************
 * File Description:
 *
 * 3G PS Signal Data Type definitions for the URLC Interface
 **************************************************************************/

#if !defined (URLC_SIG_H)
#define       URLC_SIG_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <system.h>
#include <uas_asn.h>
#include <ups_typ.h>
#include <urltypes.h>
#include <ucstypes.h>
#if !defined (ENABLE_GRR_UNIT_TESTING) /* ********** START */
#include <ci_dev_engm.h>
#endif /* ********** END */
#if defined(UMTS7_PRE_R8)
#include <urlc_ps_sig.h>
#endif

/***************************************************************************
*   Manifest Constants
***************************************************************************/

#define URLC_MAX_CSD_SDUS_PER_TTI           4
#define URLC_MAX_CSD_SDU_SIZE_BYTES ((UCS_MAX_URLC_SDU_OFFSET_BYTES) +         \
                (UCS_RTFDP_MAX_NUM_BYTES * URLC_MAX_CSD_SDUS_PER_TTI))

/* URLC_DEBUG_LENGTH_TRUNCATED (top bit) is set to indicate that the PDU length
 * (UrlcDebugMacDataReq and UrlcDebugMacDataInd) has been truncated to exclude
 * the data, so that the length indicates the original PDU length, but only the
 * header is was logged
 */
#define URLC_DEBUG_LENGTH_TRUNCATED         0x8000

/***************************************************************************
*   Macro Functions
***************************************************************************/

/* Message Unit Identifiers */
#define URLC_MUI_MODULUS                    (USHRT_MAX + 1)
#define URLC_MUI_NEXT(mui)                  ((mui + 1) % URLC_MUI_MODULUS)
#define URLC_MUI_PREV(mui)                  ((mui + URLC_MUI_MODULUS - 1) % \
                                             URLC_MUI_MODULUS)
/* =============================================================================
 * Macros to set/get fields of UrlcDebugMacDataReq/UrlcDebugMacDataInd
 * =============================================================================
 */

/* The MSB of bearer indicates that the data within the PDU has already been
 * deciphered
 */
#define URLC_DEBUG_BEARER_UNCIPHERED        0x80
#define URLC_DEBUG_BEARER_ID                0x7F

/* Size of HFNI (note MSB of HFN is packed in liModeKsiHfn */
#define URLC_DEBUG_HFNI_OCTETS              3

/* Masks for liModeKsiHfn
 *          7       6       5       4       3       2       1       0
 *      --------|-------|-------|-------|-------|-------|-------|--------
 *      | LI15  |   Mode (0-2)  |  KSI (0-6,8-14=Key,7,15=None) |  HFN  |
 *      --------|-------|-------|-------|-------|-------|-------|--------
 */
#define URLC_DEBUG_MASK_LI                  0x80
#define URLC_DEBUG_MASK_MODE                0x60
#define URLC_DEBUG_MASK_KSI                 0x1E
#define URLC_DEBUG_MASK_HFN_25              0x01

/* Macros to get liModeKsiHfn */
#define URLC_DEBUG_GET_LI(b)                ((b & URLC_DEBUG_MASK_LI   ) >> 7)
#define URLC_DEBUG_GET_MODE(b)              ((b & URLC_DEBUG_MASK_MODE ) >> 5)
#define URLC_DEBUG_GET_KSI(b)               ((b & URLC_DEBUG_MASK_KSI  ) >> 1)
#define URLC_DEBUG_GET_HFN_25(b)            (b & URLC_DEBUG_MASK_HFN_25)

/* Get the 3 lower bytes of hfn */                                            
#define URLC_DEBUG_HFN_0(hfn)               (((hfn) >> 16) & 0xFF)
#define URLC_DEBUG_HFN_1(hfn)               (((hfn) >>  8) & 0xFF)
#define URLC_DEBUG_HFN_2(hfn)                ((hfn)        & 0xFF)

/* Macros to set liModeKsiHfn, parameters are :
 *  li : FALSE = 7 bit LIs, TRUE = 15 bit LIs
 *  m  : Mode (0=TM, 1=UM, 2=AM)
 *  k  : KSI  (0-6=CS KSI, 8-15= PS KSI, 7=Unciphered)
 *  h  : HFN
 *
 * Lint code to prevent warnings when 0 or 1 is used with the MACRO
 */
#define URLC_DEBUG_LI_MODE_KSI_HFN(li,m,k,h) /*lint -save -e(572) */ \
                                             (((li&1)<<7) | ((m&3)<<5) |    \
                                              ((k&0x0f)<<1) | ((h>>24)&1))  \
                                             /*lint -restore */

/* Maximum range of txCount VT(DAT) */
#define URLC_DEBUG_TX_STATUS_MAX_NUM        0x1F

/* Maximum number of SDUs which can be confirmed or discarded in one signal */
#define URLC_MAX_SDUS_PER_CNF               4

/* Maximum number of SDUs which can be passed to upper layers in one signal */
#define URLC_MAX_SDUS_PER_IND               8 //4 // 4

#define URLC_MAX_UL_SDUS_PER_IND        16

#if defined (UPS_ENABLE_RLC_DL_SIGNAL_FLOW_CNTRL)
/* Maximum number of signals sent by RLC to PDCP in a single TTI */
#define URL_SIG_FLOW_CONTROL_MAX_SIG        4
#endif /* UPS_ENABLE_RLC_DL_SIGNAL_FLOW_CNTRL */

/* Macros to set txStatus (txCount=n, txStatus=c) */
#define URLC_DEBUG_SET_TX_STATUS(n,c)       (((n & 0x1F) << 3) | (c & 0x7))

/* Macros to set txStatus */
#define URLC_DEBUG_GET_TX_STATUS_CAUSE(s)   (s & 0x07)
#define URLC_DEBUG_GET_TX_STATUS_COUNT(s)   (s >> 3)

#define URLC_DEBUG_MAC_DATA_REQ_MAX_DATA                                    \
    (sizeof(UrlcDebugUlPduInfo) * UPS_MAX_UL_NO_OF_TB_PER_TTI) +        \
    UUL_MAX_TB_LIST_DATA_OCTETS + (SECURITY_KEY_LENGTH * UPS_KSI_ARRAY_SIZE)

#define URLC_DEBUG_MAC_DATA_IND_MAX_DATA                                    \
    (sizeof(UrlcDebugDlPduInfo) * UPS_MAX_DL_NO_OF_TB_PER_TTI) +        \
    UDL_MAX_TB_LIST_DATA_OCTETS + (SECURITY_KEY_LENGTH * UPS_KSI_ARRAY_SIZE)

/***************************************************************************
*   Types
***************************************************************************/

/**\addtogroup 3G_RLC_PDCP
* @{*/

/**\enum UrlcUlDiscardReason
 * \brief This is used for diagnostics to indicate the reason for an uplink SDU discard.
 */
typedef enum UrlcUlDiscardReasonTag
{
    /** Discard due to timer expiry */
    URLC_DISCARD_TIMER_EXPIRY               = 0,
    /** Discard due to maximum attempts */
    URLC_DISCARD_MAX_ATTEMPTS               = 1,
    /** Discard due to RLC reset */
    URLC_DISCARD_RLC_RESET_OCCURED          = 2,
    /** Discard due to re-establish */
    URLC_DISCARD_RE_ESTABLISH               = 3,
    /** Discard due to release */
    URLC_DISCARD_RELEASE                    = 4,
    /** Discard due to buffer full */
    URLC_DISCARD_BUFFER_FULL                = 5
}
UrlcUlDiscardReason;

/**\enum UrlcSduTxStatus
 * \brief This is used for diagnostics to indicate the reason for an uplink SDU discard.
 */
typedef enum UrlcSduTxStatusTag
{
    /** SDU was not transmitted */
    URLC_SDU_UNTRANSMITTED                  = 0,
    /** SDU was partially transmitted */
    URLC_SDU_PARTIALLY_TRANSMITTED          = 1,
    /** SDU was fully transmitted */
    URLC_SDU_FULLY_TRANSMITTED              = 2
}
UrlcSduTxStatus;

/**\enum UrlcSduStatus
 * \brief This is used to indicate the status of an SDU.
 */
typedef enum UrlcSduStatusTag
{
    /** Status OK */
    URLC_SDU_STATUS_OK                      = 0,
    /** CRC eror */
    URLC_SDU_STATUS_CRC_ERROR               = 1,
    /** Sequence error */
    URLC_SDU_STATUS_SEQUENCE_ERROR          = 2,
    /** Used on synchronous TM bearers to indicate there was a failure to
        receive any data at all in layer 1 (BTFD failure, TFCI decode failure)
        due to fading or lack of layer 1 synchronisation. */
    URLC_SDU_STATUS_NO_FRAME                = 3,
     /** Illegal PDU format */
    URLC_SDU_STATUS_ILLEGAL_PDU_FORMAT      = 4
}
UrlcSduStatus;

/**\enum UrlcXoffCause
 * \brief This is used to indicate why a bearer is unable to transmit.
 */
typedef enum UrlcXoffCauseTag
{
    /** Xoff due to Buffer full */
    URLC_XOFF_BUFFER_FULL                   = 0,
    /* Xoff due to SUSPEND received */
    URLC_XOFF_SUSPENDED                     = 1,
    /* Xoff due to HALT received */
    URLC_XOFF_HALTED                        = 2,
    /* Xoff due to STOP received */
    URLC_XOFF_STOPPED                       = 3,
     /* Xoff due to RELEASE received */
    URLC_XOFF_RELEASED                      = 4
}
UrlcXoffCause;

#if !defined(UMTS7_PRE_R8)
/**\enum UrlcDataUsage
 * \brief This parameter is used to indicate how the memory of the data of PDU
was created, and therefore how it should be handled and freed by the receiving task.
 */
typedef enum UrlcDataUsageTag
{
    /** Data points to const or static memory and must not be freed. */
    URLC_DATA_USAGE_PRESERVED               = 0,
    /** Memory was allocated from the kernel memory manager. */
    URLC_DATA_USAGE_KERNEL                  = 1,
    /** Memory was allocated from the traffic memory manager. */
    URLC_DATA_USAGE_TMM                     = 2,
    /* URLC_DATA_USAGE_CS obsolete */
    /** Memory was allocated from the AMR memory manager. */
    URLC_DATA_USAGE_AMR                     = 4
}
UrlcDataUsage;
#endif
#if defined (UM_RX_OPT)
typedef enum UrlUmRxSduStateTag
{
    URLUMRX_SDU_ONGOING  				,
    URLUMRX_SDU_READY_TO_REASSEMBLE		,
    URLUMRX_SDU_OOS						,
    URLUMRX_SDU_EXCEED_MAX_SIZE			,
    URLUMRX_SDU_BUFFER_FULL				,
    URLUMRX_SDU_FORMAT_ERROR			,
    URLUMRX_SDU_EMPTY,
    URLUMRX_SDU_EBIT_READY_TO_REASSEMBLE
} UrlUmRxSduState;
#endif

#if defined (ENABLE_URLC_UNIT_TEST)

typedef enum UrlcUnitTestPduTypeTag
{
	URLC_UNIT_TEST_CONTROL_PDU = 0,
	URLC_UNIT_TEST_DATA_PDU = 1
	
}UrlcUnitTestPduType;

#endif
/**\struct UrlcSdu
 * \brief This structure contains RLC SDU information.
 */
typedef struct UrlcSduTag
{
    /** Indicates the bit offset, from the most significant bit of the first
        octet of the data block, where the most significant bit of used data
        starts, in the range 0 to 65535. */
    Int16                                   bitOffset;

    /** Indicates the size of the used data, in bits, from bitOffset, which
        accepts a range 0 to 65535, however currently the maximum known SDU
        size is 1502 octets of data plus 3 octets of header. */
    Int16                                   bitLength;

    /** Indicates how memory was allocated for this SDU data. */
    UrlcDataUsage                           dataUsage;

    /** A pointer to a TMM data block . The most significant bit of used data
        starts at bitOffset bits after the most significant bit of the first octet,
        and continues for bitLength bits. The size of the data may vary with the
        type of signal, but shall not be larger than 8192 octets (65535 bits). */
    Int8                                  * data_p;
	//MODIFICATION_5M
	Int32									dwTcpAck;
	Int32									dwTcpFlag;
/* Added by zuohuaxu for mms sending optimization 20130131, begin */
    Boolean									isMMS;
/* Added by zuohuaxu for mms sending optimization 20130131, end */

#if defined (UM_RX_OPT)
	UrlUmRxSduState							sduState;					
#endif 


#if defined (DATA_IN_SIGNAL)
    /** Used by Genie to limit the amount of data displayed by Genie;
        it does not stop Genie from displaying data before the offset.
        Its value should be set to contain the value of
        ((bitOffset+bitLength-1)/8)+1. */
    Int16                                   byteLengthPlusOffset;

    /** An array of data within the signal, when compiled for DATA_IN_SIGNAL */
    Int8                                    data [UPS_MAX_RLC_SDU_OCTETS];
#endif
}
UrlcSdu;

/**\struct UrlcCellInfo
 * \brief This structure contains cell information.
 */
typedef struct UrlcCellInfoTag
{
    /** Identifies either the SFN or CFN that this data was received in.
        Note that in the case of this message being received over several
        frames, the frame number is the first frame in which this message
        was received. */
    UFrameNumber                            frameNumber;

    /** Indicate (together with primaryScramblingCode) which cell this data
        is from and is used by RRC to determine if BCH data is from the
        serving or a neighbour cell. */
    UUARFCN                                 uarfcn_DL;

    /** Indicate (together with frameNumber) which cell this data is from and is
        used by RRC to determine if BCH data is from the serving or a neighbour cell. */
    UPrimaryScramblingCode                  primaryScramblingCode;
}
UrlcCellInfo;

/**\struct UrlcAmDataCnfSdu
 * \brief This structure contains the SDU information used in UrlcAmDataCnf signal.
 */
typedef struct UrlcAmDataCnfSduTag
{
    /** Message unit identifier used to identify this message. */
    UrlcMessageUnitIdentifier               messageUnitIdentifier;
#if defined (ENABLE_UPLANE_STATISTICS)
    /** Uplink throughput toward UMAC in bits per second */
    Int32                                   bpsTxToMac;
    /** Uplink throughput toward UTRAN in bits per second */
    Int32                                   bpsAckedByUtran;
#endif /* ENABLE_UPLANE_STATISTICS */
    /** First PDU SN*/
    Int16                                   firstSn;
    /** Last PDU SN*/
    Int16                                   lastSn;
}
UrlcAmDataCnfSdu;

/**\struct UrlcAmDataIndSdu
 * \brief This structure contains the SDU information used in UrlcAmDataInd signal.
 */
typedef struct UrlcAmDataIndSduTag
{
    /** The cell related information relating to this message. */
    UrlcCellInfo                            cellInfo;
    /** contains the octet aligned SDU data. */
    struct
    {
        /** Identifies the sequence number of the PDU containing the first segment of the SDU. */
        UrlSequenceNumber                       start;
        /** Identifies the sequence number of the PDU containing the last segment of the SDU. */
        UrlSequenceNumber                       end;
    }                                       snRange;
    UrlcSdu                                 rlcSdu;
//CQ00049795: Merge "Modify CQ00049523 start 2013.12.04"on 20131206
#if 0
#if defined(UMTS7_PRE_R8)
	UrlcPsSdu								rlcPsSdu;
	Boolean									pdcp_flag;
#endif
#endif
//CQ00049795: Merge "Modify CQ00049523 end 2013.12.04" on20131206
}
UrlcAmDataIndSdu;

/**\struct UrlcUlSduDiscard
 * \brief This structure contains the SDU information used in UrlcUlSduDiscardInd signal.
 */
typedef struct UrlcUlSduDiscardTag
{
    /** Message unit identifier used to identify this message. */
    UrlcMessageUnitIdentifier               messageUnitIdentifier;
    /** The reason why the SDU was discarded. */
    UrlcUlDiscardReason                     discardReason;
    /** Indicated the transmission status of the discarded SDU. */
    UrlcSduTxStatus                         txStatus;
    /** First PDU SN*/
    Int16                                   firstSn;
    /** Last PDU SN*/
    Int16                                   lastSn;
}
UrlcUlSduDiscard;

/**\struct UrlcTmDataReq
 * \brief This signal is sent to the URLC by the higher layers to send data for a specified
radio bearer using transparent mode.
 */
typedef struct UrlcTmDataReqTag
{
    /** Identifies the radio bearer. */
    BearerIdentity                          bearerIdentity;
    /** Contains the bit aligned SDU data. */
    UrlcSdu                                 rlcSdu;
}
UrlcTmDataReq;

/**\struct UrlcTmDataInd
 * \brief This signal is sent to the higher layers, except URRC from the URLC to indicate that
transparent mode data has been received on the specified radio bearer.
 */
typedef struct UrlcTmDataIndTag
{
    /** Identifies the radio bearer. */
    BearerIdentity                          bearerIdentity;
    /** Contains the cell related information relating to this message. */
    UrlcCellInfo                            cellInfo;
    /** Indicates the status of the rlcSdu. */
    UrlcSduStatus                           sduStatus;
    /** Indicates the type of transport channel which the SDU was received on. This is
        only relevant to BCCH data which can either be received on BCH or FACH. */
    UTransportChannelType                   trChType;
    /** Contains the bit aligned SDU data, which when the sduStatus indicates an error, may be empty. */
    UrlcSdu                                 rlcSdu;
}
UrlcTmDataInd;

/**\struct UrlcUmDataReq
 * \brief This signal is sent to the URLC by the higher layers, to send data for a specified radio
bearer using unacknowledged mode.
 */
typedef struct UrlcUmDataReqTag
{
    /** Identifies the radio bearer. */
    BearerIdentity                          bearerIdentity;
    /** Indicates if a UrlcUmDataTransmittedInd should be sent to the higher layers
        when all of the SDU data has been processed (transmitted or discarded). */
    Boolean                                 txStatusRequired;
	//MODIFICATION_5M
	Boolean                                 discardable;     
    /** Contains the SDU data to be transmitted, where its length must be octet
        aligned but its offset may be bit aligned. */
    UrlcSdu                                 rlcSdu;
}
UrlcUmDataReq;

#ifdef ENABLE_OPT_DATA_FLOW_OVER_SHMEM
/**\struct UrlcUmDataCnf
 * \brief This signal is sent to the higher layers by URLC to confirm that the
UM message identified by messageUnitIdentifier has been transmitted successfully.
 */
typedef struct UrlcUmDataCnfTag
{
    /** Identifies the radio bearer. */
    BearerIdentity                          bearerIdentity;
    /** A pointer to the transmitted SDU */
    Int8                                    * data_p;
}
UrlcUmDataCnf;
#endif /* ENABLE_OPT_DATA_FLOW_OVER_SHMEM */

/**\struct UrlcUmDataTransmittedInd
 * \brief This signal is sent to the higher layers, from the URLC, to indicate that all of the
data of an unacknowledged mode SDU, which required a transmission status, has been transmitted
or discarded (note that as UM SDU's don't have an associated message unit identifier, its
not possible to use UrlcUlSduDiscardInd).
 */
typedef struct UrlcUmDataTransmittedIndTag
{
    /** Identifies the radio bearer. */
    BearerIdentity                          bearerIdentity;
}
UrlcUmDataTransmittedInd;

/**\struct UrlcUmDataInd
 * \brief This signal is sent to the higher layers, except URRC from the URLC to indicate that
unacknowledged mode data has been received on the specified radio bearer.
 */
typedef struct UrlcUmDataIndTag
{
    /** Identifies the radio bearer. */
    BearerIdentity                          bearerIdentity;
    /** Start SFN of the received SDU */
    Int16                                   startSFN;
    /** Contains the cell related information relating to this message. */
    UrlcCellInfo                            cellInfo;
    /** Contains the octet aligned SDU data. */
    UrlcSdu                                 rlcSdu;
}
UrlcUmDataInd;

/**\struct UrlcAmDataReq
 * \brief This signal is sent to the URLC by the higher layers to send data for a
specified radio bearer using acknowledged mode.
 */
typedef struct UrlcAmDataReqTag
{
    /** Identifies the radio bearer. */
    BearerIdentity                          bearerIdentity;
    /** Used to identify this message unit. */
    UrlcMessageUnitIdentifier               messageUnitIdentifier;
    /** Indicates if the upper layer requires a UrlcAmDataCnf to confirm
        when the SDU has been transmitted successfully or a UrlcUlSduDiscardInd
        when transmission of the SDU failed. */
    Boolean                                 confirmationRequired;
	Boolean                                 discardable;  //MODIFICATION_5M 
    /** Contains the SDU data, where its length must be octet aligned but its
        offset may be bit aligned. */
    UrlcSdu                                 rlcSdu;
}
UrlcAmDataReq;

/**\struct UrlcAmDataCnf
 * \brief This signal is sent to the higher layers by URLC to confirm that the message
identified by messageUnitIdentifier has been transmitted successfully. This is only sent
if the confirmationRequired flag in the corresponding UrlcAmDataReq signal is set to TRUE.
 */
typedef struct UrlcAmDataCnfTag
{
    /** Identifies the radio bearer. */
    BearerIdentity                          bearerIdentity;
    /** Identifies the number of SDUs being confirmed in this message. */
    Int8                                    numberSdus;
    /** Array containing the SDU confirmation data. */
    UrlcAmDataCnfSdu                        sdu [URLC_MAX_SDUS_PER_CNF];
}
UrlcAmDataCnf;

/**\struct UrlcAmDataInd
 * \brief This signal is sent to the higher layers from URLC to indicate that
acknowledged mode data has been received on the specified radio bearer.
The message can carry up to URLC_MAX_SDUS_PER_IND down link SDUs. */
typedef struct UrlcAmDataIndTag
{
    /** Identifies the radio bearer. */
    BearerIdentity                          bearerIdentity;
    /** Indicates the number of SDUs carried by the message. */
    Int8                                    numberSdus;
    /** Indicates if response is required after handling the last signal SDU */
    Boolean                                 rspRequired;
    /** Contains the individual SDUs. It is an array whose length is
        configurable via the compile directive URLC_MAX_SDUS_PER_IND. */
    UrlcAmDataIndSdu                        sdu [URLC_MAX_SDUS_PER_IND];
}
UrlcAmDataInd;

/*Added start by xyzheng for opt 2012-07-04*/
#if defined(UMTS7_PRE_R8)

typedef struct UrlcPsAmDataCnfSduTag
{
    /** Message unit identifier used to identify this message. */
    UrlcMessageUnitIdentifier               messageUnitIdentifier;
} UrlcPsAmDataCnfSdu;

typedef struct UrlcPsAmDataCnfTag
{
    /** Identifies the radio bearer. */
    BearerIdentity                          bearerIdentity;
    /** Identifies the number of SDUs being confirmed in this message. */
    Int8                                    numberSdus;
    /** Array containing the SDU confirmation data. */
    UrlcPsAmDataCnfSdu                      sdu[URLC_MAX_SDUS_PER_CNF];
} UrlcPsAmDataCnf;


#ifdef ENABLE_OPT_DATA_FLOW_OVER_SHMEM
/**\struct UrlcUmDataCnf
 * \brief This signal is sent to the higher layers by URLC to confirm that the
UM message identified by messageUnitIdentifier has been transmitted successfully.
 */
typedef struct UrlcPsUmDataCnfTag
{
    /** Identifies the radio bearer. */
    BearerIdentity                          bearerIdentity;
    /** A pointer to the transmitted SDU */
    Int8                                    * data_p;
}
UrlcPsUmDataCnf;
#endif /* ENABLE_OPT_DATA_FLOW_OVER_SHMEM */

#endif

/*Added end by xyzheng for opt 2012-07-04*/


/**\struct UrlcAmDlSduDiscardInd
 * \brief This signal is sent to the higher layers by URLC to indicate that a number
of downlink AM SDU's have been discarded. */
typedef struct UrlcAmDlSduDiscardIndTag
{
    /** Identifies the radio bearer that has discarded a message. */
    BearerIdentity                          bearerIdentity;
    /** Used to indicate how many downlink SDUs were discarded, where known.
        In release 99 of 25.322, its possible to use the MRW SUFI with one
        entry to discard all SDUs represented in PDUs, up-to and including
        the specified sequence number/length indicator, which may be one or more. */
    Int8                                    numDiscardedSdus;
}
UrlcAmDlSduDiscardInd;

/**\struct UrlcUlSduDiscardInd
 * \brief This signal is sent to the higher layers by URLC to indicate that the uplink message,
identified by messageUnitIdentifier, has not been transmitted successfully and has been
discarded. */
typedef struct UrlcUlSduDiscardIndTag
{
    /** Identifies the radio bearer that has discarded a message. */
    BearerIdentity                          bearerIdentity;
    /** Indicates the number of SDUs carried by the message. */
    Int8                                    numberSdus;
    /** Contains the individual SDUs. It is an array whose length is
        configurable via the compile directive URLC_MAX_UL_SDUS_PER_IND. */
    UrlcUlSduDiscard                        sdu [URLC_MAX_UL_SDUS_PER_IND];
}
UrlcUlSduDiscardInd;

/**\struct UrlcXoffInd
 * \brief This signal is sent to the higher layers by URLC to indicate when a bearer
is unable to transmit data. */
typedef struct UrlcXoffIndTag
{
    /** Identifies the radio bearer that is unable to transmit. */
    BearerIdentity                          bearerIdentity;
    /** Identifies why a bearer is unable to transmit. */
    UrlcXoffCause                           xoffCause;
}
UrlcXoffInd;

/**\struct UrlcXonInd
 * \brief This signal is sent to the higher layers by URLC to indicate when a bearer
becomes able to transmit data again. */
typedef struct UrlcXonIndTag
{
    /** Identifies the radio bearer that is able to transmit again. */
    BearerIdentity                          bearerIdentity;
}
UrlcXonInd;

/**\struct UrlcUlConfigChangeInd
 * \brief This signal is sent to the URRC from URLC to indicate that the pending
configuration change has been executed in lower layers for the uplink. Once this
signal has been received, URRC can send the configuration confirm signal. */
typedef struct UrlcUlConfigChangeIndTag
{
    /** Identifies the uplink connected frame number and is in the range 0 to 255. */
    UActivationTime                 activateAtCfn;
    /** Indicates the full uplink COUNT-C including the HFN at the activation time. */
    UmacTmCountcInfo                ulHfnCurrent;
}
UrlcUlConfigChangeInd;

/**\struct UrlcDlConfigChangeInd
 * \brief This signal is sent to the URRC from URLC to indicate that the pending
configuration change has been executed in lower layers for the downlink.
Once this signal has been received, URRC can send the configuration confirm signal. */
typedef struct UrlcDlConfigChangeIndTag
{
    /** Identifies the downlink connected frame number and is in the range 0 to 255. */
    UActivationTime                 activateAtCfn;
    /** Indicates the full downlink COUNT-C including the HFN at the activation time. */
    UmacTmCountcInfo                dlHfnCurrent;
}
UrlcDlConfigChangeInd;

/**\struct UrlcAmDataRsp
 * \brief This signal is sent to the URLC by the UPDCP to indicate that previous downlink SDUs were already handled.
 This is done for downlink SDUs flow control between URLC and UPDCP. URLC set the flow control state accordingly.
This signal has no parameters.
 */
typedef EmptySignal UrlcAmDataRsp;

/**\struct UrlcEngInfoInd
 * \brief This signal is sent to the AL from URLC to indicate engineering information */
typedef struct UrlcEngInfoIndTag
{
    /** Uplink throughput in bytes per second */
    Int32                   ulThroughput;
    /** Downlink throughput in bytes per second */
    Int32                   dlThroughput;
}
UrlcEngInfoInd;

#if !defined (ON_PC)

typedef CiDevRlcUlStatsType					UrlcEngModeUlStatistics;
typedef CiDevRlcDlStatsType					UrlcEngModeDlStatistics;
typedef CiDevWBUmtsHspaRlcStatisticsEng_RLC UrlcEngModeStatistics;
typedef CiDevWBRlcResetEng_RLC				UrlcEngModeReset;
typedef CiDevPrimSetExtEngmodeRepOptReq	    UrlcEngModeSetRepOpt;

#endif /* !ON_PC */

#if !defined (ENABLE_GRR_UNIT_TESTING) /* ********** START */
typedef CiDevCommonEngmodeInfoInd 			UrlcEngModeInd;
typedef CiDevCommonEngmodeInfoInd			UmacEdchEngModeInd;
#endif /* ********** END */

#if defined (ENABLE_UL_THROUGHPUT_IND)
#define MAX_URLC_PDP_CTX_NUM     7

/**\struct UrlcUlTpInfo
 * \brief This signal contains the information sent in UrlcDataUlTpInd signal. */
typedef struct UrlcUlTpInfoTag
{
    /** Identifies the radio bearer of the uplink throughput report. */
    BearerIdentity          bearerIdentity;
    /** Uplink throughput in bytes/sec since last report */
    Int32                   ulThroughput;
    /** Number of bytes of pending uplink SDUs */
    Int32                   pendTxBytes;
}
UrlcUlTpInfo;

/**\struct UrlcDataUlTpInd
 * \brief This signal is sent to the AL from URLC to indicate engineering information */
typedef struct UrlcDataULTpIndTag
{
    /** Number of PDP Contexts in the report (max is 7) */
    Int8                    pdpCtxNumber;
    /** Maximum allowed radio bearer uplink throughput */
    Int32                   maxLimitPerRB;
    /** Radio bearers uplink throughput information */
    UrlcUlTpInfo            UlTpInfo[MAX_URLC_PDP_CTX_NUM];
}
UrlcDataUlTpInd;
#endif /* ENABLE_UL_THROUGHPUT_IND */

#if defined (ENABLE_URLC_UNIT_TEST)

typedef struct UrlcUnitTestControlPduTag
{
	//Status pdu only
	Int8 subType;//Currently supports only POLL_SUFI = 1000 and MRW_SUFI = 0110
	Int8 length;
	Int16 sn[15];//POLL_SN or MRW_SN
	Int8 nLength;
}
UrlcUnitTestControlPdu;


typedef struct UrlcUnitTestDataPduTag
{
	Int8 sn; /*SN field of the pdu*/
	Int8 poll; /*Poll field of the pdu*/
	Int8 numberOfSdus; /*Number of RLC SDUs within this RLC PDU*/
	SignedInt16  firstSduDataOffset; /* in case the first sdu in this pdu is not the the start of an RLC SDU, this would indicate it's offset in bytes)*/
	Bool lastSduSegmented; /* Indicates that the last sdu within this pdu is not the end of a full RLC SDU */
	Int16 sduLength[10];  /*The length in bytes of each rlc sdu*/
}
UrlcUnitTestDataPdu;


typedef struct UrlcUnitTestPduTag
{
	BearerIdentity rb; /* RB of this RLC PDU*/
	
	Int8 numberSegments; /* number of descriptors this pdu allocates*/
	Int16 segmentOffset[10]; /* the offset within the pdu, where segmentation takes place*/
	
	UrlcUnitTestPduType dataOrControl; //True if Data,  otherwise Control
	union
    {
		
		UrlcUnitTestControlPdu	controlPdu;
		
		UrlcUnitTestDataPdu		dataPdu;
    }
    choice;
}
UrlcUnitTestPdu;


typedef struct UrlcUnitTestPduListTag
{
	Int8 numberOfPdus; /* Total Number of DTC Descriptors*/
	UrlcUnitTestPdu rlcPdus[20]; /* A list of RLC PDUs*/
}
UrlcUnitTestPduList;



#endif //ENABLE_URLC_UNIT_TEST
/**\struct UrlcDebugUlPduInfo
 * \brief This struct included in the signal signal sent from URLC to the RLC decoder and
 contains uplink debug information.
 */
/* Debug signals are sent to RLC decoder are packed to minimise their size :
 *
 *  Signal structure :
 *
 *      Number of transport blocks
 *      Byte length (of data)
 *      Array of "Number of transport blocks" PDU info's
 *      Array of PDU data
 *      If any PDU is ciphered
 *          Array of 14 cipher keys (ksi's 1-14)
 *      Endif
 *
 *  Uplink PDU info :
 *
 *                                     Bit
 *          7       6       5       4       3       2       1       0
 *      --------|-------|-------|-------|-------|-------|-------|--------
 *      |                                                               |
 *      -                   Bit Offset (0 - 27952)                      -
 *      |                                                               |
 *      --------|-------|-------|-------|-------|-------|-------|--------
 *      |                                                               |
 *      -                   Bit Length  (0 - 27952)                     -
 *      |                                                               |
 *      --------|-------|-------|-------|-------|-------|-------|--------
 *      |Unciphr|               Bearer (0-35, 7F=Unknown)               |
 *      --------|-------|-------|-------|-------|-------|-------|--------
 *      | LI15  |   Mode (0-2)  | KSI (0=6,8-14=Key, 7,15=None) |       |
 *      --------|-------|-------|-------|-------|-------|-------|       -
 *      |                                                               |
 *      -                                                               -
 *      |                  HFN  (UM=25 bits, AM=20 bits)                |
 *      -                                                               -
 *      |                                                               |
 *      --------|-------|-------|-------|-------|-------|-------|--------
 *      |           TX count (1-30, 31=31..128) |       TX Cause        |
 *      --------|-------|-------|-------|-------|-------|-------|--------
 */
typedef struct UrlcDebugUlPduInfoTag
{
    /** Offset of current PDU data, from last PDU data. Used for byte alignment. */
    Int16                       bitOffset;
    /** Indicates the size of the used data, in bits.
        MSB indicates that the length has been truncated to exclude data,
        use URLC_DEBUG_LENGTH_TRUNCATED */
    Int16                       bitLength;
    /** Identifies the radio bearer.
        MSB indicates data has already been deciphered */
    Int8                        bearerIdentity;
    /** LI mode, KSI and HFN. */
    Int8                        liModeKsiHfn;
    /** Array of hyper frame numbers */
    Int8                        hfn [URLC_DEBUG_HFNI_OCTETS];
    /** Debug status */
    Int8                        status;
} UrlcDebugUlPduInfo;

/**\struct UrlcDebugMacDataReq
 * \brief This signal is sent from URLC to the RLC decoder and contains uplink debug information.
 */
typedef struct UrlcDebugMacDataReqTag
{
    /** Number of transport blocks */
    Int8                        numberOfTransportBlocks;
    /** Genie length association */
    Int16                       byteLength;
    /** Contiguous byte array containing: \n
            -Array of "Number of transport blocks" PDU info's. \n
            -Data array \n
            -If any PDU is ciphered: Array of 14 cipher keys (ksi's 1-14) */
    Int8                        data [URLC_DEBUG_MAC_DATA_REQ_MAX_DATA];
} UrlcDebugMacDataReq;

#if defined (URL_DEBUG_HSDPA_OVER_DTC) || defined (URL_DEBUG_EDCH_OVER_DTC)
/**\struct UrlcDebugEncodedData
 * \brief This signal is sent from URLC to the RLC decoder and contains debug information.
 */
typedef struct UrlcDebugEncodedDataTag
{
    /** Total data length in bytes */
    Int16                   totalByteLength;
    /** Data content and meta data.
        Actual size is determined at runtime and is stated in totalByteLength. */
    Int8                    dataAndMetadata[2];
}UrlcDebugEncodedData;
#endif // URL_DEBUG_HSDPA_OVER_DTC || URL_DEBUG_EDCH_OVER_DTC

/**\enum UrlAmTxCause
 * \brief indicates why UL PDUs were transmitted, but as they must bepacked into 3
 bits of UrlcDebugUlPduInfo status, as data and status PDUs are normally mutually
 exclusive (except piggyback status), the 3 least significant bits of data and
 status causes overlay each other : \n
  - Data   causes : 0000 - 0111 \n
  - Status causes : 1000 - 1111
 */
typedef enum UrlAmTxCauseTag
{
    /** Data PDU transmit causes */
    URL_AM_TXD_CAUSE_UNTRANSMITTED          = 0,    /**< Transmitted for first time */
    URL_AM_TXD_CAUSE_NACK                   = 1,    /**< NACK received */
    URL_AM_TXD_CAUSE_TIMER_POLL             = 2,    /**< Poll timer expiry */
    URL_AM_TXD_CAUSE_TIMER_PERIODIC_POLL    = 3,    /**< Periocic poll timer expiry */
    URL_AM_TXD_CAUSE_PROHIBITED_POLL        = 4,    /**< Poll trigger was deferred
                                                         during poll prohibit timer */
    URL_AM_TXD_CAUSE_UNUSED_5               = 5,    /**< Not in used */
    URL_AM_TXD_CAUSE_UNUSED_6               = 6,    /**< Not in used */
    URL_AM_TXD_CAUSE_UNUSED_7               = 7,    /**< Not in used */

    /** Status PDU transmit causes */
    URL_AM_TXS_CAUSE_TIMER_PERIODIC_STATUS  = 8,    /**< Periodic status timer */
    URL_AM_TXS_CAUSE_MISSING_PDU            = 9,    /**< Missing PDU detected */
    URL_AM_TXS_CAUSE_POLLED                 = 10,   /**< DL poll detected */
    URL_AM_TXS_CAUSE_SDU_DISCARD            = 11,   /**< UL SDU discarded */
    URL_AM_TXS_CAUSE_WINDOW                 = 12,   /**< UL Window SUFI */
    URL_AM_TXS_CAUSE_RESET                  = 13,   /**< UL RESET/RESET_ACK PDU */
    URL_AM_TXS_CAUSE_PADDING                = 14,   /**< UL Padding control PDU */
    URL_AM_TXS_CAUSE_MAC_HS_REQ             = 15,   /**< DL UmacHsRequestStatusInd received */

    /** Values MUST NOT exceed range :\n
        - Data   : 0000 - 0111 \n
        - Status : 1000 - 1111 */
    URL_AM_TX_CAUSE_ILLEGAL                 = 16
} UrlAmTxCause;

/**\struct UrlcDebugUlPduInfo
 * \brief This struct included in the signal sent from URLC to the RLC decoder and
 contains downlink debug information.
 */
/*  Downlink PDU info :
 *
 *                                     Bit
 *          7       6       5       4       3       2       1       0
 *      --------|-------|-------|-------|-------|-------|-------|--------
 *      |                                                               |
 *      -                   Bit Offset (0 - 27952)                      -
 *      |                                                               |
 *      --------|-------|-------|-------|-------|-------|-------|--------
 *      |                                                               |
 *      -                   Bit Length  (0 - 27952)                     -
 *      |                                                               |
 *      --------|-------|-------|-------|-------|-------|-------|--------
 *      |Unciphr|               Bearer (0-35, 7F=Unknown)               |
 *      --------|-------|-------|-------|-------|-------|-------|--------
 *      | LI15  |   Mode (0-2)  | KSI (0=6,8-14=Key, 7,15=None) |       |
 *      --------|-------|-------|-------|-------|-------|-------|       -
 *      |                                                               |
 *      -                                                               -
 *      |                              HFN                              |
 *      -                                                               -
 *      |                                                               |
 *      --------|-------|-------|-------|-------|-------|-------|--------
 *      |                            Status                             |
 *      --------|-------|-------|-------|-------|-------|-------|--------
 */
typedef struct UrlcDebugDlPduInfoTag
{
    /** Offset of current PDU data, from last PDU data. Used for byte alignment. */
    Int16                       bitOffset;
    /** Indicates the size of the used data, in bits.
        MSB indicates that the length has been truncated to exclude data,
        use URLC_DEBUG_LENGTH_TRUNCATED */
    Int16                       bitLength;
    /** Identifies the radio bearer.
        MSB indicates data has already been deciphered */
    Int8                        bearerIdentity;
    /** LI mode, KSI and HFN. */
    Int8                        liModeKsiHfn;
    /** Array of hyper frame numbers */
    Int8                        hfn [URLC_DEBUG_HFNI_OCTETS];
    /** Debug status */
    Int8                        status;
} UrlcDebugDlPduInfo;

/**\struct UrlcDebugMacDataReq
 * \brief This signal is sent from URLC to the RLC decoder and contains downlink debug information.
 */
typedef struct UrlcDebugMacDataIndTag
{
    /** Number of transport blocks */
    Int8                        numberOfTransportBlocks;
    /** Genie length association */
    Int16                       byteLength;
    /** Contiguous array of PDU's, array of security keys, array of data */
    Int8                        data [URLC_DEBUG_MAC_DATA_IND_MAX_DATA];
} UrlcDebugMacDataInd;

/**\enum UrlcDebugDlPduStatus
 * \brief This is used to identify the downlink PDUs status.
 */
typedef enum UrlcDebugDlPduStatusTag
{
    URLC_DEBUG_DL_OK                        = UDL_PDU_STATUS_CRC_OK,        /**< Downlink PDU OK */
    URLC_DEBUG_DL_CRC_ERROR                 = UDL_PDU_STATUS_CRC_ERROR,     /**< CRC error */
    URLC_DEBUG_DL_MAC_ERROR                 = UDL_PDU_STATUS_MAC_ERROR,     /**< MAC error */
    URLC_DEBUG_DL_NO_FRAME                  = UDL_PDU_STATUS_MAC_NO_FRAME,  /**< MAC no frame */
    URLC_DEBUG_DL_INAVLID_BEARER            = 4,    /**< Invalid radio bearer */
    URLC_DEBUG_DL_FORMAT_ERROR              = 5,    /**< Format error */
    URLC_DEBUG_DL_SEQUENCE_ERROR            = 6,    /**< Sequence number error */
    URLC_DEBUG_DL_STOPPED                   = 7,    /**< PDU received in STOPPED state */
    URLC_DEBUG_DL_HALTED                    = 8,    /**< PDU received in haltED state */
    URLC_DEBUG_DL_WRONG_STATE               = 9,    /**< PDU received in WRONG state */
    URLC_DEBUG_DL_OUTSIDE_WINDOW            = 10,   /**< PDU sequence number is outside the receiver window */
    URLC_DEBUG_DL_DUPLICATE                 = 11,   /**< Duplicate PDU */
    URLC_DEBUG_DL_UNKNOWN_SUFI              = 12,   /**< PDU contains Unknown SUFI */
    URLC_DEBUG_DL_NACK_ERROR                = 13,   /**< NACK error */
    URLC_DEBUG_DL_INCONSISTENT_STATUS       = 14,   /**< Inconsistent status */
    URLC_DEBUG_DL_TIMER_NOT_RUNNING         = 15,   /**< Timer is not running */
    URLC_DEBUG_DL_OBSOLETE_SUFI             = 16,   /**< Obsolete SUFI */
    URLC_DEBUG_DL_LOOP_NOT_CLOSED           = 17,   /**< Loop is not closed */
    URLC_DEBUG_DL_PDU_EMPTY                 = 18,   /**< PDU is empty */
    URLC_DEBUG_DL_BUFFER_FULL               = 19,   /**< Buffer is full */
    URLC_DEBUG_DL_RESET_DISCARD             = 20,   /**< RESET discard */
    URLC_DEBUG_DL_MAX_SDU_EXCEEDED          = 21,   /**< Maximum SDU size exceeded */
    URLC_DEBUG_DL_PDU_SIZE_CHANGED          = 22,   /**< PDU size was changed */
    URLC_DEBUG_DL_DELETED_SDU_NODE          = 23,   /**< Delete SDU node */
    URLC_DEBUG_DL_DELETED_OOS_PDU           = 24,   /**< Delete out of sequence PDU */
    URLC_DEBUG_DL_DELETED_CTRL_PDU_NODE     = 25,   /**< Delete control PDU node */
    URLC_DEBUG_DL_UM_OUT_OF_SEQUENCE   		= 26,   /**< Delete control PDU node */
    URLC_DEBUG_DL_NO_SDU_BUFFER     		= 27,   /**< Delete control PDU node */
    URLC_DEBUG_DL_DELETED_AFTER_RESET       = 28,   /**< Delete control PDU node */

    URLC_DEBUG_DL_BAD_ACK_SUFI              = 29,   /**< Bad ACK SUFI */    //-+ ********** 9-Nov-2010 +-
    URLC_DEBUG_DL_BAD_LIST_SUFI             = 30,   /**< Bad list SUFI */   //-+ ********** 9-Nov-2010 +-
    URLC_DEBUG_DL_BAD_BITMAP_SUFI_START_BEFORE_VTA  = 31,   /**< Bitmap SUFI start before VTA */
    URLC_DEBUG_DL_BAD_BITMAP_SUFI_EXTEND_BEYOND_VTS = 32,   /**< Bitmap SUFI extend beyond VTS */
    URLC_DEBUG_DL_BAD_RLIST_SUFI_INVALID_FSN        = 33,   /**< Rlist SUFI contains invalid FSN */
    URLC_DEBUG_DL_BAD_RLIST_SUFI_INVALID_SN         = 34,   /**< Rlist SUFI contains invalid SN */  //-+ ********** 9-Nov-2010 +-
                                                                               
    URLC_DEBUG_DL_MRW_STORED                = 0xFD,  /**< Move receiver window stored. Never passed to RLC decoder */
    URLC_DEBUG_DL_MRW_RETX                  = 0xFE,  /**< Move receiver window retransmitted. Never passed to RLC decoder */
    URLC_DEBUG_DL_MRW_RETX_AND_STORED       = 0xFF   /**< Move receiver window retransmitted and stored. Never passed to RLCDec */
} UrlcDebugDlPduStatus;

/**\enum UrlcDebugDlPduStatus
 * \brief This is used to identify the debug parameters mode.
 */
typedef enum
{
    URLC_DEBUG_PARAM_NULL                    = 0,
    URLC_DEBUG_PARAM_SENDMRW_SET_UL_CONFIG   = 1,
    URLC_DEBUG_PARAM_SENDMRW_SEND_MRW        = 2,
    URLC_DEBUG_PARAM_SENDMRW_IDLE_TO_PENDING = 3,  /* Temporary for HD53085 */
    URLC_DEBUG_PARAM_SENDMRW_STATE_TRANSMIT  = 4,  /* Temporary for HD53085 */
    URLC_DEBUG_PARAM_SENDMRW_REASSIGNED_MRW  = 5,  /* Temporary for HD53085 */
    URLC_DEBUG_PARAM_SENDMRW_RETRY_STATUS    = 6,  /* Temporary for HD53085 */
    URLC_DEBUG_PARAM_SENDMRW_TIMEOUT_MRW     = 7,  /* Temporary for HD53085 */
    URLC_DEBUG_PARAM_SENDMRW_DEALLOC_MRW     = 8,  /* Temporary for HD53085 */
    URLC_DEBUG_PARAM_DL_MAX_SDU_EXCEEDED     = 9

}UrlcDebugParamMode;

/**\struct UrlcDebugParam
 * \brief This struct contains the debug parameters.
 */
typedef struct UrlcDebugParamTag
{
    /** identify the debug parameters mode. */
    UrlcDebugParamMode                mode;
    Int32                             param1;
    Int32                             param2;
    Int32                             param3;
    Int32                             param4;
}UrlcDebugParam;
/*@}*/



/**\struct UrlcDebugHsDiagInd
 * \brief This signal is sent from URLC to the RLC decoder and contains the HS debug parameters.
 */
typedef struct UrlcDebugHsDiagIndTag
{
    Int8        *poolAddress;
    Int16       debugMsgByteLength;
    Int32       numOfDecreasedBytes;
} UrlcDebugHsDiagInd;

/** \addtogroup 3G_CSDI_CSRLC
 * @{
 */

/**\struct UrlcAmrTmDataReq
* \brief   A signal contains the data(AUDIO) to be processed by CSDI 
* 
* \par CSDI ==> URLC
*/

typedef struct UrlcAmrTmDataReqTag
{
    /**\struct classA
    * \brief   The Class A bits description
    */
    struct
    {
        /** Identifies the radio bearer corresponding to the data flow in this circuit. for Class A */
        BearerIdentity                      bearerIdentity; /* RB_INVALID=Unused */
        /** The length in bits for class A */
        Int16                               bitLength;      /* 0=Purge buffer */
        /** Class C data - note the length is the maximum of AMR WBAMR used bits for Class A */
        Int8                                data [UCS_AMR_MAX_NUM_A_BYTES];
    } classA;

    /**\struct classB
    * \brief   The Class B bits description
    */
    struct
    {
        /** identifies the radio bearer corresponding to the data flow in this circuit. for Class B */
        BearerIdentity                      bearerIdentity; /* RB_INVALID=Unused */
        /** The length in bits for class B */
        Int16                               bitLength;      /* 0=Purge buffer */
        /** Class C data - note the length is the maximum of AMR WBAMR used bits for Class B */
        Int8                                data [UCS_AMR_MAX_NUM_B_BYTES];
    } classB;

    /**\struct classC
    * \brief   The Class C bits description
    */
    struct
    {
        /** Identifies the radio bearer corresponding to the data flow in this circuit. for Class C */
        BearerIdentity                      bearerIdentity; /* RB_INVALID=Unused */
        /** The length in bits for class C */
        Int16                               bitLength;      /* 0=Purge buffer */
        /** Class C data - note the length is the maximum of AMR WBAMR used bits for Class C */
        Int8                                data [UCS_AMR_MAX_NUM_C_BYTES];
    } classC;
}
UrlcAmrTmDataReq;

/**\struct UrlcCsdSdu
* \brief  A data structure contains metadata describes the payload data
* 
* \par  CSDI ==> URLC
*/
typedef struct UrlcCsdSduTag
{
    Int16                                   bitOffset; /**< The bit the data is started */
    Int16                                   bitLength; /**< The data length in bits */

    /* When not DATA_IN_SIGNAL these parameters are only used internally */
    UrlcDataUsage                           dataUsage; /**< Type of data may be Preserve,Kernel,TMM, AMR */
    Int8                                  * data_p; /** the data address */

#if defined (DATA_IN_SIGNAL)
    /* WARNING: As the data array is only allocated to be large enough to hold
     *          the specific SDU, it must be the last entry in the structure
     */
    Int8                                    data [URLC_MAX_CSD_SDU_SIZE_BYTES];
#endif /* DATA_IN_SIGNAL */
}
UrlcCsdSdu;

/**\struct UrlcCsdTmDataReq
* \brief  A signal contains CSD data recieved from CSD
* 
* \par  CSDI ==> URLC
*/
typedef struct UrlcCsdTmDataReqTag
{
    /** Identifies the radio bearer corresponding to the data flow in this circuit. for Class C */
    BearerIdentity                          bearerIdentity;
    /** Number of SDUs */
    Int8                                    numberOfSdus;
    /** Array of CsdSdu data */
    UrlcCsdSdu                              sdu [URLC_MAX_CSD_SDUS_PER_TTI];
}
UrlcCsdTmDataReq;



/* @} */ //3G_CSDI_CSRLC

#endif /* URLC_SIG_H */


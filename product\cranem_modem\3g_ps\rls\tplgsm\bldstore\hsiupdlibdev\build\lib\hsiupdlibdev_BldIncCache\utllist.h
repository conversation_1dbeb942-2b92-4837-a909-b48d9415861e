/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/pscommon/3g_ut.mod/api/inc/utllist.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2007/03/19 13:59:30 $
 **************************************************************************
 * File Description:
 *
 *      A set of macros to support generic linked lists which use
 *      KiAllocMemory and KiFreeMemory to allocate and free nodes.
 *
 * Notes:
 *
 * 1    Although doubly linked lists use more memory (for the previous link),
 *      they are faster because singly linked list insert and delete operations
 *      require a list search to to find the preceding node.
 *
 * 2    It may be advisable to disable interrupts during critical sections
 *      of these macros, to make the operations atomic. In which case the
 *      user should replace the LLIST_ENTER_CRITICAL and LLIST_EXIT_CRITICAL
 *      macros with the appropriate actions for their environment.
 *
 * 3    An example program is available as utllisttest.c
 **************************************************************************/

#ifndef UTLLIST_H
#define UTLLIST_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <string.h>

/***************************************************************************
*   Manifest Constants
***************************************************************************/

/***************************************************************************
*   Macro Functions
***************************************************************************/

#define LLIST_ENTER_CRITICAL  /* Insert action to disable interrupts */
#define LLIST_EXIT_CRITICAL   /* Insert action to re-enable interrupts */

#if defined (UT_LINKED_LIST_AS_MODULE)
typedef struct UtDllNodeGenericTag
{
    struct UtDllNodeGenericTag   * prev_p;
    struct UtDllNodeGenericTag   * next_p;
    Int8                           payload; /* Null type, placeholder */
} UtDllNodeGeneric;

typedef void (* UtDllFnGeneric)(void *);

typedef struct UtDllListTypeGenericTag
{
    Int32                   length;
    UtDllNodeGeneric      * head_p;
    UtDllNodeGeneric      * tail_p;
    UtDllFnGeneric          delFunction_p;
} UtDllListTypeGeneric;

Boolean UtDllistEmpty(UtDllListTypeGeneric *listName_p);
void UtDllistAppend (size_t nodeSize, UtDllListTypeGeneric *listName_p,
                     UtDllNodeGeneric *refNode_p, UtDllNodeGeneric **newNode_pp);
void UtDllistInsert (size_t nodeSize, UtDllListTypeGeneric *listName_p,
                     UtDllNodeGeneric *refNode_p, UtDllNodeGeneric **newNode_pp);
void UtDllistBreak  (UtDllListTypeGeneric *listName_p,UtDllNodeGeneric *node_p);
void UtDllistDelete (UtDllListTypeGeneric *listName_p,UtDllNodeGeneric *node_p);
void UtDllistDestroy(UtDllListTypeGeneric *listName_p);
#endif


/***************************************************************************
 *
 * Macro:        UTLL_ALLOC_MEMORY
 *               UTLL_FREE_MEMORY
 *
 * Scope:        Global
 *
 * Parameters:
 *
 * Description:  These macros allow the memory manager that is used to
 *               allocate and deallocate nodes to be changed. For example,
 *               this can be used if a module has a fixed pool of nodes that
 *               is preallocated, speeding up the node allocation/deallocation
 *               process.
 *
 ***************************************************************************/
#if !defined UTLL_ALLOC_MEMORY
#define UTLL_ALLOC_MEMORY( sIZE, nODE_PP) KiAllocMemory (sIZE, nODE_PP)
#define UTLL_ALLOC_ZERO_MEMORY( sIZE, nODE_PP) KiAllocZeroMemory (sIZE, nODE_PP)
#endif

#if !defined UTLL_FREE_MEMORY
#define UTLL_FREE_MEMORY( nODE_PP) KiFreeMemory (nODE_PP)
#endif


/***************************************************************************
 *
 * Macro:        UT_DOUBLE_LINK_LIST_DECLARE
 *
 * Scope:        Global
 *
 * Parameters:   lISTtYPE           Type of the list to be declared
 *               pAYLOADtYPE        The user defined type of payload to be
 *                                  carried by list nodes
 *
 * Description:  Declare a doubly linked list type, which is a structure
 *               containing a head and tail pointer and an optional pointer
 *               to a payload delete function.
 *
 *               It creates the typedef for the nodes, which carry a user
 *               defined payload structure:
 *                  typdef struct UtDllNode<lISTtYPE>
 *                  {
 *                      struct UtDllNode<lISTtYPE>    * prev_p;
 *                      struct UtDllNode<lISTtYPE>    * next_p;
 *                      <pAYLOADtYPE>                   payload;
 *                  } UtDllNode<lISTtYPE>;
 *
 *              Then declares the typedef for a pointer to a function
 *              which references the user defined payload type:
 *                  typedef void (* UtDllFn<lISTtYPE>)(pAYLOADtYPE *);
 *
 *              The Empty List must be initialised (e.g. in MyTaskInit())
 *              by using the UT_DOUBLE_LINK_LIST_INIT macro (see below).
 *
 ***************************************************************************/
#define UT_DOUBLE_LINK_LIST_DECLARE(lISTtYPE,pAYLOADtYPE)                   \
typedef struct UtDllNode##lISTtYPE##Tag                                     \
{                                                                           \
    struct UtDllNode##lISTtYPE##Tag   * prev_p;                             \
    struct UtDllNode##lISTtYPE##Tag   * next_p;                             \
    pAYLOADtYPE                         payload;                            \
} UtDllNode##lISTtYPE;                                                      \
                                                                            \
typedef void (* UtDllFn##lISTtYPE)(pAYLOADtYPE *);                          \
                                                                            \
typedef struct lISTtYPE##Tag                                                \
{                                                                           \
    Int32                   length;                                         \
    UtDllNode##lISTtYPE   * head_p;                                         \
    UtDllNode##lISTtYPE   * tail_p;                                         \
    UtDllFn##lISTtYPE       delFunction_p;                                  \
} lISTtYPE;

/***************************************************************************
 *
 * Macro:        UT_DOUBLE_LINK_LIST_NODE_TYPE
 *
 * Scope:        Global
 *
 * Parameters:   lISTtYPE           Type of the list to be declared
 *
 * Description:  Macro to declare the type of a specific doubly linked list
 *               node
 *
 * Usage:        UT_DOUBLE_LINK_LIST_NODE_TYPE(myListType)  * myNode_p;
 *
 ***************************************************************************/
 #define UT_DOUBLE_LINK_LIST_NODE_TYPE(lISTtYPE)    UtDllNode##lISTtYPE

/***************************************************************************
 *
 * Macro:        UT_DOUBLE_LINK_LIST_INIT
 *
 * Scope:        Global
 *
 * Parameters:   lISTnAME           Name (instance) of the list to be
 *                                  initialised
 *               dELETEfN           Either PNULL or the address of a user
 *                                  supplied function which must be called
 *                                  when a node is deleted, and must be used
 *                                  when the payload of a node carries its
 *                                  own memory references
 *
 * Description:  Macro to initialise an instance of a doubly-linked list.
 *               This macro should be called just once.
 *
 *               The user-defined delete function should be provided (or
 *               a null pointer, PNULL, may be used).
 *               Initialisation will result in the following empty list:
 *                  struct
 *                  {
 *                      UtDllNode<lISTnAME>     length        = 0;
 *                      UtDllNode<lISTnAME>   * head_p        = PNULL;
 *                      UtDllNode<lISTnAME>   * tail_p        = PNULL;
 *                      UtDllFn<lISTnAME>       delFunction_p = <dELETEfN>;
 *                  } <lISTnAME>
 *               N.B. Initialisation MUST be done for every instance of
 *               any given list type!
 *
 * Usage:        UT_DOUBLE_LINK_LIST_INIT(myList, deleteNode);
 *
 ***************************************************************************/
#define UT_DOUBLE_LINK_LIST_INIT(lISTnAME, dELETEfN)                        \
{                                                                           \
    (lISTnAME).length = 0;                                                  \
    (lISTnAME).head_p = PNULL;                                              \
    (lISTnAME).tail_p = PNULL;                                              \
    (lISTnAME).delFunction_p = dELETEfN;                                    \
}

/***************************************************************************
 *
 * Macro:        UT_DOUBLE_LINK_LIST_HEAD
 *
 * Scope:        Global
 *
 * Parameters:   lISTnAME           Name of the list to be referenced
 *
 * Description:  Macro to reference a node at the head of a doubly linked list
 *
 * Usage:        if (UT_DOUBLE_LINK_LIST_HEAD(myList) != PNULL)
 *
 ***************************************************************************/
#define UT_DOUBLE_LINK_LIST_HEAD(lISTnAME)         ((lISTnAME).head_p)

/***************************************************************************
 *
 * Macro:        UT_DOUBLE_LINK_LIST_TAIL
 *
 * Scope:        Global
 *
 * Parameters:   lISTnAME           Name of the list to be referenced
 *
 * Description:  Macro to reference a node at the head of a doubly linked list
 *
 * Usage:        if (UT_DOUBLE_LINK_LIST_TAIL(myList) != PNULL)
 *
 ***************************************************************************/
 #define UT_DOUBLE_LINK_LIST_TAIL(lISTnAME)         ((lISTnAME).tail_p)

/***************************************************************************
 *
 * Macro:        UT_DOUBLE_LINK_LIST_LENGTH
 *
 * Scope:        Global
 *
 * Parameters:   lISTnAME           Name of the list to be referenced
 *
 * Description:  Macro to find the length of the list.
 *
 ***************************************************************************/
#define UT_DOUBLE_LINK_LIST_LENGTH(lISTnAME)        ((lISTnAME).length)

/***************************************************************************
 *
 * Macro:        UT_DOUBLE_LINK_LIST_PREV
 *
 * Scope:        Global
 *
 * Parameters:   nODE_P             Pointer to the reference node
 *
 * Description:  Macro to reference the previous pointer of a node in a doubly
 *               linked list
 *
 * Usage:        myNode_p = UT_DOUBLE_LINK_LIST_PREV(myNode_p)
 *
 ***************************************************************************/
 #define UT_DOUBLE_LINK_LIST_PREV(nODE_P)           ((nODE_P)->prev_p)

/***************************************************************************
 *
 * Macro:        UT_DOUBLE_LINK_LIST_NEXT
 *
 * Scope:        Global
 *
 * Parameters:   nODE_P             Pointer to the reference node
 *
 * Description:  Macro to reference the next pointer of a node in a doubly
 *               linked list
 *
 * Usage:        myNode_p = UT_DOUBLE_LINK_LIST_NEXT(myNode_p)
 *
 ***************************************************************************/
 #define UT_DOUBLE_LINK_LIST_NEXT(nODE_P)           ((nODE_P)->next_p)

/***************************************************************************
 *
 * Macro:        UT_DOUBLE_LINK_LIST_PAYLOAD
 *
 * Scope:        Global
 *
 * Parameters:   nODE_P             Pointer to the reference node
 *
 * Description:  Macro to reference the payload of a node in a doubly
 *               linked list
 *
 * Usage:        UT_DOUBLE_LINK_LIST_PAYLOAD(myNode_p).xxx = yyy
 *
 ***************************************************************************/
 #define UT_DOUBLE_LINK_LIST_PAYLOAD(nODE_P)        ((nODE_P)->payload)

/***************************************************************************
 *
 * Macro:        UT_DOUBLE_LINK_LIST_CREATE_NODE
 *
 * Scope:        Global
 *
 * Parameters:   lISTtYPE           Type of the list to be referenced
 *               nEWnODE_P          Pointer to the node to create
 *
 * Description:  Macro to create a node
 *
 * Usage:        UT_DOUBLE_LINK_LIST_CREATE_NODE
 *                                    (myListType,myNewNode)
 *
 ***************************************************************************/
#define UT_DOUBLE_LINK_LIST_CREATE_NODE(lISTtYPE, nEWnODE_P) \
        UTLL_ALLOC_MEMORY (sizeof(UtDllNode##lISTtYPE),((void**)&(nEWnODE_P)));

/***************************************************************************
 *
 * Macro:        UT_DOUBLE_LINK_LIST_EMPTY
 *
 * Scope:        Global
 *
 * Parameters:   lISTnAME           Name of the list to be referenced
 *
 * Description:  Macro to determine if doubly linked list is empty
 *
 * Usage:        if (UT_DOUBLE_LINK_LIST_EMPTY(myList) == TRUE)
 *
 ***************************************************************************/
#if !defined (UT_LINKED_LIST_AS_MODULE)
 #define UT_DOUBLE_LINK_LIST_EMPTY(lISTnAME)                                \
    (((lISTnAME).head_p == PNULL) ? TRUE : FALSE)
#else
 #define UT_DOUBLE_LINK_LIST_EMPTY(lISTnAME)                                \
    UtDllistEmpty((UtDllListTypeGeneric *)(& (lISTnAME)) )
#endif

/***************************************************************************
 *
 * Macro:        UT_DOUBLE_LINK_LIST_APPEND
 *
 * Scope:        Local
 *
 * Parameters:   lISTtYPE           Type of the list to be referenced
 *               lISTnAME           Name of the list to be referenced
 *               rEFnODE_P          Pointer to the reference node
 *               nEWnODE_P          Pointer to the node to append
 *                                  If PNULL
 *                                      Create a new node
 *                                  Else
 *                                      Use existing node
 *                                  Endif
 *
 * Description:  If nEWnODE_P is PNULL
 *                  Create a new empty doubly linked list node of type
 *                  UtDllNode<lISTtYPE> and insert it into the linked list,
 *                  after the specified node.
 *               Else
 *                  Insert an existing doubly linked list node of type
 *                  UtDllNode<lISTtYPE> after the specified node.
 *               Endif
 *               The list head and tail pointers are updated accordingly.
 *
 * Usage:        UT_DOUBLE_LINK_LIST_APPEND
 *                                    (myListType,myList,myRefNode,myNewNode)
 *
 ***************************************************************************/
#if !defined (UT_LINKED_LIST_AS_MODULE)
#define UT_DOUBLE_LINK_LIST_APPEND(lISTtYPE,lISTnAME,rEFnODE_P,nEWnODE_P)   \
{                                                                           \
    if ((nEWnODE_P) == PNULL)                                               \
    {                                                                       \
        UT_DOUBLE_LINK_LIST_CREATE_NODE (lISTtYPE, nEWnODE_P);              \
    }                                                                       \
    LLIST_ENTER_CRITICAL;                                                   \
    if ((lISTnAME).head_p == PNULL)                                         \
    {   /* Empty list */                                                    \
        (nEWnODE_P)->prev_p = (nEWnODE_P)->next_p = PNULL;                  \
        (lISTnAME).head_p = (lISTnAME).tail_p = (nEWnODE_P);                \
    }                                                                       \
    else                                                                    \
    {                                                                       \
        DevAssert ((rEFnODE_P) != PNULL);                                   \
        (nEWnODE_P)->next_p = (rEFnODE_P)->next_p;                          \
        (rEFnODE_P)->next_p = (nEWnODE_P);                                  \
        (nEWnODE_P)->prev_p = (rEFnODE_P);                                  \
        if ((nEWnODE_P)->next_p == PNULL)                                   \
        {   /* Appending to tail */                                         \
            (lISTnAME).tail_p = (nEWnODE_P);                                \
        }                                                                   \
        else                                                                \
        {                                                                   \
            (nEWnODE_P)->next_p->prev_p = (nEWnODE_P);                      \
        }                                                                   \
    }                                                                       \
    (lISTnAME).length++;                                                    \
    LLIST_EXIT_CRITICAL;                                                    \
}
#else
  #define UT_DOUBLE_LINK_LIST_APPEND(lISTtYPE,lISTnAME,rEFnODE_P,nEWnODE_P) \
    UtDllistAppend(sizeof(UtDllNode##lISTtYPE),                             \
                         (UtDllListTypeGeneric *)(& (lISTnAME)),            \
                         (UtDllNodeGeneric *)(rEFnODE_P),                   \
                         & (UtDllNodeGeneric *)(nEWnODE_P))
#endif

/***************************************************************************
 *
 * Macro:        UT_DOUBLE_LINK_LIST_INSERT
 *
 * Scope:        Local
 *
 * Parameters:   lISTtYPE           Type of the list to be referenced
 *               lISTnAME           Name of the list to be referenced
 *               rEFnODE_P          Pointer to the reference node
 *               nEWnODE_P          Pointer to the node to insert
 *
 * Description:  If nEWnODE_P is PNULL
 *                  Create a new empty doubly linked list node of type
 *                  UtDllNode<lISTtYPE> and insert it into the linked list,
 *                  before the specified node.
 *               Else
 *                  Insert an existing doubly linked list node of type
 *                  UtDllNode<lISTtYPE> before the specified node.
 *               Endif
 *
 * Usage:        UT_DOUBLE_LINK_LIST_INSERT
 *                                    (myListType,myList,myRefNode,myNewNode)
 *
 ***************************************************************************/
#if !defined (UT_LINKED_LIST_AS_MODULE)
  #define UT_DOUBLE_LINK_LIST_INSERT(lISTtYPE,lISTnAME,rEFnODE_P,nEWnODE_P) \
  {                                                                         \
    if ((nEWnODE_P) == PNULL)                                               \
    {                                                                       \
        UT_DOUBLE_LINK_LIST_CREATE_NODE (lISTtYPE, nEWnODE_P);              \
    }                                                                       \
    LLIST_ENTER_CRITICAL;                                                   \
    if ((lISTnAME).head_p == PNULL)                                         \
    {   /* Empty list */                                                    \
        (nEWnODE_P)->prev_p = (nEWnODE_P)->next_p = PNULL;                  \
        (lISTnAME).head_p = (lISTnAME).tail_p = (nEWnODE_P);                \
    }                                                                       \
    else                                                                    \
    {                                                                       \
        DevAssert ((rEFnODE_P) != PNULL);                                   \
        (nEWnODE_P)->next_p = (rEFnODE_P);                                  \
        (nEWnODE_P)->prev_p = (rEFnODE_P)->prev_p;                          \
        (rEFnODE_P)->prev_p = (nEWnODE_P);                                  \
        if ((nEWnODE_P)->prev_p == PNULL)                                   \
        {   /* Insert at head */                                            \
            (lISTnAME).head_p = (nEWnODE_P);                                \
        }                                                                   \
        else                                                                \
        {                                                                   \
            (nEWnODE_P)->prev_p->next_p = (nEWnODE_P);                      \
        }                                                                   \
    }                                                                       \
    (lISTnAME).length++;                                                    \
    LLIST_EXIT_CRITICAL;                                                    \
  }
#else
  #define UT_DOUBLE_LINK_LIST_INSERT(lISTtYPE,lISTnAME,rEFnODE_P,nEWnODE_P) \
    UtDllistInsert(sizeof(UtDllNode##lISTtYPE),                             \
                         (UtDllListTypeGeneric *)(& (lISTnAME)),            \
                         (UtDllNodeGeneric *)(rEFnODE_P),                   \
                         &(UtDllNodeGeneric *)(nEWnODE_P))
#endif

/***************************************************************************
 *
 * Macro:        UT_DOUBLE_LINK_LIST_BREAK
 *
 * Scope:        Local
 *
 * Parameters:   lISTnAME           Name of the list to be referenced
 *               nODE_P             Pointer to the reference node
 *
 * Description:  Macro to break the links to an existing node in a doubly
 *               linked list.
 *
 * Warning:      It does not free any memory.
 *               It does not check that nODE_P is currently a member of the
 *               list!
 *
 * Usage:        UT_DOUBLE_LINK_LIST_BREAK(myList,myNode)
 *
 ***************************************************************************/
#if !defined (UT_LINKED_LIST_AS_MODULE)
#define UT_DOUBLE_LINK_LIST_BREAK(lISTnAME,nODE_P)                          \
{                                                                           \
    DevAssert ((nODE_P) != PNULL);                                          \
    LLIST_ENTER_CRITICAL;                                                   \
    if ((nODE_P)->next_p != PNULL)                                          \
    {                                                                       \
        (nODE_P)->next_p->prev_p = (nODE_P)->prev_p;                        \
    }                                                                       \
    if ((nODE_P)->prev_p != PNULL)                                          \
    {                                                                       \
        (nODE_P)->prev_p->next_p = (nODE_P)->next_p;                        \
    }                                                                       \
    if ((lISTnAME).head_p == (nODE_P))                                      \
    {                                                                       \
        (lISTnAME).head_p = (nODE_P)->next_p;                               \
    }                                                                       \
    if ((lISTnAME).tail_p == (nODE_P))                                      \
    {                                                                       \
        (lISTnAME).tail_p = (nODE_P)->prev_p;                               \
    }                                                                       \
    (lISTnAME).length--;                                                    \
    LLIST_EXIT_CRITICAL;                                                    \
}
#else
  #define UT_DOUBLE_LINK_LIST_BREAK(lISTnAME, nODE_P)                       \
    UtDllistBreak( (UtDllListTypeGeneric *)(& (lISTnAME)),                  \
                   (UtDllNodeGeneric *)(nODE_P))
#endif

/***************************************************************************
 *
 * Macro:        UT_DOUBLE_LINK_LIST_MOVE_AFTER
 *
 * Scope:        Global
 *
 * Parameters:   lISTtYPE           Type of the list to be referenced
 *               slISTnAME          Name of the source linked list
 *               snODE_P            Pointer to the node in the source list
 *                                  to be moved
 *               dlISTnAME          Name of the destination linked list
 *               dnODE_P            Pointer to the node in the destination
 *                                  list, after which the node should be
 *                                  appended
 *
 * Description:  Remove a node from the source list and append it
 *               immediately after a node in the destination list.
 *               The list head and tail pointers are updated accordingly.
 *
 * Usage:        UT_DOUBLE_LINK_LIST_MOVE_AFTER
 *                     (myListType,sourceList,sourceNode,destinationList,
 *                      destinationNode);
 *
 ***************************************************************************/
#define UT_DOUBLE_LINK_LIST_MOVE_AFTER(lISTtYPE, slISTnAME,snODE_P,         \
                                                         dlISTnAME,dnODE_P) \
{                                                                           \
    UT_DOUBLE_LINK_LIST_BREAK(slISTnAME,snODE_P);                           \
    UT_DOUBLE_LINK_LIST_APPEND(lISTtYPE, dlISTnAME,dnODE_P,snODE_P);        \
}

/***************************************************************************
 *
 * Macro:        UT_DOUBLE_LINK_LIST_MOVE_BEFORE
 *
 * Scope:        Global
 *
 * Parameters:   lISTtYPE           Type of the list to be referenced
 *               slISTnAME          Name of the source linked list
 *               snODE_P            Pointer to the node in the source list
 *                                  to be moved
 *               dlISTnAME          Name of the destination linked list
 *               dnODE_P            Pointer to the node in the destination
 *                                  list, before which the node should be
 *                                  inserted
 *
 * Description:  Remove a node from the source list and insert it
 *               immediately before a node in the destination list.
 *               The list head and tail pointers are updated accordingly.
 *
 * Usage:        UT_DOUBLE_LINK_LIST_MOVE_BEFORE
 *                     (myListType,sourceList,sourceNode,destinationList,
 *                      destinationNode);
 *
 ***************************************************************************/
#define UT_DOUBLE_LINK_LIST_MOVE_BEFORE(lISTtYPE, slISTnAME,snODE_P,        \
                                        dlISTnAME,dnODE_P)                  \
{                                                                           \
    UT_DOUBLE_LINK_LIST_BREAK(slISTnAME,snODE_P);                           \
    UT_DOUBLE_LINK_LIST_INSERT(lISTtYPE, dlISTnAME, dnODE_P,snODE_P);       \
}

/***************************************************************************
 *
 * Macro:        UT_DOUBLE_LINK_LIST_DELETE
 *
 * Scope:        Global
 *
 * Parameters:   lISTnAME           Name of the list to be used
 *               nODE_P             Pointer to the node in the list which is
 *                                  to be deleted
 *
 * Description:  Delete a node from the doubly linked list. If a delete
 *               function was defined for the list, it will be called
 *               before the node is deleted.
 *               The list head and tail pointers are updated accordingly.
 *
 ***************************************************************************/
#if !defined (UT_LINKED_LIST_AS_MODULE)
#define UT_DOUBLE_LINK_LIST_DELETE(lISTnAME,nODE_P)                         \
{                                                                           \
    UT_DOUBLE_LINK_LIST_BREAK((lISTnAME),(nODE_P));                         \
    if ((lISTnAME).delFunction_p != PNULL)                                  \
    {                                                                       \
        (lISTnAME).delFunction_p (& (nODE_P)->payload);                     \
    }                                                                       \
    UTLL_FREE_MEMORY (((void**) & (nODE_P)));                               \
}
#else
  #define UT_DOUBLE_LINK_LIST_DELETE(lISTnAME, nODE_P)                      \
    UtDllistDelete( (UtDllListTypeGeneric *)(& (lISTnAME)),                 \
                    (UtDllNodeGeneric *)(nODE_P))
#endif

/***************************************************************************
 *
 * Macro:        UT_DOUBLE_LINK_LIST_DESTROY
 *
 * Scope:        Global
 *
 * Parameters:   lISTtYPE           Type of the list to be referenced
 *               lISTnAME           Name of the list to be destroyed
 *
 * Description:  Destroy all nodes in a doubly linked list.
 *               The list head and tail pointers are updated accordingly.
 *
 ***************************************************************************/
#if !defined (UT_LINKED_LIST_AS_MODULE)
#define UT_DOUBLE_LINK_LIST_DESTROY(lISTtYPE, lISTnAME)                     \
{                                                                           \
    UtDllNode##lISTtYPE * new_p;                                            \
    while (((new_p)=(lISTnAME).head_p) != PNULL)                            \
    {                                                                       \
        UT_DOUBLE_LINK_LIST_DELETE ((lISTnAME),(new_p))                     \
    }                                                                       \
    (lISTnAME).length        = 0;                                           \
    (lISTnAME).head_p        = PNULL;                                       \
    (lISTnAME).tail_p        = PNULL;                                       \
}
#else
  #define UT_DOUBLE_LINK_LIST_DESTROY(lISTtYPE, lISTnAME)                   \
    UtDllistDestroy( (UtDllListTypeGeneric *)(& (lISTnAME)) )
#endif

#if defined (UT_LINKED_LIST_AS_MODULE)
typedef struct UtSllNodeGenericTag
{
    struct UtSllNodeGenericTag   * next_p;
    Int8                           payload; /* Null type, placeholder */
} UtSllNodeGeneric;

typedef void (* UtSllFnGeneric)(void *);

typedef struct UtSllListTypeGenericTag
{
    Int32                   length;
    UtSllNodeGeneric      * head_p;
    UtSllNodeGeneric      * tail_p;
    UtSllFnGeneric          delFunction_p;
} UtSllListTypeGeneric;

Boolean UtSllistEmpty(UtSllListTypeGeneric *listName_p);
void UtSllistAppend (size_t nodeSize, UtSllListTypeGeneric *listName_p,
                     UtSllNodeGeneric *refNode_p, UtSllNodeGeneric **newNode_pp);
void UtSllistInsert (size_t nodeSize, UtSllListTypeGeneric *listName_p,
                     UtSllNodeGeneric *refNode_p, UtSllNodeGeneric **newNode_pp);
void UtSllistBreak  (UtSllListTypeGeneric *listName_p,UtSllNodeGeneric *node_p);
void UtSllistDelete (UtSllListTypeGeneric *listName_p,UtSllNodeGeneric *node_p);
void UtSllistDestroy(UtSllListTypeGeneric *listName_p);
#endif



/**************************************************************************
 *
 * Macro:        UT_SINGLE_LINK_LIST_DECLARE
 *
 * Scope:        Global
 *
 * Parameters:   lISTtYPE           Type of the list to be declared
 *               pAYLOADtYPE        The user defined type of payload to be
 *                                  carried by list nodes
 *
 * Description: Declare a singly linked list type, which is a structure
 *               containing a head and tail pointer and an optional pointer
 *               to a payload delete function.
 *
 *               It creates the typedef for the nodes, which carry a user
 *               defined payload structure:
 *                  typdef struct UtSllNode<lISTtYPE>
 *                  {
 *                      struct UtSllNode<lISTtYPE>    * next_p;
 *                      <pAYLOADtYPE>                   payload;
 *                  } UtSllNode<lISTtYPE>;
 *
 *              Then declares the typedef for a pointer to a function
 *              which references the user defined payload type:
 *                  typedef void (* UtSllFn<lISTtYPE>)(pAYLOADtYPE *);
 *
 *              The Empty List must be initialised (e.g. in MyTaskInit())
 *              by using the UT_SINGLE_LINK_LIST_INIT macro (see below).
 *
 ***************************************************************************/
#define UT_SINGLE_LINK_LIST_DECLARE(lISTtYPE,pAYLOADtYPE)                   \
typedef struct UtSllNode##lISTtYPE##Tag                                     \
{                                                                           \
    struct UtSllNode##lISTtYPE##Tag   * next_p;                             \
    pAYLOADtYPE                         payload;                            \
} UtSllNode##lISTtYPE;                                                      \
                                                                            \
typedef void (* UtSllFn##lISTtYPE)(pAYLOADtYPE *);                          \
                                                                            \
typedef struct lISTtYPE##Tag                                                \
{                                                                           \
    Int32                   length;                                         \
    UtSllNode##lISTtYPE   * head_p;                                         \
    UtSllNode##lISTtYPE   * tail_p;                                         \
    UtSllFn##lISTtYPE       delFunction_p;                                  \
} lISTtYPE;

/***************************************************************************
 *
 * Macro:        UT_SINGLE_LINK_LIST_NODE_TYPE
 *
 * Scope:        Global
 *
 * Parameters:   lISTtYPE           Type of the list to be declared
 *
 * Description:  Macro to declare the type of a specific singly linked list
 *               node
 *
 * Usage:        UT_SINGLE_LINK_LIST_NODE_TYPE(myListType)  * myNode_p;
 *
 ***************************************************************************/
 #define UT_SINGLE_LINK_LIST_NODE_TYPE(lISTtYPE)    UtSllNode##lISTtYPE

/***************************************************************************
 *
 * Macro:        UT_SINGLE_LINK_LIST_INIT
 *
 * Scope:        Global
 *
 * Parameters:   lISTnAME           Name (instance) of the list to be
 *                                  initialised
 *               dELETEfN           Either PNULL or the address of a user
 *                                  supplied function which must be called
 *                                  when a node is deleted, and must be used
 *                                  when the payload of a node carries its
 *                                  own memory references
 *
 * Description:  Macro to initialise an instance of a singly-linked list.
 *               This macro should be called just once.
 *
 *               The user-defined delete function should be provided (or
 *               a null pointer, PNULL, may be used).
 *               Initialisation will result in the following empty list:
 *                  struct
 *                  {
 *                      UtSllNode<lISTnAME>     length        = 0;
 *                      UtSllNode<lISTnAME>   * head_p        = PNULL;
 *                      UtSllNode<lISTnAME>   * tail_p        = PNULL;
 *                      UtSllFn<lISTnAME>       delFunction_p = <dELETEfN>;
 *                  } <lISTnAME>
 *               N.B. Initialisation MUST be done for every instance of
 *               any given list type!
 *
 * Usage:        UT_SINGLE_LINK_LIST_INIT(myList, deleteNode);
 *
 ***************************************************************************/
#define UT_SINGLE_LINK_LIST_INIT(lISTnAME, dELETEfN)                        \
{                                                                           \
    (lISTnAME).length = 0;                                                  \
    (lISTnAME).head_p = PNULL;                                              \
    (lISTnAME).tail_p = PNULL;                                              \
    (lISTnAME).delFunction_p = dELETEfN;                                    \
}

/***************************************************************************
 *
 * Macro:        UT_SINGLE_LINK_LIST_HEAD
 *
 * Scope:        Global
 *
 * Parameters:   lISTnAME           Name of the list to be referenced
 *
 * Description:  Macro to reference a node at the head of a singly linked list
 *
 * Usage:        if (UT_SINGLE_LINK_LIST_HEAD(myList) != PNULL)
 *
 ***************************************************************************/
 #define UT_SINGLE_LINK_LIST_HEAD(lISTnAME)         ((lISTnAME).head_p)

/***************************************************************************
 *
 * Macro:        UT_SINGLE_LINK_LIST_TAIL
 *
 * Scope:        Global
 *
 * Parameters:   lISTnAME           Name of the list to be referenced
 *
 * Description:  Macro to reference a node at the head of a singly linked list
 *
 * Usage:        if (UT_SINGLE_LINK_LIST_HEAD(myList) != PNULL)
 *
 ***************************************************************************/
 #define UT_SINGLE_LINK_LIST_TAIL(lISTnAME)         ((lISTnAME).tail_p)

/***************************************************************************
 *
 * Macro:        UT_SINGLE_LINK_LIST_LENGTH
 *
 * Scope:        Global
 *
 * Parameters:   lISTnAME           Name of the list to be referenced
 *
 * Description:  Macro to find the length of the list.
 *
 ***************************************************************************/
#define UT_SINGLE_LINK_LIST_LENGTH(lISTnAME)        ((lISTnAME).length)

/***************************************************************************
 *
 * Macro:        UT_SINGLE_LINK_LIST_NEXT
 *
 * Scope:        Global
 *
 * Parameters:   nODE_P             Pointer to the reference node
 *
 * Description:  Macro to reference the next pointer of a node in a singly
 *               linked list
 *
 * Usage:        myNode_p = UT_SINGLE_LINK_LIST_NEXT(myList)
 *
 ***************************************************************************/
 #define UT_SINGLE_LINK_LIST_NEXT(nODE_P)           ((nODE_P)->next_p)

/***************************************************************************
 *
 * Macro:        UT_SINGLE_LINK_LIST_PAYLOAD
 *
 * Scope:        Global
 *
 * Parameters:   nODE_P             Pointer to the reference node
 *
 * Description:  Macro to reference the payload of a node in a singly
 *               linked list
 *
 * Usage:        UT_SINGLE_LINK_LIST_PAYLOAD(myList).xxx = yyy
 *
 ***************************************************************************/
#define UT_SINGLE_LINK_LIST_PAYLOAD(nODE_P)        ((nODE_P)->payload)
#define UT_SINGLE_LINK_LIST_PAYLOAD_P(nODE_P)        (&((nODE_P)->payload)) /*CQ0093486 - add*/

/***************************************************************************
 *
 * Macro:        UT_SINGLE_LINK_LIST_EMPTY
 *
 * Scope:        Global
 *
 * Parameters:   lISTnAME           Name of the list to be referenced
 *
 * Description:  Macro to determine if singly linked list is empty
 *
 * Usage:        if (UT_SINGLE_LINK_LIST_EMPTY(myList) == TRUE)
 *
 ***************************************************************************/
#if !defined (UT_LINKED_LIST_AS_MODULE)
 #define UT_SINGLE_LINK_LIST_EMPTY(lISTnAME)                                \
    (((lISTnAME).head_p == PNULL) ? TRUE : FALSE)
#else
 #define UT_SINGLE_LINK_LIST_EMPTY(lISTnAME)                                \
    UtSllistEmpty((UtSllListTypeGeneric *)(& (lISTnAME)) )
#endif

/***************************************************************************
 *
 * Macro:        UT_SINGLE_LINK_LIST_CREATE_NODE
 *
 * Scope:        Global
 *
 * Parameters:   lISTtYPE           Type of the list to be referenced
 *               nEWnODE_P          Pointer to the node to create
 *
 * Description:  Macro to create a node
 *
 * Usage:        UT_SINGLE_LINK_LIST_CREATE_NODE
 *                                    (myListType,myNewNode)
 *
 ***************************************************************************/
#define UT_SINGLE_LINK_LIST_CREATE_NODE(lISTtYPE, nEWnODE_P) \
        UTLL_ALLOC_MEMORY (sizeof(UtSllNode##lISTtYPE),((void**)&(nEWnODE_P)));


/***************************************************************************
 *
 * Macro:        UT_SINGLE_LINK_LIST_APPEND
 *
 * Scope:        Local
 *
 * Parameters:   lISTtYPE           Type of the list to be referenced
 *               lISTnAME           Name of the list to be referenced
 *               rEFnODE_P          Pointer to the reference node
 *               nEWnODE_P          Pointer to the node to append
 *
 * Description:  IF nEWnODE_P = PNULL
 *                  Create a new empty singly linked list node of type
 *                  UtSllNode<lISTtYPE> and insert it into the linked list,
 *                  after the specified node.
 *               ELSE
 *                  Append an existing singly linked list node of type
 *                  UtSllNode<lISTtYPE> and insert it into the linked list,
 *                  after the specified node.
 *               ENDIF
 *               The list head and tail pointers are updated accordingly.
 *
 * Usage:        UT_SINGLE_LINK_LIST_APPEND
 *                                    (myListType,myList,myRefNode,myNewNode)
 *
 ***************************************************************************/
#if !defined(UT_LINKED_LIST_AS_MODULE)
#define UT_SINGLE_LINK_LIST_APPEND(lISTtYPE,lISTnAME,rEFnODE_P,nEWnODE_P)   \
{                                                                           \
    if ((nEWnODE_P) == PNULL)                                               \
    {                                                                       \
        UT_SINGLE_LINK_LIST_CREATE_NODE (lISTtYPE, nEWnODE_P);              \
    }                                                                       \
    LLIST_ENTER_CRITICAL;                                                   \
    if ((lISTnAME).head_p == PNULL)                                         \
    {   /* Empty list */                                                    \
        (nEWnODE_P)->next_p = PNULL;                                        \
        (lISTnAME).head_p = (lISTnAME).tail_p = (nEWnODE_P);                \
    }                                                                       \
    else                                                                    \
    {                                                                       \
        DevAssert ((rEFnODE_P) != PNULL);                                   \
        (nEWnODE_P)->next_p = (rEFnODE_P)->next_p;                          \
        (rEFnODE_P)->next_p = (nEWnODE_P);                                  \
        if ((nEWnODE_P)->next_p == PNULL)                                   \
        {   /* Appending to tail */                                         \
            (lISTnAME).tail_p = (nEWnODE_P);                                \
        }                                                                   \
    }                                                                       \
    (lISTnAME).length++;                                                    \
    LLIST_EXIT_CRITICAL;                                                    \
}
#else
  #define UT_SINGLE_LINK_LIST_APPEND(lISTtYPE,lISTnAME,rEFnODE_P,nEWnODE_P) \
    UtSllistAppend(sizeof(UtSllNode##lISTtYPE),                             \
                         (UtSllListTypeGeneric *)(& (lISTnAME)),            \
                         (UtSllNodeGeneric *)(rEFnODE_P),                   \
                         & (UtSllNodeGeneric *)(nEWnODE_P))
#endif


/***************************************************************************
 *
 * Macro:        UT_SINGLE_LINK_LIST_INSERT
 *
 * Scope:        Local
 *
 * Parameters:   lISTtYPE           Type of the list to be referenced
 *               lISTnAME           Name of the list to be referenced
 *               rEFnODE_P          Pointer to the reference node
 *               nEWnODE_P          Pointer to the node to insert
 *
 * Description:  IF nEWnODE_P = PNULL
 *                  Create a new empty singly linked list node of type
 *                  UtSllNode<lISTtYPE> and insert it into the linked list,
 *                  before the specified node.
 *               ELSE
 *                  Append an existing singly linked list node of type
 *                  UtSllNode<lISTtYPE> and insert it into the linked list,
 *                  before the specified node.
 *               ENDIF
 *               The list head and tail pointers are updated accordingly.
 *
 * Usage:        UT_SINGLE_LINK_LIST_INSERT
 *                                   (myListType,myList,myRefNode,myNewNode);
 *
 ***************************************************************************/
#if !defined (UT_LINKED_LIST_AS_MODULE)
#define UT_SINGLE_LINK_LIST_INSERT(lISTtYPE,lISTnAME,rEFnODE_P,nEWnODE_P)   \
{                                                                           \
    if ((nEWnODE_P) == PNULL)                                               \
    {                                                                       \
        UT_SINGLE_LINK_LIST_CREATE_NODE (lISTtYPE, nEWnODE_P);              \
    }                                                                       \
    LLIST_ENTER_CRITICAL;                                                   \
    (nEWnODE_P)->next_p = (rEFnODE_P);                                      \
    if ((lISTnAME).head_p == PNULL)                                         \
    {   /* Empty list */                                                    \
        (lISTnAME).head_p = (lISTnAME).tail_p = (nEWnODE_P);                \
    }                                                                       \
    else                                                                    \
    {                                                                       \
        DevAssert ((rEFnODE_P) != PNULL);                                   \
        if ((rEFnODE_P) == (lISTnAME).head_p)                               \
        {   /* Insert before head */                                        \
            (lISTnAME).head_p = (nEWnODE_P);                                \
        }                                                                   \
        else                                                                \
        {   /* Search list to find preceding node */                        \
            UtSllNode##lISTtYPE *prev_p = (lISTnAME).head_p;                \
            while ((prev_p)->next_p != (rEFnODE_P))                         \
            {                                                               \
                (prev_p) = (prev_p)->next_p;                                \
                DevAssert ((prev_p)->next_p != PNULL);                      \
            }                                                               \
            (prev_p)->next_p = (nEWnODE_P);                                 \
        }                                                                   \
    }                                                                       \
    (lISTnAME).length++;                                                    \
    LLIST_EXIT_CRITICAL;                                                    \
}
#else
  #define UT_SINGLE_LINK_LIST_INSERT(lISTtYPE,lISTnAME,rEFnODE_P,nEWnODE_P) \
    UtSllistInsert(sizeof(UtSllNode##lISTtYPE),                             \
                         (UtSllListTypeGeneric *)(& (lISTnAME)),            \
                         (UtSllNodeGeneric *)(rEFnODE_P),                   \
                         &(UtSllNodeGeneric *)(nEWnODE_P))
#endif

/***************************************************************************
 *
 * Macro:        UT_SINGLE_LINK_LIST_BREAK
 *
 * Scope:        Local
 *
 * Parameters:   lISTtYPE           Type of the list to be used
 *               lISTnAME           Name of the list to be referenced
 *               nODE_P             Pointer to the reference node
 *
 * Description:  Macro to break the links to an existing node in a singly
 *               linked list
 *
 * Warning:      It does not free any memory.
 *               It does not check that nODE_P is currently a member of the
 *               list!
 *
 * Usage:        UT_SINGLE_LINK_LIST_BREAK(myListType,myList,myNode);
 *
 ***************************************************************************/
#if !defined(UT_LINKED_LIST_AS_MODULE)
#define UT_SINGLE_LINK_LIST_BREAK(lISTtYPE,lISTnAME,nODE_P)                 \
{                                                                           \
    DevAssert ((nODE_P) != PNULL);                                          \
    LLIST_ENTER_CRITICAL;                                                   \
    if ((lISTnAME).head_p == (nODE_P))                                      \
    {   /* Delete from head */                                              \
        (lISTnAME).head_p = (nODE_P)->next_p;                               \
        if ((lISTnAME).tail_p == (nODE_P))                                  \
        {                                                                   \
            (lISTnAME).head_p = PNULL;                                      \
            (lISTnAME).tail_p = PNULL;                                      \
        }                                                                   \
    }                                                                       \
    else                                                                    \
    {   /* Search for prev, then update its next */                         \
        UtSllNode##lISTtYPE *prev_p = (lISTnAME).head_p;                    \
        while ((prev_p)->next_p != (nODE_P))                                \
        {                                                                   \
            (prev_p) = (prev_p)->next_p;                                    \
            DevAssert ((prev_p)->next_p != PNULL);                          \
        }   /* if above assert fires, nODE_P is not in list! */             \
        (prev_p)->next_p = (nODE_P)->next_p;                                \
        if ((lISTnAME).tail_p == (nODE_P))                                  \
        {                                                                   \
            (lISTnAME).tail_p = (prev_p);                                   \
        }                                                                   \
    }                                                                       \
    (lISTnAME).length--;                                                    \
    LLIST_EXIT_CRITICAL;                                                    \
}
#else
  #define UT_SINGLE_LINK_LIST_BREAK(lISTtYPE,lISTnAME, nODE_P)              \
    UtSllistBreak( (UtSllListTypeGeneric *)(& (lISTnAME)),                  \
                   (UtSllNodeGeneric *)(nODE_P))
#endif


/***************************************************************************
 *
 * Macro:        UT_SINGLE_LINK_LIST_MOVE_AFTER
 *
 * Scope:        Global
 *
 * Parameters:   lISTtYPE           Type of the list to be declared
 *               slISTnAME          Name of the source linked list
 *               snODE_P            Pointer to the node in the source list
 *                                  to be moved
 *               dlISTnAME          Name of the destination linked list
 *               dnODE_P            Pointer to the node in the destination
 *                                  list, after which the node should be
 *                                  appended
 *
 * Description:  Remove a node from the source list and append it
 *               immediately after a node in the destination list.
 *               The list head and tail pointers are updated accordingly.
 *
 * Usage:        UT_SINGLE_LINK_LIST_MOVE_AFTER
 *                     (myListType,sourceList,sourceNode,destinationList,
 *                      destinationNode);
 *
 ***************************************************************************/
#define UT_SINGLE_LINK_LIST_MOVE_AFTER(lISTtYPE,slISTnAME,snODE_P,          \
                                                         dlISTnAME,dnODE_P) \
{                                                                           \
    UT_SINGLE_LINK_LIST_BREAK(lISTtYPE,slISTnAME,snODE_P);                  \
    UT_SINGLE_LINK_LIST_APPEND(lISTtYPE,dlISTnAME,dnODE_P,snODE_P);         \
}

/***************************************************************************
 *
 * Macro:        UT_SINGLE_LINK_LIST_MOVE_BEFORE
 *
 * Scope:        Global
 *
 * Parameters:   lISTtYPE           Type of the list to be declared
 *               slISTnAME          Name of the source linked list
 *               snODE_P            Pointer to the node in the source list
 *                                  to be moved
 *               dlISTnAME          Name of the destination linked list
 *               dnODE_P            Pointer to the node in the destination
 *                                  list, before which the node should be
 *                                  inserted
 *
 * Description:  Remove a node from the source list and insert it
 *               immediately before a node in the destination list.
 *               The list head and tail pointers are updated accordingly.
 *
 * Usage:        UT_SINGLE_LINK_LIST_MOVE_BEFORE
 *                     (myListType,sourceList,sourceNode,destinationList,
 *                      destinationNode);
 *
 ***************************************************************************/
#define UT_SINGLE_LINK_LIST_MOVE_BEFORE(lISTtYPE,slISTnAME,snODE_P,         \
                                                          dlISTnAME,dnODE_P)\
{                                                                           \
    UT_SINGLE_LINK_LIST_BREAK(lISTtYPE,slISTnAME,snODE_P);                  \
    UT_SINGLE_LINK_LIST_INSERT(dlISTnAME,dnODE_P,                           \
                                     (UtSllNode##dlISTtYPE *)snODE_P        \
                                    );                                      \
}

/***************************************************************************
 *
 * Macro:        UT_SINGLE_LINK_LIST_DELETE
 *
 * Scope:        Global
 *
 * Parameters:   lISTtYPE           Type of the list to be used
 *               lISTnAME           Name of the list to be used
 *               nODE_P             Pointer to the node in the list which
 *                                  is to be deleted
 *
 * Description:  Delete a node from the singly linked list. If a delete
 *               function was defined for the list, it will be called
 *               before the node is deleted.
 *               The list head and tail pointers are updated accordingly.
 *
 ***************************************************************************/
#if !defined(UT_LINKED_LIST_AS_MODULE)
#define UT_SINGLE_LINK_LIST_DELETE(lISTtYPE,lISTnAME,nODE_P)                \
{                                                                           \
    UT_SINGLE_LINK_LIST_BREAK(lISTtYPE,(lISTnAME),(nODE_P));                \
    if ((lISTnAME).delFunction_p != PNULL)                                  \
    {                                                                       \
        (lISTnAME).delFunction_p (& (nODE_P)->payload);                     \
    }                                                                       \
    UTLL_FREE_MEMORY (((void**) & (nODE_P)));                               \
}
#else
  #define UT_SINGLE_LINK_LIST_DELETE(lISTtYPE,lISTnAME, nODE_P)             \
    UtSllistDelete( (UtSllListTypeGeneric *)(& (lISTnAME)),                 \
                    (UtSllNodeGeneric *)(nODE_P))
#endif


/***************************************************************************
 *
 * Macro:        UT_SINGLE_LINK_LIST_DESTROY
 *
 * Scope:        Global
 *
 * Parameters:   lISTtYPE           Type of the list to be destroyed
 *               lISTnAME           Name of the list to be destroyed
 *
 * Description:  Destroy all nodes in a singly linked list.
 *               The list head and tail pointers are updated accordingly.
 *
 ***************************************************************************/
#if !defined(UT_LINKED_LIST_AS_MODULE)
#define UT_SINGLE_LINK_LIST_DESTROY(lISTtYPE,lISTnAME)                      \
{                                                                           \
    UtSllNode##lISTtYPE * new_p;                                            \
    while (((new_p)=(lISTnAME).head_p) != PNULL)                            \
    {                                                                       \
        UT_SINGLE_LINK_LIST_DELETE (lISTtYPE,(lISTnAME),(new_p))            \
    }                                                                       \
    (lISTnAME).length        = 0;                                           \
    (lISTnAME).head_p        = PNULL;                                       \
    (lISTnAME).tail_p        = PNULL;                                       \
}
#else
  #define UT_SINGLE_LINK_LIST_DESTROY(lISTtYPE, lISTnAME)                   \
    UtSllistDestroy( (UtSllListTypeGeneric *)(& (lISTnAME)) )
#endif


/***************************************************************************
 *   Types
 ***************************************************************************/

//MODIFIED_5MD 
	/***************************************************************************
	 *
	 * Macro:		 UT_DOUBLE_LINK_LIST_WITH_ALIGN_HDR_DECLARE
	 *
	 * Scope:		 Global
	 *
	 * Parameters:	 lISTtYPE			Type of the list to be declared
	 *				 pAYLOADtYPE		The user defined type of payload to be
	 *									carried by list nodes
	 *
	 * Description:  Declare a doubly linked list type, which is a structure
	 *				 containing a head and tail pointer and an optional pointer
	 *				 to a payload delete function.
	 *				 The nodes header size before the payload is 32 bytes which is
	 *				 cache line size. This force the node payload to be located in
	 *				 a different cache line of the other node header parameters. 
	 *				 This declaration is alternative to UT_DOUBLE_LINK_LIST_DECLARE
	 *				 and it's used in cases we do not want clean/invalidate of the
	 *				 node payload to effect other parameters of the node header.
	 *
	 *				 It creates the typedef for the nodes, which carry a user
	 *				 defined payload structure:
	 *					typdef struct UtDllNode<lISTtYPE>
	 *					{
	 *						struct UtDllNode<lISTtYPE>	  * prev_p;
	 *						struct UtDllNode<lISTtYPE>	  * next_p;
	 *						Int8							alignmentFiller[24];
	 *						<pAYLOADtYPE>					payload;
	 *					} UtDllNode<lISTtYPE>;
	 *
	 *				Then declares the typedef for a pointer to a function
	 *				which references the user defined payload type:
	 *					typedef void (* UtDllFn<lISTtYPE>)(pAYLOADtYPE *);
	 *
	 *				The Empty List must be initialised (e.g. in MyTaskInit())
	 *				by using the UT_DOUBLE_LINK_LIST_INIT macro (see below).
	 *
	 ***************************************************************************/
	 
#define UT_DOUBLE_LINK_LIST_WITH_ALIGN_HDR_DECLARE(lISTtYPE,pAYLOADtYPE)    \
		typedef struct UtDllNode##lISTtYPE##Tag 									\
		{																			\
			struct UtDllNode##lISTtYPE##Tag   * prev_p; 							\
			struct UtDllNode##lISTtYPE##Tag   * next_p; 							\
			/* Alignment filler as: cache line size(32) - *prev_p(4) - *next_p(4) */\
			Int8								alignmentFiller 					\
				[32 -																\
				 sizeof(struct UtDllNode##lISTtYPE##Tag *) -						\
				 sizeof(struct UtDllNode##lISTtYPE##Tag *)];						\
			pAYLOADtYPE 						payload;							\
		} UtDllNode##lISTtYPE;														\
																					\
		typedef void (* UtDllFn##lISTtYPE)(pAYLOADtYPE *);							\
																					\
		typedef struct lISTtYPE##Tag												\
		{																			\
			Int32					length; 										\
			UtDllNode##lISTtYPE   * head_p; 										\
			UtDllNode##lISTtYPE   * tail_p; 										\
			UtDllFn##lISTtYPE		delFunction_p;									\
		} lISTtYPE;

#endif /* UTLLIST_H */

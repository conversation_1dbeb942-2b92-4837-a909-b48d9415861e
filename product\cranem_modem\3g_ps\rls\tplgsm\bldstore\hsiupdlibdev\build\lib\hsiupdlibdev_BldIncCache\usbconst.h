/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/devices/transports/usb.mod/api/inc/usbconst.h#4 $
 *   $Revision: #4 $
 *   $DateTime: 2007/03/26 11:16:27 $
 **************************************************************************/
/** \file
 *    Constants supporting the USB protocol stack.
 *
 *           THIS CODE MUST BE KEPT PLATFORM INDEPENDENT
 *
 **************************************************************************/
#if defined(UPGRADE_USB)

#ifndef USBCONST_H
#define USBCONST_H

/** \addtogroup PrdDevicesUsb
 * Universal Serial Bus.
 *
 * The USB product comprises three main components:
 * <ul>
 * <li>
 * At the core is the USB Protocol Stack itself.
 * <li>
 * Below this is a 'replacable' driver for the USB hardware on the platform.
 * <li>
 * Above the Stack is a framework implementing the target 'device applications'
 * i.e. the code that implements the basic USB 'function', whether that
 * be Mass Storage, Modem, Genie, or any client vendor-specific function.
 * </ul>
 *
 * The target framework also performs the initialisation of the
 * stack, and provides for general 'USB system' functionality.
 * The framework and drivers are always provided as source.
 * The stack itself may be provided as a compiled object.
 *
 * The interfaces into and between the stack and device applications are GKI
 * signalling interfaces, however, for portability these signals are often
 * prepared and sent using utility functions.  The interfaces may be equally
 * described by either the signals or these function calls.
 *
 * The Protocol Stack is a full USB 2.0 stack, except that it does not
 * support 480Mb/s "HiSpeed USB" nor Isochronous endpoints.
 * (NB. USB2 is <b>not</b> the same as high speed USB)
 *
 * @{
 */

/*
 * Device address related constants
 */

/** The default address of a USB device. */
#define USB_SPEC_DEFAULT_ADDRESS  0

/** The maximum address a USB device may take. */
#define USB_SPEC_MAXIMUM_ADDRESS  127

/*
 * Frame number related constants
 */

/** The minimum (valid) USB start-of-frame (SOF) number. */
#define USB_SPEC_MIN_FRAME_NUMBER  0

/** The maximum (valid) USB start-of-frame (SOF) number. */
#define USB_SPEC_MAX_FRAME_NUMBER  1023

/*
 * Interface related constants
 */

/** The minimum interface number that an interface may assume. */
#define USB_SPEC_MIN_INTERFACE_NUMBER  0

/** The maximum interface number that an interface may assume. */
#define USB_SPEC_MAX_INTERFACE_NUMBER  255

/** The minimum number of interfaces that a configuration may support (# zero). */
#define USB_SPEC_MIN_NUMBER_INTERFACES  1

/** The maximum number of interfaces that a configuration may support (# zero-to-255). */
#define USB_SPEC_MAX_NUMBER_INTERFACES  256

/*
 * Endpoint related constants
 */

/** A token for the default control endpoint 'zero' */
#define USB_ENDPOINT_ZERO 0
/** A value for an invalid endpoint [Not a specified USB Constant] */
#define USB_ENDPOINT_INVALID 100


/** The absolute maximum number of endpoints on a device */
#define USB_SPEC_MAX_NUM_ENDPOINTS 16


/** The maximum number of IN (transmitter) endpoints that a USB device may support. */
#define USB_SPEC_MAX_NUM_IN_ENDPOINTS  USB_SPEC_MAX_NUM_ENDPOINTS

/** The maximum number of OUT (receiver) endpoints that a USB device may support. */
#define USB_SPEC_MAX_NUM_OUT_ENDPOINTS  USB_SPEC_MAX_NUM_ENDPOINTS

/** The maximum number that an endpoint may assume. */
#define USB_SPEC_MAX_ENDPOINT_NUMBER  (USB_SPEC_MAX_NUM_ENDPOINTS-1)

/** Legality check macro */
#define USB_SPEC_CHECK_ENDPOINT_NUMBER(eP) { DevCheck( (eP) < USB_SPEC_MAX_NUM_ENDPOINTS, (eP),0,0); }


/*
 * Device request setup packet related constants
 */

/** The length (bytes) of a USB device request setup packet. */
#define USB_SPEC_LENGTH_OF_SETUP_PACKET  8

/** @} */ /* End of group */

#endif  /* !defined (USBCONST_H) */

#endif /* defined(UPGRADE_USB) */

/* END OF FILE */


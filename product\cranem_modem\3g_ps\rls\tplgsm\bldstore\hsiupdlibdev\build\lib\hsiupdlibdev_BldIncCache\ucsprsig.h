/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/api/inc/ucsprsig.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2007/03/19 13:59:30 $
 **************************************************************************
 * File Description:
 *
 * Header for CSDI signal-processing.
 **************************************************************************/

#if !defined (UCSPRSIG_H)
#define       UCSPRSIG_H

#include <ucstypes.h>
#include <ucsrabrb.h>


/***************************************************************************
* Nested Include Files
***************************************************************************/

/***************************************************************************
*   Manifest Constants
***************************************************************************/

/***************************************************************************
*   Macro Functions
***************************************************************************/

/***************************************************************************
*   Types
***************************************************************************/

/***************************************************************************
*   Function declarations
***************************************************************************/

Boolean UcsProcessSignal (SignalBuffer *rxSignal_p);
void UcsInitialise (void);
void UcsCsdiConnectReq (UcsRabIdentity rabIdentity, UcsRabType rabType);
void UcsCsdiDisconnectReq (UcsRabIdentity rabIdentity);
void UcsSendResumeInd (UcsRabInfo *rabInfo_p);


#endif

/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2006 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/csd.mod/api/shinc/vgmxlist.h#3 $
 *   $Revision: #3 $
 *   $DateTime: 2007/02/02 13:54:44 $
 **************************************************************************/
/* \file
 * Definition of available multiplexers - this file may be included
 * multiple times.
 **************************************************************************/








                                        








































                                                                             

#if defined (UPGRADE_3G) 
#define ENABLE_SYNC_PPP
#endif

#if defined (TRANSFER_MODIFICATION)
#if defined (CONFIG_TTP_MUX_NULL_NOPL)
/** The Null Multiplexer. With no physical layer*/
MUX_DEF(VG_MUX_NULL_NOPL, VG_MUX_NULL_NOPL_TASK_ID, 4, "NULL_NOPL")
#endif
/* The dummy MUX shares with one of the other muxes. */
#endif //TRANSFER_MODIFICATION

#if defined (CONFIG_TTP_MUX_NULL)
/** The Null Multiplexer. */
MUX_DEF(VG_MUX_NULL, VG_MUX_NULL_TASK_ID, 4, "NULL")
#endif /* defined (CONFIG_TTP_MUX_NULL) */

#if defined (CONFIG_TTP_MUX_EMBEDDED)
/** The Embedded Multiplexer. */
MUX_DEF(VG_MUX_GSM_0710E, VG_MUX_GSM710E_TASK_ID, 4, "EMBEDDED")
#endif /* defined (CONFIG_TTP_MUX_EMBEDDED) */

#if defined (CONFIG_TTP_MUX_STANDARD)
/** The Standard Multiplexer. */
MUX_DEF(VG_MUX_GSM_0710S, VG_MUX_GSM710S_TASK_ID, 4, "STANDARD")
#endif /* defined (CONFIG_TTP_MUX_STANDARD) */

#if defined (CONFIG_TTP_MUX_STANDARD_MULTI)
/** The Standard Multiplexer, extended for multicontext. */
MUX_DEF(VG_MUX_GSM_0710SM, VG_MUX_GSM710SM_TASK_ID, 4, "STANDARD-MULTI")
#endif

#if defined (TRANSFER_MODIFICATION)
#if defined (CONFIG_TTP_MUX_EMBEDDED_NOPL)
/** The Embedded Multiplexer. With no physical layer*/
MUX_DEF(VG_MUX_GSM_0710E_NOPL, VG_MUX_GSM710E_NOPL_TASK_ID, 4, "EMBEDDED_NOPL")
#endif
/* The dummy MUX shares with one of the other muxes. */
#endif //TRANSFER_MODIFICATION

#if !defined (DUMMY_MUX_DEFINED)
# if defined (DEDICATED_DUMMY_MUX_TASK)
/** The Dummy Multiplexer. */
MUX_DEF(VG_MUX_DUMMY, VG_MUX_DUMMY_TASK_ID, 3, "DUMMY")
#define DUMMY_MUX_DEFINED
# endif

//MR
#if defined (TRANSFER_MODIFICATION)
# if defined (CONFIG_TTP_MUX_NULL_NOPL)
#   if !defined (DUMMY_MUX_DEFINED)
/** The Dummy Multiplexer. */
MUX_DEF(VG_MUX_DUMMY, VG_MUX_NULL_NOPL_TASK_ID, 3, "DUMMY")
#define DUMMY_MUX_DEFINED
#   endif
# endif
#endif //TRANSFER_MODIFICATION

# if defined (CONFIG_TTP_MUX_NULL)
#   if !defined (DUMMY_MUX_DEFINED)
/** The Dummy Multiplexer. */
MUX_DEF(VG_MUX_DUMMY, VG_MUX_NULL_TASK_ID, 3, "DUMMY")
#define DUMMY_MUX_DEFINED
#   endif
# endif

# if defined (CONFIG_TTP_MUX_EMBEDDED)
#   if !defined (DUMMY_MUX_DEFINED)
/** The Dummy Multiplexer. */
MUX_DEF(VG_MUX_DUMMY, VG_MUX_GSM710E_TASK_ID, 3, "DUMMY")
#define DUMMY_MUX_DEFINED
#   endif
# endif

#if defined (TRANSFER_MODIFICATION)
//MB2710 - Do we need this???MBTODO
# if defined (CONFIG_TTP_MUX_EMBEDDED_NOPL)
#   if !defined (DUMMY_MUX_DEFINED)
/** The Dummy Multiplexer. */
MUX_DEF(VG_MUX_DUMMY, VG_MUX_GSM710E_NOPL_TASK_ID, 3, "DUMMY")
#define DUMMY_MUX_DEFINED
#   endif
# endif
#endif //TRANSFER_MODIFICATION

# if defined (CONFIG_TTP_MUX_STANDARD)
#   if !defined (DUMMY_MUX_DEFINED)
/** The Dummy Multiplexer. */
MUX_DEF(VG_MUX_DUMMY, VG_MUX_GSM710S_TASK_ID, 3, "DUMMY")
#define DUMMY_MUX_DEFINED
#   endif
# endif

#endif

#if defined (DUMMY_MUX_DEFINED)
#undef DUMMY_MUX_DEFINED
#endif /* !defined (DUMMY_MUX_DEFINED) */


#if defined (CONFIG_TTP_MUX_IRDA)
/** The IRDA Multiplexer. */
MUX_DEF(IRDA_NULL_MUX, VG_MUX_IRDA_TASK_ID, 3, "IRDA")
#endif /* CONFIG_TTP_MUX_IRDA */

#if defined (CONFIG_TTP_MUX_IRDA)

/* UPGRADE_IRDA used to imply the presence of an IrDA Mux but this is no
 
* longer the case (there are other non-mux IrDA users).  Within the Mux
 * code CONFIG_TTP_MUX_IRDA should be used in place of UPGRADE_IRDA.
 * This is a sanity check to ensure CONFIG_TTP_MUX_IRDA isn't set when IrDA
 * is absent. */
/* #error CONFIG_TTP_MUX_IRDA requires UPGRADE_IRDA */
      
#endif

#if defined (UPGRADE_USB)
# if defined (USB_COMMS_DEVICE_INTERFACE)
#   if defined (CONFIG_TTP_MUX_USBNULL)
/** The USB Multiplexer. */
MUX_DEF(USB_NULL_MUX, VG_MUX_USBNULL_TASK_ID, 3, "USB")  
#   endif   /* (CONFIG_TTP_MUX_USBNULL) */
# endif   /* (USB_COMMS_DEVICE_INTERFACE) */
#endif   /* (UPGRADE_USB) */

















                               

/******************************************************************************
 * * modem_main.c - implementation of modem adapter main functions
 *
 * *(C) Copyright 2019 Asr International Ltd.
 * * All Rights Reserved
 * ******************************************************************************/
#include "lv_watch.h"

#include "../modem/mmi_modem_adaptor_main.h"
#if USE_LV_WATCH_WS_BASE != 0
#include "mmi_modem_adaptor_ws.h"
#include "ws_cJSON.h"
#if USE_CRANE_WATCH_GPS != 0
#include "lv_drivers\xf_drv\gps\GpsDrvApi.h"
#endif

#if USE_LV_UPLOAD_ORDER_WIFI_GPS_LBS != 0
extern _ws_upload_pos_state ws_upload_pos_state;
#endif 

#if USE_LV_WATCH_STATUS_WIFI
static bool has_wifi_signal = false;
#endif

#define HEADER_JSON     "Content-Type: application/json;charset=UTF-8\r\n"


#if USE_LV_WATCH_STATUS_WIFI
bool CWatchService_GetWifiStatus(void)
{
	return has_wifi_signal;
}
#endif

static void CWatchService_Wifi_Scan_Cb_Ext(uint32_t pa, int32_t pb, void *pc)
{
    app_adp_wifi_result_t result = pa;
    app_adp_wifi_ap_list * ap_list = pc;
	uint8_t valid_cnt_min =0;

#if (USE_LV_WATCH_WS_CT != 0)
	valid_cnt_min=1;
#else
	valid_cnt_min=3;  //default scan data min num
#endif

    MMI_Modem_Wifi_Ctrl_Req(0, 0, 0, 5, 0);

    ws_client *pme = MMI_ModemAdp_WS_GetClient();
    ws_base_t *userData = WS_BASE_DATA(pme);
    pfnCellInfo callback = (pfnCellInfo)userData->cellInfoCB;
    ws_wifi_info *wifi = userData->location.wifi;

    // 添加过滤逻辑调试日志
    WS_PRINTF("[WIFI_DEBUG] %s() Adapter callback: result=%d, ap_count=%d, valid_min=%d",
              __FUNCTION__, result, ap_list ? ap_list->count : 0, valid_cnt_min);

	WS_PRINTF("%s() result=%d,ap_list->count=%d\n", __FUNCTION__, result,ap_list->count);

    if(APP_ADP_WIFI_RESULT_FAILURE != result && ap_list && ap_list->count>=valid_cnt_min){
        WS_PRINTF("[WIFI_DEBUG] Scan passed filter, processing %d APs", ap_list->count);
        
        if(wifi == NULL){
            wifi = userData->location.wifi = (ws_wifi_info*)lv_mem_alloc(sizeof(ws_wifi_info));
        }

        wifi->wifi_count = ap_list->count;
        
        for(int i=0; i<ap_list->count; i++){
            wifi->wifs[i].ap_name[0]=0;
            sprintf(wifi->wifs[i].rssi, "%d", ap_list->item[i].rssi);
            sprintf(wifi->wifs[i].mac, "%02x:%02x:%02x:%02x:%02x:%02x", 
                    ap_list->item[i].mac[0],ap_list->item[i].mac[1],ap_list->item[i].mac[2],
                    ap_list->item[i].mac[3],ap_list->item[i].mac[4],ap_list->item[i].mac[5]);      
            //WS_PRINTF("%s() wifi[%d]rssi=%d, mac=%s\n", __FUNCTION__, i, wifi->wifs[i].rssi,wifi->wifs[i].mac);

        }
		
	#if USE_LV_WATCH_STATUS_WIFI
		if(wifi->wifi_count > 0)
		has_wifi_signal = true;
		else
		has_wifi_signal = false;
	#endif
	
      //  Hal_Mem_Free(ap_list);

    }else{
        // 添加扫描失败过滤的调试日志
        WS_PRINTF("[WIFI_DEBUG] Scan failed filter: result=%d, count=%d, required_min=%d",
                  result, ap_list ? ap_list->count : 0, valid_cnt_min);
        callback = NULL;
    }

	Hal_Mem_Free(ap_list);
	
#if NEED_TO_REPORT_BOTH_WIFI_AND_LBS != 0
	// after search wifi, begin to search cell info
	app_adaptor_set_ncell_bch_req(1);
#else
	// if found wifi, no need to search cell info
	if(callback){
		callback(userData->client, userData->cellInfoCBPar);
        userData->cellInfoCB = NULL;
        userData->cellInfoCBPar = NULL;
    	lv_mem_free(userData->location.wifi);
    	userData->location.wifi = NULL;
	}else{
        app_adaptor_set_ncell_bch_req(1);
	}
#endif
}

void CWatchService_Stop_Wifi_Scan_And_Ncell_Bch(ws_client * pme)
{
    ws_base_t *userData = WS_BASE_DATA(pme);

    if(userData->cellInfoCB){
        userData->cellInfoCB = NULL;
        userData->cellInfoCBPar = NULL;
        lv_mem_free(userData->location.wifi);
        userData->location.wifi = NULL;

        MMI_Modem_Wifi_Ctrl_Req(0, 0, 0, 5, 0);
    	app_adaptor_set_ncell_bch_req(0);
    }
}

void CWatchService_Wifi_Scan_Cb(app_adp_wifi_result_t result, app_adp_wifi_ap_list * ap_list)
{
    MMI_ModemAdp_Rpc_Req(CWatchService_Wifi_Scan_Cb_Ext, result, 0, ap_list);
}



void CWatchService_GetNetCellInfo_ForSOS(ws_client *pme, void* callback, uint32_t par)
{
    ws_base_t *userData = WS_BASE_DATA(pme);

    if((userData->cellInfoCB != NULL) && (userData->cellInfoCBPar != 0)){
        userData->cellInfoCBPar |= par;
        return;
    }
    
    userData->cellInfoCB = callback;
    userData->cellInfoCBPar = par;
    app_adaptor_lte_cell_info_ind_bind(CWatchService_lte_cell_info_ind);
    app_adaptor_gsm_cell_info_ind_bind(CWatchService_gsm_cell_info_ind);
	app_adaptor_wifi_scan(3, 8, 25, 0, CWatchService_Wifi_Scan_Cb);
}

void CWatchService_GetNetCellInfo(ws_client *pme, void* callback, uint32_t par)
{
	WS_PRINTF("%s() line:%d\n", __FUNCTION__, __LINE__);
	
#if USE_LV_WATCH_VIDEOCALL != 0
	if (get_voipcall_params_call_state()){
		return;
	}
#endif

    ws_base_t *userData = WS_BASE_DATA(pme);

    if((userData->cellInfoCB != NULL) && (userData->cellInfoCBPar != 0)){
        userData->cellInfoCBPar |= par;
    #if USE_CRANE_WATCH_GPS != 0
        if(par&WS_LOC_GPS_FIRST){
            CWatchService_GetNetCellInfo_gps(pme, callback, par);
        }
    #endif
		WS_PRINTF("%s() return ---line:%d\n", __FUNCTION__, __LINE__);
        return;
    }
    
    userData->cellInfoCB = callback;
    userData->cellInfoCBPar = par;
    app_adaptor_lte_cell_info_ind_bind(CWatchService_lte_cell_info_ind);
    app_adaptor_gsm_cell_info_ind_bind(CWatchService_gsm_cell_info_ind);
    //app_adaptor_get_cell_info_req(GET_CELL_INFO_ONCE_MODE);
    //app_adaptor_set_ncell_bch_req(1);
	app_adaptor_wifi_scan(3, 8, 25, 0, CWatchService_Wifi_Scan_Cb);
#if USE_CRANE_WATCH_GPS != 0
    if(par&WS_LOC_GPS_FIRST){
        CWatchService_GetNetCellInfo_gps(pme, callback, par);
    }
#endif
}


void CWatchService_lte_cell_info_ind_Ext(uint32_t pa, int32_t pb, void *pc)
{
    app_adp_lte_cells_t *ltecell_list = pc;
    ws_client *pme = MMI_ModemAdp_WS_GetClient();
    ws_base_t *userData = WS_BASE_DATA(pme);
    ws_location  *location = &userData->location ;
    pfnCellInfo callback = (pfnCellInfo)userData->cellInfoCB;
    //app_adp_reg_and_home_plmn_info_t * RegHomePlmn = NULL;

    location->cellType = WS_CELL_TYPE_LTE;
    location->lte.scell.mcc = ltecell_list->scell.mcc;
    location->lte.scell.mnc = ltecell_list->scell.mnc;
    location->lte.scell.tac = ltecell_list->scell.tac;
    location->lte.scell.euarfcn = ltecell_list->scell.euarfcn;
    location->lte.scell.cellid = ltecell_list->scell.cellid;
    location->lte.scell.rsrp = ltecell_list->scell.rsrp;
    location->lte.scell.rsrq = ltecell_list->scell.rsrq;
    //RegHomePlmn = app_adaptor_get_plmn_info_req();
    location->lte.scell.rxlev = convertRsrpToDbm(ltecell_list->scell.rsrp);//RegHomePlmn->realdbm;
	//WS_PRINTF("lte_cell_info_ind()RegHomePlmn->realdbm=%d\n", RegHomePlmn->realdbm);
    //lv_mem_free(RegHomePlmn);
    
	WS_PRINTF("%s()mcc=%d,mnc=%d,tac=%d,euarfcn=%d,cell_id=%d,rsrp=%d,rsrq=%d, rxlev=%d\n", __FUNCTION__, 
                ltecell_list->scell.mcc,ltecell_list->scell.mnc,ltecell_list->scell.tac,
                ltecell_list->scell.euarfcn,ltecell_list->scell.cellid,ltecell_list->scell.rsrp,ltecell_list->scell.rsrq,location->lte.scell.rxlev);


    location->lte.num = ltecell_list->encell_num;
	WS_PRINTF("%s()lte.num=%d\n", __FUNCTION__, ltecell_list->encell_num);
    
    for(int i=0; i<ltecell_list->encell_num; i++){
        location->lte.cells[i].mcc = ltecell_list->encell[i].mcc;
        location->lte.cells[i].mnc = ltecell_list->encell[i].mnc;
        location->lte.cells[i].cellid = ltecell_list->encell[i].cellid;
        location->lte.cells[i].tac = ltecell_list->encell[i].tac;
        location->lte.cells[i].rssi = convertRsrpToDbm(ltecell_list->encell[i].rsrp);
        WS_PRINTF("%s()[%d] mcc=%d, mnc=%d, cellid=%d,tac=%d,rssi=%d\n", __FUNCTION__, i,
                    ltecell_list->encell[i].mcc, ltecell_list->encell[i].mnc,
                    ltecell_list->encell[i].cellid,ltecell_list->encell[i].tac,location->lte.cells[i].rssi);
    }

	WS_PRINTF("lte_cell_info_ind()gsm.num=%d\n", ltecell_list->gncell_num);
    location->lte.gnum = ltecell_list->gncell_num;
    for(int i=0; i<ltecell_list->gncell_num; i++){

        location->lte.gcells[i].mcc = ltecell_list->gncell[i].mcc;
        location->lte.gcells[i].mnc = ltecell_list->gncell[i].mnc;
        location->lte.gcells[i].lac = ltecell_list->gncell[i].lac;
        location->lte.gcells[i].arfcn = ltecell_list->gncell[i].arfcn;
        location->lte.gcells[i].bsic = ltecell_list->gncell[i].bsic;
        location->lte.gcells[i].cell_id = ltecell_list->gncell[i].cell_id;
        location->lte.gcells[i].rxlev = ltecell_list->gncell[i].rxlev;

    	WS_PRINTF("%s()[%d]mcc=%d,mnc=%d,lac=%d,arfcn=%d,bsic=%d,cell_id=%d,rxlev=%d\n", __FUNCTION__, i, 
                    ltecell_list->gncell[i].mcc,ltecell_list->gncell[i].mnc,ltecell_list->gncell[i].lac,
                    ltecell_list->gncell[i].arfcn,ltecell_list->gncell[i].bsic,ltecell_list->gncell[i].cell_id,ltecell_list->gncell[i].rxlev);
    }

    
    
    
    if(callback){
		// find ncell less than 2, try again, max is 5 times!
        if(ltecell_list->encell_num<2 && userData->cellsGetTimes<5){        
            app_adaptor_set_ncell_bch_req(1);            
            userData->cellsGetTimes++;
        }else{
            callback(userData->client, userData->cellInfoCBPar);
            userData->cellInfoCB = NULL;
            userData->cellInfoCBPar = NULL;
            userData->cellsGetTimes = 0;
            lv_mem_free(userData->location.wifi);
            userData->location.wifi = NULL;
        }
    }
    
    lv_mem_free(ltecell_list);
    
}

void CWatchService_gsm_cell_info_ind_Ext(uint32_t pa, int32_t pb, void *pc)
{
    app_adp_gsm_cells_t *gsmcell_list= pc;
    ws_client *pme = MMI_ModemAdp_WS_GetClient();
    ws_base_t *userData = WS_BASE_DATA(pme);
    ws_location  *location = &userData->location ;
    pfnCellInfo callback = (pfnCellInfo)userData->cellInfoCB;
    int i = 0;

    location->cellType = WS_CELL_TYPE_GSM;
    location->gsm.num = 1 + gsmcell_list->gncell_num;
	WS_PRINTF("%s()gsm.num=%d\n", __FUNCTION__, gsmcell_list->gncell_num);

    location->gsm.cells[i].mcc = gsmcell_list->scell.mcc;
    location->gsm.cells[i].mnc = gsmcell_list->scell.mnc;
    location->gsm.cells[i].lac = gsmcell_list->scell.lac;
    location->gsm.cells[i].arfcn = gsmcell_list->scell.arfcn;
    location->gsm.cells[i].bsic = gsmcell_list->scell.bsic;
    location->gsm.cells[i].cell_id = gsmcell_list->scell.cell_id;
    location->gsm.cells[i].rxlev = convertRxlevToDbm(gsmcell_list->scell.rxlev);
//	WS_PRINTF("%s()mcc=%d,mnc=%d,lac=%d,arfcn=%d,bsic=%d,cell_id=%d,rxlev=%d\n", __FUNCTION__, 
//                gsmcell_list->scell.mcc,gsmcell_list->scell.mnc,gsmcell_list->scell.lac,
//                gsmcell_list->scell.arfcn,gsmcell_list->scell.bsic,gsmcell_list->scell.cell_id,gsmcell_list->scell.rxlev);

    
    for(i=0; i<gsmcell_list->gncell_num; i++){
        location->gsm.cells[i+1].mcc = gsmcell_list->gncell[i].mcc;
        location->gsm.cells[i+1].mnc = gsmcell_list->gncell[i].mnc;
        location->gsm.cells[i+1].lac = gsmcell_list->gncell[i].lac;
        location->gsm.cells[i+1].arfcn = gsmcell_list->gncell[i].arfcn;
        location->gsm.cells[i+1].bsic = gsmcell_list->gncell[i].bsic;
        location->gsm.cells[i+1].cell_id = gsmcell_list->gncell[i].cell_id;
        location->gsm.cells[i+1].rxlev = convertRxlevToDbm(gsmcell_list->gncell[i].rxlev);
        
//        WS_PRINTF("%s()[%d]mcc=%d,mnc=%d,lac=%d,arfcn=%d,bsic=%d,cell_id=%d,rxlev=%d\n", __FUNCTION__, i,
//                    gsmcell_list->gncell[i].mcc,gsmcell_list->gncell[i].mnc,gsmcell_list->gncell[i].lac,
//                    gsmcell_list->gncell[i].arfcn,gsmcell_list->gncell[i].bsic,gsmcell_list->gncell[i].cell_id,gsmcell_list->gncell[i].rxlev);
        

    }

    
    if(callback){
		// find ncell less than 2, try again, max is 5 times!
        if(gsmcell_list->gncell_num<2 && userData->cellsGetTimes<5){      
            userData->cellsGetTimes++;
            app_adaptor_set_ncell_bch_req(1);
        }else{
            callback(userData->client, userData->cellInfoCBPar);
            userData->cellInfoCB = NULL;
            userData->cellInfoCBPar = NULL;
            userData->cellsGetTimes = 0;
            lv_mem_free(userData->location.wifi);
            userData->location.wifi = NULL;
        }
    }
    
    lv_mem_free(gsmcell_list);

}

void CWatchService_lte_cell_info_ind(app_adp_lte_cells_t *ltecell_list)
{
    MMI_ModemAdp_Rpc_Req(CWatchService_lte_cell_info_ind_Ext, 0, 0, ltecell_list);
}

void CWatchService_gsm_cell_info_ind(app_adp_gsm_cells_t *gsmcell_list)
{
    MMI_ModemAdp_Rpc_Req(CWatchService_gsm_cell_info_ind_Ext, 0, 0, gsmcell_list);
}


#if USE_CRANE_WATCH_GPS != 0
static void CWatchService_GpsGetPosCb(GpsPositionInfo nInfo)
{
    ws_client *pme = MMI_ModemAdp_WS_GetClient();
    ws_base_t *userData = WS_BASE_DATA(pme);
    pfnCellInfo callback = (pfnCellInfo)userData->GpsInfoCB;
    ws_gps *gps = userData->location.gps;
	
	WS_PRINTF("%s() status:%d\n", __FUNCTION__, nInfo.status);
    
    if(nInfo.status != GSP_NEW_POS){
        if(gps){
            free(userData->location.gps);
            gps = userData->location.gps = NULL;
        }
    }else{
        if(gps == NULL){
            gps = userData->location.gps = (ws_gps*)malloc(sizeof(ws_gps));
        }
        if(gps){
            snprintf(gps->lon, WS_GPS_LONLAT_MAX_LEN, "%f", nInfo.lonf);
            snprintf(gps->lat, WS_GPS_LONLAT_MAX_LEN, "%f", nInfo.latf);
        }
    }
    
    if(callback){
        callback(userData->client, userData->GpsInfoCBPar);
    }
    
    userData->GpsInfoCB = NULL;
    userData->GpsInfoCBPar = NULL;
    
    if(gps){
        free(userData->location.gps);
    }
    userData->location.gps = NULL;
}

void CWatchService_GetNetCellInfo_gps(ws_client *pme, void* callback, uint32_t par)
{
    ws_base_t *userData = WS_BASE_DATA(pme);

    WS_PRINTF("%s() line:%d\n", __FUNCTION__, __LINE__);
    
    userData->GpsInfoCB = callback;
    userData->GpsInfoCBPar |= par;
    userData->cellsGetTimes = 0;
#if USE_CT_LOCATE_GPS_FIRST!=0 ||USE_FIXED_LOCATE_GPS_FIRST!=0
    app_adaptor_lte_cell_info_ind_bind(CWatchService_lte_cell_info_ind);
    app_adaptor_gsm_cell_info_ind_bind(CWatchService_gsm_cell_info_ind);
    app_adaptor_get_cell_info_req(GET_CELL_INFO_ONCE_MODE);
#endif

	GpsGetPostionInfo(CWatchService_GpsGetPosCb);
	
}

#if USE_LV_FREQUENCY_REPORT_GPS_FIRST != 0
void CWatchService_GetNetCellInfo_Frequency(ws_client *pme, void* callback, uint32_t par)
{
	WS_PRINTF("%s() line:%d\n", __FUNCTION__, __LINE__);
    ws_base_t *userData = WS_BASE_DATA(pme);
    
    userData->cellInfoCB = callback;
    userData->cellInfoCBPar = par;
    app_adaptor_lte_cell_info_ind_bind(CWatchService_lte_cell_info_ind);
    app_adaptor_gsm_cell_info_ind_bind(CWatchService_gsm_cell_info_ind);
    //app_adaptor_get_cell_info_req(GET_CELL_INFO_ONCE_MODE);
    //app_adaptor_set_ncell_bch_req(1);
	app_adaptor_wifi_scan(3, 8, 25, 0, CWatchService_Wifi_Scan_Cb);
}

static void CWatchService_GpsGetPosCbFrequency(GpsPositionInfo nInfo)
{
    ws_client *pme = MMI_ModemAdp_WS_GetClient();
    ws_base_t *userData = WS_BASE_DATA(pme);
    pfnCellInfo callback = (pfnCellInfo)userData->GpsInfoCB;
    ws_gps *gps = userData->location.gps;
	
	WS_PRINTF("%s() status:%d\n", __FUNCTION__, nInfo.status);
    
    if(nInfo.status == GSP_NO_FIXED ||nInfo.status == GSP_LAST_TIME_POS){
        if(gps){
            free(userData->location.gps);
            gps = userData->location.gps = NULL;
        }
        //无GPS
        CWatchService_GetNetCellInfo_Frequency(pme, callback, WS_LOC_POS);
    }else if(nInfo.status == GSP_NEW_POS){
        if(gps == NULL){
            gps = userData->location.gps = (ws_gps*)malloc(sizeof(ws_gps));
        }
        if(gps){
            snprintf(gps->lon, WS_GPS_LONLAT_MAX_LEN, "%f", nInfo.lonf);
            snprintf(gps->lat, WS_GPS_LONLAT_MAX_LEN, "%f", nInfo.latf);
        }
        if(callback){
            callback(userData->client, userData->GpsInfoCBPar);
        }
    }
    
    userData->GpsInfoCB = NULL;
    userData->GpsInfoCBPar = NULL;
    
    if(gps){
        free(userData->location.gps);
    }
    userData->location.gps = NULL;
}

void CWatchService_GetNetCellInfo_gps_frequency(ws_client *pme, void* callback, uint32_t par)
{
	WS_PRINTF("%s() line:%d\n", __FUNCTION__, __LINE__);
    ws_base_t *userData = WS_BASE_DATA(pme);
    
    userData->GpsInfoCB = callback;
    userData->GpsInfoCBPar = par;
    userData->cellsGetTimes = 0;
	GpsGetPostionInfo(CWatchService_GpsGetPosCbFrequency);
}

void CWatchService_GetNetCellInfo_ForServer(ws_client *pme, void* callback, uint32_t par)
{
	//WS_PRINTF("%s() line:%d\n", __FUNCTION__, __LINE__);
    ws_base_t *userData = WS_BASE_DATA(pme);

    if((userData->cellInfoCB != NULL) && (userData->cellInfoCBPar != 0)){
        userData->cellInfoCBPar |= par;
        return;
    }
    
    userData->cellInfoCB = callback;
    userData->cellInfoCBPar = par;
    app_adaptor_lte_cell_info_ind_bind(CWatchService_lte_cell_info_ind);
    app_adaptor_gsm_cell_info_ind_bind(CWatchService_gsm_cell_info_ind);
    //app_adaptor_get_cell_info_req(GET_CELL_INFO_ONCE_MODE);
    //app_adaptor_set_ncell_bch_req(1);
	app_adaptor_wifi_scan(3, 8, 25, 0, CWatchService_Wifi_Scan_Cb);
}

static void CWatchService_GpsGetPosCbForServer(GpsPositionInfo nInfo)
{
    ws_client *pme = MMI_ModemAdp_WS_GetClient();
    ws_base_t *userData = WS_BASE_DATA(pme);
    pfnCellInfo callback = (pfnCellInfo)userData->GpsInfoCB;
    ws_gps *gps = userData->location.gps;
	
	WS_PRINTF("%s() status:%d\n", __FUNCTION__, nInfo.status);
    
    if(nInfo.status == GSP_NO_FIXED ||nInfo.status == GSP_LAST_TIME_POS){
        if(gps){
            free(userData->location.gps);
            gps = userData->location.gps = NULL;
        }
    }else if(nInfo.status == GSP_NEW_POS){
        if(gps == NULL){
            gps = userData->location.gps = (ws_gps*)malloc(sizeof(ws_gps));
        }
        if(gps){
            snprintf(gps->lon, WS_GPS_LONLAT_MAX_LEN, "%f", nInfo.lonf);
            snprintf(gps->lat, WS_GPS_LONLAT_MAX_LEN, "%f", nInfo.latf);
        }
        if(callback){
            callback(userData->client, userData->GpsInfoCBPar);
        }
    }
    
    userData->GpsInfoCB = NULL;
    userData->GpsInfoCBPar = NULL;
    
    if(gps){
        free(userData->location.gps);
    }
    userData->location.gps = NULL;
}

void CWatchService_GetGpsInfo_ForServer(ws_client *pme, void* callback, uint32_t par)
{
	WS_PRINTF("%s() line:%d\n", __FUNCTION__, __LINE__);
    ws_base_t *userData = WS_BASE_DATA(pme);
    
    userData->GpsInfoCB = callback;
    userData->GpsInfoCBPar = par;
    userData->cellsGetTimes = 0;
	GpsGetPostionInfo(CWatchService_GpsGetPosCbForServer);
}

static void CWatchService_GpsGetPosCbForPositive(GpsPositionInfo nInfo)
{
    ws_client *pme = MMI_ModemAdp_WS_GetClient();
    ws_base_t *userData = WS_BASE_DATA(pme);
    pfnCellInfo callback = (pfnCellInfo)userData->GpsInfoCB;
    ws_gps *gps = userData->location.gps;
	
	WS_PRINTF("%s() status:%d\n", __FUNCTION__, nInfo.status);
    
    if(nInfo.status == GSP_NO_FIXED ||nInfo.status == GSP_LAST_TIME_POS){
        if(gps){
            free(userData->location.gps);
            gps = userData->location.gps = NULL;
        }
    }else if(nInfo.status == GSP_NEW_POS){
        if(gps == NULL){
            gps = userData->location.gps = (ws_gps*)malloc(sizeof(ws_gps));
        }
        if(gps){
            snprintf(gps->lon, WS_GPS_LONLAT_MAX_LEN, "%f", nInfo.lonf);
            snprintf(gps->lat, WS_GPS_LONLAT_MAX_LEN, "%f", nInfo.latf);
        }
        if(callback){
            callback(userData->client, userData->GpsInfoCBPar);
        }
		
		#if USE_LV_UPLOAD_ORDER_WIFI_GPS_LBS != 0
		ws_upload_pos_state.state |= UPLOAD_POS_STATE_GPS_SUC;
		#endif
    }

	#if USE_LV_UPLOAD_ORDER_WIFI_GPS_LBS != 0
	WS_PRINTF("%s() ws_upload_pos_state.state:%d\n", __FUNCTION__, ws_upload_pos_state.state);
	if((ws_upload_pos_state.state & UPLOAD_POS_STATE_GPS_SUC) == 0 && (ws_upload_pos_state.state & UPLOAD_POS_STATE_WIFI_SUC) == 0)
	{
		app_adaptor_lte_cell_info_ind_bind(CWatchService_lte_cell_info_ind);
		app_adaptor_gsm_cell_info_ind_bind(CWatchService_gsm_cell_info_ind);
		app_adaptor_get_cell_info_req(GET_CELL_INFO_ONCE_MODE);
		app_adaptor_set_ncell_bch_req(1);
	}
	#endif
	userData->GpsInfoCB = NULL;
    userData->GpsInfoCBPar = NULL;
    
    if(gps){
        free(userData->location.gps);
    }
    userData->location.gps = NULL;
}


static void CWatchService_Wifi_Scan_Cb_Ext_Positive(uint32_t pa, int32_t pb, void *pc)
{
    app_adp_wifi_result_t result = pa;
    app_adp_wifi_ap_list * ap_list = pc;
	uint8_t valid_cnt_min =0;
	
#if (USE_LV_WATCH_WS_CT != 0)
	valid_cnt_min=1;
#else
	valid_cnt_min=3;  //default scan data min num
#endif

    MMI_Modem_Wifi_Ctrl_Req(0, 0, 0, 5, 0);

    ws_client *pme = MMI_ModemAdp_WS_GetClient();
    ws_base_t *userData = WS_BASE_DATA(pme);
    pfnCellInfo callback = (pfnCellInfo)userData->cellInfoCB;
    ws_wifi_info *wifi = userData->location.wifi;
	WS_PRINTF("%s() result=%d,ap_list->count=%d\n", __FUNCTION__, result,ap_list->count);
    if(APP_ADP_WIFI_RESULT_FAILURE != result && ap_list->count>=valid_cnt_min){
        
        if(wifi == NULL){
            wifi = userData->location.wifi = (ws_wifi_info*)lv_mem_alloc(sizeof(ws_wifi_info));
        }

        wifi->wifi_count = ap_list->count;
        
        for(int i=0; i<ap_list->count; i++){
            wifi->wifs[i].ap_name[0]=0;
            sprintf(wifi->wifs[i].rssi, "%d", ap_list->item[i].rssi);
            sprintf(wifi->wifs[i].mac, "%02x:%02x:%02x:%02x:%02x:%02x", 
                    ap_list->item[i].mac[0],ap_list->item[i].mac[1],ap_list->item[i].mac[2],
                    ap_list->item[i].mac[3],ap_list->item[i].mac[4],ap_list->item[i].mac[5]);      
            //WS_PRINTF("%s() wifi[%d]rssi=%s, mac=%s\n", __FUNCTION__, i, wifi->wifs[i].rssi,wifi->wifs[i].mac);
        }
		
	#if USE_LV_WATCH_STATUS_WIFI
		if(wifi->wifi_count > 0)
		has_wifi_signal = true;
		else
		has_wifi_signal = false;
	#endif
	
      //  Hal_Mem_Free(ap_list);
	#if USE_LV_UPLOAD_ORDER_WIFI_GPS_LBS != 0
		ws_upload_pos_state.state |= UPLOAD_POS_STATE_WIFI_SUC;
	#endif
	
    }else{
        callback = NULL;
    }

	Hal_Mem_Free(ap_list);
	
	// if found wifi, no need to search cell info
	if(callback){
		callback(userData->client, userData->cellInfoCBPar);
        //userData->cellInfoCB = NULL;
        //userData->cellInfoCBPar = NULL;
    	lv_mem_free(userData->location.wifi);
    	userData->location.wifi = NULL;
	}
}

void CWatchService_Wifi_Scan_Cb_Positive(app_adp_wifi_result_t result, app_adp_wifi_ap_list * ap_list)
{
    MMI_ModemAdp_Rpc_Req(CWatchService_Wifi_Scan_Cb_Ext_Positive, result, 0, ap_list);
}

void CWatchService_GetWifiFirst_Positive(ws_client *pme, void* callback, uint32_t par)
{
	WS_PRINTF("%s() line:%d\n", __FUNCTION__, __LINE__);
    ws_base_t *userData = WS_BASE_DATA(pme);

    userData->cellInfoCB = callback;
    userData->cellInfoCBPar = par;
    userData->GpsInfoCB = callback;
    userData->GpsInfoCBPar = par;
    userData->cellsGetTimes = 0;
	app_adaptor_wifi_scan(3, 8, 25, 0, CWatchService_Wifi_Scan_Cb_Positive);
	GpsGetPostionInfo(CWatchService_GpsGetPosCbForPositive);
}
#endif

#if USE_LV_VIE_LOWMODE_REPORT_ORDER != 0
extern void CWatchService_GetPosInfoCB_LowModeCb(void *pUser, uint32_t par);		
void CWatchService_GpsGetPosCbForLowMode(GpsPositionInfo nInfo)
{
    ws_client *pme = MMI_ModemAdp_WS_GetClient();
    ws_base_t *userData = WS_BASE_DATA(pme);
    pfnCellInfo callback = (pfnCellInfo)userData->GpsInfoCB;
    ws_gps *gps = userData->location.gps;
	
	WS_PRINTF("%s() status:%d\n", __FUNCTION__, nInfo.status);
    
    if(nInfo.status == GSP_NO_FIXED ||nInfo.status == GSP_LAST_TIME_POS){
        if(gps){
            free(userData->location.gps);
            gps = userData->location.gps = NULL;
        }
		//无GPS，搜索WIFI
		CWatchService_GetWiFiInfoLowMode(pme, CWatchService_GetPosInfoCB_LowModeCb, WS_LOC_POS);
		
    }else if(nInfo.status == GSP_NEW_POS){
        if(gps == NULL){
            gps = userData->location.gps = (ws_gps*)malloc(sizeof(ws_gps));
        }
        if(gps){
            snprintf(gps->lon, WS_GPS_LONLAT_MAX_LEN, "%f", nInfo.lonf);
            snprintf(gps->lat, WS_GPS_LONLAT_MAX_LEN, "%f", nInfo.latf);
        }
        if(callback){
            callback(userData->client, userData->GpsInfoCBPar);
        }
    }
    
    userData->GpsInfoCB = NULL;
    userData->GpsInfoCBPar = NULL;
    
    if(gps){
        free(userData->location.gps);
    }
    userData->location.gps = NULL;
}

void CWatchService_GetGPSInfoLowMode(ws_client *pme, void* callback, uint32_t par)
{
	WS_PRINTF("%s() line:%d\n", __FUNCTION__, __LINE__);
    ws_base_t *userData = WS_BASE_DATA(pme);
    
    userData->GpsInfoCB = callback;
    userData->GpsInfoCBPar = par;
    userData->cellsGetTimes = 0;
	GpsGetPostionInfo(CWatchService_GpsGetPosCbForLowMode);
}
#endif

#endif


#if USE_WS_GX_TELECOM_LAOREN !=0
ws_lr_pos gLrCurrgps;
#define LAOREN_POS_SERVER_URL "http://yjyl.wjw.nanning.gov.cn:8848/iot/sos//msg/receive"
static int CWatchService_http_gx_laoren_post_pos_CB(char * data, int len, int num, void *cbdata)
{
    char *rsp_data=NULL;
    WS_PRINTF("CWatchService_http_gx_laoren_post_pos_CB()  len=%d, num=%d\n",  len, num);
    if(data == NULL && num == 0)
    {
        WS_PRINTF("CWatchService_http_gx_laoren_post_pos_CB() response_code=%d!\n", len);
        return -1;
    }
	if(num < 0)
	{
		WS_PRINTF("CWatchService_http_gx_laoren_post_pos_CB() num<0!\n");
		return -1;
	}
	
    WS_PRINTF("CWatchService_http_gx_laoren_post_pos_CB() :%s\n",data);
	
	char *dParse=(char*) malloc(len+1);
	memcpy(dParse,data,len);
	dParse[len]=0;
	
	ws_cJSON *root = ws_cJSON_Parse(dParse);
	if(root){

		rsp_data = ws_cJSON_PrintUnformatted(root);
		
        WS_PRINTF("rsp_data:%s\n", rsp_data);
		ws_cJSON *code = ws_cJSON_GetObjectItem(root, "code");
		if(code && code->valueint==200){
			
			WS_PRINTF("success,ok!");
		}
		else
		{
			WS_PRINTF("fail:%d" ,code->valueint);
		}

		WS_PRINTF("CWatchService_http_gx_laoren_post_pos_CB--delete root!");
		
		ws_cJSON_Delete(root);
    	free(rsp_data);
	}

	free(dParse);
	
    return 0;

}

char *CWatchService_build_laoren_pos_post_data(char * req_imei)
{
    char *postdata=NULL;
    hal_rtc_t time;
    char str_datetime[16] = "";
    Hal_Rtc_Gettime(&time);
    sprintf(str_datetime, "%04d-%02d-%02d %02d:%02d:%02d", time.tm_year, time.tm_mon, time.tm_mday, time.tm_hour, time.tm_min, time.tm_sec);
	
    WS_PRINTF("build_common_post_data(): imei=%s\n", req_imei);

	 ws_cJSON *root_data=ws_cJSON_CreateObject();
	 
    ws_cJSON_AddStringToObject(root_data,"MID",req_imei);
    
    ws_cJSON_AddStringToObject(root_data,"Lon", gLrCurrgps.gps_info.lon); 
    ws_cJSON_AddStringToObject(root_data,"Lat", gLrCurrgps.gps_info.lat); 
    ws_cJSON_AddStringToObject(root_data,"Address", gLrCurrgps.address); 
    ws_cJSON_AddStringToObject(root_data,"Time", str_datetime); 



    postdata = ws_cJSON_PrintUnformatted(root_data);
	
	ws_cJSON_Delete(root_data);

	return postdata;

}

void CWatchService_http_gx_laoren_post_pos(ws_client *pme)
{   
    ws_base_t *userData = (ws_base_t*)pme->adapter->data;
    void *http_req=NULL;
    char *post_data=NULL;
    
    post_data = CWatchService_build_laoren_pos_post_data(userData->ws_reg.imei);

    WS_PRINTF("CWatchService_http_gx_laoren_post_pos(%s): %s\n", LAOREN_POS_SERVER_URL,post_data);
    http_req = ws_client_http_req_msg_create_POST(LAOREN_POS_SERVER_URL, CWatchService_http_gx_laoren_post_pos_CB, NULL, HEADER_JSON, post_data, strlen(post_data), 2);
    
    MMI_ModemAdp_SendHttpReq(pme, http_req, 0);

    free(post_data);
}

#endif

#if USE_RY_WIFI_GET_LAT_LON !=0
#define POST_HEADER_JSON     "Content-Type: application/json;charset=UTF-8\r\n"
#define LAOREN_POS_SERVER_URL "https://mwisdomcampus.szrytx.com/jurisdiction/locationResolve"
extern char *CWatchService_ry_get_wifi_lbs_convert_key();
static int CWatchService_rsp_lat_lon_cb(char * data, int len, int num, void *cbdata)
{
    char *rsp_data=NULL;
    WS_PRINTF("%s()  len=%d, num=%d\n",  __FUNCTION__, len, num);
    if(data == NULL && num == 0)
    {
        WS_PRINTF("%s() response_code=%d!\n", __FUNCTION__, len);
        return -1;
    }
	if(num < 0)
	{
		WS_PRINTF("%s() num<0!\n", __FUNCTION__);
		return -1;
	}
	
    WS_PRINTF("%s() :%s\n", __FUNCTION__, data);
	
	char *dParse=(char*) malloc(len+1);
	memcpy(dParse,data,len);
	dParse[len]=0;
	
	ws_cJSON *root = ws_cJSON_Parse(dParse);
	if(root){

		rsp_data = ws_cJSON_PrintUnformatted(root);
		
        WS_PRINTF("rsp_data:%s\n", rsp_data);
		ws_cJSON *code = ws_cJSON_GetObjectItem(root, "code");
		if(code && strcmp(code->valuestring,"200")==0){
			
			WS_PRINTF("success,ok!");
			ws_cJSON *data_s=ws_cJSON_GetObjectItem(root, "data");
			if(data_s){
				ws_cJSON *ret_s=ws_cJSON_GetObjectItem(data_s, "result");
				if(ret_s){
					ws_cJSON *location_s=ws_cJSON_GetObjectItem(ret_s, "location");
					if(location_s)
					{
						char str_locate[64]={0};
						//"113.8633295,22.6572572",
						WS_PRINTF("location_s:%s",location_s->valuestring);
						#if USE_WS_GX_TELECOM_LAOREN !=0
							snprintf(str_locate,64,"%s",location_s->valuestring);
							char *pos=strstr(str_locate,",");
							if(pos){
								strncpy(gLrCurrgps.gps_info.lon,str_locate,pos-str_locate);
								pos++;
								strcpy(gLrCurrgps.gps_info.lat,pos);
								WS_PRINTF("lon:%s , lat:%s",gLrCurrgps.gps_info.lon,gLrCurrgps.gps_info.lat);
								
								/*here convert gcj02 to bd09 start */
								gps_point_t point_bd;
								gps_gcj02_to_bd09(atof(gLrCurrgps.gps_info.lon),atof(gLrCurrgps.gps_info.lat),&point_bd);
								WS_PRINTF("111--bd09:lon:%f , lat:%f",point_bd.longitude,point_bd.latitude);
								snprintf(gLrCurrgps.gps_info.lon,WS_GPS_LONLAT_MAX_LEN ,"%.6f",point_bd.longitude);
								snprintf(gLrCurrgps.gps_info.lat, WS_GPS_LONLAT_MAX_LEN ,"%.6f",point_bd.latitude);
								/*here convert gcj02 to bd09 end */
								
								ws_cJSON *addr_s = ws_cJSON_GetObjectItem((ws_cJSON*)ret_s, "desc");
								if(addr_s){
									memset(gLrCurrgps.address,0,256);
									strncpy(gLrCurrgps.address,addr_s->valuestring,255);
								}
								
								CWatchService_http_gx_laoren_post_pos(MMI_ModemAdp_WS_GetClient());
							}
					#endif
					
					}
				}
			}

		
		}
		else
		{
			WS_PRINTF("fail:%d" ,code->valueint);
		}

		WS_PRINTF("%s--delete root!", __FUNCTION__);
		
		ws_cJSON_Delete(root);
    	free(rsp_data);
	}

	free(dParse);
	
    return 0;

}

char *CWatchService_build_wifi_post_data(char * req_imei,ws_location* location)
{
    char *postdata=NULL;
	char cellTower[64];
    WS_PRINTF("build_common_post_data(): imei=%s, type:%d\n", req_imei,location->cellType);

	 ws_cJSON *root=ws_cJSON_CreateObject();
	char m_ci[16];
	char m_lac[16];
	char m_mcc[16];
	char m_mnc[16];
	char rxlev[16];
		if(location->cellType ==  WS_CELL_TYPE_GSM)
		{
			ws_cJSON *cells = NULL;
			cells = ws_cJSON_CreateArray();
			for(int i=0; i<location->gsm.num; i++)
			{
				ws_cJSON *cell = NULL;
				
				cell = ws_cJSON_CreateObject();
				memset(m_ci,0,16);
				memset(m_lac,0,16);
				memset(m_mcc,0,16);
				memset(m_mnc,0,16);
				memset(rxlev,0,16);
				sprintf(m_ci,"%d",location->gsm.cells[i].cell_id);
				sprintf(m_lac,"%d",location->gsm.cells[i].lac);
				sprintf(m_mcc,"%d",location->gsm.cells[i].mcc);
				sprintf(m_mnc,"%d",location->gsm.cells[i].mnc);
				sprintf(rxlev,"%d",location->gsm.cells[i].rxlev);
				
				ws_cJSON_AddStringToObject(cell, "ci", m_ci);
				ws_cJSON_AddStringToObject(cell, "lac", m_lac);
				ws_cJSON_AddStringToObject(cell, "mcc",m_mcc);
				ws_cJSON_AddStringToObject(cell, "mnc", m_mnc);
				ws_cJSON_AddStringToObject(cell, "rxlev", rxlev);
				ws_cJSON_AddItemToArray(cells, cell);
			}
			ws_cJSON_AddItemToObject(root, "cells",cells);
		}
		else if(location->cellType ==  WS_CELL_TYPE_LTE)
		{
			ws_cJSON *cells = NULL;
			ws_cJSON *cell = NULL;
			cells = ws_cJSON_CreateArray();
			cell = ws_cJSON_CreateObject();
			memset(m_ci,0,16);
			memset(m_lac,0,16);
			memset(m_mcc,0,16);
			memset(m_mnc,0,16);
			memset(rxlev,0,16);
			sprintf(m_ci,"%d",location->lte.scell.cellid);
			sprintf(m_lac,"%d",location->lte.scell.tac);
			sprintf(m_mcc,"%d",location->lte.scell.mcc);
			sprintf(m_mnc,"%d", location->lte.scell.mnc);
			sprintf(rxlev,"%d",location->lte.scell.rxlev);
			ws_cJSON_AddStringToObject(cell, "ci", m_ci);
			ws_cJSON_AddStringToObject(cell, "lac", m_lac);
			ws_cJSON_AddStringToObject(cell, "mcc",m_mcc);
			ws_cJSON_AddStringToObject(cell, "mnc", m_mnc);
			ws_cJSON_AddStringToObject(cell, "rxlev", rxlev);
			ws_cJSON_AddItemToArray(cells, cell);
			for(int i=0; i<location->lte.num; i++)
			{
				cell = ws_cJSON_CreateObject();
				memset(m_ci,0,16);
				memset(m_lac,0,16);
				memset(m_mcc,0,16);
				memset(m_mnc,0,16);
				memset(rxlev,0,16);
				sprintf(m_ci,"%d",location->lte.cells[i].cellid);
				sprintf(m_lac,"%d",location->lte.cells[i].tac);
				sprintf(m_mcc,"%d",location->lte.cells[i].mcc);
				sprintf(m_mnc,"%d",location->lte.cells[i].mnc);
				sprintf(rxlev,"%d",location->lte.cells[i].mnc);
				ws_cJSON_AddStringToObject(cell, "ci", m_ci);
				ws_cJSON_AddStringToObject(cell, "lac", m_lac);
				ws_cJSON_AddStringToObject(cell, "mcc",m_mcc);
				ws_cJSON_AddStringToObject(cell, "mnc", m_mnc);
				ws_cJSON_AddStringToObject(cell, "rxlev", rxlev);
				ws_cJSON_AddItemToArray(cells, cell);
			}
			ws_cJSON_AddItemToObject(root, "cells",cells);
		}
	
   		ws_cJSON_AddStringToObject(root,"imei",req_imei);
    
		if(location->wifi->wifi_count>0)
		{
			ws_cJSON *wifis = NULL;
			wifis = ws_cJSON_CreateArray();
			for(int i=0; i<location->wifi->wifi_count; i++)
			{
				ws_cJSON *wf = NULL;
				
				wf = ws_cJSON_CreateObject();
				ws_cJSON_AddStringToObject(wf, "mac", location->wifi->wifs[i].mac);
				ws_cJSON_AddStringToObject(wf, "signal", location->wifi->wifs[i].rssi);
				ws_cJSON_AddStringToObject(wf, "ssid", location->wifi->wifs[i].ap_name);
				ws_cJSON_AddItemToArray(wifis, wf);
			}
			ws_cJSON_AddItemToObject(root, "wifis",wifis);
		}

    postdata = ws_cJSON_PrintUnformatted(root);
	
	ws_cJSON_Delete(root);

	return postdata;

}

void CWatchService_ry_http_get_lat_lon_via_wifilbs(ws_client *pme,ws_location* loc_data)
{   
    ws_base_t *userData = (ws_base_t*)pme->adapter->data;
    void *http_req=NULL;
    char *post_data=NULL;
    char url[128]={0};
    char sign[65];
    MD5_CTX pms;
    uint8_t md5[16];
	char *strTosign=malloc(1024*4);
	memset(strTosign,0,1024*4);
	
	char *signkey=CWatchService_ry_get_wifi_lbs_convert_key();
	
    watch_modem_get_imei_req(userData->ws_reg.imei);
	
    post_data = CWatchService_build_wifi_post_data(userData->ws_reg.imei,loc_data);

    WS_PRINTF("CWatchService_http_gx_laoren_post_pos(%s): %s\n", LAOREN_POS_SERVER_URL,post_data);
	sprintf(strTosign, "%s%s", post_data,signkey);
    WS_PRINTF("%s\n", strTosign);
	//make md5
    MD5Init(&pms);
    MD5Update(&pms, (uint8_t*)strTosign, strlen(strTosign));
    MD5Final(md5 ,&pms);
    for(int i=0; i<16; i++)
    {
        sprintf(sign+(i*2), "%02x", md5[i]);
    }
	
	sprintf(url,"%s/%s",LAOREN_POS_SERVER_URL,sign);
	
    WS_PRINTF("%s\n", url);
	
    http_req = ws_client_http_req_msg_create_POST(url, CWatchService_rsp_lat_lon_cb, NULL, HEADER_JSON, post_data, strlen(post_data), 2);
    
    MMI_ModemAdp_SendHttpReq(pme, http_req, 0);

    free(post_data);
	free(strTosign);
	
}

void CWatchService_ry_http_get_lat_lon_via_wifilbs_cb(ws_client *pme,ws_location* loc_data,void *callbacfunc)
{   
    ws_base_t *userData = (ws_base_t*)pme->adapter->data;
    void *http_req=NULL;
    char *post_data=NULL;
    char url[128]={0};
    char sign[65];
    MD5_CTX pms;
    uint8_t md5[16];
	char *strTosign=malloc(1024*4);
	memset(strTosign,0,1024*4);
	
	char *signkey=CWatchService_ry_get_wifi_lbs_convert_key();
    watch_modem_get_imei_req(userData->ws_reg.imei);
	
    post_data = CWatchService_build_wifi_post_data(userData->ws_reg.imei,loc_data);

    WS_PRINTF("CWatchService_http_gx_laoren_post_pos(%s): %s\n", LAOREN_POS_SERVER_URL,post_data);
	sprintf(strTosign, "%s%s", post_data,signkey);
    WS_PRINTF("%s\n", strTosign);
	//make md5
    MD5Init(&pms);
    MD5Update(&pms, (uint8_t*)strTosign, strlen(strTosign));
    MD5Final(md5 ,&pms);
    for(int i=0; i<16; i++)
    {
        sprintf(sign+(i*2), "%02x", md5[i]);
    }
	
	sprintf(url,"%s/%s",LAOREN_POS_SERVER_URL,sign);
	
    WS_PRINTF("%s\n", url);
	
    http_req = ws_client_http_req_msg_create_POST(url, callbacfunc, NULL, HEADER_JSON, post_data, strlen(post_data), 2);
    
    MMI_ModemAdp_SendHttpReq(pme, http_req, 0);

    free(post_data);
	free(strTosign);
	
}


#endif


#endif


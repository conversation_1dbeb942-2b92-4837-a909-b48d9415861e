/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/devices/transports/usb.mod/api/inc/usbappif.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2007/01/12 13:10:41 $
 ***************************************************************************
 * File Description: USB Application Programming Interface
 **************************************************************************/

#if defined (UPGRADE_USB)

#ifndef USBAPPIF_H
#define USBAPPIF_H

/*
 * Essential include files
 */
#include <kitqid.h>  /* TaskId type */

/*******************************************************************************
 * USB Interface structures & types
 ******************************************************************************/

/* Convenience structure for a unit of USB data being passed around.
 *
 * The term 'quantum' is used deliberately as the terms 'packet' and 'block'
 * have specific meanings within the USB code.  It is most likely to be a USB
 * packet, but is not guaranteed to be especially when the system is under
 * load and multiple packets may have coalesced in the buffers at the device
 * level.
 *  */
typedef struct UsbDataQuantumTag
{
  Int8        logicalEndpoint;    /* Associated endpoint */
  Int8*       dataPtr;           /* Address of start of the data */
  Int16       dataLength;        /* Length in bytes of the data */
  Int16       counterValue;      /* Integrity check value used by USB stack.
                                  * Keep with quantum. */
}
UsbDataQuantum;



/* Transmit Request component types */

/* UsbRequestSubmitMode specifies how a transmit data request is submitted to
 * the USB protocol stack - via a signal or directly by function call. */

typedef enum UsbRequestSubmitModeTag
{
  USB_SUBMIT_DIRECT,
  USB_SUBMIT_BY_SIGNAL
} UsbRequestSubmitMode;

typedef enum UsbRequestTransferModeTag
{
  USB_TRANSFER_BY_SINGLE_PACKET,
  USB_TRANSFER_BY_MULTI_PACKET
} UsbRequestTransferMode;

/* Transmit Request structure */

typedef struct UsbTransmitDataRequestTag
{
  Int16                    requestID;
  Int8*                    pData;
  Int32                    length;
  Int8                     logicalEndpoint;
  Int16                    fixedPacketSize;
  Int16*                   variablePacketSize;
  Boolean                  zlpRequired;
  TaskId                   taskID;
  UsbRequestSubmitMode     requestSubmitMode;
  UsbRequestTransferMode   requestTransferMode;
  Boolean                  noTransmitDataInd;
} UsbTransmitDataRequest;



/* Convenience structure to describe a Receive Buffer */
typedef struct UsbRxBufferTag
{
  Int8        logicalEndpoint; /* Associated endpoint */
  Int8*       startAddress;    /* Byte address of start of supplied buffer */
  Int16       length;          /* Length in bytes of the buffer */
  TaskId      taskID;          /* Associated task (if relevant)  */
}
UsbRxBuffer;

typedef enum UsbResetStatusTag
{
  USB_RESET_OK,
  USB_HW_CANNOT_RESET,
  USB_DID_NOT_RESET
}UsbResetStatus;

/*******************************************************************************
 * Global Function Prototypes
 ******************************************************************************/

/*
 * Transmit queuing
 */

/* Initialise a transmit queue.
 * Sends SIG_USB_INITIALISE_TRANSMIT_REQUEST_QUEUE_REQ to stack
 */
extern void usbAppifInitTxQueue( Int8 logicalEndpoint,
                                 UsbTransmitDataRequest* startAddress,
                                 Int8 queueLength,
                                 TaskId task );

/* Initialise a Transmit-request structure */
extern void usbAppifDefaultTransmitRequest( UsbTransmitDataRequest* request );

/* Request a transmission. */
extern void usbAppifTransmit( UsbTransmitDataRequest* request );

/* Flush the transmit-request queue */
extern void usbAppifFlushTransmitRequestQueue( Int8 logicalEndpoint );




/*
 * Receive Buffering
 */

/* Initialise a UsbRxBuffer structure to it's default state.
 */
extern void usbAppifDefaultRxBufferStructure( UsbRxBuffer* rxBufferDescription );

/* Initialise a receive buffer according to the supplied data parameters.
 * Sends SIG_USB_INITIALISE_RECEIVE_BUFFER_REQ to stack.
 */
extern void usbAppifInitRxBuffer( UsbRxBuffer rxBuffer );

/* Free a buffer quantum once it's finised with */
extern void usbAppifFreeRxBuffer( UsbDataQuantum dataQuantum );

/* Pause/Restart data flow from an Rx buffer */
extern void usbAppifPauseRxFlow( Boolean pause, Int8 logicalEndpoint );

/* Flush the receive buffer */
extern void usbAppifFlushRxBuffer( Int8 logicalEndpoint );


/*
 * Endpoint control
 */

/* Stall endpoints */
extern void usbAppifStallInEndpoint( Int8 logicalEndpoint );
extern void usbAppifStallOutEndpoint( Int8 logicalEndpoint );

/*
 * General convenience functions
 */

/* Obtain an arbitrary 12-bit value. */
extern Int16 usbGetRequestID(void);

/****************************************************************************
 * Semi-public functions.  These functions need to be exported from the
 * USB stack to support the non-signal interface, however, use of these means
 * that one task is calling functions within another which opens the risk of
 * real-time problems within the called task.
 *
 * DO NOT USE these UNLESS it is absolutely necessary.
 *
 * They are specifically intended for the EMMI-USB implementation where use
 * of signals is a problem.
 ****************************************************************************/

/* Release a section of the Rxbuffer.  [SIG_USB_RECEIVE_DATA_RSP]  */
extern void usbStackReleaseRxBuffer( UsbDataQuantum dataQuantum );

/* Add a Tx request to the Tx Request Queue */
extern void usbStackTransmitDataRequest( UsbTransmitDataRequest* request );

/* Flush the Tx Request Queue */
extern void usbStackFlushTransmitDataRequestQueue( Int8 logicalEndpoint );

/* Stall an endpoint */
extern void usbStackStallEndpointAddress( Int8 endpointAddress );

extern void usbAppifRequestReceiveData(Int8 logicalEndpoint);

extern void usbAppifRequestReceiveBufferFreeSpace( Int8 logicalEndpoint );


/* Name changes retained for backward compatibility for now. */

/* usbAppifRequestReceiveBufferFreeSpace was */
 extern void usbAppifCreateReceiveBufferFreeSpaceReq(Int8 logicalEndpoint);
/* usbAppifRequestReceiveData was */
 extern void usbAppifCreateReadDataReq(Int8 logicalEndpoint);

#endif /* defined (USBAPPIF_H) */
#endif /* defined (UPGRADE_USB) */
/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/devices/transports/usb.mod/pub/cfg/usbvendor.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/24 10:16:13 $
 **************************************************************************
 *  File Description :
 *    Constants specific to TTPCom Ltd and other Vendor-Specific descriptors.
 **************************************************************************/
#if defined (UPGRADE_USB)

#ifndef USBVENDOR_H
#define USBVENDOR_H

#if defined (USB_LIBRARY_MODULE)
#error "Include file contains client-configurable build switches"
#endif

/***************************************************************************
 * Vendor ID's
 ***************************************************************************/

/* USB-IF vendor ID for TTPCom Ltd. */
#define USB_VID_TTPCOM_LTD 0x0aeb




      

/***************************************************************************
 * Product ID's
 ***************************************************************************/

/*
 * TTPCom Ltd assigned product IDs.
 *
 * Using 3rd nybble to give broad type, and 4th for variants.
 *
 * In general, this sort of thing ought to be handled by the device class
 * and interface class codes, however, MS drivers tend to be selected on the
 * basis of this identifier rather than the device's class.
 *
 * As these point, in practice, to what driver shoud be used, the TTPCom
 * EMMI and TEST interfaces are described by the _TTPCOM PID
 */
#define USB_TTPCOM_PID_TTPCOM       0x0010 /* Implies TTPCom-specific drivers */
#define USB_TTPCOM_PID_COMPOSITE    0x0020 /* No device class; use Interface Classes */
#define USB_TTPCOM_PID_STORAGE      0x0030 /* Implies MS usbstor.sys */
#define USB_TTPCOM_PID_MODEM        0x0040 /* Implies MS usbser.sys */












      


/***************************************************************************
 * Vendor Classes, Sub-classes and Protocols
 ***************************************************************************/

/*
 * TTPCom Ltd assigned interface subclasses.
 */

#define USB_TTPCOM_INTERFACE_SUBCLASS_GENIE           0x0001
#define USB_TTPCOM_INTERFACE_SUBCLASS_TEST            0x0002

/*
 * TTPCom Ltd assigned interface protocols.
 */

/* Used by Genie interface. */
#define USB_TTPCOM_INTERFACE_PROTOCOL_NOT_USED        0x0000

/* Used by Genie interface. */
#define USB_TTPCOM_INTERFACE_PROTOCOL_EMMI            0x0001

/* The following protocols are used by the Test Interface.
 * The protocol number corresponds to the test number. */
#define USB_TTPCOM_INTERFACE_PROTOCOL_CTRL            0x0002
#define USB_TTPCOM_INTERFACE_PROTOCOL_INT             0x0003
#define USB_TTPCOM_INTERFACE_PROTOCOL_BULK            0x0004
#define USB_TTPCOM_INTERFACE_PROTOCOL_ISOC            0x0005
#define USB_TTPCOM_INTERFACE_PROTOCOL_INT_STREAMING   0x0006
#define USB_TTPCOM_INTERFACE_PROTOCOL_BULK_STREAMING  0x0007
#define USB_TTPCOM_INTERFACE_PROTOCOL_FULL_DUPLEX     0x0008

#endif /* USBVENDOR_H */
#endif /* UPGRADE_USB */
/* END OF FILE */

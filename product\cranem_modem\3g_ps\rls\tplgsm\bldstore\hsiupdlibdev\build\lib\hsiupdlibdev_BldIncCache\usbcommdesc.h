/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/devices/transports/usb.mod/pub/src/usbcommdesc.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/24 10:16:13 $
 **************************************************************************
 *  File Description :
 *    Device descriptors for the Communication Data Class interface.
 **************************************************************************/
#if defined(UPGRADE_USB)
#if defined(USB_COMMS_DEVICE_INTERFACE)

/* This file may be included multiple times in a descriptor set. */

/*******************************************************************************
 * Comms Device Class interface descriptors.
 * Two interfaces
 * 1. Comm Class, 1 endpoint
 * 2. Data Class, 2 endpoints
 ******************************************************************************/

  /*
   * Communication [Control] Interface
   */
  USB_INTERFACE_DESCRIPTOR(USB_COMM_INTERFACE_NUMBER,         /* bInterfaceNumber */
                           USB_COMM_ALTERNATE_NUMBER,         /* bAlternateSetting */
                           1,                                 /* bNumEndpoints */
                           USB_INTERFACE_CLASS_COMMS_DEVICE,  /* bInterfaceClass */
                           USB_INTERFACE_SUB_CLASS_CM_ABST,   /* bInterfaceSubclass */
                           USB_INTERFACE_PROTOCOL_V25TER,     /* bInterfaceProtocol */
                           USB_STRING_NOT_DEFINED_INDEX),     /* iInterface */


  /* Comms Class CS_INTERFACE 'Functional Descriptors' Triplet */
  /* 1. Header functional Desc */
  USB_CS_INTERFACE_FUNCTIONAL_DESCRIPTOR_LENGTH_HDR,    /* descriptor length */
  USB_CS_INTERFACE_FUNCTIONAL_DESCRIPTOR_TYPE,          /* descriptor type */
  USB_CS_INTERFACE_FUNCTIONAL_DESCRIPTOR_SUBTYPE_HDR,   /* subtype = header */
  0x00, 0x01,                                           /* BCD. Spec 1.0 */

  /* 2. Call Manangement Func Desc */
  USB_CS_INTERFACE_FUNCTIONAL_DESCRIPTOR_LENGTH_CMGMT,  /* descriptor length */
  USB_CS_INTERFACE_FUNCTIONAL_DESCRIPTOR_TYPE,          /* descriptor type */
  USB_CS_INTERFACE_FUNCTIONAL_DESCRIPTOR_SUBTYPE_CMGMT, /* subtype = Call management */
  (                                                     /* bmcapabilities */
    USB_COMM_BMCAP_CMGMT_DEVICE_HAS_CMGMT
  | USB_COMM_BMCAP_CMGMT_ALLOW_CMGMT_OVER_DATA_IF
    /* MS driver usbser.sys seems to ignore this bit and sends AT over the data
     * interface anyway. */
  ),
  USB_COMM_DATA_INTERFACE_NUMBER, /* Interface number of data class interface */

  /* 3. Abstract Control Func Desc */
  USB_CS_INTERFACE_FUNCTIONAL_DESCRIPTOR_LENGTH_ABCON,  /* descriptor length */
  USB_CS_INTERFACE_FUNCTIONAL_DESCRIPTOR_TYPE,          /* descriptor type */
  USB_CS_INTERFACE_FUNCTIONAL_DESCRIPTOR_SUBTYPE_ABCON, /* subtype = Abstract CM */
  (                                                     /* bmcapabilities */
    USB_COMM_BMCAP_ABCON_DEV_SUPPORTS_LINE_STATES
    | USB_COMM_BMCAP_ABCON_DEV_SUPPORTS_COMM_FEATURES
    /* | USB_COMM_BMCAP_ABCON_DEV_SUPPORTS_NETCON_NOTIFY */
    /* | USB_COMM_BMCAP_ABCON_DEV_SUPPORTS_SEND_BREAK    */
  ),

   USB_UNION_FUNCTIONAL_DESCRIPTOR_SIZE,             /* Size of descriptor. Number of interfaces plus 3 bytes of header */
   USB_CS_INTERFACE_FUNCTIONAL_DESCRIPTOR_TYPE,      /* 0x24 - CS_INTERFACE */
   USB_COMM_UNION_FUNCTIONAL_DESCRIPTOR_TYPE,        /* 0x06 - See table 25 of document "USB Class definitions for Comms Devices" */
  USB_COMM_INTERFACE_NUMBER,
  USB_COMM_DATA_INTERFACE_NUMBER,

  USB_ENDPOINT_DESCRIPTOR((USB_COMM_CTRL_IN_PHYSICAL_ENDPOINT
                           |USB_ENDPOINT_ATTR_DIRECTION_IN),  /* bEndpointAddress */
                          USB_ENDPOINT_TYPE_INTERRUPT,        /* bmAttributes */
                          USB_COMM_MAX_INTERRUPT_PACKET_SIZE, /* wMaxPacketSize */
                          128 /* msec */ ),  /* Interrupt endpoint poll interval */

  /*
   * Data Class Interface
   */
  USB_INTERFACE_DESCRIPTOR(USB_COMM_DATA_INTERFACE_NUMBER,    /* bInterfaceNumber */
                           USB_COMM_DATA_INTERFACE_ALTERNATE_NUMBER, /* bAlternateSetting */
                           2,                                 /* bNumEndpoints */
                           USB_INTERFACE_CLASS_DATA,          /* bInterfaceClass */
                           USB_INTERFACE_SUB_CLASS_ZERO,      /* bInterfaceSubclass */
                           USB_INTERFACE_PROTOCOL_ZERO,       /* bInterfaceProtocol */
                           USB_STRING_NOT_DEFINED_INDEX),     /* iInterface */

  USB_ENDPOINT_DESCRIPTOR((USB_COMM_DATA_IN_PHYSICAL_ENDPOINT
                           |USB_ENDPOINT_ATTR_DIRECTION_IN),  /* bEndpointAddress */
                          USB_ENDPOINT_TYPE_BULK,             /* bmAttributes */
                          64,                                 /* wMaxPacketSize */
                          0),                                 /* bInterval (n/a) */

  USB_ENDPOINT_DESCRIPTOR((USB_COMM_DATA_OUT_PHYSICAL_ENDPOINT
                           |USB_ENDPOINT_ATTR_DIRECTION_OUT), /* bEndpointAddress */
                          USB_ENDPOINT_TYPE_BULK,             /* bmAttributes */
                          64,                                 /* wMaxPacketSize */
                          0)                                  /* bInterval (n/a) */


#endif /* USB_COMMS_DEVICE_INTERFACE */
#endif /* UPGRADE_USB */
/* END OF FILE */

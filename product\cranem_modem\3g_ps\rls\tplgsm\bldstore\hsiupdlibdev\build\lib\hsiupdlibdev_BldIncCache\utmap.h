/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/tplgsm/utinc/utMap.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2006/02/09 12:25:17 $
 ***************************************************************************
 * File Description: Definitions for utility library map function.
 **************************************************************************/

#ifndef UT_MAP_H
#define UT_MAP_H

/**************************************************************************
 * Nested Include Files
 **************************************************************************/

#include <system.h>
#include <kernel.h>

/**************************************************************************
 * Manifest Constants
 **************************************************************************/

/**************************************************************************
 * Type Definitions
 **************************************************************************/

typedef Boolean (*UtCompareMapKey_t)(const void* keyA_p, const void* keyB_p);
typedef Int32   UtMapHandle_t;

/**************************************************************************
 * Macros
 **************************************************************************/

/**************************************************************************
 * Function Prototypes
 **************************************************************************/

/*--------------------------------------------------------------------------
 *
 * Function:     utMapCreate
 *
 * Parameters:   keyComparitor  - Pointer to function that can compare two
 *                                key values.  This is necessary since keys
 *                                are passed in as void* hence the library
 *                                has no way to determine the type.
 *
 * Returns:      UtMapHandle_t - unique identifier for map or 0 if creation
 *               of map failed.
 *
 * Description:  Create a new map.  For each map, it is necessary to provide 
 *               a pointer to a function that knows the format of the keys.
 *               This is necessary since keys are specified as void* and 
 *               hence there is no way to know what information is
 *               represented by the pointer.
 *-------------------------------------------------------------------------*/
UtMapHandle_t utMapCreate
(
  const UtCompareMapKey_t   keyComparitor
);

/*--------------------------------------------------------------------------
 *
 * Function:     utMapDelete
 *
 * Parameters:   map            - Handle of map (previously created with
 *                                utMapCreate())
 *
 * Returns:      TRUE if operation successful, FALSE otherwise.
 *
 * Description:  Delete a map previously created by utMapCreate().  Calling
 *               this function will empty the map and delete the map - the
 *               handle will become invalid.
 *-------------------------------------------------------------------------*/
Boolean utMapDelete
(
  const UtMapHandle_t map
);

/*--------------------------------------------------------------------------
 *
 * Function:     utMapInsert
 *
 * Parameters:   map            - Handle of map (previously created with
 *                                utMapCreate())
 *               key_p          - Key for this entry
 *               keySize        - Size of key (data pointed at by key_p) in 
 *                                bytes
 *               value_p        - Value for this entry
 *               valueSize      - Size of value (data pointed at by value_p) 
 *                                in bytes
 *
 * Returns:      TRUE if item succesfully inserted into the map, FALSE
 *               otherwise.
 *
 * Description:  Insert an item into the specified map.  Note that the 
 *               item (key & value) data will be copied into a dynamically
 *               allocated node.  Note also that if the key corresponds with
 *               an existing key, the existing value will be overwritten with
 *               the new one.
 *-------------------------------------------------------------------------*/
Boolean utMapInsert
(
  const UtMapHandle_t       map,
  const void*               key_p,
  const Int16               keySize,
  const void*               value_p,
  const Int16               valueSize
);

/*--------------------------------------------------------------------------
 *
 * Function:     utMapRemove
 *
 * Parameters:   map            - Handle of map (previously created with
 *                                utMapCreate())
 *               key_p          - Key to remove from map
 *
 * Returns:      TRUE if operation successful, FALSE otherwise.
 *
 * Description:  Delete a specified key entry from a map, previously added
 *               using utMapInsert().
 *-------------------------------------------------------------------------*/
Boolean utMapRemove
(
  const UtMapHandle_t map,
  const void*         key_p
);

/*--------------------------------------------------------------------------
 *
 * Function:     utMapEmpty
 *
 * Parameters:   map            - Handle of map (previously created with
 *                                utMapCreate())
 *
 * Returns:      TRUE if operation successful, FALSE otherwise.
 *
 * Description:  Empty a map of all it's key-value entries.  Unlike 
 *               utMapDelete(), this function will not destroy the map and it
 *               can be used again with the same handle.
 *-------------------------------------------------------------------------*/
Boolean utMapEmpty
(
  const UtMapHandle_t map
);

/*--------------------------------------------------------------------------
 *
 * Function:     utMapIsPresent
 *
 * Parameters:   map            - Handle of map (previously created with
 *                                utMapCreate())
 *               key_p          - Key to check the presence of
 *
 * Returns:      TRUE if key present, FALSE otherwise.
 *
 * Description:  Check to see if a key is present in a specified map.
 *-------------------------------------------------------------------------*/
Boolean utMapIsPresent
(
  const UtMapHandle_t map,
  const void*         key_p
);

/*--------------------------------------------------------------------------
 *
 * Function:     utMapSize
 *
 * Parameters:   map            - Handle of map (previously created with
 *                                utMapCreate())
 *
 * Returns:      The number of entries in a map.  If the map is non-existant
 *               or empty, the return value will be 0.
 *
 * Description:  Fetch the size of a map.
 *-------------------------------------------------------------------------*/
Int16 utMapSize
(
  const UtMapHandle_t map
);

/*--------------------------------------------------------------------------
 *
 * Function:     utMapAt
 *
 * Parameters:   map            - Handle of map (previously created with
 *                                utMapCreate())
 *               key_p          - Key to fetch associated value for
 *
 * Returns:      Pointer to key's value, or null if key doesn't exist.
 *
 * Description:  Fetch the value associated with a key.
 *-------------------------------------------------------------------------*/
const void* utMapAt
(
  const UtMapHandle_t map,
  const void*         key_p
);

#endif /* !defined (UT_MAP_H) */

/* END OF FILE */


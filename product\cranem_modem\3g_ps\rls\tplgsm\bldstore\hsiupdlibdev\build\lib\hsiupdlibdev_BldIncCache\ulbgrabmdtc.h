/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/******************************************************************************
 * File Name:   dlbgrabmdtc.h
 *
 *
 * File Description: DTC interface header file.
 *
 * Data Transfer Cipher (DTC) - is hardware accelerator for processing load for
 * data transfer and ciphering operations.
 *
 * DTC has the followings attributes:
 * ==================================
 *  1. F8 and F9 for UMTS R99 and R5, R6 features including HSDPA and HSUPA (E-DCH).
 *  2. Support of HSDPA category10 (13.92Mbps) and HSUPA category6(5.72Mbps).
 *  3. The DTC is based on usage of a DMA for Data transfers (to save core cycles
 *     used on data copying).
 *  4. DTC Minimize number of Cipher HW Interrupts in Ciphering driver/SW and
 *     reduce context switches and latency in ARM.  This is be done by using chained
 *     configuration with HW self reloading via DMA. (for HSDPA and HSUPA).
 *  5. DTC supports chained descriptors for concatenating data transactions.
 *  6. Data and configuration descriptors can be located in different locations in memory.
 *  7. DTC supports configuration of DTC for single block ciphering mode (for R99
 *     legacy support) -  direct addressing via SW.
 *  8. DTC allows different bit offset for input and output stream.
 *     a. INPUT_BIT_OFFSET (IBO) - determines the number of bits that are not taken into
 *        the ciphering or transfer operation.
 *     b. OUTPUT_BIT_OFFSET(OBO) - Determines the number of bits offset in the output
 *        stream. Data copy with output bit offset defined writes the output stream with
 *        this offset.
 *  9. Enable write & read operations from byte aligned addresses.
 *  10.Enable cipher bypassed mode - when ciphering is not used, the DTC can be used to
 *     copy PDUs from one memory location to another location including bit shifting.
 *  11.Continuous output mode for building PDUs.
 *     In this mode the SW provides the source PDU and the destination address for the
 *     MAC-E PDU head.  The DTC handles and tracks the destination address per MAC-es PDU
 *     and bit offset within the MAC-e PDU. (i.e. SW does not need to track the destination
 *     address and bit offset per MAC-es PDU within the MAC-e PDU).
 *     CONT_OUT_MODE is mandatory for HSUPA PDU building.
 *  12.Source or Target can be from DDR, DTCM, GB L2SRAM.
 *  13.DTC supports ciphering context switch to handle processing of higher priority
 *     chained ciphering. This is required due to HSUPA latency requirements.
 *  14.DTC supports Little/Big Endean output and input modes - mode configured by SW.
 *  15.DTC will start processing the chain once a valid configuration is available.
 *     (The SW can build the chain in parallel to the HW processing)
 *  16.DTC supports retaining the cipher state between two configurations.
 *  17.DTC supports ciphering to same address (Destination Dddress = Source Address)
 *  18.DTC operation can be done in blocking/non-blocking mode.
 *
 * DTC operation:
 * ==============
 *  1. Application should build linked descriptors chain containing the below
 *     information. the chain must be NULL terminated.
 *  2. Application can provide call-back function that will be called from HISR when
 *     DTC will finish the request processing.
 *  3. The DTC request contains the followings:
 *      - DTC operation (F8/F9).
 *      - Chain end event action (call-back function/interupt/none).
 *      - Priority (high/normal).
 *      - Blocking mode (blocking/non-blocking).
 *      - Direction (uplink/downlink).
 *      - Request ID - application request ID. Will moved to call-back function.
 *      - First descriptor pointer.
 *      - Callback function address (valid address/NULL for no call-back).
 *
 * Descriptor content:
 * ===================
 *  struct UdtcF8DescriptorTag *next_p; - Next descriptor pointer
 *  Int8                  *srcData_p;   - Source data address pointer
 *  Int8                  *dstData_p;   - Destination address pointer
 *  Int16                 bitLength;    - Total bit length
 *  Int8                  bearer;       - Radio bearer ID for F8
 *  Int8                  dtcModeReg;   -
 *  ----------------------------------------------------
 *  |   r   |   r   |   c   |   c   |   r   |    F8    |
 *  |   s   |   c   |   b   |   o   |   s   |  cipher  |
 *  |   v   |   s   |       |   n   |   v   |  offset  |
 *  |       |       |       |   t   |       |          |
 *  |  (1)  |  (1)  |  (1)  |  (1)  |  (1)  |   (3)    |
 *  ----------------------------------------------------
 *  F8 Cipher Offset (3 bits), Reserved (1 bit), F8 Continuous Output (1 bit),
 *  F8 Cipher Bypass (1 bit), F8 Retain Cipher State (1 bit), Reserved (1 bit)
 *
 *  Int32                 headerReg     -
 *  ---------------------------------------------
 *  |    HDR    | RLC   |   r   |  Header Data  |
 *  |    size   | Debug |   s   |               |
 *  |    ID     | Mark  |   v   |               |
 *  |    (4)    |  (1)  |  (5)  |     (22)      |
 *  ---------------------------------------------
 *  F8 Header Data (22 bits),  Reserved (5 bits),
 *  RLC Unfiltered Debug Mark (1Bit), F8 Header Size ID (4 bits)
 *
 *  Int16                 dstBitOffset; - F8 Output bit Offset
 *  Int16                 srcBitOffset; - F8 Input bit Offset
 *  Int32                 countC;       - Parameter for cipher
 *  Int8                  *key_p;       - Ciphering key address
 *
 *****************************************************************************/

#if !defined (ULBGRABMDTC_H)
#define       ULBGRABMDTC_H

/******************************************************************************
 * Include Files
 *****************************************************************************/
#include <psinterface.h>
//#include <urlc_sig.h>
#include <sn_sig.h>
#include <xrabmdata.h>
#include <ulbgrabmdata.h>
#include <ulbgupde.h>
#include <psinterface.h>
/* Added by zuohuaxu for CQXXX 20140421, begin */
#if defined (ON_PC)
#include <ltesnpdcpsig.h>
#endif
/* Added by zuohuaxu for CQXXX 20140421, end */


#define SB_BUF_OFFSET 4
#define KI_POOL0_HWM  500

void ulbgRabmHandleSnDataReqInState(XRabmEntity *sn,XRabmPdpEntity *pdp,UlbgUpdEntity *pde,SnDataReq *snDataReq);
void ulbgRabmSendSnDataReq(XRabmEntity *sn,XRabmPdpEntity *pdp,UlbgUpdEntity *pde,SnDataReq *snDataReq,SnUlSduList *srcPlatNode);
void ulbgRabmProcessSnDataReq(XRabmEntity *sn,XRabmPdpEntity *pdp,UlbgUpdEntity *pde,SnDataReq *snDataReq);
void ulbgRabmSplitLteSnMultiDataReq(XRabmEntity *sn,SnMultiDataReq *lteSnMultiDataReq);
void ulbgRabmProcessLteSnDataReq(XRabmEntity *sn,SnMultiDataReq *lteSnMultiDataReq);

Boolean ulbgSnBufferData (XRabmPdpEntity *pdp, SnDataReq *snDataReq, Npdu *npdu );
void ulbgSnDataReq (XRabmEntity *sn,XRabmPdpEntity *pdp,SnDataReq *snDataReq);
void ulbgSnUnitDataReq (XRabmEntity *sn,XRabmPdpEntity *pdp,SnUnitDataReq *snUnitDataReq);
void ulbgSnSplitLteSnMultiDataReq(XRabmEntity *sn,SnMultiDataReq *lteSnMultiDataReq);
void ulbgSnProcessLteSnDataReq(XRabmEntity *sn);
void ulbgReleaseSnMultiDataReq(SnMultiDataReq *SnMultiDataReq);
void ulbgReleaseSnMultiDataReqPerNsapi(EpsDataList *epsDataList);
snSnDataRet ulbgSnCheckSnMultiDataReq(XRabmEntity *sn,Nsapi nsapi);
void ulbgSnSendDataToLlc(XRabmEntity *sn,XRabmPdpEntity *pdp,EpsDataList *epsDataList);

#if defined (ON_PC)
void ulbgSnProcessEpdcpLoopBackSnDataReq(XRabmEntity *sn);
void ulbgSnEpdcpLoopBackSnDataReq(XRabmEntity *sn, LteLoopBackSnDataReq *snLoopBackSnDataReq);
void ulbgRabmEpdcpLoopBackSnDataReq(XRabmEntity *sn,LteLoopBackSnDataReq *snLoopBackSnDataReq);
#endif


#endif /* ULBGRABMDTC_H */


/* END OF FILE */

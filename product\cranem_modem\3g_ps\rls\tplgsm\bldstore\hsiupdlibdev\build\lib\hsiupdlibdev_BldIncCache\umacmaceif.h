/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/


#ifndef MACMACEIF_H
#define MACMACEIF_H
#if defined (UPGRADE_3G_EDCH)

/******************************************************************************
 * Include Files
 *****************************************************************************/
#include <ki_sig.h>
/******************************************************************************
 * Constants
 *****************************************************************************/
#define UMACE_MAX_MAC_DATA_FOR_MACE_ARRAY_SIZE  (4)
#define UMACE_NUM_OF_FRAMES_FOR_UPH_ESTIMATE    (10)

/******************************************************************************
 * Type Definitions
 *****************************************************************************/

//ICAT EXPORTED ENUM
typedef enum MacDataRecordSearchResultTag
{
    MAC_CFN_RECORD_FOUND,
    MAC_CFN_RECORD_NOT_FOUND
}MacDataRecordSearchResult;
//mac mace interface

//ICAT EXPORTED STRUCT
typedef struct UmacUlInfoForMacETag
{
    Int8    cfn;  //the key
	Int16   cmGapsBitmap;
	Int8    numCmGaps;
} UmacUlInfoForMacE;

//ICAT EXPORTED STRUCT
typedef struct MacDataRecordsForMacETag
{
    Int8 writeIdx;
    UmacUlInfoForMacE macDataForMacE[UMACE_MAX_MAC_DATA_FOR_MACE_ARRAY_SIZE];
    Boolean dchReadyToSend;
    Boolean isSrbDelayPeriodOver;
} MacDataRecordsForMacE;



/******************************************************************************
 * Global Variables
 *****************************************************************************/
extern UmacUlInfoForMacE umacUlInfoForMacE;

/******************************************************************************
 * Function Prototypes
 *****************************************************************************/
void UmacMacEMacIfInitialize(void);
void UmacMacEMacDataInit(void);
MacDataRecordSearchResult UmacMacEGetMacData(Int8 cfn, UmacUlInfoForMacE **macDataForMacE_pp);//to be called by MAC-E
void UmacMacESetMacData(Int8 cfn);//to be called by MAC
void UmacMacESetDchReadyToSend(Boolean dchReadyToSend);	// to be set by MAC 
Boolean UmacMacEGetDchReadyToSend(void);	// called by MACE 
void UmacMacESetIsSrbDelayPeriodOver(Boolean isSrbDelayPeriodOver); // set by MAC 
Boolean UmacMacEGetIsSrbDelayPeriodOver(void); // called by MAC-E 

#if defined (UPGRADE_UL_ECF)
Boolean UmacMacEPersistencyCheckFn(Int8 selectedASC, Int16 dynamicPersistenceLevel, Int16 persistenceScalingFactor);
Int32 UmacMacEGetBackoffTimerMsDurationFn(Int8 nb01Min, Int8 nb01Max);
#endif

Boolean UmacMacEDidDlMacEhsPduReceived(void);
#endif //UPGRADE_3G_EDCH
#endif //MACMACEIF_H


/******************************************************************************
 * * modem.h
 *
 * *(C) Copyright 2019 Asr International Ltd.
 * * All Rights Reserved
 * ******************************************************************************/
#ifndef _APP_ADAPTOR_INTERFACE_H_
#define _APP_ADAPTOR_INTERFACE_H_

#ifdef __cplusplus
extern "C" {
#endif /* __cpluscplus */

#ifdef LV_CONF_INCLUDE_SIMPLE
#include "lvgl.h"
#include "lv_watch_conf.h"
#else
#include "../../../lvgl/lvgl.h"
#include "../../../lv_watch_conf.h"
#endif

#if USE_LV_WATCH_APP_ADAPTOR != 0

enum
{
#if USE_LV_WATCH_WS_DUER != 0 || defined(__XF_BAIDU_AI__)
    WATCH_WEATHER_ID_SUNNY = 0,
    WATCH_WEATHER_ID_CLOUDY,
    WATCH_WEATHER_ID_OVERCAST,
    WATCH_WEATHER_ID_RAIN_AND_WIND,
    WATCH_WEATHER_ID_HAILSTONE_RAIN,
    WATCH_WEATHER_ID_THUNDER,
    WATCH_WEATHER_ID_WIND,
    WATCH_WEATHER_ID_SANDSTORM,
    WATCH_WEATHER_ID_LIGHT_RAIN,
    WATCH_WEATHER_ID_HEAVY_RAIN,
    WATCH_WEATHER_ID_LITTLE_SNOW,
    WATCH_WEATHER_ID_HEAVY_SNOW,
    WATCH_WEATHER_ID_FOG,
    WATCH_WEATHER_ID_HAZE,
#elif USE_LV_WATCH_WS_TIWATCH != 0
    WATCH_WEATHER_ID_SUNNY = 0,
    WATCH_WEATHER_ID_CLOUDY,
    WATCH_WEATHER_ID_OVERCAST,        //阴
    WATCH_WEATHER_ID_LIGHT_RAIN,
    WATCH_WEATHER_ID_HEAVY_RAIN,
    WATCH_WEATHER_ID_LITTLE_SNOW,
    WATCH_WEATHER_ID_HEAVY_SNOW,
    WATCH_WEATHER_ID_FOG,
    WATCH_WEATHER_ID_HAZE,
    WATCH_WEATHER_ID_WINDY,           //风
    WATCH_WEATHER_ID_THUNDER_SHOWER,  //雷阵雨
    WATCH_WEATHER_ID_SAND_STORM,      //沙尘暴
#elif USE_LV_WATCH_WEATHER_UI_128X96!=0
    WATCH_WEATHER_ID_SUNNY = 0,
    WATCH_WEATHER_ID_CLOUDY,
    WATCH_WEATHER_ID_LIGHT_RAIN,
    WATCH_WEATHER_ID_HEAVY_RAIN,
    WATCH_WEATHER_ID_LITTLE_SNOW,
    WATCH_WEATHER_ID_HEAVY_SNOW,
    WATCH_WEATHER_ID_FOG,
    WATCH_WEATHER_ID_HAZE,
    WATCH_WEATHER_ID_WINDY,           //风
    WATCH_WEATHER_ID_THUNDER_SHOWER,  //雷阵雨
    WATCH_WEATHER_ID_SHOWER,          //阵雨
    WATCH_WEATHER_ID_SAND_STORM,      //沙尘暴
    WATCH_WEATHER_ID_SLEET,           //雨夹雪
    WATCH_WEATHER_ID_SMOG,            //雾霾
    WATCH_WEATHER_ID_SUNNY_NIGHT,     //晴天夜晚
#else
    WATCH_WEATHER_ID_SUNNY = 0,
    WATCH_WEATHER_ID_CLOUDY,
    WATCH_WEATHER_ID_OVERCAST,        //阴
    WATCH_WEATHER_ID_LIGHT_RAIN,
    WATCH_WEATHER_ID_HEAVY_RAIN,
    WATCH_WEATHER_ID_LITTLE_SNOW,
    WATCH_WEATHER_ID_HEAVY_SNOW,
    WATCH_WEATHER_ID_FOG,
    WATCH_WEATHER_ID_HAZE,
    WATCH_WEATHER_ID_WINDY,           //风
    WATCH_WEATHER_ID_THUNDER_SHOWER,  //雷阵雨
    WATCH_WEATHER_ID_SHOWER,          //阵雨
    WATCH_WEATHER_ID_SAND_STORM,      //沙尘暴
    WATCH_WEATHER_ID_SLEET,           //雨夹雪
    WATCH_WEATHER_ID_SMOG,            //雾霾
    WATCH_WEATHER_ID_SUNNY_NIGHT,     //晴天夜晚
#endif    
    WATCH_WEATHER_ID_OTHERS = 99,
    WATCH_WEATHER_ID_NO_INFO = 255
};
typedef uint8_t watch_app_adp_weather_id_t;

#define WEATHER_INFO_LEN	128
#define WEATHER_INFO_FEATURE_LEN	256
typedef struct
{
    watch_app_adp_weather_id_t  weather_id;
    int8_t                     current_temperature;
    int8_t                     lowest_temperature;
    int8_t                     highest_temperature;
    uint8_t                    uv;
    uint16_t                   aqi;
    char                        city[32];
	char 						wind_der[32];
	char 						wind[32];
	char 						date[32];
#if USE_LV_WATCH_WEATHER_JSDX!=0
	char 						info[WEATHER_INFO_LEN];
	char 						info_feautre[WEATHER_INFO_FEATURE_LEN];
#endif	
} app_adaptor_weather_t;

typedef struct
{
    int8_t                     lowest_temperature;
    int8_t                     highest_temperature;
	char *dayweather;
	char *nightweather;
	char 						wind_der[32];
	char 						wind[32];
    char                        city[48];
} app_adaptor_weather_forecast_t;

enum {
    WATCH_PORTRAIT_ID_FATHER = 1,
    WATCH_PORTRAIT_ID_MOTHER,
    WATCH_PORTRAIT_ID_PATERNAL_GRADFATHER,
    WATCH_PORTRAIT_ID_PATERNAL_GRADMOTHER,
    WATCH_PORTRAIT_ID_MATERNAL_GRADFATHER,
    WATCH_PORTRAIT_ID_MATERNAL_GRADMOTHER,
    WATCH_PORTRAIT_ID_UNCLE,
    WATCH_PORTRAIT_ID_AUNT,
    WATCH_PORTRAIT_ID_BROTHER,
    WATCH_PORTRAIT_ID_SISTER,
    WATCH_PORTRAIT_ID_BOY = 21,
    WATCH_PORTRAIT_ID_GIRL = 22,
    WATCH_PORTRAIT_ID_FAMILY = 99,
    WATCH_PORTRAIT_ID_OTHERS = 100,
    WATCH_PORTRAIT_ID_SOS = 120,
#if USE_LV_WATCH_CYTEK4_UI !=1
#if defined(__XF_WS_VENDOR_PS_HJYHEX__)||defined(__XF_WS_VENDOR_CT_GUIZHOU__)
    WATCH_PORTRAIT_ID_CAB = 150,
#endif
#if USE_LV_WATCH_PHONEBOOK_SPLIT !=0
	WATCH_PORTRAIT_ID_SOS = 152,
#endif
#endif
    WATCH_PORTRAIT_ID_CUST = 160,
    WATCH_PORTRAIT_ID_NEED_DELETE = 254, /*xphone add for ct*/
    WATCH_PORTRAIT_ID_NO_INFO = 255,
    #if USE_LV_WATCH_KR_WEILIAO != 0
    WATCH_PORTRAIT_ID_OWNER_BOY,
    WATCH_PORTRAIT_ID_OWNER_GIRL,
    #endif
};
typedef uint8_t watch_app_adp_portrait_id_t;

#define WATCH_CONTACTS_MAX_NAME_LEN    (63 + 1) /*equal to NV_CONTACTS_MAX_NAME_LEN*/
#if defined(__XF_WS_VENDOR_CT_YNYD__)
#define WATCH_CONTACTS_MAX_NUMBER_LEN  (20 + 1) /*equal to NV_CONTACTS_MAX_NUMBER_LEN*/
#else
#define WATCH_CONTACTS_MAX_NUMBER_LEN  (63 + 1) /*equal to NV_CONTACTS_MAX_NUMBER_LEN*/
#endif
typedef struct {
    char contact_number[WATCH_CONTACTS_MAX_NUMBER_LEN];
    char contact_name[WATCH_CONTACTS_MAX_NAME_LEN]; /*English or Chinese depend on app's input*/
    uint8_t contact_type; /*0:administrator, 1:standby administrator, 2: others*/
    watch_app_adp_portrait_id_t portrait_id;
#if USE_LV_WATCH_WHITE_CONTACT_ONE_WITH_TIMERANGE!=0
	uint8_t	start_time1[NV_WS_STRING_LEN]; /*"15:30:00"*/
	uint8_t	end_time1[NV_WS_STRING_LEN]; /*"15:30:00"*/
#endif
	#if USE_LV_WATCH_WHITELIST_SHOW_ON_PB != 0
	uint8_t is_show;
	#endif
} app_adaptor_contact_t;


#define WATCH_CONTACTS_MAX_FILE_LEN      (60+1)

typedef struct {
    char friend_number[WATCH_CONTACTS_MAX_NUMBER_LEN];
    char friend_name[WATCH_CONTACTS_MAX_NAME_LEN]; /*English or Chinese depend on app's input*/
	char friend_imei[WATCH_CONTACTS_MAX_NUMBER_LEN];
	char friend_image[WATCH_CONTACTS_MAX_FILE_LEN];
	uint32_t time;
    uint8_t contact_type; /*0:administrator, 1:standby administrator, 2: others*/
    watch_app_adp_portrait_id_t portrait_id;
	int canVideo;
} app_adaptor_friend_t;

typedef struct
{
    hal_rtc_t date;
    uint32_t step;
    uint16_t kcal;
    float    km;
} app_adaptor_pedometer_record_t;

enum
{
    SLEEP_GOOD = 0,
    SLEEP_SECONDARY,
    SLEEP_BAD,
    SLEEP_TERRIBLE
};
typedef uint8_t watch_app_adp_sleep_t;

typedef struct
{
    uint8_t heartrate;
    uint8_t bloodoxygen;
    uint8_t sleep;
    uint8_t pressure;
}app_adaptor_health_info_t;

#define WATCH_ALARM_TOKEN_LEN_MAX      (32+1)
typedef struct
{
    uint8_t valid;                                                // 1: valid, 0: invalid
    uint8_t on_off;                                               // 1: alarm on, 0: alarm off
    uint8_t hour;                                                 // [0, 23]
    uint8_t min;                                                  // [0, 59]
    uint32_t alarm_id;
    uint8_t repeat_bitmap;                                        /* Bit 7: 0 means alarm once, 1 means repeat alarm */
    /* Bit 0 ~ 6 are valid if bit 7 is 1 */
    /* Bit 0: Sunday, bit 1: Monday ... bit 6: Saturday */
#if USE_LV_WATCH_SINGLE_ALARM_LIST != 0
    uint8_t token[WATCH_ALARM_TOKEN_LEN_MAX];
#endif
#if USE_LV_WATCH_SCHEDULE_POWERON_OFF!=0 ||  USE_LV_WATCH_SETTING_POWERON_OFF != 0 || USE_LV_WATCH_SPAIN != 0
	uint8_t type;												   // [0, 59]
#endif	
#if USE_LV_WATCH_ALARM_RING_OR_VIB_ENABLE!=0
	uint8_t ring_en;												  //0:disable
	uint8_t vib_en; 												 //0:disable
#endif
#if USE_LV_WATCH_ALARM_S2C_NAME!=0
	char name[40+1];
#endif
#if USE_LV_WATCH_ALARM_PARAM_CUST_ZHJ!=0
    uint16_t realert_delay; 
    uint8_t is_skip_holiday; 
	uint8_t repeat_str[16];
	uint8_t namegbk[WATCH_ALARM_TOKEN_LEN_MAX*2];
#endif
} app_adaptor_alarm_t;

#if USE_LV_WATCH_VOICE_MSG_YNYD != 0
#if USE_LV_WATCH_KR_WEILIAO != 0
#define APP_ADAPTOR_VOICE_MSG_YN_MAX_CONTACTS_NUM (NV_WATCH_MAX_FRIENDS_NUM+NV_WATCH_MAX_VOICE_MSG_GROUP_NUM+2)
#else
#define APP_ADAPTOR_VOICE_MSG_YN_MAX_CONTACTS_NUM 30
#endif
#endif

#if USE_LV_WATCH_ALARM_PARAM_CUST_ZHJ!=0
#define ALARM_MAX_HOLIDAYS_NUM 20
typedef struct
{
    uint16_t year;                                                // 1: valid, 0: invalid
    uint8_t month;                                               // 1: alarm on, 0: alarm off
    uint8_t day;                                                 // [0, 23]
}holiday_i_t;
#endif


#if USE_LV_WATCH_VOICE_MSG_YNYD != 0
#define APP_ADAPTOR_VOICE_MSG_YN_MAX_CONTACTS_NUM 30
#endif

#ifdef USE_WATCH_LITE
#define APP_ADAPTOR_VOICE_MSG_MAX_CHAT_MSG_NUM 8
#else
#define APP_ADAPTOR_VOICE_MSG_MAX_CHAT_MSG_NUM 20
#endif
#if USE_LV_WATCH_QRCODE_HEALTH != 0
#define HEALTH_QR_PARAMETER_SHORT_LEN	32
#define HEALTH_QR_PARAMETER_LONG_LEN	64
#define HEALTH_QR_PARAMETER_JKM_LEN	640
typedef struct 
{
	uint8_t is_valid;
	uint8_t is_update;
	char name[HEALTH_QR_PARAMETER_LONG_LEN];  //utf8,max 8 hanzi
	char idcardNo[HEALTH_QR_PARAMETER_SHORT_LEN]; //indentify number
    uint8_t open;  //be open to go through
    int  color;
	uint8_t traveltype; /*1-5*/
	char timeUpdate[HEALTH_QR_PARAMETER_SHORT_LEN]; //2022-04-22 07:11:22
	char timeValid[HEALTH_QR_PARAMETER_SHORT_LEN];
	char vaccine_time[HEALTH_QR_PARAMETER_SHORT_LEN];
	char vaccine_txt[HEALTH_QR_PARAMETER_LONG_LEN];
	uint8_t vaccine_color;
	char temperature_time[HEALTH_QR_PARAMETER_SHORT_LEN];
	char temperature_txt[HEALTH_QR_PARAMETER_LONG_LEN];
	uint8_t temperature_color;
	char hsjc_time[HEALTH_QR_PARAMETER_SHORT_LEN];
	char hsjc_ret[HEALTH_QR_PARAMETER_LONG_LEN];
	uint8_t hsjc_color;
	char jkm_txt[HEALTH_QR_PARAMETER_JKM_LEN]; //string array
	char travel_txt[HEALTH_QR_PARAMETER_JKM_LEN]; //string array
#if defined(__XF_WS_VENDOR_CT_LEZHI__)||defined(__XF_WS_VENDOR_CT_LEZHI_V2__)
	uint8_t vaccine_cnt;
	uint8_t vaccine_type;
	uint8_t hsjc_type;
	uint8_t hsjc_valid;
#endif
} app_adaptor_health_t;
#endif

#if USE_LV_YN_EMERGENCY != 0
#define YN_EMERGENCY_TITLE_LEN	64
#define YN_EMERGENCY_INFO_LEN	128
typedef struct 
{
	uint8_t is_valid;
	char title[YN_EMERGENCY_TITLE_LEN+1];  //utf8,max 8 hanzi
	char info[YN_EMERGENCY_INFO_LEN+1]; //indentify number
	int timeDelay;
	int timeShow;
} app_yn_emergency_t;
#endif

#if USE_LV_YN_PERSON_CODE != 0
#define YN_PERSON_TITLE_LEN	64
#define YN_PERSON_INFO_LEN	256
#define YN_PERSON_LIST_NUM	10
#define YN_PERSON_LIST_LEN	64

#define STR_PERSON_GET_TYPE_LIST	"get_type_list"
#define STR_PERSON_GET_QRCODE		"get_person_qrcode"
#define STR_PERSON_PUSH_SCAN_RET	"push_scan_result"
#define STR_PERSON_PSW_REST			"reset_password"
typedef struct
{
	char type[YN_PERSON_LIST_LEN+1];  //utf8,max 8 hanzi
	char name[YN_PERSON_LIST_LEN+1]; //indentify number
} app_yn_person_list_t;

typedef struct 
{
	uint8_t is_valid;
	uint8_t listnum;
	app_yn_person_list_t typelist[YN_PERSON_LIST_NUM];
	char title[YN_PERSON_TITLE_LEN+1];  //utf8,max 8 hanzi
	char info[YN_PERSON_INFO_LEN+1]; //indentify number
	double balance;
	char *per_code;
	uint16_t updatedur;
	uint8_t act_id; /*yn_person_act_type*/
	uint8_t status; /*1:SUCCESS , 2:FAIL*/
	uint8_t needPsw;
	uint8_t password[9];
	char *pOpenType;
	uint8_t OpenIdx;
} app_yn_person_t;

typedef enum{
ACT_GET_NONE=0,
ACT_GET_TYPELIST,
ACT_PUSH_QRCODE,
ACT_RESET_PSW,
ACT_SCAN_RET,
}yn_person_act_type;
#endif


#if USE_LV_WATCH_VOICE_MSG_YNYD != 0
#define APP_ADAPTOR_VOICE_MSG_YN_MAX_CONTACTS_NUM 30
#endif

#define APP_ADAPTOR_VOICE_MSG_MAX_CHAT_MSG_NUM 30
#if USE_LV_WATCH_QRCODE_HEALTH != 0
#define HEALTH_QR_PARAMETER_SHORT_LEN	32
#define HEALTH_QR_PARAMETER_LONG_LEN	64
#define HEALTH_QR_PARAMETER_JKM_LEN	640
typedef struct 
{
	uint8_t is_valid;
	uint8_t is_update;
	char name[HEALTH_QR_PARAMETER_LONG_LEN];  //utf8,max 8 hanzi
	char idcardNo[HEALTH_QR_PARAMETER_SHORT_LEN]; //indentify number
    uint8_t open;  //be open to go through
    int  color;
	uint8_t traveltype; /*1-5*/
	char timeUpdate[HEALTH_QR_PARAMETER_SHORT_LEN]; //2022-04-22 07:11:22
	char timeValid[HEALTH_QR_PARAMETER_SHORT_LEN];
	char vaccine_time[HEALTH_QR_PARAMETER_SHORT_LEN];
	char vaccine_txt[HEALTH_QR_PARAMETER_LONG_LEN];
	uint8_t vaccine_color;
	char temperature_time[HEALTH_QR_PARAMETER_SHORT_LEN];
	char temperature_txt[HEALTH_QR_PARAMETER_LONG_LEN];
	uint8_t temperature_color;
	char hsjc_time[HEALTH_QR_PARAMETER_SHORT_LEN];
	char hsjc_ret[HEALTH_QR_PARAMETER_LONG_LEN];
	uint8_t hsjc_color;
	char jkm_txt[HEALTH_QR_PARAMETER_JKM_LEN]; //string array
	char travel_txt[HEALTH_QR_PARAMETER_JKM_LEN]; //string array
#if defined(__XF_WS_VENDOR_CT_LEZHI__)
	uint8_t vaccine_cnt;
	uint8_t vaccine_type;
	uint8_t hsjc_type;
	uint8_t hsjc_valid;
#endif
} app_adaptor_health_t;
#endif
#if USE_LV_WATCH_QRCODE_HEALTH_FMAXK != 0
typedef struct 
{
	uint8_t is_valid;
	char name[HEALTH_QR_PARAMETER_LONG_LEN];  //utf8,max 8 hanzi
	char idcardNo[HEALTH_QR_PARAMETER_SHORT_LEN]; //indentify number
    uint8_t open;  //be open to go through
    int  color;
	char timeUpdate[HEALTH_QR_PARAMETER_SHORT_LEN]; //2022-04-22 07:11:22
	char timeValid[HEALTH_QR_PARAMETER_SHORT_LEN];
	char *jkm_txt; //string array
	uint8_t type; 
	uint32_t img_size;
	uint8_t dayValid; 
	} app_adaptor_health_fm_t;
#endif


#if USE_LV_WATCH_CALL_DURATION_LIMIT!=0
#define PHONE_LIMIT_CALL_IN 1
#define PHONE_LIMIT_CALL_OUT 2
#define PHONE_LIMIT_CALL_ALL 3
#define PHONE_LIMIT_CALL_COST_M 1
#define PHONE_LIMIT_CALL_COST_W 2
#define PHONE_LIMIT_CALL_COST_D 3
#define PHONE_LIMIT_CONTACT_ONLY_SOS 1
#define PHONE_LIMIT_CONTACT_LIMIT_ALL 2
#define PHONE_LIMIT_CONTACT_LIMIT_WHITE 3
#define PHONE_LIMIT_CONTACT_LIMIT_FAMILY 4

typedef struct
{
	uint8_t bEnbale;
	uint16_t daycost;
	uint32_t weekcost;
	uint32_t monthcost;
	uint8_t lastmon;
	uint8_t lastweek;
	uint8_t lastday;
	uint16_t limit_duration; //minutes
	uint8_t limittype ;/*1:Month,2:Week,3:day*/
	uint8_t calltype ;/*1:limit-in,2:limit-out,3:all-limit*/
	uint8_t limitcontact ;/*1:only sos,2:limit all,3:limit white number 4:limit family*/
} app_adaptor_call_duration_limit_t;
#endif


#if USE_LV_WATCH_LZ_YMT_QRCODE!=0 ||USE_LV_WATCH_LG_PAY_QRCODE!=0
typedef struct 
{
	uint8_t is_exist;
	char *qr_txt; //string array
} lz_eqrcode_info_t;
#endif

#if USE_LV_WATCH_LZ_LOCK_DEV!=0
#define DEVLOCK_TIP_LEN	128
typedef struct 
{
	uint8_t lock_type;
	char tip_txt[DEVLOCK_TIP_LEN]; //string array
} lz_devlock_t;

typedef enum{
LZ_DEVLOCK_NONE=0,
LZ_DEVLOCK_ARREARS,
LZ_DEVLOCK_LOSE,
LZ_DEVLOCK_SIM_CHANGE,
LZ_DEVLOCK_NUMBER_CANCEL,
LZ_DEVLOCK_DISMANTLE,
}lz_devlock_type;
lz_devlock_t * lz_devlock_get_data(void);
#endif

#if defined(__XF_WS_VENDOR_CT_SHXA__)
#define AQ_QUESTION_ID_LEN	20
typedef struct 
{
	uint8_t is_set;
	uint8_t time_out;  //minute
	char aq_id[AQ_QUESTION_ID_LEN+1]; //string array
} app_adaptor_aq_t;
#elif defined(__XF_WS_VENDOR_LYZH__)
#define AQ_QUESTION_ID_LEN	48
#define AQ_QUESTION_CHOICE_LEN	32
#define AQ_DT_NUM_MAX 10
typedef struct 
{
	char subject[AQ_QUESTION_ID_LEN+1]; 
	char id[AQ_QUESTION_ID_LEN+1]; 
	char choices[AQ_QUESTION_CHOICE_LEN+1]; 
	char user_choice[AQ_QUESTION_CHOICE_LEN+1]; 
} app_adaptor_aq_one_t;

typedef struct 
{
	uint8_t num;
	uint8_t currt_idx;
	app_adaptor_aq_one_t aqs[AQ_DT_NUM_MAX];
} app_adaptor_aq_t;

//USE_LV_WATCH_LYZH_HOMEWORK
#define SUBJECT_NUM_MAX 6
#define SUBJECT_NAME_LEN 18
#define WORK_NUM_MAX 30
#define WORK_ID_LEN	48

#define WORK_CONTENT_LEN	256
#define WORK_SOURCE_TIME 18
#define WORK_TIME_LEN 10
typedef struct 
{
	uint8_t is_done;/*wether student do complete*/
	char id[WORK_ID_LEN+1]; 
	char content[WORK_CONTENT_LEN+1]; 
	char source[WORK_TIME_LEN+1]; 
	char time[WORK_TIME_LEN+1]; 
} app_adaptor_work_one_t;

typedef struct 
{
	char name[SUBJECT_NAME_LEN];
	uint8_t qst_num;
	app_adaptor_work_one_t questions[WORK_NUM_MAX];
} app_adaptor_subject_one_t;

typedef struct 
{
	uint8_t num;
	uint8_t daybak;
	uint8_t curr_subject;
	bool need_save;
	app_adaptor_subject_one_t sujects[SUBJECT_NUM_MAX];
} app_adaptor_homwork_t;

#define WRONG_COTENT_LEN 64
#define	WRONG_CONTENT_LEN	128
#define WRONG_QST_NUM_MAX 100
#define WRONG_NUM_MAX 20

typedef struct 
{
	char name[WRONG_CONTENT_LEN+1]; 
	uint8_t is_makewrong;
} app_adaptor_wrong_one_t;

typedef struct 
{
	char name[SUBJECT_NAME_LEN];
	char title[WORK_CONTENT_LEN];
	char id[WORK_ID_LEN+1]; 
	uint8_t status;
	uint8_t cnt_num;
	app_adaptor_wrong_one_t contents[WRONG_QST_NUM_MAX];
} app_adaptor_wrong_subject_one_t;

typedef struct 
{
	uint8_t num;
	uint8_t curr_subject;
	bool need_save;
	app_adaptor_wrong_subject_one_t sujects[WRONG_NUM_MAX];
} app_adaptor_qst_wrong_t;

#endif

#if USE_LV_WATCH_YMBH_CMCC_NETTAQ !=0
#define AQ_QUESTION_ID_LEN	48
#define AQ_QUESTION_CHOICE_LEN	32
#define AQ_DT_NUM_MAX 30


//USE_LV_WATCH_LYZH_HOMEWORK
#define SUBJECT_NUM_MAX 20
#define SUBJECT_NAME_LEN 64
#define WORK_NUM_MAX 30
#define WORK_ID_LEN	48
#define WORK_TIME_LEN 20

#define QST_CONTENT_LEN	64
#define QST_CMM_LEN	32
#define QST_CMM_SHORT_LEN	16
typedef struct 
{
	uint16_t exerciseId; /*习题 id*/
	uint16_t parenExerciseId; /*父题 id*/
	char answer[QST_CONTENT_LEN]; /*习题学生答案单选、多选、判断、选择填空通过Answer 判断学生选择的选项*/
	char exerciseNumber[QST_CMM_SHORT_LEN]; /*题号*/
	char exerciseType[QST_CMM_LEN]; /*基础题型（中文），界面显示使用*/
	char pageName[QST_CMM_SHORT_LEN]; /*页码*/
	char categoryName[QST_CMM_LEN]; /*题类*/
	char eName[QST_CMM_SHORT_LEN]; /*基础题型（英文），判断题型使用*/
	uint8_t isRight; /*0=错，1=对，2=半对，3=待判断，4=未做，5=未提交*/
	char answerResult[QST_CONTENT_LEN];
	char user_choice[QST_CONTENT_LEN+1]; 
	uint8_t fillBlankAnswerCount; /*填空题空数量*/
	uint8_t choiceAnswerCount; /*选择题选项数量*/
	char choiceFillBlankAnswerOptionList[QST_CMM_SHORT_LEN];
	
} app_adaptor_qst_one_t;


typedef struct 
{
	uint8_t num;
	uint8_t currt_idx;
	uint32_t exercisePoolId;
	char exercisePoolName[SUBJECT_NAME_LEN];
	app_adaptor_qst_one_t qsts[WORK_NUM_MAX];
} app_adaptor_aq_t;

typedef struct 
{
	uint32_t hteId; /*作业 id*/
	char name[SUBJECT_NAME_LEN];	/* 作业名称*/
	char serialNo[SUBJECT_NAME_LEN]; /*序号*/
	uint8_t exerciseCount; /*习题数量*/
	uint8_t waitDoExerciseCount; /*未做习题数量，*/
	uint16_t subjectId; /*学科 id*/
	char subjectName[SUBJECT_NAME_LEN];	/* 学科名称*/
	uint8_t isSubmit;  /*是否提交*/
	uint8_t isMark;  /*是否可以标记*/
	uint8_t isMarkTime; /*是否标记时间 true:已标记 false:未标记*/
	char publishTime[WORK_TIME_LEN]; /*发布时间*/
	char startTime[WORK_TIME_LEN];
} app_adaptor_work_one_t;

typedef struct 
{
	uint8_t num;
	uint8_t curr_work;
	app_adaptor_work_one_t works[SUBJECT_NUM_MAX];
} app_adaptor_homwork_t;

#endif //USE_LV_WATCH_YMBH_CMCC_NETTAQ


#if USE_LV_WATCH_ZJ_BT_JUMP!=0
#define TRAIN_NUM_MAX 20
#define TRAIN_NAME_LEN 30
typedef struct 
{
	char exerciseName[TRAIN_NAME_LEN]; /*练习名称*/
	char exerciseMode[TRAIN_NAME_LEN]; /*练习模式：1：计时跳绳、2：计次跳绳、3：模拟考试*/
	char exerciseDuration[TRAIN_NAME_LEN];  /*练习时长*/
	char exerciseNum[TRAIN_NAME_LEN]; /*练习数量*/
	char exerciseId[TRAIN_NAME_LEN]; /*练习id*/
	char createUserType[TRAIN_NAME_LEN];  /*创建人类型，1标识教师，2标识家长*/
} app_adaptor_jump_train_one_t;
typedef struct 
{
	uint8_t num;
	uint8_t curr_train;
	app_adaptor_jump_train_one_t trains[TRAIN_NUM_MAX];
	char deviceName[TRAIN_NAME_LEN]; /*设备名称*/
	char deviceCode[TRAIN_NAME_LEN]; /*设备编号*/
} app_adaptor_jump_trainlist_t;
#endif


#if USE_LV_WATCH_OFFLINE_AT_NIGHT_ENABLE!=0 
typedef struct
{
	uint8_t     is_open;
	uint8_t     on_hour;
	uint8_t     on_min;
	uint8_t 	off_hour;
	uint8_t 	off_min; 
}app_adaptor_nightOffline_t;
#endif
#if USE_LV_WATCH_WS_SLEEP_TIME_CHECK!=0 
typedef struct
{
	uint8_t     on_off;
	uint8_t     on_hour;
	uint8_t     on_min;
	uint8_t 	off_hour;
	uint8_t 	off_min; 
}app_adaptor_reminder_t;
#ifdef SLEEP_TEST
typedef struct
{
	uint8_t sleep_hour;
	uint8_t sleep_min;
	int motion_count ;
	int step_count ;
	int arr_cout;
}app_adaptor_sleep_time_t;
#else
typedef struct
{
	uint16_t deep_count;
	uint16_t light_count;
	int step;
}app_adaptor_sleep_time_t;

#endif
#endif

#if USE_LV_WATCH_REPORT_FUNTION_USE_COUNT!=0
enum {
    FUNC_TYPE_USE_PEDOMETER = 0,
	FUNC_TYPE_USE_ALIPAY,
	FUNC_TYPE_USE_CLOCK,
	FUNC_TYPE_USE_CALENDAR,
	FUNC_TYPE_USE_MESSAGE,
	FUNC_TYPE_USE_CALL_RECORD,
	FUNC_TYPE_USE_HOMEWORK,
	FUNC_TYPE_USE_ANSWERCARD,
	FUNC_TYPE_USE_ADDRESSBOOK,
	FUNC_TYPE_USE_HEALTHCODE,
	FUNC_TYPE_USE_IDCODE,
};
#endif


enum {
    WATCH_VOICE_MSG_TYPE_TEXT = 0,
    WATCH_VOICE_MSG_TYPE_VOICE,
    WATCH_VOICE_MSG_TYPE_EXPRESSION,
    WATCH_VOICE_MSG_TYPE_PHOTO,
    WATCH_VOICE_MSG_TYPE_EMOJI_LIST,
#if USE_LV_WATCH_VOICE_MSG_YNYD!=0
    WATCH_VOICE_MSG_TYPE_CMD_CFM=10,
	WATCH_VOICE_MSG_SET_CONTACT_LIST,
	WATCH_VOICE_MSG_SET_SESSION_LIST,
	WATCH_VOICE_MSG_SET_SESSION_CONTENT, /*all is new*/
	WATCH_VOICE_MSG_SET_SESSION_CONTENT_ADD, /*increase*/
	WATCH_VOICE_MSG_SET_SESSION_VOICE_UPLOAD, /*increase*/
#endif
};
typedef uint8_t watch_app_adp_voice_msg_type_t;

typedef struct
{
    uint16_t year;
    uint8_t month;
    uint8_t day;
    uint8_t hour;
    uint8_t min;
} app_adp_time_and_date_t;


typedef struct
{
    uint8_t index;      /*indicate which unread voice message is read*/
    uint8_t * file;     /*WATCH_VOICE_DATA_TYPE_BUFFER: data buffer, WATCH_VOICE_DATA_TYPE_FILE: file path string*/
    uint32_t voice_len;
    uint32_t duration;
} app_adaptor_voice_msg_voice_t;

typedef struct
{
    uint32_t data_size;
    uint8_t * data;     /*WATCH_VOICE_DATA_TYPE_BUFFER: data buffer, WATCH_VOICE_DATA_TYPE_FILE: file path string*/
} app_adaptor_voice_msg_img_t;

typedef struct
{
    uint32_t cnt;
    uint32_t *list;     /*WATCH_VOICE_DATA_TYPE_BUFFER: data buffer, WATCH_VOICE_DATA_TYPE_FILE: file path string*/
} app_adaptor_emoji_list_img_t;


enum {
    WATCH_VOICE_MSG_FROM_UI = 0,
    WATCH_VOICE_MSG_FROM_OTHER_DEVICE,
};
typedef uint8_t watch_app_adp_voice_msg_direction_t;

enum {
    WATCH_VOICE_DATA_TYPE_BUFFER = 0,
    WATCH_VOICE_DATA_TYPE_FILE,
};
typedef uint8_t watch_app_adp_voice_msg_data_type_t;

typedef struct
{
    char * name;    /*only for message received in group chat*/
    watch_app_adp_voice_msg_direction_t direction;
    bool read_flag; /*true:read, false:unread*/
    app_adp_time_and_date_t time;
    watch_app_adp_voice_msg_type_t type;
    watch_app_adp_voice_msg_data_type_t data_type; /*WATCH_VOICE_DATA_TYPE_BUFFER: transfer data buffer,
                                                *WATCH_VOICE_DATA_TYPE_FILE: transfer file path*/
    union
    {
        char * text; /*WATCH_VOICE_DATA_TYPE_BUFFER: text string, WATCH_VOICE_DATA_TYPE_FILE: file path string*/
        app_adaptor_voice_msg_voice_t voice;
        app_adaptor_voice_msg_img_t   img; /*for expression or photo*/
        app_adaptor_emoji_list_img_t  emoji_list; /*for imoji list*/
    } content;

    void *node;
    
    char msg_id[64];
    
} app_adaptor_voice_msg_info_t;

enum {
    WATCH_VOICE_MSG_INVALID_CHAT = 0,
    WATCH_VOICE_MSG_SINGLE_CHAT,
    WATCH_VOICE_MSG_FAMILY_GROUP_CHAT,
    WATCH_VOICE_MSG_FRIENDS_GROUP_CHAT
};
typedef uint8_t watch_app_adp_voice_msg_chat_type_t;

typedef struct
{
    watch_app_adp_voice_msg_chat_type_t chat_type;  /*chat type: watch_app_adp_voice_msg_chat_type_t*/
    char * name;
    char * number;      /*not NULL if chat_type is WATCH_VOICE_MSG_SINGLE_CHAT, otherwise NULL*/
    char * imei;
    uint8_t index;
} app_adaptor_voice_msg_chat_id_t;

typedef struct
{
    watch_app_adp_voice_msg_chat_type_t chat_type;  /*chat type: watch_app_adp_voice_msg_chat_type_t*/
#if USE_LV_WATCH_VOICE_MSG_YNYD != 0
	uint8_t addcontent;
#endif
    char * name;
    char * number;      /*not NULL if chat_type is WATCH_VOICE_MSG_SINGLE_CHAT, otherwise NULL*/
    char * imei;
    uint8_t index;
    uint8_t count; /*count of messages*/
    app_adaptor_voice_msg_info_t * msg;
} app_adaptor_voice_msg_t;

enum {
    WATCH_VOICE_MSG_RESULT_SUCCESS = 0,
    WATCH_VOICE_MSG_RESULT_FAILURE
};
typedef uint8_t watch_app_adp_voice_msg_result_t;

typedef struct {
    uint16_t mcc;
    uint16_t mnc;
    uint16_t tac;
    uint16_t euarfcn;
    uint32_t cellid;
    uint32_t rsrp;
    uint32_t rsrq;
	uint32_t phycellid;
} app_adp_lte_scell_info_t;

typedef struct {
    uint8_t  index;
    uint16_t euarfcn;
    uint32_t phycellid;
    uint32_t rsrp;
    uint32_t rsrq;
	uint32_t cellid;
	uint32_t tac;
	int16_t  rxlev;
    uint16_t mcc;
    uint16_t mnc;
} app_adp_lte_ncell_Info_t;

typedef struct {
    uint16_t mcc;
    uint16_t mnc;
    uint16_t lac;
    uint16_t arfcn;
    uint8_t  bsic;
    uint32_t cell_id;
    uint8_t  rxlev;
} app_adp_gsm_cell_info_t;

#define APP_ADP_MAX_LTE_NCELL_NUM   6
#define APP_ADP_MAX_GSM_NCELL_NUM   6

typedef struct
{
    app_adp_lte_scell_info_t  scell;
    uint8_t                   encell_num;
    app_adp_lte_ncell_Info_t  encell[APP_ADP_MAX_LTE_NCELL_NUM];
    uint8_t                   gncell_num;
    app_adp_gsm_cell_info_t   gncell[APP_ADP_MAX_GSM_NCELL_NUM];
} app_adp_lte_cells_t;

typedef struct
{
    app_adp_gsm_cell_info_t   scell;
    uint8_t                   gncell_num;
    app_adp_gsm_cell_info_t   gncell[APP_ADP_MAX_GSM_NCELL_NUM];
    uint8_t                   encell_num;
    app_adp_lte_ncell_Info_t  encell[APP_ADP_MAX_LTE_NCELL_NUM];
} app_adp_gsm_cells_t;

enum {
    GET_CELL_INFO_ONCE_MODE,                         /*app only get cell info one time*/
    ENTER_CELL_INFO_AUTO_REPORT_MODE_OPEN,           /*enter the cell info changed auto-report mode*/
    EXIT_CELL_INFO_AUTO_REPORT_MODE                  /*exit the cell info changed auto-report mode*/
};
typedef uint8_t watch_app_adp_get_cell_info_mode_t;

enum {
    APP_SMS_SEND_SUCCESS = 0,                        /*app send sms success*/
    APP_SMS_SEND_FAIL                                /*app send sms fail*/
};
typedef uint8_t watch_app_adp_sms_send_result_type_t;

enum {
    APP_SMS_CODE_TYPE_UTF_8,                         /*smspdu decode to utf-8 type*/
    APP_SMS_CODE_TYPE_UTF_16,                        /*smspdu decode to utf-16 type*/
    APP_SMS_CODE_TYPE_8_BIT                          /*smspdu decode to 8bit type*/
};
typedef uint8_t watch_app_adp_sms_code_type_t;

typedef struct
{
    char * number;
    uint8_t number_len;
    char * time;
    uint8_t time_len;
    char * data;
    uint16_t data_len;
} app_adp_new_sms_info_ind_t;

enum
{
    APP_PLMN_RAT_GSM = 0,                                                 // GSM network
    APP_PLMN_RAT_UMTS,                                                    // UTRAN network
    APP_PLMN_RAT_LTE,                                                     // LTE network
    APP_PLMN_RAT_UNKNOW
};

typedef uint8_t watch_app_plmn_rat_type_t;

typedef struct {
    char regMnc[4];   ///register Mnc 3 character
    char regMcc[4];
    char homeMnc[4];   ///home Mnc 3 characters
    char homeMcc[4];
    int  signalstrength;
    int  lac;
    int  cid;
#define  INVALID_INT_MAX 0x7FFFFFFF
    int  realdbm;        //INVALID_INT_MAX is invalid value
    watch_app_plmn_rat_type_t  rat;
} app_adp_reg_and_home_plmn_info_t;

/* wifi begin*/
#define APP_ADP_WIFI_MAC_LEN           6
#if USE_LV_WATCH_YIXIN !=0
#define APP_ADP_WIFI_AP_MAX_NUM        30
#else
#define APP_ADP_WIFI_AP_MAX_NUM        20
#endif

enum {
    APP_ADP_WIFI_RESULT_SUCCESS = 0,
    APP_ADP_WIFI_RESULT_FAILURE,
    APP_ADP_WIFI_RESULT_TIMEOUT,
};
typedef uint8_t app_adp_wifi_result_t;

typedef struct {
    uint8_t mac[APP_ADP_WIFI_MAC_LEN];
    int32_t rssi;
} app_adp_wifi_ap_item;

typedef struct {
    uint8_t count;
    app_adp_wifi_ap_item item[APP_ADP_WIFI_AP_MAX_NUM];
} app_adp_wifi_ap_list;
/* wifi end*/

enum {
    APP_ADP_MEDCR_POSITION_LTE  = 46,
    APP_ADP_MEDCR_POSITION_UMTS = 47,
    APP_ADP_MEDCR_POSITION_GSM  = 48,
};
typedef uint8_t app_adp_medcr_position_t;

/* message begin */
typedef struct
{
    uint8_t msg_source; /* message source, 0 : SMS; 1 : WeChat */
    uint32_t msg_index; /* message index, use for deleting message */
    char * name;        /* messager name or number */
    char * data;        /* message content */
} app_adaptor_new_message_info_t;

enum {
    APP_ADP_MSG_SOURCE_SMS = 0,
    APP_ADP_MSG_SOURCE_WECHAT,
};
typedef uint8_t app_adp_msg_source_t;
/* message end */

#define ACTIVITY_WDAY_CNT    7   //from Sunday to Saturday
#define ACTIVITY_OCLOCK_CNT 16   //from 7:00 to 22:00

typedef struct
{
    uint32_t steps_goal; //steps
    uint32_t steps_today;
    uint32_t active_calorie_goal;    
    float   active_calorie_today;
    double active_distance_goal;   
    double active_distance_today;
    double calorie;  //kcal
    double distance; //km
    double stairs;   //m
    uint8_t active[ACTIVITY_OCLOCK_CNT];  //0 or 1
} app_adaptor_activity_info_t;

/**
 * get weather information
 * param (out) weather: app_adaptor_weather_t * 255 means no weather information
 * return  void
 */
void app_adaptor_set_weather_req(app_adaptor_weather_t *weather);
void app_adaptor_get_weather_req(app_adaptor_weather_t *weather);

/**
 * update phonebook
 * param (in) contactlist: app_adaptor_contact_t *
 * param (in) count: uint8_t
 * return  void
*/
void app_adaptor_update_phonebook_ind(app_adaptor_contact_t * contact_list, uint16_t count, uint16_t index_start, uint16_t index_end);

/**
 * get model id of the wathc
 * param (in) void
 * return  char
*/
char *app_adaptor_get_model_id(void);

/**
 * get version of the software
 * param (in) void
 * return  char
*/
char *app_adaptor_get_version(void);

/**
 * get cid for generating qrcode
 * param (in) void
 * return  char
*/
char *app_adaptor_get_cid_for_qrcode(void);

/**
 * get cid for generating barcode
 * param (in) void
 * return  char
*/
char *app_adaptor_get_cid_for_barcode(void);

/**
 * send the sos information to app
 * param (out) void
 * return  void
 */
void app_adaptor_send_sos_info_req(void);

#if USE_LV_WATCH_CT_NETTAQ_TCP != 0
bool app_adaptor_aq_commit_answer(char * data);
#endif

#if defined(__XF_WS_VENDOR_CT_YNYD__)
bool app_adaptor_aq_common_send_answer(char * data);
bool app_adaptor_aq_common_send_answer_bykey(int keyValue);
#endif
/**
 * update pedometer recent record
 * param (in) record: app_adaptor_pedometer_record_t *
 * return  void
 */
void app_adaptor_update_pedometer_record_ind(app_adaptor_pedometer_record_t* record);

/**
 * update pedometer current steps
 * param (in) record: app_adaptor_pedometer_record_t *
 * return  void
 */
void app_adaptor_get_pedometer_info_req(app_adaptor_pedometer_record_t * record);

/**
 * get health info
 * param (in) health: app_adaptor_health_info_t *
 * return  void
 */
void app_adaptor_get_health_info_req(app_adaptor_health_info_t * health);

/**
 * bind set monitor call back function
 * param (in) monitor: bool, true means on, false means off
 * return  void
 */
void app_adaptor_set_monitor(bool monitor);

/**
 * set alarm call back function
 * param (in) alarm: alarm_t *
 * param (in) count: uint8_t
 * return  void
 */
typedef void (*app_adaptor_set_alarm)(app_adaptor_alarm_t *alarm, uint8_t count);

/**
 * alarm ind back function
 * param (in) alarm: alarm_t *
 * param (in) count: uint8_t
 * return  void
 */
typedef void (*app_adaptor_alarm_ind)(app_adaptor_alarm_t *alarm, uint8_t count);

/**
 * bind set alarm call back function
 * param (in) func app_adaptor_set_alarm
 * return  void
 */
void app_adaptor_set_alarm_bind(app_adaptor_set_alarm func);

/**
 * bind alarm ind call back function
 * param (in) func app_adaptor_set_alarm
 * return  void
 */
void app_adaptor_alarm_ind_bind(app_adaptor_alarm_ind func);

/**
 * shutdown call back function
 * param (in) poweron_time: hal_rtc_t *
 * param (in) CallBack: AlarmCallback
 * return  void
 */
typedef void (*app_adaptor_shutdown)(hal_rtc_t *poweron_time);

/**
 * bind shutdown call back function
 * param (in) func app_adaptor_shutdown
 * return  void
 */
void app_adaptor_shutdown_bind(app_adaptor_shutdown func);

/**************for voice msg**************/
/**
 * receive a new message from other device
 * param (in) msg: app_adaptor_voice_msg_t *
 * return  void
 */
void app_adaptor_voice_msg_rcv_ind(app_adaptor_voice_msg_t * msg);

/**
 * send a new message from ui
 * param (in) msg: app_adaptor_voice_msg_t *
 * return  char *: file path string if voice data is written into file, otherwise NULL
 */
char * app_adaptor_voice_msg_send_req(app_adaptor_voice_msg_t * msg);

/**
 * result for sending the new message from ui
 * param (in) watch_app_adp_voice_msg_result_t
 * return  void
 */
void app_adaptor_voice_msg_send_cnf(watch_app_adp_voice_msg_result_t result);

/**
 * get all of the messages for the chat
 * param (in) id: app_adaptor_voice_msg_chat_id_t *
 * return  app_adaptor_voice_msg_t *
 */
app_adaptor_voice_msg_t * app_adaptor_voice_msg_get_msgs_req(app_adaptor_voice_msg_chat_id_t * id);

/**
 * inform that the unread voice message with index in the chat is read
 * param (in) id: app_adaptor_voice_msg_chat_id_t *
 * param (in) index: uint8_t, index in app_adaptor_voice_msg_voice_t
 * return  void
 */
void app_adaptor_voice_msg_read_voice_req(app_adaptor_voice_msg_chat_id_t * id, uint8_t index);

/**************for speech recog**************/
/**
 * network search from user for speech recog
 * param (in) voice_data: uint8_t *
 * param (in) voice_size: uint32_t
 * return  void
 */
void app_adaptor_speech_recog_nw_search_req(uint8_t * voice_data, uint32_t voice_size);

/**
 * return text through network search from user for speech recog
 * param (in) text: char *, network failed if txt is NULL
 * return  void
 */
void app_adaptor_speech_recog_nw_search_cnf(char * text);

/**
 * inform of text with voice from server for speech recog
 * param (in) text: char *
 * param (in) voice_data: uint8_t *
 * param (in) voice_size: uint32_t
 * return  void
 */
void app_adaptor_speech_recog_txt_msg_ind(char * text, uint8_t * voice_data, uint32_t voice_size);

/**
 * inform of text and index for audio from server for speech recog
 * param (in) audio_text: char *
 * param (in) audio_index: uint8_t, for playing the audio with index, to be confirmed
 * return  void
 */
void app_adaptor_speech_recog_audio_msg_ind(char * audio_text, uint8_t audio_index);

/**
 * request to play the audio with index from serverfor speech recog
 * param (in) audio_index: uint8_t, for playing the audio with index
 * return  void
 */
void app_adaptor_speech_recog_play_audio_req(uint8_t audio_index);

/**
 * start to play the audio
 * param (in) total_sec: uint16_t, total time for audio in seconds, not zero
 * return  void
 */
void app_adaptor_speech_recog_play_audio_cnf(uint16_t total_sec);

/**
 * request to stop the audio with index
 * param (in) audio_index: uint8_t, for stopping the audio with index
 * return  void
 */
void app_adaptor_speech_recog_stop_audio_req(uint8_t audio_index);

/**
 * playing time for audio
 * param (in) cur_sec: uint16_t, in seconds
 * return  void
 */
void app_adaptor_speech_recog_play_audio_ind(uint16_t cur_sec);

/**
 * get cell information request
 * param (in) watch_app_adp_get_cell_info_mode_t mode:refer to
 * the define of "watch_app_adp_get_cell_info_mode_t"
 * return  void
*/
void app_adaptor_get_cell_info_req(watch_app_adp_get_cell_info_mode_t mode);

/**
 * lte cell information indication call back function
 * param (in) cell_list: app_adp_lte_cells_t
 * return  void
 */
typedef void (*app_adaptor_lte_cell_info_ind)(app_adp_lte_cells_t *cell_list);

/**
 * bind lte cell information indication call back
 * param (in) func app_adaptor_shutdown
 * return  void
 */
void app_adaptor_lte_cell_info_ind_bind(app_adaptor_lte_cell_info_ind func);

/**
 * GSM cell information indication call back function
 * param (in) cell_list: app_adp_lte_cells_t
 * return  void
 */
typedef void (*app_adaptor_gsm_cell_info_ind)(app_adp_gsm_cells_t *cell_list);

/**
 * bind GSM cell information indication call back
 * param (in) func app_adaptor_lte_cell_info_ind
 * return  void
 */
void app_adaptor_gsm_cell_info_ind_bind(app_adaptor_gsm_cell_info_ind func);

/**
 * SMS send result call back function
 * param (in) cell_list: app_adp_lte_cells_t
 * return  void
 */
typedef void (*app_adaptor_sms_send_result_ind)(watch_app_adp_sms_send_result_type_t result);

/**
 * send a short message(data encode with utf-8) from app
 * param (in) number: char *
 *            data: char *
 *            datalen:uint32_t,the length without '\0'
 *            smsc_number: char *
 *            app_adaptor_sms_send_result_ind func:the call back for sms send result
 * return  uint8_t:1 is correct,0 is incorrect
*/
uint8_t app_adaptor_sms_send_req(char * number, char * data, uint32_t datalen, char * smsc_number, app_adaptor_sms_send_result_ind func);

/**
 * send a short message(data encode with utf-16) from app
 * param (in) number: char *
 *            data: char *
 *            datalen:uint32_t,the length without '\0'
 *            smsc_number: char *
 * return  uint8_t:1 is correct,0 is incorrect
*/
uint8_t app_adaptor_sms_send_with_utf16_req(char * number, char * data, uint32_t datalen, char * smsc_number, app_adaptor_sms_send_result_ind func);

/**
 * send a short message(data encode with 8 bit) from app
 * param (in) number: char *
 *            data: char *
 *            datalen:uint8_t,the length without '\0'
 *            smsc_number: char *
 *            app_adaptor_sms_send_result_ind func:the call back for sms send result
 * return  uint8_t:1 is correct,0 is incorrect
*/
uint8_t app_adaptor_sms_send_with_8bit_req(char * number, char * data, uint8_t datalen, char * smsc_number, app_adaptor_sms_send_result_ind func);

/**
* new sms information indication call back function
* param (in) sms_info: app_adp_new_sms_info_ind_t *
* return  void
*/
typedef void (*app_adaptor_new_sms_info_ind)(app_adp_new_sms_info_ind_t *sms_info);

/**
 * bind new sms information indication call back
 * param (in) func app_adaptor_new_sms_info_ind
 *            decode_type:APP_SMS_CODE_TYPE_UTF_8 or APP_SMS_CODE_TYPE_UTF_16
 * return  void
 */
void app_adaptor_new_sms_info_ind_bind(app_adaptor_new_sms_info_ind func, watch_app_adp_sms_code_type_t decode_type);

/**
 * bind new 8-bit sms information indication call back
 * param (in) func app_adaptor_new_sms_info_ind
 * return  void
 */
void app_adaptor_new_8bit_sms_info_ind_bind(app_adaptor_new_sms_info_ind func);

/**
 * get ICCID request
 * param (in) void:
 * return  char *ICCID:if null means no sim or ICCID not present
*/
char* app_adaptor_get_iccid_info_req(void);

/**
 * get Plmn request
 * param (in) void:
 * return  app_adp_reg_and_home_plmn_info_t* plmn:it contains register plmn and home plmn,
 * if plmn are 0,it means invaild.
*/
app_adp_reg_and_home_plmn_info_t* app_adaptor_get_plmn_info_req(void);

/**
 * get phone number request
 * param (in) void:
 * return  char * phonenumber:if null means no sim or phone number not present
*/
char* app_adaptor_get_msisdn_info_req(void);

/**
 * wifi scan call back function
 * param (in) result: app_adp_wifi_result_t
 * param (in) ap_list: app_adp_wifi_ap_list *
 * return  void
 */
typedef void (* app_adp_wifi_scan_cb)(app_adp_wifi_result_t result, app_adp_wifi_ap_list * ap_list);

/**
 * wifi scan request
 * param (in) scan_rounds: range 1-3, rounds of wifi scan
 * param (in) max_hotspot_number: range 4-10, maximum number of wifi hotspots reported
 * param (in) timeout_seconds: range 1-25, timeout value to scan wifi in seconds
 * param (in) priority: 0, data traffic has higher priority than wifi scan,
 *                         data traffic will interrupt the wifi scan;
 *                      1, data traffic has lower priority than wifi scan,
 *                         data traffic will be blocked during wifi scan
 * param (in) cb: wifi scan call back function
 * return  uint8_t
 */
uint8_t app_adaptor_wifi_scan(uint8_t scan_rounds,
                              uint8_t max_hotspot_number,
                              uint32_t timeout_seconds,
                              uint8_t priority,
                              app_adp_wifi_scan_cb cb);

/**
 * get sim status
 * param (in) void:
 * return bool: 0: sim not present; 1: sim present
*/
uint8_t app_adaptor_get_sim_status_req(void);

/*
 * set Neighbour Bch query option
 * param   option: 0/1
 * return  void
 */
void app_adaptor_set_ncell_bch_req(UINT8 option);

/**
 * radio power status call back function
 * param (in) status: uint8_t, 1(+CFUN:1), 4(+CFUN:4)
 * return  void
 */
typedef void (* app_adp_radio_power_cb)(uint8_t status);

/**
 * get radio power status(AT+CFUN?)
 * param (in) cb: app_adp_radio_power_cb
 * return void
 */
void app_adp_get_radio_power_status_req(app_adp_radio_power_cb cb);

/**
 * request radio power(AT+CFUN=1 or AT+CFUN=4)
 * param (in) onoff: uint8_t, 1(AT+CFUN=1), 0(AT+CFUN=4)
 * return void
 */
void app_adp_radio_power_req(uint8_t onoff);

/**
 * fast dormancy request
 * param (in) void:
 * return void
*/
void app_adaptor_fast_dormancy_req(void);

/**
 * drx pch request
 * param (in) position: app_adp_drx_pch_position_t
 * param (in) config_val: 1, 2, 4, 8, 16, 32
 * return void
*/
void app_adaptor_drx_pch_req(app_adp_medcr_position_t position, uint8_t config_val);

/**
 * enable/disable LTE fast dormancy
 * param (in) time_value: (unit: second), 0 for disable.
 * return void
*/
void app_adaptor_enable_lte_fast_dormancy_req(uint8_t time_value);

/**
 * get roaming status
 * param (in) void
 * return uint8_t: 0 not roaming, 1 roaming
*/
uint8_t app_adp_get_roaming_stat_req(void);

/**
 * get IMSI of sim
 * param (out)  imsi: char*
 * return  void
 */
void app_adp_get_imei_req(char * imei);

/**
 * get IMEI of watch
 * param (out) imei: char*
 * return  void
 */
void  app_adp_get_imsi_req(char * imsi);

/* message begin */
/**
* new message information
* param (in) msg_info: app_adaptor_new_message_info_t *
* return  void
*/
void app_adaptor_update_message_info_req(app_adaptor_new_message_info_t * msg_info);

/**
* delete message information since message deleted by user or message read by user
* param (in) index: uint32_t
* return  void
*/
void app_adaptor_delete_message_info_req(uint32_t index);
/* message end */

/**
 * get activity info
 * param (in) activity: app_adaptor_activity_info_t *
 * return  void
 */
void app_adaptor_get_activity_info_req(app_adaptor_activity_info_t * activity);

/**
 * qq new msg ind
 * param (in) msg_type: 0-new msg; 1-new friend
 * return  void
 */
void app_adaptor_qq_new_message_ind(uint8_t msg_type);

#endif /*USE_LV_WATCH_APP_ADAPTOR*/

#ifdef __cplusplus
}
#endif /* __cpluscplus */

#endif /* end of _APP_ADAPTOR_INTERFACE_H_ */

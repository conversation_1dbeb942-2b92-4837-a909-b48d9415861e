/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/2g.mod/api/shinc/mm_types.h#3 $
 *   $Revision: #3 $
 *   $DateTime: 2007/02/02 13:54:44 $
 **************************************************************************
 * File Description :
 *
 * Contains definitions of GSM MM data structures, including MM's
 * description.
 **************************************************************************/

#ifndef UMM_TYPES_H
#define UMM_TYPES_H

/***************************************************************************
 * Nested Include Files
 **************************************************************************/
#include <gmmrasig.h>
#include <rrc_typ.h>
#include <sml3_typ.h>
#include <lterrcnasinterface.h>

#include <mnl3_typ.h>
#include <lteUtraGsmIratItf.h>
#include <emm_typ.h>
#if defined (UPGRADE_LTE)
#include    <emm_esm_sig.h>
#endif
/***************************************************************************
 * Manifest Constants
 **************************************************************************/
#if defined(UPGRADE_VOLTE)
#define IMS_SRV_TYPE_REGISTER_GROUP       0x01
#define IMS_SRV_TYPE_SERVICE_GROUP        0x10
#define IMS_SRV_TYPE_MO_CALL              0x01
#define IMS_SRV_TYPE_SMS                  0x10
#define IMS_SRV_TYPE_SS                   0x20
#define IMS_SRV_TYPE_ECALL                0x40
#define IMS_SRV_TYPE_MT_CALL              0x80
#define POC_SRV                           0XA0 //add by xyma for **********
#endif


#if defined(UPGRADE_DSDS)

//ICAT EXPORTED ENUM
typedef enum DualIsWaitRsumeCnfTag
{
    UMM_NO_NEED_IDLE,
    UMM_NO_NEED_UMTS_IDLE_IRAT_FAIL,
    UMM_NO_NEED_CANCEL_SERVICE,
    UMM_NEED_WAIT
} DualIsWaitRsumeCnf;


//ICAT EXPORTED STRUCT
typedef struct DualMmflagTag
{
    Int16       taskId;
    Boolean     bStartLau;
    Boolean     bStartRauAttach;
    Boolean     bRauAttachReqHasBeenSendout;
    Boolean     bStartPs;
    Boolean     bStartCs;
    Boolean     bStartIRATReselection;
    DualIsWaitRsumeCnf  dualIsWaitRsumeCnf;
    Int8                dualServiceComplete;
} DualMmflag;
#endif

#define MAX_T3346_RUNNING_SYSTEM_NUMBER   3
typedef struct T3346StartedSystemTag
{
    Boolean         systemUsed;
    Plmn            plmnId;
    TimerStateType state;
    Boolean         moExcept; //add by xyma for **********
}
T3346StartedSystem;

/**\var MmMode
* \brief Holds list of possible technology modes.
*/
//ICAT EXPORTED ENUM
typedef enum UmmModeTag
{
    UMM_MODE_LTE,
    UMM_MODE_NULL
} UmmMode;

//ICAT EXPORTED ENUM
typedef enum ServiceReqReasonTag
{
    SR_NONE,
    SR_RABM_REESTAB,
    SR_SM_PROC,
#if defined (SUPPORT_SMS)
    SR_SMS_PROC,
#endif
#if defined(UPGRADE_APP_INFO)//CQ00144739
    SR_SS_PROC,
    SR_ABSS_PROC,
#endif
    SR_PAGING_RESPONSE
#if defined (UPGRADE_LTE)
    ,SR_CS_FALLBACK,
    SR_PAGING_RES_FOR_EPS,
    SR_PAGING_RES_FOR_CS_FALLBACK
#endif
}
ServiceReqReason;

typedef Int16 ServiceReqTriggerMask; /**< Service Request triggers bitmask. According to ServiceReqTrigger ENUM */



typedef enum FollowOnTag  /* used to track follow on request and grant */
{
    FO_NONE,
    FO_REQUESTED,
    FO_GRANTED,
    FO_NOT_GRANTED
}
FollowOn;

//ICAT EXPORTED ENUM
typedef enum MmAsUpdateTypeTag
{
    MM_AS_NO_UPDATE,
    MM_AS_UPDATE_REQUIRED
#if defined (UPGRADE_R99)
    , MM_AS_UPDATE_EPLMN
#endif
}
MmAsUpdateType;

typedef enum MmTimerIdTag
{
    NO_TIMER,
#if defined (UPGRADE_3G)
    T3218,
    T3214_T3216,
    T3316,
    T3318_T3320,
    T3323,
#if !defined (UPGRADE_EXCLUDE_2G)
    RESEL_CELL_VALIDITY,
#endif
#endif
#if defined (UPGRADE_GPRS)
    GMM_TIMER,
    T3302,
    T3311,
    T3312,
    T3314,
    HOLD_OFF_TIMER,
#endif
#if defined (UPGRADE_LTE)
    EMM_TIMER, /*T3410, T3417, T3417ext, T3421, T3430*/
    T3402_FOR_SYSTEM_ONE,
    T3402_FOR_SYSTEM_TWO,
    T3402_FOR_SYSTEM_THREE,
    T3411,
    T3412,
    T3416,
    T3418_T3420,
    T3423,
    T3440,
    T3442,
    T3346_FOR_SYSTEM_ONE,
    T3346_FOR_SYSTEM_TWO,
    T3346_FOR_SYSTEM_THREE,
    THROTTLING_TIMER_FOR_SYSTEM_ONE,
    THROTTLING_TIMER_FOR_SYSTEM_TWO,
    THROTTLING_TIMER_FOR_SYSTEM_THREE,
    FORBIDDEN_TA_TIMER,
    TAU_REJECT_NEED_ATTACH_TIMER,
    ENABLE_EUTRA_TIMER,
    CS_MT_CALL_TIMER,
    WAIT_FOR_ERRC_ACT_CNF_TIMER,
#if defined (CRANE_PLATFORM)
    FLUSH_FLASH_PROTECT_TIMER,
#endif
#if defined(UPGRADE_DSDS)
    PS_PAGING_PROTECTION_TIMER,
    TAU_PROTECTION_TIMER,
    WAIT_RRC_RELEASE_IND_TIMER,
#endif
#endif
    T3211_T3213,
    T3212,
    T3214,
    T3215,
    T3340,
    T3242,
    T3243,
    ECALL_UPDATE_NVM_FREQ,
    T3246
    ,TDMM_RETRY_SUSPEND /*Modify by qinglanwang, CQ00118173, 20200116 */
    ,RABM_REESTABLISH_REQ_TIMER
    ,T3324
    ,T3448
    ,LONG_EDRX_TIMER
    ,IMS_SERVICE_END_PROTECTION_TIMER /*Modify by qinglanwang, CQ00128828, 2021.03.16 */
}
MmTimerId;
#if 0
typedef enum MmCongestedProcedureTag
{
    CS_CONGESTION_NORMAL_LAU_CONGESTED       =   0,
    CS_CONGESTION_PERIODIC_LAU_CONGESTED =  1,
    CS_CONGESTION_CM_SERVICE_CONGESTED      =   2,
    CS_CONGESTION_NONE_CONGESTED   =   3
}
MmCongestedProcedure;
#endif
typedef enum SimMarkedInvalidCauseTag
{
    SIM_MARKED_INVALID_CAUSE_VALID_INIT = 0,
    SIM_MARKED_INVALID_CAUSE_VALID_AFTER_SIM_REMOVED,  //SIM is valid
    SIM_MARKED_INVALID_CAUSE_POWER_DOWN,                   //power down, SIM marked as invalid
    SIM_MARKED_INVALID_CAUSE_NW_MT_DETACH,
    SIM_MARKED_INVALID_CAUSE_NW_REJ_CS_ON_COMB_PROC,
    SIM_MARKED_INVALID_CAUSE_NW_REJ_AUTH,
    SIM_MARKED_INVALID_CAUSE_NW_REJ_GPRS_ATT,
    SIM_MARKED_INVALID_CAUSE_NW_REJ_COMB_ATT,
    SIM_MARKED_INVALID_CAUSE_NW_REJ_RA,
    SIM_MARKED_INVALID_CAUSE_NW_REJ_COMB_RA,
    SIM_MARKED_INVALID_CAUSE_NW_REJ_GMM_SER_REQ,
    SIM_MARKED_INVALID_CAUSE_NW_REJ_LAU,
    SIM_MARKED_INVALID_CAUSE_NW_ABORT_CONN,
    SIM_MARKED_INVALID_CAUSE_NW_REJ_SER_REQ
}
SimMarkedInvalidCause;

#if 0

typedef struct MmDeactCnfRecivedFlagTag
{
    Boolean rrcDeactReceived;
    Boolean grrDeactReceived;
}
MmDeactCnfRecivedFlag;
#endif
typedef enum MmRejectTypeTag
{
    MM_AUTH_FAIL,
    MM_AUTH_NW_REJ,
    MM_NOT_APPLICABLE
}
MmRejectType;

#if defined (UPGRADE_LTE)
typedef enum EmmRejectTypeTag
{
    EMM_ATTACH_REJECT,
    EMM_TAU_REJECT,
    EMM_AUTH_FAIL,
    EMM_AUTH_NW_REJ,
    EMM_NOT_APPLICABLE
}
EmmRejectType;
#endif

typedef enum MmSelectGeranUtranControlModuleTag
{
    MM_SGU_CONTROL_MODULE_MM,
    MM_SGU_CONTROL_MODULE_AB
}
MmSelectGeranUtranControlModule;

#if 0
#if defined (UPGRADE_3G)
typedef enum RrcLoopStateTag
{
    RRC_LOOP_INACTIVE,
    RRC_LOOP_ACTIVE
} RrcLoopState;
#endif  /* UPGRADE_3G */

typedef enum UmtsAuthOnGoingDomainTag /*UNIFICATION for DS_DEV and OT_DEV*/
{
    UMTS_NO_AUTH,
    UMTS_CS_AUTH,
    UMTS_PS_AUTH
}
UmtsAuthOnGoingDomain;
#endif

typedef enum  PagingFailureIndTypeTag
{
    MM_NO_PAGING_FAILURE_IND,
    MM_GRR_DS_PAGING_FAILURE_IND,
    MM_URR_DS_PAGING_FAILURE_IND,
    MM_ERR_DS_PAGING_FAILURE_IND
}
PagingFailureIndType;

typedef enum  MmMonitorEventTypeTag
{
    GMM_EVENT_NEW_ATTACH_REQ = 0,             /*GPRS ATTACH START*/
    GMM_EVENT_ATTACH_SUCCESS,                 /*GPRS ATTACH SUCESS*/
    GMM_EVENT_ATTACH_REJECT,                  /*GPRS ATTACH REJECT*/
    GMM_EVENT_RAU_REJECT,                     /*GPRS RAU REJECT*/
    GMM_EVENT_SERVICE_REJECT,                 /*GPRS SERVICE REJECT*/
    GMM_EVENT_AUTHENTICATION_REJECT,          /*GPRS AUTHENTICATION REJECT*/
    MM_EVENT_LU_REJECT,                       /*LU REJECT*/
    MM_EVENT_AUTHENTICATION_REJECT,           /*CS AUTHENTICATION REJECT*/
    EMM_EVENT_NEW_ATTACH_REQ = 0,             /*EPS ATTACH START*/
    EMM_EVENT_ATTACH_SUCCESS,                 /*EPS ATTACH SUCCESS*/
    EMM_EVENT_ATTACH_REJECT,                  /*EPS ATTACH REJECT*/
    EMM_EVENT_TAU_REJECT,                     /*EPS TAU REJECT*/
    EMM_EVENT_SERVICE_REJECT,                 /*EPS SERVICE REJECT*/
  EMM_EVENT_AUTHENTICATION_REJECT,           /*EPS AUTHENTICATION REJECT*/
  EMM_MT_DETACH,//add by xyma for CQ00129108 2021319
  EMM_TAU_ACCEPT,//add by xyma for CQ00136292
  EMM_EXTENDED_ACCESS_BARRED, //CQ00140695 add
  EMM_ATTACH_TIMEOUT,//CQ00143272 add
  EMM_TAU_TIMEOUT,//CQ00140695 add
  EMM_SERVICE_REQ_TIMEOUT //CQ00140695 add
}
MmMonitorEventType;

typedef enum  MmMsgTypeInQueueTag
{
    MM_PLMN_LIST_REQ_IN_QUEUE,
    MM_CAMP_REQ_IN_QUEUE,
    MM_REG_REQ_IN_QUEUE
}
MmMsgTypeInQueue;


typedef struct MmEventFlagsTag
{
    Boolean                     waitForSimResponse;        /* wait for SIM read/authentication     */
    Boolean                     imsiAttachAllowed;         /* Prevents unnecessary IMSI attach after coverage loss */
    SimMarkedInvalidCause       simMarkedInvalidCause;
    UCN_DomainIdentity          RejectDomain;              /* add by jiabin for ********** , 20111108  */
#if defined (UPGRADE_LTE)
    EmmCause                    lastEmmRejectCause;        /* Last recieved (DL Only) reject cause in EMM procedures */
    EmmRejectType               emmRejectType;
#endif
#if defined (UPGRADE_DSDS)
    DualMmflag                  dualMmflag;
    DualMmflag                  dualMmflagBak;
    DualMmflag                  dualDMmflag;
    DualMmflag                  dualDMmflagBak;
    Boolean                     dualMmPsSuspendByDMm;
    Boolean                     dualMmSuspendmmrReqReqWhenReceiveSimDataCnf;
    Boolean                     bSmPduRejectDuetoOtherSimInPs;
    PagingFailureIndType        bHandleXrrcDsPagingFailureInd;
    Boolean                     isTrigSuspendByPagingFailure;
    Boolean                     bMMSProcBegin;
    Boolean                     bwaitForResumeCnf;
    Int8                        tiForMmsPdp;
    DualMmPsSuspendCause        dualMmPsSuspendCause;
    Boolean                     bUpdateIndhasSentOut;
#endif
    MmSelectGeranUtranControlModule     mmSguControlModule;
    T3346StartedSystem          t3346StartedSystem[MAX_T3346_RUNNING_SYSTEM_NUMBER];
#if defined (UPGRADE_DSDS)
    Boolean                     bBufferedGmmRabmReestablishReq;
    Boolean                     bBufferedEsmUnitDataReq;
#if defined (SUPPORT_SMS)
    Boolean                     bBufferedPsmsEstReq;
#endif
    Boolean                     bPendingMtCall;
    Boolean                     bNotResumeAnotherCard;
#endif
#if defined (SUPPORT_SMS)
    Boolean                     bSmsProcBegin;
#endif
    Boolean                     deactiveAnotherCard;
    Boolean                     bNeedSendResumeCnf;
    Boolean                     isPsEnabled;
    MmMonitorEventType          reportEventInd;     /*EVENT*/
    EmmCause                    mmRejectCause;      /*REJECT CAUSE*/
    Boolean                     bRetrySuspendReq;/*Modify by qinglanwang, CQ00118173, 20200116 */
    Boolean                     bPowerDown;
    /*Added by qinglanwang, CQ00130609, 2021.06.03, begin*/
#if defined (UPGRADE_DSDS)
    DualMmPsCancelByServ        dualMmPsCancelByServ;
#endif
    /*Added by qinglanwang, CQ00130609, 2021.06.01, end*/
    Boolean                     bWrittenLociToSim; /*CQ00144655, added by fxzhang, 20230710*/
}
MmEventFlags;


#if defined (UPGRADE_LTE)
//ICAT EXPORTED ENUM
typedef enum PeriodicTauStatusTag
{
    PTAU_NOT_REQUIRED = 0,
    PTAU_TIMER_EXPIRED,
    PTAU_TIMER_EXPIRED_OUT_OF_COVERAGE,
    PTAU_TIMER_EXPIRED_WHILE_THROTTLING_TIMER_RUNNING,
    PTAU_TIMER_EXPIRED_PSM
} PeriodicTauStatus;

//ICAT EXPORTED ENUM
typedef enum TauRequiredReasonTag
{
    TAU_NOT_REQUIRED = 0,
    TAU_REQUIRED,
    TAU_INTER_RAT,
    TAU_CSFB_RETURN_LTE
} TauRequiredReason;



#define MAX_THROTTLING_SYSTEM_NUMBER   3
//ICAT EXPORTED STRUCT
typedef struct ThrottlingSystemTag
{
    Boolean                     systemUsed;
    Int8                        throttlingCounter;
    Plmn                        plmnId;
}
ThrottlingSystem;

#define MAX_T3402_RUNNING_SYSTEM_NUMBER   3
//ICAT EXPORTED STRUCT
typedef struct T3402StartedSystemTag
{
    Boolean                     systemUsed;
    Plmn                        plmnId;
}
T3402StartedSystem;


//ICAT EXPORTED STRUCT
typedef struct AttachOrTauTypeTag
{
    Boolean                         attachTypePresent;
    EpsAttachTypeElement            attachType;
    Boolean                         updateTypePresent;
    EpsUpdateType                   updateType;
}
AttachOrTauType;

//ICAT EXPORTED STRUCT
typedef struct EmmTauInfoTag
{
    FrameTicks                  taUpdateTimer;
    Int8                        trackingUpdateAttempts;
    Boolean                     epsBearerContextStatusPresent;
    EpsBearerContextStatus      epsBearerContextStatus;
    EmmSpecificProcedure        requiredTauProc;
    Boolean                     rxEpsBearerContextStatusPresent;
    EpsBearerContextStatus      rxEpsBearerContextStatus;
    /* Added by fxzhang, CQ00116169, 20190909, begin */
    GmmTriggerIndicator         rxEpsBearerContextTriggerInd;
    /* Added by fxzhang, CQ00116169, 20190909, end */
    PeriodicTauStatus           periodicTau;
    EmmCause                    l3MsgRejectCause;
    Boolean                     suspended;
    Boolean                     wasEpsAndNonEpsAttached;
    TauRequiredReason           tauNeeded;
    Boolean                     attemptingToUpdateMm;
    Boolean                     startT3440;
    Boolean                     successForEpsOnly;
}
EmmTauInfo;

//ICAT EXPORTED STRUCT
typedef struct EmmSecurityCapabilityCheckContextTag
{
    Int8 Length;
    Int8 SecurityCapabilityContext[NUM_OF_SECURITY_CAPABILITY_LEN];
} EmmSecurityCapabilityCheckContext;

//ICAT EXPORTED ENUM
typedef enum RequiredTauTypeTag
{
    NO_TA_UPDATE_NEEDED                = 0,
    NORMAL_TA_UPDATE                   = 1,
    COMBINED_TA_UPDATE                 = 2,
    COMBINED_TA_UPDATE_WITH_IMSI_ATTACH = 3,
}
RequiredTauType;

//ICAT EXPORTED ENUM
typedef enum EmmImsSrvCauseTag
{
    EMM_IMS_SRV_CAUSE_SUCCESS = 0,   /*Successful*/
    EMM_IMS_SRV_CAUSE_TERMINATE,     /*Failed, another card is doing CS service*/
    EMM_IMS_SRV_CAUSE_REDIAL,        /*Failed, another card is doning high priority service, IMS will redial later*/
    EMM_IMS_SRV_CAUSE_WAIT_FOR_RESUMECNF, /*Failed, current card is waiting for resumeCnf, IMS will redial later*/
    EMM_IMS_SRV_CAUSE_NUM
}
EmmImsSrvCause;

/* Modify by qinglanwang, for **********, 2021.04.14, begin */
//ICAT EXPORTED STRUCT
typedef struct EmmRejectMmrPlmnListCnfTag
{
    Boolean                     mmDirectReject;
    PlmnListStatus              status;
}EmmRejectMmrPlmnListCnf;
/* Modify by qinglanwang, for **********, 2021.04.14, end */

/* Added by fxzhang, **********, 20220617, begin */
#define MAX_ESMBACKOFFTIMER_SYSTEM_NUMBER   3
//ICAT EXPORTED STRUCT
typedef struct EsmBackOffTimerSystemTag
{
    Boolean                     systemUsed;
    Plmn                        plmnId;
}
EsmBackOffTimerSystem;
/* Added by fxzhang, **********, 20220617, end */

//ICAT EXPORTED STRUCT
typedef struct EmmEventFlagsTag
{
    /* copy from emm.emmFlags begin */
    Boolean                     serviceReqOnNewConnection;
    Boolean                     followOnRequested;        /* requested follow-on in RAU or Attach request */
    Boolean                     checkServiceReq;
    Boolean                     pdpActivationPending;
    Boolean                     followOnPending;
    Boolean                     regRequestPending;
    Boolean                     epsAttached;
    Boolean                     waitForEpsEstabllishCnf;
    Boolean                     bIntegrityProtect;        /* Integrity protect of NAS messages start */
    Boolean                     bEncrypt;           /* Encryption of NAS messages start */
    GmmRabmCause                rabmCause;            /* Use previous data structure */
    ServiceReqReason            serviceReqReason;
    ServiceReqTriggerMask       serviceReqTriggerMask;
    EmmCause                    securityCommandRejCause;
    Boolean                     imeisvRequest;
    Boolean                     useTmpCount;          /* Indication of use the NAS COUNT of ATTACH REQUEST/TAU REQUEST or SECURITY MODE COMPLETE */
    Boolean                     sendMmrInfoInd;         /* reminder to send engineering info  */
    MmAsUpdateType              sendAsUpdate;         /* reminder to send paging info to AS */
    Boolean                     searchingForPlmns;        /* to delay idle timers         */
    Boolean                     currentSearchIsManual;      /* is the search manual */ 
    Boolean                     waitPsConnRelCnf;
    Boolean                     accessClassBarred;        /* establishment failed due to access restrictions */
    GsmCause                    causeToSend;          /* used in MM_STATUS message      */
    IdType                      idRespType;           /* holds ID required in ID response   */
    Boolean                     needMmrNregCnf;         /* for AL to know PS has shut down    */
    ActivateStatus              rrActStatus;          /* last received value from GRR_ACT_IND */
    Boolean                     mmFirstReg;           /* set at power on            */
    Boolean                     campedOn;           /* MM is only camped and not registered */
    Boolean                     simUnavailableBeacuseOfNregReq;
    Boolean                     t3421ExpiryAndSimRemoved;
    AccStratumDeactCause        deactCause;           /* reason for deact (power off, silent shutdown) */
    Boolean                     suspendDeact;         /*Added by fxzhang, **********, 20181031*/
    EmmCause                    lastEmmRejectCause;       /* Last recieved (DL Only) reject cause in EMM procedures */
    Boolean                     lastEmmRejectCauseAbnormal;     /* Last recieved (DL Only) reject cause is ABNORMAL */
    EmmCause                    lastRealEmmRejectCause;       /***********, Last recieved (DL Only) real reject cause(not non-standard remapping) in EMM procedures   */
    Boolean                     bRemapToDefHandler;   /***********, Added by fxzhang*/
    Boolean                     psAuthProcOngoing;
    Boolean                     tauForLoadBalancing;      /* This indicates the TAU to be initiated is cause by RRC connection released
                                                                                 with cause "load balancing TAU required", so provide ERRC neither S-TMSI
                                                                                no registered MME identifier. */
#if defined(UPGRADE_FAKE_EXTENDED_SERVICE)
    Boolean                     tauForLocalRelease;
    Boolean                     sendEsmReleaseInd;
#endif
    Boolean                     reAttachFlg;          /* This flag indicates a MO-MT detach collison */
    Boolean                     srOngoing;            /* This flag indicates a service request procedure is initiated */
    Boolean                     rcvAuthReq;           /* Received authentication before security mode command or not */
    Int8                        emmAttachTauRejectCounter;    /* For the cause make sim invalid*/
    Boolean                     drbSetup;
    Int8                        numOfThrottlingSystem;
    ThrottlingSystem            throttlingSystem[MAX_THROTTLING_SYSTEM_NUMBER];
    Int8                        numOfESMThrottlingSystem;
    ThrottlingSystem            ESMthrottlingSystem[MAX_THROTTLING_SYSTEM_NUMBER];
    T3402StartedSystem          t3402StartedSystem[MAX_T3402_RUNNING_SYSTEM_NUMBER];
    Int8                        esmFailureCounter;                /* Counter for consecutive EMM attach reject with #19 "ESM failure" */
    Boolean                     esmFailureSpecialCause;         /* record of ESM cause 29 or 33 */
    Boolean                     esmFailureCause33;             /*Record of EMM CAUSE#19 && ESM CASUE#33*/
    Boolean                     integrityProtectedMessage;    /* Mark the attach/tau/service reject message is integrity protected or not */
    AttachOrTauType             attachOrTauType;
    Boolean                     nonStandardForbiddenTa[MAX_FORBIDDEN_TAI_LIST_NUM];
    Int8                        addForbiddenTaCount;
    Boolean                     constructMMMessageFlag;
    Boolean                     destructL3MessageFlag;
    Boolean                     destructASMessageFlag;
    Boolean                     expectedMessageFlag; /*unexpected message, also include abnormal procedure*/
    Int16                       assertDebugCode;
    Boolean                     srForEmergencyPDN;
    Boolean                     tauForEmergencyPDN;
    Boolean                     emergencyPDNExist;
    Boolean                     emergencyAttached;
    Boolean                     emergencyDetachImplicitly;
    TrackingInformation         epsLocInfoPriorToEmergency;
    Boolean                     deactivateNonEbsOnly;
    Boolean                     networkRelease;
    Boolean                     readNvramEpsInfo;
    EsmTransFailCause           esmTransFailCause;
    Int16                       numOfEsmTransFailure;
    Int8                        lastTransEsmMessageIndex;
    Boolean                     UCS2Support;
    EmmSecurityCapabilityCheckContext  emmSecurityCapabilityCheckContext;
    Boolean                     bRrConnRecovery;
    Boolean                     pendCsServiceNotificationInd;
    RequiredTauType             requiredTauType;                 //Indicate voiceDomainPrefer change trigger which type of TAU
#if defined(UPGRADE_DSDS)
#if defined(LTE_DS_SUSPEND_BY_SIM2)
    Boolean                     waitErrcSuspendCnf;
    Boolean                     waitForErrcReest;  /*This parameter is used to indicate after errc suspended, errc try to reestab or not*/
    Boolean                     isErrcSuspended;   /*This flag is used to indicate the errc is supended or not*/
    DualMmPsSuspendCause        errcSuspendedCause;   /*This flag is used to record the cause*/
    Boolean                     needTriggerFakeMsg;
    Boolean                     suspendedByPowerDown;
#endif
    //Boolean                     bDelaySim2Resume;
    Boolean                     bRelTriBySimRemoved;
    Boolean                     bTrigProtectForbStartCs;/*Added by fxzhang, CQ00138169, 20220804*/
    Int8                        gmmRabmReestablishCount;
#endif
#if defined(UPGRADE_VOLTE)
    Boolean                     bPendImsSrvStatusReq;
    Boolean                     imsSrvResult;
    Int16                       imsSrvType;
    Int16                       imsServiceDetailInfo;
    EmmImsSrvCause              imsServiceCause;
    ErrcImsSrvStatus            imsSrvStatus;   
    /*Added by fxzhang, CQ00137505, 20220629, begin*/
    ErrcImsSrvType              imsErrcServiceType;
    /*Added by fxzhang, CQ00137505, 20220629, end*/
#if defined(UPGRADE_DSDS)
    Boolean                     bWaitForImsServiceReq;   /*Resp 173 to IMS and wait for new IMS Service Req*/
#endif
    Boolean                     bTrigImsKeepAlive; /*Added by fxzhang, **********, 20220725*/
    Boolean                     bRecImsCallActiveInd; /*Added by fxzhang, **********, 20220726*/
#endif
    Boolean                     serviceRejectReceived;
#if defined(UPGRADE_DSDSLTE)
    Boolean                     isCellUpdateReceivedInImsRegister; /*This flag is used to indicate cellUpdateInd is received at SIP register in idle, but no PS establish is triggered for abnormal scenario*/
    Boolean                     bBufferedLteRabmEnqueueDataInd;   /*This flag is indicated that there is a buffered LteRabmEnqueueDataInd, which indicates there are buffered data in L2 at master card, need MM to interrupt another card PS*/
#endif
    Boolean                     bEmmMainTimerExpiry;
    Boolean                     bRcvPageAfterErrcConnRelInd;
    Boolean                     b15NatTAIListFlag;
#if defined (CRANE_PLATFORM)
    Boolean                     bEnterIdleTrigFlushFlash;
#endif
    Boolean                     bEnterIdleTriByFdyTimerExp;
    Boolean                     bAttachRejectCause15;
    Boolean                     bDirHandleErrcPageInd;  /*default value is FALSE, set to TRUE when current card is suspended and another card is running plmnSearch.*/
    Boolean                     bTriTauAfterT3440Exp;
    Boolean                     bRestartBecauseTransFailureInd;/*Added by qinglanwang for **********, 2020.12.31*/
    Boolean                     bNotReportToSac;/*Added by qinglanwang for **********, 2021.01.05*/
    Boolean                     bDirHandleMoPsService;/*Modified by qinglanwang, **********, 2021.02.23*/
    EmmCause                    esmAbortCause;/*Added by qinglanwang for **********, 2021.04.02*/
    Boolean                     cpsrSendout; //add by xyma for **********
    EmmRejectMmrPlmnListCnf     emmRejectMmrPlmnList;/* Modify by qinglanwang, for **********, 2021.04.14 */
    Boolean                     nasLowPrioritySet;  //add by xyna for **********
#if defined(UPGRADE_DS_PHASE_II_WITH_NAS)
    Boolean                     bClearEpsCellInfoDueToSuspend; /*change by qinglanwang, **********, 2021.07.02 */
#endif
    Boolean                     bReportOosForPos;  /*Added by fxzhang, **********, 20210914*/
    Boolean                     bNeedReReg;/* add by qinglanwang, for **********, 2022.07.19 */
    Boolean                     cellBarForAuth; /*Added by fxzhang, **********, 20220722*/
    Boolean                     bNotPrintDebugInfoInd;
    /* Added by fxzhang, **********, 20220617, begin */
    Int8                        numOfESMBackOffTimerSystem;
    EsmBackOffTimerSystem       ESMBackOffTimerSystem[MAX_ESMBACKOFFTIMER_SYSTEM_NUMBER];
    /* Added by fxzhang, **********, 20220617, end */
    gmmSmReleaseCause           smReleaseCause;
    Boolean               bReqTimeout; //CQ00143272
    Boolean               bTauReqSendSuccess; //CQ00144408
    Boolean               bNotReportMmrCampInd; /*CQ00146606, added by fxzhang, 20231027*/
}
EmmEventFlags;

typedef struct EmmEventDataTag
{
    EmmEventFlags       flags;        /* things that happen during active  */
    Err                 status;       /* reflects error status of event     */
    MmErrorCause        reqError;     /* for AL req in incompatible state   */
}
EmmEventData;

//ICAT EXPORTED STRUCT
typedef struct SecurityContextTag
{
    NativeEpsSecurityContextType    nativeType;                 /* The type of native EPS security context,ie full or paitial,not used for mapped EPS security context */
    NasKeySetIdentifierElement      nasKsi;                     /* Key set identifier */
    AsmeKey                         asmeKey;                    /* Kasme */
    NasCount                        uplinkNasCount;             /* Uplink NAS COUNT */
    NasCount                        downlinkNasCount;           /* Downlink NAS COUNT */
    EpsIntegrityAlgorithm           epsIntegrityAlgorithm;      /* EPS NAS integrity algorithm */
    EpsEncryptionAlgorithm          epsEncryptionAlgorithm;     /* EPS NAS encryption algorithm */
    KeyNasInt                       nasIntKey;                  /* Knas-int */
    KeyNasEnc                       nasEncKey;                  /* Knas-enc */
}
SecurityContext;

//ICAT EXPORTED STRUCT
typedef struct SecurityParamsTag
{
    SecurityContext    nonCurrentSecurityContext;           /* Non-current EPS security context */
    SecurityContext    currentSecurityContext;              /* Current EPS security context */
#if 0
    SecurityContext    mappedSecurityContext;               /* Mapped security context created during handover */
#endif
    NonceElement       nonceUe;                             /* NONCEue */
    NonceElement       nonceMme;                            /* NONCEmme */
    NasCount           tempUplinkNasCount;                  /* NAS COUNT of ATTACH REQUEST/TAU REQUEST/SECURITY MODE COMPLETE */
}
SecurityParams;

#endif

#endif

/* END OF FILE */

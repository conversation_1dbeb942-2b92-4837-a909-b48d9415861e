/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urltmtx.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2007/03/19 13:59:30 $
 **************************************************************************
 * File Description:
 *
 *      Header file for UrlTmTx.c.
 *      Contains function call declarations, constants and types for use
 *      by other URLC modules.
 **************************************************************************/

#if !defined (URLTMTX_H)
#define       URLTMTX_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <urltmtyp.h>
#include <umacerlcif.h>

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/

/***************************************************************************
*   Macro Functions
***************************************************************************/
/* Macro to determine if UL data is available for transmission */
#define URL_TM_UL_DATA_AVAILABLE(entity_p)                                     \
    (Boolean)                                                                  \
    ((((entity_p)->config.segmentation) &&                                     \
      ((entity_p)->bufferInfo.tfcSelection.info.tm.data.segmented.sduBits>0)   \
     ) ||                                                                      \
     ((!(entity_p)->config.segmentation) &&                                    \
      ((entity_p)->bufferInfo.tfcSelection.info.tm.data.unsegmented.sduBits>0) \
     )                                                                         \
    )

/* Macro to determine if a bearer is configured for loopback mode, either
 * active or pending
 */
#define URL_TMTX_LOOPBACK(entity_p)          ((entity_p)->loopback)


/***************************************************************************
 * Typed Constants
 ***************************************************************************/

/***************************************************************************
 * Function Prototypes
 ***************************************************************************/

extern void
UrlTmTxHandler
    (UrlTmTxEntity                                    * entityData_p,
     SignalBuffer                                     * rxSignal_p
    );
extern  void
UrlTmTxUrlcTmDataReq
    (UrlTmTxEntity                                    * entityData_p,
     UrlcTmDataReq                                    * urlcTmDataReq_p
    );
extern void
UrlTmTxUrlcAmrTmDataReq
    (UrlTmTxEntity                                    * entityData_p,
     UrlcAmrTmDataReq                                 * urlcAmrTmDataReq_p
    );
extern  void
UrlTmTxUrlcCsdTmDataReq
    (UrlTmTxEntity                                    * entityData_p,
     UrlcCsdTmDataReq                                 * urlcCsdTmDataReq_p
    );
extern  void
UrlTmTxGetTransportBlock
    (UrlTmTxEntity                                    * entityData_p,
     dtcF8DescriptorInfo                              * cipherInfo_p
    );

#if defined UPGRADE_3G_EDCH
extern void
UrlTmTxGetTransportBlockEdch
    (UrlTmTxEntity                                    * entityData_p,
     UmacEdchTxReqId_t                                  edchTxReqId,
     dtcF8DescriptorInfo                              * cipherInfo_p
    );
extern void
UrlTmTxUmacTxStatusIndEdch
    (UrlTmTxEntity                                    * entityData_p,
     UmacEdchTxStatusInd                              * umacEdchTxStatusInd_p
    );

#endif

extern  void
UrlTmTxUpdateBufferInfo
    (UrlTmTxEntity                                    * entityData_p
    );
extern  void
UrlTmTxDiagnosticsReq
    (UrlTmTxEntity                                    * entityData_p
    );
extern  void
UrlTmTxSduBufferShrinkage
    (UrlTmTxEntity                                    * entityData_p
    );
  void
extern  UrlTmTxUmacTxStatusInd
    (UrlTmTxEntity                                    * entityData_p
    );

#endif /* URLTMTX_H */

/* END OF UrlTmTx.h */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/3g.mod/lib/src/ulbgupdcp.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2011/03/08 17:17:00 $
 **************************************************************************
 * File Description:
 *
 *      The function interface between the ULBG task and UPDCP implementation.
 **************************************************************************/

#if !defined (ULBGUPDCP_H)
#define ULBGUPDCP_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <gpcntr.h>
#include <xrabmdata.h>

/***************************************************************************
* Manifest Constants
***************************************************************************/

/***************************************************************************
* Macro Functions
***************************************************************************/

/***************************************************************************
* Types
***************************************************************************/

/***************************************************************************
* Typed Constants
***************************************************************************/

/***************************************************************************
* Function Prototypes
***************************************************************************/

extern void UlbgUpdcpInit(void);
extern Boolean UlbgUpdcpProcessSignal(XRabmEntity *sn,SignalBuffer *sigBuf);

#endif	/* ULBGUPDCP_H */

/* END OF FILE */

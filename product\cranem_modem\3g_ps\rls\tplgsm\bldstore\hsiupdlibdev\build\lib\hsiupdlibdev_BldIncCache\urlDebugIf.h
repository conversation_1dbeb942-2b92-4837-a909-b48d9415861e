/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/


/** \file: urlDebugIf.h
 *******************************************************************************
 * File Name:   urlDebugIf.h
 *
 *
 * File Description: URLC Debug (RLC Decoder) Interface Header File. 
 *					 Should be included by RLC debug utility users, e.g.:
 *					 HSDPA / HSUPA RLC Debug controllers
 *
 *******************************************************************************
 */
#if !defined (URLDEBUGIF_H)  
#define       URLDEBUGIF_H

#if defined (URL_DEBUG_HSDPA_OVER_DTC) && !defined (UPGRADE_3G_HSDPA)
#error "Cannot define URL_DEBUG_HSDPA_OVER_DTC without UPGRADE_3G_HSDPA"
#endif

#if defined (URL_DEBUG_EDCH_OVER_DTC) && !defined (UPGRADE_3G_EDCH)
#error "Cannot define URL_DEBUG_EDCH_OVER_DTC without UPGRADE_3G_EDCH"
#endif


/********************************************************************************
 * Nested Include Files
 ********************************************************************************/

 
 #include <system.h>
 #include <udtcif.h>
 #include <urltypes.h>
 #include <ups_typ.h>
 #include <u1_cfg.h>
 #include <umac_sig.h>
 
 #include <diag.h>

 
/*********************************************************************************
 * Manifest Constants
 *********************************************************************************/

 #define 	URL_RBS_DEBUG_MAX_VALID_RBS				(12)

 #define 	URL_DEBUG_USERID_CALLERID_HSDPA 		(0)	
 #define    URL_DEBUG_USERID_CALLERID_EDCH			(1)
 #define 	URL_DEBUG_USERID_CALLERID_EHSDPA		(2)

 #define	URL_DEBUG_USERID_RELEASEID_REL6			(0)
 #define	URL_DEBUG_USERID_RELEASEID_REL7			(1)

 #define 	URL_DEBUG_USERID_RELEASEID_LEN_BITS		(4)
 #define 	URL_DEBUG_USERID_CALLERID_LEN_BITS		(4)
 #define 	URL_DEBUG_USERID_RELEASEID_MASK			(0xF0)
 #define 	URL_DEBUG_USERID_CALLERID_MASK			(0x0F)

 #define	URL_DEBUG_USERID_REL7_HSDPA				( (URL_DEBUG_USERID_RELEASEID_REL7<<URL_DEBUG_USERID_RELEASEID_LEN_BITS) | URL_DEBUG_USERID_CALLERID_HSDPA)
 #define	URL_DEBUG_USERID_REL7_EDCH				( (URL_DEBUG_USERID_RELEASEID_REL7<<URL_DEBUG_USERID_RELEASEID_LEN_BITS) | URL_DEBUG_USERID_CALLERID_EDCH)
 #define	URL_DEBUG_USERID_REL7_EHSDPA			( (URL_DEBUG_USERID_RELEASEID_REL7<<URL_DEBUG_USERID_RELEASEID_LEN_BITS) | URL_DEBUG_USERID_CALLERID_EHSDPA)

 /* MSB of UserId is used to mark endianity:
  * 0: Little Endian
  * 1: Big Endian 	*/
 #define	URL_DEBUG_USERID_BIG_ENDIAN_BIT			(0x80)

 #define 	URL_DEBUG_NUM_OF_USER_DATA_BYTES		(4)
 #define 	URL_DEBUG_MAX_PDU_DATA_BIT_LEN_DEFAULT	(320)
 #define 	URL_DEBUG_MAX_PDU_DATA_BIT_LEN_NO_LIMIT	(0xFFFF)
 #define 	URL_DEBUG_MAX_BITS_IN_DIAG_BUFFER		(0x7FFF8)
 #define    URL_DEBUG_MAX_HS_PDUS_IN_DEBUG_CHAIN	UDTC_IF_HSDPA_DESCRIPTORS_NUM
 
/* Debug Message Encoding, depending on Debug Case (stated in CommonMetadata at message beginning):
 * ************************************************************************************************************************************************************
 * 
 * URL_DEBUG_CASE_METADATA_ONLY:
 * |----------------|----------------------------|
 * |  				| (CommonMetadata.NumOfPdus) |
 * | CommonMetadata |            X 				 |
 * |                | 	  (EncodedPduInfo) 		 |		
 * |----------------|----------------------------|
 *
 *
 * URL_DEBUG_CASE_ALL_DATA:
 * |----------------|----------------------------|------------|--------------------------------|------------------|----------------------|
 * |  				| (CommonMetadata.NumOfPdus) | First PDU, | (CommonMetadata.NumOfPdus - 1) |Possible padding  | If message contains  |
 * | CommonMetadata |            X 				 |    W.O.    |			   X			       |of size of first  | a ciphered PDU:	  	 |
 * |                | 	  (EncodedPduInfo) 		 | Its Header | (PDU Including header) 	       |PDU header 		  | Cipher key table	 |
 * |----------------|----------------------------|------------|--------------------------------|------------------|----------------------|
 *
 *
 * URL_DEBUG_CASE_NORMAL:
 * |----------------|----------------------------|----------------------------------|----------------------------------|
 * |  				| (CommonMetadata.NumOfPdus) | (Num. Of Fully Unfiltered PDU's) | If message contains  			   |
 * | CommonMetadata |            X 				 |			   X			        | a fully unfiltered ciphered PDU: |							  
 * |                | 	  (EncodedPduInfo) 		 | (PDU W.O. header) 	            | Cipher key table	  		  	   |
 * |----------------|----------------------------|----------------------------------|----------------------------------|
 *
 * 
 * URL_DEBUG_CASE_ALL_DATA_INCL_HDRS:
 * |----------------|----------------------------|----------------------------|----------------------|
 * |  				| (CommonMetadata.NumOfPdus) | (CommonMetadata.NumOfPdus) | If message contains  |
 * | CommonMetadata |            X 				 |			   X			  | a ciphered PDU:	     |
 * |                | 	  (EncodedPduInfo) 		 | (PDU Including header) 	  | Cipher key table	 |
 * |----------------|----------------------------|----------------------------|----------------------|
 *
 *
 * URL_DEBUG_CASE_ALL_DATA_CT_OFFSET:
 * |----------------|----------------------------|------------|---------------------------------------------------|------------------|----------------------|
 * |  				| (CommonMetadata.NumOfPdus) | First PDU, | (CommonMetadata.NumOfPdus - 1)                    |Possible padding  | If message contains  |
 * | CommonMetadata |            X 				 |    W.O.    |			   X			                          |of size of first  | a ciphered PDU:	    |
 * |                | 	  (EncodedPduInfo) 		 | Its Header | (4 bits of MAC CT field + PDU Including header)   |PDU header 		 | Cipher key table	    |
 * |----------------|----------------------------|------------|---------------------------------------------------|------------------|----------------------|
 *
 *
 *************************************************************************************************************************************************************/
 
 #define 	URL_DEBUG_CASE_METADATA_ONLY				(0)
 #define	URL_DEBUG_CASE_ALL_DATA					(1)
 #define	URL_DEBUG_CASE_NORMAL						(2)
 #define	URL_DEBUG_CASE_DATA_INCL_HDRS			(3)
 #define	URL_DEBUG_CASE_ALL_DATA_CT_OFFSET		(4)
 
/***************************************************************************
  * External variables
  ***************************************************************************/
 
 extern UrlcDebugDlPduStatus udlPduStatusToDebugStatus[];

/***************************************************************************
 *   Macro Functions
 ***************************************************************************/

 /* Currently only a bit offset of 4 is supported. In case this is changed to a non constant value, 
  * Need to add a new field to CommonMetadata */
 #define 	URL_DEBUG_ALL_DATA_CT_PDU_BIT_OFFSET	(4)


/* Masks for liMode
 *          7       6       5       4       3	     2       1       0    	
 *       |-------|-------|-------|-------|-------|-------|-------|-------|
 *       | 			 	 Reserved  			 	 | LI15  |   Mode (0-2)  |
 *       |-------|-------|-------|-------|-------|-------|-------|-------|
 */

#define URLC_DEBUG_LI_MODE_MASK_LI         (0x04)
#define URLC_DEBUG_LI_MODE_MASK_MODE       (0x03)

#define URLC_DEBUG_LI_MODE_OFFSET_LI		  (2)
#define URLC_DEBUG_LI_MODE_OFFSET_MODE	  (0)

#define URLC_DEBUG_PDU_SPECIAL_HE_VALUE		(0x02)
#define URLC_DEBUG_PDU_HE_MASK				(0x03)


/* Set Li and Mode of liMode 
 * (currently, mode offset is zero so saving unnecessary shift) */                                            
#define URLC_DEBUG_LI_MODE(li,m)   		  (((li)<<URLC_DEBUG_LI_MODE_OFFSET_LI) | (m))

/* Get Li and Mode of liMode */                                            
#define URLC_DEBUG_LI_MODE_GET_LI(liMode)       (((liMode) & URLC_DEBUG_LI_MODE_MASK_LI   ) >> URLC_DEBUG_LI_MODE_OFFSET_LI)
#define URLC_DEBUG_LI_MODE_GET_MODE(liMode)     ((liMode) & URLC_DEBUG_LI_MODE_MASK_MODE )


#define URL_DEBUG_TOTAL_METADATA_BYTES(numOfDebugPdus) \
	(sizeof(UrlDebugCommonMetadata) + (numOfDebugPdus * sizeof(UrlDebugEncodedPduInfo)))

/* Convert the pdu DL status enum to Debug status enum*/
#define URL_DEBUG_GET_DEBUG_STATUS(status) udlPduStatusToDebugStatus[status]

#if defined DIAG_GET_TRACE_DATA
#define URLC_DEBUG_GET_IS_DIAG_FILTERED(isDiagFiltered)\
    {\
        reportFilterStatus rep;\
        DIAG_GET_TRACE_DATA(PS_3G, URLC, DebugEncodedData, &rep);\
        isDiagFiltered = rep.isFiltered;\
    }

#else
#define URLC_DEBUG_GET_IS_DIAG_FILTERED(isDiagFiltered)  \
    (isDiagFiltered = FALSE)
    
#endif

#ifdef UPGRADE_DSDSWB_L2
#define URLC_DEBUG_SET_RB_FILTER(isDiagFiltered,bearerExists,b,mode) \
        if (( bearerExists ) && \
            (!isDiagFiltered) && \
            (isDiagConnected()) && \
            (urlGDB_gp->testMode.debugMode.enabled) && \
            (urlGDB_gp->testMode.debugMode.bearer[b]) && \
            (urlGDB_gp->testMode.debugMode.mode[mode] != CRLC_FILTER_NONE) )\
        { \
            if (b <= RB_4) \
            {\
                urlGDB_gp->bearer[RlcActiveSimid][b].debugFilter = URL_DEBUG_BEARER_FILTER_ENABLED_FULL;\
            }\
            else \
            {\
                if (urlGDB_gp->testMode.debugMode.mode[mode] == CRLC_FILTER_ALL) \
                {\
                    urlGDB_gp->bearer[RlcActiveSimid][b].debugFilter = URL_DEBUG_BEARER_FILTER_ENABLED_FULL;\
                }\
                else\
                {\
                    urlGDB_gp->bearer[RlcActiveSimid][b].debugFilter = URL_DEBUG_BEARER_FILTER_ENABLED_SELECTIVE;\
                }\
            }\
        }\
        else\
        {\
            urlGDB_gp->bearer[RlcActiveSimid][b].debugFilter = URL_DEBUG_BEARER_FILTER_DISABLED;\
        }
#else

#define URLC_DEBUG_SET_RB_FILTER(isDiagFiltered,bearerExists,b,mode) \
        if (( bearerExists ) && \
            (!isDiagFiltered) && \
            (isDiagConnected()) && \
            (urlGDB_gp->testMode.debugMode.enabled) && \
            (urlGDB_gp->testMode.debugMode.bearer[b]) && \
            (urlGDB_gp->testMode.debugMode.mode[mode] != CRLC_FILTER_NONE) )\
        { \
            if (b <= RB_4) \
            {\
                urlGDB_gp->bearer[b].debugFilter = URL_DEBUG_BEARER_FILTER_ENABLED_FULL;\
            }\
            else \
            {\
                if (urlGDB_gp->testMode.debugMode.mode[mode] == CRLC_FILTER_ALL) \
                {\
                    urlGDB_gp->bearer[b].debugFilter = URL_DEBUG_BEARER_FILTER_ENABLED_FULL;\
                }\
                else\
                {\
                    urlGDB_gp->bearer[b].debugFilter = URL_DEBUG_BEARER_FILTER_ENABLED_SELECTIVE;\
                }\
            }\
        }\
        else\
        {\
            urlGDB_gp->bearer[b].debugFilter = URL_DEBUG_BEARER_FILTER_DISABLED;\
        }
#endif

/***************************************************************************
 * Types
 ***************************************************************************/


typedef enum UrlDebugBearerFilterTag
{
	URL_DEBUG_BEARER_FILTER_DISABLED = 0,	  			/* Debug is disabled for this RB */ 
	URL_DEBUG_BEARER_FILTER_ENABLED_SELECTIVE = 1,    	/* TM: Full debug , UM/AM: Full debug for CTRL and LI PDUs, just headers for the rest */ 
	URL_DEBUG_BEARER_FILTER_ENABLED_FULL = 2			/* Full debug for all kinds of PDU's */
}UrlDebugBearerFilter;

 typedef enum UrlDebugBearerModeTag
{
	/** Transparent Mode */
	URL_DEBUG_BEARER_MODE_TM = URL_BEARER_MODE_TM,
	
	/** Unacknowledged Mode */
	URL_DEBUG_BEARER_MODE_UM	= URL_BEARER_MODE_UM,
	
	/** Acknowledged Mode */
	URL_DEBUG_BEARER_MODE_AM	= URL_BEARER_MODE_AM,

	/** Unacknowledged mode with Alternative Ebit configured */
	URL_DEBUG_BEARER_MODE_UM_ALT = URL_DEBUG_BEARER_MODE_AM + 1
	
} UrlDebugBearerMode;

//ICAT EXPORTED STRUCT
 typedef struct UrlDebugControlParamsTag
 {
 	/** needCiphering == TRUE iff at least one PDU is ciphered */
 	Boolean					needCiphering;
 	
 	/** (numOfFullyUnfilteredDescriptors == 0) iff all data bits where filtered
 	 * either only the data of the PDUS or whole PDU's
 	 * (numOfFullyUnfilteredDescriptors == 0) and (debugNumPdus > 0) => debug case is METADATA_ONLY 
 	 * all data mode happens when (debugNumPdus == numOfFullyUnfilteredDescriptors == numOfPdus in message > 0)
 	 * normal mode happens when (debugNumPdus>0) and (debugNumPdus > numOfFullyUnfilteredDescriptors). */
 	Int32					numOfFullyUnfilteredDescriptors;

 	/** used in both normal and all data cases */
 	Int16					totalMetadataBytes;

 	/** Used in the normal case */
 	Int32					totalUnfilteredDataBits;
 	
 	/** This accumulates the data bits to be copied,
 	 * Pointed by the allData descriptor in the allData debug case
 	 * This includes all the data bits to be copied from the L1 buffer,
 	 * But does not include the metadata and ciphering information. 	 */
 	Int16					totalL1BufferBits;

	/* Used to optimize usage of EDCH debug descriptors */
	Boolean					dataNeededOnLastDesc;

	Int32					debugPduNum;

	/* currently for E/Hsdpa only, save the total bits per pdu for debug*/
	Int16					maxPduBitLength;

 } UrlDebugControlParams;

//ICAT EXPORTED STRUCT
typedef struct UrlDebugRlcPduInfoTag
{
   /** bit length of the PDU, including header.
    * MSB: Indicates that the length has been truncated
    * to exclude data, use URLC_DEBUG_LENGTH_TRUNCATED */
    Int16                       bitLength;           

    /** MSB: Indicates data has already been deciphered */
    Int8                        bearerIdentity;      

    /** li [1 bit]: Li type (7/15 bits)
     * Mode [2 bits]: TM/AM/UM/UM Alt' E-bit */
    Int8                        liMode; 

    /** All 25 bits of HFN in least significant bits */
    Int32                        hfn;  

	/** KSI [4 bits]: cipher key choosing: 
     * (0=6,8-14=Key, 7,15=None) 	
     */
    Int8						ksi;

    /** Status of PDU: OK / CRC Error etc. */
    Int8                        status;	   
    
    /** 0 in case of control PDU 
     * only 8 rightmost bits are relevant in case of UM */
    Int16	         header;       
}UrlDebugRlcPduInfo;

//ICAT EXPORTED STRUCT
typedef struct UrlDebugEdchInfoTag
{
	UrlDebugRlcPduInfo		rlcInfo;
	// MacE TSN value 
	Int8					maceTsn;
	Int8  					reserved[3]; 
}UrlDebugEdchInfo;

//ICAT EXPORTED STRUCT
typedef struct UrlDebugHsInfoTag
{
	UrlDebugRlcPduInfo		rlcInfo;
	Int8 					reserved[4];
}UrlDebugHsInfo;

//ICAT EXPORTED STRUCT
typedef struct UrlDebugEhsInfoTag
{
	UrlDebugRlcPduInfo			rlcInfo;
	// first TSN of this reordering-PDU
	Int8						firstMacEhsTsn; 
	// last TSN of this reordering-PDU
	Int8						lastMacEhsTsn; 
	Int8						reserved[2];
}UrlDebugEhsInfo;


//ICAT EXPORTED STRUCT
typedef union UrlDebugEncodedPduInfoTag
{
	UrlDebugEdchInfo 			edchInfo;
	UrlDebugHsInfo 				hsInfo;
	UrlDebugEhsInfo				ehsInfo;
}UrlDebugEncodedPduInfo;



typedef struct UrlDebugEdchUserDataTag
{
	// Reserved for future use
	Int8	reserved[2];
}UrlDebugEdchUserData;


typedef struct UrlDebugHsdpaUserDataTag
{
	//QueueId of the currently delivered MAC-HS pdu 
	Int8	queueId;
	//TSN of the currently delivered MAC-HS pdu
	Int8	tsn;

}UrlDebugHsdpaUserData;

//ICAT EXPORTED STRUCT
typedef struct UrlDebugEhsdpaUserDataTag
{
	//Reserved for future used
	Int8	reserved[2];
}UrlDebugEhsdpaUserData;

//ICAT EXPORTED STRUCT
typedef union UrlDebugUserDataTag
{
	UrlDebugHsdpaUserData	hsData;
	UrlDebugEhsdpaUserData	ehsData;
	UrlDebugEdchUserData	edchData;
}UrlDebugUserData;

//ICAT EXPORTED STRUCT
typedef struct UrlDebugCommonMetadataTag
{

 /** 
  * ==================================================
  * |     releaseId         |		callerId 	 	 |
  * ==================================================

  * releaseId (4 bits):
  *		States the version of this release:
  *		 0x0 - Version for Release6 (Supported)
  *		 0x1 - Version for Release7 (Supported)
  *		 0x2 - Version for Release8
  *
  * callerId (4 bits): 
  *		States the caller entity. Current supported users:
  *		 0x0 - HSDPA
  *		 0x1 - HSUPA
  * 	 0x2 - EHSDPA
  */

  Int8				userId;
   
 /** States the debug case of the debug message. 
  * (MetadataOnly, AllData, Normal, AllDataInclHeaders) 
  */
   Int8 			debugCase;
   
  /** States the number of PDU's in debug message */
  Int16				numOfPdus;

  /** Progressing message ID */	
  Int32				messageIdentifier;

  //Maximum length of data message to display in decoder
  Int16				maxPduBitLength;

  /** Additional mac data */
  UrlDebugUserData	macData;

} UrlDebugCommonMetadata;


typedef struct UrlDebugEncodedMetadataTag
{

/** This data structure holds message data types that are common
 * to all RLC decoder messages, such as HSDPA and HSUPA debug messages.
 */
 UrlDebugCommonMetadata		commonMetadata;
  
/* Debug metadata is kept in this array for every PDU, Actuall allocation is different per debug user. (HSUPA / HSDPA etc.) */
 UrlDebugEncodedPduInfo  	debugPduInfo [2];
}UrlDebugEncodedMetadata;

typedef struct UrlDebugChainDescriptorsTag
{
   dtcF8DescriptorInfo			*metadataDesc_p;
   dtcF8DescriptorInfo			*cipherDesc_p;
   dtcF8DescriptorInfo			*allDataDesc_p;
   dtcF8DescriptorInfo			*unfilteredChainTailDesc_p;
   dtcF8DescriptorInfo			*filteredChainHeadDesc_p;
   dtcF8DescriptorInfo			*filteredChainTailDesc_p;
   dtcF8DescriptorInfo			*chainNextDesc_p;
   Boolean 						originalHeadUnfiltered;  
} UrlDebugChainDescriptors;

#if defined (URL_DEBUG_HSDPA_OVER_DTC) 
typedef struct UrlcDebugHsTag
{
   UrlDebugEncodedMetadata		*debugMetadata_p;
   SignalBuffer 				sigBufUmacHsDataInd;
   SignalBuffer					sigBufUrlcDebugHsDiagInd;
   SignalBuffer					sigBufUrlcDebugHsDiagIndProto;
   UrlDebugChainDescriptors		debugChainDescriptors;	
   Boolean						normalMode; 
   dtcTransferReq_ts 			transferRequest;   
   Int32						messageIdentifier;
   Int16						maxPduBitLength; 
} UrlDebugHs;

#endif // URL_DEBUG_HSDPA_OVER_DTC


#if defined (URL_DEBUG_EDCH_OVER_DTC)

 typedef enum UrlDebugEdchDtcStateTag
{
	URL_DEBUG_EDCH_DTC_ACTIVE, 

	URL_DEBUG_EDCH_DTC_NOT_ACTIVE 
	
} UrlDebugEdchDtcState;

typedef struct UrlDebugEdchChainDescriptorsTag
{
   dtcF8DescriptorInfo			*metadataDesc_p;
   dtcF8DescriptorInfo			*cipherDesc_p;
   dtcF8DescriptorInfo			*debugDataChainHeadDesc_p;
   dtcF8DescriptorInfo			*debugDataChainLastDesc_p;
   dtcF8DescriptorInfo			*debugDataChainNextDesc_p;
} UrlDebugEdchChainDescriptors;

typedef struct UrlDebugEdchTag
{
   UrlDebugEncodedMetadata			*debugMetadata_p;
   UrlDebugEdchChainDescriptors		debugChainDescriptors;	
   UrlDebugControlParams			controlParams;
   dtcTransferReq_ts 				transferRequest;
   Int8								*destPoolAddr;
   Int16							requestedBytesInDebugBuffer;
   Int32							numOfDecreasedBytes;
   UrlDebugEdchDtcState				dtcState;
   UdtcRequestInfo					*txChainRequsetInfo_p; 
   Int32							messageIdentifier;
   
} UrlDebugEdch;
#endif // URL_DEBUG_EDCH_OVER_DTC

//ICAT EXPORTED STRUCT
typedef struct UrlcRbsDebugRxBearerTag
{
	Int8	rb;
	Int8	numOfRxSdus; 	/* Number of RX sdus pending in bearer */
	Int8    mode; 			/* RB mode */
	Int8	state; 			/* TM: State / UM: State / AM: dlState */
	Int8	runState;		/* UM: runState */
} UrlcRbsDebugRxBearer;

//ICAT EXPORTED STRUCT
typedef struct UrlcRbsDebugTxBearerTag
{
	Int8	rb;
	Int8	numOfTxSdus; 	/* Number of TX sdus pending in bearer */
	Int8    mode;			 	/* RB mode */
	Int8	state;			/* TM: State / UM: State / AM: txState (Transmitter state) */
	Int8	ulState;			/* UM: ulState / AM: ulState */
}UrlcRbsDebugTxBearer;

//ICAT EXPORTED STRUCT
typedef struct UrlRbsDebugTag
{
	Int8	numValidRxRb;  /* Number of valid RX bearers */
	UrlcRbsDebugRxBearer validRxRb[URL_RBS_DEBUG_MAX_VALID_RBS];
	
	Int8	numValidTxRb;  /* Number of valid TX bearers */
	UrlcRbsDebugTxBearer	validTxRb[URL_RBS_DEBUG_MAX_VALID_RBS];
} UrlRbsDebug;



/***************************************************************************
 * Constants
 ***************************************************************************/

 

/***************************************************************************
 * Function Prototypes
 ***************************************************************************/


/* Debug Chain Builder Utility Functions */

Int16
UrlDebugBuildMetadataOnlyChain(UrlDebugChainDescriptors 		*debugDescs_p, 
								  UrlDebugControlParams  			*controlParams_p);
Int16
UrlDebugBuildAlldataChain(UrlDebugChainDescriptors 	*debugDescs_p, 
							 UrlDebugControlParams  		*controlParams_p);
Int32
UrlDebugBuildNormalChain(UrlDebugChainDescriptors 	*debugDescs_p, 
							 UrlDebugControlParams  		*controlParams_p,
							 Int32							numOfDescs);


dtcF8DescriptorInfo* 
UrlDebugUnifyChains (UrlDebugChainDescriptors 	*debugDescs_p);

/* Downlink Debug Control Functions */
void				 
UrlDebugHandleDlAmPdu(
					 	UmacDlPduListInfo	 	 	   *pduInfo_p,	    	/* IN */
					 	UrlSequenceNumber				sn,					/* IN */
					 	UrlDebugControlParams			*controlParams_p,	/* IN/OUT (Accumulated)  */
					 	UrlDebugRlcPduInfo				*rlcDebugInfo_p     /* OUT */
						);

void				 
UrlDebugHandleDlUmPdu(
					 	UmacDlPduListInfo				*pduInfo_p,			/* IN */
					 	UrlSequenceNumber				sn,					/* IN */
						UrlDebugControlParams			*controlParams_p,	/* IN/OUT (Accumulated)  */
						UrlDebugRlcPduInfo				*rlcDebugInfo_p		/* OUT */
					 	);

void				 
UrlDebugHandleDlTmPdu(
						UmacDlPduListInfo				*pduInfo_p, 		/* IN */
						UrlDebugControlParams			*controlParams_p,	/* IN/OUT (Accumulated)  */
						UrlDebugRlcPduInfo				*rlcDebugInfo_p 	/* IN/OUT */
						);

#if defined (URL_DEBUG_EDCH_OVER_DTC)
/* Edch Debug Control Functions */
void				 
UrlDebugHandleEdchAmPdu(
					 	dtcF8DescriptorInfo	 	  	  * cipherInfo_p,	    /* IN */
						Int8						  * debugSrcAddr,		/* IN */
					 	UrlDebugControlParams		  * controlParams_p,	/* IN/OUT (Accumulated)  */
					 	UrlDebugRlcPduInfo 			  * rlcDebugInfo_p,    /* IN/OUT */
					 	Boolean						    li15bits			/* IN */
						);
void				 
UrlDebugHandleEdchUmPdu(
					 	dtcF8DescriptorInfo	 	  	  * cipherInfo_p,	    /* IN */
					 	Int8	      	  			  * debugSrcAddr,		/* IN */
					 	UrlDebugControlParams		  * controlParams_p,	/* IN/OUT (Accumulated)  */
					 	UrlDebugRlcPduInfo 			  * rlcDebugInfo_p    /* IN/OUT */		
						); 
#endif // URL_DEBUG_EDCH_OVER_DTC

/* Debug Chain Copyer Utility Functions */
void 
UrlDebugCopyChainReq(dtcTransferReq_ts *debugTransferRequest_p, 
								Int32	dataTransferRequestId,
								Int8 	*destBuf, 
								Int16 	requestedDstBufByteSize);						


#if defined (ON_PC)
void 
UrlDebugSendAsGkiSignal(Int8 	*debugMsgBuffer_p, Int16 byteLength);
#endif // ON_PC

#if defined (URL_DEBUG_HSDPA_OVER_DTC)

void 
UrlDebugHsCopyChainIndCallBack (dtcTransferEnd_ts 	*dtcTransferEndInd_p);
#endif // URL_DEBUG_HSDPA_OVER_DTC

void 
UrlDebugInit(void);


#if defined (UPGRADE_3G_EDCH)

void 
UrlDebugEdchInitDtcCallback(void);

void 
UrlDebugEdchCopyChainIndCallBack (dtcTransferEnd_ts 	*dtcTransferEndInd_p);

void 
UrlDebugEdchTraceDebug(void);

void
UrlDebugEdchSendDebug(void);
#endif // UPGRADE_3G_EDCH


#if defined (ON_PC)
void
UrlDebugPrintDescriptorInfoChain(dtcF8DescriptorInfo 		*currentF8Desc_p,
								  Int32		 				maxDescsToPrint,
								  Int8 						*headerString);
#else
void 
UrlDebugDisplayAllDebugFilters (void);
#endif // ON_PC								  

void 
UrlDebugResetRbDebugScheme(void);

void
UrlDebugResetPsRbDebugScheme(void);

void 
UrlDebugAllocPtr(Int8 	**destPoolAddr_p, 
					 Int16 	  requestedBytesInDebugBuffer, 
					 Int32 	 *numOfDecreasedBytes_p);

void 
UrlDebugTracePtr(Int8  **destPoolAddr_p, 
					  Int16   requestedBytesInDebugBuffer, 
					  Int32  *numOfDecreasedBytes_p);					 

#endif // if !defined (URLDEBUGUTL_H) 

/* Send RB status debug */
void
UrlSendRbsDebug(void);

 

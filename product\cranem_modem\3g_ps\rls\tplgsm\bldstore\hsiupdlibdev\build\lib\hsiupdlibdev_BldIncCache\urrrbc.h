/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urrrbc.h#5 $
 *   $Revision: #5 $
 *   $DateTime: 2007/03/19 13:59:30 $
 **************************************************************************
 * File Description:
 *
 *    Header file for URRRBC.C. Contains function call declarations
 *    for use by other URRC modules.
 **************************************************************************/

#if !defined (URRRBC_H)
#define       URRRBC_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <urrtypes.h>
#include <urr_sig.h>
#include <urrsirty.h>
#include <urrsirut.h>
#include <urrsir.h>
#include <urrcmr.h>
#include <haw_util.h>
#include <release_api.h> /* ********** change Release_api.h to release_api.h */

/* Define the macros so that the Linked List utility functions use the URRC
 * local allocator and deallocator functions rather than calling KiAllocMemory
 * and KiFreeMemory. The local allocator/deallocator routines are much quicker
 * and reduce system loading. The definition of these macros must be done
 * before utllist.h is included, otherwise, utllist.h will use the default
 * allocator/deallocator routines. */
/* CQ00010790, modified begin */
#define UTLL_ALLOC_MEMORY(sIZE, nODE_PP) UrrLocalAllocZeroMemory (sIZE, nODE_PP)
/* CQ00010790, modified end */
#define UTLL_FREE_MEMORY(nODE_PP) UrrLocalFreeMemory (nODE_PP)

#include <utllist.h>

extern Int32 RRC_TRACE_MASK;

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/

    /* BetaC = 0 is invalid */
#define URR_RBC_GAIN_BETA_C_USE_COMPUTED 0
    /* derived from range of referenceTFC_ID: 0..3 */
#define URR_RBC_REFERENCE_GAIN_FACTOR_ARRAY_SIZE 4
    /* Every element of the array should be initialised to this */
#define URR_RBC_TFC_NOT_USED                       0xFFFFFFFF

#define START_VALUE_FOR_NEW_KEY     0 /* as per 25.331 - 8.1.12.3.1 */

/********** - start*/
/* Number of mSecs in frame */
#define URR_RBC_MILLISECONDS_PER_FRAME  (10)

/* Offset for COUNT-C Activation time timer.
 * The offset is required in order to cover the maximum
 * delay between the creation of MAC answer (CmacHfnConfigCnf) and the
 * actual timer starting in RRC */
#define URR_RBC_COUNT_C_ACT_TIME_TIMER_FRAME_GAP_OFFSET  (4)
/********** - end*/
#define URR_RBC_GUARD_TIMER_LENGTH  (20) /* ********** added  */ //CQ00119887 change to 20 from 5

/***************************************************************************
*   Macro Functions
***************************************************************************/

/* Macros translate between the uas_asn.h type URB_Identity and the          */
/* ups_typ.h type BearerIdentity, and vice versa.  These types have the same */
/* values, but different sizes (Int8 vs enum(int)), so a simple typecast is  */
/* sufficient for this purpose                                               */
/* Convert to BearerIdentity:   */
#define RBC_BEARERIDENTITY(uRBiDENTITY)     ((BearerIdentity)(uRBiDENTITY))
/* Convert to URB_Identity:   */
#define RBC_URB_IDENTITY(bEARERiDENTITY)    ((URB_Identity)(bEARERiDENTITY))


/* Bitmap manipulation macros */
#define SET_BITS(mASK,bITS)     \
{                               \
    (mASK) |= (bITS);           \
}

#define SET_BIT(mASK,bIT)       \
{                               \
    (mASK) |= (1 << (bIT));     \
}

#define CLEAR_BITS(mASK,bITS)   \
{                               \
    (mASK)  &= ~(bITS);         \
}

#define TEST_BITS(mASK,bITS)   (((mASK) & (bITS)) == (bITS))

#define TEST_BIT(mASK,bIT)     ((mASK) & (1<<(bIT)))

#define MASK_EMPTY(mASK,eMPTY)   ((mASK) == (eMPTY))


/* Bits for Radio Bearer List Entry structure's  - rbRlcConfigurationChanged  */
/* one bit for each severity of change for an RLC entity                      */
#define  RBC_RLC_CHANGE_NONE          0x00
#define  RBC_RLC_CHANGE_MINOR         0x01   /* May reconfigure on the fly    */
#define  RBC_RLC_CHANGE_MAJOR         0x02   /* Must re-initialise RLC entity */
#define  RBC_RLC_CHANGE_RE_ESTABLISH  0x04   /* May re-establish RLC entity   */
#define  RBC_RLC_CHANGE_INIT_CONFIG   0x08   /* Must config the entity        */
#define  RBC_RLC_CHANGE_DL_ONLY_RE_ESTABLISH  0x10   /* May re-establish DL RLC entity   */
/* **********, added begin */
#if defined (UPGRADE_3G_UL_EL2)
#define  RBC_RLC_CHANGE_UL_ONLY_RE_ESTABLISH 0x20    /* May re-establish UL RLC entity   */
#endif //UPGRADE_3G_UL_EL2
/* **********, added end */

/* A dummy Transport Channel Id used throughout the RBC for HS DSCH       */
/* channels since there can only ever be one in existence                 */
#define  RBC_DUMMY_HSDSCH_TR_CH_ID    0xFD

/* A dummy Transport Channel Id used throughout the RBC for EDCH       */
/* channels since there can only ever be one in existence                 */
#define  RBC_DUMMY_EDCH_TR_CH_ID    0xFD

#define  RB_MAPPING_NOT_SELECTED    0xFF

/* A dummy Transport Channel Id used throughout the RBC for RACH channels */
/* since there can only ever be one in existence                          */
/* Note: must be matched in MAC and must larger than any valid uplink     */
/* transport channel id                                                   */
#define  RBC_DUMMY_RACH_TR_CH_ID      0xFE

/* A dummy Transport Channel Id used throughout the RBC for FACH channels */
/* since RB mapping info for FACH does not specify a Tr Ch Id (not needed */
/* by MAC                                                                 */
#define  RBC_DUMMY_FACH_TR_CH_ID      0xFF

/* Defines the default minimum offset when determining a activation time */
#define  RBC_MIN_ACTIVATION_TIME_OFFSET 3


/* **********, added begin */
/* rab id is nsapi (25.413 9.2.1.2), nsapi range (0,15) (24.008.10.5.6.2) */
#define RBC_DUMMY_RAD_ID            0xFF
/* **********, added end */

//********** start
#if defined (UPGRADE_ESCC)
#define RBC_TIMER_T324              0x04
#endif
//********** end


/* Output the file and line info if DEVELOPMENT_VERSION otherwise */
/* just set the UE variable                                       */
#if defined (DEVELOPMENT_VERSION)
#define UrrRbcSetInvalidConfiguration(vALID) \
       UrrRbcSetInvalidConfigurationSubstituted ((vALID), (MODULE_NAME),(__LINE__))
#define UrrRbcSetUnsupportedConfiguration(vALID) \
       UrrRbcSetUnsupportedConfigurationSubstituted ((vALID), (MODULE_NAME),(__LINE__))
#define UrrRbcSetOrderedReconfiguration(oRDER) \
       UrrRbcSetOrderedReconfigurationSubstituted ((oRDER), (MODULE_NAME),(__LINE__))
#else
#define UrrRbcSetInvalidConfiguration(vALID) \
       UrrRbcSetInvalidConfigurationSubstituted ((vALID))
#define UrrRbcSetUnsupportedConfiguration(vALID) \
       UrrRbcSetUnsupportedConfigurationSubstituted ((vALID))
#define UrrRbcSetOrderedReconfiguration(oRDER) \
       UrrRbcSetOrderedReconfigurationSubstituted ((oRDER))
#endif

/* **********, added begin */
#define UrrRbcMgOutputCphyCctrchCnfAwaitInfo(cFIGID, tRANS_P) \
       UrrRbcMgOutputCphyCctrchCnfAwaitInfoSubstituted ((cFIGID), (tRANS_P), (MODULE_NAME),(__LINE__))
/* **********, added end */

/* when the Rrc Sequence Number is initialised during a 'modify' the HFN may
 * wrap. If this value is present in the stored Rrc Sequence Number,
 * the HFN will not be wrapped */
#define     URRC_IGNORE_THIS_DL_RRCSN_BEFORE_SMC_REINIT  0

#define     COMMON_NUM_OF_SRBS 5
/* **********, added begin */
#if defined (UPGRADE_3G_UL_EL2)
#define RBC_MULTIPLY_BY_EIGHT_PLUS_SIXTEEN(s)          ( ((s) << 3) + 16 )
#endif //UPGRADE_3G_UL_EL2
/* **********, added begin */
/***************************************************************************
 * Types
 ***************************************************************************/

typedef enum EhsSupportedStatesTag
{
    FACH,
    PCH
}
EhsSupportedStates;

//ICAT EXPORTED ENUM
typedef enum UrrRbcQueueTypeTag
{
    RBC_QUEUE_TYPE_HS,
    RBC_QUEUE_TYPE_EHS,
    RBC_QUEUE_TYPE_NONE
}
UrrRbcQueueType;

typedef struct UrrRbcReferenceGainFactorTag
{
    UGainFactor betaC;
    UGainFactor betaD;
    Int32       ctfc;
    Int8        lRef;
    Int32       kRef;
}
UrrRbcReferenceGainFactor;

/* Structure stores TFCS elements to be sent to the MAC task */
/* Cmac Tfcs message structure */
typedef struct UrrRbcRbMacTfcsTag
{
    MacTFCS                     cmacInfo;
}
UrrRbcRbMacTfcs;

UT_SINGLE_LINK_LIST_DECLARE (UrrRbcRbMacTfcsList, UrrRbcRbMacTfcs);

/* Structure stores Uplink TFCS elements to be sent to the PHY task */
/* Cphy Ul Tfcs message structure */

typedef struct UrrRbcRbUlPhyTfcsTag
{
    UlTfcsConfig     ulTfcsConfig;
}
UrrRbcRbUlPhyTfcs;

UT_SINGLE_LINK_LIST_DECLARE (UrrRbcRbUlPhyTfcsList, UrrRbcRbUlPhyTfcs);

/* Structure stores TFC Subset elements to be sent to the MAC task */
/* Cmac Tfc subset message structure                               */
typedef struct UrrRbcRbPhyTfcSubsetTag
{
    MacTFC_Subset     subset;
}
UrrRbcRbPhyTfcSubset;

UT_SINGLE_LINK_LIST_DECLARE (UrrRbcRbMacTfcSubsetList, UrrRbcRbPhyTfcSubset);

/* Type for use as a bitmap */
typedef Int32 UrrRbcBitMap;

/* Explicit list of TFIs valid on a logical channel, gleaned from the  */
/* RLC size List IE of RB Mapping Info, and preprocessed for the MAC   */
typedef struct UrrRbcUlTfiListTag
{
    MacTfiList  data [maxLoCHperRLC];
}
UrrRbcUlTfiList;


/* Number of types in the enum UTransportChannelType (ups_typ.h) */
/* Bitmap type used to keep track of which Tr Chans have been processed  */
typedef struct UrrRbcProcessedTrChansTag
{
    UrrRbcBitMap  processedType [NUM_TR_CHAN_TYPES];
}
UrrRbcProcessedTrChans;

/* RB mapping structure */
typedef struct UrrRbcRbMappingInfoTag
{
    URB_MappingInfo_latest   asn1MappingInfo;
    UrrRbcUlTfiList  *processedMappingInfo_p [maxRBMuxOptions];
}
UrrRbcRbMappingInfo;

/* PDCP Configuration enum */
typedef enum UrrRbcPdcpConfigTag
{
    RBC_PDCP_UNCONFIGURED,
    RBC_PDCP_CHANGED,
    RBC_PDCP_CONFIGURED
}
UrrRbcPdcpConfig;

/* RADIO BEARER DATABASE ENTRY TYPES */

/* Radio Bearer List entry */
typedef struct UrrRbcRbListElementTag
{
    URB_Identity                    rbIdentity;
    /* For RLC configuration */
    UrrRbcBitMap                    rbRlcConfigurationChanged;
    CrlcConfigReq                   rbRlcConfiguration;
    Int32                           crlcDlAmPduSize;
/* **********, added begin */    
#if defined (UPGRADE_3G_UL_EL2)
    Boolean                         oneSidedRlcReEstablishment; //TRUE if this IE was set to TRUE in RLCInfo IE, FALSE o\w
#endif //UPGRADE_3G_UL_EL2
/* **********, added end */
    /* For MAC configuration */
    UrrRbcRbMappingInfo             rbMappingInfo;
    CpdcpConfigReq                 *pdcpConfigReq_p;
    UrrRbcPdcpConfig                pdcpConfig;
}
UrrRbcRbListElement;

/* Radio Bearer list */
UT_SINGLE_LINK_LIST_DECLARE (UrrRbcRbList, UrrRbcRbListElement);

/* Radio Bearer List Deletion entry */
typedef struct UrrRbcRbDeletionListElementTag
{
    URB_Identity                    rbIdentity;
    Boolean                         signallingRb;
}
UrrRbcRbDeletionListElement;

/* Radio Bearer Deletion list */
UT_SINGLE_LINK_LIST_DECLARE (UrrRbcRbDeletionList, UrrRbcRbDeletionListElement);

/* Radio Bearer List Deletion entry */
typedef struct UrrRbcPdcpDeletionListElementTag
{
    URAB_Identity                   rabIdentity;
    UCN_DomainIdentity              cnDomainIdentity;
    Boolean                         loopback;
}
UrrRbcPdcpDeletionListElementTag;

/* Radio Bearer Deletion list */
UT_SINGLE_LINK_LIST_DECLARE (UrrRbcPdcpDeletionList, UrrRbcPdcpDeletionListElementTag);


typedef struct UrrRbcMacParametersTag
{
    SignedInt16              logPowerRelation; //********** Change powerleft
    USpreadingFactor        spreadingFactor;
#if defined(UPS_CFG_UL_DATA_CLASS_768KBPS)
    Int8                    numberOfDpdchs;
#endif /* UPS_CFG_UL_DATA_CLASS_768KBPS */
    Int8                    numHlSchedGapsForSfReduction;
}
UrrRbcMacParameters;

typedef struct UrrRbcPowerOfsetTag
{
    UGainFactor         gainFactorBetaC;
    UGainFactor         gainFactorBetaD;
    UPowerOffsetPp_m    powerOffsetPp_m;
}
UrrRbcPowerOfset;

/* NOTE: CTFC array may contain CTFCs that have been 'removed'. These will
 * be set to TFCI_NOT_USED, and will be interspered through the array.
 * The removed CTFCs are not included in the 'numberOfTFCs' field. */
typedef struct UrrRbcDlTfcsTableTag
{
    Int32                       ctfc [UPS_MAX_DL_TFC];
    Int16                       numberOfTFCs; /* valid CTFCs only */
}
UrrRbcDlTfcsTable;

/* NOTE: CTFC array may contain CTFCs that have been 'removed'. These will
 * be set to TFCI_NOT_USED, and will be interspered through the array.
 * The removed CTFCs are not included in the 'numberOfTFCs' field. */
typedef struct UrrRbcUlTfcsTableTag
{
    Int32                       ctfc [UPS_MAX_UL_TFC];
    UrrRbcMacParameters         additionalMacParameters [UPS_MAX_UL_TFC];
    UrrRbcPowerOfset            powerOffsets [UPS_MAX_UL_TFC];
    Int16                       numberOfTFCs; /* valid CTFCs only */
    Boolean                     missingTfcsParametersCalculated;
    SignedInt16                  logPowerForCM [CMAC_MAX_SLOT_GAPS_PER_FRAME]; //********** Change powerleft
    USpreadingFactor            minimumSF;
}
UrrRbcUlTfcsTable;


/* Common Transport Channel Info */
typedef struct UrrRbcCommonTrChInfoTag
{
    /* The Ul and Dl DCH TFCS tables */
    UrrRbcUlTfcsTable           *ulDchTfcsTable_p;
    UrrRbcDlTfcsTable           *dlDchTfcsTable_p;
    UrrRbcReferenceGainFactor   dchRefGains[URR_RBC_REFERENCE_GAIN_FACTOR_ARRAY_SIZE];

    /* N.B. Although SCCPCH and PRACH TFCS table IEs are present in
     * AIS messages they must not be used in the current release of the
     * specs. If they are, RRC sets INVALID CONFIGURATION FLAG. Therefore,
     * they are only present in the system information and stored in a
     * seperate location */

    /* Pending MAC UL DCH TFC Subset updates */
    Boolean                     newPendingUlDchTfcSubsetUpdates;
    UrrRbcRbMacTfcSubsetList    pendingUlDchTfcSubsetUpdates;
    /* minimum TFC set */
    MacTFC_List                 minTfcSet;
    /* Extra fields related to UL DCH TFC Subset */
    Boolean                     tfcControlDurationPresent;
    UTFC_ControlDuration        tfcControlDuration;
}
UrrRbcCommonTrChInfo;

/* Logical Channel count element for Transport Channel Lists */
typedef struct UrrRbcLogicalChannelCountTag
{
    Boolean            notMapped;
    Int8               noOfLogChansOnThisTrCh;
    Boolean            noOfLogChansHistory;
    /* Add the support of whether the Dl/Ul_addReconfTransChInfo is present */
    Boolean            UpdateFlag;
}
UrrRbcLogicalChannelCount;

/* UL Transport Channel list element for RACH */
typedef struct UrrRbcRachTrChListElementTag
{
    CmacUlTrChConfigReq            *ulMacTrchInfo_p;
    CmacRachConfigReq              *macRachInfo_p;
    CphyUlTrchConfigReq            *ulPhyTrchInfo_p;
    CphyRachConfigReq              *phyRachInfo_p;
}
UrrRbcRachTrChListElement;

/* UL Transport Channel list element for DCH */
typedef struct UrrRbcUlDchTrChListElementTag
{
    CmacUlTrChConfigReq            *ulMacTrchInfo_p;
    CphyUlTrchConfigReq            *ulPhyTrchInfo_p;
    Boolean                        validForConfiguration;
}
UrrRbcUlDchTrChListElement;

/* UL Transport Channel list element */
typedef struct UrrRbcULTrChListElementTag
{
    UTransportChannelType      trChType;
    UTransportChannelIdentity  transportChannelIdentity;
    union
    {
        UrrRbcRachTrChListElement    rachInfo;
        UrrRbcUlDchTrChListElement   dchInfo;
    }
    trChInfo;
    Int16                      numBitsInFrameB4RatMtch[maxTF];
    Int32                      p_i; /* Pi according to CTFC calculation,
                                       TS 25.331 section 14.10 */
    UrrRbcLogicalChannelCount  logicalChanCount;
}
UrrRbcULTrChListElement;

/* UL Transport Channel list */
UT_DOUBLE_LINK_LIST_DECLARE (UrrRbcULTrChList, UrrRbcULTrChListElement);

/* DL Transport Channel list element for FACH */
typedef struct UrrRbcFachTrChListElementTag
{
    USecondaryCCPCH_Info_r4_modeInfo  *sccpchInfo_p;
    Int8                              ccTrChIndex;
    Boolean                           ctchIndicator;
    SemiStaticTfData                  semiStaticTfData;
    DynamicTfData                     dynamicTfData;
    MacLogicalChannelList             logicalChannelList [maxTF];
    UTransportChannelType             fachTrChType;
}
UrrRbcFachTrChListElement;

/* DL Transport Channel list element for DCH */
typedef struct UrrRbcDlDchTrChListElementTag
{
    SemiStaticTfData                semiStaticTfData;
    DynamicTfData                   dynamicTfData;
    MacLogicalChannelList           logicalChannelList [maxTF];
    UBLER_QualityValue              blerQualityValue;
    Boolean                         validForConfiguration;
}
UrrRbcDlDchTrChListElement;

/* DL Transport Channel list element */
typedef struct UrrRbcDLTrChListElementTag
{
    UTransportChannelType      trChType;
    UTransportChannelIdentity  transportChannelIdentity;
    union
    {
        UrrRbcFachTrChListElement    fachInfo;
        UrrRbcDlDchTrChListElement   dchInfo;
    }
    trChInfo;
    UrrRbcLogicalChannelCount  logicalChanCount;
/*********** add begin*/	
#if defined (RRC_DC_HSDPA)
	Boolean                    configured;
#endif // (RRC_DC_HSDPA)
/*********** add end*/
}
UrrRbcDLTrChListElement;

/* UL Transport Channel list type definition */
UT_SINGLE_LINK_LIST_DECLARE (UrrRbcDLTrChList, UrrRbcDLTrChListElement);

/* Common Radio Link Frequency Info State Enum */
typedef enum UrrRbcCommonRlInfoFrequencyTag
{
    RBC_FREQ_UNINITIALISED,
    RBC_FREQ_NO_CHANGE,
    RBC_FREQ_UPDATED,
    RBC_FREQ_UPDATED_SAME
}
UrrRbcCommonRlInfoFrequency;

typedef enum UrrRbcRadioLinkStateTag
{
    RBC_RL_ADDED,
    RBC_RL_CONFIGURED,
    RBC_RL_MODIFIED
}
UrrRbcRadioLinkState;

/* Radio Link list element definition */
typedef struct UrrRbcRadioLinkListElementTag
{
    CphyRlSetupReq           setupMsg;
    UrrRbcRadioLinkState     rlState;
    Boolean                         dl_DPCH_InfoPerRLPresent;
}
UrrRbcRadioLinkListElement;

/* Radio Link list type definition */
UT_SINGLE_LINK_LIST_DECLARE (UrrRbcRadioLinkList, UrrRbcRadioLinkListElement);


/* List of pointers to Radio Links to be removed */
typedef struct UrrRbcRlRemovalTag
{
    UT_SINGLE_LINK_LIST_NODE_TYPE (UrrRbcRadioLinkList)  *rlToBeRemoved_p;
}
UrrRbcRlRemoval;

UT_SINGLE_LINK_LIST_DECLARE (UrrRbcRlRemovalList, UrrRbcRlRemoval);

/* DOFF                              */
/* ref: 25.331 section 13.4.4 */
typedef struct UrrRbcDoffTag
{
    Boolean                         doffPresent;
    UDefaultDPCH_OffsetValueFDD     doff;
}
UrrRbcDoff;

/* UE variable: TGPS_IDENTITY defines */
/* ref: 25.331 v3.a.0 section 13.4.25 */
//ICAT EXPORTED ENUM
typedef enum  UrrRbcTgpsStatusFlagTag
{
    RBC_TGPS_INACTIVE,
    RBC_TGPS_ACTIVATE,
    RBC_TGPS_ACTIVE,
    RBC_TGPS_DEACTIVATE,
    RBC_TGPS_REINITIALISE
}
UrrRbcTgpsStatusFlag;

//ICAT EXPORTED STRUCT
typedef struct UrrRbcTgpsIdentityElementTag
{
    UTGPSI                             tgpsi;
    UrrRbcTgpsStatusFlag               tgpsStatus;
    UTGCFN                             tgcfn;
    Boolean                            tgpsConfigurationParamsPresent;
    UTGPS_ConfigurationParams_latest   tgpsConfigurationParams;
}
UrrRbcTgpsIdentityElement;

/* UE variable: TGPS_IDENTITY */
//ICAT EXPORTED STRUCT
typedef struct UrrRbcTgpsIdentityTag
{
    Int16                           n;
    UrrRbcTgpsIdentityElement       data[maxTGPS];
}
UrrRbcTgpsIdentity;

/* Established RABs Radio Bearer Status (Started,Stopped) */
/* Note: Started is the default                           */
typedef enum UrrRbcRbStartedTag
{
    URR_RBC_RB_STARTED,
    URR_RBC_RB_STOPPED,
    URR_RBC_RB_HALTED
}
UrrRbcRbStarted;

/* Established RABs Radio Bearer List entry */
typedef struct UrrRbcRabRbListElementTag
{
    URB_Identity                   rbIdentity;
    Int8                           rbSubflow; /* not applicable for SRB */
    UrrRbcRbStarted                rbStarted;
}
UrrRbcRabRbListElement;

/* Established RABs Radio Bearer List */
typedef struct UrrRbcRabRbListTag
{
    Int8                n; /* 1 to maxRBperRAB */
    UrrRbcRabRbListElement data [maxRBperRAB];
}
UrrRbcRabRbList;

/* Established RABS Signalling Radio Bearer List */
typedef struct UrrRbcRabSRbListTag
{
    Int8            n; /* 1 to maxSRBsetup */
    UrrRbcRabRbListElement data [maxSRBsetup];
}
UrrRbcRabSRbList;

/* Established RABs List Element */
typedef struct UrrRbcEstablishedRabsElementTag
{
    URAB_Identity                        rabIdentity;
    UCN_DomainIdentity                   cnDomainIdentity;
    Boolean                              nasSynchronisationIndicatorPresent;
    UNAS_Synchronisation_Indicator       nasSynchronisationIndicator;
    URe_EstablishmentTimer               reEstablishmentTimer;
    Boolean                              loopback;
    UrrRbcRabRbList                      rbList;
}
UrrRbcEstablishedRabsListElement;

/* UrrRbcEstablishedRabs list type definition */
UT_SINGLE_LINK_LIST_DECLARE (UrrRbcEstablishedRabsList, UrrRbcEstablishedRabsListElement);

/*  ESTABLISHED_RABS  25.331 section 13.4.5  */
typedef struct UrrRbcEstablishedRabsTag
{
 UrrRbcEstablishedRabsList  establishedRabsList;
 UrrRbcRabSRbList           srbList;
}
UrrRbcEstablishedRabs;

#if defined (UPGRADE_3G_HSDPA)

typedef struct UMAC_hs_DelQueueExpandTag
{
 Int8  /* 0 to 7 */     mac_hsQueueId;
 Boolean                IsFromAddOrReconf;
}
UMAC_hs_DelQueueExpand;

/* Store MAC HS-Queue and MAC-d FLOW information */
UT_SINGLE_LINK_LIST_DECLARE (UrrRbcCmacHsQueueList, CmacHsQueueInfo);
UT_SINGLE_LINK_LIST_DECLARE (UrrRbcCmacHsDelQueueList, UMAC_hs_DelQueueExpand);

//ICAT EXPORTED STRUCT
typedef struct UrrRbcHsdpaInfoTag
{
    /* 13.4.8oo HS_DSCH_RECEPTION
     * This variable indicates whether HS-SCCH and HS-DSCH reception procedures
     * are ongoing. See subclause 8.5.25 for actions related to the setting of this variable. */
    Boolean                         hsDschReception;
    /* 13.4.8o H_RNTI
     * This variable stores the assigned H-RNTI for this UE when in CELL-DCH
     * state and a HS-DSCH transport channel has been allocated. */
    Boolean                         hrntiPresent;
    UH_RNTI                         hrnti;
    UrrHrntiType                    hrntiType;                              /**< Indicate whether the type of H-RNTI used */

    /* HS-SCCH Info */
    Boolean                         hs_scch_InfoPresent;
    UHS_SCCH_Info_latest_fdd        hs_scch_Info;
    /* Measurement Feedback Info */
    Boolean                                 measurement_feedback_InfoPresent;
    UMeasurement_Feedback_Info_latest_fdd   measurement_feedback_Info;
    /* HARQ Info */
    Boolean                         harqInfoPresent;
    Int8                            numberOfHarqProcesses;
    UHARQMemorySize                 explicitHarqInfo[ maxHProcesses ];
    Boolean                         implicitHarqInfo;
    /* Power Control */
    Boolean                         deltaACKPresent;
    UDeltaACK                       deltaACK;
    Boolean                         deltaNACKPresent;
    UDeltaNACK                      deltaNACK;
    Boolean                         ack_NACK_repetition_factorPresent;
    UACK_NACK_repetitionFactor      ack_NACK_repetition_factor;
    UHARQ_Preamble_Mode             harqPreambleMode;
    /* Serving HD-DSCH Radio Link */
    Boolean                         servingCellPresent;
    UPrimaryScramblingCode          servingCell;
    /* Additional serving HD-DSCH cell parameters to be passed to L1 */
    UTX_DiversityMode               txDiversityMode;
    Int16                           dpchFrameOffset;
    /* 64QAM support */
    Boolean                         downlink64QamConfigured;

    /* Tr BK Size Table */
    Boolean                         trBkSizeTableOctetAligned;

    /* MAC-d Flows & MAC-HS Queues */
    /* E.S.: These lists are not displayed correctly in ACAT struct viewer,
      therefore they should stay at the end of this strcut */
    UrrRbcCmacHsQueueList           hsQueueList;
    UrrRbcCmacHsDelQueueList        hsDelQueueList;
}
UrrRbcHsdpaInfo;

#endif /* UPGRADE_3G_HSDPA */
/*********** add begin*/
#if defined (RRC_DC_HSDPA)

typedef struct UrrRbcDcHsdpaInfoTag
{
    //TRUE -indicate that the Secondary CEll Info Fdd IE was present and saved. FALSE - the IE params are invalid.
    Boolean secondaryCellInfoFddPresent;
    /*   (13.4.8od) - SECONDARY_CELL_HS_DSCH_RECEPTION
             This variable indicates whether HS-SCCH and HS-DSCH reception procedures are configured
             for a secondary serving.HS-DSCH cell See subclause 8.5.51 for actions related to the setting of this variable
       */
    Boolean                         secondaryCellhsDschReception;

     /* 13.4.8o H_RNTI
     * This variable stores the assigned H-RNTI for this UE when in CELL-DCH
     * state and a HS-DSCH transport channel has been allocated. */
    UH_RNTI                         hrnti;

    /* 64QAM support */
    Boolean                                            downlink64QamConfigured;

    /* Tr BK Size Table */
    Boolean                                            trBkSizeTableOctetAligned;

    UPrimaryScramblingCode                             primaryScramblingCode;

    UPrimaryScramblingCode                             dl_ScramblingCode;//use of UPrimaryScramblingCode - Int 16 do to note in 103.3.6.31a

    UDL_SecondaryCellInfoFDD_r9_hS_SCCHChannelisationCodeInfo hS_SCCHChannelisationCodeInfo;
    UMeasurementPowerOffset                            measurementPowerOffset;
    UUARFCN                                            uarfcn_DL;

    Boolean                                            differentTxDiversityMode;
/* ********* add begin */
#if defined (UPGRADE_HARQ_CONFIG_DC_HSDPA)
    Int8                                               numberOfHarqProcesses;
    UHARQMemorySize                                    explicitHarqInfo[ maxHProcesses ];
#endif //UPGRADE_HARQ_CONFIG_DC_HSDPA
/* ********* add end */
}
UrrRbcDcHsdpaInfo;

#endif /* RRC_DC_HSDPA */
/*********** add end*/
#if defined (UPGRADE_3G_EDCH)

//ICAT EXPORTED STRUCT
typedef struct UrrRbcHsupaInfoTag
{
    Boolean         primaryErntiPresent;
    Boolean         secondaryErntiPresent;
    Boolean         ttiPresent;
    Boolean         harqPresent;
    Boolean         eDpcchInfoPresent;
    Boolean         eDpdchInfoPresent;
    Boolean         servingCellPresent;
//********** start
#if !defined (UPGRADE_3G_POWER_INTERPOLATION)
//********** end
    Boolean         powerInterpolation;
//********** start
#endif
//********** end
    UE_RNTI         primaryErnti;           /* E_RNTI variable (13.4.4.a) */
    UE_RNTI         secondaryErnti;         /* E_RNTI variable (13.4.4.a) */
    UPrimaryScramblingCode  servingCell;
    UUL_AddReconfTransChInformation_harq_Info_latest harqRvConfig;
    CmacEdchTfcsConfigReq   cmacEdchTfcsConfigReq;
    Int8                macdFlowMap;
    UE_DCH_AddReconf_MAC_d_Flow_latest  macdFlowList [maxE_DCHMACdFlow];
    Int16               logicalChannelsMap;
    CmacEdchMacDFlowLogChInfo   logicalChannelList [CMAC_MAX_LOG_CH_PER_TF];
} UrrRbcHsupaInfo;

#endif /* UPGRADE_3G_EDCH */

typedef struct AdditionalRachConfigurationTag
{
    Boolean             present;
    Int16               rlcSize;
    Int8                tfi;
    Boolean             powerOffsetPp_mPresent;
    UPowerOffsetPp_m    powerOffsetPp_m;
    UGainFactor         gainFactorBetaC;
    UGainFactor         gainFactorBetaD;
    Boolean             referenceTFC_IDPresent;
    UReferenceTFC_ID    referenceTFC_ID;
}
AdditionalRachConfiguration;

#if defined(UPGRADE_3G_HSDPA)

/**************** Enhanced CELL_FACH ****************/

/**\struct UrrRbcEhsReceptionGlobalsTag - Global Variables
 */
typedef struct UrrRbcEhsReceptionGlobalsTag
{
    Boolean triggeredMeasurementReport;                   /**< TRIGGERED_MEASUREMENT_REPORT indicator */
    Boolean hsDschReceptionOfCcchEnabled;                 /**< HS_DSCH_RECEPTION_OF_CCCH_ENABLED indicator */
    Boolean hsDschReceptionCellFachState;                 /**< HS_DSCH_RECEPTION_CELL_FACH_STATE indicator */
    Boolean hsDschReceptionGeneral;                       /**< HS_DSCH_RECEPTION_GENERAL indicator */
//********** begin
#if defined(UPGRADE_HS_FACH_DRX)
    Boolean hsDschCellFachDrx;                            /**< HS_DSCH_DRX_CELL_FACH_STATUS indicator */
#endif
//********** end
}
UrrRbcEhsReceptionGlobals;

typedef struct UrrRbcEhsSelectedParamsIndexesTag
{
     /** Selection Params */
    Int8                            selectedCommonHrntiIndex;   /**< Holds the index of the selected Common H-RNTI */
    Int8                            selectedPichForHsIndex;     /**< Holds the index of the selected PICH for HS-DSCH paging configuration */
    Int8                            selectedHsPdschIndex;       /**< Holds the index of the selected HS-PDSCH code to be used in HS Less reception */
    UrrHrntiType                    selectedHrntiType;          /**< The type of the selected H-RNTI to used in the configuration */
}
UrrRbcEhsSelectedParamIndexes;

/**\struct UrrRbcEhsDataTag - RBC EHS Database
 */
typedef struct UrrRbcEhsDataTag
{
    UrrRbcEhsReceptionGlobals       ehsReceptionVars;           /**< Global HS-DSCH status variable */
    Boolean                         sysInfoParamsValid;         /**< ECF related system information IEs received */
    USysInfoType5_v770ext_IEs_fdd   sysInfoParams;              /**< Common/Paging system information IEs */
    UrrRbcEhsSelectedParamIndexes   selectedParams;             /**< Struct of all selected params available */
//********** begin
#if defined(UPGRADE_HS_FACH_DRX) 
    USysInfoType5_v860ext_IEs_fdd   hsFachDrxParams;
#endif
//********** end

    Boolean             macEhsReset;  /**< TRUE when MacHsReset is set by NW and not sent to MAC yet */

    Int8    noOfQueuesInAddList; /**< num of queues in ehsQueueList */
    Int8    noOfQueuesInDelList; /**< num of queues in ehsDelQueueList */
    UMAC_ehs_AddReconfReordQ_r9  ehsQueueList[maxQueueIDs]; /**< MAC-ehs queues to add */
    CmacEhsDelReorderingQueueInfo  ehsDelQueueList[maxQueueIDs]; /**< MAC-ehs queues to delete */
}
UrrRbcEhsData;
#endif //UPGRADE_3G_HSDPA 

/**************** Enhanced CELL_FACH ****************/

#if defined(UPGRADE_UL_ECF) // ********** begin   	  
/****************UL Enhanced CELL_FACH ****************/

/**\struct UrrRbcEhsReceptionGlobalsTag - Global Variables
 */
typedef struct UrrRbcCommonEdchGlobalsTag
{
    Boolean commonEdchTransmission;                       /**< COMMON_E_DCH_TRANSMISSION  indicator */
    Boolean readyForCommonEdch;                           /**< READY_FOR_COMMON_EDCH  indicator */
    Boolean prevReadyForCommonEdch;                       /**< holds previous READY_FOR_COMMON_EDCH  indicator - needed for 25.331 - 8.4.1.11.1 */
    Boolean hspaRntiStoredCellPch ;                       /**< HSPA_RNTI_STORED_CELL_PCH  indicator */
}
UrrRbcCommonEdchGlobals;


/**\struct UrrRbcEhsDataTag - RBC EHS Database
 */
typedef struct UrrRbcCommonEdchDataTag
{
    UrrRbcCommonEdchGlobals               commonEdchVars;             /**< Global Common Edch status variable */
    Boolean                               sysInfoParamsValid;         /**< UL ECF related system information IEs received */
    UCommonEDCHSystemInfoFDD              sysInfoParams;              /**< Common E-DCH system information IEs */
    UrrSirAllocatedMemList                commonEdchMemList;        
    UUARFCN                               uarfcn_dl;
    UUARFCN                               uarfcn_ul;
    Boolean             maciReset;  /**< TRUE when MacHsReset is set by NW and not sent to MAC yet */
    UPrimaryScramblingCode                servingCellPsc;    /**<holds the PSC of the the cell which the READY_FOR_COMMON_EDCH relates to  - needed for 25.331 - 8.4.1.11.1 */
    UUARFCN                               currentDlUarfcn;   /**<holds the dlUarfcn of the the cell which the READY_FOR_COMMON_EDCH relates to  - needed for 25.331 - 8.4.1.11.1 */
    UrrSmcModeAndState                    prevModeState;     /**<holds the prev Mode and State of the Ready for Common Edch Variable was determined -  needed for 25.331 - 8.4.1.11.1 */    
    UrrSmcModeAndState                    curModeState;     /**<holds the current Mode and State of the Ready for Common Edch Variable was determined -  needed for 25.331 - 8.4.1.11.1 */
    Boolean                               commonEdchIsConfigured;   /**< if set to TRUE, EDCH is donfigured in MAC */

}
UrrRbcCommonEdchData;

/****************UL Enhanced CELL_FACH ****************/ 
#endif /* UPGRADE_UL_ECF  */ // ********** end 

//ICAT EXPORTED STRUCT
typedef struct UrrRbcCpcInfoTag
{
    UDTX_DRX_TimingInfo_latest      dtxDrxTimingInfo;
    UDTX_DRX_Info_latest            dtxDrxParams;
    Boolean                         isDtxDrxParamsSet;
    UHS_SCCH_LessInfo_latest        hsScchLessParams;
    Boolean                         isHsScchLessParamsSet;
}
UrrRbcCpcInfo;

/* **********, added begin */
/* stick in a pin whenever there's room */
typedef struct RabmRejectedEstablishedRabTag
{
    Int8            numOfRabIds;
    Int8            rabIdList[RABMRRC_MAX_RABS];
}RabmRejectedEstablishedRab;
/* **********, added end */

//********** start
#if defined (UPGRADE_ESCC)
//ICAT EXPORTED ENUM
typedef enum  UrrRbcEsccProcedureStateTag 
{
	ESCC_OFF, //HSCCH is closed. 
	/* ********** remove */
	ESCC_WAITING_HSCCH_SETUP_CNF, 
	ESCC_WAITING_HSCCH_RELEASE_CNF,
	ESCC_WAITING_FOR_CFN_CNF, //waiting for Activation time CNF from L2
	ESCC_WAITING_FOR_CFN_AND_SETUP_CNF, //waiting for Activation time from L2 & HSCCH setup confirmation from L1
	ESCC_WAITING_FOR_SCC_ORDER, //HSCCH is open, no change cell indication was received yet
	ESCC_PROCESS_SERVING_CELL_CHANGE //Change cell indication was received on HSCCH
	
}UrrRbcEsccProcedureState;

typedef struct  UrrRbcEsccParametersTag 
{
	UrrRbcEsccProcedureState		esccState;
    Int8                            occupiedMask;/* ********** add */
    UServingCellChangeParameters    servingCellChangeParameters[UPS_MAX_RADIO_LINKS]; /* ********** modify */
    /* ********** remove */
	UTargetCellPreconfigInfo_r9     targetCellPreconfigInfo[UPS_MAX_RADIO_LINKS];
	Int8							indexTargetCell;
	Boolean                         activationTimePresent;
	UActivationTime                 activationTime;
    Boolean                         isE1dPending;
    UPrimaryScramblingCode          pendingPsc;
    Int16                           e1dMessageId; /* ********** add */
}UrrRbcEsccParameters;

#endif//UPGRADE_ESCC
//********** end


/* RADIO BEARER DATABASE type */
typedef struct UrrRbcRadioBearerDatabaseTag
{
    Boolean                         dataBaseIsInit;
    Boolean                         dataBaseIsValid;
    /* Radio Bearer List */
    UrrRbcRbList                    radioBearerList;
    /* Radio Bearer List Deletions */
    UrrRbcRbDeletionList            radioBearerDeletionsList;
    /* Pdcp/RABM List Deletions */
    UrrRbcPdcpDeletionList          pdcpDeletionsList;
    /* Common Transport Channel Info */
    UrrRbcCommonTrChInfo            commonTrChInfo;
    /* UL Transport Channel List */
    UrrRbcULTrChList                ulTrChList;
    /* DL Transport Channel List */
    UrrRbcDLTrChList                dlTrChList;
    /* Common Radio Link Info - only valid in CELL_DCH */
    CphyRlCommonSetupReq            commonRlInfoReq;
    /* Radio Link List - only valid in CELL_DCH */
    UrrRbcRadioLinkList             radioLinkList;
    /* Radio Link List Deletions - only valid in CELL_DCH */
    UrrRbcRadioLinkList             radioLinkDeletionsList;
    /* Default DPCH Offset Value */
    UrrRbcDoff                      doff;
    /* UE variable: TGPS_IDENTITY  25.331 section 13.4.25 */
    UrrRbcTgpsIdentity              tgpsIdentity;
    /*  UE variable: ESTABLISHED_RABS 25.331 section 13.4.5  */
    UrrRbcEstablishedRabs           establishedRabs;
    UMaxAllowedUL_TX_Power          maxAllowedUL_TX_Power;
    /* This flag is set to indicate that we have had a radio link failure
     * and must only configure rb mappings for SRBs until we have
     * successfully completed a CELL UPDATE */
    Boolean                         onlyConfigSRBs;
    Boolean                         fachRachSRBsConfigured; //FACH/RACH SRBs mapped and configured
    Boolean                         fachRachSRBsMapped; //FACH/RACH SRBs mapped but not configured yet
    Boolean                         ecfSRBsMapped;   //HS-DSCH/RACH SRBs mapped but not configured yet
#if defined (UPGRADE_3G_HSDPA)
    UrrRbcHsdpaInfo                 hsdpaInfo;
#endif
#if defined (UPGRADE_3G_EDCH)
    UrrRbcHsupaInfo                 hsupaInfo;
#endif /* UPGRADE_3G_EDCH */
/*********** add begin*/
#if defined (RRC_DC_HSDPA)
	UrrRbcDcHsdpaInfo				dcHsdpaInfo;
#endif //RRC_DC_HSDPA
/*********** add end*/
#if defined(UPGRADE_UL_ECF) // ********** begin
    Boolean                         hsupaInfoDchBackupWhileOnFachIsSet;
    UrrRbcHsupaInfo                 *hsupaInfoDchBackupWhileOnFach_p; // holds HsupaInfo (for CELL_DCH) built from last NW Hsupa configuration while on CELL_FACH
#endif /* UPGRADE_UL_ECF  */ // ********** end   


    Boolean                         AssignCount_C;
    /* CCCH message enhancement (see RP-50392) */
    AdditionalRachConfiguration additionalPrachConfiguration;
    Boolean                         ulDchConfigured;
    Boolean                         dlDchConfigured;
    Boolean                         edchConfigured;
    Boolean                         hsdschConfigured;
    Int8                            ulBestRbMappings [RB_32];
    Int8                            dlBestRbMappings [RB_32];
    Boolean                         resetUeToIdleModeSent;
    UrrRbcQueueType                 currentMacQueueType; /**< Which MAC queues are currently configured */
#if defined(UPGRADE_3G_HSDPA)
    UrrRbcEhsData                   urrRbcEhs; /**< EL2 & ECF database. Located here to support backup during reconfigurations */
    //CPC related parameters
    UrrRbcCpcInfo                   cpcInfo;
    Boolean                         dtxDrxStatus;
    Boolean                         hsScchLessStatus;
#endif //UPGRADE_3G_HSDPA 
#if defined(UPGRADE_UL_ECF) // ********** begin	 
    UrrRbcCommonEdchData            urrRbcCommonEdch; // common Edch info (ECF_UL)
#endif /* UPGRADE_UL_ECF  */ // ********** end   
/* **********, added begin */
    RabmRejectedEstablishedRab      rabmRejectedEstablishedRabs;
/* **********, added end */

//********** start
#if defined (UPGRADE_ESCC)
	UrrRbcEsccParameters			esccParm;
#endif//UPGRADE_ESCC
//********** end



}
UrrRbcRadioBearerDatabase;

/* COMMON PHYSICAL CHANNEL SYSTEM INFORMATION type */
/* There will be one of these for SIB5 data and one for SIB 6 data,
 * if it exists */
typedef struct UrrRbcCommonPhysChSysInfoTag
{
    UPICH_PowerOffset                             pich_PowerOffset;
    UAICH_PowerOffset                             aich_PowerOffset;
    Boolean                                       tx_DiversityIndicator;
    Boolean                                       tx_DiversityIndicatorPresent;
    UPRACH_SystemInformationList                  prach_SystemInformationList;
    UrrSirAllocatedMemList                        prachSysInfoMemList;
    USCCPCH_SystemInformationList                 sCCPCH_SystemInformationList;
    UrrSirAllocatedMemList                        sccpchSysinfoMemList;
    UCBS_DRX_Level1Information                    cbs_DRX_Level1Information;
    Boolean                                       cbs_DRX_Level1InfoPresent;
    Boolean                                       cBS_DRX_Level1Information_extensionPresent;
    UCBS_DRX_Level1Information_extension_latest       cBS_DRX_Level1Information_extension;
    /* The persistence Level data is dynamic and therefore NOT in SIB 5/6.
     * However it does releate to SIB5/6. In SIB7 there are sib5 and sib6 IEs.
     * If the prach sys info IE is present in SIB 6 then the SIB 6 persistent
     * Level IE is present in SIB7. If the prach sys info IE is not present in
     * SIB 6 or SIB 6 itself is not present then the SIB 6 persistent Level IE
     * is NOT present in SIB7. Therefore the IE is include here. */
    UDynamicPersistenceLevelList                  perLevelList;
    Boolean                                       hsdpaCapCellInd;
    Boolean                                       hsupaCapCellInd;
	Boolean                                       hspaPlusCapCellInd;/*CQ00088273 add*/
#if defined (RRC_DC_HSDPA)
	Boolean                                       dcHspacapCellInd;/* CQ00115429 added */
#endif /* RRC_DC_HSDPA */
}
UrrRbcCommonPhysChSysInfo;

//********** start

/********** - modify start*/
//ICAT EXPORTED ENUM
typedef enum UrrRbcTimerIdentityTag
{       
    URRC_RBC_TIMER_COUNT_C_ACT_TIME_TX_WAIT_RLC_ACK = 0,
#if defined (UPGRADE_ESCC)        
    URRC_RBC_T324 = 1, 
#endif    //UPGRADE_ESCC
    URR_RBC_GUARD = 2, /* ********** added */
	URRC_RBC_MAX_TIMERS
}
UrrRbcTimerIdentity;
/********** - modify end*/

typedef struct UrrRbcTimerTag
{
	KiTimer timer;
	void	(*expiryFn)(UrrRbcTimerIdentity timerId); //********* modify /********** - modify*/
}
UrrRbcTimer;

//********** end

/* **********, added, begin */
typedef struct UrrRbcRachDataTag
{
    /* RACH VARIABLES */

    /* Configuration bitmask flags which configuration data has been received */
    UrrRbcBitMap                  rachConfigMask;

    /* UL Interference (from SIB7) */
    UUL_Interference              ulInterference;

    /* Save Rach configuration message */
    /* Save CPHY & CMAC UL Trch configuration messages */
    UrrRbcULTrChList              ulTrChList;

    /* Stored TFCS for the idle mode RACH */
    UrrRbcUlTfcsTable             *tfcsDataList_p;

    /* Pending connected mode RACH TFCS configuration for the
     * lower layers */
    UrrRbcUlTfcsTable             *rachTfcsTable_p;

    /* The TTI of the last PRACH that was selected */
    UTransmissionTimeInterval      lastSelectedTti;

    /* The index of the last selected PRACH index */
    Int8                           lastSelectedPrachIndex;
}
UrrRbcRachData;
/* **********, added, end */

#define RBC_SIB6_TX_DIVERSITY_PRESENT 0x01
#define RBC_SIB6_PRACH_SYS_INFO_LIST_PRESENT 0x02
#define RBC_SIB6_SCCPCH_SYS_INFO_LIST_PRESENT 0x04
#define RBC_SIB6_CBS_DRX_LEVEL1_PRESENT 0x08
#define RBC_SIB6_PICH_INFO_PRESENT 0x10

#define RBC_SIB6_IE_ALL_PRESENT  (RBC_SIB6_TX_DIVERSITY_PRESENT |           \
                                  RBC_SIB6_PRACH_SYS_INFO_LIST_PRESENT |    \
                                  RBC_SIB6_SCCPCH_SYS_INFO_LIST_PRESENT |   \
                                  RBC_SIB6_CBS_DRX_LEVEL1_PRESENT)

/***************************************************************************
 * Typed Constants
 ***************************************************************************/

/***************************************************************************
 * Function Prototypes
 ***************************************************************************/
void UrrRbcHandleDataActivityQuery(RadioResourceControlEntity *urr_p);

Boolean UrrRbcPhyConfigInProgress (void);

Boolean UrrRbcMgConfigInProgress (URRC_StateIndicator stateInd);               //-+ PTK ********** 11-Nov-2012 +-
#if defined (UPGRADE_DSDS)
void UrrRbcSendCphyDsControlPchReq(Boolean                pchOn);
/* **********, added begin */
void UrrRbcSendCphySuspendByPchReq(void);
void UrrRbcSendCphyResumeByPchReq(void);
/* **********, added end */
/* ********** added begin */	
#if defined (UPGRADE_DSDSWB)
Boolean UrrRbcPhyConfigInProgressOfSpecifiedCard (Int8 urrcTaskNum);
#endif//UPGRADE_DSDSWB
/* ********** added end */	
#endif

void UrrRbcSendCphyUeCapabilityConfigReq (void);

void UrrRbcSetPhyConfigInProgress (Boolean value);

void UrrRbcSendInternalSignalPhyConfigFinish (void);

void UrrRbcActivate (void);

void UrrRbcProcessInternalSignal (SignalBuffer *signal_p);

void UrrRbcCpdcpRelocCnf (RadioResourceControlEntity *urr_p);
void UrrRbcHandleCpdcpReleaseReqCont  (RadioResourceControlEntity *urr_p); /********** - add*/


void UrrRbcCphyOutOfSyncInd (RadioResourceControlEntity *urr_p);

void UrrRbcCrlcStatusInd (RadioResourceControlEntity *urr_p);

void UrrRbcCphyCcTrChConfigCnf (RadioResourceControlEntity *urr_p);

void UrrRbcCphySyncInd (RadioResourceControlEntity *urr_p);

#if !defined (UPGRADE_EXCLUDE_2G)
void UrrRbcCphySuspendInd (RadioResourceControlEntity *urr_p);
#endif

void UrrRbcCrlcResumeInd (RadioResourceControlEntity *urr_p);

void UrrRbcCrlcSuspendCnf (RadioResourceControlEntity *urr_p);

void UrrRbcCrlcPrepareCipherCfgChangeCnf (RadioResourceControlEntity *urr_p);

void UrrRbcCrlcPrepareCipherCfgChangeCnf (RadioResourceControlEntity *urr_p);

void UrrRbcRrcSecurityKeySetConfigReq (RadioResourceControlEntity *urr_p);

/* **********, added begin */
#if defined (UPGRADE_SRVCC)
void UrrRbcRrcSecurityKeySetConfigReqForBuffered (RrcSecurityKeySetConfigReq *msg_p);
Boolean UrrRbcGetRrcSecuKeyCfgReqValid (void);
void UrrRbcSetRrcSecuKeyCfgReqValid (Boolean rrcSecuKeyCfgReqValid);
RrcSecurityKeySetConfigReq* UrrRbcGetRrcSecuKeyCfgReq (void);
#endif
/* **********, added end */

void UrrRbcSendCrlcStopReq (Int8 numberOfBearers,
                            BearerIdentity bearers []);

void UrrRbcSendCrlcHaltReqAllRbs (Boolean maintainSRBdataReceive);/*CQ00034612 modify*/

void UrrRbcSendCrlcContinueReq (Int8 nofBearers, BearerIdentity  bearers []);

void UrrRbcSendCrlcContinueReqAllRbs (void);

void UrrRbcRrcPowerClassReq (RadioResourceControlEntity *urr_p);

void UrrRbcRrcMeDataReq (UrrInternalSignalRrcMeDataReq *sig_p);

void UrrRbcProcessSib1 (UrrSirCellType          cellType,
                        UPrimaryScramblingCode  cellPrimaryScrCode,
                        UUARFCN                 uarfcn,
                        USysInfoType1          *uSysInfoType1_p);

void UrrRbcProcessSib3 (UMaxAllowedUL_TX_Power  maxPwr);

void UrrRbcProcessSib5 (UrrSirCellType                  cellType,
                        UPrimaryScramblingCode          cellPrimaryScrCode,
                        UUARFCN                         uarfcn,
                        USysInfoType5                  *uSysInfoType5_p);

void UrrRbcProcessSib6 (UrrSirCellType                  cellType,
                        UPrimaryScramblingCode          cellPrimaryScrCode,
                        UUARFCN                         uarfcn,
                        USysInfoType6                  *uSysInfoType6_p);

void UrrRbcProcessSib7 (USysInfoType7  *uSysInfoType7_p);

void UrrRbcInit (void);

void UrrRbcFlattenLogChanList (MacLogicalChannelList  *msgList_p,
                               ULogicalChannelList    *chanList_p);

void UrrRbcFlattenSemiStaticTfData (UTransmissionTimeInterval  tti,
                                    SemiStaticTfData          *msg_p,
                                    UTransportFormatSet       *asn1_p);

void UrrRbcFlattenMacTfcsList (UrrRbcRbMacTfcsList *flatTfcsList_p,
                               UTFCS *tfcs_p,
                               UrrRbcReferenceGainFactor *refGains_p);

Int32 UrrRbcFindLargestRlcSize (DynamicTfData *tfs_p, BearerIdentity rbId,
                                Int8  logChanNo,
                                MacLogicalChannelList  logChanList [],
                                MacTfiList  *tfiList_p);

Boolean UrrRbcFindOnlyRlcSize (DynamicTfData *tfs_p, BearerIdentity rbId,
                               Int8  logChanNo,
                               MacLogicalChannelList  logChanList [],
                               MacTfiList  *tfiList_p,
                               Int32  *rlcSize_p);

UTFCS *UrrRbcFindSccpchTfcs (USCCPCH_SystemInformationList *sccpchList_p,
                             USCCPCH_SystemInformationListElement *ele_p);

UT_SINGLE_LINK_LIST_NODE_TYPE (UrrRbcDLTrChList) *UrrRbcFindDlTrch (
                                             UTransportChannelType     trChType,
                                             UTransportChannelIdentity trChId);

Boolean UrrRbcSaveDlTfcInfo (USCCPCH_SystemInformationList        *list_p,
                             USCCPCH_SystemInformationListElement *ele_p,
                             UrrRbcDlTfcsTable        *fachPchTfcsTable_p);

void UrrRbcSaveDlTfs (Int8 macHeaderSize,
                      SemiStaticTfData *ss_p, DynamicTfData *dyn_p,
                      UTransportFormatSet *tfs_p);

UT_SINGLE_LINK_LIST_NODE_TYPE(UrrRbcRbList) *UrrRbcGetRbListElement (
                                                 URB_Identity rbId,
                                                 Boolean      createIfNotFound);

UT_SINGLE_LINK_LIST_NODE_TYPE(UrrRbcEstablishedRabsList)
                                                 *UrrRbcGetRabListElement(
                                                 URAB_Info_latest    *rabInfo_p,
                                                 Boolean      createIfNotFound);

UT_SINGLE_LINK_LIST_NODE_TYPE (UrrRbcEstablishedRabsList)
                                    *UrrRbcGetPreconfiguredRabListElement (void);

Boolean UrrRbcCheckRbConfigured (BearerIdentity rbId);

UT_SINGLE_LINK_LIST_NODE_TYPE(UrrRbcDLTrChList) *UrrRbcCreateDlTrChListElement (
                                    UrrRbcDLTrChList           *list_p,
                                    UTransportChannelType       chanType,
                                    UTransportChannelIdentity   chanId);

void UrrRbcCreateUlTrChListElement (UrrRbcULTrChList           *list_p,
                                    UTransportChannelType       chanType,
                                    UTransportChannelIdentity   chanId);

void UrrRbcDestroyDlTrChListElement (UrrRbcDLTrChListElement *ele_p);

void UrrRbcDestroyUlTrChListElement (UrrRbcULTrChListElement *ele_p);

void UrrRbcReConfigureIdleModeSccpch (void);

void UrrRbcSendCmacRachConfigReqs (Int8  configId,
                                   CmacRachConfigReq *fixed_p,
                                   UrrRbcUlTfcsTable  *tfcs_p,
                                   ActivationTime *activationTime_p);

void UrrRbcSendCmacUlTrchConfigReq (Int8  configId,
                                    ActivationTime         *actTime_p,
                                    Boolean                 firstSignal,
                                    CmacUlTrChConfigReq    *msgToSend_p);

void UrrRbcSendCphySccpchConfigReq (Int8 configId, Int8 ccTrchIndex,
                                 USecondaryCCPCH_Info_latest_modeInfo *sccpchInfo_p,
                                 ActivationTime *activationTime_p);

void UrrRbcSendCphyCcTrChReleaseReq (Int8 configId, Int8 ccTrchIndex);

void UrrRbcSendCphyDlTfcConfigReqs (Int8  configId, Int8 ccTrchIndex,
                                    ActivationTime *actTime_p,
                                    UrrRbcDlTfcsTable *dlTfcsTable_p);

void UrrRbcSendCphyDlTrchConfigReq (Int8                       configId,
                                    Int8                       ccTrchIndex,
                                    ActivationTime            *actTime_p,
                                    UTransportChannelType      trChType,
                                    UTransportChannelIdentity  trChId,
                                    SemiStaticTfData          *ssData_p,
                                    DynamicTfData             *dyData_p,
                                    Boolean                   ctchIndicator,
                                    UBLER_QualityValue        blerQualityValue);

void UrrRbcSendCphyRachConfigReq (Int8  configId, CphyRachConfigReq  *msg_p,
                                  ActivationTime    *activationTime_p);

void UrrRbcSendCphyRlSetupReq (Int8           configId,
                               CphyRlSetupReq *msg_p,
                               ActivationTime *actTime_p);

void UrrRbcSendCphyRlReleaseReq (Int8                   configId,
                                 Int8                   ccTrchIndex,
                                 UPrimaryScramblingCode primScrCode,
                                 ActivationTime         *actvationTime_p);

void UrrRbcSendCphyUlTrchConfigReq (Int8  configId, ActivationTime *actTime_p,
                                    Int8  cctrchIndex,
                                    CphyUlTrchConfigReq *msg_p);

void UrrRbcSendCrlcReleaseReq (BearerIdentity  rbId);

void UrrRbcSetMaxUlTxPower (UMaxAllowedUL_TX_Power maxPwr);

UMaxAllowedUL_TX_Power UrrRbcGetMaxUlTxPower (void);

void UrrRbcSetUePowerClass (UUE_PowerClass pwrClass);

UUE_PowerClass UrrRbcGetUePowerClass (void);

void UrrRbcSetUrnti (UU_RNTI uRnti);

UU_RNTI UrrRbcGetUrnti (void);

void UrrRbcSetCrnti (UC_RNTI cRnti);

UC_RNTI UrrRbcGetCrnti (void);

void UrrRbcSetCpichRscp (SignedInt16 cpichRscp);

SignedInt16 UrrRbcGetCpichRscp (void);

void UrrRbcSetPdcpSnInfo (URB_WithPDCP_InfoList *pdcpInfoList_p);

/* internal use only */
#if defined (DEVELOPMENT_VERSION)
void UrrRbcSetInvalidConfigurationSubstituted (Boolean invalid,
    const char *const file_p,
    const Int32 line);

void UrrRbcSetUnsupportedConfigurationSubstituted (Boolean invalid,
    const char *const file_p,
    const Int32 line);

void UrrRbcSetOrderedReconfigurationSubstituted (Boolean invalid,
    const char *const file_p,
    const Int32 line);
#else
void UrrRbcSetInvalidConfigurationSubstituted (Boolean invalid);
void UrrRbcSetUnsupportedConfigurationSubstituted (Boolean invalid);
void UrrRbcSetOrderedReconfigurationSubstituted (Boolean invalid);
#endif /* DEVELOPMENT_VERSION */

/* **********, added begin */
void UrrRbcMgOutputCphyCctrchCnfAwaitInfoSubstituted(Int8 configId,
                                                           void *trans_p,
                                                           const char *const file_p,
                                                           const Int32 line);
/* **********, added end */

/* Function that should be called externally */
Boolean UrrRbcGetInvalidConfiguration (void);

Boolean UrrRbcGetUnsupportedConfiguration (void);

UmtsMobileEquipmentData* UrrRbcGetUmtsMobileEquipmentData (void);

void UrrRbcGetUeRadioAccessCapability (UUE_RadioAccessCapability  *cap_p);

UUE_RadioAccessCapability_v370ext* UrrRbcGetUeRadioAccessCapabilityV370Ext
                                          (void);

UAccessStratumReleaseIndicator UrrRbcGetAccessStratumReleaseIndicator (void);

Boolean UrrRbcGetOrderedReconfiguration (void);

Int8  UrrRbcCalcMacHeaderSize (UTransportChannelType trChType,
                               BearerIdentity        rbIdentity);

void UrrRbcDiv64By16 (Int16 *dividend_p, Int16 divisor, Int16 *result_p,
                      Int16  *mod_p);

Int8 UrrRbcGetMacHeaderSize (UTransportChannelType trChType,
                             Boolean               logChanCcch,
                             Int8                  logChansOnThisTrCh);

Int8 UrrRbcAllocateConfigIdentifier (void);

void UrrRbcRemoveRbEstRabs (URB_Identity rbId,
  UT_SINGLE_LINK_LIST_NODE_TYPE (UrrRbcEstablishedRabsList) *rabsListElement_p);

void UrrRbcRemoveSignallingRbEstRabs (URB_Identity srbId);

Boolean UrrRbcFindCnEstRabs (UCN_DomainIdentity searchId);

Boolean UrrRbcFindRbEstRabs (URB_Identity rbId,
                             UrrRbcRabRbListElement **rbListEle_p,
      UT_SINGLE_LINK_LIST_NODE_TYPE (UrrRbcEstablishedRabsList) **rabListEle_p);

Boolean UrrRbcFindSignallingRbEstRabs (URB_Identity rbIdentity,
                                       UrrRbcRabRbListElement **srbListEle_p);

Nsapi  UrrRbcGetNsapi (URAB_Identity *asn1Id);

void UrrRbcPdcpSnInfoRestart (void);

void UrrRbcAddSnInfoElement (URB_Identity  rbId, UPDCP_SN_Info pdcpSnInfo);

void UrrRbcProcessPdcpSn (URB_Identity rbId, UPDCP_SN_Info snInfo);

Boolean UrrRbcGetDoff (UDefaultDPCH_OffsetValueFDD *doff_p);

UActivationTime UrrRbcGetOrderedReconfigurationCfn (void);

void UrrRbcSetCurrentActivationTime (Boolean now, UActivationTime cfn);

void UrrRbcGetCurrentActivationTime (ActivationTime *currTime_p);

void UrrRbcSetCurrentActivationTimeValidCfn (Boolean ValidCfn);

void UrrRbcCmacActivationTimeCnf (RadioResourceControlEntity *urr_p);

void UrrRbcUrlcUlConfigChangeInd (RadioResourceControlEntity *urr_p);

void UrrRbcUrlcDlConfigChangeInd (RadioResourceControlEntity *urr_p);

void UrrRbcRabmRrcEstablishRes (RadioResourceControlEntity *urr_p);

void UrrRbcRabmRrcReleaseRes (RadioResourceControlEntity *urr_p);

void UrrRbcRabmRrcEstablishRej (RadioResourceControlEntity *urr_p);

void UrrRbcRlcConfigurationChange (
                    UrrRbcBitMap configBitMap,
                    UT_SINGLE_LINK_LIST_NODE_TYPE (UrrRbcRbList) *rbListEle_p);

UrrRbcRabSRbList *UrrRbcGetSrbListInEstablishedRabs (void);

void UrrRbcCphyDeactivateCnf (RadioResourceControlEntity *urr_p);

Boolean UrrRbcUpdateCurrentDlUarfcn (UUARFCN currentDlUarfcn);

Boolean UrrRbcGetCurrentDlUarfcn (UUARFCN *currentDlUarfcn_p);

void UrrRbcUpdateCurrentUlUarfcn (UUARFCN currentUlUarfcn);

Boolean UrrRbcGetCurrentUlUarfcn (UUARFCN *currentUlUarfcn_p);

Boolean UrrRbcUpdateCurrentServingCellPsc (UPrimaryScramblingCode servingCellPsc);

UPrimaryScramblingCode UrrRbcGetCurrentServingCellPsc (void);

Int16 UrrRbcGetCcchSduSize (void);

#if defined(UPGRADE_3G_HSDPA)
void UrrRbcClearHsdpaData (Boolean clearEcfData);
#endif //UPGRADE_3G_HSDPA 

#if defined(UPGRADE_3G_EDCH)
// ********** begin	
void UrrRbcSetPrimaryErnti (Boolean value, Int8 idx);
// ********** end   
#endif //UPGRADE_3G_EDCH 

#if defined(UPGRADE_3G_HSDPA)
void UrrRbcClearHspdschData (void);
#endif //UPGRADE_3G_HSDPA 

void UrrRbcCbmcRxInd (RadioResourceControlEntity *urr_p);

void UrrRbcCbmcConfigInd (RadioResourceControlEntity *urr_p);

void UrrRbcHandleCbsForbiddenDueToPlms(Boolean cbsForbiddenDueToPlms); /* ********** PTK_CQ00304242 */

Boolean UrrRbcSmGetIncompatibleSecurityReconfig (void);

#if !defined (UPGRADE_EXCLUDE_2G)
void UrrRbcUmphHandoverToUmtsFailCnf (RadioResourceControlEntity *urr_p);
#endif

void UrrRbcSendCmacDlRb0MappingConfigReq (Int8  configId, ActivationTime *actTime_p, Boolean setupEcfCcch);


/* From file urrrbcpg.c (Paging) */
Boolean UrrRbcPgCbPagingAllowed (UrrSmcModeAndState  *modeAndState_p);

void UrrRbcPgCbUpdateCellChangeInfo(void);

void UrrRbcPgCbSetCbSupportedInSCell(Boolean);

void UrrRbcPgCbSelectPagingCbsChannels (UrrRbcCommonPhysChSysInfo *commonPhysChSysInfo_p);

Boolean UrrRbcPgCbRemovePagingCbsChannels (URRC_StateIndicator rrcState);

void UrrRbcPgCbImsi (MobileIdElement *newImsi_p);

void UrrRbcPgCbInit (Boolean firstInitialisation, Boolean firstInitFromRrcTaskStart); /* ********** */

void UrrRbcPgCbInitialisePagingCbsForNewCell (void);

Boolean UrrRbcPgCbGetServingFrequency (UFrequencyInfo *frequencyInfo_p);

Int16 UrrRbcPgCbGetPsDrxCycleLength (void);

Int16 UrrRbcPgCbGetCsDrxCycleLength (void);

Int16 UrrRbcPgCbGetUtranDrxCycleLength (void);

Int16 UrrRbcPgCbGetConfiguredDrxCycleLength (void);

// ********** start
#if defined(ENABLE_END_OF_DRX_MEAS_IND)
Int16 UrrRbcPgCbGetLongestConfiguredDrxCycleLength1Or2 (void);
#endif /* ENABLE_END_OF_DRX_MEAS_IND */
// ********** end

Boolean UrrRbcPgCbPagingConfigured (void);
/* added for CQ00056932 begin */
void UrrRbcSetPgCbPagingFlag (Boolean pagingOn);
/* added for CQ00056932 end */

void UrrRbcChannelsImplictlyRemoved (void);

void UrrRbcPgCbSetMacConfigSent(Boolean macConfigSent);

Boolean UrrRbcPgCbGetMacConfigSent(void);

void UrrRbcPgCbReSendMacConfigForCbs(void);

/* From file urrrbcra.c (Rach) */
void UrrRbcRachSib56Info (Boolean   listPresent);

void UrrRbcRachSib3Info (UMaxAllowedUL_TX_Power   maxAllowedUlTxPwr);

void UrrRbcRachSib7Info (USysInfoType7 *sib7_p, Boolean *sib7ValuesHaveChanged);

void UrrRbcRachInit (Boolean firstInitialisation);

UrrRbcPrachFachSetupResult UrrRbcRachSelectRach (BearerIdentity rbId,
                                  Int32 msgSize,
                                  UTransmissionTimeInterval  ttiRequired,
                                  Int16                      accessClass,
                                  Boolean                    initialRach,
                                  UTransmissionTimeInterval *ttiSelected_p,
                                  Int32                     *tfSize_p,
                                  UrrRbcRadioBearerDatabase *rbDb_p);

void UrrRbcFachRemoveInitialFach (Boolean releaseCctrch);

void UrrRbcFachInit (void);

void UrrRbcCphySwitchRatCnf (RadioResourceControlEntity *urr_p);

void UrrRbcStartResetUeToIdle (void);

/* From file urrrbcie.c (IEs) */
void UrrRbcIeInit (Boolean firstInitialisation);

void UrrRbcIeDeleteSavedCnInfoIeIfPresent (void);
Boolean UrrRbcIeIsCnInfoPresent(void);               /* for ********** PTK_CQ00225315 */

void UrrRbcIeProcessStoreCnInfoIeIfPresent (void);

void UrrRbcHandleTimers (RadioResourceControlEntity *urr_p);

void UrrRbcReleaseT314Rabs (void);

void UrrRbcReleaseT315Rabs (void);

Boolean UrrRbcAnyT314BearersDefined (void);

Boolean UrrRbcAnyT315BearersDefined (void);

UrrRbcTgpsIdentity *UrrRbcGetTgpsIdentity (void);

void UrrRbcActivateRbTestLoopModeReq (RadioResourceControlEntity *urr_p);
void UrrRbcCloseTestLoopModeReq (RadioResourceControlEntity *urr_p);
void UrrRbcOpenTestLoopModeReq (RadioResourceControlEntity *urr_p);
void UrrRbcDlTrchCrcCnf (RadioResourceControlEntity *urr_p);
void UrrRbcDeactivateRbTestLoopModeReq (RadioResourceControlEntity *urr_p);
void UrrRbcCrlcDlAmPduSizeInd (RadioResourceControlEntity *urr_p);

void UrrRbcIeProcessCipheringModeInfo (UCipheringModeInfo *cipheringModeInfo_p);

void UrrRbcCmacHfnConfigCnf (RadioResourceControlEntity *urr_p);

Boolean UrrRbcProcessMeasControlCMInfo (UDPCH_CompressedModeStatusInfo *info_p);

void UrrRbcCrlcPrepareCipherCfgChangeCnf (RadioResourceControlEntity *urr_p);

Boolean UrrRbcIsTgpsTgmpActive (UTGMP_r8 tgmp);

Boolean UrrRbcSrbIsUmInUl (BearerIdentity bearerIdentity);

Boolean UrrRbcSrbIsAmInUl (BearerIdentity bearerIdentity);

Boolean UrrRbcIsAmOrUm (UT_SINGLE_LINK_LIST_NODE_TYPE(UrrRbcRbList) *rb_p);

Boolean UrrRbcIsAm (BearerIdentity rbId);

Int8 UrrRbcGetNumSrbsInEstablishedRabs (void);

void UrrRbcSetCurrentMacQueueType (UrrRbcQueueType queueType);
UrrRbcQueueType UrrRbcGetCurrentMacQueueType (void);

/* SECURITY MODULE */
void UrrRbcSmAddIntegrityProtectionActivationInfo (
                                        BearerIdentity             rbId,
                                        URRC_MessageSequenceNumber sequenceNum);
void UrrRbcSmClearIntegrityProtectionActivationInfo (void);
void UrrRbcSmClearRbUlCipherActivationTime (void);
void UrrRbcSmClearSecurityModification (void);
Boolean UrrRbcSmGetCipherStatusReconfig (UReceivedMessageType   *msgType_p);
RrcKeySequence UrrRbcSmGetCurrentKeySetIdentifier (UCN_DomainIdentity cnDomain);
RrcKeySequence UrrRbcSmGetCurrentPendingKeySetIdentifier (UCN_DomainIdentity cnDomain);/*CQ00098020 add*/
UCN_DomainIdentity UrrRbcSmGetLatestConfiguredCnDomain (void);
Boolean UrrRbcSmGetCurrentIkFromNas (UCN_DomainIdentity cnDomain,
                                    USecurityKey *ik_p,
                                    KeySeqId *ksi_p);
Boolean UrrRbcSmGetPendingIkFromNas (UCN_DomainIdentity cnDomain,
                                     USecurityKey *ik_p,
                                     KeySeqId *ksi_p,
                                     Boolean *isUnusedKey_p);
void UrrRbcSmIntegrityProtectionReconfigurationFailure (void);
Boolean UrrRbcSmSecurityProcedureIsInProgress (UReceivedMessageType *msgType_p);
void UrrRbcSmSetCipherStatusReconfigToFalse (void);
void UrrRbcSmSetIncompatibleSecurityReconfig (Boolean isr);
void UrrRbcSmSetLatestConfiguredCnDomain (
                                UCN_DomainIdentity latestConfigDomain);
void UrrRbcSmSetLatestConfiguredCnDomainToPrevious (void);
void UrrRbcSmSetFirstSecurityProcedureCompleted (Boolean smc,
                                                          UCN_DomainIdentity cn_DomainIdentity);
Boolean UrrRbcSmGetFirstSecurityProcedureCompleted (UCN_DomainIdentity cn_DomainIdentity);
Boolean  UrrRbcSmValidateIkAndCk (UCN_DomainIdentity lccd,
                                  Boolean checkIk,
                                  Boolean checkCk);
Boolean UrrRbcSmHasCipheringStarted (UCN_DomainIdentity cnDomain);
Boolean UrrRbcSmGetCurrentCipheringMode (UCN_DomainIdentity     cnDomain,
                                         UCipheringModeCommand_r7 *modeCommand_p);

/* START CALCULATION MODULE */
void UrrRbcStBuildStartList (USTARTList *startList_p);
USTART_Value UrrRbcStCalculateStart (UCN_DomainIdentity cnDomain);
void UrrRbcStCmacHfnMeasurementInd (RadioResourceControlEntity *urr_p);
void UrrRbcStCrlcCountcCnf (RadioResourceControlEntity *urr_p);
void UrrRbcStCrlcCountcInd (RadioResourceControlEntity *urr_p);
USTART_Value UrrRbcStGetLatestTxedStart (UCN_DomainIdentity cnDomain);
void UrrRbcStHandleCounterCheck (UrrInternalSignalProcessRxAirSignal *sig_p);
void UrrRbcStSendStartValueTxedForStartList (USTARTList *startList_p);
void UrrRbcStSendStartValueTxed (USTART_Value startValue,
                                            UCN_DomainIdentity cnDomain);
void UrrRbcStRrcConnectionSetupStartList (USTARTList *startList_p);
void UrrRbcStSetupStartInfoArray (UCN_DomainIdentity cnDomain,
                                  Int8 *numberOfStartInfo_p,
                                  RrcStartInformation *startInfo_p);
Boolean UrrRbcStGetCountcDataIsValid(void); /* ********** added */

void UrrRbcStCrlcRb2ActTimeInd  (RadioResourceControlEntity *urr_p);

/* MG MODULE */
/* Merge the fix from TD ********** */
//void UrrRbcMgAbortSecurityModeCommand (void);
Boolean UrrRbcMgAbortSecurityModeCommand (void);
/* Merge the fix from TD ********** */
void UrrRbcMgCompleteSecurityModeCommandAbort (void);

void UrrRbcMgIratHandoverRequest (RadioResourceControlEntity *urr_p);
void UrrRbcMgIratHandoverAck (RadioResourceControlEntity *urr_p);
void UrrRbcCphyHandoverToLteCnf (RadioResourceControlEntity *urr_p);
void UrrRbcSendCmacHandoverReqToLte (ActivationTime  *activationTime_p,
                                     CmacHandoverType handoverType,
                                     URAB_Identity    *rab_Identity_p);
void UrrRbcmgCphyHandoverToUmtsFailCnf (RadioResourceControlEntity *urr_p);
#if defined (UPGRADE_SRVCC)
Boolean UrrRbcCheckSameDomainForAllRab (Boolean *outputCsDomainConfigured_p, Boolean *outputPsDomainConfigured_p);/*CQ00054806 add*/ //CQ00078980 modify
#endif/*UPGRADE_SRVCC*/
void UrrRbcMgCphyCompressedModeErrorInd (RadioResourceControlEntity *urr_p);
#if !defined (UPGRADE_EXCLUDE_2G)
void UrrRbcMgGrrRcHandoverToGsmCnf (RadioResourceControlEntity *urr_p);
void UrrRbcMgGrrRcHandoverToUmtsReq (RadioResourceControlEntity *urr_p);
void UrrRbcMgCopyNasMsgToRrcNasTxQueueInfoInd (Int8 *rawNasMsg_p,
                                               Int32 *output_p);

GrrRcUmtsUeCapInfoInd UrrRbcSendGrrRcUmtsUeCapInfoInd (UPredefinedConfigStatusList *predefinedList_p); /* ********** modified *//*********** */
#endif
void UrrRbcMgHandleRrcDeactReq (UDeactivateReason deactivateReason);

void UrrRbcDeactivateCompressedModePatterns (void);
void UrrRbcDeleteAllInterFreqCompressedModePatterns (void);

void UrrRbcSmSetConnectedAndSecModeComplete (Boolean complete, UCN_DomainIdentity cnDomain);
Boolean UrrRbcSmGetConnectedAndSecModeComplete (UCN_DomainIdentity cnDomain);

void UrrRbcNoCell (void);

void UrrRbcSmClearAllInProgressCipherCommands (void);

void UrrRbcMgHandleSecuritySettingsInCellUpdateConfirmFailure (securityConfigType configToCheck);

Boolean UrrRbcRadioLinkFailureOcurred (void);

void UrrRbcRemoveFromConfigList(SignalGroupId groupId, Boolean oldConfigOnly);
Boolean UrrRbcFindPendingPchConfigFromConfigList (void); /* ********** added */

Boolean UrrRbcSendNextConfigurationIfReady (void);

void UrrRbcHandleDataStatusInd(UrrSmcModeAndState *modeAndState_p);

void UrrRbcHandleDataStatusReq(RadioResourceControlEntity *urr_p);

Boolean UrrRbcIsRlInRlList (UPrimaryScramblingCode sc);

Boolean UrrRbcIsRlInRlAdditionList (UPrimaryScramblingCode sc);

#if defined (UPGRADE_3G_HSDPA)
Boolean UrrRbcHsdpaIsSupported (void);
Boolean UrrRbcGetHsdpaServingCell (UPrimaryScramblingCode *servingCell);
void UrrRbcClearHrntiFromHsdpaDataBase (void);
void UrrRbcClearHarqFromHsdpaDataBase (void);
#endif /* UPGRADE_3G_HSDPA */
/*********** add begin*/
#if defined (RRC_DC_HSDPA)
//**********: Removed UrrRbcDualCellHsIsSupported()
void UrrRbcClearSecondaryCellInfoFdd(void);
void UrrRbcSetSecondayCellHsDspchValue (Boolean value, Int8 index);
Boolean UrrRbcIsUrrRbcSecondaryCellInfoFddPresent(void);
#endif //RRC_DC_HSDPA
/*********** add end*/
#if defined (UPGRADE_3G_EDCH)
Boolean UrrRbcHsupaIsSupported (void);
Int8 UrrRbcGetEdchActiveSet (UPrimaryScramblingCode *edchActiveSet);
Boolean UrrRbcIsCellInEdchActiveSet (UPrimaryScramblingCode sc);
#endif /* UPGRADE_3G_EDCH */

void UrrRbcActivateRlc(Boolean send);

#if defined(UPGRADE_3G_HSDPA)
void UrrRbcSetEngModeHsInfo (UEngHsdpaInfo *hsdpa_p, UEngHsupaInfo *hsupa_p);
#endif //UPGRADE_3G_HSDPA 

Int8 UrrRbcCountEstablishedRabsOfDomain (UCN_DomainIdentity cnDomain);

UrrRbcRadioBearerDatabase *UrrRbcGetRbcDatabase (void);

Boolean UrrRbcGetInitialFachRachConfigured (void);

Boolean UrrRbcGetFachRachSRBsConfigured(void);

Boolean UrrRbcGetFachRachOrEcfSRBsMapped (void);

#if defined(UPGRADE_3G_HSDPA)
UrrRbcHsdpaInfo* UrrRbcGetHsdpaInfo(void);
#endif //UPGRADE_3G_HSDPA 
UrrRbcRadioBearerDatabase* UrrRbcDataBase(void);/* ********** added */

// ********** begin	
#if defined(UPGRADE_UL_ECF) 
UrrRbcHsupaInfo* UrrRbcGetHupaInfo(void);

Boolean UrrRbcComEdchVerifyRbUlMapping(void);

Boolean UrrRbcGetCommonEdchSupported(void);
#endif /* UPGRADE_UL_ECF  */ // ********** end  

Boolean UrrRbcGetEhsSupported(EhsSupportedStates supportedState);
#if defined(UPGRADE_UL_ECF) // ********** begin	
void UrrRbcCheckAndPrepareEcfConfig(Boolean *ecfDl, Boolean *edfUl);
#else /* UPGRADE_UL_ECF */
Boolean UrrRbcCheckAndPrepareEcfConfig(void);
#endif /* UPGRADE_UL_ECF  */ 

Boolean VerifyEdchServingCellConfiguration (UPrimaryScramblingCode sc);
// ********** end  

#if defined(UPGRADE_3G_HSDPA)
void UrrRbcSetTrBkSizeTable (Boolean octetAligned);

void UrrRbcSetHsdpaServingCell (UPrimaryScramblingCode PSC);
#endif //UPGRADE_3G_HSDPA 

UReceivingWindowSize UrrRbcConvertMaxWindowSize (void);

void UrrRbcGetStates (      Boolean *rbcInvalidConfiguration_p,
                            Boolean *rbcOrderedReconfiguration_p,
                            Boolean *rbcHsDschReception_p,
                            Boolean *rbcEDchTransmission_p);

void UrrRbcMgGetState (UrrRbcCellUpdateState *rbcMgState_p);

Boolean UrrRbcMgGetDisregardFastDormancyAwaitHoToUtranComplete(void); /* **********, added */ 

void UrrRbcMgClearConnectedModeParams(void);                                   //-+ ********** 19-Sep-2012 +-
Boolean UrrRbcMgGetResettingToIdle(void);
Int8 UrrRbcGetNumConfiguredUlTrch (void);

Boolean UrrRbcDchUlTrchExists (void);

Boolean UrrRbcUlTrchExists (UTransportChannelType trChType,UTransportChannelIdentity trChId);

#if defined (UPGRADE_3G_FDPCH)
Boolean UrrRbcFdpchIsSupported(void);
#endif /* UPGRADE_3G_FDPCH */

void UrrRbcSetNumberOfTPC_BitsAndPresent(
                            T_UUL_DPCH_Info_r7_dpdchPresence dpdchPresenceTag,
                            Boolean numberOfTPC_BitsPresent,
                            UNumberOfTPC_Bits numberOfTPC_Bits);

Boolean UrrRbcPgCbCalculateDrxCycleLength(void);

Int16 UrrRbcPgCbGetDrxCycleLength (void) ;

void UrrRbcSetNeedToToDiscardRlcMessages(Boolean newVal);                      //-+ PTK_CQ00238451 11-Oct-2012 +-

void UrrRbcPlwCphyT319ExpiryInd (RadioResourceControlEntity *urr_p);

#if defined(UPGRADE_3G_HSDPA)
Boolean RbcIsCpcConfigured(void);
#endif //UPGRADE_3G_HSDPA 

/* **********, begin */
typedef struct UrrRbcCountcDomainInfoTag
{
    KeySeqId                    currentKsi;
    USTART_Value                currentStart;
    USTART_Value                runningStart;
        /* "latest transmitted START value" */
    USTART_Value                latestTxedStart;
        /* only used while hfnInitIsInProgress */
    Boolean                     hfnInitIsInProgress;
    Int8                        rxCount;
    UMacHyperFrameNumber        macHfn;
    KeySeqId                    macKsi;
}
UrrRbcCountcDomainInfo;
UrrRbcCountcDomainInfo *GetCnDomainInfo (UCN_DomainIdentity cnDomain);
/* **********, end */

/* CQ00003290, added begin */
void UrrRbcDeletePdcp (void);
/* CQ00003290, added end */


#if defined (UPGRADE_DSDS)
void UrrRbcWaitResumeFinish(void);/* CQ00013886,CQ00015480 modify */
void UrrRbcSetPchConfigFailFlag (Boolean phyConfigFailInd);  /*CQ00020407 add*/
Boolean UrrRbcGetPchConfigFailFlag (void);                   /*CQ00020407 add*/
void UrrRbcClearCphyConfigCctrchCnfForPCH(void);/*CQ00017708 add*/
void UrrRbcMmrPsPagingChangeInd (RadioResourceControlEntity *urr_p);/*CQ00036511*/
#if defined(UPGRADE_COMMON_STORE)
void UrrRbcIratDsPowerOffCompleteInd (RadioResourceControlEntity *urr_p); /* ***********/
#else
void UrrRbcGrrRcPowerOffCompleteInd (RadioResourceControlEntity *urr_p); /* ***********/
#endif
/* **********, added begin */
void ConfigurePhyForDsControlPch (void);
/* **********, added end */
/* ********** added begin */	
#if defined (ENABLE_WAIT_ABORT_SEARCH_CNF)
void UrrRbcIratDsPhyConfigFinishInd(RadioResourceControlEntity *urr_p);
void UrrRbcSetPhyConfigInProgressOfOtherCard (Boolean setValue);
#endif//ENABLE_WAIT_ABORT_SEARCH_CNF
/* ********** added end */	
//********** added begin     
#if defined (UPGRADE_DSDSWB)
void UrrRbcHandleResumePchFinishInd(void);
#endif //end of UPGRADE_DSDSWB
//********** added end     
#endif

void UrrRbcChkRadioLinkForDch(URRC_StateIndicator    rrcState);/*********** add*/

/*********** add begin*/
Boolean CheckIfThisRBMappedOnUlDch (UT_SINGLE_LINK_LIST_NODE_TYPE (UrrRbcRbList) *info_p);
Boolean UrrRbcCheckAtLeastOneUlDchMappingByRB (void);
void UrrRbcCheckUlDpchInfoValid(void);
/*********** add end*/

void UrrRbsStClearStartForCnDomain (UCN_DomainIdentity cnDomain, RrcStartInformation *startInfo_p);/***********, add*/

Boolean UrrRbcGetEDchTransmission(void);    /* **********, added */
// ********** begin
#if defined(UPGRADE_UL_ECF) 	
UrrSmcModeAndState UrrRbcConvertStateIndToSmcModeAndState(URRC_StateIndicator rrcStateind);

UrrRbcCommonPhysChSysInfo *UrrRbcGetRbcSib5Info (void);
Boolean urrRbcCheckNewPrimaryErntiIsReceived(void);// ********** added

#endif /* UPGRADE_UL_ECF  */ 
UAC_To_ASC_Mapping UrrRbcMapAcToAsc (Int16 accessClass, UAC_To_ASC_MappingTable *table_p);
// ********** end 

#if defined (RRC_UPGRADE_ENG_MODE_REDESIGN) //********** begin
Boolean UrrRbcEngModeFindBestRbDlMappingDch (BearerIdentity bearerIdentity, T_UDL_TransportChannelType_r7 *tag_p);
Boolean UrrRbcEngModeFindBestRbUlMappingDch (BearerIdentity bearerIdentity, T_UUL_LogicalChannelMapping_r8_ul_TrCH_Type *tag_p, Boolean *dchNotRach);

URB_Identity UrrRbcGetConvertBearerIdentityToRadIdentity (BearerIdentity    bearerIdentity);

#endif //RRC_UPGRADE_ENG_MODE_REDESIGN :********** end
/********** - start*/
Boolean UrrRbcTimerIsRunning (const UrrRbcTimerIdentity timerId);
void UrrRbcStartTimer (const UrrRbcTimerIdentity timerId, const FrameTicks duration);
void UrrRbcStopTimer (const UrrRbcTimerIdentity timerId);
void UrrRbcInitTimers(void);
void RbcCountCActTimeTxWaitRlcAckTimerExpiryEvent (UrrRbcTimerIdentity timerId);
/********** - end*/

//********** start
#if defined (UPGRADE_ESCC)
void UrrRbcEsccSaveData(UTargetCellPreconfigInfo_r9	*targetCellPreconfigInfo_p /* ********** modify */
                                ,UServingCellChangeParameters *servingCellChangeParameters_p);

Boolean UrrRbcEsccIsEvent1DOccur(void);
Boolean UrrRbcEsccHandleEvent1D(UPrimaryScramblingCode	e1DPSC);
void UrrRbcEsccSendTcHsccReleaseReq(void);
//********* modify begin
void UrrRbcEsccHandleTcHsccReleaseCnf(RadioResourceControlEntity *urr_p);
void UrrRbcEsccHandleTcHsccSetupCnf(RadioResourceControlEntity *urr_p);
void UrrRbcEsccHandleServingCellChangeInd(RadioResourceControlEntity *urr_p);
//********* modify end
void UrrRbcEsccSendTcHsccSetupReq(UPrimaryScramblingCode	TcPSC,
												 UTargetCellPreconfigInfo_r9 *targetCellPreconfigInfo_p);
void UrrRbcEsccSendCmacActivationTimeOffsetReq(UActivationTimeOffset	activationTimeOffset,
    															  UTransmissionTimeInterval	maxTransmissionTimeInterval);
void UrrRbcEsccHandleCmacActivationTimeOffsetCnf(RadioResourceControlEntity *urr_p);//********** modify
void UrrRbcEsccSaveTargetCellData(Int8 index, UTargetCellPreconfigInfo_r9	*targetCellPreconfigInfo_p);
void UrrRbcEsccDeleteTargetCellData(Int8 index);
void UrrRbcEsccBuildDummyRBSetup(void);
void UrrRbcEsccBuildDummyRBReconfiguration(void);
void UrrRbcEsccBuildDummyTCReconfiguration(void);
void UrrRbcEsccBuildDummyPCReconfiguration(void);
void UrrRbcEsccReleaseSignal(UrrInternalSignalProcessRxAirSignal *sig_p);
//********** start
void UrrRbcEsccReleaseDummyRBSetup(UrrInternalSignalProcessRxAirSignal     *sig_p);
void UrrRbcEsccReleaseDummyRBReconfiguration(UrrInternalSignalProcessRxAirSignal     *sig_p);
void UrrRbcEsccReleaseDummyTCReconfiguration(UrrInternalSignalProcessRxAirSignal     *sig_p);
void UrrRbcEsccReleaseDummyPCReconfiguration(UrrInternalSignalProcessRxAirSignal     *sig_p);
//********** end
UrrRbcEsccProcedureState UrrRbcEsccGetState(void);
//********** start
void UrrRbcEsccReleaseTcHscchIfNeed(UReceivedMessageType        msgType,
                                                  UrrAisSendStatus            sendStatus);
//********** end
Boolean UrrRbcEsccIsActivationTimeOffsetPresent(void);
void UrrRbcT324Expiry(UrrRbcTimerIdentity timerId);

/********** - deleted*/
/* ********** remove */
void UrrRbcEsccHandleCmacActivationTimeExpiry(RadioResourceControlEntity *urr_p);//********* modify
void UrrRbcEsccStopAndInitData(Boolean leavingCellDch); /* ********** modify */
//********** end

void UrrRbcEsccRemovePreconfigInfoIfExist (UPrimaryScramblingCode sc); /* ********** modify */
#endif//UPGRADE_ESCC
//********** end

/* ********** added begin */
void UrrRbcGuardTimerExpiry(UrrRbcTimerIdentity timerId);
void UrrRbcStartGuardTimer (void);
/* ********** added end */
void RbcHandleIntSigAbortPlmnSearchCnf (void);    /* **********, added */

/*********** add begin*/
Boolean UrrRbcSmGetWaitingRlcAckOfSmc (void);
void UrrRbcSmSetWaitingRlcAckOfSmc (Boolean flag);
/*********** add end*/

Boolean UrrRbcMgCheckIfWaitingforAckForCompletemessage (void);/*********** add */

UUARFCN UrrRbcGetActiveSetUlUarfcn (void);    /* ********** added */

Boolean UrrRbcEnterDchIsOngoing (void);      /*********** add */

void UrrRbcMgRemoveInputForPreviousCellUraUpdate (void);/*********** add*/

void UrrRbcMgPrintAwaitedInputsList(void);/*********** add*/

void UrrRbcMgSetP2DReconfigFlag(Boolean value);//********** added
Boolean UrrRbcMgGetP2DReconfigFlag(void);//********** added
void UrrRbcPgCbPagingDrxLenChangeInd (RadioResourceControlEntity *urr_p);//********** added

/* ********** add begin */
#if defined (UPGRADE_ESCC)
Boolean UrrRbcEsccIsStartT324Needed(void);
Int8 UrrRbcEsccGetAvailablePreconfigIndex(void);
void UrrRbcEsccDeleteServingCellChangeParams(Int8);


/******************************************************************************
 * Function     : SET_ESCC_CURRENT_STATE
 * Scope        : Global
 * Parameters   : sTATE - New ESCC procdure sTATE
 * Returns      : None
 * Description  : Sets the ESCC state in RBC database
 *****************************************************************************/
#if !defined(ON_PC)
#define SET_ESCC_CURRENT_STATE(sTATE) \
    RRC_TRACE_2(URRC_RBC, SET_ESCC_CURRENT_STATE, URRC_RBC_DEFAULT, \
        "Current ESCC state %e -> %e",\
        urrRbc.rbDataBase.esccParm.esccState,(UrrRbcEsccProcedureState)sTATE);\
        urrRbc.rbDataBase.esccParm.esccState = sTATE;
#else
#define SET_ESCC_CURRENT_STATE(sTATE) \
    RRC_TRACE_2(URRC_RBC, SET_ESCC_CURRENT_STATE, URRC_RBC_DEFAULT, \
        "Current ESCC state %d -> %d",\
        urrRbc.rbDataBase.esccParm.esccState,(UrrRbcEsccProcedureState)sTATE);\
        urrRbc.rbDataBase.esccParm.esccState = sTATE;
#endif //ON_PC

#endif //UPGRADE_ESCC
/* ********** add end */
#endif
/* END OF FILE */

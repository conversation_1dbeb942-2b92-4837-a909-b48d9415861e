/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Intel Corporation
 **************************************************************************
 *   $Id: //central/releases/delivery/Branch_release_14/tplgsm/l1inc/umph_typ.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2005/06/08 12:53:33 $
 **************************************************************************
 * Group            : Dual Mode, Signalling Interface
 * File Description :
 **************************************************************************/
#if !defined (UMPH_TYP_H)
#define       UMPH_TYP_H

#if defined (INTEL_UPGRADE_DUAL_RAT)
/***************************************************************************
 * Nested Include Files
 **************************************************************************/
#include "system.h"
#ifdef LTE_W_PS
#if defined (PHS_L1_SW_UPDATE_R7) //add this include since this file will be copied to 3g_ps\rls\tplgsm\bldstore\hsiupdlibdev\build\lib\hsiupdlibdev_BldIncCache as include files in FS30's hsiupdlibdev.bld rule
#include <mph_typ.h> /* Types needed for handover command */
#endif
#endif

/***************************************************************************
 * Manifest Constants
 **************************************************************************/
#if defined(CPHY_MAX_FDD_FREQUENCIES_PER_RSSI_SCAN)
#define UMPH_MAX_FDD_FREQUENCIES_PER_RSSI_SCAN CPHY_MAX_FDD_FREQUENCIES_PER_RSSI_SCAN
#else
#define UMPH_MAX_FDD_FREQUENCIES_PER_RSSI_SCAN 32
#endif

#define MAX_STORED_FDD_FREQUENCIES  8
#define MAX_FREQ_BANDS              2
#define UMAX_NUM_FDD_NCELLS         32
#define UMAX_NUM_MEAS_FDD_NCELLS    36
#define UMAX_NUM_GSM_CELLS          6
#define UMAX_NUM_MEAS_FDD_UTRANS    3
#define INVALID_FDD_SCRAMBLING_CODE 0xffff

/***************************************************************************
 * Type Definitions
 **************************************************************************/
typedef Int16 UArfcn;
#ifdef LTE_W_PS
typedef Int16 UUARFCN;
#endif
typedef Int16 UPrimaryScramblingCode;
/* Define the BCH transport block size */
#define UPS_BCH_TB_SIZE_BITS      246
#define UPS_BCH_TB_SIZE_OCTETS    BITS_TO_INT8S(UPS_BCH_TB_SIZE_BITS)
#define INVALID_UUARFCN 0xFFFF
/** Defines the minimum value of UARFCN for the 2.1 GHz band as defined in
 * <tt> [3G TS 25.101 section 5.4.4] </tt>.
 */
# define URRC_CSRR_MIN_UARFCN_IN_2_1_BAND                         10562

/** Defines the maximum value of UARFCN for the 2.1 GHz band as defined in
 * <tt> [3G TS 25.101 section 5.4.4] </tt>.
 */
# define URRC_CSRR_MAX_UARFCN_IN_2_1_BAND  10838

#if defined(UPGRADE_FDD_MULTIBAND)
  #define URRC_CSRR_MIN_UARFCN_MULTIBAND  1162
  #define URRC_CSRR_MAX_UARFCN_MULTIBAND  1513
  /** Defines the maximum number of frequencies supported in the 2.1 GHz band.
   */
  #define URRC_CSRR_MAX_FREQS ((URRC_CSRR_MAX_UARFCN_MULTIBAND + 1) - \
                              URRC_CSRR_MIN_UARFCN_MULTIBAND)
  /* Max number of ARFCN is 352. */
#else
  /** Defines the maximum number of frequencies supported in the 2.1 GHz band.
   */
  #define URRC_CSRR_MAX_FREQS ((URRC_CSRR_MAX_UARFCN_IN_2_1_BAND + 1) - \
                              URRC_CSRR_MIN_UARFCN_IN_2_1_BAND)
#endif

/** SIB Decoding control
 *  Turn on/off or skip SIB decode
 */
typedef enum USysInfoBlockControlTag
{
    USIB_OFF  = 0,
    USIB_ALL  = 1,
    USIB_SKIP = 2
}
USysInfoBlockControl;

/** 3G Neighbour cell measurements
 *  Turn on/off 3G monitoring of nieghnour cells
 */
typedef enum FddNcellMeasCtrlTag
{
    FDD_NCELL_MEAS_OFF = 0,
    FDD_NCELL_MEAS_ON  = 1
}
FddNcellMeasCtrl;

/** Abort 3G Neighbour cell measurements
 *  ALL, RSSI Scan or Find Cell.
 */
typedef enum FddAbortCtrlTag
{
    FDD_ABORT_ALL           = 0,
    FDD_ABORT_RSSI_SCAN     = 1,
    FDD_ABORT_FIND_CELL     = 2
}
FddAbortCtrl;

typedef struct UtraRssiReportTag
{
    UArfcn                             	frequency;
    SignedInt16                         utra_CarrierRSSI;
}
UtraRssiReport;

/** Handover from Utran Command
 *  Used the main /a L1GsmHandoverReq handover data type
 */
typedef L1GsmHandoverReq UHandoverFromUtranCommand;

/** Utran FDD Cells on given UARFCN
 *  Neighbour cell data for 3G cells per Uarfcn
 */
typedef struct UFddUtranElementTag
{
     UArfcn                  uArfcn;
     Int16                   numPrimaryScramblingCodes;
     /* txDiversityIndicMapBit - Bit 0 (set to '1') indicates if Tx-diversity is on for the cell
     ** defined in primaryScramblingCode[0]. Bit 1 indicates psc[1] .. etc */
     Int32                   txDiversityIndicMap;
     UPrimaryScramblingCode  primaryScramblingCode [UMAX_NUM_FDD_NCELLS];
}
UFddUtranElement;

/** PDU Decode Status based on CRC check
 */
typedef enum FddDlPduStatusTag
{
    /* Member: UDL_PDU_STATUS_CRC_OK = CRC for this PDU okay. */
    FDD_DL_PDU_STATUS_CRC_OK       = 0,
    /* Member: UDL_PDU_STATUS_CRC_ERROR = CRC for this PDU failed. */
    FDD_DL_PDU_STATUS_CRC_ERROR    = 1,
    /* Member: UDL_PDU_STATUS_UNUSED = The associated structure is not being used. */
    FD_DL_PDU_STATUS_UNUSED
}
FddDlPduStatus;

/** RSSI scan for cell selection.
 * GRR sends this signal to trigger a full scan of the UTRAN RSSI.
 * This signal is sent when GRR initiates Inter-RAT cell selection
 * or a PLMN search procedure.
 *
 * If numFreq greater than 0, layer 1 ust scan the frequencies defined
 * in teh freqList. If numFreq equal to 0 than layer 1 must scan all
 * frequencies in between minFreqInBand and MaxFreqInBand.
 *
 * When performing measurements on neighbouring cells,
 * if L1 is in Idle or Packet Idle mode, L1 will continue to pick up
 * paging on the current serving cell.
 */

typedef struct UmphFddRssiScanReqTag
{
    /**  Number of Frequencies
     * The number of frequencies to search in the freqList array
     */
    Int16    numFreq;

    /**  Frequencies List
     * frequencies to search as defined by numFreq
     */

    UArfcn   freqList      [UMPH_MAX_FDD_FREQUENCIES_PER_RSSI_SCAN];

    /**
     * If numFreq equal to zero the following fields are valid. */

    /**  Minimum Frequency In Band
     * First Frequency to search in band
     */
    UArfcn   minFreqInBand;
    /**  Maximum Frequency In Band
     * Last Frequency to search in band
     */
    UArfcn   maxFreqInBand;
}
UmphFddRssiScanReq;

/** Measurement indication.
 * This is sent by layer 1 in reponse to a #UmphFddRssiScanReq.
 * This signal contains the RSSI measurements and the corresponding frequencies.
 * The utraRssiReport array is sorted in descending RSSI strength order.
 *
 */

typedef struct UmphFddRssiScanCnfTag
{
    /**  Number of Measurements returned
     * This is the number of measurements returned in the utraRssiReport array
     */
    Int16           numMeas;

    /**  Inter-RAT RSSI Measurement Report
     * reported
     */

    UtraRssiReport  utraRssiReport [URRC_CSRR_MAX_FREQS];
}
UmphFddRssiScanCnf;

/** Find Cell Request
 * This signal is sent by GRR to GSM L1 to find the strongest cell in the
 * frequency specified by uArfcn. This signal requests L1 to synchronise
 * to and camp onto the strongest UTRAN cell for the requested uArfcn.
 * If a cell has been found, L1 will return a #UmphFddFindCellCnf
 * indicating the primary scrambling code of the cell
 *
 * If L1 is unable to find a cell for the requested uArfcn,
 * then L1 will return a UmphFddFindCellCnf with an indication
 * that the cell was not found.
 */

typedef struct UmphFddFindCellReqTag
{
    /** UArfcn to scan for cells
     * */
    UArfcn  uArfcn;
}
UmphFddFindCellReq;

/** Find Cell Confimation
 * L1 sends GRR this signal in response to a #UmphFddFindCellReq
 *
 * L1 indicates whether it successfully camped onto this cell and
 * if it was successful, reports the primary scrambling code
 */

typedef struct UmphFddFindCellCnfTag
{
    /** Cell Detected
     * Indicates if cell detected or not,
     * */
    Boolean                 cellDetected;

    /** Primary scrambling code
     * Primary scrambling code of cell detected
     * */

    UPrimaryScramblingCode  primaryScramblingCode;

    /** Ec/No measurement
     * This is the Ec/No measurement for the CPICH
     * in 1/8dB steps (e.g. -80 = -10dB).
     * */

    SignedInt16             cpichEcN0;

    /** RSCP measurement
     * This is the RSCP measurement for the CPICH
     * in 1/8dBm steps (e.g. -80 = -10dBm).
     * */

    SignedInt16             cpichRscp;

#ifdef UPGRADE_PLMS	
    /** RSSI measurement
     * This is the RSSI measurement
     * in 1/8dBm steps (e.g. -80 = -10dBm).
     * */
    SignedInt16            rssi;
#endif //UPGRADE_PLMS	
}
UmphFddFindCellCnf;

/** SIB Decode Indication
 * This signal is sent from L1 to GRR to return MIB's and SIB'd
 * received on the current UMTS cell as requested in
 * #UmphFddSibDecodeReq
 */

typedef struct UmphFddSibUnitDataIndTag
{
    Int16                   sfn;
    UArfcn                  uArfcn;
    UPrimaryScramblingCode  primaryScramblingCode;
	#if defined (ENABLE_NETWORK_SCAN_REPORT)
	// [New_Feature]add by wpan for add EcNo and Rscp in bchRpt
	INT16  cpichEcNo;						/* add by wpan to rpt ecno of detect wb cell */
	INT16  cpichRscp;						/* add by wpan to rpt rscp of detect wb cell */
	#endif
    FddDlPduStatus          status;
    Int8                    data [UPS_BCH_TB_SIZE_OCTETS];
}
UmphFddSibUnitDataInd;


/** SIB Decode Error Indication
 * This signal is sent from L1 to GRR to indicate the MIB or SIB
 * decode failed on the cell defined.
 */

typedef struct UmphFddSibErrorIndTag
{
    UArfcn                  uArfcn;
    UPrimaryScramblingCode  primaryScramblingCode;
    L1Error                 errorCode;
}
UmphFddSibErrorInd;


/** Find Next Cell Confimation
 * L1 sends GRR this signal in response to a #UmphFddFindNextCellReq
 *
 * L1 indicates whether it successfully camped onto this cell and
 * if it was successful, reports the primary scrambling code as
 * with #UmphFddFindCellCnf
 */

typedef UmphFddFindCellCnf UmphFddFindNextCellCnf;

/** Neighbour Cell Measurement Report
 * This signal is used to report UMTS neighbour cell measurements to GRR.
 *
 * UmphFddNcellMeasInd is used to report 3G ncell info to L3. The index is used as follows:-
 * for fddUtranElement[0] the index runs from 0 .. numPrimaryScramblingCodes where 32 cells are possible.
 * for fddUtranElement[1] the index runs from 32 to (32 + numPrimaryScramblingCodes) where 32 cells are possible.
 * for fddUtranElement[2] the index runs from 64 to (64 + numPrimaryScramblingCodes) where 32 cells are possible.
 *
 * For example, the 3G cell defined by fddUtranElement[1], primaryScramblingCode[2], the index will be set to 34.
 *
 * If the 'numPrimaryScramblingCodes' is set to zero, it indicates a wideband RSSI measurememt reported in the RSSI field.
 *
 * For example, RSSI only for fddUtranElement[1] is reported in rssi[1]. RSSI is reported for all UARFCN's requested.
 * stack will determine if value should be used based on 'numPrimaryScramblingCodes' requested for that UARFCN
 *
 */
typedef struct UmphFddNcellMeasIndTag
{
    Int8        seqNum;
    Int8        numMeas;

    Int8        index[UMAX_NUM_MEAS_FDD_NCELLS];
    SignedInt16 cpichEcN0 [UMAX_NUM_MEAS_FDD_NCELLS];
    SignedInt16 cpichRscp [UMAX_NUM_MEAS_FDD_NCELLS];

    SignedInt16 rssi[UMAX_NUM_MEAS_FDD_UTRANS];
}
UmphFddNcellMeasInd;

#endif /* (INTEL_UPGRADE_DUAL_RAT) */
#endif



/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/tplgsm/utinc/utte_cfg.h#9 $
 *   $Revision: #9 $
 *   $DateTime: 2006/09/14 16:55:52 $
 **************************************************************************
 * File Description:
 *
 * Task Execution environment support function configuration
 * This file configures data id and flash memory usage
 **************************************************************************/

/* This file will be included many times */

/**** #defines  ************************************************************/
#if defined (TE_DATA_ITEM)
#  undef TE_DATA_ITEM
#endif

#if UTTE_DATA_ITEM_TYPE == UTTE_DATA_ID_ENUM
#   define TE_DATA_ITEM(iDeNUM, fUNC, hND) \
               iDeNUM,
#endif

#if UTTE_DATA_ITEM_TYPE == UTTE_DATA_SIZE_FUNC
#   define TE_DATA_ITEM(iDeNUM, fUNC, hND) \
               fUNC,
#endif

#if UTTE_DATA_ITEM_TYPE == UTTE_DATA_SIZE_FUNC_PROTO
#   define TE_DATA_ITEM(iDeNUM, fUNC, hND) \
               Int32 fUNC(TeHnd param);
#endif

#if UTTE_DATA_ITEM_TYPE == UTTE_DATA_SIZE_FUNC_HND
#   define TE_DATA_ITEM(iDeNUM, fUNC, hND) \
               (TeHnd) hND,
#endif

#if !defined(TE_DATA_ITEM)
#   error "Invalid data item type"
#endif

/* Data item configuration */
/* TE DATA_ITEM (Id, SizeFunction, Hnd)
 *            Id - enum used as data id
 *            SizeFunction - Int32 SizeFunction(Int32 Hnd)
 *            Hnd - Handle used as param in SizeFunction
 */

TE_DATA_ITEM(TE_DATA_ID_DUMMY,                    teGetSize,                        0)    /* dummy */

/* Items configured in library (configured by full source build only) *******************/










      

/* Fully configurable items *************************************************************/
#if defined(ABLM_USE_TE_ALPHA_STORE)
TE_DATA_ITEM(TE_DATA_ID_ABLM_ALPHA_STORE,         abcfGetTeAlphaStoreSize,          PNULL)
#endif

#if defined(ENABLE_ENHANCED_AL)
#if defined(ABLM_USE_TE_ASSOC_TABLE)
TE_DATA_ITEM(TE_DATA_ID_ABLM_ASSOC_TABLE,         abcfGetTeAssocTableSize,          PNULL)
#endif
#endif

#if defined(UPGRADE_SIM_APP_TOOLKIT)
#if defined(ABST_USE_TE_MNCB_MACRO_MESSAGE)
TE_DATA_ITEM(TE_DATA_ID_ABST_MNCB_MACRO_MESSAGE,  abstGetTeMncbMacroMessageSize,    PNULL)
#endif


#if defined(AFST_USE_TE_SET_UP_MENU)
TE_DATA_ITEM(TE_DATA_ID_AFST_SET_UP_MENU,  afstGetTeSetUpMenuSize,    PNULL)
#endif

#if defined(ASST_USE_TE_SET_UP_MENU)
TE_DATA_ITEM(TE_DATA_ID_ASST_SET_UP_MENU,  asstGetTeSetUpMenuSize,    PNULL)
#endif

#if defined(ASST_USE_TE_SET_UP_CALL)
TE_DATA_ITEM(TE_DATA_ID_ASST_SET_UP_CALL,  asstGetTeSetUpCallSize,    PNULL)
#endif

#if defined(ASST_USE_TE_SEND_SM)
TE_DATA_ITEM(TE_DATA_ID_ASST_SEND_SM,  asstGetTeSendSmSize,    PNULL)
#endif

#if defined(ASST_USE_TE_SEND_SS)
TE_DATA_ITEM(TE_DATA_ID_ASST_SEND_SS,  asstGetTeSendSsSize,    PNULL)
#endif
#endif /* #if defined(UPGRADE_SIM_APP_TOOLKIT) */

/* Add TE data items above this line ****************************************************/
/* Ready for next including of this file */
#undef  UTTE_DATA_ITEM_TYPE

/* END OF FILE */



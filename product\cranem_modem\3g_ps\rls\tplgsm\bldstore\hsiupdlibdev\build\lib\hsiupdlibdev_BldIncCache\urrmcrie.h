/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urrmcrie.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2006/11/08 16:38:01 $
 **************************************************************************
 * File Description:
 *
 ***************************************************************************
 *
 * Revision Details
 **************************************************************************/

#if !defined (URRMCRIE_H)
#define       URRMCRIE_H

/***************************************************************************
* Nested Include Files
***************************************************************************/
#include <urrmcrty.h>

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/

/***************************************************************************
*   Macro Functions
***************************************************************************/

/***************************************************************************
 * Types
 ***************************************************************************/
typedef struct McrieRssiDataTag
{
    UUARFCN             frequency;
    SignedInt16         rssi_13b3;
    Boolean             rssiUpdated;
}
McrieRssiData;

typedef struct McrieRssiInfoTag
{
    Int8                noOfFreq;
    McrieRssiData       rssiData [MAX_FDD_INTER_FREQUENCY_CELLS];
}
McrieRssiInfo;

//ICAT EXPORTED STRUCT
typedef struct IeCellInfoTag
{
    UUARFCN             uarfcn_DL;
    McrCellData         cellData;
    Int8                noOfRssiMeas;
}
IeCellInfo;

typedef struct IeMeasInfoTag
{
/*********** add begin*/
    Int32          measResultsMask;  /* Bit mask indicating which locations in
                                        measResults are occupied. (changed to 32bit in UPGRADE_RRC_NO_CM_ON_ADJ_FREQ activity)   */
/*********** add end*/
    McrMeasResults measResults [UPS_MAX_MEASURED_INTER_FREQ_CELLS];
                                     /* Measurement results.          */
    McrMeasStatus  measStatus;       /* Ongoing measurements status.  */
    McrieRssiInfo  *rssiInfo;        /* Stored RSSI information.      */
}
IeMeasInfo;

typedef struct IeMeasConfigTag
{
    CellIdMask   occupied;
    IeCellInfo   cellInfo [maxCellMeas];
    IeMeasInfo   *measInfo;
    McrFilters   filters;
    UUARFCN      newFreq;
}
IeMeasConfig;

typedef struct IeMeasDataTag
{
    CellIdMask   occupied;       /* Indicates which positions are occupied in
                                    inter-frequency CELL_INFO_LIST.         */
    IeCellInfo   cellInfo [maxCellMeas]; /* Inter-frequency CELL_INFO_LIST. */
    McrInterFreqList freqList;
    IeMeasInfo   *measInfo;      /* Ongoing inter-freq measurements data.   */
    McrFilters   filters;
    IeMeasConfig *savedConfig;
/*********** add begin*/	
#if defined (UPGRADE_RRC_NO_CM_ON_ADJ_FREQ)
    UUARFCN      oldAdjacentFrequencyInfo;
    UUARFCN      adjacentFrequencyInfo;
#endif //UPGRADE_RRC_NO_CM_ON_ADJ_FREQ
/*********** add end*/
/* CQ00104672 added begin */
#if defined (UPGRADE_RRC_NO_CM_ON_INTER_BAND_FREQ)
    UUARFCN      oldInterBandFrequencyInfo;
    UUARFCN      interBandFrequencyInfo;
#endif //UPGRADE_RRC_NO_CM_ON_INTER_BAND_FREQ
/* CQ00104672 added end */
}
IeMeasData;

/***************************************************************************
 *  Macros
 **************************************************************************/

/***************************************************************************
 * Global Data declarations
***************************************************************************/
#if defined(UPGRADE_DSDSWB)
extern IeMeasData   ieMeasData1[URRC_TASK_NUM];
#define ieMeasData ieMeasData1[URRC_DSDS_TASK]
#else/*UPGRADE_DSDSWB*/
extern IeMeasData   ieMeasData;
#endif/*UPGRADE_DSDSWB*/

/***************************************************************************
 * Function Prototypes
 ***************************************************************************/
UUTRA_CarrierRSSI UrrMcrieMapRssi (SignedInt16 rssiVal);


Boolean McrieGetRssiValue (UUARFCN frequency, SignedInt16 *rssiVal_p);

IeMeasData *McrieGetIeMeasDataPtr (void);

McrMeasQuantity *McrieGetMeasQuantityPtr (UIntraFreqMeasQuantity_FDD measQtyType);

void McrieExtractInterFMFromSib11 (McrInterFreqMeasSysInfo *sib11InterFM_p);

void McrieExtractSibInterFM (UMeasurementControlSysInfo  *sys_p,
                           Int8                        index,
                           Boolean                     isSib12);

void McrieExtractSib12NonCriticalData (
           USysInfoType12_v6b0NonCriticalExtensions  *nonCriticalExtensions_p);


Boolean McrieEventDiffFrom2d2fConfigured(UInterFreqReportCriteria_latest *reportCriteria);
/*********** add begin*/
#if defined (UPGRADE_RRC_NO_CM_ON_ADJ_FREQ)
UUARFCN McrieGetAdjacentFrequency (void);
void McrieStopAdjacentFrequencyMeas (void);
/* CQ00104672 added begin */
void McrieRevertAdjacentFrequency(void);
void McrieInitAdjacentFrequancyInfo  (void);
/* CQ00104672 added end */
#endif // UPGRADE_RRC_NO_CM_ON_ADJ_FREQ
/*********** add end*/
/* CQ00104672 added begin */
#if defined (UPGRADE_RRC_NO_CM_ON_INTER_BAND_FREQ)
UUARFCN McrieGetInterBandFrequency(void);
void McrieRevertInterBandFrequency(void);
void McrieStopInterBandFrequencyMeas (void);
void McrieInitInterBandFrequancyInfo  (void);
#endif // UPGRADE_RRC_NO_CM_ON_INTER_BAND_FREQ
#if defined (UPGRADE_RRC_NO_CM_ON_ADJ_FREQ) || defined (UPGRADE_RRC_NO_CM_ON_INTER_BAND_FREQ)
UUARFCN McrieGetSecondaryFrequency (void);
#endif
/* CQ00104672 added end */
#endif  /* URRMCRIE_H */




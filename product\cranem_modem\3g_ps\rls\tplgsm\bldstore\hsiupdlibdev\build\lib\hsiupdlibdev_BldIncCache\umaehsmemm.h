/***************************************************************************
 * Marvell International Ltd
 *
 ***************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/umaehsmemm.h
 *
 ***************************************************************************
 * File Description:
 *
 *    UMAC Enhanced High Speed (DL) Memory Manager
 *
 ***************************************************************************/

#ifndef UMAEHSMEMM_H
#define UMAEHSMEMM_H
/***************************************************************************
* Nested Include Files
***************************************************************************/


#include <system.h>
#include <ut_array_list.h>
#include <umamain.h>
#include <kicachedep.h>
#include <urlstatistics.h>
#include <umaxhsphy.h>



typedef struct UmaEhsMemmSegmentHeaderTag
{
	Int32  bufferIndex; 			      /*< Indicating in which buffer out of the 3 this segment is located > */
	Int32  queueBitmap; 			      /*< a bitmap indicating which queues are using this buffer. 0 means it's empty> */
	Int32  dataSize; 				      /*< Size of the segment in bytes*/
	Int32  prevPoolSize;				  /*< Total bytes before this segments of free memory */
	Int32  prevInUseSegmentOffset;        /*< offset of previous segment which is occupied by valid data */
}UmaEhsMemmSegmentHeader;



typedef struct UmaEhsMemmSegmentTag
{
	UmaEhsMemmSegmentHeader header;	 				       			  	     /*< Size of the segment in bytes*/
	Int8 				    reserved[32 - sizeof(UmaEhsMemmSegmentHeader)];  /*< alignment of segment's data to 32 bytes */
	Int8 				    data[2]; 					   					 /*< 2 in only for compliation, actual size is bigger */
}UmaEhsMemmSegment;



#define UMAEHSMEMM_NUM_ACTIVE_BUFFERS			UMA_PHY_HSDCH_IF_NUM_PHY_POINTERS

#define UMAEHSMEMM_NUM_SPARE_BUFFERS			1

#define UMAEHSMEMM_NUM_BUFFERS					(UMAEHSMEMM_NUM_ACTIVE_BUFFERS + UMAEHSMEMM_NUM_SPARE_BUFFERS)

#define UMAEHSMEMM_MAX_FULL_SEGMENT_PER_BUFFER	15 /* can be incremented if needed to increase the buffer size*/

#define UMAEHSMEMM_ADDITIONAL_SEGMENT_SPACE		(UPHY_MAC_HS_PDU_ADDITIONAL_SPACE + 32) /* 32 is for the E/HS entity management) */

#define UMAEHSMEMM_MAX_SEGMENT_SIZE				(offsetof(UmaEhsMemmSegment,data) + UMAEHSMEMM_ADDITIONAL_SEGMENT_SPACE + UDL_HS_MAX_MAC_HS_PDU_DATA_OCTETS)

#define UMAEHSMEMM_BUFFER_SIZE					(UMAEHSMEMM_MAX_SEGMENT_SIZE*UMAEHSMEMM_MAX_FULL_SEGMENT_PER_BUFFER)

#define UMAEHSMEMM_PROTECTED_ZONE_THRESHOLD		(UMAEHSMEMM_BUFFER_SIZE - UMAEHSMEMM_MAX_SEGMENT_SIZE)

#define UMAEHSMEMM_UNAVAILABLE_MEMORY_HWM		(UMAEHSMEMM_BUFFER_SIZE-3*UMAEHSMEMM_MAX_SEGMENT_SIZE)

#define UMAEHSMEMM_NO_QUEUES_BITMAP				0x0000



typedef struct UmaEhsMemmBufferHeaderTag
{
	Int32 primaryPoolStartOffset; 	   /*< offset from data[0], indicating the current segment held by L1 */
	Int32 primaryPoolEndOffset;    	  /*< offset of the segment which terminates the primary pool */
	Int32 numOfPrimaryPoolSwitch;	  /*< total times the buffer switch to a new primary pool */
	Int32 totalSegmentsInUse; 		  /*<Total segments containing valid data - for statistics*/
	Int32 usedUnavailableMemory; 	  /*<Total bytes that can be used for hsdpa -  for statistics */	
}UmaEhsMemmBufferHeader;


typedef struct UmaEhsMemmBufferTag
{
	UmaEhsMemmBufferHeader header; 										 /*< Header of the memory manager buffer */
	Int8				   reserved[32-sizeof(UmaEhsMemmBufferHeader)];  /*< alignment of buffer's data to 32 bytes */
	Int8 				   data[UMAEHSMEMM_BUFFER_SIZE]; 			 /*< The buffer data */
}UmaEhsMemmBuffer;


typedef struct UmaEhsMemoryManagerTag
{
	UmaEhsMemmBuffer  *buffer_p[UMAEHSMEMM_NUM_BUFFERS]; /*< an array of pointers to mac-ehs memory manager buffers */
	UtArrayList		  expectedL1Buffers;				 /*<list of upcoming buffers expected from L1  */
	UtArrayList		  spareBuffersList;				     /*<list of buffers which currently dont hold pointers to L1  */
}UmaEhsMemoryManager;



Int8* UmaEhsMemInitBuffer(Int8 bufferIndex);

Int8* UmaEhsMemmGetFreeSegment(Int8 *receivedSegment_p,Int16 receivedSegmentSize,Int16 queueBitmap);

void UmaEhsMemmRemoveQueueFromSegment(Int8* pdu_p,Int8 queueId);

#endif //UMAEHSMEMM_H
/* END OF FILE */


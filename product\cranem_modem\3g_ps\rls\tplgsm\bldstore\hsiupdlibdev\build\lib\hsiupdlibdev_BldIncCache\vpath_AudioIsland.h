/******************************************************************************
 *
 *  (C)Copyright ASRMicro. All Rights Reserved.
 *
 *  THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF ASRMicro.
 *  The copyright notice above does not evidence any actual or intended
 *  publication of such source code.
 *  This Module contains Proprietary Information of ASRMicro and should be
 *  treated as Confidential.
 *  The information in this file is provided for the exclusive use of the
 *  licensees of ASRMicro.
 *  Such users have the right to use, modify, and incorporate this code into
 *  products for purposes authorized by the license agreement provided they
 *  include this notice and the associated copyright notice with any such
 *  product.
 *  The information in this file is provided "AS IS" without warranty.
 *
 ******************************************************************************/

/******************************************************************************
*               MODULE IMPLEMENTATION FILE
*******************************************************************************
* Title: Header for AudioIsland in COMM subsystem
*
* Filename: vpath_AudioIsland.h
*
* Authors: <AUTHORS>
*
* Description: Header file for AudioIsland.
*
* Last Updated:
*
* Notes:
******************************************************************************/
#ifndef _AUDIOISLAND_H_
#define _AUDIOISLAND_H_

#ifdef __cplusplus
extern "C"{
#endif


//Max size of one SPI transmission (Tx/Rx)
#define AUDIOISLAND_MAX_TRANSMISSIONSIZE    360             //SSP_DMA_SIZE, count in "short"
#define AUDIOISLAND_NBPCMSIZE               160             //count in "short"
#define AUDIOISLAND_WBPCMSIZE               320             //count in "short"


//ICAT EXPORTED ENUM
typedef enum {
    AUDIOISLAND_DATATYPE_NULL = 0,
    AUDIOISLAND_DATATYPE_PCM,
    AUDIOISLAND_DATATYPE_AMR,
    AUDIOISLAND_DATATYPE_CONTROL,
    AUDIOISLAND_DATATYPE_DEBUG,
    AUDIOISLAND_DATATYPE_NUM = AUDIOISLAND_DATATYPE_DEBUG,
} AUDIOISLAND_DATATYPE;



//ICAT EXPORTED STRUCT
typedef struct
{
    unsigned short   Type;      //AUDIO_DATATYPE
    unsigned short   Length;	// Data[] valid length, count in UINT16
    unsigned short   Reserved1;	//reserved
    unsigned short   Reserved2;	//reserved
    unsigned short   *pData;    //serial data, UINT16 * Length
} AudioIsland_Data;


//ICAT EXPORTED STRUCT
typedef struct
{
    unsigned short   opCode;
    unsigned short   subLength;	//subData[] valid length, count in UINT16
    unsigned short   *psubData;	//serial data, UINT16 * subLength
} AudioIsland_subData;


void AudioIsland_Init(void);
int AudioIsland_FillTxBuffer(unsigned short type, unsigned short *pdata, unsigned int size);
int AudioIsland_ParseRxBuffer(unsigned short *pdata, int bufLen);
int AudioIsland_sendDTMFCtrl(unsigned short onOff, unsigned short type, unsigned short code);

#ifdef __cplusplus
}
#endif
#endif  /* _AUDIOISLAND_H_ */

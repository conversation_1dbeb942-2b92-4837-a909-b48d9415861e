/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/


/******************************************************************************
 * File Name:	umaephy.h
 *
 *
 * File Description: UMACE - PHY interface header file
 *
 *****************************************************************************/
#if !defined (UMAE_PHY_H)
#define UMAE_PHY_H

#if defined (UPGRADE_3G_EDCH)
#if !defined (UMACE_UNIT_TEST)

#include <system.h>

void UmaePhyCreateSharedMemory(void);
void UmaePhyEdchRxChDataIndCallback(void);
void UmaePhyBindEdchRxChDataInd(void);
Int32 UmaePhyGetIsrTicksCounter (void);

#endif /* UMACE_UNIT_TEST */
#endif /* UPGRADE_3G_EDCH */
#endif /* UMAE_PHY_H */


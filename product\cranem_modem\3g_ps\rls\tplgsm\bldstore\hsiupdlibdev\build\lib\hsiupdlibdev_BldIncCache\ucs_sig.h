/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/api/inc/ucs_sig.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2007/01/12 13:10:41 $
 **************************************************************************
 * File Description:
 *
 *    File containing the 'signals' union for UCS.
 **************************************************************************/

#if !defined (UCS_SIG_H)
#define       UCS_SIG_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <crlc_sig.h>
#include <urlc_sig.h>
#include <umac_sig.h>
#include <csdi_sig.h>
#include <csscsdi_sig.h>

/** \addtogroup 3G_CSDI CSDI (CSDI signaling interface)
 *
 * @{
 */
/**\union Signal
* \brief  Union of all signals used in CSDI
*/ 

	
union Signal
{
    UrlcTmDataReq urlcTmDataReq;
    UrlcAmrTmDataReq urlcAmrTmDataReq;
    UrlcCsdTmDataReq urlcCsdTmDataReq;
    UrlcTmDataInd urlcTmDataInd;
    UrlcUlSduDiscardInd urlcUlSduDiscardInd;

    CcsdResumeReq ccsdResumeReq;
    CcsdSuspendReq ccsdSuspendReq;
    CcsdConfigReq ccsdConfigReq;
    CcsdReleaseReq ccsdReleaseReq;
    CcsdUlTtiInd ccsdUlTtiInd;
    CcsdDlTtiInd ccsdDlTtiInd;

    CsdiAmrDataReq csdiAmrDataReq;
    CsdiAmrDataInd csdiAmrDataInd;
    CsdiCsdDataReq csdiCsdDataReq;
    CsdiCsdDataInd csdiCsdDataInd;

    CsdiConnectReq csdiConnectReq;
    CsdiDisconnectReq csdiDisconnectReq;
    CsdiConnectCnf csdiConnectCnf;
    CsdiDisconnectCnf csdiDisconnectCnf;

    CsdiTtiInd csdiTtiInd;

    CsdiResumeInd csdiResumeInd;
    CsdiSuspendInd csdiSuspendInd;
    CsdiConfigInd csdiConfigInd;
    CsdiReleaseInd csdiReleaseInd;

    CsdiEngInfoInd csdiEngInfoInd;
	
	/*CQ29374 yhang 2013-02-27*/
    RrcCodecInd    rrcCodecInd; 

#if defined (SIG_DEF)
#   undef SIG_DEF
#endif
#define SIG_DEF(ENUM,UNION) UNION;
#include <csscsdisig.h>
};
/** @} */ //3G_CSDI

#endif

/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2010] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/
/**\file umacb.h
Contains the declarations for UMAC callback functions.*/


#if !defined (UMACB_H)
#define       UMACB_H
/***************************************************************************
* Nested Include Files
***************************************************************************/

/******************************************************************************
 * Constants
 *****************************************************************************/

/******************************************************************************
 * Type Definitions
 *****************************************************************************/

/***************************************************************************
 * Extern Variables
 ***************************************************************************/

/******************************************************************************
 * Macros
 *****************************************************************************/

/******************************************************************************
 * Function Prototypes
 *****************************************************************************/
void UmaBindCallbackFunctions (void);
void UmaUnBindCallbackFunctions (void);
void UmaBindPhyAccessCnf (void); 
#endif	//UMACB_H

/* END OF FILE */

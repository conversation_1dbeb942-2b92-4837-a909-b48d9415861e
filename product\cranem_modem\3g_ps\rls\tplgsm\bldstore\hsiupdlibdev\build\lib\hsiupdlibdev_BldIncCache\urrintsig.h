
#if !defined (URRINTSIG_H)
#define       URRINTSIG_H

/***************************************************************************
* Nested Include Files
***************************************************************************/
#include <urrtypes.h>
#include <urrsirut.h>

/***************************************************************************
 * Types
 ***************************************************************************/

typedef struct UrrInternalSignalBcchSysInfoTag
{
    UrlcTmDataInd           urlcTmDataInd;
}
UrrInternalSignalBcchSysInfo;

//ICAT EXPORTED STRUCT
typedef struct UrrInternalSignalChangeStateTag
{
    UrrSmcModeAndState   modeAndState;
    Boolean              dueToReconfig;
}
UrrInternalSignalChangeState;

typedef struct UrrInternalSignalNewUeStateTag
{
    UrrSmcModeAndState   modeAndState;
    UrrSmcModeAndState   lastModeAndState;
    UrrModeTransition    modeTransition;
    UrrStateTransition   stateTransition;
    Boolean              dueToReconfig;

    /*********** add begin*/    
    // Relevant only if the above dueToReconfig field is TRUE.
    // Indicates if the reconfiguration message from NW included 
    // Primary Scrambling Code
    Boolean              reconfigIncludedPsc;
    /*********** add end*/    
}
UrrInternalSignalNewUeState;

typedef struct UrrInternalSignalChangePhySmTag
{
    UrrSmcPhyState       state;
}
UrrInternalSignalChangePhySm;

typedef RrcMsDataReq UrrInternalSignalRrcMsDataReq;

#if defined (NON_NEZHA)
typedef RrcMeDataReq UrrInternalSignalRrcMeDataReq;
#else
typedef UrrMeDataReq  UrrInternalSignalRrcMeDataReq;
#endif

typedef RrcUpdateReq UrrInternalSignalRrcUpdateReq;

typedef struct UrrInternalSignalCellSelectedTag
{
    UUARFCN                     uarfcn_dl;
    UPrimaryScramblingCode      primaryScramblingCode;
}
UrrInternalSignalCellSelected;

typedef struct UrrInternalSignalNCellSelectedTag
{
    UUARFCN                     uarfcn_dl;
    UPrimaryScramblingCode      primaryScramblingCode;
}
UrrInternalSignalNCellSelected;

//ICAT EXPORTED STRUCT
typedef struct UrrInternalSignalSelectCellTag
{
    ActivationTime              activationTime;

    UUMTS_CellInformation       umtsCellInformation;

    // flag indicating that cell is to be selected on GSM and wait time
     // to be passed to the GSM side of the stack //
    Boolean                     otherRat;
    AsRat                       targetRat; //********** added
    UWaitTime                   waitTime;

    // NOTE: the following field is, in sequence,
    //       a) set by the URRC sub-process generating this internal signal
    //       b) received and stored by CSR
    //       c) returned by CSR in UrrInternalSignalCampedOnCell. //
    Boolean                     reconfiguration;

    // flag to indicate if the selection is caused by a radio link failure //
    Boolean                     radioLinkFailure;

    //RP-040110/2286
    Boolean                     redirectionInitiated;
    Int8                        numCellRedirectToGSM;
    UGSM_TargetCellInfo         cellsListRedirectToGSM[MAX_GSM_TARGET_CELLS];
    Int8                        numCellRedirectToLTE;
    UEUTRA_TargetFreqInfo       cellsListRedirectToLTE[maxEUTRATargetFreqs];
}
UrrInternalSignalSelectCell;

#if !defined (L1_SUPPORT_GSM_FG_PLMN_SEARCH) && !defined UPGRADE_PLMS /*********** add UPGRADE_PLMS*/
typedef struct UrrInternalSignalSwitchFromGsmPlmnSearchTag
{
	UrrSmcModeAndState previousModeAndState;
}
UrrInternalSignalSwitchFromGsmPlmnSearch;
#endif

typedef struct UrrInternalSignalCphyDeactivateReqTag	/*********** add*/
{
	Boolean fullDeactivation;		//if true L1, L2 and SMC will be deativated. If false only L1 will be deactivated 
}UrrInternalSignalCphyDeactivateReq;

typedef struct UrrInternalSignalIcsEvalFinishedTag	
{
	Boolean isSuccessFul;		
}UrrInternalSignalIcsEvalFinished;

//ICAT EXPORTED STRUCT
typedef struct UrrInternalSignalCampedOnCellTag
{
    ActivateStatus              activateStatus;
    UUARFCN                     uarfcn_dl;
    UPrimaryScramblingCode      primaryScramblingCode;
    Boolean                     frequencyHasChanged;

                             // NOTE: the following fields are set by CSR and
                             // reflect the requests for the relevant pieces of
                             // information in the received
                             // UrrInternalSignalSelectCell. //

    UUARFCN                     requestedUarfcn_dl;
    UPrimaryScramblingCode      requestedPSCode;

                             // NOTE: the following field is set by CSR to the
                             // same value as CSR found in the received
                             // UrrInternalSignalSelectCell. //

    Boolean                     reconfiguration;
    Int16                       lac;
    Int16                       rac;
}
UrrInternalSignalCampedOnCell;

//ICAT EXPORTED STRUCT
typedef struct UrrInternalSignalBcchModInfoTag
{
    Int8                        mibValueTag;          // 0..8 //
    Boolean                     modTimeValid;
    Int16                       bcchModificationTime; // 0..4088 //
}
UrrInternalSignalBcchModInfo;

//ICAT EXPORTED STRUCT
typedef struct UrrInternalSignalCellUpdateTag
{
    UCellUpdateCause cellUpdateCause;
}
UrrInternalSignalCellUpdate;

//ICAT EXPORTED STRUCT
typedef struct UrrInternalSignalUpdateConfirmProcessedTag
{
    Boolean physicalChannelFailure;
    Boolean incompatibleSimulatneousReconfig;
    Boolean cipheringModePresent;
	Boolean integrityModePresent;
}
UrrInternalSignalUpdateConfirmProcessed;

typedef struct UrrInternalSignalIndicateSibSchedulingTag
{
    Boolean         servingCell;
    Int8            numberOfMissingSibs;
    USIB_Type       missingSibs [MAX_SIB_SCHEDULE_SIZE];
    Int8            numberOfSibsToRead;
    USIB_Type       sibsToRead [MAX_SIB_SCHEDULE_SIZE];
    Int8            numberOfMissingExtSibs;
    UrrSirSibTypeExtExt2  missingExtSibs [MAX_EXT_SIB_SCHEDULE_SIZE];
    Int8            numberOfExtSibsToRead;
    UrrSirSibTypeExtExt2  extSibsToRead [MAX_EXT_SIB_SCHEDULE_SIZE];
    Boolean         sib5bisScheduled;
}
UrrInternalSignalIndicateSibScheduling;

typedef struct UrrInternalSignalAirSignalSendStatusTag
{
    UrrAisSendStatus            sendStatus;
    Int16                       messageIdentifier;
}
UrrInternalSignalAirSignalSendStatus;

typedef struct UrrInternalSignalProcessRxAirSignalTag
{
    UrrAisAirSignalType             messageType;
    union
    {
        UDL_CCCH_MessageType *dl_CCCH_Message;
        UDL_DCCH_MessageType *dl_DCCH_Message;
    }
    message;
    URRC_TransactionIdentifier      rxTransId;
    UrrAisTransactionAcceptReject   transactionStatus;
    Boolean                         protocolError;
    PerError                        perError;
    UProtocolErrorCause             cause;
    T_UDL_RLC_Mode_latest           rlcMode;
    BearerIdentity                  bearerIdentity;
    UrlcCellInfo                    cellInfo;
    UIntegrityProtectionInfo        integrityCheckStatus;
    UrrRxMsgVersion                 rxMsgVersion;
}
UrrInternalSignalProcessRxAirSignal;

typedef CmacRachTxStatusInd UrrInternalSignalIndCmacTxStatusInd;

//ICAT EXPORTED STRUCT
typedef struct UrrInternalSignalDrxCycleTag
{
    Int16       drxCycleLength;
}
UrrInternalSignalDrxCycle;

typedef struct UrrInternalSignalRrcConnectionSetupTag
{
    URRCConnectionSetup_latest           *rrcConnSetup_p;
    UrrInternalSignalProcessRxAirSignal rxAirSignal;
}
UrrInternalSignalRrcConnectionSetup;

//ICAT EXPORTED STRUCT
typedef struct UrrInternalSignalRrcConnSetupProcessedTag
{
    RrcConnSetupSuccess errorCode;
}
UrrInternalSignalRrcConnSetupProcessed;

//ICAT EXPORTED STRUCT
typedef struct UrrInternalSignalActiveSetTag
{
    UUARFCN             freq;
    Int8                numLinks;
    UrrActiveSetEntry   activeSet [UPS_MAX_RADIO_LINKS * 2];
}
UrrInternalSignalActiveSet;

typedef struct UrrInternalSignalRachMeasCnfTag
{
    UMeasuredResultsOnRACH *measResults_p;
    UrrSirAllocatedMemList rachMemList;
    UMeasuredResultsOnRACHinterFreq measResultsInterFreq;
    Boolean measResultsInterFreqPresent;
}
UrrInternalSignalRachMeasCnf;

typedef struct UrrInternalSignalSibReceivedTag
{
    USIB_Type   sibType;
    Boolean     connectedModeSibPresent;
}
UrrInternalSignalSibReceived;

//ICAT EXPORTED STRUCT
typedef struct UrrInternalSignalPhyToIdleStateTag
{
    ActivationTime              activationTime;
}
UrrInternalSignalPhyToIdleState;

typedef struct UrrInternalSignalSelectInitFachRachTag
{
    Boolean                     isForRrcConnReq;                /* for ********** PTK_CQ00227845 */      
}
UrrInternalSignalSelectInitFachRach;

//ICAT EXPORTED STRUCT
typedef struct UrrInternalSignalInitFachRachSelectedTag
{
    Boolean     initFachRachIsSelected;
}
UrrInternalSignalInitFachRachSelected;

//ICAT EXPORTED STRUCT
typedef struct UrrInternalSignalStartValueTransmittedTag
{
    UCN_DomainIdentity          cnDomain;
    USTART_Value                startValue;
}
UrrInternalSignalStartValueTransmitted;

//ICAT EXPORTED STRUCT
typedef struct UrrInternalSignalTmHfnCfgIndTag
{
    UCN_DomainIdentity  cnDomain;
                    // CFN at which the HFN info is applied //
    UActivationTime     activationTime;
    UrrTmHfnInfo        tmHfnInfo;
} UrrInternalSignalTmHfnCfgInd;

//ICAT EXPORTED STRUCT
typedef struct UrrInternalSignalTmHfnIndTag
{
    Boolean         psIsPresent;
    UrrTmHfnInfo    psHfn;          // when psIsPresent //
    Boolean         csIsPresent;
    UrrTmHfnInfo    csHfn;          // when csIsPresent //
} UrrInternalSignalTmHfnInd;

#if ! defined (UPGRADE_EXCLUDE_2G)
//ICAT EXPORTED STRUCT
typedef struct UrrInternalSignalReselectToGsmReqTag
{
    Boolean             activationTimePresent;
    UActivationTime     activationTime;
}
UrrInternalSignalReselectToGsmReq;

//ICAT EXPORTED STRUCT
typedef struct UrrInternalSignalReselectToGsmFailureTag
{
    UrrSmcModeAndState modeAndState; // .... before the failure. //
}
UrrInternalSignalReselectToGsmFailure;

//ICAT EXPORTED STRUCT
typedef struct UrrInternalSignalReselectToLteFailureTag
{
    UrrSmcModeAndState modeAndState; // .... before the failure. //
}
UrrInternalSignalReselectToLteFailure;

//ICAT EXPORTED STRUCT
typedef struct UrrInternalSignalReselectToGsmCnfTag
{
    ActivationTime      activationTime;
}
UrrInternalSignalReselectToGsmCnf;

//ICAT EXPORTED STRUCT
typedef struct UrrInternalSignalCellSelectIndTag
{
    Boolean success;
}
UrrInternalSignalCellSelectInd;
#endif   // UPGRADE_EXCLUDE_2G //

typedef CphyNcellBchCnf UrrInternalSignalNcellBchCnf;

//ICAT EXPORTED STRUCT
typedef struct UrrInternalSignalResyncCellCnfTag
{
    Boolean             success;
}
UrrInternalSignalResyncCellCnf;

//ICAT EXPORTED STRUCT
//-+ UID START  ********** 29-Mar-2012  +-
typedef struct UrrInternalSignalBcchErrorTag
{
    BcchError        bcchError;
}
UrrInternalSignalScellBcchError, UrrInternalSignalNcellBcchError;
//-+ UID END  ********** 29-Mar-2012  +-

//ICAT EXPORTED STRUCT                                                         //-+ ********** 29-Mar-2012 +-
typedef struct UrrInternalSignalRlcUnrecoverableErrorTag
{
    BearerIdentity      bearerIdentity;
}
UrrInternalSignalRlcUnrecoverableError;

//ICAT EXPORTED STRUCT
typedef struct UrrInternalSignalDisableCellSelectionOnFreqTag
{
    UUARFCN             uarfcn_dl;
}
UrrInternalSignalDisableCellSelectionOnFreq;

//ICAT EXPORTED STRUCT
typedef struct UrrInternalSignalIndicateScellSibsToAcquireTag
{
    UrrSubProcessId     subProcessId;
    Int8                numberOfSibs;
    USIB_Type           sib_Type [MAX_SIBS_REQUESTED];
    Int8                numberOfExtSibs;
    UrrSirSibTypeExtExt2      sib_TypeExt [MAX_EXT_SIBS_REQUESTED];
}
UrrInternalSignalIndicateScellSibsToAcquire;

typedef UrrInternalSignalIndicateScellSibsToAcquire
                                    UrrInternalSignalIndicateNcellSibsToAcquire;

//ICAT EXPORTED STRUCT
typedef struct UrrInternalSignalResetUeToIdleTag
{
    UReleaseCause   releaseCause;
    Boolean         needActInd; /*********** add*/
}
UrrInternalSignalResetUeToIdle;

typedef struct UrrInternalSignalCellUpdateConfirmTag
{
    UCellUpdateConfirm_latest_IEs       *cellUpdateConfirm_p;
    UrrInternalSignalProcessRxAirSignal rxAirSignal;
}
UrrInternalSignalCellUpdateConfirm;


typedef struct UrrInternalSignalUraUpdateConfirmTag
{
    UraUpdateConfirmType                uraUpdateConfirmType;
    void                                *uraUpdateConfirm_p;
    UrrInternalSignalProcessRxAirSignal rxAirSignal;
}
UrrInternalSignalUraUpdateConfirm;

typedef struct UrrInternalSignalQueueEelementHeaderTag
{
    UrrSubProcessId                         sourceSubProcess;
    UrrSubProcessId                         destinationSubProcess;
}
UrrInternalSignalQueueEelementHeader;

/* Note: RRInternalSignal*** structures are used by the KI internal signals queue
 *         UrrInternalSignal*** structures are used by the inner queues used by some processes
 *         (uurcmr, urrsir)
 */
typedef struct EmptyIntSigTag
{
    UrrInternalSignalQueueEelementHeader     header;
}
EmptyIntSig;

typedef struct RrcIntBcchSysInfoTag
{
    UrrInternalSignalQueueEelementHeader     header;
    UrrInternalSignalBcchSysInfo          payload;
}
RrcIntBcchSysInfo;

//ICAT EXPORTED STRUCT
typedef struct RrcIntChangeStateTag
{
    UrrInternalSignalQueueEelementHeader header;
    UrrInternalSignalChangeState      payload;
}
RrcIntChangeState;

typedef struct RrcIntNewUeStateTag
{
    UrrInternalSignalQueueEelementHeader header;
    UrrInternalSignalNewUeState       payload;
}
RrcIntNewUeState;

typedef struct RrcIntChangePhySmTag
{
    UrrInternalSignalQueueEelementHeader     header;
    UrrInternalSignalChangePhySm          payload;
}
RrcIntChangePhySm;

typedef struct RrcIntRrcMsDataReqTag
{
    UrrInternalSignalQueueEelementHeader     header;
    UrrInternalSignalRrcMsDataReq payload;
}
RrcIntRrcMsDataReq;

typedef struct RrcIntRrcMeDataReqTag
{
    UrrInternalSignalQueueEelementHeader     header;
    UrrInternalSignalRrcMeDataReq payload;
}
RrcIntRrcMeDataReq;

typedef struct RrcIntRrcUpdateReqTag
{
    UrrInternalSignalQueueEelementHeader     header;
    UrrInternalSignalRrcUpdateReq payload;
}
RrcIntRrcUpdateReq;

typedef struct RrcIntCellSelectedTag
{
    UrrInternalSignalQueueEelementHeader     header;
    UrrInternalSignalCellSelected         payload;
}
RrcIntCellSelected;

//ICAT EXPORTED STRUCT
typedef struct RrcIntSelectCellTag
{
    UrrInternalSignalQueueEelementHeader     header;
    UrrInternalSignalSelectCell           payload;
}
RrcIntSelectCell;

//ICAT EXPORTED STRUCT
typedef struct RrcIntCampedOnCellTag
{
    UrrInternalSignalQueueEelementHeader     header;
    UrrInternalSignalCampedOnCell         payload;
}
RrcIntCampedOnCell;

//ICAT EXPORTED STRUCT
typedef struct RrcIntBcchModInfoTag
{
    UrrInternalSignalQueueEelementHeader     header;
    UrrInternalSignalBcchModInfo          payload;
}
RrcIntBcchModInfo;

//ICAT EXPORTED STRUCT
typedef struct RrcIntCellUpdateTag
{
    UrrInternalSignalQueueEelementHeader     header;
    UrrInternalSignalCellUpdate           payload;
}
RrcIntCellUpdate;

//ICAT EXPORTED STRUCT
typedef struct RrcIntUpdateConfirmProcessedTag
{
    UrrInternalSignalQueueEelementHeader           header;
    UrrInternalSignalUpdateConfirmProcessed     payload;
}
RrcIntUpdateConfirmProcessed;

typedef struct RrcIntIndicateSibSchedulingTag
{
    UrrInternalSignalQueueEelementHeader      header;
    UrrInternalSignalIndicateSibScheduling payload;
}
RrcIntIndicateSibScheduling;

typedef struct RrcIntAirSignalSendStatusTag
{
    UrrInternalSignalQueueEelementHeader     header;
    UrrInternalSignalAirSignalSendStatus  payload;
}
RrcIntAirSignalSendStatus;

typedef struct RrcIntProcessRxAirSignalTag
{
    UrrInternalSignalQueueEelementHeader     header;
    UrrInternalSignalProcessRxAirSignal   payload;
}
RrcIntProcessRxAirSignal;

//ICAT EXPORTED STRUCT
typedef struct RrcIntDrxCycleTag
{
    UrrInternalSignalQueueEelementHeader     header;
    UrrInternalSignalDrxCycle             payload;
}
RrcIntDrxCycle;

typedef struct RrcIntRrcConnectionSetupTag
{
    UrrInternalSignalQueueEelementHeader     header;
    UrrInternalSignalRrcConnectionSetup   payload;
}
RrcIntRrcConnectionSetup;

//ICAT EXPORTED STRUCT
typedef struct RrcIntRrcConnSetupProcessedTag
{
    UrrInternalSignalQueueEelementHeader      header;
    UrrInternalSignalRrcConnSetupProcessed payload;
}
RrcIntRrcConnSetupProcessed;

//ICAT EXPORTED STRUCT
typedef struct RrcIntActiveSetTag
{
    UrrInternalSignalQueueEelementHeader      header;
    UrrInternalSignalActiveSet             payload;
}
RrcIntActiveSet;

//ICAT EXPORTED STRUCT
typedef struct RrcIntRachMeasCnfTag
{
    UrrInternalSignalQueueEelementHeader header;
    UrrInternalSignalRachMeasCnf payload;
}
RrcIntRachMeasCnf;

typedef struct RrcIntSibReceivedTag
{
    UrrInternalSignalQueueEelementHeader     header;
    UrrInternalSignalSibReceived          payload;
}
RrcIntSibReceived;

//ICAT EXPORTED STRUCT
typedef struct RrcIntPhyToIdleStateTag
{
    UrrInternalSignalQueueEelementHeader     header;
    UrrInternalSignalPhyToIdleState       payload;
}
RrcIntPhyToIdleState;

typedef struct RrcIntSelectInitFachRachTag
{
    UrrInternalSignalQueueEelementHeader     header;
    UrrInternalSignalSelectInitFachRach   payload;
}
RrcIntSelectInitFachRach;

//ICAT EXPORTED STRUCT
typedef struct RrcIntInitFachRachSelectedTag
{
    UrrInternalSignalQueueEelementHeader     header;
    UrrInternalSignalInitFachRachSelected payload;
}
RrcIntInitFachRachSelected;

//ICAT EXPORTED STRUCT
typedef struct RrcIntStartValueTransmittedTag
{
    UrrInternalSignalQueueEelementHeader      header;
    UrrInternalSignalStartValueTransmitted payload;
}
RrcIntStartValueTransmitted;

//ICAT EXPORTED STRUCT
typedef struct RrcIntTmHfnCfgIndTag
{
    UrrInternalSignalQueueEelementHeader header;
    UrrInternalSignalTmHfnCfgInd      payload;
}
RrcIntTmHfnCfgInd;

//ICAT EXPORTED STRUCT
typedef struct RrcIntTmHfnIndTag
{
    UrrInternalSignalQueueEelementHeader header;
    UrrInternalSignalTmHfnInd         payload;
}
RrcIntTmHfnInd;

#if ! defined (UPGRADE_EXCLUDE_2G)
//ICAT EXPORTED STRUCT
typedef struct RrcIntReselectToGsmReqTag
{
    UrrInternalSignalQueueEelementHeader    header;
    UrrInternalSignalReselectToGsmReq    payload;
}
RrcIntReselectToGsmReq;

//ICAT EXPORTED STRUCT
typedef struct RrcIntReselectToGsmFailureTag
{
    UrrInternalSignalQueueEelementHeader     header;
    UrrInternalSignalReselectToGsmFailure payload;
}
RrcIntReselectToGsmFailure;

//ICAT EXPORTED STRUCT
typedef struct RrcIntReselectToLteFailureTag
{
    UrrInternalSignalQueueEelementHeader     header;
    UrrInternalSignalReselectToLteFailure payload;
}
RrcIntReselectToLteFailure;

#if ! defined (UPGRADE_EXCLUDE_2G)
//ICAT EXPORTED STRUCT
typedef struct RrcIntReselectToGsmCnfTag
{
    UrrInternalSignalQueueEelementHeader header;
    UrrInternalSignalReselectToGsmCnf payload;
}
RrcIntReselectToGsmCnf;
#endif   /* UPGRADE_EXCLUDE_2G */

//ICAT EXPORTED STRUCT
typedef struct RrcIntCellSelectIndTag
{
    UrrInternalSignalQueueEelementHeader header;
    UrrInternalSignalCellSelectInd    payload;
}
RrcIntCellSelectInd;
#endif   /* UPGRADE_EXCLUDE_2G */

typedef struct RrcIntNcellBchCnfTag
{
    UrrInternalSignalQueueEelementHeader   header;
    UrrInternalSignalNcellBchCnf        payload;
}
RrcIntNcellBchCnf;

//ICAT EXPORTED STRUCT
typedef struct RrcIntResyncCellCnfTag
{
    UrrInternalSignalQueueEelementHeader   header;
    UrrInternalSignalResyncCellCnf      payload;
}
RrcIntResyncCellCnf;

//-+ UID START  ********** 29-Mar-2012  +-
//ICAT EXPORTED STRUCT
typedef struct RrcIntScellBcchErrorTag
{
    UrrInternalSignalQueueEelementHeader    header;
    UrrInternalSignalScellBcchError         payload;
}
RrcIntScellBcchError;
//-+ UID END  ********** 29-Mar-2012  +-

//-+ UID START  ********** 29-Mar-2012  +-
//ICAT EXPORTED STRUCT
typedef struct RrcIntNcellBcchErrorTag
{
    UrrInternalSignalQueueEelementHeader    header;
    UrrInternalSignalNcellBcchError         payload;
}
RrcIntNcellBcchError;
//-+ UID END  ********** 29-Mar-2012  +-

/*CQ00098682 add begin*/
typedef struct UrrInternalSignalAbortPlmnSearchInfoTag
{
	Boolean						causedBySIM2Service;	
}
UrrInternalSignalAbortPlmnSearchInfo;

//ICAT EXPORTED STRUCT
typedef struct RrcIntAbortPlmnSearchReqTag
{
    UrrInternalSignalQueueEelementHeader    header;
    UrrInternalSignalAbortPlmnSearchInfo    payload;
}
RrcIntAbortPlmnSearchReq;
/*CQ00098682 add end*/


//ICAT EXPORTED STRUCT
typedef struct RrcIntRlcUnrecoverableErrorTag
{
    UrrInternalSignalQueueEelementHeader       header;
    UrrInternalSignalRlcUnrecoverableError  payload;
}
RrcIntRlcUnrecoverableError;

//ICAT EXPORTED STRUCT
typedef struct RrcIntDisableCellSelectionOnFreqTag
{
    UrrInternalSignalQueueEelementHeader           header;
    UrrInternalSignalDisableCellSelectionOnFreq payload;
}
RrcIntDisableCellSelectionOnFreq;

//ICAT EXPORTED STRUCT
typedef struct RrcIntIndicateScellSibsToAcquireTag
{
    UrrInternalSignalQueueEelementHeader           header;
    UrrInternalSignalIndicateScellSibsToAcquire payload;
}
RrcIntIndicateScellSibsToAcquire;

typedef struct RrcIntIndicateNcellSibsToAcquireTag
{
    UrrInternalSignalQueueEelementHeader           header;
    UrrInternalSignalIndicateNcellSibsToAcquire payload;
}
RrcIntIndicateNcellSibsToAcquire;

//ICAT EXPORTED STRUCT
typedef struct RrcIntResetUeToIdleTag
{
    UrrInternalSignalQueueEelementHeader  header;
    UrrInternalSignalResetUeToIdle        payload;
}
RrcIntResetUeToIdle;

typedef struct RrcIntCellUpdateConfirmTag
{
    UrrInternalSignalQueueEelementHeader     header;
    UrrInternalSignalCellUpdateConfirm    payload;
}
RrcIntCellUpdateConfirm;

typedef struct RrcIntUraUpdateConfirmTag
{
    UrrInternalSignalQueueEelementHeader     header;
    UrrInternalSignalUraUpdateConfirm     payload;
}
RrcIntUraUpdateConfirm;

#if !defined (L1_SUPPORT_GSM_FG_PLMN_SEARCH) && !defined UPGRADE_PLMS /*********** add UPGRADE_PLMS*/
typedef struct RrcIntSwitchFromGsmPlmnSearchTag
{
    UrrInternalSignalQueueEelementHeader     header;
    UrrInternalSignalSwitchFromGsmPlmnSearch   payload;
}
RrcIntSwitchFromGsmPlmnSearch;
#endif

typedef struct RrcIntCphyDeactivateReq /*********** add*/
{
    UrrInternalSignalQueueEelementHeader     header;
    UrrInternalSignalCphyDeactivateReq   payload;
}
RrcIntCphyDeactivateReq;

typedef struct RrcIntIcsEvalFinished //********** add
{
    UrrInternalSignalQueueEelementHeader     header;
    UrrInternalSignalIcsEvalFinished        payload;
}
RrcIntIcsEvalFinished;

#endif
/* END OF FILE */

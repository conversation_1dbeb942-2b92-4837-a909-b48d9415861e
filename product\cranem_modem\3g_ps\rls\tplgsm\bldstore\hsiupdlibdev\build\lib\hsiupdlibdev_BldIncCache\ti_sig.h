/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/2g.mod/api/inc/ti_sig.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2007/01/12 13:10:41 $
 **************************************************************************
 *  File Description :
 *      L1  - test task interface
 **************************************************************************/

#ifndef TI_SIG_H
#define TI_SIG_H

/****************************************************************************
 * Nested Include Files
 ****************************************************************************/

#include <kitqid.h>
#include <l13_typ.h>

/****************************************************************************
 * Macros
 ****************************************************************************/

/****************************************************************************
 * Types
 ****************************************************************************/
typedef enum DaiModeTag
{
    DAI_OFF = 0,
    DAI_DOWNLINK,
    DAI_UPLINK,
    DAI_AUDIO
}
DaiMode;

typedef enum TchModeTag
{
    TCH_OFF,
    TCH_ON,
    TCH_SUBCHANNEL_1
}
TchMode;

/****************************************************************************
 * Signal types
 ****************************************************************************/

typedef struct TiDaiReqTag
{
    DaiMode                         daiMode;
    TaskId                          testTask;
}
TiDaiReq;

typedef struct TiTchReqTag
{
    TchMode                         tchMode;
    TchLoopbackMode                 loopbackMode;
    TaskId                          testTask;
}
TiTchReq;

#endif  /* include guard */
/* END OF FILE */

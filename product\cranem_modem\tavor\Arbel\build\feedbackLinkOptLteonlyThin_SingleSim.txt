;#<FEEDBACK># ARM Linker, 5060422: Last Updated: Mon Aug 04 16:36:50 2025
;VERSION 0.2
;FILE 3DES.o
Base64Decode <= USED 0
Base64Encode <= USED 0
CovertKey <= USED 0
GetByte <= USED 0
MyDesInit <= USED 0
Run1Des <= USED 0
RunPad <= USED 0
RunRsm <= USED 0
;FILE A2dpAudioSink.o
;FILE AAtomizer.o
_ZN14streamingmedia9AAtomizer4HashEPKc <= USED 0
;FILE ABitReader.o
_ZN10ABitReader19getBitsWithFallbackEjj <= USED 0
_ZN10ABitReader7putBitsEjj <= USED 0
_ZN10ABitReader8skipBitsEj <= USED 0
_ZN10ABitReaderD0Ev <= USED 0
_ZNK10ABitReader4dataEv <= USED 0
;FILE ABuffer.o
_ZN14streamingmedia7ABuffer18setFarewellMessageEN7android2spINS_8AMessageEEE <= USED 0
_ZN14streamingmedia7ABuffer4metaEv <= USED 0
_ZN14streamingmedia7ABufferC1EPvj <= USED 0
;FILE ALooper.o
;FILE ALooperRoster.o
_ZN14streamingmedia13ALooperRoster10findLooperEi <= USED 0
_ZN14streamingmedia13ALooperRoster20postAndAwaitResponseERKN7android2spINS_8AMessageEEEPS4_ <= USED 0
_ZN14streamingmedia13ALooperRoster9postReplyEjRKN7android2spINS_8AMessageEEE <= USED 0
;FILE AMRDecoder.o
_ZThn8_N10AMRDecoderD0Ev <= USED 0
_ZThn8_N10AMRDecoderD1Ev <= USED 0
;FILE AMRExtractor.o
_ZN12AMRExtractorC1EPc <= USED 0
_ZThn4_N9AMRSourceD0Ev <= USED 0
_ZThn4_N9AMRSourceD1Ev <= USED 0
;FILE AMRRTPAssembler.o
;FILE AMediaPlayer.o
_ZN12AMediaPlayer12getMediaMetaEP18AMediaFileMetadata <= USED 0
_ZN12AMediaPlayer12getTrackInfoEPtS0_PjS0_S0_S0_ <= USED 0
_ZN12AMediaPlayer13setDataSourceEPKc <= USED 0
_ZN12AMediaPlayer13setDataSourceEPhjPFijS0_jEj <= USED 0
_ZN12AMediaPlayer16setUsingA2dpSinkEb <= USED 0
_ZN12AMediaPlayer27configVideoRenderParametersEP21VideoRenderParameters <= USED 0
_ZN12AMediaPlayer27setAudioSinkWithOffloadModeEv <= USED 0
_ZN12AMediaPlayer6AVSyncD1Ev <= USED 0
;FILE AMediaRecorder.o
_ZN14AMediaRecorder13getDurationMsEPj <= USED 0
_ZN14AMediaRecorder13setOutputFileEPKc <= USED 0
_ZN14AMediaRecorder14setMaxFileSizeEj <= USED 0
_ZN14AMediaRecorder15InsertSyncFrameEv <= USED 0
_ZN14AMediaRecorder15setOutputBufferEPhj <= USED 0
_ZN14AMediaRecorder18setMaxFileDurationEj <= USED 0
_ZN14AMediaRecorder7getSizeEPj <= USED 0
;FILE AMessage.o
_ZN14streamingmedia8AMessage10FromParcelERKNS_6ParcelE <= USED 0
_ZN14streamingmedia8AMessage10setMessageEPKcRKN7android2spIS0_EE <= USED 0
_ZN14streamingmedia8AMessage17setObjectInternalEPKcRKN7android2spINS3_7RefBaseEEENS0_4TypeE <= USED 0
_ZN14streamingmedia8AMessage20postAndAwaitResponseEPN7android2spIS0_EE <= USED 0
_ZN14streamingmedia8AMessage5clearEv <= USED 0
_ZN14streamingmedia8AMessage7setRectEPKciiii <= USED 0
_ZN14streamingmedia8AMessage7setSizeEPKcj <= USED 0
_ZN14streamingmedia8AMessage8freeItemEPNS0_4ItemE <= USED 0
_ZN14streamingmedia8AMessage8setFloatEPKcf <= USED 0
_ZN14streamingmedia8AMessage9postReplyEj <= USED 0
_ZN14streamingmedia8AMessage9setBufferEPKcRKN7android2spINS_7ABufferEEE <= USED 0
_ZN14streamingmedia8AMessage9setDoubleEPKcd <= USED 0
_ZN14streamingmedia8AMessage9setTargetEi <= USED 0
_ZNK14streamingmedia8AMessage10findBufferEPKcPN7android2spINS_7ABufferEEE <= USED 0
_ZNK14streamingmedia8AMessage10findDoubleEPKcPd <= USED 0
_ZNK14streamingmedia8AMessage10findStringEPKcPNS_7AStringE <= USED 0
_ZNK14streamingmedia8AMessage11debugStringEi <= USED 0
_ZNK14streamingmedia8AMessage11findMessageEPKcPN7android2spIS0_EE <= USED 0
_ZNK14streamingmedia8AMessage12countEntriesEv <= USED 0
_ZNK14streamingmedia8AMessage13writeToParcelEPNS_6ParcelE <= USED 0
_ZNK14streamingmedia8AMessage14getEntryNameAtEjPNS0_4TypeE <= USED 0
_ZNK14streamingmedia8AMessage20senderAwaitsResponseEPj <= USED 0
_ZNK14streamingmedia8AMessage8findRectEPKcPiS3_S3_S3_ <= USED 0
_ZNK14streamingmedia8AMessage8findSizeEPKcPj <= USED 0
_ZNK14streamingmedia8AMessage9findFloatEPKcPf <= USED 0
;FILE AString.o
_ZN14streamingmedia12StringPrintfEPKcz <= USED 0
_ZN14streamingmedia7AString4trimEv <= USED 0
_ZN14streamingmedia7AString5clearEv <= USED 0
_ZN14streamingmedia7AString5eraseEjj <= USED 0
_ZN14streamingmedia7AString5setToEPKc <= USED 0
_ZN14streamingmedia7AString6appendEPv <= USED 0
_ZN14streamingmedia7AString6appendERKS0_jj <= USED 0
_ZN14streamingmedia7AString6appendEd <= USED 0
_ZN14streamingmedia7AString6appendEf <= USED 0
_ZN14streamingmedia7AString6appendEl <= USED 0
_ZN14streamingmedia7AString6appendEm <= USED 0
_ZN14streamingmedia7AString6appendEx <= USED 0
_ZN14streamingmedia7AString6appendEy <= USED 0
_ZN14streamingmedia7AString6insertEPKcjj <= USED 0
_ZN14streamingmedia7AString6insertERKS0_j <= USED 0
_ZN14streamingmedia7AString7astrdupEPKc <= USED 0
_ZN14streamingmedia7AString7tolowerEv <= USED 0
_ZN14streamingmedia7AStringC1ERKS0_jj <= USED 0
_ZNK14streamingmedia7AString10startsWithEPKc <= USED 0
_ZNK14streamingmedia7AString4findEPKcj <= USED 0
_ZNK14streamingmedia7AString4hashEv <= USED 0
_ZNK14streamingmedia7AString4sizeEv <= USED 0
_ZNK14streamingmedia7AString7compareERKS0_ <= USED 0
_ZNK14streamingmedia7AString8endsWithEPKc <= USED 0
_ZNK14streamingmedia7AStringgtERKS0_ <= USED 0
_ZNK14streamingmedia7AStringltERKS0_ <= USED 0
;FILE ATimers.o
_ZN7android13DurationTimer13addTotimevalTEP8timevalTl <= USED 0
_ZN7android13DurationTimer17subtracttimevalTsEPK8timevalTS3_ <= USED 0
_ZN7android13DurationTimer4stopEv <= USED 0
_ZN7android13DurationTimer5startEv <= USED 0
_ZNK7android13DurationTimer13durationUsecsEv <= USED 0
toMillisecondTimeoutDelay <= USED 0
;FILE AVideoSink.o
_ZN10AVideoSink18releaseFramebufferEPv <= USED 0
_ZN10AVideoSink4selfEv <= USED 0
_ZThn4_N10AVideoSinkD0Ev <= USED 0
_ZThn4_N10AVideoSinkD1Ev <= USED 0
;FILE AifAudioSink.o
;FILE AifOffloadAudioSink.o
;FILE AmrBEPayloadEncap.o
_ZN14streamingmedia17AmrBEPayloadEncap13releaseframesERN7android6VectorIPNS_18MediaBufferWrapperEEE <= USED 0
;FILE AmrOAPayloadEncap.o
_ZN14streamingmedia17AmrOAPayloadEncap13releaseframesERN7android6VectorIPNS_18MediaBufferWrapperEEE <= USED 0
;FILE AmrPayloadEncap.o
_ZN14streamingmedia15AmrPayloadEncap9setNewCMREh <= USED 0
;FILE Andratomic.o
android_atomic_acquire_cas <= USED 0
android_atomic_acquire_load <= USED 0
android_atomic_acquire_store <= USED 0
android_atomic_and <= USED 0
android_atomic_cas <= USED 0
android_atomic_cmpxchg_thumb <= USED 0
android_atomic_or <= USED 0
android_atomic_release_load <= USED 0
android_atomic_release_store <= USED 0
android_compiler_barrier <= USED 0
android_memory_barrier <= USED 0
android_memory_store_barrier <= USED 0
;FILE Andrlog.o
__android_log_btwrite <= USED 0
__android_log_buf_print <= USED 0
__android_log_bwrite <= USED 0
__android_log_vprint <= USED 0
__non_inline_android_log_write <= USED 0
getLogLevel <= USED 0
setLogLevel <= USED 0
;FILE AntTunerConfig.o
AntTunerParamDynamicSwitch <= USED 0
;FILE AntTunerConfig_NVM.o
AntTunerConfig_CreateNVMFile <= USED 0
;FILE AucCodecStub.o
_ZN14streamingmedia13amrTxCallBackEP15amrFrameInfo_ts <= USED 0
_ZN14streamingmedia19amrRxCallBack_neteqEPv <= USED 0
;FILE AucEncoder.o
_ZN14streamingmedia10AucEncoder12packEVSFrameEP15IMSFrameInfo_tsx <= USED 0
_ZN14streamingmedia10AucEncoder7getMuteEv <= USED 0
;FILE AudioAMRFormatParameter.o
_ZN23AudioAMRFormatParameter17set_amr_parameterERK13AMRParameter_ <= USED 0
_ZN23AudioAMRFormatParameter19set_amr_support_redEb <= USED 0
_ZN23AudioAMRFormatParameter30set_amr_mode_change_capabilityEj <= USED 0
;FILE AudioEVSFormatParameter.o
;FILE AudioExtern.o
CraneCodecADCOnOff <= USED 0
LteVoiceResume <= USED 0
LteVoiceSuspend <= USED 0
amrResume <= USED 0
amrSuspend <= USED 0
audioGsmResumeCB <= USED 0
audioGsmSuspendCB <= USED 0
;FILE AudioHAL_Control.o
AudioHAL_AifBindCodec_CB <= USED 0
AudioHAL_AifBindHeadsetDetectionCB <= USED 0
AudioHAL_AifBindSpeakerPA_CB <= USED 0
AudioHAL_AifBind_BTEvent_CB <= USED 0
AudioHAL_AifBitClkControl <= USED 0
AudioHAL_AifConfigure <= USED 0
AudioHAL_AifControlMCLK <= USED 0
AudioHAL_AifDTMF <= USED 0
AudioHAL_AifGetHeadsetInfo <= USED 0
AudioHAL_AifGetOpenedDevice <= USED 0
AudioHAL_AifGetTxCodecGainStage1 <= USED 0
AudioHAL_AifGetTxCodecGainStage2 <= USED 0
AudioHAL_AifGetTxDSPGain <= USED 0
AudioHAL_AifGetVolume <= USED 0
AudioHAL_AifGet_VAD <= USED 0
AudioHAL_AifHeadsetDetection <= USED 0
AudioHAL_AifSetDevicePort <= USED 0
AudioHAL_AifSetDspRxMute <= USED 0
AudioHAL_AifSetMute <= USED 0
AudioHAL_AifSetPhoneStatus <= USED 0
AudioHAL_AifSetSideTone <= USED 0
AudioHAL_AifSetTxCodecGain <= USED 0
AudioHAL_AifSetTxDSPGain <= USED 0
AudioHAL_AifSet_VAD <= USED 0
AudioHAL_AifTone <= USED 0
AudioHAL_AifTonePause <= USED 0
AudioHAL_RequestCodecReport <= USED 0
AudioHAL_TestBindSpkPA <= USED 0
AudioHAL_TestControl <= USED 0
AudioHAL_TestLoopback <= USED 0
AudioHAL_TestRequestCodecReport <= USED 0
AudioHAL_TestSetMute <= USED 0
AudioHAL_TestSetTxGain <= USED 0
AudioHAL_TestSetVolume <= USED 0
AudioHAL_TestWriteCodec <= USED 0
AudioHAL_control_PA <= USED 0
AudioHAL_sspaSetBCLKInvert <= USED 0
AudioHAL_sspaSetBCLKType <= USED 0
AudioHAL_sspaSetDelay <= USED 0
AudioHAL_sspaSetFrameClkInvert <= USED 0
AudioHAL_sspaSetFrameClkWidth <= USED 0
AudioHAL_sspaSetI2S <= USED 0
AudioHAL_sspaSetSlave <= USED 0
AudioHAL_sspaTestMode <= USED 0
AudioHAL_sspa_test_mode <= USED 0
AudioHAL_start_bt_hfp_hfMode <= USED 0
AudioHAL_switch_FrameClk_invert <= USED 0
AudioHAL_switch_FrameClk_width <= USED 0
AudioHAL_switch_PCM_I2S <= USED 0
AudioHAL_switch_bclk_fs <= USED 0
AudioHAL_switch_bclk_invert <= USED 0
AudioHAL_switch_mic <= USED 0
AudioHAL_switch_sspa_slave <= USED 0
AudioHAL_vibration_control <= USED 0
AudioHAL_vibration_setFreq <= USED 0
AudioHAL_vibration_setGain <= USED 0
AudioHAL_vibration_setHfpMode <= USED 0
gpio13_pa_gpio_init <= USED 0
gpio28_pa_gpio_init <= USED 0
gpio75_pa_gpio_init <= USED 0
hwtest_micphone_speaker_test <= USED 0
sspa_mfpr_disable_DataPin <= USED 0
sspa_mfpr_dump <= USED 0
test_BitClkControl <= USED 0
test_MclkControl <= USED 0
test_switch_BTHFP_HFMode <= USED 0
;FILE AudioHAL_Stream.o
AudioHAL_AifPause <= USED 0
AudioHAL_DRCReset <= USED 0
AudioHAL_FadingControl <= USED 0
AudioHAL_PlaybackPause <= USED 0
AudioHAL_SetFadingStep <= USED 0
AudioHAL_SetResBufCnt <= USED 0
AudioHAL_Switch_close_delay <= USED 0
AudioHAL_Switch_record_outputRate <= USED 0
AudioHAL_TestDRCReset <= USED 0
AudioHAL_TestMediaLoopback <= USED 0
AudioHAL_VoiceRecord <= USED 0
AudioHAL_set_close_delay <= USED 0
AudioHAL_test_adaptor <= USED 0
AudioHal_AifPlay2Farend <= USED 0
TestPlay_HalfHandler_mediaLoop <= USED 0
TestRecord_HalfHandler_mediaLoop <= USED 0
TestSetBufCnt <= USED 0
audio_add_extra_process <= USED 0
audio_remove_extra_process <= USED 0
audiohal_adaptor_unbind <= USED 0
test_set_MediaVEIndex <= USED 0
;FILE AudioInit.o
ACMAudioFormatSupported <= USED 0
audioInited <= USED 0
ecallApp_Init <= USED 0
;FILE AudioTriState.o
;FILE Biquad_IIR.o
;FILE ByteUtils.o
_Z8U64LE_ATPKh <= USED 0
;FILE COMCfg.o
COMCfgAddRecord <= USED 0
COMCfgFindFirst <= USED 0
COMCfgFindNext <= USED 0
COMCfgGetParameter <= USED 0
COMCfgPhase1Init <= USED 0
COMCfgReadName <= USED 0
IsCOMCfgValid <= USED 0
testCOMCfgFindExact <= USED 0
testCOMCfgFindNext <= USED 0
;FILE CPMediaManagerClient.o
_ZN14streamingmedia20CPMediaManagerClient11AddBearerIdEi <= USED 0
_ZN14streamingmedia20CPMediaManagerClient11EnableVoLTEEb <= USED 0
_ZN14streamingmedia20CPMediaManagerClient14GetMediaConfigE15SDPMeida_Config <= USED 0
_ZN14streamingmedia20CPMediaManagerClient14RemoveBearerIdEi <= USED 0
_ZN14streamingmedia20CPMediaManagerClient15SetVoLTEAddressEPKc <= USED 0
_ZN14streamingmedia20CPMediaManagerClient15getMediaSessionEb <= USED 0
_ZN14streamingmedia20CPMediaManagerClient24SetConfigCommonInterfaceEjRKNS_6ParcelE <= USED 0
_ZN14streamingmedia20CPMediaManagerClient7releaseEv <= USED 0
_ZN14streamingmedia20CPMediaManagerClient8UnitTestERKN7android7String8Ei <= USED 0
_ZN14streamingmedia20CPMediaManagerClient9GetConfigE14Media_Config_tPjPv <= USED 0
_ZN14streamingmedia20CPMediaManagerClientD2Ev <= USED 0
;FILE CPMediaSessionClient.o
_ZN14streamingmedia20CPMediaSessionClient10getRtpPortEv <= USED 0
_ZN14streamingmedia20CPMediaSessionClient7getMuteEb <= USED 0
_ZN14streamingmedia20CPMediaSessionClient7releaseEv <= USED 0
_ZN14streamingmedia20CPMediaSessionClient7setMuteEbb <= USED 0
_ZN14streamingmedia20CPMediaSessionClient8SendDTMFEPK16Media_DTMFData_t <= USED 0
;FILE CPMediaStreamClient.o
_ZN14streamingmedia19CPMediaStreamClient12GetLocalPortEPi <= USED 0
_ZN14streamingmedia19CPMediaStreamClient20GetStreamCodecConfigE26Media_SessionCodecConfig_ti15Media_DirectionPjPv <= USED 0
;FILE CacheSource.o
;FILE CapNeg.o
;FILE CapNegMediaStreamToSDP.o
;FILE CapNegSDPToMediaStream.o
;FILE CapNegUtils.o
CNGetAttributeFromList <= USED 0
CNGetProfileLevelIdValue <= USED 0
CN_Destroy_MediaStreamList <= USED 0
CN_Destroy_SdpSessionInfo <= USED 0
dump_sdp_session_info <= USED 0
;FILE CpVolume.o
;FILE DSPDebugConfig.o
dspDbg_SendFTtoolDisableCmd <= USED 0
dspDbg_SendFTtoolDisableCmd_Auto <= USED 0
dspDbg_SendFTtoolEnableCmd_Auto <= USED 0
dspDbg_StartPsramAccess <= USED 0
dspDbg_stopAutoFTT <= USED 0
;FILE DSPDebugConfig_NVM.o
DSPDebugConfig_CreateNVMFile <= USED 0
;FILE DTMFFormatParameter.o
;FILE DTMF_Para_Gen.o
;FILE DataSource.o
;FILE DiagSig_PS.o
GetCurrentHISRPointer <= USED 0
L1TraceSignal <= USED 0
_Test8KStructure <= USED 0
;FILE Drc_Loudspeaker.o
;FILE ECM.o
;FILE EEHandler.o
eeHandlerSaveDescFileLineParamWarning <= USED 0
eeHandlerSaveDescFileLineWarning <= USED 0
eeHandlerSaveDescWarning <= USED 0
errHandlerSaveToFDIPeriodic <= USED 0
errorHandlerPhase1Init <= USED 0
;FILE EEHandler_Serial.o
DBG_Heartbeat <= USED 0
DBG_HeartbeatMsgSet <= USED 0
SerialHeartbeatOff <= USED 0
;FILE EEHandler_config.o
;FILE EEHandler_fatal.o
EELOG_WaitForAck <= USED 0
EELOG_WaitForEQ <= USED 0
FatalErrorHandler <= USED 0
SilentReset_set <= USED 0
Trigger_SilentReset_For_SDcard <= USED 0
eeDeferredFinalActionRunEx <= USED 0
eeExtExceptionHandlerBind <= USED 0
eeForceNonDeferred <= USED 0
eeGetPcStep <= USED 0
eeHandlerSaveDescFileLineAssertRegs <= USED 0
eeParkThreadEx <= USED 0
eeh_is_data_abort <= USED 0
eeh_set_data_abort <= USED 0
errHandlerFinalAction <= USED 0
lcdDisplayExceptionInfo <= USED 0
sd_eeh_prepare <= USED 0
;FILE EEHandler_hal.o
AllfilesDone <= USED 0
EELOG_finish_ <= USED 0
SspEeSendEehHalRcvEvent <= USED 0
WDTSRDYActive <= USED 0
WDTSRDYInactive <= USED 0
assertSRDYActive <= USED 0
assertSRDYInactive <= USED 0
ee_delay <= USED 0
ee_delay_us <= USED 0
;FILE EEHandler_handlers.o
;FILE EE_Postmortem.o
;FILE EE_silentReset.o
GetAmbientStatusBeforeReset <= USED 0
Is_silentReset <= USED 0
ambient_status_set <= USED 0
check_data <= USED 0
reset_flag_read <= USED 0
silentReset_Check <= USED 0
;FILE EE_wdtManager.o
TriggerWDTInt <= USED 0
WDTMoniterTrigger <= USED 0
eeWdtEehTimeStamp <= USED 0
;FILE ESDS.o
_ZNK4ESDS10getBitRateEPjS0_ <= USED 0
;FILE EVSRTPAssembler.o
;FILE FDI_Partition.o
;FILE FDI_Transport.o
ACATwrapper_print_nameList <= USED 0
;FILE FatSysWrapper.o
FDI5_fileinfo_date2str <= USED 0
FDI5_fileinfo_time2str <= USED 0
FDI_Access <= USED 0
FDI_CloseDir <= USED 0
FDI_DirRead <= USED 0
FDI_GetUsedSpaceSize_fatsys <= USED 0
FDI_MakeDir <= USED 0
FDI_OpenDir <= USED 0
FDI_Stat <= USED 0
FDI_feof <= USED 0
FDI_feof_fatsys <= USED 0
FDI_timercount <= USED 0
fdi_sem_lock_check <= USED 0
;FILE FileSource.o
_ZN10FileSourceC1EPc <= USED 0
;FILE FormatParameter.o
;FILE FreqChange.o
AxiFreqChangeTo208 <= USED 0
CpCoreFreqChangeTo832 <= USED 0
CpCoreGetFreq <= USED 0
Crane_Get_Svc_Level_Values <= USED 0
Crane_Set_Svc_cust <= USED 0
Delay_32k <= USED 0
Delta_32k_Tick <= USED 0
GetCurrentCpCoreFreq <= USED 0
GetCurrentPsramPhyFreq <= USED 0
GetLastCpuIdleRate <= USED 0
PsramPhyFreqChangeTo156 <= USED 0
PsramPhyFreqChangeTo208 <= USED 0
PsramPhyFreqChangeTo533 <= USED 0
PsramPhyFreqChangeTo600 <= USED 0
PsramPhyFreqChangeTo711 <= USED 0
PsramPhyGetFreq <= USED 0
axi_clk_fc_config <= USED 0
cpcore_fc_fix_bywifi <= USED 0
cpu_dvc_init <= USED 0
efuse_dro <= USED 0
get_prf_num_by_dro_crn <= USED 0
phy_pll2_fc <= USED 0
read_dro <= USED 0
;FILE GBMemoryDump.o
GBMemDumpGetAssertCauseFlag <= USED 0
;FILE GKITick.o
;FILE GenericNACKFeedBackPacket.o
_ZN14streamingmedia25GenericNACKFeedBackPacket19ConstructPacketHeadEv <= USED 0
_ZN14streamingmedia25GenericNACKFeedBackPacket6SetBLPEt <= USED 0
_ZN14streamingmedia25GenericNACKFeedBackPacket6SetPIDEt <= USED 0
_ZNK14streamingmedia25GenericNACKFeedBackPacket6GetBLPEv <= USED 0
_ZNK14streamingmedia25GenericNACKFeedBackPacket6GetPIDEv <= USED 0
;FILE ID3.o
_ZN3ID310findFrame2EPKcS1_PPKhPjS5_ <= USED 0
_ZN3ID310scanFramesEv <= USED 0
_ZN3ID311getAlbumArtEPjPcj <= USED 0
_ZN3ID311getTagValueEPKcS1_Phj <= USED 0
_ZN3ID320ParseSyncsafeIntegerEPKhPj <= USED 0
_ZN3ID323removeUnsynchronizationEv <= USED 0
_ZN3ID327removeUnsynchronizationV2_4Eb <= USED 0
_ZN3ID37parseV1EP10DataSource <= USED 0
_ZN3ID37parseV2EP10DataSourcej <= USED 0
_ZN3ID3C1EP10DataSourcebj <= USED 0
_ZN3ID3D1Ev <= USED 0
_ZNK3ID315getHeaderLengthEv <= USED 0
_ZNK3ID37isValidEv <= USED 0
_ZNK3ID37versionEv <= USED 0
_ZNK3ID39getstringEPKhjjPcjb <= USED 0
;FILE IMEI.o
IMEI2ExtendedRead <= USED 0
IMEI2IsDefaultNumber <= USED 0
IMEI2Read <= USED 0
IMEI2ReadOnly <= USED 0
IMEI2ReadStr <= USED 0
IMEIExtendedRead <= USED 0
IMEIIsDefaultNumber <= USED 0
IMEIReadOnly <= USED 0
;FILE InputStream.o
_ZN14streamingmedia11InputStream12prepareAsyncEv <= USED 0
_ZN14streamingmedia11InputStream14initRenderer_lEv <= USED 0
_ZN14streamingmedia11InputStream16postVideoEvent_lEx <= USED 0
_ZN14streamingmedia11InputStream17notifyVideoSize_lEv <= USED 0
_ZN14streamingmedia11InputStream18cancelPlayerEventsEb <= USED 0
_ZN14streamingmedia11InputStream18startAudioPlayer_lEb <= USED 0
_ZN14streamingmedia11InputStream19onPrepareAsyncEventEv <= USED 0
_ZN14streamingmedia11InputStream19postVideoLagEvent_lEv <= USED 0
_ZN14streamingmedia11InputStream19setVideoScalingModeEi <= USED 0
_ZN14streamingmedia11InputStream20postBufferingEvent_lEv <= USED 0
_ZN14streamingmedia11InputStream21postStreamDoneEvent_lEi <= USED 0
_ZN14streamingmedia11InputStream21setVideoScalingMode_lEi <= USED 0
_ZN14streamingmedia11InputStream22shutdownVideoDecoder_lEv <= USED 0
_ZN14streamingmedia11InputStream25postCheckAudioStatusEventEx <= USED 0
_ZN14streamingmedia11InputStream4initEv <= USED 0
_ZN14streamingmedia11InputStream5resetEv <= USED 0
_ZN14streamingmedia11InputStream7pause_lEb <= USED 0
_ZN14streamingmedia19InputStreamRendererC2Ev <= USED 0
_ZNK14streamingmedia11InputStream4dumpEiRKN7android6VectorINS1_8String16EEE <= USED 0
_ZNK14streamingmedia11InputStream9isPlayingEv <= USED 0
;FILE LzmaDec.o
LzmaDec_Allocate <= USED 0
LzmaDec_DecodeToBuf <= USED 0
LzmaDec_Free <= USED 0
;FILE LzmaLib.o
;FILE MP3Decoder.o
_ZThn8_N10MP3DecoderD0Ev <= USED 0
_ZThn8_N10MP3DecoderD1Ev <= USED 0
;FILE MP3Extractor.o
_ZN12MP3Extractor14getID3MetadataEP18AMediaFileMetadata <= USED 0
_ZN12MP3ExtractorC1EPc <= USED 0
_ZThn4_N9MP3SourceD0Ev <= USED 0
_ZThn4_N9MP3SourceD1Ev <= USED 0
;FILE MPEG4Decoder.o
_ZThn8_N12MPEG4DecoderD0Ev <= USED 0
_ZThn8_N12MPEG4DecoderD1Ev <= USED 0
;FILE MPEG4Extractor.o
_ZN14MPEG4ExtractorC1EPc <= USED 0
_ZThn4_N11MPEG4SourceD0Ev <= USED 0
_ZThn4_N11MPEG4SourceD1Ev <= USED 0
;FILE MRD.o
ATMrdRespStr <= USED 0
AtMrdMips <= USED 0
GetMRDBakFlashAddress <= USED 0
Get_VSIM_ICCID <= USED 0
Get_VSIM_IMSI <= USED 0
Get_VSIM_Parameter1 <= USED 0
Get_VSIM_Parameter2 <= USED 0
MRDFileDirFirst <= USED 0
MRDFileDirNext <= USED 0
MRDGetBufferAddr <= USED 0
MRDInit <= USED 0
MRDItemGet <= USED 0
VSIM_str2hex <= USED 0
imei_data_to_rdisk <= USED 0
isMRDOperationAllowed <= USED 0
mrd_read_item_to_rdisk <= USED 0
myhex2str <= USED 0
nvm_data_to_rdisk <= USED 0
rdisk_to_imei_data <= USED 0
version_compare <= USED 0
;FILE MediaAudioStream.o
;FILE MediaBuffer.o
_ZN14streamingmedia11MediaBuffer5claimEv <= USED 0
_ZN14streamingmedia11MediaBuffer5cloneEv <= USED 0
_ZN14streamingmedia11MediaBufferC1EPvj <= USED 0
_ZN14streamingmedia11MediaBufferC1ERKN7android2spINS_7ABufferEEE <= USED 0
;FILE MediaBufferGroupT.o
;FILE MediaBufferPool.o
;FILE MediaDescription.o
;FILE MediaManager.o
_ZN14streamingmedia12MediaManager12getDTMFEventEPjPc <= USED 0
_ZN14streamingmedia12MediaManager14GetAudioDomainEv <= USED 0
_ZN14streamingmedia12MediaManager14GetMediaConfigEv <= USED 0
_ZN14streamingmedia12MediaManager14logMediaconfigE17SDPMedia_Config_t <= USED 0
_ZN14streamingmedia12MediaManager15SetCameraConfigEi14Media_CamCap_t <= USED 0
_ZN14streamingmedia12MediaManager15getFragmentSizeEv <= USED 0
_ZN14streamingmedia12MediaManager16get_G711_OVERLAYEv <= USED 0
_ZN14streamingmedia12MediaManager18getCodecCapabilityEPcP16Media_CodecCap_t <= USED 0
_ZN14streamingmedia12MediaManager19GetCameraCapabilityEiPiR14Media_CamCap_t <= USED 0
_ZN14streamingmedia12MediaManager24GetCameraCapabilityCountEv <= USED 0
_ZN14streamingmedia12MediaManager7GetGainEb <= USED 0
_ZN14streamingmedia12MediaManager7GetMuteEb <= USED 0
_ZN14streamingmedia12MediaManager9getCameraEv <= USED 0
_ZN14streamingmedia12MediaManager9setCameraEP14Media_CamCap_t <= USED 0
;FILE MediaSession.o
_ZN14streamingmedia12MediaSession10getRtpPortEv <= USED 0
_ZN14streamingmedia12MediaSession7getMuteEb <= USED 0
;FILE MediaSource.o
_ZN14streamingmedia11MediaSource11ReadOptions11clearSeekToEv <= USED 0
_ZN14streamingmedia11MediaSource11ReadOptions5resetEv <= USED 0
_ZN14streamingmedia11MediaSource11ReadOptions9setLateByEx <= USED 0
_ZN14streamingmedia11MediaSource11ReadOptions9setSeekToExNS1_8SeekModeE <= USED 0
_ZN14streamingmedia11MediaSource11ReadOptionsC1Ev <= USED 0
_ZNK14streamingmedia11MediaSource11ReadOptions9getLateByEv <= USED 0
_ZNK14streamingmedia11MediaSource11ReadOptions9getSeekToEPxPNS1_8SeekModeE <= USED 0
;FILE MediaSourceT.o
_ZN14streamingmedia12MediaSourceT12readVecBlockERN7android6VectorIPNS_18MediaBufferWrapperEEEx <= USED 0
_ZN14streamingmedia12MediaSourceT9readBlockEPPNS_11MediaBufferEx <= USED 0
_ZN14streamingmedia12MediaSourceT9setConfigEPNS_8MetaDataE <= USED 0
;FILE MediaStream.o
_ZN14streamingmedia11MediaStream11stopReceiveEv <= USED 0
_ZN14streamingmedia11MediaStream12pauseReceiveEv <= USED 0
_ZN14streamingmedia11MediaStream12startReceiveEv <= USED 0
_ZN14streamingmedia11MediaStream13resumeReceiveEv <= USED 0
;FILE MetaData.o
_ZN14streamingmedia8MetaData10setCStringEjPKc <= USED 0
_ZN14streamingmedia8MetaData10setPointerEjPv <= USED 0
_ZN14streamingmedia8MetaData10typed_data11freeStorageEv <= USED 0
_ZN14streamingmedia8MetaData10typed_data15allocateStorageEj <= USED 0
_ZN14streamingmedia8MetaData10typed_data5clearEv <= USED 0
_ZN14streamingmedia8MetaData10typed_dataC1Ev <= USED 0
_ZN14streamingmedia8MetaData10typed_dataD1Ev <= USED 0
_ZN14streamingmedia8MetaData10typed_dataaSERKS1_ <= USED 0
_ZN14streamingmedia8MetaData11findCStringEjPPKc <= USED 0
_ZN14streamingmedia8MetaData6removeEj <= USED 0
_ZN14streamingmedia8MetaData7setRectEjiiii <= USED 0
_ZN14streamingmedia8MetaData8findRectEjPiS1_S1_S1_ <= USED 0
_ZN14streamingmedia8MetaData8setFloatEjf <= USED 0
_ZN14streamingmedia8MetaData8setInt64Ejx <= USED 0
_ZN14streamingmedia8MetaData9findFloatEjPf <= USED 0
_ZN14streamingmedia8MetaData9findInt64EjPx <= USED 0
_ZN14streamingmedia8MetaDataC1ERKS0_ <= USED 0
_ZNK14streamingmedia8MetaData10typed_data7getDataEPjPPKvS2_ <= USED 0
_ZNK14streamingmedia8MetaData10typed_data8asStringEv <= USED 0
_ZNK14streamingmedia8MetaData9dumpToLogEv <= USED 0
;FILE MidiExtractor.o
_ZN10MidiEngine13getDurationMsEv <= USED 0
_ZN10MidiSource4initEv <= USED 0
_ZN13MidiExtractorC1EPc <= USED 0
;FILE MidiIoWrapper.o
_ZN13MidiIoWrapper4sizeEv <= USED 0
;FILE MrvXML.o
AddAttrToXML <= USED 0
AddElementToXML <= USED 0
AddToList <= USED 0
DeleteAttrFromXML <= USED 0
DeleteElementFromXML <= USED 0
DeleteFromList <= USED 0
DeleteFromListTial <= USED 0
FreeList <= USED 0
InitList <= USED 0
MrvAddCData <= USED 0
MrvAttributeAttach <= USED 0
MrvCountElement <= USED 0
MrvCreateElements <= USED 0
MrvCreateXMLString <= USED 0
MrvCreateXMLStringR <= USED 0
MrvEnumPrintf <= USED 0
MrvGetClearTags <= USED 0
MrvGetError <= USED 0
;FILE MrvXMLUtil.o
;FILE NTPTime.o
_ZN14streamingmedia7NTPTime7addmsecEj <= USED 0
_ZN14streamingmedia7NTPTimeltERKS0_ <= USED 0
;FILE NUtick.o
;FILE NetEQWrapper.o
RTPInjection2IMS <= USED 0
ReceiveRTP <= USED 0
_ZN14streamingmedia12NetEQWrapper12getAudioInfoERiS1_S1_ <= USED 0
_ZN14streamingmedia12NetEQWrapper13extractPacketEPs <= USED 0
configInjectRTPType <= USED 0
configInjectRTPTypeRate <= USED 0
startInjectRTP <= USED 0
;FILE NetworkSelectionAL_AT.o
networkSelectionAL_HdlRegisterNotify <= USED 0
networkSelectionAL_HdlUnregisterNotify <= USED 0
;FILE OSA_Audio.o
mmf_a2dp_get_status <= USED 0
mmf_aif_get_opened_path <= USED 0
mmf_aif_pause_play <= USED 0
mmf_aif_pause_record <= USED 0
mmf_aif_resume_play <= USED 0
mmf_aif_resume_record <= USED 0
mmf_bt_device_is_connected <= USED 0
;FILE OSA_UOS.o
mmf_file_delete <= USED 0
mmf_file_flush <= USED 0
mmf_file_open <= USED 0
mmf_get_library_version <= USED 0
mmf_sem_count <= USED 0
mmf_sem_reset <= USED 0
mmf_thread_cancel <= USED 0
mmf_thread_cond_destroy <= USED 0
mmf_thread_cond_init <= USED 0
mmf_thread_cond_signal <= USED 0
mmf_thread_cond_wait <= USED 0
;FILE OffloadAMRWriter.o
_ZN16OffloadAMRWriter13getDurationMsEPj <= USED 0
_ZN16OffloadAMRWriter7getSizeEPj <= USED 0
_ZThn12_N16OffloadAMRWriterD0Ev <= USED 0
_ZThn12_N16OffloadAMRWriterD1Ev <= USED 0
;FILE OffloadAifAudioSource.o
;FILE OffloadWAVWriter.o
_ZN16OffloadWAVWriter13getDurationMsEPj <= USED 0
_ZN16OffloadWAVWriter7getSizeEPj <= USED 0
_ZThn12_N16OffloadWAVWriterD0Ev <= USED 0
_ZThn12_N16OffloadWAVWriterD1Ev <= USED 0
;FILE OutputStream.o
_ZN14streamingmedia12OutputStream17createAudioSourceEv <= USED 0
_ZN14streamingmedia12OutputStream17setupVideoEncoderEN7android2spINS_11MediaSourceEEEiPS4_ <= USED 0
_ZN14streamingmedia12OutputStream19decoderProfileLevelEhhh <= USED 0
_ZN14streamingmedia12OutputStream5resetEv <= USED 0
_ZNK14streamingmedia12OutputStream5mutedEv <= USED 0
;FILE PCA_api.o
ACMVoiceGetMuteIn <= USED 0
ACMVoiceGetMuteOut <= USED 0
AucSwitchAudioOn <= USED 0
AudioPatch_DisablePcmStream <= USED 0
AudioSetCompanderOff <= USED 0
AudioSetCompanderOn <= USED 0
DisableHwPath <= USED 0
DisableHwPath_Envelope <= USED 0
DisablePcmStream_Envelope <= USED 0
DisablePhonePcm <= USED 0
EnableHwPath <= USED 0
EnableHwPath_Envelope <= USED 0
EnablePcmStream_Envelope <= USED 0
EnablePhonePcm <= USED 0
EndVoiceCall <= USED 0
EndVoiceCall_No_PS <= USED 0
GetECallData <= USED 0
GetECallVoice <= USED 0
GetFarSideMute <= USED 0
GetNearSideMute <= USED 0
LteVoiceResume_internal <= USED 0
LteVoiceSuspend_internal <= USED 0
MuteVoicepath <= USED 0
RegisterCallback <= USED 0
SetAudioVoLTE <= USED 0
SetCTMControl <= USED 0
SetDebugCmd <= USED 0
SetECallConnectEnv <= USED 0
SetECallData <= USED 0
SetECallVoice <= USED 0
SetFarSideMute_Envelope <= USED 0
SetGainIn_Envelope <= USED 0
SetIPCControl <= USED 0
SetNearSideMute_Envelope <= USED 0
SetSspConfiguration <= USED 0
SetSspConfiguration_Envelope <= USED 0
SetStreamPriority <= USED 0
SetVoiceEnhanceModuleControl <= USED 0
SetVolumeOut_Envelope <= USED 0
StartVoIPAudio <= USED 0
StartVoiceCall <= USED 0
StartVoiceCall_No_PS <= USED 0
StopVoIPAudio <= USED 0
isStreamInActive <= USED 0
isStreamOutActive <= USED 0
sacNotifyAudioConnected <= USED 0
sendRTP <= USED 0
stopRTPInjection <= USED 0
;FILE Parcel.o
_ZN14streamingmedia14acquire_objectERKN7android2spINS_12ProcessStateEEERKNS_18flat_binder_objectEPKv <= USED 0
_ZN14streamingmedia14flatten_binderERKN7android2spINS_12ProcessStateEEERKNS0_2wpINS_7IBinderEEEPNS_6ParcelE <= USED 0
_ZN14streamingmedia14flatten_binderERKN7android2spINS_12ProcessStateEEERKNS1_INS_7IBinderEEEPNS_6ParcelE <= USED 0
_ZN14streamingmedia14release_objectERKN7android2spINS_12ProcessStateEEERKNS_18flat_binder_objectEPKv <= USED 0
_ZN14streamingmedia16unflatten_binderERKN7android2spINS_12ProcessStateEEERKNS_6ParcelEPNS0_2wpINS_7IBinderEEE <= USED 0
_ZN14streamingmedia16unflatten_binderERKN7android2spINS_12ProcessStateEEERKNS_6ParcelEPNS1_INS_7IBinderEEE <= USED 0
_ZN14streamingmedia6Parcel10appendFromEPKS0_jj <= USED 0
_ZN14streamingmedia6Parcel10writeFloatEf <= USED 0
_ZN14streamingmedia6Parcel10writeInt32Ei <= USED 0
_ZN14streamingmedia6Parcel10writeInt64Ex <= USED 0
_ZN14streamingmedia6Parcel11finishWriteEj <= USED 0
_ZN14streamingmedia6Parcel11setDataSizeEj <= USED 0
_ZN14streamingmedia6Parcel11writeDoubleEd <= USED 0
_ZN14streamingmedia6Parcel11writeIntPtrEi <= USED 0
_ZN14streamingmedia6Parcel11writeObjectERKNS_18flat_binder_objectEb <= USED 0
_ZN14streamingmedia6Parcel12pushAllowFdsEb <= USED 0
_ZN14streamingmedia6Parcel12restartWriteEj <= USED 0
_ZN14streamingmedia6Parcel12writeCStringEPKc <= USED 0
_ZN14streamingmedia6Parcel12writeInplaceEj <= USED 0
_ZN14streamingmedia6Parcel12writeString8ERKN7android7String8E <= USED 0
_ZN14streamingmedia6Parcel13continueWriteEj <= USED 0
_ZN14streamingmedia6Parcel13writeString16EPKtj <= USED 0
_ZN14streamingmedia6Parcel13writeString16ERKN7android8String16E <= USED 0
_ZN14streamingmedia6Parcel13writeUnpaddedEPKvj <= USED 0
_ZN14streamingmedia6Parcel14acquireObjectsEv <= USED 0
_ZN14streamingmedia6Parcel14releaseObjectsEv <= USED 0
_ZN14streamingmedia6Parcel15restoreAllowFdsEb <= USED 0
_ZN14streamingmedia6Parcel15setDataCapacityEj <= USED 0
_ZN14streamingmedia6Parcel15writeWeakBinderERKN7android2wpINS_7IBinderEEE <= USED 0
_ZN14streamingmedia6Parcel16writeNoExceptionEv <= USED 0
_ZN14streamingmedia6Parcel17writeStrongBinderERKN7android2spINS_7IBinderEEE <= USED 0
_ZN14streamingmedia6Parcel19ipcSetDataReferenceEPKhjPKjjPFvPS0_S2_jS4_jPvES6_ <= USED 0
_ZN14streamingmedia6Parcel19writeFileDescriptorEib <= USED 0
_ZN14streamingmedia6Parcel19writeInterfaceTokenERKN7android8String16E <= USED 0
_ZN14streamingmedia6Parcel20closeFileDescriptorsEv <= USED 0
_ZN14streamingmedia6Parcel22writeDupFileDescriptorEi <= USED 0
_ZN14streamingmedia6Parcel5writeEPKvj <= USED 0
_ZN14streamingmedia6Parcel5writeERKN7android11FlattenableE <= USED 0
_ZN14streamingmedia6Parcel6removeEjj <= USED 0
_ZN14streamingmedia6Parcel7setDataEPKhj <= USED 0
_ZN14streamingmedia6Parcel8freeDataEv <= USED 0
_ZN14streamingmedia6Parcel8growDataEj <= USED 0
_ZN14streamingmedia6Parcel8setErrorEi <= USED 0
_ZNK14streamingmedia6Parcel10errorCheckEv <= USED 0
_ZNK14streamingmedia6Parcel10ipcObjectsEv <= USED 0
_ZNK14streamingmedia6Parcel10readDoubleEPd <= USED 0
_ZNK14streamingmedia6Parcel10readDoubleEv <= USED 0
_ZNK14streamingmedia6Parcel10readIntPtrEPi <= USED 0
_ZNK14streamingmedia6Parcel10readIntPtrEv <= USED 0
_ZNK14streamingmedia6Parcel10readObjectEb <= USED 0
_ZNK14streamingmedia6Parcel10scanForFdsEv <= USED 0
_ZNK14streamingmedia6Parcel11ipcDataSizeEv <= USED 0
_ZNK14streamingmedia6Parcel11readCStringEv <= USED 0
_ZNK14streamingmedia6Parcel11readInplaceEj <= USED 0
_ZNK14streamingmedia6Parcel11readString8Ev <= USED 0
_ZNK14streamingmedia6Parcel12dataCapacityEv <= USED 0
_ZNK14streamingmedia6Parcel12dataPositionEv <= USED 0
_ZNK14streamingmedia6Parcel12objectsCountEv <= USED 0
_ZNK14streamingmedia6Parcel12readString16Ev <= USED 0
_ZNK14streamingmedia6Parcel14checkInterfaceEPNS_7IBinderE <= USED 0
_ZNK14streamingmedia6Parcel14readWeakBinderEv <= USED 0
_ZNK14streamingmedia6Parcel15ipcObjectsCountEv <= USED 0
_ZNK14streamingmedia6Parcel15setDataPositionEj <= USED 0
_ZNK14streamingmedia6Parcel16enforceInterfaceERKN7android8String16EPNS_14IPCThreadStateE <= USED 0
_ZNK14streamingmedia6Parcel16readStrongBinderEv <= USED 0
_ZNK14streamingmedia6Parcel17readExceptionCodeEv <= USED 0
_ZNK14streamingmedia6Parcel18hasFileDescriptorsEv <= USED 0
_ZNK14streamingmedia6Parcel18readFileDescriptorEv <= USED 0
_ZNK14streamingmedia6Parcel19readString16InplaceEPj <= USED 0
_ZNK14streamingmedia6Parcel4dataEv <= USED 0
_ZNK14streamingmedia6Parcel4readERN7android11FlattenableE <= USED 0
_ZNK14streamingmedia6Parcel7ipcDataEv <= USED 0
_ZNK14streamingmedia6Parcel7objectsEv <= USED 0
_ZNK14streamingmedia6Parcel8dataSizeEv <= USED 0
_ZNK14streamingmedia6Parcel9dataAvailEv <= USED 0
_ZNK14streamingmedia6Parcel9readFloatEPf <= USED 0
_ZNK14streamingmedia6Parcel9readFloatEv <= USED 0
_ZNK14streamingmedia6Parcel9readInt32EPi <= USED 0
_ZNK14streamingmedia6Parcel9readInt64EPx <= USED 0
_ZNK14streamingmedia6Parcel9readInt64Ev <= USED 0
;FILE PayloadEncap.o
;FILE PicoIms3gppImsXmlInterpreter.o
;FILE PicoImsInit.o
IMS_ClearPreCondition <= USED 0
IMS_ClearProxy <= USED 0
IMS_DeleteUser <= USED 0
IMS_GetDefaultUser <= USED 0
IMS_GetNumPublicUser <= USED 0
IMS_SMIPSendAbortSMMA <= USED 0
IMS_StartMWI <= USED 0
IMS_StopMWI <= USED 0
IMS_SuspendNwOperation <= USED 0
PICO_IMS_BuildDateGet <= USED 0
PICO_IMS_BuildMachineGet <= USED 0
PICO_IMS_BuildTimeGet <= USED 0
PICO_IMS_BuildUserGet <= USED 0
PICO_IMS_BuildVersionGet <= USED 0
;FILE PicoImsMwi.o
;FILE PicoImsReg.o
;FILE RDisk.o
Rdisk_addto_filelist <= USED 0
;FILE RTC_stub.o
RTCAlarmSet <= USED 0
RTCIsAlarmSet <= USED 0
RTCNotifyOnTimeSetBind <= USED 0
RTCNotifyOnTimeSetEvent <= USED 0
RTCNotifyOnTimeSetUnBind <= USED 0
RTCNotifyUsers <= USED 0
RTCPhase1Init <= USED 0
RTCPhase2Init <= USED 0
RTCPowerUpAlarmCheck <= USED 0
RTCToANSIConvert <= USED 0
SCS_IsAlarmEnabled <= USED 0
TimeGetTest <= USED 0
;FILE RTPAssembler.o
;FILE RXN_API.o
RXN_Get_API_Version <= USED 0
getMaxNumPrns <= USED 0
isValidPRN <= USED 0
;FILE RXN_MSL.o
RXN_MSL_GetApiVersion <= USED 0
;FILE RXN_MSL_AbstractCalls.o
RXN_Mem_Alloc <= USED 0
;FILE RXN_MSL_Common.o
;FILE RXN_MSL_Log.o
;FILE RXN_MSL_Platform.o
;FILE RXN_MSL_Rtos.o
MSL_CleanDir <= USED 0
MSL_CommandReceived <= USED 0
MSL_CreateDir <= USED 0
MSL_Sleep <= USED 0
;FILE RXN_MSL_Time.o
MSL_ConvertBeidouTimeToGPSTime <= USED 0
MSL_ConvertGPSTimeToBeidouTime <= USED 0
MSL_ConvertGPSTimeToGalileoTime <= USED 0
MSL_ConvertGPSTimeToIrnssTime <= USED 0
MSL_ConvertGPSTimeToUTC <= USED 0
MSL_ConvertGalileoTimeToGPSTime <= USED 0
MSL_ConvertGlonassTimeToGPSTime <= USED 0
MSL_ConvertIrnssTimeToGPSTime <= USED 0
MSL_ConvertNativeTimeToGpsTime <= USED 0
MSL_GetGPSLeapSec <= USED 0
MSL_GetTimeUncertThresh <= USED 0
MSL_SetTimeUncertThresh <= USED 0
MSL_Time_Reset <= USED 0
;FILE RXN_bits.o
RXN_Extract8Byte <= USED 0
RXN_Set_Bit <= USED 0
RXN_Set_S64_Bits <= USED 0
RXN_Set_U64_Bits <= USED 0
;FILE RdGenData.o
RDGenGetProdMode <= USED 0
RDGenGetUniqueId <= USED 0
;FILE RefBase.o
_ZN7android7RefBase12weakref_type14attemptIncWeakEPKv <= USED 0
_ZN7android7RefBase12weakref_type7trackMeEbb <= USED 0
_ZN7android7RefBase20extendObjectLifetimeEi <= USED 0
_ZNK7android7RefBase11getWeakRefsEv <= USED 0
_ZNK7android7RefBase12weakref_type12getWeakCountEv <= USED 0
_ZNK7android7RefBase12weakref_type7refBaseEv <= USED 0
_ZNK7android7RefBase12weakref_type9printRefsEv <= USED 0
_ZNK7android7RefBase14forceIncStrongEPKv <= USED 0
_ZNK7android7RefBase14getStrongCountEv <= USED 0
;FILE ReliableData.o
RDTest <= USED 0
ReliableDataBakCheck <= USED 0
ReliableDataCheck <= USED 0
ReliableDataPhase1 <= USED 0
ReliableDataVerify <= USED 0
UpdateIMEI <= USED 0
UpdateIMEI2 <= USED 0
getSerialNumber <= USED 0
mrd_nvm_unpack <= USED 0
rd_linkstatus_ind <= USED 0
rf_calibration_status <= USED 0
strStartsWith <= USED 0
;FILE RtcpByePacket.o
;FILE RtcpCompoundPacket.o
;FILE RtcpRRPacket.o
_ZNK14streamingmedia12RtcpRRPacket10GotoReportEj <= USED 0
_ZNK14streamingmedia12RtcpRRPacket13GetSenderSSRCEv <= USED 0
_ZNK14streamingmedia12RtcpRRPacket15GetFractionLostEj <= USED 0
_ZNK14streamingmedia12RtcpRRPacket18GetLostPacketCountEj <= USED 0
_ZNK14streamingmedia12RtcpRRPacket32GetExtendedHighestSequenceNumberEj <= USED 0
_ZNK14streamingmedia12RtcpRRPacket7GetSSRCEj <= USED 0
_ZNK14streamingmedia12RtcpRRPacket9GetJitterEj <= USED 0
;FILE RtcpSDESPacket.o
_ZN14streamingmedia14RtcpSDESPacket11GetItemDataEv <= USED 0
_ZN14streamingmedia14RtcpSDESPacket12GotoNextItemEv <= USED 0
_ZN14streamingmedia14RtcpSDESPacket13GotoFirstItemEv <= USED 0
_ZN14streamingmedia14RtcpSDESPacket13GotoNextChunkEv <= USED 0
_ZN14streamingmedia14RtcpSDESPacket14GotoFirstChunkEv <= USED 0
_ZNK14streamingmedia14RtcpSDESPacket11GetItemTypeEv <= USED 0
_ZNK14streamingmedia14RtcpSDESPacket12GetChunkSSRCEv <= USED 0
_ZNK14streamingmedia14RtcpSDESPacket13GetChunkCountEv <= USED 0
_ZNK14streamingmedia14RtcpSDESPacket13GetItemLengthEv <= USED 0
;FILE RtcpSRPacket.o
_ZNK14streamingmedia12RtcpSRPacket13GetSenderSSRCEv <= USED 0
_ZNK14streamingmedia12RtcpSRPacket15GetFractionLostEj <= USED 0
_ZNK14streamingmedia12RtcpSRPacket18GetLostPacketCountEj <= USED 0
_ZNK14streamingmedia12RtcpSRPacket19GetSenderOctetCountEv <= USED 0
_ZNK14streamingmedia12RtcpSRPacket20GetSenderPacketCountEv <= USED 0
_ZNK14streamingmedia12RtcpSRPacket7GetSSRCEj <= USED 0
;FILE RtcpSession.o
_ZN14streamingmedia11RtcpSession10processBYEEN7android2spINS_10RtcpPacketEEE <= USED 0
_ZN14streamingmedia11RtcpSession11processSDESEN7android2spINS_10RtcpPacketEEE <= USED 0
_ZN14streamingmedia11RtcpSession15getRtcpTimeInfoERxRi <= USED 0
_ZN14streamingmedia11RtcpSession16onByeRequestSendERKN7android2spINS_8AMessageEEE <= USED 0
_ZN14streamingmedia11RtcpSession7sendByeEv <= USED 0
_ZN14streamingmedia11RtcpSession8sendAVPFERKN7android2spINS_8AMessageEEE <= USED 0
;FILE RtpSession.o
_ZN14streamingmedia10RtpSession10getRtpPortEv <= USED 0
_ZN14streamingmedia13StreamAdapter12getDirectionEv <= USED 0
_ZN14streamingmedia13StreamAdapter14onSetDirectionEv <= USED 0
_ZN14streamingmedia13StreamAdapter15getRtcpTimeInfoERxRi <= USED 0
_ZN14streamingmedia13StreamAdapter21setNATKeepAliveSchemeEb26MEDIA_RTPKeepaliveScheme_t <= USED 0
_ZN14streamingmedia13StreamAdapter9parseRTCPEPNS_18MediaBufferWrapperE <= USED 0
_ZN14streamingmedia18RecRtpSocketLooperC1Ev <= USED 0
;FILE RtpSource.o
_ZN14streamingmedia9RtpSource5pauseEv <= USED 0
;FILE RtpWriter.o
_ZN14streamingmedia9RtpWriter12addRTPHeaderEPNS_18MediaBufferWrapperE <= USED 0
_ZN14streamingmedia9RtpWriter13resetSourceIdEj <= USED 0
;FILE SMIP.o
IMS_RLCb <= USED 0
IMS_RLToLLCb <= USED 0
ims_HdlIsAdminSms <= USED 0
;FILE SMIP2.o
ims_HdlSMIP2SendAck <= USED 0
;FILE SMRL.o
SMRL_GetConfig <= USED 0
;FILE SampleIterator.o
;FILE SampleTable.o
_ZNK11SampleTable17countChunkOffsetsEv <= USED 0
;FILE SessionDescription.o
_ZN14streamingmedia18SessionDescription3logEPKc <= USED 0
;FILE SharedBuffer.o
_ZNK7android12SharedBuffer4editEv <= USED 0
_ZNK7android12SharedBuffer5resetEj <= USED 0
;FILE SimCard.o
;FILE SimCardAL.o
SimCardAL_Apps_Get <= USED 0
SimCardAL_IMS_HomeDN_Get <= USED 0
SimCardAL_SoftSim_Reset <= USED 0
SimCardAL_SoftSim_Set <= USED 0
SimCardAL_StatusCbRegister <= USED 0
SimCardAL_StatusCbUnRegister <= USED 0
SimCardAL_WLAN_PLMN_Get <= USED 0
SimCardAL_WLAN_SID_Get <= USED 0
SimIntf_Release <= USED 0
;FILE SimCardUtil.o
ascii_dump <= USED 0
;FILE SimFiles.o
;FILE SimSmsStorage.o
SimCardAL_SMIP_GetMWIStatus <= USED 0
SimCardAL_SMIP_GetPreferredMemoryForStorage <= USED 0
SimCardAL_SMIP_SetMWIStatus <= USED 0
;FILE StreamingCacheSource.o
_ZN15StreamingManage12colletHeaderEPhj <= USED 0
_ZN15StreamingManage13getHeaderSizeEv <= USED 0
_ZN15StreamingManage13stopStreamingEv <= USED 0
_ZN15StreamingManage6readAtEjPvj <= USED 0
_ZN15StreamingManageD1Ev <= USED 0
_ZN20StreamingCacheSource6readAtEjPvj <= USED 0
_ZN20StreamingCacheSource7getSizeEv <= USED 0
_ZN20StreamingCacheSourceC1EPhjPFijS0_jEj <= USED 0
_ZN20StreamingCacheSourceD0Ev <= USED 0
_ZN20StreamingCacheSourceD1Ev <= USED 0
_ZNK20StreamingCacheSource9initCheckEv <= USED 0
;FILE StreamingMediaCodecFactory.o
;FILE String16.o
_ZN7android18terminate_string16Ev <= USED 0
_ZN7android8String1610replaceAllEtt <= USED 0
_ZN7android8String165setToEPKt <= USED 0
_ZN7android8String165setToEPKtj <= USED 0
_ZN7android8String165setToERKS0_ <= USED 0
_ZN7android8String165setToERKS0_jj <= USED 0
_ZN7android8String166appendEPKtj <= USED 0
_ZN7android8String166appendERKS0_ <= USED 0
_ZN7android8String166insertEjPKt <= USED 0
_ZN7android8String166insertEjPKtj <= USED 0
_ZN7android8String166removeEjj <= USED 0
_ZN7android8String169makeLowerEv <= USED 0
_ZN7android8String16C1EPKc <= USED 0
_ZN7android8String16C1EPKcj <= USED 0
_ZN7android8String16C1EPKt <= USED 0
_ZN7android8String16C1EPKtj <= USED 0
_ZN7android8String16C1ERKNS_7String8E <= USED 0
_ZN7android8String16C1ERKS0_ <= USED 0
_ZN7android8String16C1ERKS0_jj <= USED 0
_ZN7android8String16C1Ev <= USED 0
_ZN7android8String16D1Ev <= USED 0
_ZNK7android8String1610startsWithEPKt <= USED 0
_ZNK7android8String1610startsWithERKS0_ <= USED 0
_ZNK7android8String168findLastEt <= USED 0
_ZNK7android8String169findFirstEt <= USED 0
;FILE String8.o
_ZN7android17terminate_string8Ev <= USED 0
_ZN7android7String810appendPathEPKc <= USED 0
_ZN7android7String810lockBufferEj <= USED 0
_ZN7android7String811setPathNameEPKc <= USED 0
_ZN7android7String811setPathNameEPKcj <= USED 0
_ZN7android7String812appendFormatEPKcz <= USED 0
_ZN7android7String812unlockBufferEj <= USED 0
_ZN7android7String812unlockBufferEv <= USED 0
_ZN7android7String813appendFormatVEPKcSt9__va_list <= USED 0
_ZN7android7String816convertToResPathEv <= USED 0
_ZN7android7String85setToEPKcj <= USED 0
_ZN7android7String85setToEPKjj <= USED 0
_ZN7android7String85setToEPKtj <= USED 0
_ZN7android7String86appendERKS0_ <= USED 0
_ZN7android7String86formatEPKcz <= USED 0
_ZN7android7String87formatVEPKcSt9__va_list <= USED 0
_ZN7android7String87toLowerEjj <= USED 0
_ZN7android7String87toLowerEv <= USED 0
_ZN7android7String87toUpperEjj <= USED 0
_ZN7android7String87toUpperEv <= USED 0
_ZN7android7String8C1EPKcj <= USED 0
_ZN7android7String8C1EPKj <= USED 0
_ZN7android7String8C1EPKjj <= USED 0
_ZN7android7String8C1EPKt <= USED 0
_ZN7android7String8C1EPKtj <= USED 0
_ZN7android7String8C1ERKNS_8String16E <= USED 0
_ZNK7android7String810getPathDirEv <= USED 0
_ZNK7android7String810getUtf32AtEjPj <= USED 0
_ZNK7android7String811getBasePathEv <= USED 0
_ZNK7android7String811getPathLeafEv <= USED 0
_ZNK7android7String814find_extensionEv <= USED 0
_ZNK7android7String814getUtf32LengthEv <= USED 0
_ZNK7android7String816getPathExtensionEv <= USED 0
_ZNK7android7String88getUtf32EPj <= USED 0
_ZNK7android7String88walkPathEPS0_ <= USED 0
;FILE SysDynSilicon.o
SysUSIMWakeupIntResetEna <= USED 0
Sys_ACIPCIntEna <= USED 0
Sys_AplpRFD_GetRxTxDelay <= USED 0
Sys_CSSR_WDT_KICK_MODE_BIT_SetIsEnable <= USED 0
Sys_CommpmMemConfRequired <= USED 0
Sys_CommpmUsimNmosEngageInUse <= USED 0
Sys_CrdGBdump <= USED 0
Sys_CrdGBdump_L1DataABuffSize <= USED 0
Sys_IsDataOMSLOverACIPC <= USED 0
Sys_IsProductIDTavorLlike <= USED 0
Sys_IsWorkingWith3dBOffset <= USED 0
Sys_PowerModificationsForPVEnable <= USED 0
Sys_TavorSiliconIs_PV_B0orUpper <= USED 0
Sys_getDSSPMode <= USED 0
Sys_intcIsThirdRegisterExist <= USED 0
Sys_isCpuEshel <= USED 0
Sys_mslGetCpuID <= USED 0
Sys_usbVersion <= USED 0
Sys_usimDetectGpioPinNo <= USED 0
Sys_usimUseDma <= USED 0
Sys_wdtKickRegAddress <= USED 0
;FILE TSXController.o
AdcOutVerify <= USED 0
ContructAugumentedMatrix <= USED 0
DacOutVerify <= USED 0
GaussElimination <= USED 0
TSXCtrlDisable <= USED 0
TSXCtrlPolyFitting <= USED 0
TSXCtrlPolyShell <= USED 0
TSXCtrlReadNvm <= USED 0
TSXCtrlSendFreqOffsetInfo <= USED 0
TSXCtrlStateMachine <= USED 0
TSXCtrlWriteNvm <= USED 0
TSXCtrl_WriteNVMFile <= USED 0
TSXTCCtrlCreateNVMFileTask <= USED 0
TSXTCCtrl_CreateNVMFile <= USED 0
TSXTCTestCmd <= USED 0
TSX_Verification <= USED 0
TsxCtrlLimitFRange <= USED 0
TsxCtrlMsgIpcHandler <= USED 0
TsxCtrlRegCompare <= USED 0
TsxCtrlRegVerify <= USED 0
TsxCtrlTCScore <= USED 0
TsxRegUpdate <= USED 0
TsxTCTask <= USED 0
TsxTcTaskHandler <= USED 0
TsxVerifyTaskCreate <= USED 0
;FILE Threads.o
_Z24androidCreateThreadGetIDPFvPvES_PS_ <= USED 0
_ZN7android6Thread11_threadLoopEPv <= USED 0
_ZN7android6ThreadC1Eb <= USED 0
_ZNK7android6Thread11exitPendingEv <= USED 0
androidCreateThread <= USED 0
androidCreateThreadEtc <= USED 0
androidGetTid <= USED 0
androidSetCreateThreadFunc <= USED 0
;FILE TimedEventQueue.o
_ZN14streamingmedia15TimedEventQueue11cancelEventEi <= USED 0
_ZN14streamingmedia15TimedEventQueue12cancelEventsEPFbPvRKN7android2spINS0_5EventEEEES1_b <= USED 0
_ZN14streamingmedia15TimedEventQueue13ThreadWrapperEPv <= USED 0
_ZN14streamingmedia15TimedEventQueue15postEventToBackERKN7android2spINS0_5EventEEE <= USED 0
_ZN14streamingmedia15TimedEventQueue18postEventWithDelayERKN7android2spINS0_5EventEEEx <= USED 0
_ZN14streamingmedia15TimedEventQueue5startEv <= USED 0
_ZN14streamingmedia15TimedEventQueue9postEventERKN7android2spINS0_5EventEEE <= USED 0
;FILE UART_atcmd.o
uart_atcmd_get_cfg <= USED 0
uart_atcmd_init <= USED 0
uart_diag_init <= USED 0
;FILE UART_drv.o
uartdrv_clear_receive_fifo <= USED 0
uartdrv_enable_int <= USED 0
uartdrv_flush <= USED 0
uartdrv_get_IntStatus <= USED 0
uartdrv_get_base_addr <= USED 0
uartdrv_get_baudrate <= USED 0
uartdrv_getc <= USED 0
uartdrv_getchar_noblock <= USED 0
uartdrv_is_RxInt <= USED 0
uartdrv_isr <= USED 0
uartdrv_rts_is_enable <= USED 0
uartdrv_rx_dma_enable <= USED 0
uartdrv_set_baudrate <= USED 0
;FILE UART_utils.o
PMIC_Set_CP_UART_PowerOff_and_AIB_3_3V <= USED 0
PMIC_Set_UART_1_8V <= USED 0
PMIC_Set_UART_3_3V <= USED 0
PMIC_Set_UART_Poweroff <= USED 0
PMIC_Set_UART_Poweron <= USED 0
UARTCharacterWrite <= USED 0
UARTFIFORead <= USED 0
UARTFIFOWrite <= USED 0
UARTISRBind <= USED 0
UARTISRUnbind <= USED 0
UARTInterruptControl <= USED 0
UARTRxWakeLISR <= USED 0
UartLogPrintf <= USED 0
UartLogPrintf_UI <= USED 0
send_data_2uart <= USED 0
set_at_diag_mode <= USED 0
uartSleepKick <= USED 0
uartrxwakeinit <= USED 0
;FILE USSI.o
USSI_MsgHandler <= USED 0
ims_HdlCancelUSSI <= USED 0
ims_HdlInitUSSI <= USED 0
ims_HdlSendUSSI <= USED 0
ims_HdlShutUSSI <= USED 0
;FILE Unicode.o
strcmp16 <= USED 0
strcpy16 <= USED 0
strlen16 <= USED 0
strlen32 <= USED 0
strncmp16 <= USED 0
strncpy16 <= USED 0
strnlen16 <= USED 0
strnlen32 <= USED 0
strzcmp16 <= USED 0
strzcmp16_h_n <= USED 0
utf16_to_utf8 <= USED 0
utf16_to_utf8_length <= USED 0
utf32_from_utf8_at <= USED 0
utf32_to_utf8 <= USED 0
utf32_to_utf8_length <= USED 0
utf8_length <= USED 0
utf8_to_utf16 <= USED 0
utf8_to_utf16_length <= USED 0
utf8_to_utf16_no_null_terminator <= USED 0
utf8_to_utf32 <= USED 0
utf8_to_utf32_length <= USED 0
;FILE UsbMgr.o
M_KiOsGetSignalInt <= USED 0
USBMgrDevicePlugIn <= USED 0
USBMgrDeviceUnplug <= USED 0
USBMgrEndpointStall <= USED 0
USBMgrFlushRxBuffer <= USED 0
USBMgrFlushTxQueue <= USED 0
USBMgrGetHWCfgEPMaxPacketSize <= USED 0
USBMgrRegister <= USED 0
USBMgrRxConfirmation <= USED 0
USBMgrRxConfirmationExt <= USED 0
USBMgrSetupCommandRsp <= USED 0
USBMgrTempFuncUpdateHardCodedUSBDescriptor <= USED 0
USBMgrTransmit <= USED 0
UsbMgrGetIfIndexByAppId <= USED 0
;FILE Utils.o
;FILE VBRISeeker.o
;FILE VectorImpl.o
_ZN7android10VectorImpl11appendArrayEPKvj <= USED 0
_ZN7android10VectorImpl11setCapacityEj <= USED 0
_ZN7android10VectorImpl12appendVectorERKS0_ <= USED 0
_ZN7android10VectorImpl13insertArrayAtEPKvjj <= USED 0
_ZN7android10VectorImpl14insertVectorAtERKS0_j <= USED 0
_ZN7android10VectorImpl3addEv <= USED 0
_ZN7android10VectorImpl3popEv <= USED 0
_ZN7android10VectorImpl4pushEv <= USED 0
_ZN7android10VectorImpl4sortEPFiPKvS2_E <= USED 0
_ZN7android10VectorImpl4sortEPFiPKvS2_PvES3_ <= USED 0
_ZN7android10VectorImpl8insertAtEjj <= USED 0
_ZN7android10VectorImpl9replaceAtEj <= USED 0
_ZN7android16SortedVectorImpl5mergeERKNS_10VectorImplE <= USED 0
_ZN7android16SortedVectorImpl5mergeERKS0_ <= USED 0
_ZN7android16SortedVectorImplC2ERKNS_10VectorImplE <= USED 0
_ZN7android16SortedVectorImplaSERKS0_ <= USED 0
_ZNK7android10VectorImpl8itemSizeEv <= USED 0
_ZNK7android16SortedVectorImpl7orderOfEPKv <= USED 0
;FILE Vmbuf.o
vdup <= USED 0
vputint <= USED 0
vrealloc <= USED 0
vtrim <= USED 0
;FILE WAVExtractor.o
_ZN12WAVExtractorC1EPc <= USED 0
_ZThn4_N9WAVSourceD0Ev <= USED 0
_ZThn4_N9WAVSourceD1Ev <= USED 0
;FILE WS_CmdMsg.o
IPCCommMessageRead <= USED 0
IPCCommReplyRequiredCommandSend <= USED 0
IPCCommReplyRequiredNewOpcodeCommandSend <= USED 0
cmdMsgReturnOSObjects <= USED 0
handleReplyRequiredRdIntToDSP <= USED 0
ipcDispatchMsg <= USED 0
;FILE WS_CmnSrv.o
APLP_AAAPDataBufferFreeNotification <= USED 0
APLP_AAAPDataChannelFreeNotification <= USED 0
APLP_AAAPDataReceivedNotification <= USED 0
APLP_AAAPGetDataPointer <= USED 0
APLP_AAAPModeRegistration <= USED 0
BsramMemoryDumpRoutine <= USED 0
IPCCommClose <= USED 0
IPCCommConfig <= USED 0
IPCCommGetConfigure <= USED 0
IPCCommGetVersion <= USED 0
IPCCommMemoryDumpRoutine <= USED 0
IPCCommPhase1Init <= USED 0
IPCCommSpyCmd <= USED 0
IPCCommStatus <= USED 0
IPCCommUnSpyCmd <= USED 0
cmnSrvCheckAppIDValidity <= USED 0
cmnSrvReturnOSObjects <= USED 0
cmnSrvSpyCmdBroadcastingRoutine <= USED 0
plDratApplicationIdReturn <= USED 0
;FILE WS_Data.o
IPCCommSendSelfEvent <= USED 0
IPCErrorIndicationCallBackBind <= USED 0
dataDebugFreeBufferAndChannel <= USED 0
dataDebugSimulateDspData <= USED 0
dataReturnOSObjects <= USED 0
;FILE WS_HwAcs.o
hwAcsConfigureInterrupts <= USED 0
hwAcsDisableInterrupts <= USED 0
reset_MSA_fix_silent_reset <= USED 0
;FILE WS_IPCICATFunc.o
AAAPDataBufferFreeNotification <= USED 0
AAAPDataChannelFreeNotification <= USED 0
AAAPDataReceivedNotification <= USED 0
AAAPGetDataPointer <= USED 0
getFilterArrayPointer <= USED 0
ipcMsaRestart <= USED 0
setNoPlpMode <= USED 0
setPlpCommandFilters <= USED 0
setPlpMessageFilters <= USED 0
setPlpMode <= USED 0
simulateDspDataMsg <= USED 0
simulatePlpDataAck <= USED 0
;FILE WS_TcmSrv.o
IPCCreateEvent <= USED 0
;FILE XINGSeeker.o
;FILE Xmlussdformatter.o
;FILE a2dp.o
a2dp_config_process_set_atrac <= USED 0
a2dp_config_process_set_mpeg_aac <= USED 0
a2dp_config_process_set_mpeg_audio <= USED 0
a2dp_config_process_set_other <= USED 0
a2dp_subevent_id_for_avdtp_subevent_id <= USED 0
;FILE a2dp_sink.o
a2dp_sink_register_media_config_validator <= USED 0
a2dp_sink_set_config_atrac <= USED 0
a2dp_sink_set_config_mpeg_aac <= USED 0
a2dp_sink_set_config_mpeg_audio <= USED 0
a2dp_sink_set_config_other <= USED 0
a2dp_sink_set_config_sbc <= USED 0
;FILE a2dp_source.o
a2dp_max_media_payload_size <= USED 0
a2dp_source_finalize_stream_endpoint <= USED 0
a2dp_source_reconfigure_stream_sampling_frequency <= USED 0
a2dp_source_register_media_config_validator <= USED 0
a2dp_source_set_config_atrac <= USED 0
a2dp_source_set_config_mpeg_aac <= USED 0
a2dp_source_set_config_mpeg_audio <= USED 0
a2dp_source_set_config_other <= USED 0
a2dp_source_set_config_sbc <= USED 0
a2dp_source_stream_send_media_packet <= USED 0
a2dp_source_stream_send_media_payload <= USED 0
a2dp_source_stream_send_media_payload_rtp <= USED 0
;FILE a_refl.o
;FILE aam.o
AAMAcquireHandle <= USED 0
AAMAcquireHandleForLptBeforeIntDis <= USED 0
AAMBeforeIntDisRegister <= USED 0
AAMPhase1Init <= USED 0
AAMPhase2Init <= USED 0
AAM_GET_BIT_MASK_BY_HANDLE <= USED 0
GetRegisteredApps <= USED 0
mmiAAMInit <= USED 0
mmiAAMRegister <= USED 0
mmiAAMRequest <= USED 0
mmiAAMUnregister <= USED 0
;FILE abcfg.o
;FILE abem_fnc.o
;FILE abgl_fnc.o
abglSetTimer <= USED 0
printContextInfo <= USED 0
;FILE abgp_fnc.o
abgpDoApexDataSwtichReq <= USED 0
abgpStringCmpI <= USED 0
abmmRmUserModeIncludeLteMode <= USED 0
;FILE ablm_ufn.o
;FILE ablm_util.o
;FILE abmm_blm.o
abmmBlGetLteArfcnListsFromCuList <= USED 0
abmmBlIsNotPresentLteEuarfcnList <= USED 0
abmmBlUpdateLteArfcnListsFromCuList <= USED 0
abmmBlmClearAnayArfcnNvm <= USED 0
abmmBlmGetArfcnFromNvm <= USED 0
abmmBlmInsertArfcnNvm <= USED 0
abmmBlmUpdateAllArfcnNvm <= USED 0
abmmLteListClearLists <= USED 0
abmmRmGetModemInfo <= USED 0
;FILE abmm_bmm.o
abmmBmNewFddLteBandMode <= USED 0
abmmBmNewTdLteBandMode <= USED 0
abmmBmSetFrequencyCapability <= USED 0
abmmBmSetGPRSServiceType <= USED 0
;FILE abmm_cdm.o
WriteInitData2Nvm <= USED 0
abmmBmSetFddBandFromMedataInit <= USED 0
abmmCdGetImei <= USED 0
abmmDoMmrUpdateNvmInd <= USED 0
;FILE abmm_plm.o
abmmPlAddToTempFplmnList <= USED 0
abmmPlCopyAvailableLtePlmns <= USED 0
abmmPlDeleteFromPlmnListWithAct <= USED 0
abmmPlDestroyTempForbiddenList <= USED 0
abmmPlGprsServicesPresent <= USED 0
abmmPlIsLastPlmn <= USED 0
abmmPlIsPowerOn <= USED 0
abmmPlNumPrefPlmns <= USED 0
;FILE abmm_pmm.o
abmmPmResuming <= USED 0
abmmPmUpdateAllNvmData <= USED 0
;FILE abmm_rmm.o
abmmDoMmrAbortRegCnf <= USED 0
abmmDoMmrAsRregInd <= USED 0
abmmPlIsRoamingForbidenPlmn <= USED 0
abmmRmAreAllLteOnlyHplmnsInList <= USED 0
abmmRmCheckInvalidImei <= USED 0
abmmRmContinue <= USED 0
abmmRmGetCurrentPlmn <= USED 0
abmmRmGetManualPlmn <= USED 0
abmmRmHplmnIsBoltSmartfrenOrnSim <= USED 0
abmmRmHplmnIsCMHKSim <= USED 0
abmmRmHplmnIsPlay <= USED 0
abmmRmInterruptReg <= USED 0
abmmRmIsNarrowHplmnSearchNeededAndCalcNetworkMode <= USED 0
abmmRmIsPlmnAwaitingMmRetry <= USED 0
abmmRmIsPowerUp <= USED 0
abmmRmPartiallyRegistered <= USED 0
abmmRmReceivedForbiddenNatLaInDualRatManualMode <= USED 0
abmmRmReceivedForbiddenPlmnInDualRatManualMode <= USED 0
abmmRmSetSearchCtrlBits <= USED 0
abmmRmStateEcallExplicitReg <= USED 0
abmmRmStopTimersWithoutHplmnTimer <= USED 0
abmmRmT3245EventHandle <= USED 0
abmmRmT3245Expiry <= USED 0
abmmRmT3245Restart <= USED 0
abmmRmT3245Stop <= USED 0
abmmRmWhetherEPlmnlistNeedUpdate <= USED 0
get_commonReserved_3_value <= USED 0
get_commonReserved_4_value <= USED 0
;FILE abmm_sig.o
abmmDoAnrm2DeleteDataCnf <= USED 0
abmmSendAnrm2DeleteDataReq <= USED 0
abmmSendAnrm2DeleteFileRecordReq <= USED 0
abmmSendAnrm2ListFileRecordsReq <= USED 0
abmmSendAnrm2ReadFileRecordReq <= USED 0
abmmSendAnrm2WriteFileRecordReq <= USED 0
abmmSendMmrAbortPeerPlmnListReq <= USED 0
abmmSendMmrExplicitEcallInactivityReq <= USED 0
abmmSendPeerSearchPlmnResult <= USED 0
abmmSgFillLteBands <= USED 0
abmmSgFillLteBandsForResume <= USED 0
abmmSgIsOpPrefPlmn <= USED 0
abmmSgIsSendAutoPlmnListReqAndRefillBands <= USED 0
abmmSigGetNumOfBand <= USED 0
;FILE abmm_utils.o
abccCheckCurrentIsEccCall <= USED 0
abccIsInCall <= USED 0
abmmBlGetBaListForGrr <= USED 0
abmmCheckIsCellIdChanged <= USED 0
abmmCheckPeerHplmnIsTest <= USED 0
abmmCheckPeerIsSimTest <= USED 0
abmmDelNvmFPlmnsList <= USED 0
abmmIfContinueSearch <= USED 0
abmmInsertFplmnToNvmFPlmnsList <= USED 0
strnlen_util <= USED 0
;FILE abmmdebug.o
;FILE abmmmain.o
SetVUsimPowerSateApi <= USED 0
abmmDoResume <= USED 0
;FILE abnv_init.o
ICATsetHsdpaMode <= USED 0
ICATsetUserMode <= USED 0
PsCreateInitFiles <= USED 0
SetCipheringA5Algo <= USED 0
SetGprsAlgo <= USED 0
abmmCdHandleMeCustomConfig <= USED 0
;FILE absh_fnc.o
abshGetControllingTask <= USED 0
abshRestoreAbActiveSim <= USED 0
abshSendIndExcludeTask <= USED 0
abshSetAbActiveSim <= USED 0
;FILE absh_lcl.o
abshDoMmiKeyPressInd <= USED 0
abshDoMmiPowerKeyInd <= USED 0
abshOnQueue <= USED 0
abshSendApexGlErrorInd <= USED 0
;FILE abshmain.o
abshTask1 <= USED 0
abshTaskExitRoutine <= USED 0
;FILE absi_fnc.o
absiAclStatus <= USED 0
absiCheckAlsuConfirmStatus <= USED 0
absiEFReadReq <= USED 0
absiGetBaList <= USED 0
absiGetCbmidList <= USED 0
absiGetCurrentHplmn <= USED 0
absiGetEccList <= USED 0
absiGetImsi <= USED 0
absiGetMsAdminAdditionalInfo <= USED 0
absiGetPlmnFromImsi <= USED 0
absiGetRplmn <= USED 0
absiGetRplmnFromEFPsloci <= USED 0
absiGetSimPhase <= USED 0
absiGetSimProfile <= USED 0
absiGid1Available <= USED 0
absiGid2Available <= USED 0
absiGsmAccessAvailable <= USED 0
absiLpStatus <= USED 0
absiMepReqEfData <= USED 0
absiSetAclStatus <= USED 0
absiSetRatBalancingMode <= USED 0
absiSimChanged <= USED 0
absiUpdateRplmn <= USED 0
;FILE absisigs.o
absiDoAlsiReadNasConfigCnf <= USED 0
absiDoAlsiReadNasConfigReq <= USED 0
absiDoAlsiWriteEfTstReq <= USED 0
absiDoApexSimIdleReq <= USED 0
absiFetchSimDataIfAny <= USED 0
;FILE absm_apx.o
;FILE absm_ems.o
;FILE absm_ini.o
;FILE absm_sim.o
;FILE absm_trn.o
absmGetPendingTransactions <= USED 0
;FILE absm_ts.o
;FILE absm_uti.o
absmDeleteSmspParameters <= USED 0
absmGetFreeStoredMsg <= USED 0
absmGetMsgByReference <= USED 0
;FILE abst_fn3.o
;FILE abst_fn4.o
;FILE abst_fn5.o
;FILE abst_fn6.o
;FILE abst_fn7.o
;FILE access_intf.o
AccessIntf_NW_GetProperty <= USED 0
AccessIntf_NW_SetProperty <= USED 0
AccessIntf_PS_GetProperty <= USED 0
AccessIntf_QueryNetwork <= USED 0
AccessIntf_SelectNetwork <= USED 0
;FILE acmDspIf.o
ACMDspIfAudioCTMCtrl <= USED 0
ACMDspIfAudioDownLinkModeGet <= USED 0
ACMDspIfAudioIPCCtrl <= USED 0
ACMDspIfAudioSetSspConfiguration <= USED 0
ACMDspIfAudioUpLinkModeGet <= USED 0
ACMDspIfInvalidateDspModes <= USED 0
ACMGetECallData <= USED 0
ACMGetECallVoice <= USED 0
ACMSetECallData <= USED 0
ACMSetECallVoice <= USED 0
SetSideToneGain_Env <= USED 0
;FILE acm_Codec.o
GSSP_SEL_BY_CP <= USED 0
GSSP_SEL_REG_ADDR <= USED 0
IsCPOnlyBoard <= USED 0
;FILE acm_audio_effect.o
;FILE acm_audio_record.o
acm_audio_record_config <= USED 0
acm_audio_record_debug <= USED 0
acm_audio_record_debug_env <= USED 0
acm_audio_record_free <= USED 0
acm_audio_record_get <= USED 0
acm_audio_record_get_available <= USED 0
acm_audio_record_get_framesize <= USED 0
acm_audio_record_get_softgain <= USED 0
acm_audio_record_load_eq <= USED 0
acm_audio_record_read <= USED 0
audio_record_get <= USED 0
audio_record_open <= USED 0
audio_record_read <= USED 0
destroy_record_task_env <= USED 0
record_set_gain <= USED 0
record_set_timeout <= USED 0
record_start_test <= USED 0
record_start_test1 <= USED 0
record_start_test2 <= USED 0
record_start_test3 <= USED 0
record_start_test4 <= USED 0
record_start_test5 <= USED 0
record_start_test6 <= USED 0
record_start_test_get <= USED 0
record_start_test_read <= USED 0
record_stop_test <= USED 0
record_stop_test1 <= USED 0
record_stop_test2 <= USED 0
record_stop_test3 <= USED 0
record_stop_test4 <= USED 0
record_stop_test5 <= USED 0
record_stop_test6 <= USED 0
;FILE acm_audio_tone.o
StartToneStream_impl <= USED 0
audio_tone_duration <= USED 0
audio_tone_duration_env <= USED 0
audio_tone_start_ascii <= USED 0
audio_tone_start_ascii_env <= USED 0
audio_tone_start_frequency <= USED 0
audio_tone_start_frequency_env <= USED 0
audio_tone_stop <= USED 0
audio_tone_stop_env <= USED 0
;FILE acm_audio_track.o
acm_audio_track_config <= USED 0
acm_audio_track_config_env <= USED 0
acm_audio_track_debug <= USED 0
acm_audio_track_debug_env <= USED 0
acm_audio_track_get_available <= USED 0
acm_audio_track_get_framesize <= USED 0
acm_audio_track_get_softgain <= USED 0
acm_audio_track_load_gain_env <= USED 0
acm_audio_track_load_stream <= USED 0
acm_audio_track_pipeline_config <= USED 0
acm_audio_track_pipeline_read <= USED 0
destroy_track_task_env <= USED 0
track_play_gain_0db <= USED 0
track_play_gain_6db <= USED 0
track_play_mute_off <= USED 0
track_play_mute_on <= USED 0
track_play_resume <= USED 0
track_play_start_test <= USED 0
track_play_start_test1 <= USED 0
track_play_start_test2 <= USED 0
track_play_start_test3 <= USED 0
track_play_start_test4 <= USED 0
track_play_start_test5 <= USED 0
track_play_stop_test <= USED 0
track_play_stop_test1 <= USED 0
track_play_stop_test2 <= USED 0
track_play_stop_test3 <= USED 0
track_play_stop_test4 <= USED 0
track_play_stop_test5 <= USED 0
track_play_suspend <= USED 0
track_play_switch_drain <= USED 0
track_play_switch_id <= USED 0
track_play_vendor_stop_test <= USED 0
track_play_vendor_test <= USED 0
track_play_write_test <= USED 0
;FILE acm_calibration.o
ACMCalibration_GetCodecGainParam <= USED 0
ACMCalibration_GetMediaVEParam <= USED 0
ACMCalibration_GetVEParam <= USED 0
test_GetCodecGainParam <= USED 0
test_GetVEMediaEParam <= USED 0
test_GetVEParam <= USED 0
;FILE acm_control.o
ACMAudioBindECallGetHandler <= USED 0
ACMAudioDTMFPushButton_Start_Env <= USED 0
ACMAudioDTMFTone_Start <= USED 0
ACMAudioDTMF_Start_Env <= USED 0
ACMAudioDTMF_Start_Volume_scan <= USED 0
ACMAudioDTMF_mix_switch <= USED 0
ACMAudioOutGainSet <= USED 0
ACMAudioRBT_Start_Env <= USED 0
ACMAudioSetDTMFMode <= USED 0
ACMAudioSetDTMFVolumeTest <= USED 0
ACMAudio_LoopbackControl_ex <= USED 0
ACMEnableDataDump <= USED 0
ACMVoiceInGainSet <= USED 0
ACMVoiceMuteIn <= USED 0
ACMVoiceMuteOut <= USED 0
ACMVoiceOutGainSet <= USED 0
ACMVoiceSideToneGainSet <= USED 0
ATCDTMFControl <= USED 0
ATCDTMFDetectionCtrl <= USED 0
ATCECallCtrl <= USED 0
ATCECallCtrl_Env <= USED 0
ATCEQCtrl <= USED 0
ATCLoopbackCtrl <= USED 0
ATCPCMCfg <= USED 0
ATCPCMCtrl <= USED 0
ATCPCMExpertCfg <= USED 0
ATCPathCtrl <= USED 0
ATCVolumeCtrl <= USED 0
TEST_DumpGssp <= USED 0
TEST_ReadRegesiter <= USED 0
TEST_WriteRegesiter <= USED 0
VcmECallDataGetHandle_Env <= USED 0
VcmECallDataIndHandle_Env <= USED 0
VcmECallVoiceGetHandle_Env <= USED 0
VcmECallVoiceIndHandle_Env <= USED 0
dump_audio_config <= USED 0
set_phone_state <= USED 0
;FILE acm_stream.o
ACMAudioStreamInStart <= USED 0
ACMAudioStreamInStop <= USED 0
ACMAudioStreamOutStart <= USED 0
ACMStreamReset <= USED 0
;FILE ad_parser.o
ad_data_contains_uuid128 <= USED 0
ad_data_contains_uuid16 <= USED 0
;FILE adaptive_smooth_no_mmx.o
AdaptiveSmooth_NoMMX <= USED 0
;FILE aes.o
AES_CBC_decrypt_buffer <= USED 0
AES_CBC_encrypt_buffer <= USED 0
AES_CTR_xcrypt_buffer <= USED 0
AES_ECB_encrypt <= USED 0
AES_ctx_set_iv <= USED 0
AES_init_ctx_iv <= USED 0
mbedtls_aes_decrypt <= USED 0
mbedtls_aes_encrypt <= USED 0
;FILE agc.o
_Z18energy_new_WrapperPssPi <= USED 0
_Z18energy_old_WrapperPssPi <= USED 0
;FILE amidala_md5.o
sd_md5 <= USED 0
;FILE amr_payload.o
unpackAmrFrame <= USED 0
;FILE amrdecode.o
;FILE amrencode.o
AMREncodeReset <= USED 0
;FILE ap4m_psram_lpm_table.o
AP4M_psram_lpm_table <= USED 0
;FILE ap8m_psram_lpm_table.o
AP8M_psram_lpm_table <= USED 0
;FILE api_lib.o
netconn_close <= USED 0
netconn_delete_tcp <= USED 0
netconn_gethostbyname <= USED 0
netconn_recv_tcp_pbuf <= USED 0
netconn_recv_udp_raw_netbuf <= USED 0
netconn_sendto <= USED 0
netconn_write <= USED 0
;FILE api_msg.o
;FILE aplp_stub_ds3.o
BcchGsmToUtranReselection <= USED 0
GSMStopPeriodicUpdate <= USED 0
GsmStartPeriodicUpdateTemp <= USED 0
IsWbSleep <= USED 0
LteOnlyL1ErrEntrance <= USED 0
ModifySIMID <= USED 0
PlMsrCheckIfCellSearchActiveAndAbort <= USED 0
PopSIMID <= USED 0
RspGet3GCalibrationStatus <= USED 0
crcLengthConvert <= USED 0
getBchFromOtherRatFlag <= USED 0
getCycleBchCounter <= USED 0
getSchdBitmap <= USED 0
getWbGsmSuspendFlag <= USED 0
gwiAbortBch <= USED 0
gwiAckEndOfWbRfAllocation <= USED 0
gwiClearWbMeasIndicator <= USED 0
gwiDsdsGsm2WbSleepReq <= USED 0
gwiDsdsGsmActivateInd <= USED 0
gwiDsdsGsmActivateRsp <= USED 0
gwiDsdsGsmDeactivateInd <= USED 0
gwiDsdsGsmEarlyWakupReq <= USED 0
gwiDsdsRcvWbPCHInGsmPsReq <= USED 0
gwiDsdsResumeGsmCnf <= USED 0
gwiDsdsResumeWbReq <= USED 0
gwiDsdsSuspendGsmCnf <= USED 0
gwiDsdsSuspendWbReq <= USED 0
gwiDsdsWbActivateRsp <= USED 0
gwiDsdsWbDeactivateRsp <= USED 0
gwiDsdsWbInactivateRsp <= USED 0
gwiGetGsmCellInfoFromAplpDb <= USED 0
gwiGetWbMeasIndicator <= USED 0
gwiGsmBcchDecodeErrInd <= USED 0
gwiGsmL1Ready <= USED 0
gwiIsUarfcnValid <= USED 0
gwiRegisterDeactivate <= USED 0
gwiSendAfcCtrlDefreezeMode <= USED 0
gwiSendPlwGsmBcchDecodeInd <= USED 0
gwiStartBch <= USED 0
gwiUtranCellMeasAtGsmReq <= USED 0
gwiUtranRssiMeasReq <= USED 0
gwiWbDetectedMeasReqInGsm <= USED 0
gwiWbMeasAtGsmTimerExpiration <= USED 0
plAMPlpTransReq <= USED 0
plAMSetCurrentPlpSm <= USED 0
plAMgetWbResumeFlag <= USED 0
plAPLPInit <= USED 0
plAmAplpWarningHandler <= USED 0
plAmCheckValidityOfBchDecodeParameters <= USED 0
plAmSpyWriteRrcCnfEvent <= USED 0
plAmUpdatePlpSm <= USED 0
plAplpSendEmptyMsg2Schd <= USED 0
plAplpSendMsg2SchdApi <= USED 0
plAplpSetGsmTaskReq2Schd <= USED 0
plAtlGetSfn <= USED 0
plCalibEnterWCDMAMode <= USED 0
plCalibExitWCDMAMode <= USED 0
plCalibGetAcqRssiValue <= USED 0
plCalibSetAcquisition <= USED 0
plCalibStartRxTest <= USED 0
plCalibStartTxTest <= USED 0
plCalibStopTxTest <= USED 0
plDataHandleFrameInterrupt <= USED 0
plMCLClearChain <= USED 0
plMSPccpchSetNumOfShifts <= USED 0
plMSRRakeUpdatePathInfoEvent <= USED 0
plMsDratSet3GRestore <= USED 0
plMsSmSendMsaPowerManagementParameters <= USED 0
plMsrAfcDacTableFileUpdate <= USED 0
plMsrBcchTimerExpired <= USED 0
plMsrClearMcl <= USED 0
plMsrClearMeasureTypesState <= USED 0
plMsrDbClearAllPathsMeasuremnts <= USED 0
plMsrGsWbReset <= USED 0
plMsrGsWbStop <= USED 0
plMsrGsmPlmnPrintDb <= USED 0
plMsrGsmSrvGSMPowerup <= USED 0
plMsrGsmSrvGSMReset <= USED 0
plMsrGsmSrvGSMStartup <= USED 0
plMsrGsmSrvGSMTerminate <= USED 0
plMsrInGsmRestoreMsrModeAndStateIfExist <= USED 0
plMsrInLteRestoreMsrModeAndStateIfExist <= USED 0
plMsrPlpGsmRefTimeSet <= USED 0
plMsrSendAPLPApiEvent <= USED 0
plMsrSendDefaultMeasParams <= USED 0
plMsrSendMultiBcchReportToRrc <= USED 0
plMsrStopSkipPreventor <= USED 0
plMsrWbMeasAtGsmAbortReq <= USED 0
plMsr_AM_SearchEngInit <= USED 0
plRFDCAPTParasOptFile_ReadNVMFile <= USED 0
plRFDGetBandByUarfcn <= USED 0
plRFDGetPDDebugInfoCnf <= USED 0
plRFDGetSupportedRfBands <= USED 0
plRFDIsDlUarfcnValid <= USED 0
plRFDIsMaxPowerActive <= USED 0
plRFDMaxPowerInitApi <= USED 0
plRFDOpen <= USED 0
plRfDivTestDivAnt <= USED 0
plRfDivTestInit <= USED 0
plRfDivTestPriAntOnly <= USED 0
plRfDivTestSecAntOnly <= USED 0
plSchdSendGsmCopyCodeCmd <= USED 0
plTCCDefaultBindFunction <= USED 0
plTccSetStopShift <= USED 0
plTccVirtualMaceIntHisr <= USED 0
pldGsmTerminate <= USED 0
pldGsmTerminate2 <= USED 0
pldInit <= USED 0
pldRDASetGsm <= USED 0
pldSetGsm <= USED 0
pldSetWcdma <= USED 0
pldWcdmaTerminate <= USED 0
plgRFDStartTempUpdateOnce <= USED 0
plgSetRfResetReq <= USED 0
plwBindCphyDetectedCellMeasInd <= USED 0
plwBindCphyFreqScanInd <= USED 0
plwBindCphyIntraFreqCellMeasurementInd <= USED 0
plwBindCphyMeasuredIntraFreqCellsInd <= USED 0
plwBindCphyOutOfSyncInd <= USED 0
plwBindCphyPsOutOfSyncInd <= USED 0
plwBindCphyPsSyncInd <= USED 0
plwBindCphyRlReleaseCnf <= USED 0
plwBindCphyRlSetupCnf <= USED 0
plwBindCphySyncInd <= USED 0
plwBindPhyDataInd <= USED 0
plwBindPhyDownlinkDataTransferEnd <= USED 0
plwBindPhyHsDataInd <= USED 0
plwBindPhyUplinkDataSync <= USED 0
plwBindPhyUplinkDataTransferEnd <= USED 0
plwCphyDeactivateReq_internal <= USED 0
plwCphyDetectedCellMeasReq <= USED 0
plwCphyDpchSetupReq <= USED 0
plwCphyFreqScanReq <= USED 0
plwCphyIntraFreqCellMeasReq <= USED 0
plwCphyPccpchSetupReq <= USED 0
plwCphyRlReleaseReq <= USED 0
plwCphySetCompressedModeParams <= USED 0
plwDpchDlEstablished <= USED 0
plwLoopBackMode2 <= USED 0
plwNotifyNextTtiTfc <= USED 0
plwPhyDataReq <= USED 0
plwPhyHsPointerAssignReq <= USED 0
resetBchFromOtherRatFlag <= USED 0
schdAbortIntrabchWhenResumeGsmorWb <= USED 0
setBcchDecodeState <= USED 0
;FILE arc4random.o
;FILE arm946e_sCP15.o
Arm946eCP15DrainWriteBuffer <= USED 0
Arm946eCP15FlushDataCache <= USED 0
Arm946eCP15FlushInstCache <= USED 0
Arm946eCP15GetAlternateVectorSelect <= USED 0
Arm946eCP15GetBigEndian <= USED 0
Arm946eCP15GetCacheTypeReg <= USED 0
Arm946eCP15GetDataCacheEnable <= USED 0
Arm946eCP15GetDataCacheSize <= USED 0
Arm946eCP15GetDataTCMEnable <= USED 0
Arm946eCP15GetDataTCMLoadMode <= USED 0
Arm946eCP15GetDataTCMRegionBaseAndSizeReg <= USED 0
Arm946eCP15GetDataTCMSize <= USED 0
Arm946eCP15GetInstCacheEnable <= USED 0
Arm946eCP15GetInstTCMEnable <= USED 0
Arm946eCP15GetInstTCMLoadMode <= USED 0
Arm946eCP15GetInstTCMRegionBaseAndSizeReg <= USED 0
Arm946eCP15GetInstTCMSize <= USED 0
Arm946eCP15GetInstructionCacheSize <= USED 0
Arm946eCP15GetRegionBaseAndSizeReg <= USED 0
Arm946eCP15GetTCMSizeReg <= USED 0
Arm946eCP15Read <= USED 0
Arm946eCP15RestoreDataTCMEnable <= USED 0
Arm946eCP15RestoreInstTCMEnable <= USED 0
Arm946eCP15SetAlternateVectorSelect <= USED 0
Arm946eCP15SetBigEndian <= USED 0
Arm946eCP15SetControlReg <= USED 0
Arm946eCP15SetDataCacheDisable <= USED 0
Arm946eCP15SetDataTCMDisable <= USED 0
Arm946eCP15SetDataTCMLoadMode <= USED 0
Arm946eCP15SetInstCacheDisable <= USED 0
Arm946eCP15SetInstTCMDisable <= USED 0
Arm946eCP15SetInstTCMLoadMode <= USED 0
Arm946eCP15SetLowPowerState <= USED 0
Arm946eCP15Write <= USED 0
;FILE arm946e_sCP15_memRetain.o
Arm946eCP15GetControlReg_DS <= USED 0
Arm946eCP15GetMPUEnalbe_DS <= USED 0
Arm946eCP15RestoreMPUEnableDisable_DS <= USED 0
Arm946eCP15SetDataCacheEnable_DS <= USED 0
Arm946eCP15SetDataTCMEnable_DS <= USED 0
Arm946eCP15SetDataTCMRegionBaseAndSizeReg_DS <= USED 0
Arm946eCP15SetInstCacheEnable_DS <= USED 0
Arm946eCP15SetInstTCMEnable_DS <= USED 0
Arm946eCP15SetInstTCMRegionBaseAndSizeReg_DS <= USED 0
Arm946eCP15SetMPUDisable_DS <= USED 0
Arm946eCP15SetMPUEnalbe_DS <= USED 0
Arm946eCP15SetRegionAccessPermission_DS <= USED 0
Arm946eCP15SetRegionBaseAndSizeReg_DS <= USED 0
;FILE ascc_fnc.o
;FILE ascs_fnc.o
ascsGetFirstNwStateInd <= USED 0
;FILE asgki.o
;FILE asgl_cfg.o
;FILE asgl_fnc.o
;FILE asmn_fnc.o
simatDontDestroySignal <= USED 0
simatTask1 <= USED 0
;FILE asms_fnc.o
;FILE asn1parse.o
mbedtls_asn1_find_named_data <= USED 0
mbedtls_asn1_free_named_data <= USED 0
mbedtls_asn1_free_named_data_list <= USED 0
mbedtls_asn1_get_alg_null <= USED 0
;FILE asn1write.o
mbedtls_asn1_store_named_data <= USED 0
mbedtls_asn1_write_algorithm_identifier <= USED 0
mbedtls_asn1_write_bitstring <= USED 0
mbedtls_asn1_write_bool <= USED 0
mbedtls_asn1_write_ia5_string <= USED 0
mbedtls_asn1_write_int <= USED 0
mbedtls_asn1_write_named_bitstring <= USED 0
mbedtls_asn1_write_null <= USED 0
mbedtls_asn1_write_octet_string <= USED 0
mbedtls_asn1_write_oid <= USED 0
mbedtls_asn1_write_printable_string <= USED 0
mbedtls_asn1_write_raw_buffer <= USED 0
mbedtls_asn1_write_tagged_string <= USED 0
mbedtls_asn1_write_utf8_string <= USED 0
;FILE aspl_fnc.o
;FILE aspp_fnc.o
;FILE asr_agps_api.o
getGPSTime <= USED 0
getRefTime <= USED 0
tp_decode_data <= USED 0
tp_get_location <= USED 0
;FILE asr_bt_prepare.o
;FILE asr_property.o
asr_property_dump <= USED 0
asr_property_get_buffer <= USED 0
asr_property_set <= USED 0
;FILE asrd_fnc.o
simatRefreshInit <= USED 0
;FILE asros_mbedtls_platform.o
mbedtls_timing_get_delay <= USED 0
mbedtls_timing_set_delay <= USED 0
;FILE astm_fnc.o
;FILE atParser.o
ATCMDDump <= USED 0
ATReParse <= USED 0
getAutoAnswerDelayValue <= USED 0
utlAtCmdGetSetting <= USED 0
utlAtCommandTimeoutModify <= USED 0
utlAtParserInitCommandParameters <= USED 0
utlCloseAtParser <= USED 0
utlGetDCDmode <= USED 0
utlGetDTRmode <= USED 0
utlSetEcho <= USED 0
;FILE at_cmds_mmi.o
ATTestHandleRespInd <= USED 0
MMI_Command_Test <= USED 0
Set_At_AudioTest_Cb <= USED 0
at_conf_ind_callback <= USED 0
hwtest_get_build_time <= USED 0
hwtest_get_fix_version <= USED 0
hwtest_get_opearte_name <= USED 0
hwtest_get_speaker_test_result <= USED 0
hwtest_get_storage <= USED 0
hwtest_set_build_time <= USED 0
hwtest_set_fix_version <= USED 0
hwtest_set_opearte_name <= USED 0
hwtest_set_speaker_test_result <= USED 0
kaer_Set_air_calibrate_Callback <= USED 0
kaer_Set_hearttest_Callback <= USED 0
ke_at_cmd_keset_cb_register <= USED 0
mmiCnf <= USED 0
;FILE at_common.o
ATCommon_release <= USED 0
;FILE at_iointf_requestor.o
;FILE at_parser.o
;FILE at_prop.o
ATPROP_GetCPBS <= USED 0
ATPROP_GetNewSMSIndicationSettings <= USED 0
ATPROP_GetPreferredMemoryForStorage <= USED 0
ATPROP_GetS0 <= USED 0
ATPROP_GetSimMemoryStatus <= USED 0
ATPROP_GetZDON <= USED 0
ATPROP_Hdl_Get_SMS_Capability <= USED 0
ATPROP_Hdl_Get_Sim_Memory_Status <= USED 0
ATPROP_SetCMRSS <= USED 0
ATPROP_SetCPBR <= USED 0
ATPROP_SetCPBS <= USED 0
ATPROP_SetProxyATReqBackByTransparentTransmission <= USED 0
;FILE at_provider.o
AT_SendTestResultCode <= USED 0
at_provider_get_proxy_req_group_name <= USED 0
at_provider_update_crnt_proxy_req <= USED 0
;FILE at_proxy.o
AT_Proxy_SetIO <= USED 0
;FILE at_proxy_provider.o
at_Proxy_SetInitOwner <= USED 0
;FILE at_requestor.o
AT_CancelRequest <= USED 0
AT_CancelSyncRequest <= USED 0
;FILE atcmd_hdlr.o
;FILE atcmd_list.o
;FILE att_db.o
att_dump_attributes <= USED 0
att_read_callback_handle_blob <= USED 0
att_read_callback_handle_byte <= USED 0
att_read_callback_handle_little_endian_16 <= USED 0
att_read_callback_handle_little_endian_32 <= USED 0
att_uuid_for_handle <= USED 0
gatt_server_get_client_configuration_handle_for_characteristic_with_uuid128 <= USED 0
gatt_server_get_client_configuration_handle_for_characteristic_with_uuid16 <= USED 0
gatt_server_get_descriptor_handle_for_characteristic_with_uuid16 <= USED 0
gatt_server_get_handle_range_for_service_with_uuid128 <= USED 0
gatt_server_get_handle_range_for_service_with_uuid16 <= USED 0
gatt_server_get_included_service_with_uuid16 <= USED 0
gatt_server_get_server_configuration_handle_for_characteristic_with_uuid16 <= USED 0
gatt_server_get_value_handle_for_characteristic_with_uuid128 <= USED 0
gatt_server_get_value_handle_for_characteristic_with_uuid16 <= USED 0
;FILE att_dispatch.o
;FILE att_server.o
att_server_can_send_packet_now <= USED 0
att_server_get_mtu <= USED 0
att_server_register_service_handler <= USED 0
att_server_request_to_send_indication <= USED 0
att_server_response_ready <= USED 0
;FILE audio_bind.o
AcmGetPCMRate <= USED 0
AcmSetPCMRate <= USED 0
Audio_production_mode_test <= USED 0
Audio_production_result_get_test <= USED 0
OEMAudio_BindGetVoiceDataCallback <= USED 0
OEMAudio_SetVoiceData <= USED 0
OEMAudio_UnBindGetVoiceDataCallback <= USED 0
OEMTest_AMRRxHandler <= USED 0
OEMTest_AMRTxHandler <= USED 0
OEMTest_AMR_Loopback <= USED 0
OEMTest_AMR_nodata <= USED 0
OEMTest_DTMFDetectionDemo <= USED 0
OEMTest_DTMFDetectionHandler <= USED 0
OEMTest_GetVoiceDataDemo <= USED 0
OEMTest_Loopback <= USED 0
OEMTest_LoopbackRxHandler <= USED 0
OEMTest_LoopbackRxHandler_2 <= USED 0
OEMTest_LoopbackTxHandler <= USED 0
OEMTest_Loopback_EX <= USED 0
OEMTest_Loopback_EX2 <= USED 0
OEMTest_SetVoiceData <= USED 0
POCVoice_BindAMRHandlers <= USED 0
POCVoice_BindPCMRecord <= USED 0
POCVoice_ConfigPlay2FarEnd <= USED 0
POCVoice_ConfigPlay2NearEnd <= USED 0
POCVoice_ConfigRecod_switch <= USED 0
POCVoice_ConfigRecord <= USED 0
POCVoice_ConfigureAMR <= USED 0
POCVoice_DTMF_Button <= USED 0
POCVoice_DTMF_test <= USED 0
POCVoice_DTMF_test2 <= USED 0
POCVoice_MuteTTSPlay <= USED 0
POCVoice_PCMPlayConfig <= USED 0
POCVoice_SWITCH_FORMAT <= USED 0
POCVoice_SWITCH_NB <= USED 0
POCVoice_SWITCH_RATE <= USED 0
POCVoice_StartTTSPlay <= USED 0
POCVoice_StopTTSPlay <= USED 0
POCVoice_UnBindAMRHandlers <= USED 0
POCVoice_UnBindAny <= USED 0
POCVoice_UnBindPCMRecord <= USED 0
POCVoice_tone_test <= USED 0
Test_GetVoiceDataCallback <= USED 0
audioBindDoDataSwapBytes <= USED 0
audioBindGetRAT <= USED 0
audioBindGsmGetDtxSupport <= USED 0
audioBindGsmGetTxBuffer <= USED 0
audioBindGsmGetVocoderTypeRate <= USED 0
audioBindGsmSetAmrSidTypeInd <= USED 0
audioBindGsmSetAmrToc <= USED 0
audioBindGsmSetDecoderSidInd <= USED 0
audioBindGsmSetEncoderFlags <= USED 0
audioBindGsmStartVoicePath <= USED 0
audioBindGsmStopVoicePath <= USED 0
audioStubReverseWBAMR <= USED 0
setPOCBindType <= USED 0
;FILE audio_file.o
common_access <= USED 0
register_audio_fops <= USED 0
;FILE audio_loopback.o
test_speaker_loopback <= USED 0
;FILE audio_pcm.o
tg_pcm_open <= USED 0
tg_pcm_read <= USED 0
tg_pcm_set_state_listener <= USED 0
tg_pcm_start <= USED 0
tg_volume_set <= USED 0
;FILE audio_resample.o
ReSample_SwitchFileWrite_Env <= USED 0
ReSample_Test_Env <= USED 0
;FILE audio_server_api_ttc.o
;FILE auto_test.o
;FILE autocorr.o
;FILE automode.o
WebRtcNetEQ_BufferLevelFilter <= USED 0
;FILE avdtp.o
avdtp_abort_stream <= USED 0
avdtp_get_capabilities <= USED 0
avdtp_get_configuration <= USED 0
avdtp_get_source_stream_endpoint_for_media_codec <= USED 0
avdtp_get_source_stream_endpoint_for_media_codec_other <= USED 0
avdtp_reconfigure <= USED 0
avdtp_register_content_protection_category <= USED 0
avdtp_register_header_compression_category <= USED 0
avdtp_register_multiplexing_category <= USED 0
avdtp_register_recovery_category <= USED 0
avdtp_register_reporting_category <= USED 0
avdtp_set_preferred_channel_mode <= USED 0
avdtp_set_preferred_sampling_frequency <= USED 0
avdtp_sink_register_media_config_validator <= USED 0
avdtp_source_register_media_config_validator <= USED 0
avdtp_stop_stream <= USED 0
;FILE avdtp_acceptor.o
;FILE avdtp_initiator.o
;FILE avdtp_sink.o
avdtp_sink_abort_stream <= USED 0
avdtp_sink_delay_report <= USED 0
avdtp_sink_disconnect <= USED 0
avdtp_sink_discover_stream_endpoints <= USED 0
avdtp_sink_get_all_capabilities <= USED 0
avdtp_sink_get_capabilities <= USED 0
avdtp_sink_get_configuration <= USED 0
avdtp_sink_open_stream <= USED 0
avdtp_sink_reconfigure <= USED 0
avdtp_sink_register_content_protection_category <= USED 0
avdtp_sink_register_header_compression_category <= USED 0
avdtp_sink_register_multiplexing_category <= USED 0
avdtp_sink_register_recovery_category <= USED 0
avdtp_sink_register_reporting_category <= USED 0
avdtp_sink_set_configuration <= USED 0
avdtp_sink_start_stream <= USED 0
avdtp_sink_stop_stream <= USED 0
avdtp_sink_suspend <= USED 0
;FILE avdtp_source.o
avdtp_max_media_payload_size <= USED 0
avdtp_source_abort_stream <= USED 0
avdtp_source_disconnect <= USED 0
avdtp_source_discover_stream_endpoints <= USED 0
avdtp_source_finalize_stream_endpoint <= USED 0
avdtp_source_get_all_capabilities <= USED 0
avdtp_source_get_capabilities <= USED 0
avdtp_source_get_configuration <= USED 0
avdtp_source_open_stream <= USED 0
avdtp_source_reconfigure <= USED 0
avdtp_source_register_content_protection_category <= USED 0
avdtp_source_register_header_compression_category <= USED 0
avdtp_source_register_multiplexing_category <= USED 0
avdtp_source_register_recovery_category <= USED 0
avdtp_source_register_reporting_category <= USED 0
avdtp_source_set_configuration <= USED 0
avdtp_source_start_stream <= USED 0
avdtp_source_stop_stream <= USED 0
avdtp_source_stream_send_media_packet <= USED 0
avdtp_source_stream_send_media_payload_rtp <= USED 0
avdtp_source_suspend <= USED 0
;FILE avdtp_util.o
avdtp_config_atrac_set_sampling_frequency <= USED 0
avdtp_config_atrac_store <= USED 0
avdtp_config_mpeg_aac_set_sampling_frequency <= USED 0
avdtp_config_mpeg_aac_store <= USED 0
avdtp_config_mpeg_audio_set_sampling_frequency <= USED 0
avdtp_config_mpeg_audio_store <= USED 0
;FILE avrcp.o
avrcp_get_browsing_connection_for_l2cap_cid_for_role <= USED 0
avrcp_get_connection_for_browsing_cid_for_role <= USED 0
avrcp_get_connection_for_browsing_l2cap_cid_for_role <= USED 0
avrcp_get_connections <= USED 0
avrcp_sdp_query_browsing_l2cap_psm <= USED 0
;FILE avrcp_controller.o
avrcp_controller_add_item_from_scope_to_now_playing_list <= USED 0
avrcp_controller_deinit <= USED 0
avrcp_controller_disable_notification <= USED 0
avrcp_controller_fast_forward <= USED 0
avrcp_controller_force_send_press_cmd <= USED 0
avrcp_controller_get_element_attributes <= USED 0
avrcp_controller_get_now_playing_info <= USED 0
avrcp_controller_get_now_playing_info_for_media_attribute_id <= USED 0
avrcp_controller_get_play_status <= USED 0
avrcp_controller_get_supported_company_ids <= USED 0
avrcp_controller_mute <= USED 0
avrcp_controller_play_item_for_scope <= USED 0
avrcp_controller_press_and_hold_backward <= USED 0
avrcp_controller_press_and_hold_fast_forward <= USED 0
avrcp_controller_press_and_hold_forward <= USED 0
avrcp_controller_press_and_hold_mute <= USED 0
avrcp_controller_press_and_hold_pause <= USED 0
avrcp_controller_press_and_hold_play <= USED 0
avrcp_controller_press_and_hold_rewind <= USED 0
avrcp_controller_press_and_hold_stop <= USED 0
avrcp_controller_press_and_hold_volume_down <= USED 0
avrcp_controller_press_and_hold_volume_up <= USED 0
avrcp_controller_query_shuffle_and_repeat_modes <= USED 0
avrcp_controller_release_press_and_hold_cmd <= USED 0
avrcp_controller_rewind <= USED 0
avrcp_controller_send_custom_command <= USED 0
avrcp_controller_set_addressed_player <= USED 0
avrcp_controller_set_max_num_fragments <= USED 0
avrcp_controller_set_repeat_mode <= USED 0
avrcp_controller_set_shuffle_mode <= USED 0
avrcp_controller_skip <= USED 0
avrcp_controller_start_press_and_hold_cmd <= USED 0
avrcp_controller_subunit_info <= USED 0
avrcp_controller_unit_info <= USED 0
;FILE avrcp_target.o
avrcp_target_addressed_player_changed <= USED 0
avrcp_target_adjust_absolute_volume <= USED 0
avrcp_target_battery_status_changed <= USED 0
avrcp_target_deinit <= USED 0
avrcp_target_operation_rejected <= USED 0
avrcp_target_playing_content_changed <= USED 0
avrcp_target_register_set_addressed_player_handler <= USED 0
avrcp_target_set_now_playing_info <= USED 0
avrcp_target_set_playback_status <= USED 0
avrcp_target_set_subunit_info <= USED 0
avrcp_target_set_unit_info <= USED 0
avrcp_target_support_companies <= USED 0
avrcp_target_support_event <= USED 0
avrcp_target_track_changed <= USED 0
avrcp_target_volume_changed <= USED 0
;FILE az_lsp.o
_Z14Chebps_WrappersPssPi <= USED 0
;FILE b_cn_cod.o
;FILE backend_flash.o
backend_free_env <= USED 0
;FILE bal_tasks.o
DM_CheckPowerOnCause <= USED 0
DM_GetPowerOnCause <= USED 0
DM_IsPowerOnByCharger <= USED 0
UI_DevMonitorTask <= USED 0
UI_SendKeyMessage <= USED 0
UI_UsbMonitorTask <= USED 0
dm_PowerOnCheck <= USED 0
dm_SendArlarmMessage <= USED 0
dm_SendISRKeyMessage <= USED 0
dm_SendKeyMessage <= USED 0
dm_SendPMMessage <= USED 0
dm_SendPowerOnMessage <= USED 0
dm_SendSwitchLcdMessage <= USED 0
dm_SendTpMessage <= USED 0
hal_KeyOnOffStateAtPowerOn <= USED 0
hal_SysGetPowerUpCause <= USED 0
;FILE base64.o
mbedtls_base64_encode <= USED 0
;FILE bgnscd.o
;FILE bignum.o
mbedtls_mpi_div_int <= USED 0
mbedtls_mpi_mod_int <= USED 0
mbedtls_mpi_read_binary_le <= USED 0
mbedtls_mpi_read_string <= USED 0
mbedtls_mpi_safe_cond_swap <= USED 0
mbedtls_mpi_set_bit <= USED 0
mbedtls_mpi_swap <= USED 0
mbedtls_mpi_write_string <= USED 0
;FILE bip.o
BipCmdTaskInitInternal <= USED 0
PollPdps <= USED 0
STK_CleanSocket <= USED 0
STK_EnvelopeFormat_DataAvail <= USED 0
STK_ReadLoop <= USED 0
STK_RxBufMemCpy <= USED 0
STK_TcpServerThreadFunc <= USED 0
STK_UdpClientThreadFunc <= USED 0
STK_getChannelidByStatus <= USED 0
STK_getNumbersOfCid <= USED 0
STK_getRxLen <= USED 0
STK_isEventInList <= USED 0
STK_isTargetPDPActive <= USED 0
processOth <= USED 0
;FILE bipIpSockets.o
MTIL_Accept <= USED 0
MTIL_Listen <= USED 0
MTIL_RecvSock <= USED 0
MTIL_SelectReadMult <= USED 0
;FILE bits2prm.o
;FILE bitstream.o
PV_BitstreamFlushBitsCheck <= USED 0
;FILE ble_demo_utils.o
;FILE ble_hid_demo.o
;FILE block_idct.o
_Z15Copy_Blk_to_VopPhS_i <= USED 0
;FILE bspUartManager.o
bspUartApplUseAltFunc <= USED 0
bspUartGetIoUart <= USED 0
bspUartNumOfUartsGet <= USED 0
bspUartPhase1Init <= USED 0
bspUartPhase2Init <= USED 0
bspUartPlatUseAltFunc <= USED 0
;FILE bsp_common.o
arch_clean_cache_range <= USED 0
arch_clean_invalidate_cache_range <= USED 0
arch_disable_cache <= USED 0
arch_enable_cache <= USED 0
arch_invalidate_cache_range <= USED 0
arch_sync_cache_range <= USED 0
board_sys_tick_init <= USED 0
board_sys_tick_start <= USED 0
board_sys_tick_stop <= USED 0
flush_cache <= USED 0
gpio_enable_edge_detect <= USED 0
gpio_init <= USED 0
gpio_input_get <= USED 0
gpio_register_int_handler <= USED 0
interrupts_init <= USED 0
irq_disable <= USED 0
irq_enable <= USED 0
irq_is_in <= USED 0
irq_restore <= USED 0
lv_get_ticks_per_second <= USED 0
mask_interrupt <= USED 0
pmic_get_bat_remain <= USED 0
pmic_get_chg_status <= USED 0
pmic_powerup_get_reason <= USED 0
pmic_register_chg_handle <= USED 0
pmic_rtc_enable_alarm <= USED 0
pmic_rtc_get_alarm <= USED 0
pmic_rtc_get_time <= USED 0
pmic_rtc_get_utc_sec <= USED 0
pmic_rtc_get_utc_time <= USED 0
pmic_rtc_set_alarm <= USED 0
pmic_rtc_set_time <= USED 0
pmic_sw_pdown <= USED 0
pmic_sw_reboot <= USED 0
read_onkey <= USED 0
register_int_handler <= USED 0
timer_disable <= USED 0
timer_enable <= USED 0
unmask_interrupt <= USED 0
watch_uart_printf <= USED 0
;FILE bsp_hisr.o
bspParkThread <= USED 0
;FILE bsp_tavor.o
DelayBeforeCPOff <= USED 0
EnableDataPktInfo <= USED 0
EnableDataPktInfoCmd <= USED 0
EnableDebugLog <= USED 0
GetUmtsChipVersion <= USED 0
InitGPIOWakeupForUSB <= USED 0
IsChipCraneG_A1 <= USED 0
IsChipCraneL_A0 <= USED 0
IsChip_CraneG_A0_or_Above <= USED 0
IsChip_CraneM_A0_or_Above <= USED 0
IsChip_Crane_A0_or_Above <= USED 0
PlatformGetConstData <= USED 0
ReEnableUSIMGPIODetection <= USED 0
SetspecialPShandle <= USED 0
USIM_GPIO_WK <= USED 0
bootup_gpio_ind <= USED 0
bootup_mode_cfun4 <= USED 0
bspBoardIsDKB <= USED 0
bspGetAcLinkName <= USED 0
bspGetIpcInts <= USED 0
bspGetPlpFrameInt <= USED 0
bspI2CToNAInit <= USED 0
bspI2cInit <= USED 0
bspInit <= USED 0
bspInitRfInterface <= USED 0
bspIpcInit <= USED 0
eeh_gpio_ind <= USED 0
hsi_gpio_init <= USED 0
hsic_emu_wait_ap_gpio <= USED 0
hsic_gpio_init <= USED 0
hsic_ready_gpio_ind <= USED 0
hsic_ready_gpio_set_after_enum <= USED 0
is_dev_chnl_ready <= USED 0
is_pmic801_used_func <= USED 0
m_atoi <= USED 0
platform_type_init <= USED 0
print_specialPShandle <= USED 0
set_cp2ap_pm_gpio_high <= USED 0
set_cp2ap_pm_gpio_low <= USED 0
set_ps_dev_chnl_ready <= USED 0
usim2_enable <= USED 0
usim_gpio_init <= USED 0
;FILE bt_a2dp_sink.o
;FILE bt_a2dp_source.o
asr_a2dp_close <= USED 0
;FILE bt_api.o
appbt_accept_bonding <= USED 0
appbt_enable_hcidump <= USED 0
appbt_get_leaddr <= USED 0
appbt_le_clear_device_record <= USED 0
appbt_pin_negative_reply <= USED 0
appbt_pin_reply <= USED 0
appbt_send_keypress_notification <= USED 0
appbt_set_check_controller_alive <= USED 0
appbt_set_coexist_mode <= USED 0
appbt_set_headset_mode <= USED 0
appbt_set_page_scan_type <= USED 0
appbt_set_visisable_connectable <= USED 0
appbt_test_hci_reset <= USED 0
appbt_user_passkey_request_negative_reply <= USED 0
appbt_user_passkey_request_reply <= USED 0
bt_base_timer <= USED 0
bt_system_shutdown <= USED 0
;FILE bt_api_a2dp.o
;FILE bt_api_a2dp_sink.o
appbt_a2dp_sink_srv_reg <= USED 0
;FILE bt_api_audio.o
bt_hfp_audio_open <= USED 0
;FILE bt_api_avrcp.o
appbt_avrcp_vol_key <= USED 0
;FILE bt_api_ftp.o
appbt_ftp_pull_next <= USED 0
appbt_ftp_send_file <= USED 0
;FILE bt_api_hfp.o
appbt_get_sco_status <= USED 0
appbt_hfp_reject_connection <= USED 0
appbt_hfp_set_blocklist <= USED 0
appbt_hfp_set_whitelist <= USED 0
appbt_sco_data_send <= USED 0
appbt_switch_voice_to_headset <= USED 0
appbt_switch_voice_to_phone <= USED 0
bt_telephone_status_cb <= USED 0
;FILE bt_api_hfp_hf.o
;FILE bt_api_hid.o
appbt_hid_send_interrupt <= USED 0
;FILE bt_api_le.o
appbt_le_clear_white_list <= USED 0
appbt_le_create_adv_data <= USED 0
appbt_le_custom_gatt_scan_subscribe_default <= USED 0
appbt_le_custom_get_conn_handle <= USED 0
appbt_le_read_adv_phy_txpower <= USED 0
appbt_le_set_adv_set_random_address <= USED 0
appbt_le_set_default_phy <= USED 0
appbt_le_set_ext_adv_data <= USED 0
appbt_le_set_ext_scan_response <= USED 0
appbt_le_set_pair_enable <= USED 0
appbt_le_set_passkey_from_local <= USED 0
appbt_le_set_white_list <= USED 0
appbt_le_write <= USED 0
;FILE bt_api_map.o
appbt_map_flow_control <= USED 0
appbt_map_get_folder_listing <= USED 0
appbt_map_get_message_listing <= USED 0
appbt_map_get_next <= USED 0
appbt_map_set_notifications <= USED 0
;FILE bt_api_obex.o
appbt_obex_send_file <= USED 0
appbt_obex_send_response <= USED 0
;FILE bt_api_pbap.o
appbt_pbap_lookup_number <= USED 0
appbt_pbap_pull_next <= USED 0
appbt_pbap_set_path <= USED 0
;FILE bt_api_spp.o
;FILE bt_avrcp.o
asr_avrcp_set_volume_changed <= USED 0
;FILE bt_ftp_client.o
;FILE bt_hfp_ag.o
;FILE bt_hfp_hf.o
asr_hfp_hf_reject_incoming_call <= USED 0
;FILE bt_hid.o
;FILE bt_ipc.o
btdm_enable_spi_log <= USED 0
btdm_module_clear_ipc_interrupt <= USED 0
;FILE bt_map_client.o
asr_set_flow_control <= USED 0
;FILE bt_mgr.o
appbt_acl_role <= USED 0
appbt_get_a2dp_status <= USED 0
appbt_get_mgr_state <= USED 0
;FILE bt_opp_client.o
;FILE bt_opp_service.o
;FILE bt_pbap_client.o
asr_pbap_client_pull_vcard_entry <= USED 0
asr_pbap_client_pull_vcard_listing <= USED 0
;FILE bt_spp.o
;FILE bt_uart.o
bt_uart_set_baudrate <= USED 0
;FILE btdm.o
;FILE btdm_2_mcu_ipc.o
clear_btdm_2_mcu_message_db_index_info <= USED 0
;FILE btstack_chipset_asr.o
;FILE btstack_command_handler.o
;FILE btstack_controller_alive.o
;FILE btstack_crypto.o
btstack_crypto_aes128_cmac_generator <= USED 0
btstack_crypto_aes128_cmac_message <= USED 0
btstack_crypto_aes128_cmac_zero <= USED 0
btstack_crypto_ccm_decrypt_block <= USED 0
btstack_crypto_ccm_digest <= USED 0
btstack_crypto_ccm_encrypt_block <= USED 0
btstack_crypto_ccm_get_authentication_value <= USED 0
btstack_crypto_ccm_init <= USED 0
btstack_crypto_ecc_p256_set_key <= USED 0
btstack_crypto_idle <= USED 0
btstack_crypto_reset <= USED 0
;FILE btstack_cvsd_plc.o
btstack_cvsd_dump_statistics <= USED 0
btstack_cvsd_plc_rcos <= USED 0
;FILE btstack_device_record.o
asr_add_device_record <= USED 0
asr_remove_device_record_by_addr <= USED 0
;FILE btstack_event_handler.o
btstack_event_clear <= USED 0
;FILE btstack_hid_parser.o
btstack_hid_parser_get_field <= USED 0
btstack_hid_parser_has_more <= USED 0
btstack_hid_parser_init <= USED 0
;FILE btstack_link_key_db_memory.o
;FILE btstack_linked_list.o
;FILE btstack_main_asr.o
btstack_set_debug_level <= USED 0
;FILE btstack_memory.o
btstack_memory_avrcp_browsing_connection_free <= USED 0
btstack_memory_avrcp_browsing_connection_get <= USED 0
btstack_memory_battery_service_client_free <= USED 0
btstack_memory_battery_service_client_get <= USED 0
btstack_memory_bnep_channel_free <= USED 0
btstack_memory_bnep_channel_get <= USED 0
btstack_memory_bnep_service_free <= USED 0
btstack_memory_bnep_service_get <= USED 0
btstack_memory_hid_host_connection_free <= USED 0
btstack_memory_hid_host_connection_get <= USED 0
btstack_memory_hids_client_free <= USED 0
btstack_memory_hids_client_get <= USED 0
btstack_memory_periodic_advertiser_list_entry_get <= USED 0
btstack_memory_scan_parameters_service_client_free <= USED 0
btstack_memory_scan_parameters_service_client_get <= USED 0
btstack_memory_sm_lookup_entry_get <= USED 0
;FILE btstack_run_loop.o
btstack_run_loop_add_data_source <= USED 0
btstack_run_loop_base_add_callback <= USED 0
btstack_run_loop_base_execute_callbacks <= USED 0
btstack_run_loop_base_poll_data_sources <= USED 0
btstack_run_loop_base_process_timers <= USED 0
btstack_run_loop_disable_data_source_callbacks <= USED 0
btstack_run_loop_enable_data_source_callbacks <= USED 0
btstack_run_loop_execute_on_main_thread <= USED 0
btstack_run_loop_get_data_source_fd <= USED 0
btstack_run_loop_get_data_source_handle <= USED 0
btstack_run_loop_poll_data_sources_from_irq <= USED 0
btstack_run_loop_remove_data_source <= USED 0
btstack_run_loop_set_data_source_fd <= USED 0
btstack_run_loop_set_data_source_handle <= USED 0
btstack_run_loop_set_data_source_handler <= USED 0
btstack_run_loop_timer_dump <= USED 0
btstack_run_loop_trigger_exit <= USED 0
;FILE btstack_run_loop_asr.o
;FILE btstack_sdp_db.o
;FILE btstack_slip.o
;FILE btstack_sniff_ctrl.o
;FILE btstack_tlv.o
;FILE btstack_tlv_asr.o
btstack_tlv_asr_deinit <= USED 0
;FILE btstack_uart_asr.o
;FILE btstack_uart_h5_asr.o
;FILE btstack_uart_slip_wrapper.o
;FILE btstack_util.o
btstack_clz <= USED 0
btstack_crc8_check <= USED 0
btstack_next_cid_ignoring_zero <= USED 0
btstack_time16_delta <= USED 0
btstack_virtual_memcpy <= USED 0
printf_hexdump <= USED 0
reverse_24 <= USED 0
reverse_256 <= USED 0
reverse_48 <= USED 0
sscanf_bd_addr <= USED 0
;FILE bufstats_decision.o
WebRtcNetEQ_BufstatsDecision <= USED 0
;FILE c1035pf.o
;FILE c2_11pf.o
;FILE c2_9pf.o
Test_build_code <= USED 0
Test_search_2i40 <= USED 0
;FILE c3_14pf.o
;FILE c4_17pf.o
;FILE c8_31pf.o
;FILE c_g_aver.o
;FILE c_ip.o
;FILE c_udp.o
;FILE cal_dc_scaler.o
;FILE calc_cor.o
;FILE calc_en.o
;FILE casaid.o
supl2cas_initc <= USED 0
supl2cas_mcc <= USED 0
;FILE cbsearch.o
;FILE cc_api.o
CC_SyncAudio <= USED 0
PARSER_SET_AUTO_ANSWER_DELAY <= USED 0
PARSER_START_RINGING <= USED 0
resetAllCalls <= USED 0
;FILE cc_api_mini.o
initCCContext <= USED 0
;FILE ccm.o
;FILE cellular.o
;FILE cellular_ps.o
;FILE cfg_center.o
config_center_init <= USED 0
config_read <= USED 0
config_write <= USED 0
;FILE cgpio.o
GPIO_INTERRUPT_TEST <= USED 0
GpioClearEdgeDetection_wakeup <= USED 0
GpioCommonWakeupCallback <= USED 0
GpioDisableEdgeDetection_wakeup <= USED 0
GpioEnableEdgeDetection_wakeup <= USED 0
GpioPhase1Init <= USED 0
dump_isr_count <= USED 0
getGpioLvl <= USED 0
gpio_isr <= USED 0
setGpioLvl <= USED 0
;FILE chap.o
;FILE chv_filter.o
CombinedHorzVertFilter <= USED 0
CombinedHorzVertFilter_NoSoftDeblocking <= USED 0
;FILE chvr_filter.o
CombinedHorzVertRingFilter <= USED 0
;FILE ci_api.o
ciShDeregisterReq_client <= USED 0
;FILE ci_client_task_ttc.o
;FILE ci_client_ttc.o
Bcd2String <= USED 0
ReleaseCiClientMem <= USED 0
ciRequest_Transfer_client <= USED 0
ciRespond_1_client <= USED 0
ciShDeregisterReq_1_client <= USED 0
ciShRegisterReq_1_client <= USED 0
ciShRequest_1_client <= USED 0
clearCciActionlist <= USED 0
clientCiDefConfirmCallback_client <= USED 0
clientCiSgFreeReqMem <= USED 0
clientCiShConfirmCallback_client <= USED 0
findCiSgId <= USED 0
string2Number <= USED 0
;FILE ci_mem.o
ciAdjustSacCiCnf <= USED 0
ciAdjustSacCiInd <= USED 0
ciAdjustSacCiReq <= USED 0
ciNegotiateSgCiVersion <= USED 0
ciSgCiVersionInit <= USED 0
cimem_CiSgAllocMem <= USED 0
cimem_CiSgCnfIndAllocMem <= USED 0
cimem_CiSgFreeMem <= USED 0
cimem_CiShAllocCnfMem <= USED 0
cimem_CiShAllocReqMem <= USED 0
cimem_CiShFreeCnfMem <= USED 0
cimem_GetCiNumericListDataSize <= USED 0
cimem_GetCiShCnfDataSize <= USED 0
cimem_GetCiShReqDataSize <= USED 0
;FILE ci_server_task_ttc.o
ACIPCDImsRxdefault <= USED 0
ACIPCDImsTxdefault <= USED 0
ciServerStubInit_Task <= USED 0
deleteCiServerInitTaskByTimer <= USED 0
getATRdyPhase2 <= USED 0
;FILE cid.o
;FILE cimodem.o
DataServiceResumed <= USED 0
Deactive_PDP_Context_by_Itself <= USED 0
LinkStatusIndCB <= USED 0
SendATCmdChars <= USED 0
ci_modem_PS_data_tx <= USED 0
ci_modem_clear_ind_apt_index <= USED 0
ci_modem_get_default_channel_index <= USED 0
ci_modem_init <= USED 0
ci_modem_rx <= USED 0
ci_modem_set_channel_data_stop_flag <= USED 0
ci_modem_setlog <= USED 0
mprintf <= USED 0
;FILE cimodem_usb.o
atmodemDataRxInd <= USED 0
atmodemDataRxInd_modem2 <= USED 0
usb_tx <= USED 0
;FILE cinit1.o
Arm946eDisableInterrupts <= USED 0
Arm946eEnableInterrupts <= USED 0
Arm946eMemcpy <= USED 0
Arm946eRestoreInterrupts <= USED 0
Cinit1 <= USED 0
Configure_RTC_WTC <= USED 0
DisableMFPRGSSPFunction <= USED 0
DynamicMemoryInit <= USED 0
EnableMFPRGSSPFunction <= USED 0
GetSiliconFoundry <= USED 0
IS_TDNewChip <= USED 0
IsCinit1Passed <= USED 0
IsPhase2Passed <= USED 0
MFPR_Config <= USED 0
TDChipIS_A0 <= USED 0
TDChipIS_A1 <= USED 0
TDChipIS_Y2 <= USED 0
WA_40LP_PLL1_stablize <= USED 0
copyRWtoImage <= USED 0
;FILE cinit2.o
Cinit2 <= USED 0
;FILE cipher.o
mbedtls_cipher_check_tag <= USED 0
mbedtls_cipher_info_from_string <= USED 0
mbedtls_cipher_list <= USED 0
mbedtls_cipher_update_ad <= USED 0
mbedtls_cipher_write_tag <= USED 0
;FILE cl_ltp.o
;FILE cmdintf.o
;FILE cmux_api.o
DetachCloseSocketReq <= USED 0
HexStrToByte <= USED 0
atNotifyFTPADone <= USED 0
cmuxConfigured <= USED 0
cmuxGetChannelIndexWithType <= USED 0
cmuxGetNetAtpIndexs <= USED 0
cmuxGetPdpInfo <= USED 0
cmuxGetPdpStatus <= USED 0
cmuxGetUrc <= USED 0
cmuxInitChannelConNum <= USED 0
cmuxInitConNum <= USED 0
cmuxIsDataTransMode <= USED 0
cmuxIsSocketBindChannel <= USED 0
cmuxSetCommandMode <= USED 0
cmuxSetFtpAtpIndex <= USED 0
cmuxSetNetSvcParam <= USED 0
cmuxSetPdpCfgp <= USED 0
cmuxSetPdpCfgt <= USED 0
cmuxSetUrc <= USED 0
cmuxUpdatePdpApnToAtcmd <= USED 0
cmuxWaitFTPPutA <= USED 0
cmux_set_ftp_resp_timeout <= USED 0
getFtpOpenCnf <= USED 0
get_cmux_channelCtrl <= USED 0
get_ipsec_test_enable <= USED 0
initMsgQRef <= USED 0
init_cmux_channelCtrl <= USED 0
init_e20_config <= USED 0
setFtpGetFileCnf <= USED 0
setFtpPutFileConnCnf <= USED 0
setFtpPutFileDataCnf <= USED 0
setFtpSizeCnf <= USED 0
setNetCloseCnf <= USED 0
set_ipsec_test_enable <= USED 0
;FILE cng.o
WebRtcNetEQ_CngUpdateState <= USED 0
WebRtcNetEQ_EvsCngUpdateState <= USED 0
;FILE cod_amr.o
cod_amr_first <= USED 0
;FILE codec_db.o
WebRtcNetEQ_DbAdd <= USED 0
WebRtcNetEQ_DbGetCodec <= USED 0
WebRtcNetEQ_DbGetPtrs <= USED 0
WebRtcNetEQ_DbRemove <= USED 0
;FILE comBasicCfg_nvm.o
comBasicCfgNvm_CREATE <= USED 0
comBasicCfgNvm_GetPM <= USED 0
;FILE combined_decode.o
;FILE commpm.o
CheckIfSoc32kFromRf26M <= USED 0
CommPMCP15D2Prepare <= USED 0
CommPMControlEnterD1state <= USED 0
CommPMD4Enter <= USED 0
CommPMFakeSleepEnable <= USED 0
CommPMGetGSMWakeupTime <= USED 0
CommPMGetWBWakeupTime <= USED 0
CommPMHardwareInit <= USED 0
CommPMInit <= USED 0
CommPMLPTInit <= USED 0
CommPMPowerdownUsbPhy <= USED 0
CommPMPowerupUsbPhy <= USED 0
DCXOLPMDisbale <= USED 0
DCXOLPMUnregister <= USED 0
DEBUG_ASSERT <= USED 0
DisableSleep <= USED 0
DumpCPADebugInfo <= USED 0
DumpFlashFlushDebugInfo <= USED 0
DumpFlashFlushInfo <= USED 0
DumpTimerThreadDebugInfo <= USED 0
EnableL1Register <= USED 0
ForceEndlessD2 <= USED 0
ForceMSADisableSleep <= USED 0
GetDDRLock <= USED 0
GetPMWakupEvent <= USED 0
Moh_PM_Init <= USED 0
Sea_PM_Init <= USED 0
SetMSASLPEN <= USED 0
SetTDRFPinFloating <= USED 0
SetTDRFPinPulldown <= USED 0
SetWakeupMaskForTDSCDMA <= USED 0
TriggerAsserttoDumpPowerRegs <= USED 0
UnsetWakeupMaskForTDSCDMA <= USED 0
dump_pmu_reg <= USED 0
mmiC1CallbackRegister <= USED 0
mmiD2CallbackRegister <= USED 0
mmiStatusCallbackRegister <= USED 0
print_Seagull_frequency <= USED 0
test_releaseApp_c <= USED 0
test_releaseComm_c <= USED 0
timer_thread_hook_before <= USED 0
;FILE commpm_debug.o
CommPMDDRStateDisplay <= USED 0
CommPMDDRStateSet <= USED 0
CommPMTestTask <= USED 0
CommPmDutyCycleConfigMultResults <= USED 0
CommPmDutyCycleDisplay <= USED 0
CommPmDutyCycleResetCount <= USED 0
CommPmDutyCycleStart <= USED 0
CommPmDutyCycleStop <= USED 0
KickWDTonD2Exit_ON <= USED 0
KickWDTonD2Exit_Off <= USED 0
PMD1controlStruct <= USED 0
PMD1disable <= USED 0
PMD1enable <= USED 0
PMD1status <= USED 0
PMSetToDDRFullFuncIsOFF <= USED 0
PMSetToDDRFullFuncIsON <= USED 0
PMdisplayMode <= USED 0
PMsetAllowC1 <= USED 0
PMsetC1 <= USED 0
PMsetD2Full <= USED 0
PMsetD2_C1fullSA <= USED 0
PMsetD2_D0fullSA <= USED 0
PMsetD2alike <= USED 0
PMsetD2alikeSA <= USED 0
PMsetD2fullSA <= USED 0
PMsetD2mode <= USED 0
PMsetDebugWaitTicks <= USED 0
PMsetLPTdecisionOnly <= USED 0
PMsetLPTidle <= USED 0
PMsetLPTsleepTicksStruct <= USED 0
PMsetNotifyMSA <= USED 0
SetEndlessD2 <= USED 0
SetLoopD2DDRgrant <= USED 0
SetLoopD2DDRgrantStruct <= USED 0
SetLoopD2Exit <= USED 0
SetLoopD2ExitStruct <= USED 0
TestParamsDisplay <= USED 0
TestParamsSet <= USED 0
perf_cnt_begin <= USED 0
perf_cnt_stop <= USED 0
setMaxDDRAckDuration <= USED 0
test_sample <= USED 0
;FILE comp_ip_id_offset.o
rohc_comp_detect_ip_id_behavior <= USED 0
;FILE comp_list.o
;FILE comp_list_ipv6.o
;FILE comp_rfc4996.o
c_field_scaling <= USED 0
c_optional_ip_id_lsb <= USED 0
c_static_or_irreg16 <= USED 0
c_static_or_irreg32 <= USED 0
c_static_or_irreg8 <= USED 0
c_zero_or_irreg16 <= USED 0
c_zero_or_irreg32 <= USED 0
dscp_encode <= USED 0
rsf_index_enc <= USED 0
rsf_index_enc_possible <= USED 0
tcp_is_ack_scaled_possible <= USED 0
tcp_is_ack_stride_static <= USED 0
variable_length_32_enc <= USED 0
;FILE comp_scaled_rtp_ts.o
;FILE comp_wlsb.o
wlsb_ack <= USED 0
wlsb_get_k_8bits <= USED 0
wlsb_get_kp_16bits <= USED 0
wlsb_get_kp_8bits <= USED 0
wlsb_is_kp_possible_16bits <= USED 0
wlsb_is_kp_possible_32bits <= USED 0
wlsb_is_kp_possible_8bits <= USED 0
;FILE conceal.o
;FILE configReader.o
;FILE connect_management.o
CM_DeactiveDataCallAllDone <= USED 0
CM_DialerStatus <= USED 0
CM_DiscardMsg <= USED 0
CM_GetDefaultApnName <= USED 0
CM_GetDefaultConnectStatus <= USED 0
CM_GetRoamingStatus <= USED 0
CM_Get_ConnectionNum <= USED 0
CM_InitDataOnRoaming <= USED 0
CM_RegisterIpChangedCb <= USED 0
CM_SendAndCompareCgdcontApn <= USED 0
CM_SetConnectionSwitchNoPersist <= USED 0
CM_SetConnectionSwitchOnlyNV <= USED 0
CM_Set_ConnectType_WEBUI <= USED 0
CM_SwitchSimDone <= USED 0
CM_init <= USED 0
convertCmConnStateToString <= USED 0
getDualSimType <= USED 0
getRadioPowerFunc <= USED 0
resetDefaultCidReplaceFlag <= USED 0
setDualSimType <= USED 0
setMasterSimID <= USED 0
;FILE convert.o
;FILE convolve.o
;FILE copy.o
;FILE copy_set_operations.o
;FILE cor_h.o
;FILE cor_h_x.o
;FILE cor_h_x2.o
;FILE crane_ds_mpu.o
crane_ds_mpu_ext_flash_enable <= USED 0
;FILE crc.o
compute_crc_ctrl_fields <= USED 0
;FILE crc32.o
;FILE crossPlatformSW.o
FDI_GetFreeSpaceSize <= USED 0
FDI_GetUsedSpaceSize <= USED 0
FDI_IsFormatted <= USED 0
FDI_eof <= USED 0
crossPlatformSWPhase1Init <= USED 0
crossPlatformSWPhaseAfterAllNvmInit <= USED 0
disable_nvm_flush <= USED 0
enable_nvm_flush <= USED 0
fs_mode_is_ramfs <= USED 0
fs_mode_is_tfs <= USED 0
get_fs_mode <= USED 0
set_fs_mode <= USED 0
;FILE csw_mem.o
PhysicalToVirtualNonCached <= USED 0
VirtualToPhysical <= USED 0
align32Malloc <= USED 0
alignFree <= USED 0
dtcmHeapInit <= USED 0
extMemHeapInit <= USED 0
intMemHeapInit <= USED 0
memClean <= USED 0
memPoolX_Init <= USED 0
romAlloc <= USED 0
romFree <= USED 0
;FILE ctr_drbg.o
mbedtls_ctr_drbg_set_entropy_len <= USED 0
mbedtls_ctr_drbg_set_prediction_resistance <= USED 0
mbedtls_ctr_drbg_set_reseed_interval <= USED 0
mbedtls_ctr_drbg_update <= USED 0
mbedtls_ctr_drbg_update_ret <= USED 0
;FILE d1035pf.o
;FILE d2_11pf.o
;FILE d2_9pf.o
;FILE d3_14pf.o
;FILE d4_17pf.o
;FILE d8_31pf.o
;FILE d_gain_c.o
;FILE d_gain_p.o
;FILE d_ip.o
;FILE d_plsf.o
;FILE d_plsf_3.o
;FILE d_plsf_5.o
;FILE d_udp.o
;FILE datapart_decode.o
;FILE dcac_prediction.o
;FILE dcxoControll.o
CreateDcxoTempCompensationNvmFile <= USED 0
DcxoCalibration_CreateNVMFile <= USED 0
DcxoCalibration_PrintCalibdata <= USED 0
DcxoIpcDspMsgIpcHandler <= USED 0
DcxoTempCompensation_CreateNVMFile <= USED 0
DcxoTempCompensation_PrintData <= USED 0
DcxoTempCompensation_WriteNVMFile <= USED 0
SetRfDcxoIsUsingFlgWithGPIO <= USED 0
plDcxoProcessModeReport <= USED 0
plDcxoSendRfTemCompenLutToPlp <= USED 0
plDcxoSetDspToDcxoMode <= USED 0
plDcxoSetRfTempretureReadMode <= USED 0
polyfitforcomp <= USED 0
;FILE dec.o
DecodeGSMSPDUDataToSMSText <= USED 0
;FILE dec_amr.o
;FILE dec_gain.o
;FILE dec_lag3.o
;FILE dec_lag6.o
;FILE dec_pred_intra_dc.o
;FILE decomp_ip_id_offset.o
;FILE decomp_list.o
;FILE decomp_list_ipv6.o
;FILE decomp_scaled_rtp_ts.o
;FILE decomp_wlsb.o
rohc_lsb_is_ready <= USED 0
;FILE def.o
lwip_itoa <= USED 0
lwip_mac_vlan_pton <= USED 0
lwip_stricmp <= USED 0
lwip_strnstr <= USED 0
;FILE deringing_chroma.o
Deringing_Chroma <= USED 0
;FILE deringing_luma.o
Deringing_Luma <= USED 0
;FILE des.o
mbedtls_des_key_check_key_parity <= USED 0
mbedtls_des_key_check_weak <= USED 0
mbedtls_des_key_set_parity <= USED 0
;FILE dev_api.o
DEV_GetEngineModeInfo <= USED 0
DEV_GetIMLConfigReq <= USED 0
DEV_GetLPECID <= USED 0
DEV_GetLPOtdoaReq <= USED 0
DEV_GetMccMncCcReq <= USED 0
DEV_GetRsrpReq <= USED 0
DEV_GetRsrqReq <= USED 0
DEV_GetRssiReq <= USED 0
DEV_GetSinrReq <= USED 0
DEV_SetIMLConfigReq <= USED 0
DEV_SetLPOtdoaAbort <= USED 0
DEV_SetMccMncCcReq <= USED 0
DEV_setLPNWUL <= USED 0
GetCfunValue <= USED 0
GlbGetFuncConf <= USED 0
GlbSetFuncConf <= USED 0
SetCfunValue <= USED 0
SysSleepProcessSetFunc <= USED 0
_processWifiHotspotInfoInd <= USED 0
sim_libConvertNumToHexString <= USED 0
wait_cfun_done <= USED 0
;FILE dev_api_mini.o
resetDevParas <= USED 0
;FILE device_cus.o
GetBatteryVoltage <= USED 0
GetFmChipId <= USED 0
cus_get_backlight_gpio <= USED 0
cus_get_device_board_id <= USED 0
cus_get_lcd_reset_gpio <= USED 0
cus_get_lcd_spi_4line_dcx_gpio <= USED 0
cus_get_lcd_spi_cs_gpio <= USED 0
cus_is_board_cranel_evb <= USED 0
cus_is_board_cranel_evb_a0 <= USED 0
cus_is_board_cranelr_evb_a0 <= USED 0
;FILE device_id_server.o
;FILE dhcp.o
dhcp_cleanup <= USED 0
dhcp_inform <= USED 0
dhcp_set_struct <= USED 0
dhcp_start <= USED 0
;FILE dhcp6.o
dhcp6_client_tmr <= USED 0
dhcp6_find_opt <= USED 0
dhcp6_reply_opt_dns <= USED 0
dhcp6_reply_opt_domain <= USED 0
dhcp6_reply_opt_xid <= USED 0
dhcp6_start <= USED 0
;FILE dhcp6d.o
dhcp6d_router_dns6 <= USED 0
dhcpv6_dump_packet <= USED 0
;FILE dhcp_cntrl.o
dhcp_HdlInit <= USED 0
dhcp_HdlShutdown <= USED 0
frame_generic_packet <= USED 0
;FILE dhcp_interface.o
;FILE dhcpd.o
dhcpd_check_dongle_dns1 <= USED 0
dhcpd_check_dongle_dns2 <= USED 0
dhcpd_check_dongle_gw <= USED 0
dhcpd_check_dongle_mask <= USED 0
dhcpd_get_opt43_flag <= USED 0
dhcpd_release_manual <= USED 0
dhcpd_reset_netif_ip <= USED 0
dhcpd_set_opt43_flag <= USED 0
;FILE dhm.o
mbedtls_dhm_calc_secret <= USED 0
mbedtls_dhm_make_params <= USED 0
mbedtls_dhm_make_public <= USED 0
mbedtls_dhm_parse_dhm <= USED 0
mbedtls_dhm_read_params <= USED 0
mbedtls_dhm_read_public <= USED 0
mbedtls_dhm_set_group <= USED 0
;FILE di2stub.o
di_bufferCreated <= USED 0
di_bufferDestroyed <= USED 0
di_displayLine <= USED 0
di_getInfo <= USED 0
di_getPixel <= USED 0
di_invalidateRect <= USED 0
di_powerDown <= USED 0
di_reset <= USED 0
di_setContrast <= USED 0
di_setPixel <= USED 0
di_updateDisplay <= USED 0
;FILE diagDB.o
__trace_ACIPC__ACIPCD__tcInit3 <= USED 0
__trace_ADEMO__DIAGTHRPUT__Enable <= USED 0
__trace_ADEMO__RESET__PrintfResetStatus1 <= USED 0
__trace_ADEMO__START_PS__startCommInit1 <= USED 0
__trace_ADEMO__TCRu64__TimerCountReadU64 <= USED 0
__trace_APLP__AM__l1GsmCheckDisableD2inSetGsmSimb <= USED 0
__trace_APLP__AM__l1GsmCheckEnableD2inSetGsmSimb <= USED 0
__trace_APLP__MSR__MSR_IPC_ERR1 <= USED 0
__trace_ATCMD__Debug__DumpCtrl_1 <= USED 0
__trace_AUDIO__ACMCOMM__DisableMSASleep <= USED 0
__trace_AUDIO__ACMCOMM__ENSABLEMSASLEEP <= USED 0
__trace_AUDIO__ACMCOMM__Switch_ConfirmProcID <= USED 0
__trace_AUDIO__ACM_STREAM__ACMAudioStreamOutStart_0 <= USED 0
__trace_AUDIO__ACM_STREAM__ACMAudioStreamOutStart_Error <= USED 0
__trace_AUDIO__ACM_STREAM__ACMAudioStreamOutStart_end <= USED 0
__trace_AUDIO__ACM__ACMAudioBindECallGetHandler <= USED 0
__trace_AUDIO__ACM__ACMAudioDTMF_Start_Volume_scan <= USED 0
__trace_AUDIO__ACM__ACMAudioDTMF_mix_switch <= USED 0
__trace_AUDIO__ACM__ACMAudioOutGainSet <= USED 0
__trace_AUDIO__ACM__ACMAudioSetDTMFMode <= USED 0
__trace_AUDIO__ACM__ACMDspIfInvalidateDspModes <= USED 0
__trace_AUDIO__ACM__ACMEnableDataDump <= USED 0
__trace_AUDIO__ACM__ACMVoiceInGainSet <= USED 0
__trace_AUDIO__ACM__ACMVoiceMuteIn <= USED 0
__trace_AUDIO__ACM__ACMVoiceMuteOut <= USED 0
__trace_AUDIO__ACM__ACMVoiceOutGainSet <= USED 0
__trace_AUDIO__ACM__ACMVoiceSideToneGainSet <= USED 0
__trace_AUDIO__ACM__AcmSetPCMRate <= USED 0
__trace_AUDIO__ACM__AcmSetPCMRate_fail <= USED 0
__trace_AUDIO__ACM__AudioHAL_AifDTMF_error <= USED 0
__trace_AUDIO__ACM__BindGetVoiceDataCallback_Failed_1 <= USED 0
__trace_AUDIO__ACM__BindGetVoiceDataCallback_Failed_2 <= USED 0
__trace_AUDIO__ACM__BindGetVoiceDataCallback_OK <= USED 0
__trace_AUDIO__ACM__DTMFTimer_Start <= USED 0
__trace_AUDIO__ACM__DTMFTone <= USED 0
__trace_AUDIO__ACM__DTMFTone_WrongCookie <= USED 0
__trace_AUDIO__ACM__GSSPRead_WrongCookie <= USED 0
__trace_AUDIO__ACM__GSSP_SEL_BY_CP_dump <= USED 0
__trace_AUDIO__ACM__GetVoiceDataCallback <= USED 0
__trace_AUDIO__ACM__OEMAudio_SetVoiceData <= USED 0
__trace_AUDIO__ACM__OEMAudio_SetVoiceData_fail <= USED 0
__trace_AUDIO__ACM__OEMTest_TestSetVoiceData <= USED 0
__trace_AUDIO__ACM__POCVoice_BindAMRHandlers <= USED 0
__trace_AUDIO__ACM__POCVoice_BindAMRHandlers_Failed <= USED 0
__trace_AUDIO__ACM__POCVoice_BindAMRHandlers_OK <= USED 0
__trace_AUDIO__ACM__POCVoice_BindAMRHandlers_inuse <= USED 0
__trace_AUDIO__ACM__POCVoice_BindAMR_check_fail <= USED 0
__trace_AUDIO__ACM__POCVoice_BindPCMRecord <= USED 0
__trace_AUDIO__ACM__POCVoice_BindPCMRecord_OK <= USED 0
__trace_AUDIO__ACM__POCVoice_BindPCMRecord_failed <= USED 0
__trace_AUDIO__ACM__POCVoice_BindPCMRecord_inuse <= USED 0
__trace_AUDIO__ACM__POCVoice_ConfigPlay2FarEnd <= USED 0
__trace_AUDIO__ACM__POCVoice_ConfigPlay2NearEnd <= USED 0
__trace_AUDIO__ACM__POCVoice_ConfigRecord <= USED 0
__trace_AUDIO__ACM__POCVoice_ConfigureAMR <= USED 0
__trace_AUDIO__ACM__POCVoice_POCVoice_UnBindAMRHandlers_Failed <= USED 0
__trace_AUDIO__ACM__POCVoice_POCVoice_UnBindAMRHandlers_OK <= USED 0
__trace_AUDIO__ACM__POCVoice_UnBindAMRHandlers <= USED 0
__trace_AUDIO__ACM__POCVoice_UnBindAny <= USED 0
__trace_AUDIO__ACM__POCVoice_UnBindPCMRecord <= USED 0
__trace_AUDIO__ACM__POCVoice_UnBindPCMRecord_OK <= USED 0
__trace_AUDIO__ACM__POCVoice_UnBindPCMRecord_failed <= USED 0
__trace_AUDIO__ACM__POCVoice_record_res_init_failed <= USED 0
__trace_AUDIO__ACM__SendPCMToAP <= USED 0
__trace_AUDIO__ACM__UnBindGetVoiceDataCallback_Failed_1 <= USED 0
__trace_AUDIO__ACM__UnBindGetVoiceDataCallback_Failed_2 <= USED 0
__trace_AUDIO__ACM__UnBindGetVoiceDataCallback_OK <= USED 0
__trace_AUDIO__ACM__dump_audio_config <= USED 0
__trace_AUDIO__ACM__getVoiceMix <= USED 0
__trace_AUDIO__ACM__getVoiceRx <= USED 0
__trace_AUDIO__ACM__getVoiceTx <= USED 0
__trace_AUDIO__ACM__poc_record_handler_reinit_res_fail <= USED 0
__trace_AUDIO__ACM__setPOCBindType <= USED 0
__trace_AUDIO__ACM__voiceDataRx_resmple_init_failed <= USED 0
__trace_AUDIO__ACM__voiceDataTx_resmple_init_failed <= USED 0
__trace_AUDIO__ACM__voiceData_reample_destroy <= USED 0
__trace_AUDIO__ACM__voiceData_reample_init <= USED 0
__trace_AUDIO__ACM__voiceData_reample_init_OK <= USED 0
__trace_AUDIO__ATC__ATCDTMFControl_API <= USED 0
__trace_AUDIO__ATC__ATCDTMFControl_Error <= USED 0
__trace_AUDIO__ATC__ATCDTMFDetectionCtrl_API <= USED 0
__trace_AUDIO__ATC__ATCECallCtrl_API <= USED 0
__trace_AUDIO__ATC__ATCECallCtrl_API_NG <= USED 0
__trace_AUDIO__ATC__ATCECallCtrl_Env <= USED 0
__trace_AUDIO__ATC__ATCEQCtrl_API <= USED 0
__trace_AUDIO__ATC__ATCLoopbackCtrl_1 <= USED 0
__trace_AUDIO__ATC__ATCLoopbackCtrl_API <= USED 0
__trace_AUDIO__ATC__ATCLoopbackCtrl_NG0 <= USED 0
__trace_AUDIO__ATC__ATCPathCtrl_API <= USED 0
__trace_AUDIO__ATC__ATCVolumeCtrl_API <= USED 0
__trace_AUDIO__ATC__AifSetPhoneStatus <= USED 0
__trace_AUDIO__ATC__VolumeSet_DSPSideToneDisabled <= USED 0
__trace_AUDIO__ATC__VolumeSet_NotFound <= USED 0
__trace_AUDIO__ATC__set_phone_state <= USED 0
__trace_AUDIO__ATC__set_phone_state_done <= USED 0
__trace_AUDIO__AudioInit__ecallApp_Init <= USED 0
__trace_AUDIO__Calibration__ACMCalibration_GetVEParam <= USED 0
__trace_AUDIO__Calibration__ACMCalibration_GetVEParam_NG <= USED 0
__trace_AUDIO__Calibration__GetCodecGainParam <= USED 0
__trace_AUDIO__Calibration__GetCodecGainParam_NG <= USED 0
__trace_AUDIO__Calibration__GetMediaVEParam <= USED 0
__trace_AUDIO__Calibration__GetMediaVEParam_NG <= USED 0
__trace_AUDIO__Calibration__test_GetCodecGainParam <= USED 0
__trace_AUDIO__Calibration__test_GetCodecGainParam_2 <= USED 0
__trace_AUDIO__Calibration__test_GetVEParam <= USED 0
__trace_AUDIO__Calibration__test_GetVEParam_2 <= USED 0
__trace_AUDIO__Calibration__test_GetVMediaEParam <= USED 0
__trace_AUDIO__Calibration__test_GetVMediaEParam_2 <= USED 0
__trace_AUDIO__HAL__AifBindHeadsetDetectionCB <= USED 0
__trace_AUDIO__HAL__AifBindHeadsetDetectionCB_fail <= USED 0
__trace_AUDIO__HAL__AifConfigure <= USED 0
__trace_AUDIO__HAL__AifGetOpenedDevice <= USED 0
__trace_AUDIO__HAL__AifHeadsetDetection <= USED 0
__trace_AUDIO__HAL__AifPause <= USED 0
__trace_AUDIO__HAL__AifPauseErr <= USED 0
__trace_AUDIO__HAL__AifSetMute <= USED 0
__trace_AUDIO__HAL__AifSetSideTone <= USED 0
__trace_AUDIO__HAL__AifSetSideTone1 <= USED 0
__trace_AUDIO__HAL__AifSetSideTone_Err1 <= USED 0
__trace_AUDIO__HAL__AifSetSideTone_Err2 <= USED 0
__trace_AUDIO__HAL__AifSetSideTone_Fixed <= USED 0
__trace_AUDIO__HAL__AifTone <= USED 0
__trace_AUDIO__HAL__AifTonePause <= USED 0
__trace_AUDIO__HAL__AudioHAL_AifBindCodec_CB <= USED 0
__trace_AUDIO__HAL__AudioHAL_AifBindSpeakerPA_CB <= USED 0
__trace_AUDIO__HAL__AudioHAL_AifBind_BTEvent_CB <= USED 0
__trace_AUDIO__HAL__AudioHAL_AifBitClkControl <= USED 0
__trace_AUDIO__HAL__AudioHAL_AifBitClkControl_err <= USED 0
__trace_AUDIO__HAL__AudioHAL_AifControlMCLK <= USED 0
__trace_AUDIO__HAL__AudioHAL_AifControlMCLK_fail <= USED 0
__trace_AUDIO__HAL__AudioHAL_AifControlMCLK_on <= USED 0
__trace_AUDIO__HAL__AudioHAL_AifDTMF <= USED 0
__trace_AUDIO__HAL__AudioHAL_AifGetHeadsetInfo <= USED 0
__trace_AUDIO__HAL__AudioHAL_AifGetTxCodecGainStage1 <= USED 0
__trace_AUDIO__HAL__AudioHAL_AifGetTxCodecGainStage2 <= USED 0
__trace_AUDIO__HAL__AudioHAL_AifGetTxDSPGain <= USED 0
__trace_AUDIO__HAL__AudioHAL_AifGetVolume <= USED 0
__trace_AUDIO__HAL__AudioHAL_AifSetDevicePort <= USED 0
__trace_AUDIO__HAL__AudioHAL_AifSetDspRxMute <= USED 0
__trace_AUDIO__HAL__AudioHAL_AifSetTxCodecGain <= USED 0
__trace_AUDIO__HAL__AudioHAL_AifSetTxDSPGain <= USED 0
__trace_AUDIO__HAL__AudioHAL_AifSet_VAD_not_support <= USED 0
__trace_AUDIO__HAL__AudioHAL_AudioHAL_sspaSetDelay_error <= USED 0
__trace_AUDIO__HAL__AudioHAL_DRCReset <= USED 0
__trace_AUDIO__HAL__AudioHAL_FadingCtl <= USED 0
__trace_AUDIO__HAL__AudioHAL_PlaybackPause <= USED 0
__trace_AUDIO__HAL__AudioHAL_SetFadingStep <= USED 0
__trace_AUDIO__HAL__AudioHAL_SetResBufCnt <= USED 0
__trace_AUDIO__HAL__AudioHAL_Switch_close_delay <= USED 0
__trace_AUDIO__HAL__AudioHAL_Switch_record_outputRate <= USED 0
__trace_AUDIO__HAL__AudioHAL_TestBindSpkPA <= USED 0
__trace_AUDIO__HAL__AudioHAL_TestBindSpkPA_craneG <= USED 0
__trace_AUDIO__HAL__AudioHAL_TestBindSpkPA_craneL <= USED 0
__trace_AUDIO__HAL__AudioHAL_TestBindSpkPA_maybeCraneM <= USED 0
__trace_AUDIO__HAL__AudioHAL_TestBindSpkPA_remove_external_pa <= USED 0
__trace_AUDIO__HAL__AudioHAL_TestDRCReset <= USED 0
__trace_AUDIO__HAL__AudioHAL_TestSetTxGain <= USED 0
__trace_AUDIO__HAL__AudioHAL_classgDCAlignSet <= USED 0
__trace_AUDIO__HAL__AudioHAL_set_close_delay <= USED 0
__trace_AUDIO__HAL__AudioHAL_sspaSetBCLKInvert <= USED 0
__trace_AUDIO__HAL__AudioHAL_sspaSetBCLKType <= USED 0
__trace_AUDIO__HAL__AudioHAL_sspaSetBCLKType_error <= USED 0
__trace_AUDIO__HAL__AudioHAL_sspaSetDelay <= USED 0
__trace_AUDIO__HAL__AudioHAL_sspaSetFrameClkInvert <= USED 0
__trace_AUDIO__HAL__AudioHAL_sspaSetFrameClkWidth <= USED 0
__trace_AUDIO__HAL__AudioHAL_sspaSetFrameClkWidth_error <= USED 0
__trace_AUDIO__HAL__AudioHAL_sspaSetI2S <= USED 0
__trace_AUDIO__HAL__AudioHAL_sspaSetSlave <= USED 0
__trace_AUDIO__HAL__AudioHAL_sspaTestMode <= USED 0
__trace_AUDIO__HAL__AudioHAL_sspa_test_mode <= USED 0
__trace_AUDIO__HAL__AudioHAL_start_bt_hfp_hfMode <= USED 0
__trace_AUDIO__HAL__AudioHAL_start_bt_hfp_hfMode_error1 <= USED 0
__trace_AUDIO__HAL__AudioHAL_start_bt_hfp_hfMode_error2 <= USED 0
__trace_AUDIO__HAL__AudioHAL_start_bt_hfp_hfMode_inVoiceCall <= USED 0
__trace_AUDIO__HAL__AudioHAL_switch_FrameClk_invert <= USED 0
__trace_AUDIO__HAL__AudioHAL_switch_FrameClk_width <= USED 0
__trace_AUDIO__HAL__AudioHAL_switch_PCM_I2S <= USED 0
__trace_AUDIO__HAL__AudioHAL_switch_bclk_fs <= USED 0
__trace_AUDIO__HAL__AudioHAL_switch_bclk_invert <= USED 0
__trace_AUDIO__HAL__AudioHAL_switch_mic <= USED 0
__trace_AUDIO__HAL__AudioHAL_switch_sspa_slave <= USED 0
__trace_AUDIO__HAL__AudioHAL_test_adaptor <= USED 0
__trace_AUDIO__HAL__AudioHAL_vibration_control <= USED 0
__trace_AUDIO__HAL__AudioHAL_vibration_control_already_close <= USED 0
__trace_AUDIO__HAL__AudioHAL_vibration_control_already_open <= USED 0
__trace_AUDIO__HAL__AudioHAL_vibration_setFreq <= USED 0
__trace_AUDIO__HAL__AudioHAL_vibration_setFreq_error <= USED 0
__trace_AUDIO__HAL__AudioHAL_vibration_setFreq_found <= USED 0
__trace_AUDIO__HAL__AudioHAL_vibration_setGain <= USED 0
__trace_AUDIO__HAL__AudioHAL_vibration_setHfpMode <= USED 0
__trace_AUDIO__HAL__AudioHal_AifPlay2Farend <= USED 0
__trace_AUDIO__HAL__PCMControl <= USED 0
__trace_AUDIO__HAL__PCMControlErr <= USED 0
__trace_AUDIO__HAL__POCVoice_ConfigRecod_switch <= USED 0
__trace_AUDIO__HAL__POCVoice_DTMF_Button <= USED 0
__trace_AUDIO__HAL__POCVoice_DTMF_test <= USED 0
__trace_AUDIO__HAL__POCVoice_DTMF_test2 <= USED 0
__trace_AUDIO__HAL__POCVoice_PCMPlayConfig <= USED 0
__trace_AUDIO__HAL__POCVoice_tone_test <= USED 0
__trace_AUDIO__HAL__RequestCodecReport_Err <= USED 0
__trace_AUDIO__HAL__TestControl <= USED 0
__trace_AUDIO__HAL__TestLoopback <= USED 0
__trace_AUDIO__HAL__TestMediaLoopback <= USED 0
__trace_AUDIO__HAL__TestMediaLoopback_close <= USED 0
__trace_AUDIO__HAL__TestMediaLoopback_drain_return <= USED 0
__trace_AUDIO__HAL__TestPlay_HalfHandler_mediaLoop_data <= USED 0
__trace_AUDIO__HAL__TestRecord_HalfHandler_mediaLoop <= USED 0
__trace_AUDIO__HAL__TestRecord_HalfHandler_mediaLoop_data <= USED 0
__trace_AUDIO__HAL__TestRequestCodecReport <= USED 0
__trace_AUDIO__HAL__TestSetBufCnt <= USED 0
__trace_AUDIO__HAL__TestSetMute <= USED 0
__trace_AUDIO__HAL__TestSetVolume <= USED 0
__trace_AUDIO__HAL__TestWriteCodec <= USED 0
__trace_AUDIO__HAL__VoiceRecord <= USED 0
__trace_AUDIO__HAL__VoiceRecord_close <= USED 0
__trace_AUDIO__HAL__audio_add_extra_process <= USED 0
__trace_AUDIO__HAL__audio_add_extra_process_OK <= USED 0
__trace_AUDIO__HAL__audio_add_extra_process_already_exist <= USED 0
__trace_AUDIO__HAL__audio_add_extra_process_fail <= USED 0
__trace_AUDIO__HAL__audio_remove_extra_process <= USED 0
__trace_AUDIO__HAL__audio_remove_extra_process_OK <= USED 0
__trace_AUDIO__HAL__audio_remove_extra_process_fail <= USED 0
__trace_AUDIO__HAL__dump_g_AudioConfigTable <= USED 0
__trace_AUDIO__HAL__gpio13_output_pa_enable <= USED 0
__trace_AUDIO__HAL__gpio13_output_pa_enable_error_1 <= USED 0
__trace_AUDIO__HAL__gpio13_output_pa_enable_error_3 <= USED 0
__trace_AUDIO__HAL__gpio13_pa_gpio_init_error2 <= USED 0
__trace_AUDIO__HAL__gpio28_output_pa_enable <= USED 0
__trace_AUDIO__HAL__gpio28_output_pa_enable_error_1 <= USED 0
__trace_AUDIO__HAL__gpio28_output_pa_enable_error_3 <= USED 0
__trace_AUDIO__HAL__gpio28_pa_gpio_init_error2 <= USED 0
__trace_AUDIO__HAL__gpio75_output_pa_enable <= USED 0
__trace_AUDIO__HAL__gpio75_output_pa_enable_error_1 <= USED 0
__trace_AUDIO__HAL__gpio75_output_pa_enable_error_3 <= USED 0
__trace_AUDIO__HAL__gpio75_pa_gpio_init_error2 <= USED 0
__trace_AUDIO__HAL__headset_callback <= USED 0
__trace_AUDIO__HAL__headset_callback_already_bind <= USED 0
__trace_AUDIO__HAL__headset_callback_iter <= USED 0
__trace_AUDIO__HAL__ningbo_update_bit_error <= USED 0
__trace_AUDIO__HAL__ningbo_update_bit_error_2 <= USED 0
__trace_AUDIO__HAL__testBufferInit <= USED 0
__trace_AUDIO__HAL__testBufferInit_error1 <= USED 0
__trace_AUDIO__HAL__testBufferInit_error2 <= USED 0
__trace_AUDIO__HAL__testBufferInit_error3 <= USED 0
__trace_AUDIO__HAL__test_BitClkControl <= USED 0
__trace_AUDIO__HAL__test_MclkControl <= USED 0
__trace_AUDIO__HAL__test_set_MediaVEIndex <= USED 0
__trace_AUDIO__HAL__test_switch_BTHFP_HFMode <= USED 0
__trace_AUDIO__HAL__tg_pcm_open_buffer <= USED 0
__trace_AUDIO__HAL__tg_pcm_play_buffer_init <= USED 0
__trace_AUDIO__HAL__tg_pcm_record_buffer_init <= USED 0
__trace_AUDIO__NETEQ__WebRtcNetEQ_BufstatsDecision_1 <= USED 0
__trace_AUDIO__NETEQ__WebRtcNetEQ_BufstatsDecision_CngUpdateMissed <= USED 0
__trace_AUDIO__NETEQ__WebRtcNetEQ_SignalMcu_NewCodecOrReinit <= USED 0
__trace_AUDIO__NETEQ__WebRtcNetEQ_SignalMcu_NewCodecWorkaround <= USED 0
__trace_AUDIO__NETEQ__WebRtcNetEQ_SignalMcu_NoPacketFoundButRequired <= USED 0
__trace_AUDIO__NETEQ__WebRtcNetEQ_SignalMcu_SamplesOverflowCheck <= USED 0
__trace_AUDIO__NETEQ__WorkaroundForAquila1826 <= USED 0
__trace_AUDIO__OEMTest__Audio_production_mode_test <= USED 0
__trace_AUDIO__OEMTest__Audio_production_result_get_test <= USED 0
__trace_AUDIO__OEMTest__Loopback <= USED 0
__trace_AUDIO__OEMTest__LoopbackDump <= USED 0
__trace_AUDIO__OEMTest__OEMTest_AMRRxHandler <= USED 0
__trace_AUDIO__OEMTest__OEMTest_AMRTxHandler <= USED 0
__trace_AUDIO__OEMTest__OEMTest_DTMFDetectionHandler <= USED 0
__trace_AUDIO__OEMTest__OEMTest_LoopbackRxHandler <= USED 0
__trace_AUDIO__OEMTest__OEMTest_LoopbackRxHandler_2 <= USED 0
__trace_AUDIO__OEMTest__OEMTest_buff_init <= USED 0
__trace_AUDIO__PCA_API__DisableHwPath <= USED 0
__trace_AUDIO__PCA_API__DisablePhonePcm <= USED 0
__trace_AUDIO__PCA_API__EnableHwPath <= USED 0
__trace_AUDIO__PCA_API__EnablePhonePcm <= USED 0
__trace_AUDIO__PCA_API__GetFarSideMute <= USED 0
__trace_AUDIO__PCA_API__GetNearSideMute <= USED 0
__trace_AUDIO__PCA_API__LteVoiceResume <= USED 0
__trace_AUDIO__PCA_API__LteVoiceSuspend <= USED 0
__trace_AUDIO__PCA_API__RegisterCallback <= USED 0
__trace_AUDIO__PCA_API__SetAudioVoLTE <= USED 0
__trace_AUDIO__PCA_API__SetAudioVoLTE_CallNotActive <= USED 0
__trace_AUDIO__PCA_API__SetIPCControlErr1 <= USED 0
__trace_AUDIO__PCA_API__SetIPCControlErr2 <= USED 0
__trace_AUDIO__PCA_API__SetIPCControl_API <= USED 0
__trace_AUDIO__PCA_API__SetIPCControl_CMD <= USED 0
__trace_AUDIO__PCA_API__SetSspConfiguration_Update <= USED 0
__trace_AUDIO__PCA_API__SetSspConfiguration_msg <= USED 0
__trace_AUDIO__PCA_API__SetStreamPriority <= USED 0
__trace_AUDIO__PCA_API__StartVoIPAudio <= USED 0
__trace_AUDIO__PCA_API__StartVoIPAudio_ignore <= USED 0
__trace_AUDIO__PCA_API__StopVoIPAudio <= USED 0
__trace_AUDIO__PCA_API__StopVoIPAudio_ignore <= USED 0
__trace_AUDIO__PCA_API__audioRATResume_ignore <= USED 0
__trace_AUDIO__PCA_API__audioRATSuspend_ignore <= USED 0
__trace_AUDIO__POC__poc_record_handler <= USED 0
__trace_AUDIO__POC__record_handler_reinit_res <= USED 0
__trace_AUDIO__RAT__audioBindGetRAT <= USED 0
__trace_AUDIO__RECORD__acm_audio_record_config <= USED 0
__trace_AUDIO__RECORD__acm_audio_record_debug <= USED 0
__trace_AUDIO__RECORD__acm_audio_record_free <= USED 0
__trace_AUDIO__RECORD__acm_audio_record_get <= USED 0
__trace_AUDIO__RECORD__acm_audio_record_get_available <= USED 0
__trace_AUDIO__RECORD__acm_audio_record_get_framesize <= USED 0
__trace_AUDIO__RECORD__acm_audio_record_get_softgain <= USED 0
__trace_AUDIO__RECORD__acm_audio_record_load_eq <= USED 0
__trace_AUDIO__RECORD__acm_audio_record_read <= USED 0
__trace_AUDIO__RECORD__audio_record_get <= USED 0
__trace_AUDIO__RECORD__audio_record_open <= USED 0
__trace_AUDIO__RECORD__audio_record_read <= USED 0
__trace_AUDIO__RECORD__destroy_record_task <= USED 0
__trace_AUDIO__RECORD__record_read_size_check <= USED 0
__trace_AUDIO__RECORD__record_test_get_tick <= USED 0
__trace_AUDIO__RECORD__record_test_read_tick <= USED 0
__trace_AUDIO__ReSampleProcess__SwitchFileWrite <= USED 0
__trace_AUDIO__ReSampleProcess__Test_0 <= USED 0
__trace_AUDIO__ReSampleProcess__Test_Err <= USED 0
__trace_AUDIO__ReSampleProcess__Test_Loop <= USED 0
__trace_AUDIO__ReSampleProcess__Time <= USED 0
__trace_AUDIO__ReSampleProcess__TimeGap <= USED 0
__trace_AUDIO__StubServer__audioStubReverseWBAMR <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_open <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_open_error <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_open_error_play <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_open_error_record <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_play_HalfHandler <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_play_HalfHandler_already_stop <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_play_HalfHandler_already_stop_1 <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_play_HalfHandler_buf <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_play_HalfHandler_data <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_play_HalfHandler_notify_end <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_play_HalfHandler_too_busy <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_play_HalfHandler_underrun <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_play_buffer_init_error1 <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_read <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_read_1 <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_read_return <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_record_HalfHandler <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_record_HalfHandler_already_stop <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_record_HalfHandler_already_stop_1 <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_record_HalfHandler_data <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_record_HalfHandler_notify_end <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_record_HalfHandler_overrun <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_record_HalfHandler_too_busy <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_record_buffer_init_error1 <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_start <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_start_play <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_start_record <= USED 0
__trace_AUDIO__TG_PCM__tg_volume_set <= USED 0
__trace_AUDIO__TONE__audio_tone_duration <= USED 0
__trace_AUDIO__TONE__audio_tone_start_ascii <= USED 0
__trace_AUDIO__TONE__audio_tone_start_frequency <= USED 0
__trace_AUDIO__TONE__audio_tone_stop <= USED 0
__trace_AUDIO__TRACK__StreamDumpPipeline <= USED 0
__trace_AUDIO__TRACK__acm_audio_track_config <= USED 0
__trace_AUDIO__TRACK__acm_audio_track_debug <= USED 0
__trace_AUDIO__TRACK__acm_audio_track_get_available <= USED 0
__trace_AUDIO__TRACK__acm_audio_track_get_framesize <= USED 0
__trace_AUDIO__TRACK__acm_audio_track_get_softgain <= USED 0
__trace_AUDIO__TRACK__acm_audio_track_load_stream <= USED 0
__trace_AUDIO__TRACK__acm_audio_track_pipeline_config <= USED 0
__trace_AUDIO__TRACK__acm_audio_track_pipeline_read <= USED 0
__trace_AUDIO__TRACK__destroy_track_task <= USED 0
__trace_AUDIO__TRACK__load_stream_conflict <= USED 0
__trace_AUDIO__TRACK__track_play_switch_drain <= USED 0
__trace_AUDIO__TRACK__track_play_switch_id <= USED 0
__trace_AUDIO__Test__DumpGssp <= USED 0
__trace_AUDIO__Test__ReadRegesiter <= USED 0
__trace_AUDIO__Test__WriteRegesiter <= USED 0
__trace_AUDIO__VOCODER__audio_decoder_amr_start <= USED 0
__trace_AUDIO__VOCODER__audio_decoder_amr_stop <= USED 0
__trace_AUDIO__VOCODER__audio_encoder_amr_start <= USED 0
__trace_AUDIO__VOCODER__audio_encoder_amr_stop <= USED 0
__trace_AUDIO__VOCODER__dlstream <= USED 0
__trace_AUDIO__VOCODER__ulstream <= USED 0
__trace_AUDIO__Voice__CraneCodecADCOnOff_internal <= USED 0
__trace_AUDIO__Voice__GetAucState <= USED 0
__trace_AUDIO__Voice__IMSAmrRate <= USED 0
__trace_AUDIO__Voice__IMSAmrRateNotNormal <= USED 0
__trace_AUDIO__Voice__IMSAmrRateValue_0 <= USED 0
__trace_AUDIO__Voice__IMSAmrRateValue_1 <= USED 0
__trace_AUDIO__Voice__IMSEVSRateValue_2 <= USED 0
__trace_AUDIO__Voice__IMSEVSRateValue_3 <= USED 0
__trace_AUDIO__Voice__IMSSourceRateControl_1 <= USED 0
__trace_AUDIO__Voice__IMSSourceRateControl_2 <= USED 0
__trace_AUDIO__Voice__MuteVoicepath <= USED 0
__trace_AUDIO__Voice__PcmStreamRecordInvalidBuffer <= USED 0
__trace_AUDIO__Voice__ReceivePCMFromDSP <= USED 0
__trace_AUDIO__Voice__SendPCMToDSP <= USED 0
__trace_AUDIO__Voice__SetDebugCmdErr <= USED 0
__trace_AUDIO__Voice__SetVoiceEnhanceModuleControl <= USED 0
__trace_AUDIO__Voice__TxAMRFromBuf <= USED 0
__trace_AUDIO__Voice__amrResume <= USED 0
__trace_AUDIO__Voice__amrServiceDeActivate <= USED 0
__trace_AUDIO__Voice__amrServiceDeActivate_Called <= USED 0
__trace_AUDIO__Voice__amrSuspend <= USED 0
__trace_AUDIO__Voice__audioGsmResumeCB <= USED 0
__trace_AUDIO__Voice__audioGsmResumeCB_L1G <= USED 0
__trace_AUDIO__Voice__audioGsmSuspendCB <= USED 0
__trace_AUDIO__Voice__audioGsmSuspendCB_L1G <= USED 0
__trace_AUDIO__Voice__audioResume <= USED 0
__trace_AUDIO__Voice__audioSuspend <= USED 0
__trace_AUDIO__Voice__handlePcmStreamRecordMsg <= USED 0
__trace_AUDIO__Voice__lteResume <= USED 0
__trace_AUDIO__Voice__lteSuspend <= USED 0
__trace_AUDIO__Voice__poc_record_handler_data <= USED 0
__trace_AUDIO__Voice__sacNotifyAudioConnected <= USED 0
__trace_AUDIO__Voice__set_AMR_Rate_Value <= USED 0
__trace_AUDIO__Voice__sspa_mfpr_disable_DataPin <= USED 0
__trace_AUDIO__Voice__sspa_mfpr_dump <= USED 0
__trace_AUDIO__Voice__sspa_mfpr_dump_1 <= USED 0
__trace_AUDIO__Voice__voicePathRateSet_0 <= USED 0
__trace_AUDIO__Voice__vpathCTMControl <= USED 0
__trace_AUDIO__Voice__vpathCTMControl_0 <= USED 0
__trace_AUDIO__Voice__vpathCodecDBConfig <= USED 0
__trace_AUDIO__Voice__vpathCodecIfChange <= USED 0
__trace_AUDIO__Voice__vpathCodecIfChange_Env <= USED 0
__trace_AUDIO__Voice__vpathConfig <= USED 0
__trace_AUDIO__Voice__vpathDTMFControl_Env <= USED 0
__trace_AUDIO__Voice__vpathDebugCmd <= USED 0
__trace_AUDIO__Voice__vpathDmaIntInd <= USED 0
__trace_AUDIO__Voice__vpathECallDataSet <= USED 0
__trace_AUDIO__Voice__vpathFMControl <= USED 0
__trace_AUDIO__Voice__vpathFMControl_status <= USED 0
__trace_AUDIO__Voice__vpathGSSPControl <= USED 0
__trace_AUDIO__Voice__vpathGSSPRead <= USED 0
__trace_AUDIO__Voice__vpathKWS_set_addr <= USED 0
__trace_AUDIO__Voice__vpathMp3HpfControl <= USED 0
__trace_AUDIO__Voice__vpathMsaECallDataGet <= USED 0
__trace_AUDIO__Voice__vpathMsaECallVoiceGet <= USED 0
__trace_AUDIO__Voice__vpathMsaECallVoiceSet <= USED 0
__trace_AUDIO__Voice__vpathMuteControl_Env <= USED 0
__trace_AUDIO__Voice__vpathPcmStreamControl_Env <= USED 0
__trace_AUDIO__Voice__vpathPrintVoiceTestControl <= USED 0
__trace_AUDIO__Voice__vpathRegisterRx <= USED 0
__trace_AUDIO__Voice__vpathRegisterTx <= USED 0
__trace_AUDIO__Voice__vpathRxPacketForDebugSet <= USED 0
__trace_AUDIO__Voice__vpathRxbControl <= USED 0
__trace_AUDIO__Voice__vpathSelfInvocation_Env <= USED 0
__trace_AUDIO__Voice__vpathSetMode <= USED 0
__trace_AUDIO__Voice__vpathSetSelfInvocation <= USED 0
__trace_AUDIO__Voice__vpathSetSspaTestMode <= USED 0
__trace_AUDIO__Voice__vpathSetSspaTestMode_error <= USED 0
__trace_AUDIO__Voice__vpathSideToneControl_Env <= USED 0
__trace_AUDIO__Voice__vpathVadDumpControl <= USED 0
__trace_AUDIO__Voice__vpathVibrationControl <= USED 0
__trace_AUDIO__Voice__vpathVibrationControl_hfp <= USED 0
__trace_AUDIO__Voice__vpathVocoderControl_Env <= USED 0
__trace_AUDIO__Voice__vpathVoiceControl <= USED 0
__trace_AUDIO__Voice__vpathVoiceControl_Env <= USED 0
__trace_AUDIO__Voice__vpathVoiceHandover_Env <= USED 0
__trace_AUDIO__Voice__vpathVoiceTestControl_Env <= USED 0
__trace_AUDIO__Voice__wbSpeechSupportedQueryGet <= USED 0
__trace_AUDIO__Volte__RTPInjection2IMS <= USED 0
__trace_AUDIO__Volte__RTPInjection_20msExpire_notimer <= USED 0
__trace_AUDIO__Volte__RTPInjection_20msExpire_slowdown <= USED 0
__trace_AUDIO__Volte__RTPInjection_20msExpire_starttimer <= USED 0
__trace_AUDIO__Volte__RTPInjection_20msExpire_ts <= USED 0
__trace_AUDIO__Volte__RTPSenderTimer_Expire <= USED 0
__trace_AUDIO__Volte__ReceiveRTP <= USED 0
__trace_AUDIO__Volte__sendRTP <= USED 0
__trace_AUDIO__Volte__sendRTP_full <= USED 0
__trace_AUDIO__Volte__sendRTP_init <= USED 0
__trace_AUDIO__Volte__sendRTP_start <= USED 0
__trace_AUDIO__Volte__stopRTPInjection_release <= USED 0
__trace_AUDIO__Volte__stopRTPInjection_startprocess <= USED 0
__trace_CIModem__DLC_DISC__Deactive_Arg_error <= USED 0
__trace_CIModem__DLC_DISC__Deactive_req_error <= USED 0
__trace_CIModem__DLC_DISC__PSDATA_RUN <= USED 0
__trace_CIModem__Init__start <= USED 0
__trace_CIModem__RecTask__linkstatusind <= USED 0
__trace_CIModem__RecTask__linkstatusind_abnormal <= USED 0
__trace_CI_SERVER_STUB__CI_SERVER_TASK__583 <= USED 0
__trace_CI__MMI__mmiCnf_1 <= USED 0
__trace_COMMPM__DCXOLPM__DCXOLPMUnregister <= USED 0
__trace_CP_APP__enableDisable__disableCpaFunc <= USED 0
__trace_CP_APP__enableDisable__enableCpaFunc <= USED 0
__trace_CRD__ANT_TUNER_CFG__ANT_TUNER_CFG_NVM_FILE_CREATE_FAIL <= USED 0
__trace_CRD__ANT_TUNER_CFG__antTunerCfg_CreateFile <= USED 0
__trace_CRD__DSP_DBG_CFG__DSP_DBG_CFG_NVM_FILE_CREATE_FAIL <= USED 0
__trace_CRD__DSP_DBG_CFG__dspDbgCfg_CreateFile <= USED 0
__trace_CRD__DSP_DEBUG__START_PSRAM_ACCESS <= USED 0
__trace_CRD__DSP_DEBUG__STOPT_PSRAM_ACCESS <= USED 0
__trace_CRD__DTC__dtcCountErrorHisr <= USED 0
__trace_CRD__DTC__dtcSpyModifyReportAndWriteTest <= USED 0
__trace_CRD__FTT__FTT_DISABLE <= USED 0
__trace_CRD__FTT__FTT_DISABLE_AUTO <= USED 0
__trace_CRD__FTT__FTT_DISABLE_ERR <= USED 0
__trace_DCXO__DCXOInit__SetRfDcxoIsUsingFlgWithGPIO <= USED 0
__trace_DRAT__DEBUG__wgi_Bind_function2 <= USED 0
__trace_Diag__CommandAssemblyFailure__respond010 <= USED 0
__trace_Diag__CommandAssemblyFailure__respond011 <= USED 0
__trace_Diag__CommandAssemblyFailure__respond012 <= USED 0
__trace_Diag__CommandAssemblyFailure__respond03 <= USED 0
__trace_Diag__CommandAssemblyFailure__respond04 <= USED 0
__trace_Diag__CommandAssemblyFailure__respond05 <= USED 0
__trace_Diag__CommandAssemblyFailure__respond06 <= USED 0
__trace_Diag__CommandAssemblyFailure__respond07 <= USED 0
__trace_Diag__CommandAssemblyFailure__respond08 <= USED 0
__trace_Diag__CommandAssemblyFailure__respond09 <= USED 0
__trace_Diag__CommandAssemblySuccess__respond013 <= USED 0
__trace_Diag__CommandAssemblySuccess__respond014 <= USED 0
__trace_Diag__Command_In_Parts__respond01 <= USED 0
__trace_Diag__Command_In_Parts__respond02 <= USED 0
__trace_Diag__Command_In_Parts__respond03 <= USED 0
__trace_Diag__DEB__STATS_DATA04 <= USED 0
__trace_Diag__DEB__STATS_DATA05 <= USED 0
__trace_Diag__DEB__STATS_DATA06 <= USED 0
__trace_Diag__Debug__NumberOfBadRxMessages <= USED 0
__trace_Diag__Debug__SetCPBytes <= USED 0
__trace_Diag__Debug__StatsGetParams <= USED 0
__trace_Diag__Utils__ExtIfData1 <= USED 0
__trace_Diag__Utils__ExtIfTxMulti <= USED 0
__trace_Diag__Utils__IntIfData1 <= USED 0
__trace_Diag__Utils__LostMsg_1 <= USED 0
__trace_Diag__Utils__MaxInMsgBodySize <= USED 0
__trace_Diag__Utils__NVM_AVAILABLE <= USED 0
__trace_Diag__Utils__PS_IS_RUNNING_RESPONSE <= USED 0
__trace_Diag__Utils__ST_8kTest <= USED 0
__trace_Diag__Utils__diagGetCPInterfaceVersion1 <= USED 0
__trace_Diag__Utils__diagGetCPInterfaceVersion2 <= USED 0
__trace_FDI__FDI5to6__FDI_feof <= USED 0
__trace_FDI__Transport__F_Name_List_Error <= USED 0
__trace_FDI__fatsys__F_Makedir <= USED 0
__trace_FDI__fatsys__F_Makedir_succeed <= USED 0
__trace_FDI__fatsys__F_eof_start <= USED 0
__trace_FDI__fatsys__F_eof_succeed <= USED 0
__trace_GPLC_DUAL_SIM__IDLE__StartIdleMode_jjx168 <= USED 0
__trace_GPLC_DUAL_SIM__IDLE__StartIdleMode_jjx178 <= USED 0
__trace_GPLC_DUAL_SIM__IDLE__StartIdleMode_jjx198 <= USED 0
__trace_GPLC_DUAL_SIM__IDLE__StartIdleMode_jjx199 <= USED 0
__trace_GPLC_DUAL_SIM__IDLE__StartIdleMode_jjx200 <= USED 0
__trace_GPLC_RF__RF_INIT__DcxoTempCompensation_PrintData <= USED 0
__trace_GRFD__CALIB__RspGet4GCalibrationStatus <= USED 0
__trace_HAL__IPC__APLP_AAAPDataBufferFreeNotification <= USED 0
__trace_HAL__IPC__DATA_ALLOC_ERROR_3 <= USED 0
__trace_HAL__IPC__DATA_ALLOC_ERROR_DSP <= USED 0
__trace_HAL__IPC__DATA_ALLOC_ERROR_DSP1 <= USED 0
__trace_HAL__IPC__SET_NO_PLP_MODE <= USED 0
__trace_HAL__IPC__SET_PLP_MODE <= USED 0
__trace_HAL__PM803__PM803_LDO6_1V8 <= USED 0
__trace_HAL__PM803__PM803_LDO7_3V0 <= USED 0
__trace_HAL__PM803__PM803_LDO7_3V3 <= USED 0
__trace_HAL__PM803__PM803_LDO8_1V9 <= USED 0
__trace_HAL__PM803__PM803_LDO8_3V3 <= USED 0
__trace_HAL__USIM_DL__USIM_DL_T1decode <= USED 0
__trace_HAL__USIM_DL__USIM_DL_T1decode1 <= USED 0
__trace_HAL__USIM_DL__USIM_DL_decodeIBlock <= USED 0
__trace_HAL__USIM_DL__USIM_DL_decodeSBlock <= USED 0
__trace_HAL__USIM_DL__USIM_DLrequest2 <= USED 0
__trace_HAL__USIM__USIMOemFlag <= USED 0
__trace_HAL__USIM__USIMVersionGet <= USED 0
__trace_HW_PLAT__utilities__BeginBoardInfo <= USED 0
__trace_HW_PLAT__utilities__InfoForBoardTracking <= USED 0
__trace_HW_PLAT__utilities__WDTTest <= USED 0
__trace_HW_PLAT__utilities__endBoardInfo <= USED 0
__trace_HW_PLAT__utilities__readUINT16 <= USED 0
__trace_HW_PLAT__utilities__readUINT32 <= USED 0
__trace_HW_PLAT__utilities__readUINT8 <= USED 0
__trace_HW_PLAT__utilities__writeUINT16 <= USED 0
__trace_HW_PLAT__utilities__writeUINT32 <= USED 0
__trace_HW_PLAT__utilities__writeUINT8 <= USED 0
__trace_IMS__GetIMSEnable__1 <= USED 0
__trace_IMS__IMS_STUB__ACIPCDImsRxdefault <= USED 0
__trace_IMS__IMS_STUB__ACIPCDImsTxdefault <= USED 0
__trace_IMS__SetStChannel__ENTER_IN <= USED 0
__trace_IMS__TASK_RESUME__ENTER <= USED 0
__trace_IMS__TASK_RESUME__EXIT <= USED 0
__trace_IMS__TASK_SUSPEND__ENTER <= USED 0
__trace_IMS__TASK_SUSPEND__EXIT <= USED 0
__trace_IMS__restartImsTask__ENTER <= USED 0
__trace_L1A__L1ACAL__L1aAcatTriggerAdcQueryReq_enter <= USED 0
__trace_L1A__L1ACAL__L1aCheckIfNvmIsCorrupted_TRUE <= USED 0
__trace_L1A__L1ACAL__L1aHandleCalResultDataAck_afc <= USED 0
__trace_L1A__L1ACAL__L1aHandleCalResultDataAck_agc <= USED 0
__trace_L1A__L1ACAL__L1aHandleCalResultDataAck_apc <= USED 0
__trace_L1A__L1ACAL__L1aHandleCalResultDataAck_error <= USED 0
__trace_L1A__L1ACAL__L1aHandleCalResultDataAck_notMatch <= USED 0
__trace_L1A__L1ACAL__L1aHandleEcphyCommonRsp_default <= USED 0
__trace_L1A__L1ACAL__L1aHandleL1LogInfoInd_rawData <= USED 0
__trace_L1A__L1ACAL__L1aHandlePaCtrlTableData_failOpen <= USED 0
__trace_L1A__L1ACAL__L1aHandleReadAdcRtpValueCnf_end <= USED 0
__trace_L1A__L1ACAL__L1aHandleReadAdcValueCnf_end <= USED 0
__trace_L1A__L1ACAL__L1aHandleReadRfTempCnf_1 <= USED 0
__trace_L1A__L1ACAL__L1aHandleReadRfTempCnf_2 <= USED 0
__trace_L1A__L1ACAL__L1aHandleRfTuningToolData_failOpen <= USED 0
__trace_L1A__L1ACAL__L1aSendDefaultTableWithZero_11 <= USED 0
__trace_L1A__L1ACAL__L1aSendDefaultTableWithZero_14 <= USED 0
__trace_L1A__L1ACAL__L1aSendPureCalibrationDataToDspByShareMemory_03 <= USED 0
__trace_L1A__L1ACAL__L1aSendPureCalibrationDataToDspByShareMemory_06 <= USED 0
__trace_L1A__L1ACAL__L1aSendPureCalibrationDataToDspByShareMemory_08 <= USED 0
__trace_L1A__L1ACAL__L1aSendPureCalibrationDataToDspByShareMemory_data1 <= USED 0
__trace_L1A__L1ACAL__L1aSendPureCalibrationDataToDspByShareMemory_enter <= USED 0
__trace_L1A__L1ACAL__L1aSendPureCalibrationDataToDsp_01 <= USED 0
__trace_L1A__L1ACAL__L1aSendPureCalibrationDataToDsp_03 <= USED 0
__trace_L1A__L1ACAL__L1aSendPureCalibrationDataToDsp_05 <= USED 0
__trace_L1A__L1ACAL__L1aSendPureCalibrationDataToDsp_06 <= USED 0
__trace_L1A__L1ACAL__L1aSendPureCalibrationDataToDsp_07 <= USED 0
__trace_L1A__L1ACAL__L1aSendPureCalibrationDataToDsp_08 <= USED 0
__trace_L1A__L1ACAL__L1aSendPureCalibrationDataToDsp_12 <= USED 0
__trace_L1A__L1ACAL__L1aSendPureCalibrationDataToDsp_13 <= USED 0
__trace_L1A__L1ACAL__L1aSendPureCalibrationDataToDsp_enter <= USED 0
__trace_L1A__L1ACAL__L1aSendPureCalibrationDataToDsp_remainLength <= USED 0
__trace_L1A__L1ACAL__L1aSendRfTuningToolSuperCmd_1 <= USED 0
__trace_L1A__L1ACAL__L1aSendRfTuningToolSuperCmd_4 <= USED 0
__trace_L1A__L1ACAL__L1aSendRfTuningToolSuperCmd_8_4 <= USED 0
__trace_L1A__L1ACAL__L1aSendRfTuningToolSuperCmd_8_4_9 <= USED 0
__trace_L1A__L1ACAL__L1aSendRfTuningToolSuperCmd_9 <= USED 0
__trace_L1A__L1ACAL__L1aStartDigRf4BusLoopbackTest_1 <= USED 0
__trace_L1A__L1ACAL__L1aTempReadingCallback_1 <= USED 0
__trace_L1A__L1ACAL__PlatToL1aCommonIpcReq_enter <= USED 0
__trace_L1A__L1ACAL__getTxPowerBackoffMode_00 <= USED 0
__trace_L1A__L1a_Response__L1aIpcResponseEntrance_2 <= USED 0
__trace_L1A__L1a_Response__L1aIpcResponseEntrance_Err <= USED 0
__trace_L1A__l1a_drat__L1aSetSimGsmActivated_SIM2 <= USED 0
__trace_L1A__l1a_drat__L1aSetSimLteActivated_SIM1 <= USED 0
__trace_L1A__l1a_phy__L1aCheckCurrentStateNeedCancelSleep_TRUE <= USED 0
__trace_L1A__l1a_phy__L1aHandleLteSleepInd_longEdrx <= USED 0
__trace_L1A__l1a_phy__L1aPermittSleepInd_result_NonDsds <= USED 0
__trace_L1A__l1a_phy__fliScanGapFinishInd_enter <= USED 0
__trace_L1A__l1a_phy__fliScanGapReqInd_enter <= USED 0
__trace_L1A__l1a_response__L1aHandlePlatCommonIpcRsp_enter <= USED 0
__trace_L1A__l1a_sys__L1aCheckIfNeedCancelSleep_TRUE <= USED 0
__trace_LTE_PS__ERRC_AIS__AIS_DECODE_DL_DCCH_FromOtherRat <= USED 0
__trace_LTE_PS__ERRC_BAND__LteBandGetBandsInformForPlmnSearch_entry <= USED 0
__trace_LTE_PS__ERRC_BAND__LteRrcCsrCheckIfBandInMultiBandListSupported_err <= USED 0
__trace_LTE_PS__ERRC_CELL__LteRrcCellMgrDebugAssert466_check <= USED 0
__trace_LTE_PS__ERRC_CELL__LteRrcCellMgrDebugAssert466_head <= USED 0
__trace_LTE_PS__ERRC_CELL__LteRrcCellMgrDebugAssert466_skip <= USED 0
__trace_LTE_PS__ERRC_CER__ETE_COPY_RRC_CONNECTION_RELEASE <= USED 0
__trace_LTE_PS__ERRC_CER__LteCerConstructAndSendEutraProximityInd_earfcnErr <= USED 0
__trace_LTE_PS__ERRC_CER__LteCerGetHoNotAlloweFlag_1 <= USED 0
__trace_LTE_PS__ERRC_CER__LteCerGetRplmn_false <= USED 0
__trace_LTE_PS__ERRC_CER__LteRrcCheckMeasResultListEUTRA_Cell <= USED 0
__trace_LTE_PS__ERRC_CER__LteRrcCheckMeasResultListEUTRA_CellNum <= USED 0
__trace_LTE_PS__ERRC_CER__LteRrcCheckMeasResultListEUTRA_Freq <= USED 0
__trace_LTE_PS__ERRC_CER__LteRrcCheckMeasResultListEUTRA_FreqNum <= USED 0
__trace_LTE_PS__ERRC_CER__LteRrcSendRrcConnectionReleaseR12_decodeError <= USED 0
__trace_LTE_PS__ERRC_CER__LteRrcSendRrcConnectionReleaseR12_default <= USED 0
__trace_LTE_PS__ERRC_CER__LteRrcSendRrcConnectionReleaseR12_tag <= USED 0
__trace_LTE_PS__ERRC_CSR__GsmGetBandWidthOfSertvingCell_1 <= USED 0
__trace_LTE_PS__ERRC_CSR__LteCheckLockOnLteFreq_1 <= USED 0
__trace_LTE_PS__ERRC_CSR__LteCsrHandleEcphySwitchRatToUmtsCnf_warning <= USED 0
__trace_LTE_PS__ERRC_CSR__LteCsrIsRequestPlmnListContainCMCC_no <= USED 0
__trace_LTE_PS__ERRC_CSR__LteCsrRemoveCellFromBchCrcErrorBarList_1 <= USED 0
__trace_LTE_PS__ERRC_CSR__LteRrcCsrCheckIfNcellOnIntendedBand_leave <= USED 0
__trace_LTE_PS__ERRC_CSR__LteRrcDisableRandomReestablish1 <= USED 0
__trace_LTE_PS__ERRC_CSR__LteRrcDsdsBufferActReq_1 <= USED 0
__trace_LTE_PS__ERRC_CSR__LteRrcEnableRandomReestablish1 <= USED 0
__trace_LTE_PS__ERRC_CSR__LteRrcManualReestablish1 <= USED 0
__trace_LTE_PS__ERRC_CSR__LteRrcQueryTddConfig_error <= USED 0
__trace_LTE_PS__ERRC_CSR__TriggerLteOos_enter <= USED 0
__trace_LTE_PS__ERRC_ETE__LteAisEncodeUeCapabilityRatContainer_1 <= USED 0
__trace_LTE_PS__ERRC_ETE__LteEteSendEcphyStopEngInfoReq_notStat <= USED 0
__trace_LTE_PS__ERRC_MCR__LteMcrChangeA3ConditionTimeToTrigger_changed <= USED 0
__trace_LTE_PS__ERRC_MCR__LteMcrCheckIfNeedTriggerB2_FALSE <= USED 0
__trace_LTE_PS__ERRC_MCR__LteMcrCheckIfScellBad_TRUE <= USED 0
__trace_LTE_PS__ERRC_MCR__LteMcrCheckIsCmccEsrvccImsStartForSpecialTac_TRUE <= USED 0
__trace_LTE_PS__ERRC_MCR__LteMcrIsBadQuality_TRUE <= USED 0
__trace_LTE_PS__ERRC_MCR__LteMcrTriggerIntraInterFreqMeasReq_enter <= USED 0
__trace_LTE_PS__ERRC_MCR__LteMcrTriggerIntraInterFreqMeasStopReq_end <= USED 0
__trace_LTE_PS__ERRC_SECURITY__LteCerHandleSecurityConfigForHOFromUtra1 <= USED 0
__trace_LTE_PS__ERRC_SIR__SetLteInterFreqInConnected_1 <= USED 0
__trace_LTE_PS__ERRC_SIR__SetLteInterFreq_1 <= USED 0
__trace_LTE_PS__ERRC_SIR__SetPagingisMismatch_1 <= USED 0
__trace_LTE_PS__ERRC__LteRrcVarMemInit_1 <= USED 0
__trace_LTE_PS__ERRC__LteRrcVarMemInit_2 <= USED 0
__trace_LTE_PS__ERR_UTIL__LLteRrcCheckEnterConnected_2 <= USED 0
__trace_LTE__BM__LteBmProcessDataAbortInd0 <= USED 0
__trace_LTE__BM__LtePDCPReleaseRxL2BBlock2t <= USED 0
__trace_LTE__BM__UMTSMacAllocateL2RxBuffer0 <= USED 0
__trace_LTE__BM__UMTSMacAllocateL2RxBuffer000726 <= USED 0
__trace_LTE__BM__UMTSMacAllocateL2RxBuffer000727 <= USED 0
__trace_LTE__BM__UMTSMacAllocateL2RxBuffer008 <= USED 0
__trace_LTE__BM__UMTSMacAllocateL2RxBuffer09w <= USED 0
__trace_LTE__BM__UMTSRlcReleaseL2RxBuffer0 <= USED 0
__trace_LTE__BM__UMTSRlcReleaseL2RxBuffer009 <= USED 0
__trace_LTE__BM__UMTSRlcReleaseL2RxBuffer0_umts <= USED 0
__trace_LTE__BM__UMTSRlcReleaseL2buffer0001 <= USED 0
__trace_LTE__DTC__LteHandlePdicHisrCh0AboveY0_SimIdInvalid <= USED 0
__trace_LTE__DTC__LteHandlePdicHisrCh0AboveY0_noSubmitPlusOne <= USED 0
__trace_LTE__DTC__LtePdcpDlTraceIpPkt01 <= USED 0
__trace_LTE__DTC__LtePdcpDlTraceIpPkt02 <= USED 0
__trace_LTE__EPDCP__LteHandlePdicHisrCh0AboveY0_00_check_dtc <= USED 0
__trace_LTE__MACDL__LteMacdlHandleEphyDataIndDiscardTB <= USED 0
__trace_LTE__MACUL__LteMacTrafficReq4 <= USED 0
__trace_LTE__MACUT__DebugL2ImsInfo_1 <= USED 0
__trace_LTE__MACUT__LteMacutCADisable1 <= USED 0
__trace_LTE__MACUT__LteMacutCAEnable1 <= USED 0
__trace_LTE__MACUT__LteMacutDisableL2Exception_print <= USED 0
__trace_LTE__MACUT__LteMacutDisableMacInternalSig1 <= USED 0
__trace_LTE__MACUT__LteMacutDisableUplaneDiag1 <= USED 0
__trace_LTE__MACUT__LteMacutEnableMacInternalSig1 <= USED 0
__trace_LTE__MACUT__LteMacutEnableUplaneDiag1 <= USED 0
__trace_LTE__MACUT__LteMacutEnableUplaneDiag6 <= USED 0
__trace_LTE__MACUT__LteMacutGsmVoiceCallInform1 <= USED 0
__trace_LTE__MACUT__LteMacutHighSpeedDiagEnable1 <= USED 0
__trace_LTE__MACUT__LteMacutHighSpeedDiagEnable145 <= USED 0
__trace_LTE__MACUT__LteMacutSetBSRValueInGsmVoiceCall1 <= USED 0
__trace_LTE__MACUT__LteMacutSetL2PendingLenToMax_1 <= USED 0
__trace_LTE__MACUT__LteMacutSetL2PendingLenToMax_13 <= USED 0
__trace_LTE__MACUT__LteMacutSetWarningHandleLevel_1 <= USED 0
__trace_LTE__MACUT__LteMacutSetWarningHandleLevel_2 <= USED 0
__trace_LTE__MACUT__LteMacutSetWarningHandleLevel_3 <= USED 0
__trace_LTE__MACUT__LteMacutTerminateConnection_22 <= USED 0
__trace_LTE__MACUT__isBadQualityDetected1 <= USED 0
__trace_LTE__MAC__LteMacSendEcphyFlashDspReq01 <= USED 0
__trace_LTE__MAC__LteMacSendEcphyFlashDspReq02 <= USED 0
__trace_LTE__MAC__LteMacSendEcphyFlashDspReq03 <= USED 0
__trace_LTE__PDCP__LtePdcpSrbSoftEea_ebiInvalid <= USED 0
__trace_LTE__PDCP__tLtePdcpSrbDataIntegrityError <= USED 0
__trace_LTE__RABM__LteRabmCTFlagDisable1 <= USED 0
__trace_LTE__RABM__LteRabmCTFlagEnable1 <= USED 0
__trace_LTE__RABM__LteRabmCTFlagShow1 <= USED 0
__trace_LTE__RABM__LteRabmCheckSnDataReq_5869 <= USED 0
__trace_LTE__RABM__LteRabmCheckSnDataReq_9858 <= USED 0
__trace_LTE__RABM__LteRabmCheckSnDataReq_gg68 <= USED 0
__trace_LTE__RABM__LteRabmDiagFlagOff1 <= USED 0
__trace_LTE__RABM__LteRabmDiagFlagOn1 <= USED 0
__trace_LTE__RABM__LteRabmDiagFlagShow1 <= USED 0
__trace_LTE__RABM__LteRabmGetAllPdcpPendingUplinkData_XX <= USED 0
__trace_LTE__RABM__LteRabmReleasePendingSnDataReq1100 <= USED 0
__trace_LTE__RABM__LteRabmStartLoopBackTimer1 <= USED 0
__trace_LTE__RABM__LteRabmStartWaitToReestTimer1 <= USED 0
__trace_LTE__RABM__LteRabmStopWaitToReestTimer1 <= USED 0
__trace_LTE__RLC__LteAmRlcDiscardUlPackets0a0 <= USED 0
__trace_LTE__RLC__LteRlcControlMaxAmPduNumToDlAssemble <= USED 0
__trace_LTE__RLC__LteRlcControlTraceFlag1 <= USED 0
__trace_MAT__MATHandleIndication__GPIO_INTERRUPT_TEST_0 <= USED 0
__trace_MAT__MATHandleIndication__GPIO_INTERRUPT_TEST_1 <= USED 0
__trace_MAT__MATHandleIndication__dump_isr_count_0 <= USED 0
__trace_MAT__MATHandleIndication__getGpioLvl_0 <= USED 0
__trace_MAT__MATHandleIndication__setGpioLvl_0 <= USED 0
__trace_MAT__MATHandleResponse___MATConfIndCB_0 <= USED 0
__trace_MEM__DUMP__GetUUID_High <= USED 0
__trace_MEM__DUMP__GetUUID_Low <= USED 0
__trace_MIFI__BT__a2dp_source_260 <= USED 0
__trace_MIFI__BT__att_db_263 <= USED 0
__trace_MIFI__BT__att_db_266 <= USED 0
__trace_MIFI__BT__att_db_269 <= USED 0
__trace_MIFI__BT__att_db_271 <= USED 0
__trace_MIFI__BT__att_db_273 <= USED 0
__trace_MIFI__BT__att_server_1186 <= USED 0
__trace_MIFI__BT__avdtp_1250 <= USED 0
__trace_MIFI__BT__avdtp_1256 <= USED 0
__trace_MIFI__BT__avdtp_1261 <= USED 0
__trace_MIFI__BT__avdtp_1283 <= USED 0
__trace_MIFI__BT__avdtp_1289 <= USED 0
__trace_MIFI__BT__avdtp_1294 <= USED 0
__trace_MIFI__BT__avdtp_1368 <= USED 0
__trace_MIFI__BT__avdtp_1427 <= USED 0
__trace_MIFI__BT__avdtp_1489 <= USED 0
__trace_MIFI__BT__avdtp_1500 <= USED 0
__trace_MIFI__BT__avdtp_1505 <= USED 0
__trace_MIFI__BT__avdtp_378 <= USED 0
__trace_MIFI__BT__avdtp_396 <= USED 0
__trace_MIFI__BT__avdtp_408 <= USED 0
__trace_MIFI__BT__avdtp_422 <= USED 0
__trace_MIFI__BT__avdtp_448 <= USED 0
__trace_MIFI__BT__avdtp_acceptor_466 <= USED 0
__trace_MIFI__BT__avdtp_sink_178 <= USED 0
__trace_MIFI__BT__avdtp_sink_184 <= USED 0
__trace_MIFI__BT__avdtp_sink_190 <= USED 0
__trace_MIFI__BT__avdtp_sink_195 <= USED 0
__trace_MIFI__BT__avdtp_source_227 <= USED 0
__trace_MIFI__BT__avdtp_source_232 <= USED 0
__trace_MIFI__BT__avdtp_source_252 <= USED 0
__trace_MIFI__BT__avdtp_source_257 <= USED 0
__trace_MIFI__BT__avdtp_source_284 <= USED 0
__trace_MIFI__BT__avdtp_source_289 <= USED 0
__trace_MIFI__BT__avrcp_browsing_controller_490 <= USED 0
__trace_MIFI__BT__avrcp_browsing_controller_538 <= USED 0
__trace_MIFI__BT__avrcp_browsing_controller_543 <= USED 0
__trace_MIFI__BT__avrcp_browsing_controller_568 <= USED 0
__trace_MIFI__BT__avrcp_browsing_controller_573 <= USED 0
__trace_MIFI__BT__avrcp_browsing_controller_609 <= USED 0
__trace_MIFI__BT__avrcp_browsing_controller_615 <= USED 0
__trace_MIFI__BT__avrcp_browsing_controller_633 <= USED 0
__trace_MIFI__BT__avrcp_browsing_controller_640 <= USED 0
__trace_MIFI__BT__avrcp_browsing_controller_645 <= USED 0
__trace_MIFI__BT__avrcp_browsing_controller_670 <= USED 0
__trace_MIFI__BT__avrcp_browsing_controller_677 <= USED 0
__trace_MIFI__BT__avrcp_browsing_controller_682 <= USED 0
__trace_MIFI__BT__avrcp_browsing_controller_702 <= USED 0
__trace_MIFI__BT__avrcp_browsing_controller_709 <= USED 0
__trace_MIFI__BT__avrcp_browsing_controller_714 <= USED 0
__trace_MIFI__BT__avrcp_controller_1748 <= USED 0
__trace_MIFI__BT__avrcp_controller_1773 <= USED 0
__trace_MIFI__BT__bt_a2dp_source_689 <= USED 0
__trace_MIFI__BT__bt_a2dp_source_698 <= USED 0
__trace_MIFI__BT__bt_avrcp_366 <= USED 0
__trace_MIFI__BT__bt_map_client_249 <= USED 0
__trace_MIFI__BT__bt_pbap_client_279 <= USED 0
__trace_MIFI__BT__bt_pbap_client_291 <= USED 0
__trace_MIFI__BT__btstack_cvsd_plc_508 <= USED 0
__trace_MIFI__BT__btstack_cvsd_plc_509 <= USED 0
__trace_MIFI__BT__btstack_cvsd_plc_510 <= USED 0
__trace_MIFI__BT__btstack_cvsd_plc_511 <= USED 0
__trace_MIFI__BT__btstack_event_handler_137 <= USED 0
__trace_MIFI__BT__btstack_hid_parser_344 <= USED 0
__trace_MIFI__BT__btstack_link_key_db_tlv_152 <= USED 0
__trace_MIFI__BT__btstack_link_key_db_tlv_167 <= USED 0
__trace_MIFI__BT__btstack_link_key_db_tlv_178 <= USED 0
__trace_MIFI__BT__btstack_link_key_db_tlv_96 <= USED 0
__trace_MIFI__BT__btstack_tlv_asr_108 <= USED 0
__trace_MIFI__BT__btstack_tlv_asr_332 <= USED 0
__trace_MIFI__BT__btstack_tlv_asr_355 <= USED 0
__trace_MIFI__BT__btstack_tlv_asr_360 <= USED 0
__trace_MIFI__BT__btstack_tlv_asr_367 <= USED 0
__trace_MIFI__BT__goep_client_134 <= USED 0
__trace_MIFI__BT__goep_client_147 <= USED 0
__trace_MIFI__BT__goep_client_160 <= USED 0
__trace_MIFI__BT__goep_client_extern_226 <= USED 0
__trace_MIFI__BT__goep_client_extern_239 <= USED 0
__trace_MIFI__BT__goep_client_extern_252 <= USED 0
__trace_MIFI__BT__hci_619 <= USED 0
__trace_MIFI__BT__hci_8870 <= USED 0
__trace_MIFI__BT__hfp_ag_1209 <= USED 0
__trace_MIFI__BT__hfp_ag_1218 <= USED 0
__trace_MIFI__BT__hfp_ag_1382 <= USED 0
__trace_MIFI__BT__hfp_ag_1607 <= USED 0
__trace_MIFI__BT__hfp_ag_1663 <= USED 0
__trace_MIFI__BT__hfp_ag_3139 <= USED 0
__trace_MIFI__BT__hfp_ag_3142 <= USED 0
__trace_MIFI__BT__hfp_msbc_108 <= USED 0
__trace_MIFI__BT__hid_device_631 <= USED 0
__trace_MIFI__BT__hid_device_931 <= USED 0
__trace_MIFI__BT__hid_device_962 <= USED 0
__trace_MIFI__BT__hid_device_965 <= USED 0
__trace_MIFI__BT__hid_device_974 <= USED 0
__trace_MIFI__BT__hid_device_977 <= USED 0
__trace_MIFI__BT__hsp_ag_334 <= USED 0
__trace_MIFI__BT__hsp_ag_388 <= USED 0
__trace_MIFI__BT__hsp_ag_398 <= USED 0
__trace_MIFI__BT__hsp_ag_726 <= USED 0
__trace_MIFI__BT__hsp_ag_731 <= USED 0
__trace_MIFI__BT__hsp_ag_736 <= USED 0
__trace_MIFI__BT__l2cap_5065 <= USED 0
__trace_MIFI__BT__l2cap_5071 <= USED 0
__trace_MIFI__BT__l2cap_5082 <= USED 0
__trace_MIFI__BT__l2cap_5237 <= USED 0
__trace_MIFI__BT__l2cap_5248 <= USED 0
__trace_MIFI__BT__l2cap_5266 <= USED 0
__trace_MIFI__BT__l2cap_5362 <= USED 0
__trace_MIFI__BT__l2cap_5366 <= USED 0
__trace_MIFI__BT__l2cap_5374 <= USED 0
__trace_MIFI__BT__l2cap_5646 <= USED 0
__trace_MIFI__BT__l2cap_5652 <= USED 0
__trace_MIFI__BT__l2cap_5660 <= USED 0
__trace_MIFI__BT__l2cap_5666 <= USED 0
__trace_MIFI__BT__l2cap_5672 <= USED 0
__trace_MIFI__BT__l2cap_5678 <= USED 0
__trace_MIFI__BT__l2cap_5686 <= USED 0
__trace_MIFI__BT__l2cap_5692 <= USED 0
__trace_MIFI__BT__l2cap_5698 <= USED 0
__trace_MIFI__BT__l2cap_5704 <= USED 0
__trace_MIFI__BT__l2cap_5710 <= USED 0
__trace_MIFI__BT__l2cap_5716 <= USED 0
__trace_MIFI__BT__l2cap_760 <= USED 0
__trace_MIFI__BT__l2cap_774 <= USED 0
__trace_MIFI__BT__le_device_db_tlv_498 <= USED 0
__trace_MIFI__BT__le_device_db_tlv_506 <= USED 0
__trace_MIFI__BT__map_client_141 <= USED 0
__trace_MIFI__BT__map_client_154 <= USED 0
__trace_MIFI__BT__map_client_168 <= USED 0
__trace_MIFI__BT__pbap_client_225 <= USED 0
__trace_MIFI__BT__pbap_client_238 <= USED 0
__trace_MIFI__BT__pbap_client_252 <= USED 0
__trace_MIFI__BT__pbap_client_268 <= USED 0
__trace_MIFI__BT__pbap_client_286 <= USED 0
__trace_MIFI__BT__rfcomm_1280 <= USED 0
__trace_MIFI__BT__rfcomm_1281 <= USED 0
__trace_MIFI__BT__rfcomm_2754 <= USED 0
__trace_MIFI__BT__sdp_client_208 <= USED 0
__trace_MIFI__BT__sdp_client_216 <= USED 0
__trace_MIFI__BT__sdp_client_255 <= USED 0
__trace_MIFI__BT__sdp_client_262 <= USED 0
__trace_MIFI__BT__sdp_client_407 <= USED 0
__trace_MIFI__BT__sdp_client_428 <= USED 0
__trace_MIFI__BT__sdp_client_455 <= USED 0
__trace_MIFI__BT__sdp_client_470 <= USED 0
__trace_MIFI__BT__sdp_client_487 <= USED 0
__trace_MIFI__BT__sdp_client_494 <= USED 0
__trace_MIFI__BT__sdp_client_509 <= USED 0
__trace_MIFI__BT__sdp_client_515 <= USED 0
__trace_MIFI__BT__sdp_client_541 <= USED 0
__trace_MIFI__BT__sdp_client_548 <= USED 0
__trace_MIFI__BT__sdp_client_568 <= USED 0
__trace_MIFI__BT__sm_5132 <= USED 0
__trace_MIFI__HTTPCLI__126 <= USED 0
__trace_MIFI__HTTPCLI__http_client_stop_2131 <= USED 0
__trace_MIFI__LOG__LWIP_LOG <= USED 0
__trace_MIFI__LOG__SaveToFS <= USED 0
__trace_MIFI__LWIP__duster_ip_recovery_156 <= USED 0
__trace_MIFI__LWIP__duster_ip_recovery_169 <= USED 0
__trace_MIFI__LWIP__duster_ip_recovery_194 <= USED 0
__trace_MIFI__LWIP__duster_ip_recovery_218 <= USED 0
__trace_MIFI__LWIP__duster_ip_recovery_229 <= USED 0
__trace_MIFI__LWIP__duster_ip_recovery_250 <= USED 0
__trace_MIFI__LWIP__duster_ip_recovery_305 <= USED 0
__trace_MIFI__LWIP__lwip_atctl_1768 <= USED 0
__trace_MIFI__LWIP__lwip_atctl_380 <= USED 0
__trace_MIFI__LWIP__lwip_atctl_381 <= USED 0
__trace_MIFI__LWIP__lwip_atctl_382 <= USED 0
__trace_MIFI__LWIP__lwip_atctl_548 <= USED 0
__trace_MIFI__LWIP__lwip_atctl_901 <= USED 0
__trace_MIFI__LWIP__lwip_customer_700 <= USED 0
__trace_MIFI__LWIP__lwip_customer_701 <= USED 0
__trace_MIFI__LWIP__lwip_net_bridge_2130 <= USED 0
__trace_MIFI__LWIP__lwip_net_bridge_2135 <= USED 0
__trace_MIFI__LWIP__lwip_net_bridge_2136 <= USED 0
__trace_MIFI__LWIP__lwip_net_bridge_2160 <= USED 0
__trace_MIFI__LWIP__lwip_net_bridge_2200 <= USED 0
__trace_MIFI__LWIP__lwip_net_bridge_2220 <= USED 0
__trace_MIFI__LWIP__lwip_net_bridge_2230 <= USED 0
__trace_MIFI__utl__130 <= USED 0
__trace_MIFI__utl__131 <= USED 0
__trace_MIFI__utl__161 <= USED 0
__trace_MIFI__utl__162 <= USED 0
__trace_MIFI__utl__26 <= USED 0
__trace_MIFI__utl__65 <= USED 0
__trace_MIFI__utl__66 <= USED 0
__trace_MMI__LOG__uartlog <= USED 0
__trace_MM__Wifi__MM2WifiStartScan2FTModeReqProcess <= USED 0
__trace_MSG_IPC__DCXO__plAMsendGsmMeasureParams1 <= USED 0
__trace_MSG_IPC__DCXO__plDcxoProcessModeReport <= USED 0
__trace_MSG_IPC__DCXO__plSetDspToDcxoMode_1 <= USED 0
__trace_MSG_IPC__DCXO__plSetDspToDcxoMode_2 <= USED 0
__trace_MSG_IPC__DCXO__plSetRfTempretureReadMode <= USED 0
__trace_MSG_IPC__DCXO__plSetRfTempretureReadMode_2 <= USED 0
__trace_MSG_IPC__L1IpcDspMsgIpcHandler__DcxoIpcDspMsgIpcHandler <= USED 0
__trace_OSA__OSA_DUMP__OSAListAllCreatedBytesPoolStatus_Mem <= USED 0
__trace_OSA__OSA_DUMP__OSAListAllCreatedBytesPoolStatus_Name <= USED 0
__trace_OSA__OSA_DUMP__OSAListAllCreatedBytesPoolStatus_Usage <= USED 0
__trace_OSA__OSA_DUMP__OsaListAllCreatedBlockPools_1 <= USED 0
__trace_OSA__OSA_DUMP__OsaListAllCreatedBlockPools_2 <= USED 0
__trace_OSA__OSA_DUMP__OsaListAllCreatedEventFlags_1 <= USED 0
__trace_OSA__OSA_DUMP__OsaListAllCreatedEventFlags_2 <= USED 0
__trace_OSA__OSA_DUMP__OsaListAllCreatedMsgQs_1 <= USED 0
__trace_OSA__OSA_DUMP__OsaListAllCreatedMsgQs_2 <= USED 0
__trace_OSA__OSA_DUMP__OsaListAllCreatedMutexs_1 <= USED 0
__trace_OSA__OSA_DUMP__OsaListAllCreatedMutexs_2 <= USED 0
__trace_OSA__OSA_DUMP__OsaListAllCreatedSemaphores_1 <= USED 0
__trace_OSA__OSA_DUMP__OsaListAllCreatedSemaphores_2 <= USED 0
__trace_OSA__OSA_DUMP__OsaListAllCreatedTasks_1 <= USED 0
__trace_OSA__OSA_DUMP__OsaListAllCreatedTasks_HISR_2 <= USED 0
__trace_OSA__OSA_DUMP__OsaListAllCreatedTasks_TASK_2 <= USED 0
__trace_OSA__OSA_DUMP__OsaListAllCreatedTimers_1 <= USED 0
__trace_OSA__OSA_DUMP__OsaListAllCreatedTimers_2 <= USED 0
__trace_OSA__OSA__AccTimerStart <= USED 0
__trace_OSA__OSA__AccTimerStop <= USED 0
__trace_OSA__OsaMem__FreeList <= USED 0
__trace_OSA__OsaMem__MemBlk <= USED 0
__trace_OSA__OsaMem__PoolBroken <= USED 0
__trace_OSA__OsaMem__PoolOK <= USED 0
__trace_PM__AAM__AppsInfo <= USED 0
__trace_PM__CommPM__CPM_INFO_100 <= USED 0
__trace_PM__CommPM__CPM_INFO_101 <= USED 0
__trace_PM__CommPM__CPM_INFO_102 <= USED 0
__trace_PM__CommPM__CPM_INFO_107 <= USED 0
__trace_PM__CommPM__CPM_INFO_108 <= USED 0
__trace_PM__CommPM__CPM_INFO_109 <= USED 0
__trace_PM__CommPM__CPM_INFO_110 <= USED 0
__trace_PM__CommPM__CPM_INFO_111 <= USED 0
__trace_PM__CommPM__CPM_INFO_112 <= USED 0
__trace_PM__CommPM__CPM_INFO_113 <= USED 0
__trace_PM__CommPM__CPM_INFO_114 <= USED 0
__trace_PM__CommPM__CPM_INFO_115 <= USED 0
__trace_PM__CommPM__CPM_INFO_116 <= USED 0
__trace_PM__CommPM__CPM_INFO_117 <= USED 0
__trace_PM__CommPM__CPM_INFO_118 <= USED 0
__trace_PM__CommPM__CPM_INFO_119 <= USED 0
__trace_PM__CommPM__CPM_INFO_121 <= USED 0
__trace_PM__CommPM__CPM_INFO_122 <= USED 0
__trace_PM__CommPM__CPM_INFO_123 <= USED 0
__trace_PM__CommPM__CPM_INFO_124 <= USED 0
__trace_PM__CommPM__CPM_INFO_125 <= USED 0
__trace_PM__CommPM__CPM_INFO_126 <= USED 0
__trace_PM__CommPM__CommPMDDRStateSet <= USED 0
__trace_PM__CommPM__CommPMDDRStatusDisplay <= USED 0
__trace_PM__CommPM__DISABLE1 <= USED 0
__trace_PM__CommPM__DISABLE2 <= USED 0
__trace_PM__CommPM__DISABLE3 <= USED 0
__trace_PM__CommPM__DumpFlashFlushInfo_ <= USED 0
__trace_PM__CommPM__DutyCycleAlreadyStarted <= USED 0
__trace_PM__CommPM__DutyCycleAlreadyStopped <= USED 0
__trace_PM__CommPM__DutyCycleDisplayCore <= USED 0
__trace_PM__CommPM__DutyCycleDisplayCoreErr <= USED 0
__trace_PM__CommPM__DutyCycleDisplayInCycles <= USED 0
__trace_PM__CommPM__DutyCycleDspNotEnabled <= USED 0
__trace_PM__CommPM__DutyCycleMultipleResults <= USED 0
__trace_PM__CommPM__DutyCycleResetCount <= USED 0
__trace_PM__CommPM__DutyCycleStart <= USED 0
__trace_PM__CommPM__DutyCycleStop <= USED 0
__trace_PM__CommPM__FORCED2 <= USED 0
__trace_PM__CommPM__PMSetToDDRFullFuncIsOFF <= USED 0
__trace_PM__CommPM__PMSetToDDRFullFuncIsON <= USED 0
__trace_PM__CommPM__TCMStartFITimer_1 <= USED 0
__trace_PM__CommPM__TCMTestTimerHandle_1 <= USED 0
__trace_PM__CommPM__setMaxDDRAckDuration <= USED 0
__trace_PM__DisableSleep__CPM_INFO_000 <= USED 0
__trace_PM__FreqChange__AxiFreqChangeTo208 <= USED 0
__trace_PM__FreqChange__CpCoreFreqChangeTo832 <= USED 0
__trace_PM__FreqChange__CpCoreGetFreq <= USED 0
__trace_PM__FreqChange__PsramPhyFreqChangeTo156 <= USED 0
__trace_PM__FreqChange__PsramPhyFreqChangeTo208 <= USED 0
__trace_PM__FreqChange__PsramPhyFreqChangeTo533 <= USED 0
__trace_PM__FreqChange__PsramPhyFreqChangeTo600 <= USED 0
__trace_PM__FreqChange__PsramPhyFreqChangeTo711 <= USED 0
__trace_PM__FreqChange__PsramPhyGetFreq <= USED 0
__trace_PM__FreqChange__psram_phy_fc_config1 <= USED 0
__trace_PM__Logger__PM_INFO_01 <= USED 0
__trace_PM__Logger__PM_INFO_02 <= USED 0
__trace_PM__Logger__PM_INFO_03 <= USED 0
__trace_PM__Logger__PM_INFO_04 <= USED 0
__trace_PM__PMAPPS__TestData01 <= USED 0
__trace_PM__PMAPPS__TestData04 <= USED 0
__trace_PM__PMAPPS__TestData05 <= USED 0
__trace_PM__PMAPPS__TestData10 <= USED 0
__trace_PM__PMAPPS__TestData11 <= USED 0
__trace_PM__PMAPPS__TestData12 <= USED 0
__trace_PM__PMAPPS__TestData13 <= USED 0
__trace_PM__PRM__EnableGPBclose <= USED 0
__trace_PM__PRM__GetExceptionSrvc1 <= USED 0
__trace_PM__PRM__RM_EVENTS_GET1 <= USED 0
__trace_PM__PRM__RM_EVENTS_GET2 <= USED 0
__trace_PM__PRM__RM_EVENTS_GET3 <= USED 0
__trace_PM__PRM__SetExceptionSrvc1 <= USED 0
__trace_PM__PRM__SetExceptionSrvc2 <= USED 0
__trace_PM__PRM__SetResourceState1 <= USED 0
__trace_PM__RM__AllocDDRHighFreq0 <= USED 0
__trace_PM__RM__AllocDDRHighFreq1 <= USED 0
__trace_PM__RM__FreeDDRHighFreq0 <= USED 0
__trace_PM__RM__FreeDDRHighFreq1 <= USED 0
__trace_PM__RM__dumpRMCount_1 <= USED 0
__trace_PM__TestD2__err1 <= USED 0
__trace_PM__TestD2__err2 <= USED 0
__trace_PM__TestD2__err3 <= USED 0
__trace_PM__print_Seagull_frequency__CPM_INFO_20100122_1 <= USED 0
__trace_PM__print_Seagull_frequency__CPM_INFO_20100122_2 <= USED 0
__trace_PM__print_Seagull_frequency__CPM_INFO_20100122_3 <= USED 0
__trace_PM__print_Seagull_frequency__CPM_INFO_20100122_4 <= USED 0
__trace_PM__print_Seagull_frequency__CPM_INFO_20100122_5 <= USED 0
__trace_PM__print_Seagull_frequency__CPM_INFO_20100122_6 <= USED 0
__trace_PM__test_mips__CPM_INFO_0826_011 <= USED 0
__trace_PM__test_mips__CPM_INFO_0826_3 <= USED 0
__trace_PM__test_mips__CPM_INFO_0826_33 <= USED 0
__trace_PM__test_mips__CPM_INFO_0826_4 <= USED 0
__trace_PM__test_mips__CPM_INFO_0826_44 <= USED 0
__trace_PM__utils__AckReConfigureTestTimer1 <= USED 0
__trace_PM__utils__AckReConfigureTestTimer2 <= USED 0
__trace_PM__utils__PM_MFT_01 <= USED 0
__trace_PM__utils__PM_MFT_02 <= USED 0
__trace_PSNAS__HOTRESET__saveContext_begin <= USED 0
__trace_PSNAS__HOTRESET__saveContext_end <= USED 0
__trace_PSNAS__POWEROFF__saveContextForPowerOffOnKey_begin <= USED 0
__trace_PSNAS__abmm__abmmBmRequestGPRSService_41 <= USED 0
__trace_PSNAS__abmm__abmmBmRequestGPRSService_42 <= USED 0
__trace_PSNAS__abmm__abmmRmStopTimersWithoutHplmnTimer_1 <= USED 0
__trace_PS_2G__GRR__CR595_1 <= USED 0
__trace_PS_2G__GRR__CR595_11_0 <= USED 0
__trace_PS_2G__GRR__CR595_11_1 <= USED 0
__trace_PS_2G__GRR__CR595_11_2 <= USED 0
__trace_PS_2G__GRR__CR595_11_3 <= USED 0
__trace_PS_2G__GRR__CR595_11_4 <= USED 0
__trace_PS_2G__GRR__CR595_11_5 <= USED 0
__trace_PS_2G__GRR__CR595_11_6 <= USED 0
__trace_PS_2G__GRR__CR595_2 <= USED 0
__trace_PS_2G__GRR__CR595_2_00 <= USED 0
__trace_PS_2G__GRR__CR595_2_01 <= USED 0
__trace_PS_2G__GRR__CR595_2_02 <= USED 0
__trace_PS_2G__GRR__CR595_2_03 <= USED 0
__trace_PS_2G__GRR__CR595_2_11 <= USED 0
__trace_PS_2G__GRR__CR595_3 <= USED 0
__trace_PS_2G__GRR__CR595_4 <= USED 0
__trace_PS_2G__GRR__CR595_5 <= USED 0
__trace_PS_3G__ABMM__ABMM_RM_STATE_FUNC_abmmRmStateEcallExplicitReg <= USED 0
__trace_PS_3G__ABMM__AbNonCacheGetRecovryFlag <= USED 0
__trace_PS_3G__ABMM__SetUsimPowerSateApi_0 <= USED 0
__trace_PS_3G__ABMM__SetUsimPowerSateApi_2 <= USED 0
__trace_PS_3G__ABMM__abmmBlUpdateLteArfcnListsFromCuList <= USED 0
__trace_PS_3G__ABMM__abmmBlUpdateLteArfcnListsFromCuList_1 <= USED 0
__trace_PS_3G__ABMM__abmmBlmGetArfcnNvm_5 <= USED 0
__trace_PS_3G__ABMM__abmmBlmGetArfcnNvm_6 <= USED 0
__trace_PS_3G__ABMM__abmmBlmInsertArfcnNvm_2 <= USED 0
__trace_PS_3G__ABMM__abmmBlmUpdateAllArfcnNvm_1 <= USED 0
__trace_PS_3G__ABMM__abmmBmNewFddLteBandMode_1 <= USED 0
__trace_PS_3G__ABMM__abmmBmNewTdLteBandMode_1 <= USED 0
__trace_PS_3G__ABMM__abmmDoMmrAsRregInd_0 <= USED 0
__trace_PS_3G__ABMM__abmmGetCommonReserved3alue_0 <= USED 0
__trace_PS_3G__ABMM__abmmGetCommonReserved4Value_0 <= USED 0
__trace_PS_3G__ABMM__abmmLteListClearLists_1 <= USED 0
__trace_PS_3G__ABMM__abmmPlAddToTempFplmnList_1 <= USED 0
__trace_PS_3G__ABMM__abmmPlCopyAvailableLtePlmns_7 <= USED 0
__trace_PS_3G__ABMM__abmmRmContinue_1 <= USED 0
__trace_PS_3G__ABMM__abmmRmInterruptReg_1 <= USED 0
__trace_PS_3G__ABMM__abmmRmNarrowHplmnSearchNetworkMode_1 <= USED 0
__trace_PS_3G__ABMM__abmmRmPartiallyRegistered_1 <= USED 0
__trace_PS_3G__ABMM__abmmRmReceivedForbiddenNatLaInDualRatManualMode_1 <= USED 0
__trace_PS_3G__ABMM__abmmRmReceivedForbiddenPlmnInDualRatManualMode_1 <= USED 0
__trace_PS_3G__ABMM__abmmRmT3245EventHandle_1 <= USED 0
__trace_PS_3G__ABMM__abmmRmT3245Restart_1 <= USED 0
__trace_PS_3G__ABMM__abmmRmT3245Stop_1 <= USED 0
__trace_PS_3G__ABMM__abmmSendPeerSearchPlmnResult_0 <= USED 0
__trace_PS_3G__ABSM__absmProcessAlsiWriteSmCnfForStore_2 <= USED 0
__trace_PS_3G__EPDCP__ImsDefaultPdnDataReceived_1 <= USED 0
__trace_PS_3G__EPDCP__lteSnTraceDL <= USED 0
__trace_PS_3G__EPDCP__lteSnTraceNone <= USED 0
__trace_PS_3G__EPDCP__lteSnTraceStatus <= USED 0
__trace_PS_3G__EPDCP__lteSnTraceUL <= USED 0
__trace_PS_3G__EPDCP__lteSnTraceULDL <= USED 0
__trace_PS_3G__GRR__CC_CHANNEL_RELEASE_1 <= USED 0
__trace_PS_3G__GRR__CC_CHANNEL_RELEASE_2 <= USED 0
__trace_PS_3G__GRR__CC_CHANNEL_RELEASE_3 <= USED 0
__trace_PS_3G__GRR__CC_CHANNEL_RELEASE_4 <= USED 0
__trace_PS_3G__GRR__CC_CHANNEL_RELEASE_DEBUG_IE_FDD <= USED 0
__trace_PS_3G__GRR__CC_CHANNEL_RELEASE_DEBUG_IE_GSM <= USED 0
__trace_PS_3G__GRR__CC_CHANNEL_RELEASE_DEBUG_IE_INVALID <= USED 0
__trace_PS_3G__GRR__DEC_PCCO_MEAS_PARAM_ENH <= USED 0
__trace_PS_3G__GRR__DEC_PCCO_MEAS_PARAM_ENH_TDD <= USED 0
__trace_PS_3G__GRR__DEC_PCCO_MEAS_PARAM_NORMAL <= USED 0
__trace_PS_3G__GRR__DEC_PMO_MEAS_PARAM_ENH <= USED 0
__trace_PS_3G__GRR__DEC_PMO_MEAS_PARAM_NORMAL <= USED 0
__trace_PS_3G__GRR__DEC_PSI5_MEAS_PARAM_ENH <= USED 0
__trace_PS_3G__GRR__DEC_PSI5_MEAS_PARAM_NORMAL <= USED 0
__trace_PS_3G__GRR__DEC_SYS_INFO2QUA <= USED 0
__trace_PS_3G__GRR__DEC_SYS_INFO2QUA_3G_MEAS_FDD_1 <= USED 0
__trace_PS_3G__GRR__DEC_SYS_INFO2QUA_3G_MEAS_TDD_1 <= USED 0
__trace_PS_3G__GRR__DEC_SYS_INFO2QUA_ENH <= USED 0
__trace_PS_3G__GRR__EncodeR99MsRadAccCapbIE_0 <= USED 0
__trace_PS_3G__GRR__GpDecNcellParam_max <= USED 0
__trace_PS_3G__GRR__GpEncPacketResourceRequest_0a <= USED 0
__trace_PS_3G__GRR__GpEncPacketResourceRequest_0d <= USED 0
__trace_PS_3G__GRR__GpEncPacketResourceRequest_0f <= USED 0
__trace_PS_3G__GRR__GpEncPacketResourceRequest_1 <= USED 0
__trace_PS_3G__GRR__GpEncPacketResourceRequest_10 <= USED 0
__trace_PS_3G__GRR__GpEncPacketResourceRequest_2 <= USED 0
__trace_PS_3G__GRR__GpEncPacketResourceRequest_3 <= USED 0
__trace_PS_3G__GRR__GpEncPacketResourceRequest_4 <= USED 0
__trace_PS_3G__GRR__GpEncPacketResourceRequest_5 <= USED 0
__trace_PS_3G__GRR__GpEncPacketResourceRequest_6 <= USED 0
__trace_PS_3G__GRR__GpEncPacketResourceRequest_7 <= USED 0
__trace_PS_3G__GRR__GpEncPrrDecideContent_000 <= USED 0
__trace_PS_3G__GRR__GpEncPrrDecideContent_001 <= USED 0
__trace_PS_3G__GRR__GpEncPrrDecideContent_002 <= USED 0
__trace_PS_3G__GRR__GpEncPrrDecideContent_003 <= USED 0
__trace_PS_3G__GRR__GpEncPrrDecideContent_006 <= USED 0
__trace_PS_3G__GRR__GpEncPrrDecideContent_007 <= USED 0
__trace_PS_3G__GRR__GpEncPrrDecideContent_009 <= USED 0
__trace_PS_3G__GRR__GpEncPrrDecideContent_1 <= USED 0
__trace_PS_3G__GRR__GpEncPrrDecideContent_2 <= USED 0
__trace_PS_3G__GRR__GpEncPrrR99Part_1 <= USED 0
__trace_PS_3G__GRR__GpEncR99MsRadAccCapb_0 <= USED 0
__trace_PS_3G__GRR__GpEncR99MsRadAccCapb_1 <= USED 0
__trace_PS_3G__GRR__GpEncR99MsRadAccCapb_10 <= USED 0
__trace_PS_3G__GRR__GpEncR99MsRadAccCapb_11 <= USED 0
__trace_PS_3G__GRR__GpEncR99MsRadAccCapb_2 <= USED 0
__trace_PS_3G__GRR__GpEncR99MsRadAccCapb_3 <= USED 0
__trace_PS_3G__GRR__GpEncR99MsRadAccCapb_4 <= USED 0
__trace_PS_3G__GRR__GpEncR99MsRadAccCapb_5 <= USED 0
__trace_PS_3G__GRR__GpEncR99MsRadAccCapb_6 <= USED 0
__trace_PS_3G__GRR__GpEncR99MsRadAccCapb_6f <= USED 0
__trace_PS_3G__GRR__GpEncR99MsRadAccCapb_7 <= USED 0
__trace_PS_3G__GRR__GpEncR99MsRadAccCapb_8 <= USED 0
__trace_PS_3G__GRR__GpEncR99MsRadAccCapb_9 <= USED 0
__trace_PS_3G__GRR__IMM_ASS_EDGE_GPRS <= USED 0
__trace_PS_3G__GRR__LB_1 <= USED 0
__trace_PS_3G__GRR__LB_12 <= USED 0
__trace_PS_3G__GRR__LB_2 <= USED 0
__trace_PS_3G__GRR__LB_3 <= USED 0
__trace_PS_3G__GRR__PDCH_PMO_NO_PEMR_60 <= USED 0
__trace_PS_3G__GRR__PDCH_PMO_NO_PEMR_70 <= USED 0
__trace_PS_3G__GRR__PDCH_PMO_NO_PEMR_80 <= USED 0
__trace_PS_3G__KIOS__WarnAssertFail <= USED 0
__trace_PS_3G__MM__DecodeNonAccStrMessage <= USED 0
__trace_PS_3G__MM__L3DecodeGprsMessage <= USED 0
__trace_PS_3G__MM__L3EncodeGprsMessage <= USED 0
__trace_PS_3G__MM__L3EncodeMessage <= USED 0
__trace_PS_3G__MM__MmIsPendingPlmnSearch_1 <= USED 0
__trace_PS_3G__MM__MmMain_0606 <= USED 0
__trace_PS_3G__Mm__MmIsCallOngoing_2 <= USED 0
__trace_PS_3G__Mm__MmIsIratToLteAllowed_1 <= USED 0
__trace_PS_3G__PSM__saveContext_2 <= USED 0
__trace_PS_3G__RR_COM__rrDsCheckIfSendIratDsPagingDrxLengthChangeInd_0 <= USED 0
__trace_PS_3G__RR_COM__rrDsGetActiveSubstituted_0 <= USED 0
__trace_PS_3G__RR_COM__rrDsGetGsmPagingDrxLength_0 <= USED 0
__trace_PS_3G__RR_COM__rrDsGetGsmPagingDrxLength_1 <= USED 0
__trace_PS_3G__RR_COM__rrDsGetUmtsPagingDrxLength_0 <= USED 0
__trace_PS_3G__RR_COM__rrDsGetUmtsPagingDrxLength_1 <= USED 0
__trace_PS_3G__RR_COM__rrDsSavePagingDrxLength_1 <= USED 0
__trace_PS_3G__RR_COM__rrDsSendIratDsPagingDrxLengthChangeInd_0 <= USED 0
__trace_PS_3G__RR_COM__rrDsSendIratDsVoiceBadInd_0 <= USED 0
__trace_PS_3G__SAC_DAT__sacGetCurrentNsapi <= USED 0
__trace_PS_3G__SM__cancelRel5QoS_1 <= USED 0
__trace_PS_3G__SM__setRel5QoS_1 <= USED 0
__trace_PS_3G__UMAC__f8Enhanced0 <= USED 0
__trace_PS_3G__UMAC__f8Enhanced01 <= USED 0
__trace_PS_3G__UMAC__f8Enhanced02 <= USED 0
__trace_PS_3G__UMAC__f8Enhanced04 <= USED 0
__trace_PS_3G__UMAC__f8Enhanced05 <= USED 0
__trace_PS_3G__UMAC__f8Enhanced10 <= USED 0
__trace_PS_3G__UMAC__f8Enhanced11 <= USED 0
__trace_PS_3G__UMAC__f8Enhanced14 <= USED 0
__trace_PS_3G__URRC_CSRR__IsRedirectionFailureHandlingEnable_0 <= USED 0
__trace_PS_3G__URRC_CSRR__NeedAbandonGsmBandSearctWhenRedirectFromLTE_0 <= USED 0
__trace_PS_3G__URRC_CSRR__NeedAbandonGsmBandSearctWhenRedirectFromLTE_1 <= USED 0
__trace_PS_3G__URRC_CSR_Priority_1__RrIsTestSimGcfMode_1 <= USED 0
__trace_PS_3G__pool__PoolRuningUsage <= USED 0
__trace_PS_4G_ERRC__COMDB__RrComDbSetManuallySelectedPlmn_1 <= USED 0
__trace_PS_4G_ERRC__PLMS__PlmsCheckIfFgPlmnOngoing_1 <= USED 0
__trace_PS_4G_ERRC__PLMS__PlmsDbInitInterferenceDetection_1 <= USED 0
__trace_PS_4G_ERRC__PLMS__PlmsFndAddEarfcnToScanOrderTable_1 <= USED 0
__trace_PS_4G_ERRC__PLMS__PlmsFndDetermineInterferedCarriersNum_1 <= USED 0
__trace_PS_4G_ERRC__PLMS__PlmsFndDetermineInterferedCarriersNum_2 <= USED 0
__trace_PS_4G_ERRC__PLMS__PlmsScnAllocationWbScoreResultTable_1 <= USED 0
__trace_PS_4G_ERRC__PLMS__PlmsScnAllocationWbScoreResultTable_2 <= USED 0
__trace_PS_4G_ERRC__PLMS__PlmsSetOptimizations_1 <= USED 0
__trace_PS_4G__MM__EmmImsServiceEndProtectionTimerExpiry_1 <= USED 0
__trace_PS_4G__MM__EmmServiceRejectReceived_0 <= USED 0
__trace_PS_4G__MM__EmmStartT3423_1 <= USED 0
__trace_PS_4G__MM__MmIsInCUCCNetWork_1 <= USED 0
__trace_PS_4G__MM__MmIsMmrRegReqInQueue_0 <= USED 0
__trace_PS_4G__PLMS__PlmsStoreSeqNum_1 <= USED 0
__trace_PS_PLT__IPNET_DL__DLLogDisable <= USED 0
__trace_PS_PLT__IPNET_DL__DLLogEnable <= USED 0
__trace_PS_PLT__IPNET_DL__usbnet_allocmem_extend_debug_0 <= USED 0
__trace_PS_PLT__IPNET_DL__usbnet_allocmem_extend_debug_1 <= USED 0
__trace_PS_PLT__IPNET_DL__usbnet_freemem_debug <= USED 0
__trace_PS_PLT__IPNET_DL__usbnet_putmem_debug <= USED 0
__trace_PS_PLT__IPNET_UL__IpNetLwipGetUlPacketPoll_debug_0 <= USED 0
__trace_PS_PLT__IPNET_UL__IpNetLwipGetUlPacketPoll_debug_1 <= USED 0
__trace_PS_PLT__IPNET_UL__LogDisable <= USED 0
__trace_PS_PLT__IPNET_UL__LogEnable <= USED 0
__trace_PS_PLT__IpNetUsbGetUlPacketPoll__DiscardPacket <= USED 0
__trace_PS_PLT__IpNetUsbGetUlPacketPoll__GetPacket <= USED 0
__trace_PS_PLT__LWIP_DL__dump_lwip_memory_info_DL <= USED 0
__trace_PS_PLT__LWIP_DL__lwip_downlink_putmem_debug <= USED 0
__trace_PS_PLT__LWIP_UL__dump_lwip_memory_info_UL <= USED 0
__trace_PS_PLT__ModemTFT__FindOldTft <= USED 0
__trace_PS_PLT__ModemTFT__MatchInfofindIp6_1 <= USED 0
__trace_PS_PLT__ModemTFT__MatchInfofindIp6_2 <= USED 0
__trace_PS_PLT__ModemTFT__MatchInfofindIp6_dstport <= USED 0
__trace_PS_PLT__ModemTFT__MatchInfofindIp6_sorceport <= USED 0
__trace_PS_PLT__ModemTFT__PFNodeAllocFailed <= USED 0
__trace_PS_PLT__ModemTFT__PFReconfigure <= USED 0
__trace_PS_PLT__ModemTFT____tft_delete_tft <= USED 0
__trace_PS_PLT__ModemTFT____tft_delete_tft_1 <= USED 0
__trace_PS_PLT__ModemTFT__addNewPFOK <= USED 0
__trace_PS_PLT__ModemTFT__addTFTInorderEPI_0 <= USED 0
__trace_PS_PLT__ModemTFT__addTFTInorderEPI_1 <= USED 0
__trace_PS_PLT__ModemTFT__ip_nat_get_cid_port_IP6_Add <= USED 0
__trace_PS_PLT__ModemTFT__ip_nat_get_cid_port_IP6_port <= USED 0
__trace_PS_PLT__ModemTFT__ip_nat_get_epsid1forIp4_No_Proto <= USED 0
__trace_PS_PLT__ModemTFT__ip_nat_get_epsid1forIp6 <= USED 0
__trace_PS_PLT__ModemTFT__ipfragment_failure <= USED 0
__trace_PS_PLT__ModemTFT__ipv6fragment_failure <= USED 0
__trace_PS_PLT__PrintCid__GetDefault4g_parameter_invalid <= USED 0
__trace_PS_PLT__PsMemTest__lwipDownMemTest <= USED 0
__trace_PS_PLT__PsMemTest__lwipUpMemTest <= USED 0
__trace_PS_PLT__TGSINK__SINKSEND <= USED 0
__trace_PS_PLT__resume__ipnet_rx_buffer <= USED 0
__trace_PS_RR__COMDB__RrComDbCheckRequestPlmnOfTwoSimCard_4 <= USED 0
__trace_PS__FDI__DATA_ERASE <= USED 0
__trace_PS__PS_INIT__Finished <= USED 0
__trace_PS__PS_INIT__Started <= USED 0
__trace_PS__UMTS__CIPHERING_PARAMS_CS <= USED 0
__trace_PS__UMTS__CIPHERING_PARAMS_PS <= USED 0
__trace_SAC__DATA__SAC_TRACE_API <= USED 0
__trace_SAC__PS__sacPsGetSim4GQciValue_1 <= USED 0
__trace_SAC__SACDEV__sacCiDevPrimGetInternalRevisionIdReqFunc_01 <= USED 0
__trace_SAC__SACLIB__sacShCheckSpecPendingCiReq_1 <= USED 0
__trace_SAC__SACPS__sacPsProcessApexMmBandInd_3 <= USED 0
__trace_SW_PLAT__AAAP__holdPLP_reply <= USED 0
__trace_SW_PLAT__AAAP__plpVersionReport <= USED 0
__trace_SW_PLAT__AAAP__setPlpFiltersFailed <= USED 0
__trace_SW_PLAT__AAAP__setPlpFiltersRep <= USED 0
__trace_SW_PLAT__BSP__print_specialPShandle <= USED 0
__trace_SW_PLAT__CONTROL__ArmRead16Reply <= USED 0
__trace_SW_PLAT__CONTROL__ArmRead32Reply <= USED 0
__trace_SW_PLAT__CONTROL__ArmWrite16Reply <= USED 0
__trace_SW_PLAT__CONTROL__ArmWrite32Reply <= USED 0
__trace_SW_PLAT__CONTROL__GetSteppingID_rep <= USED 0
__trace_SW_PLAT__CONTROL__ICacheParityErrorCount_rep <= USED 0
__trace_SW_PLAT__CONTROL__free_reply <= USED 0
__trace_SW_PLAT__CONTROL__malloc_reply <= USED 0
__trace_SW_PLAT__CONTROL__memTestReply <= USED 0
__trace_SW_PLAT__CONTROL__memTestReplyRead <= USED 0
__trace_SW_PLAT__CONTROL__memTestReplyWrite <= USED 0
__trace_SW_PLAT__CONTROL__readUINT16 <= USED 0
__trace_SW_PLAT__CONTROL__readUINT32 <= USED 0
__trace_SW_PLAT__CONTROL__readUINT8 <= USED 0
__trace_SW_PLAT__CONTROL__writeUINT8 <= USED 0
__trace_SW_PLAT__DEBUG__CpuProductID <= USED 0
__trace_SW_PLAT__DIAG__TestDiagCommReply <= USED 0
__trace_SW_PLAT__DIAG__setGLFeatureFlag <= USED 0
__trace_SW_PLAT__GripNotifyAP__GRIPSENSOR0 <= USED 0
__trace_SW_PLAT__GripNotifyAP__GRIPSENSOR1 <= USED 0
__trace_SW_PLAT__I2C__I2CMasterNotifyDataReceived2 <= USED 0
__trace_SW_PLAT__I2C__I2C_CTRL_FAST1 <= USED 0
__trace_SW_PLAT__I2C__I2C_CTRL_SLOW <= USED 0
__trace_SW_PLAT__I2C__I2C_ERR <= USED 0
__trace_SW_PLAT__I2C__I2C_ERROR_INFO_READ <= USED 0
__trace_SW_PLAT__I2C__I2C_ERROR_INFO_WRITE <= USED 0
__trace_SW_PLAT__I2C__I2C_FAST <= USED 0
__trace_SW_PLAT__I2C__I2C_FAST1 <= USED 0
__trace_SW_PLAT__I2C__I2C_GOT_ERROR <= USED 0
__trace_SW_PLAT__I2C__I2C_HELP <= USED 0
__trace_SW_PLAT__I2C__I2C_HELP1 <= USED 0
__trace_SW_PLAT__I2C__I2C_HELP2 <= USED 0
__trace_SW_PLAT__I2C__I2C_HELP3 <= USED 0
__trace_SW_PLAT__I2C__I2C_HELP4 <= USED 0
__trace_SW_PLAT__I2C__I2C_HELP5 <= USED 0
__trace_SW_PLAT__I2C__I2C_HELP_EXAMP1 <= USED 0
__trace_SW_PLAT__I2C__I2C_HELP_EXAMP2 <= USED 0
__trace_SW_PLAT__I2C__I2C_MICCO_FAST <= USED 0
__trace_SW_PLAT__I2C__I2C_MICCO_SLOW <= USED 0
__trace_SW_PLAT__I2C__I2C_MUX_BOERNE1 <= USED 0
__trace_SW_PLAT__I2C__I2C_MUX_BOERNE2 <= USED 0
__trace_SW_PLAT__I2C__I2C_NO_REPEAT_START <= USED 0
__trace_SW_PLAT__I2C__I2C_REC_END <= USED 0
__trace_SW_PLAT__I2C__I2C_REPEAT_START <= USED 0
__trace_SW_PLAT__I2C__I2C_RESET <= USED 0
__trace_SW_PLAT__I2C__I2C_SLOW1 <= USED 0
__trace_SW_PLAT__I2C__I2C_TEST_MICCO_D1D2_1 <= USED 0
__trace_SW_PLAT__I2C__I2C_TEST_MICCO_D1D2_2 <= USED 0
__trace_SW_PLAT__I2C__NotifyErrorCBack <= USED 0
__trace_SW_PLAT__I2C__i2cMicco_I2CMasterReceive1 <= USED 0
__trace_SW_PLAT__I2C__i2cMicco_I2CMasterReceive1_retry <= USED 0
__trace_SW_PLAT__I2C__i2cMicco_I2CMasterReceive2 <= USED 0
__trace_SW_PLAT__I2C__i2cMicco_I2CMasterSend1 <= USED 0
__trace_SW_PLAT__I2C__i2cMicco_I2CMasterSend1_retry <= USED 0
__trace_SW_PLAT__I2C__i2cMicco_I2CMasterSend2 <= USED 0
__trace_SW_PLAT__MRD__xf_partition_erase <= USED 0
__trace_SW_PLAT__MRD__xf_partition_read <= USED 0
__trace_SW_PLAT__MRD__xf_partition_write <= USED 0
__trace_SW_PLAT__NVM__BAD_ComBasicCfg <= USED 0
__trace_SW_PLAT__NVM__Create <= USED 0
__trace_SW_PLAT__NVM__CreateBasic <= USED 0
__trace_SW_PLAT__NVM__NoFile <= USED 0
__trace_SW_PLAT__NVM__OldVerUpdate <= USED 0
__trace_SW_PLAT__NVM__platNvmPmuFlagRead1 <= USED 0
__trace_SW_PLAT__NVM__platNvmPmuFlagRead2 <= USED 0
__trace_SW_PLAT__NVM__utlAtCmdGetSetting <= USED 0
__trace_SW_PLAT__OSA_UTIL__OSA_UTIL_RESUME <= USED 0
__trace_SW_PLAT__OSA_UTIL__OSA_UTIL_SUSPEND <= USED 0
__trace_SW_PLAT__OSVERSION__diagGetOSVersion <= USED 0
__trace_SW_PLAT__PERFORMANCE__IdleTaskCreateError <= USED 0
__trace_SW_PLAT__PERFORMANCE__IdleTaskCreateError1 <= USED 0
__trace_SW_PLAT__PERFORMANCE__QueryLoadReply <= USED 0
__trace_SW_PLAT__RDA__ReliableDataUnPackDo <= USED 0
__trace_SW_PLAT__RDA__rd_Port_DataInd <= USED 0
__trace_SW_PLAT__RTC__TIME_GET <= USED 0
__trace_SW_PLAT__TAVOR_VER_INFO__TAVOR_Packages1 <= USED 0
__trace_SW_PLAT__TAVOR_VER_INFO__TAVOR_Packages10 <= USED 0
__trace_SW_PLAT__TAVOR_VER_INFO__TAVOR_Packages11 <= USED 0
__trace_SW_PLAT__TAVOR_VER_INFO__TAVOR_Packages12 <= USED 0
__trace_SW_PLAT__TAVOR_VER_INFO__TAVOR_Packages13 <= USED 0
__trace_SW_PLAT__TAVOR_VER_INFO__TAVOR_Packages14 <= USED 0
__trace_SW_PLAT__TAVOR_VER_INFO__TAVOR_Packages15 <= USED 0
__trace_SW_PLAT__TAVOR_VER_INFO__TAVOR_Packages16 <= USED 0
__trace_SW_PLAT__TAVOR_VER_INFO__TAVOR_Packages17 <= USED 0
__trace_SW_PLAT__TAVOR_VER_INFO__TAVOR_Packages18 <= USED 0
__trace_SW_PLAT__TAVOR_VER_INFO__TAVOR_Packages19 <= USED 0
__trace_SW_PLAT__TAVOR_VER_INFO__TAVOR_Packages2 <= USED 0
__trace_SW_PLAT__TAVOR_VER_INFO__TAVOR_Packages4 <= USED 0
__trace_SW_PLAT__TAVOR_VER_INFO__TAVOR_Packages5 <= USED 0
__trace_SW_PLAT__TAVOR_VER_INFO__TAVOR_Packages6 <= USED 0
__trace_SW_PLAT__TAVOR_VER_INFO__TAVOR_Packages8 <= USED 0
__trace_SW_PLAT__TAVOR_VER_INFO__TAVOR_Packages9 <= USED 0
__trace_SW_PLAT__WATCHDOG__exeReset <= USED 0
__trace_SW_PLAT__partition_ops__erase <= USED 0
__trace_SW_PLAT__partition_ops__write <= USED 0
__trace_SW_PLAT__utilities__DisableModeChange <= USED 0
__trace_SW_PLAT__utilities__EnableModeChange <= USED 0
__trace_SYSTEM__PROD__AT_SER_OK <= USED 0
__trace_SYSTEM__SYSNVM__SYS_NVM_CreateCompNew <= USED 0
__trace_SYSTEM__SYSNVM__SYS_NVM_CreateCompNew1 <= USED 0
__trace_SYSTEM__SYSNVM__SYS_NVM_CreateCompPsInitAuto <= USED 0
__trace_SYSTEM__SYSNVM__SYS_NVM_CreateNew <= USED 0
__trace_SYSTEM__SYSNVM__SYS_NVM_CreateNew1 <= USED 0
__trace_SYSTEM__SYSNVM__SYS_NVM_CreateNew2 <= USED 0
__trace_SYSTEM__SYSNVM__SYS_NVM_CreateNew3 <= USED 0
__trace_SYSTEM__SYSNVM__SYS_NVM_CreateNew6 <= USED 0
__trace_SYSTEM__SYSNVM__SYS_NVM_CreateNew7 <= USED 0
__trace_SYSTEM__SYSNVM__SYS_NVM_CreateNew71 <= USED 0
__trace_SYSTEM__SYSNVM__SYS_NVM_CreatePsInitAuto1 <= USED 0
__trace_SYSTEM__SYSNVM__psVendorPrintf_1 <= USED 0
__trace_SYSTEM__SYSNVM__psVendorPrintf_2 <= USED 0
__trace_SYSTEM__SYSNVM__psVendorPrintf_3 <= USED 0
__trace_SYSTEM__SYSNVM__psVendorPrintf_4 <= USED 0
__trace_SYSTEM__SYSNVM__psVendorPrintf_5 <= USED 0
__trace_SYSTEM__SYSNVM__psVendorPrintf_7 <= USED 0
__trace_SYSTEM__SYSNVM__psVendorPrintf_8 <= USED 0
__trace_SYSTEM__SYSNVM__psVendorPrintf_9 <= USED 0
__trace_TSX_CTRL__MSG_IPC__TsxCtrlMsgIpcHandler <= USED 0
__trace_TSX_CTRL__NVM_CTRL__TSXCtrl_WriteNVMFile <= USED 0
__trace_TSX_CTRL__NVM_CTRL__TSXCtrl_WriteNVMFile_FindFail <= USED 0
__trace_TSX_CTRL__NVM_CTRL__TSXCtrl_WriteNVMFile_NVMOpenFail <= USED 0
__trace_TSX_CTRL__NVM_CTRL__TSXCtrl_WriteNVMFile_Success <= USED 0
__trace_TSX_CTRL__NVM_CTRL__TSXTCCtrl_CreateNVMFile_1 <= USED 0
__trace_TSX_CTRL__NVM_CTRL__TSXTCCtrl_CreateNVMFile_Fail <= USED 0
__trace_TSX_CTRL__NVM_CTRL__TSXTCCtrl_CreateNVMFile_OK <= USED 0
__trace_TSX_CTRL__NVM_CTRL__TSXTCCtrl_CreateNVMFile_Shell_1 <= USED 0
__trace_TSX_CTRL__NVM_CTRL__TSXTCCtrl_CreateNVMFile_Shell_2 <= USED 0
__trace_TSX_CTRL__NVM_CTRL__TSXTCCtrl_CreateNVMFile_Shell_3 <= USED 0
__trace_TSX_CTRL__PolyFit__TSXPolyShell_0 <= USED 0
__trace_TSX_CTRL__PolyFit__TSXPolyShell_1 <= USED 0
__trace_TSX_CTRL__PolyFit__TsxCtrlLimitFRange_0 <= USED 0
__trace_TSX_CTRL__PolyFit__TsxCtrlTCScore_0 <= USED 0
__trace_TSX_CTRL__TC_TASK__TsxTCTask_1 <= USED 0
__trace_TSX_CTRL__TC_TASK__TsxTCTask_2 <= USED 0
__trace_TSX_CTRL__TSX_TEST__AdcOutVerify_1 <= USED 0
__trace_TSX_CTRL__TSX_TEST__AdcOutVerify_2 <= USED 0
__trace_TSX_CTRL__TSX_TEST__AdcOutVerify_3 <= USED 0
__trace_TSX_CTRL__TSX_TEST__AdcOutVerify_4 <= USED 0
__trace_TSX_CTRL__TSX_TEST__DacOutVerify_1 <= USED 0
__trace_TSX_CTRL__TSX_TEST__TSXTCTestCmd_1 <= USED 0
__trace_TSX_CTRL__TSX_TEST__TSXTCTestCmd_2 <= USED 0
__trace_TSX_CTRL__TSX_TEST__TSXTCTestCmd_3 <= USED 0
__trace_TSX_CTRL__TSX_TEST__TSX_Verification_1 <= USED 0
__trace_TSX_CTRL__TSX_TEST__TsxCtrlRegCompare <= USED 0
__trace_TSX_CTRL__TSX_TEST__TsxCtrlRegVerif_1 <= USED 0
__trace_TSX_CTRL__TSX_TEST__TsxCtrlRegVerif_2 <= USED 0
__trace_TSX_CTRL__TSX_TEST__TsxCtrlRegVerif_3 <= USED 0
__trace_TSX_CTRL__TSX_TEST__TsxCtrlRegVerif_4 <= USED 0
__trace_TSX_CTRL__TSX_TEST__TsxRegUpdate_1 <= USED 0
__trace_TSX_CTRL__TSX_TEST__TsxRegUpdate_2 <= USED 0
__trace_TSX_CTRL__TSX_TEST__TsxRegUpdate_3 <= USED 0
__trace_TSX_CTRL__TSX_TEST__TsxVerifyTask_1 <= USED 0
__trace_TSX_CTRL__TSX_TEST__TsxVerifyTask_2 <= USED 0
__trace_USIMLOG__USIM_DetectFoundByHW__LOG001 <= USED 0
__trace_USIMLOG__USIM_DetectFoundByHW__LOG002 <= USED 0
__trace_USIMLOG__USIM_DetectFoundByHW__LOG003 <= USED 0
__trace_USIMLOG__usim_2_det_hisr__LOG001 <= USED 0
__trace_USIMLOG__usim_2_det_task_entry__LOG001 <= USED 0
__trace_USIMLOG__usim_2_det_task_entry__LOG003 <= USED 0
__trace_USIMLOG__usim_2_det_task_entry__LOG005 <= USED 0
__trace_USIMLOG__usim_2_det_task_entry__LOG006 <= USED 0
__trace_USIMLOG__usim_2_det_task_entry__LOG008 <= USED 0
__trace_USIMLOG__usim_2_det_task_entry__LOG010 <= USED 0
__trace_USIMLOG__usim_2_det_task_entry__LOG012 <= USED 0
__trace_USIMLOG__usim_2_det_timer_handler__LOG001 <= USED 0
__trace_WIFI__AbortWifi__getWifiAbortReqProcess_2 <= USED 0
__trace_WIFI__Abort__getWifiAbortReqProcess <= USED 0
__trace_WIFI__BG__WifiBgStartScanReqProcess2 <= USED 0
__trace_WIFI__BG__setTimer2WifiBg2rrDsSendIratDsWifiStartReq <= USED 0
__trace_WIFI__CpCoreFreqChange__PreprocessWifiHotspotInfoIn_Err4 <= USED 0
__trace_WIFI__FAKE__plWIFIMainLoop_2 <= USED 0
__trace_WIFI__FAKE__plWIFIMainLoop_3 <= USED 0
__trace_WIFI__FINISHCNF__getPhyGapFinishIndProcess_3 <= USED 0
__trace_WIFI__INIT__WifiPostFreePoolMainError <= USED 0
__trace_WIFI__Judge__judgeBgPreprocessWifiHotspotInfoInd_3 <= USED 0
__trace_WIFI__Judge__judgeBgPreprocessWifiHotspotInfoInd_4 <= USED 0
__trace_WIFI__Judge__judgeLeftTimetoWifiScanReq_1 <= USED 0
__trace_WIFI__Judge__judgeLeftTimetoWifiScanReq_10 <= USED 0
__trace_WIFI__Judge__judgeLeftTimetoWifiScanReq_2 <= USED 0
__trace_WIFI__Judge__judgeLeftTimetoWifiScanReq_3 <= USED 0
__trace_WIFI__Judge__judgeLeftTimetoWifiScanReq_4 <= USED 0
__trace_WIFI__Judge__judgeLeftTimetoWifiScanReq_6 <= USED 0
__trace_WIFI__Judge__judgeLeftTimetoWifiScanReq_7 <= USED 0
__trace_WIFI__Judge__judgeLeftTimetoWifiScanReq_8 <= USED 0
__trace_WIFI__LOw_MAIN_LOOP__MainLoop_MsgTrace1_A <= USED 0
__trace_WIFI__MAIN_LOOP__MainLoop_MsgTrace1_A <= USED 0
__trace_WIFI__ML__plWIFIlowMainLoop <= USED 0
__trace_WIFI__ML__plWIFIlowMainLoop2 <= USED 0
__trace_WIFI__MMBg__WifiBgStartScanReqProcess <= USED 0
__trace_WIFI__MM__MM2WifiStartScan2FTModeReqProcess <= USED 0
__trace_WIFI__MM__MM2WifiStartScan2FTModeReqProcess1 <= USED 0
__trace_WIFI__MM__MM2WifiStartScanReqProcess1 <= USED 0
__trace_WIFI__MM__MM2WifiStartScanReqProcess2 <= USED 0
__trace_WIFI__MM__MM2WifiStartScanReqProcess3 <= USED 0
__trace_WIFI__MM__MM2WifiStartScanReqProcess_err2 <= USED 0
__trace_WIFI__MM__MM2WifiStopScanReqProcess <= USED 0
__trace_WIFI__MM__getWifiAbortReq_2 <= USED 0
__trace_WIFI__MM__getWifiAbortReq_error <= USED 0
__trace_WIFI__PrintDebug__PrintLeftTimeInfor_1 <= USED 0
__trace_WIFI__PrintDebug__PrintLeftTimeInfor_2 <= USED 0
__trace_WIFI__PrintDebug__PrintLeftTimeInfor_3 <= USED 0
__trace_WIFI__STOPCNF__getPhyStopScanCnfProcess_2 <= USED 0
__trace_WIFI__TIMER__Timerexpire2Wifi2rrDsSendIratDsWifiStartReq <= USED 0
__trace_WIFI__TIMER__Timerexpire2Wifi2rrDsSendIratDsWifiStartReq_1 <= USED 0
__trace_WIFI__WIFI__gfiScanGapStartInd_3 <= USED 0
__trace_WIFI__WML__plWIFIMain <= USED 0
__trace_WIFI__fgi__gfiWifiScanAbortCnf <= USED 0
__trace_WIFI__getPhyScanIndProcess14__MSG_WIFI_SCAN_IND12 <= USED 0
__trace_WIFI__getPhyScanIndProcess24__MSG_WIFI_SCAN_IND22 <= USED 0
__trace_WIFI__getPhyScanIndProcess_4__MSG_WIFI_SCAN_IND_2 <= USED 0
__trace_WIFI__getPhyScanIndProcess_equa21__MSG_WIFI_SCAN_IND_1 <= USED 0
__trace_WIFI__getPhyScanIndProcess_equa31__MSG_WIFI_SCAN_IND_1 <= USED 0
__trace_WIFI__getPhyScanIndProcess_equal__MSG_WIFI_SCAN_IND_1 <= USED 0
__trace_WIFI__gfi__gfiScanGapStartInd <= USED 0
__trace_WIFI__gfi__gfiScanGapStartInd_1 <= USED 0
__trace_WIFI__gfi__gfiScanGapStartInd_Err <= USED 0
__trace_WIFI__gwfi__gwfiScanGapStartIndProcess_2 <= USED 0
__trace_WIFI__hotspotind__PreprocessWifiHotspotInfoIn_3 <= USED 0
__trace_WIFI__hotspotind__PreprocessWifiHotspotInfoIn_Err4 <= USED 0
__trace_WIFI__hotspotind__PreprocessWifiHotspotInfoInd2 <= USED 0
__trace_WIFI__hotspotind__PreprocessWifiHotspotInfoIndErr <= USED 0
__trace_WIFI__ipc__L1IpcDspMsgIpcWIFIHandler <= USED 0
__trace_WIFI__ipc__L1IpcDspMsgIpcWIFIHandlerErr <= USED 0
__trace_WIFI__ipc__L1IpcDspMsgIpcWIFIHandler_2 <= USED 0
__trace_WIFI__ipc__L1IpcDspMsgIpcWIFIHandler_6 <= USED 0
__trace_WIFI__ipc__send2WIFI_MsgQ_Err <= USED 0
__trace_WIFI__pgetPhyScanIndProcess_5__MSG_WIFI_SCAN_IND_1 <= USED 0
__trace_WIFI__rrcWifi__Wifi2rrDsSendIratDsWifiStartReq <= USED 0
__trace_WIFI__rrcWifi__getIratDsWifiStartCnfProcess <= USED 0
__trace_WIFI__rrcWifi__getRrcWifiStartCnf_5 <= USED 0
__trace_WIFI__rrcWifi__getRrcWifiStartCnf_6 <= USED 0
__trace_WIFI__task__AbortTriggerSource_RRC6 <= USED 0
__trace_WIFI__task__AbortTriggerSource_WB_5 <= USED 0
__trace_WIFI__task__PreCpCoreFreqChangeTo416_1 <= USED 0
__trace_WIFI__task__PreCpCoreFreqChangeTo416_2 <= USED 0
__trace_WIFI__task__PreCpCoreFreqChangeTo416_Err <= USED 0
__trace_WIFI__task__PreprocessWifiHotspotInfoInd <= USED 0
__trace_WIFI__task__getPhyScanIndProcess_1 <= USED 0
__trace_WIFI__task__getPhyScanIndProcess_10 <= USED 0
__trace_WIFI__task__getPhyScanIndProcess_12 <= USED 0
__trace_WIFI__task__getPhyScanIndProcess_15 <= USED 0
__trace_WIFI__task__getPhyScanIndProcess_report <= USED 0
__trace_WIFI__task__getPhyScanIndProcess_start2 <= USED 0
__trace_WIFI__task__getPhyScanIndProcess_yiyuanerror2 <= USED 0
__trace_WIFI__task__gwfiScanGapStartIndProcess <= USED 0
__trace_WIFI__task__plWIFIMainLoopMsgHandler <= USED 0
__trace_WIFI__task__plWIFIMainLoopMsgHandler1 <= USED 0
__trace_WIFI__task__plWIFIlowMainLoopMsgHandler3 <= USED 0
__trace_WIFI__timerexpire__Timerexpire2Wifi2rrDsSendIratDsWifiStartReqProcess_1 <= USED 0
__trace_WIFI__wifiind__memBssidcmpNotEqual_2 <= USED 0
__trace_WIFI__wifiind__memBssidcmpNotEqual_3 <= USED 0
__trace_WIFI__wifiind__memBssidcmpWithZero1 <= USED 0
__trace_WS__LOG__PSlog <= USED 0
__trace_diag__throughput__Test_02 <= USED 0
__trace_generic_mini_no_params_func <= USED 0
__trace_generic_mini_params_func <= USED 0
__trace_generic_no_params_func_000 <= USED 0
__trace_generic_no_params_func_001 <= USED 0
__trace_generic_no_params_func_002 <= USED 0
__trace_generic_no_params_func_003 <= USED 0
__trace_generic_no_params_func_004 <= USED 0
__trace_generic_no_params_func_005 <= USED 0
__trace_generic_no_params_func_006 <= USED 0
__trace_generic_no_params_func_007 <= USED 0
__trace_generic_no_params_func_008 <= USED 0
__trace_generic_no_params_func_009 <= USED 0
__trace_generic_no_params_func_010 <= USED 0
__trace_generic_no_params_func_011 <= USED 0
__trace_generic_no_params_func_012 <= USED 0
__trace_generic_no_params_func_013 <= USED 0
__trace_generic_no_params_func_014 <= USED 0
__trace_generic_no_params_func_015 <= USED 0
__trace_generic_no_params_func_016 <= USED 0
__trace_generic_no_params_func_017 <= USED 0
__trace_generic_no_params_func_018 <= USED 0
__trace_generic_no_params_func_019 <= USED 0
__trace_generic_no_params_func_020 <= USED 0
__trace_generic_no_params_func_021 <= USED 0
__trace_generic_no_params_func_022 <= USED 0
__trace_generic_no_params_func_023 <= USED 0
__trace_generic_no_params_func_024 <= USED 0
__trace_generic_params_func_000 <= USED 0
__trace_generic_params_func_001 <= USED 0
__trace_generic_params_func_002 <= USED 0
__trace_generic_params_func_003 <= USED 0
__trace_generic_params_func_004 <= USED 0
__trace_generic_params_func_005 <= USED 0
__trace_generic_params_func_006 <= USED 0
__trace_generic_params_func_007 <= USED 0
__trace_generic_params_func_008 <= USED 0
__trace_generic_params_func_009 <= USED 0
__trace_generic_params_func_010 <= USED 0
__trace_generic_params_func_011 <= USED 0
__trace_generic_params_func_012 <= USED 0
__trace_generic_params_func_013 <= USED 0
__trace_generic_params_func_014 <= USED 0
__trace_generic_params_func_015 <= USED 0
__trace_generic_params_func_016 <= USED 0
__trace_generic_params_func_017 <= USED 0
__trace_generic_params_func_018 <= USED 0
__trace_generic_params_func_019 <= USED 0
__trace_generic_params_func_020 <= USED 0
__trace_generic_params_func_021 <= USED 0
__trace_generic_params_func_022 <= USED 0
__trace_generic_params_func_023 <= USED 0
__trace_generic_params_func_024 <= USED 0
__trace_generic_params_func_025 <= USED 0
__trace_generic_params_func_026 <= USED 0
__trace_generic_params_func_027 <= USED 0
__trace_generic_params_func_028 <= USED 0
__trace_generic_params_func_029 <= USED 0
__trace_generic_params_func_030 <= USED 0
__trace_generic_params_func_031 <= USED 0
__trace_generic_params_func_032 <= USED 0
__trace_generic_params_func_033 <= USED 0
__trace_generic_params_func_034 <= USED 0
__trace_generic_params_func_035 <= USED 0
__trace_generic_params_func_036 <= USED 0
__trace_generic_params_func_037 <= USED 0
__trace_generic_params_func_038 <= USED 0
__trace_generic_params_func_039 <= USED 0
__trace_generic_params_func_040 <= USED 0
__trace_generic_params_func_041 <= USED 0
__trace_generic_params_func_042 <= USED 0
__trace_generic_params_func_043 <= USED 0
__trace_generic_params_func_044 <= USED 0
__trace_generic_params_func_045 <= USED 0
__trace_generic_params_func_046 <= USED 0
__trace_generic_params_func_047 <= USED 0
__trace_generic_params_func_048 <= USED 0
__trace_generic_params_func_049 <= USED 0
__trace_generic_params_func_050 <= USED 0
__trace_generic_params_func_051 <= USED 0
__trace_generic_params_func_052 <= USED 0
__trace_generic_params_func_053 <= USED 0
__trace_generic_params_func_054 <= USED 0
__trace_generic_params_func_055 <= USED 0
;FILE diag_API.o
diagStructPrintf_Empty <= USED 0
isDiagTracePermitted <= USED 0
setDiagTraceProhibited <= USED 0
;FILE diag_Utils.o
ActivateCommandInputInParts <= USED 0
DiagAPI_GetTimeStamp <= USED 0
diagGetInMsgBodyLimit <= USED 0
getIfDataStat <= USED 0
;FILE diag_buff.o
LostMSGdata <= USED 0
SetDiagTxMsgCounterPtr <= USED 0
StatsGetCPQ <= USED 0
StatsGetParams <= USED 0
StatsSetCPBytes <= USED 0
StatsTestCPonly <= USED 0
updateStatsOnApps <= USED 0
;FILE diag_comm.o
Diag_Switch <= USED 0
IsDeviceConnctedInternally <= USED 0
TestDiagComm <= USED 0
;FILE diag_comm_EXTif.o
CopyDiagDirectToRingbuf <= USED 0
DiagCommExtIfTxEventQCallback <= USED 0
UART_diag_toAT <= USED 0
clearMinBuffToSendExtIf <= USED 0
diagCommTransmitToRingBuf <= USED 0
diagExtIFStatusDiscNotify <= USED 0
diagExtIFstatusConnectNotify <= USED 0
setMinBuffToSendExtIf <= USED 0
;FILE diag_comm_EXTif_OSA_NUCLEUS.o
ActivateDiagThroughPutHisr <= USED 0
diagCommTransmitToUart1 <= USED 0
diagThrougPutHISR <= USED 0
diag_os_TransmitToExtIf <= USED 0
diag_os_TransmitToExtIf1 <= USED 0
setExtUsbWayLock <= USED 0
uartRxLISRCB <= USED 0
uartTxLISRCB <= USED 0
;FILE diag_comm_INTif.o
;FILE diag_comm_L2.o
getDiagSppRxNumberOfBadMessages <= USED 0
;FILE diag_comm_L4.o
diagCommL4Init <= USED 0
;FILE diag_comm_if.o
diagCommSetChunk <= USED 0
;FILE diag_header_handler.o
FindSoTOffset <= USED 0
GetMessageCoreIDForIntIf <= USED 0
GetMsgIDFromTxHeader <= USED 0
GetRxMsgSourceID <= USED 0
GetUsbMsgLen <= USED 0
IsC2CStats <= USED 0
;FILE diag_init.o
SetTraceBeFiltered <= USED 0
SetTraceNotFiltered <= USED 0
diagEnterBootLoaderCBFuncBind <= USED 0
diagICATReadyNotifyEventBind <= USED 0
diagPhase1Init <= USED 0
diagStubPsCheckOKfn <= USED 0
isTraceFiltered <= USED 0
;FILE diag_mem.o
DiagMemFreeInternalItem <= USED 0
DiagMemPoolAlignFree <= USED 0
DiagMemPoolAlignMalloc <= USED 0
rtdm_memory_pool_delete <= USED 0
;FILE diag_nvm.o
diagCfgFilter <= USED 0
diagCfgSDLAutoDelete <= USED 0
diagCfgUseHighUart <= USED 0
diagStartPsCfg <= USED 0
eeh_dump_output_dev_is_spi <= USED 0
eeh_dump_output_dev_set_spi <= USED 0
eeh_dump_output_dev_set_usb_sd <= USED 0
getDiagUartSpeed <= USED 0
get_diag_dbversion <= USED 0
set_diag_dev_sd <= USED 0
set_diag_dev_usb <= USED 0
set_sulog_dev_sd <= USED 0
set_sulog_dev_usb <= USED 0
sulog_output_dev_set_sd <= USED 0
sulog_output_dev_set_usb <= USED 0
;FILE diag_osif.o
diagBSPconfig <= USED 0
diagOSspecificPhase1Init <= USED 0
diagOSspecificPhase2Init <= USED 0
;FILE diag_port.o
DiagDisable <= USED 0
DiagEnable <= USED 0
;FILE diag_restore_fixups.o
;FILE diag_rx.o
IsExternalConnectCeased <= USED 0
ResumeDiagMsgs <= USED 0
UpdateExtIfConnectionStatus <= USED 0
diagGetLibsVersion <= USED 0
diagPreDefCMMInit <= USED 0
;FILE diag_rx_OSA_NUCLEUS.o
setDiagCmdServerStatus <= USED 0
;FILE diag_tx.o
SetDiagTxMsgFramNumberPtr <= USED 0
;FILE dial.o
at_cmux_resp_cb <= USED 0
at_over_uart_setcb <= USED 0
at_uart_resp_cb <= USED 0
;FILE dialer_task.o
Is_2Gmode <= USED 0
PSServiceOption <= USED 0
SetImsState <= USED 0
SetRadioPowerState <= USED 0
dialer_Queue_init <= USED 0
dialer_get_compare_auth_flag <= USED 0
dialer_get_deact_default_pdp_flag <= USED 0
dialer_get_deactive_pdp_context_flag <= USED 0
dialer_get_dns_present_flag <= USED 0
dialer_set_compare_auth_flag <= USED 0
dialer_set_deact_default_pdp_flag <= USED 0
dialer_set_deactive_pdp_context_flag <= USED 0
dialer_set_dns_present_flag <= USED 0
dialer_task_init <= USED 0
getCurrentConnectNum <= USED 0
get_auto_apn_match_info <= USED 0
get_auto_apn_name <= USED 0
set_cgdf_save_mode <= USED 0
set_dialer_connmode <= USED 0
set_force_apn_reattach <= USED 0
;FILE digest.o
digest_authenticate <= USED 0
;FILE div_32.o
;FILE division_operations.o
WebRtcSpl_DivU32U16 <= USED 0
WebRtcSpl_DivW32W16ResW16 <= USED 0
;FILE dlmwdcfg.o
;FILE dmnvmfile.o
;FILE dmnvminit.o
;FILE dmnvmlcl.o
dmNvMTest <= USED 0
sendDmNvPReadCalReq <= USED 0
;FILE dmnvmtask.o
DmNvMTask1 <= USED 0
;FILE dmnvptask.o
DmNvPHandleReadCalCnf <= USED 0
DmNvPNvramSelectCal <= USED 0
DmNvPTask1 <= USED 0
;FILE dmpmdown.o
;FILE dmpmup.o
DmPmStartUpFromCharging <= USED 0
;FILE dns.o
dns_clear_setflg <= USED 0
dns_get_DNSserv <= USED 0
dns_get_seqno <= USED 0
dns_get_ttl <= USED 0
dns_set_ttl <= USED 0
;FILE dns_relay.o
dnsr_check_serverip <= USED 0
dnsr_get_ip4_server1 <= USED 0
dnsr_get_ip4_server2 <= USED 0
set_mifi_dns_name <= USED 0
webui_set_dns_server <= USED 0
;FILE download.o
fota_dump_buffer <= USED 0
fota_start <= USED 0
get_fota_result <= USED 0
init_fota <= USED 0
mini_fota_image_verify <= USED 0
mini_fota_pkg_write <= USED 0
test_mini_fota_api <= USED 0
xf_fota_1_bin_auto_start <= USED 0
xf_fota_auto_start <= USED 0
;FILE drat_coma_bsp_adap.o
dratComGetL1Version <= USED 0
;FILE drat_coma_power_adap.o
;FILE dsp_filters.o
;FILE dtc_api.o
dtcCh0Hisr <= USED 0
dtcCh0Lisr <= USED 0
dtcCh1Hisr <= USED 0
dtcCh1Lisr <= USED 0
dtcCountErrorHisr <= USED 0
dtcD2Prepare <= USED 0
dtcD2Recover <= USED 0
dtcErrorLisr <= USED 0
dtcHisrHandler <= USED 0
dtcPostF8TransferReq <= USED 0
dtcPreF8TransferReq <= USED 0
dtcSetDtcResetAfterRequest <= USED 0
dtcTransferReq <= USED 0
;FILE dtc_spy.o
dtcSpyModifyReportAndWriteTestFunction <= USED 0
plStartDtcDebug <= USED 0
plStopDtcDebug <= USED 0
;FILE dtx_dec.o
;FILE dtx_enc.o
;FILE dump.o
_Z11dump_vectorRKN7android6VectorIjEEPKc <= USED 0
_Z23dump_frame_info_verbosebRK9MediaInfoPKcS3_ <= USED 0
;FILE duster_common.o
ConvertASCtoString <= USED 0
ConvertStrToChar <= USED 0
ConvertStrToInteger64 <= USED 0
ConvertV6IntegertoString <= USED 0
IsWeakSignal <= USED 0
SetUsbMode <= USED 0
SimRemove_cb <= USED 0
clear_pdp_list <= USED 0
close_logical_channel <= USED 0
dualSimImsStateInit <= USED 0
duster_auto_select_netwrok <= USED 0
enableAutoTimeZone <= USED 0
get_china_sim_operator <= USED 0
get_iccid <= USED 0
get_imei <= USED 0
get_imsi <= USED 0
get_meid <= USED 0
get_qci_status <= USED 0
get_rx_byte <= USED 0
get_rx_byte_all <= USED 0
get_sim_iccid <= USED 0
get_sys_mode <= USED 0
get_tx_byte <= USED 0
get_tx_byte_all <= USED 0
htonl <= USED 0
htons <= USED 0
initAutoApn <= USED 0
initConnectMode <= USED 0
isAutoTimeZone <= USED 0
is_mcc_mnc_rau <= USED 0
is_pdp_connected <= USED 0
is_uicc_sim <= USED 0
lpa_init <= USED 0
open_logical_channel <= USED 0
sendImsDeregisterMsg <= USED 0
sendImsRegisterMsg <= USED 0
sendSimRemoveSimResetMsgToCm <= USED 0
sendUartDataToBindChannel <= USED 0
setBindSimId <= USED 0
setBindSimIdCheck <= USED 0
set_ntp_time_type <= USED 0
set_qci_status <= USED 0
set_stk_process_entity <= USED 0
sim_check_wrong_msg <= USED 0
wan_get_apn_iptype_change_type <= USED 0
;FILE duster_dongle.o
convertEventIdToString <= USED 0
discard_event_msg <= USED 0
duster_Queue_init <= USED 0
duster_module_init <= USED 0
set_default_dhcp <= USED 0
sim_plugout_event_cb <= USED 0
;FILE eas_config.o
EAS_CMEnumOptData <= USED 0
EAS_CMEnumOptModules <= USED 0
;FILE eas_dlssynth.o
;FILE eas_flog.o
;FILE eas_hostmm.o
EAS_HWBackLight <= USED 0
EAS_HWFileLength <= USED 0
EAS_HWLED <= USED 0
EAS_HWVibrate <= USED 0
;FILE eas_math.o
;FILE eas_mdls.o
;FILE eas_midi.o
;FILE eas_mixer.o
;FILE eas_pcm.o
EAS_PEClose <= USED 0
EAS_PEContinueStream <= USED 0
EAS_PEGetFileHandle <= USED 0
EAS_PELocate <= USED 0
EAS_PEOpenStream <= USED 0
EAS_PEPause <= USED 0
EAS_PERelease <= USED 0
EAS_PEReset <= USED 0
EAS_PEResume <= USED 0
EAS_PESeek <= USED 0
EAS_PEState <= USED 0
EAS_PEUpdateParams <= USED 0
EAS_PEUpdatePitch <= USED 0
EAS_PEUpdateVolume <= USED 0
;FILE eas_public.o
EAS_CloseMIDIStream <= USED 0
EAS_GetFileType <= USED 0
EAS_GetNoteCount <= USED 0
EAS_GetParameter <= USED 0
EAS_GetPolyphony <= USED 0
EAS_GetPriority <= USED 0
EAS_GetRenderTime <= USED 0
EAS_GetRepeat <= USED 0
EAS_GetSynthPolyphony <= USED 0
EAS_GetVolume <= USED 0
EAS_IntGetStrmParam <= USED 0
EAS_LoadDLSCollection <= USED 0
EAS_OpenJETStream <= USED 0
EAS_OpenMIDIStream <= USED 0
EAS_RegisterMetaDataCallback <= USED 0
EAS_SetHeaderSearchFlag <= USED 0
EAS_SetMaxLoad <= USED 0
EAS_SetMaxPCMStreams <= USED 0
EAS_SetParameter <= USED 0
EAS_SetPlayMode <= USED 0
EAS_SetPlaybackRate <= USED 0
EAS_SetPolyphony <= USED 0
EAS_SetPriority <= USED 0
EAS_SetRepeat <= USED 0
EAS_SetSoundLibrary <= USED 0
EAS_SetSynthPolyphony <= USED 0
EAS_WriteMIDIStream <= USED 0
;FILE eas_smf.o
;FILE eas_voicemgt.o
VMGetNoteCount <= USED 0
VMGetPolyphony <= USED 0
VMGetPriority <= USED 0
VMGetSynthPolyphony <= USED 0
VMGetTranposition <= USED 0
VMIncRefCount <= USED 0
VMSetGlobalDLSLib <= USED 0
VMSetGlobalEASLib <= USED 0
VMSetPitchBendRange <= USED 0
VMSetSynthPolyphony <= USED 0
VMSetWorkload <= USED 0
;FILE eas_wtengine.o
;FILE eas_wtsynth.o
;FILE ebndcfg.o
EbndCombinationFddAndTddBands <= USED 0
EbndDoEutraBandsDlOverlap <= USED 0
EbndGetOffsetInBandForEarfcn <= USED 0
EbndIsEarfcnInStoredList <= USED 0
;FILE ec_gains.o
;FILE ecdh.o
mbedtls_ecdh_get_params <= USED 0
;FILE ecdsa.o
mbedtls_ecdsa_genkey <= USED 0
mbedtls_ecdsa_sign <= USED 0
mbedtls_ecdsa_verify <= USED 0
;FILE ecp.o
mbedtls_ecp_curve_info_from_name <= USED 0
mbedtls_ecp_curve_list <= USED 0
mbedtls_ecp_gen_key <= USED 0
mbedtls_ecp_gen_keypair <= USED 0
mbedtls_ecp_gen_keypair_base <= USED 0
mbedtls_ecp_muladd <= USED 0
mbedtls_ecp_point_cmp <= USED 0
mbedtls_ecp_point_read_string <= USED 0
mbedtls_ecp_read_key <= USED 0
mbedtls_ecp_tls_read_group <= USED 0
;FILE ecp_curves.o
;FILE emaccf.o
;FILE emacdl.o
;FILE emacra.o
;FILE emacsig.o
KiLteL2CreateFreqIntSignal <= USED 0
KiLteL2SendFreqIntSignal <= USED 0
KiLteL2SendFreqSignal <= USED 0
;FILE emacul.o
LtePdcpSetLochData <= USED 0
;FILE emacut.o
DebugL2ImsInfo <= USED 0
GetAppsResvHeadroom <= USED 0
LteGetL2ImsInfo <= USED 0
LteMacGetL2Stat <= USED 0
LteMacInitialize2 <= USED 0
LteMacSendEcphyFlashDspReq <= USED 0
LteMacutCADisable <= USED 0
LteMacutCAEnable <= USED 0
LteMacutCheckEmacTask <= USED 0
LteMacutDisableL2Exception <= USED 0
LteMacutDisableMacInternalSig <= USED 0
LteMacutDisableUplaneDiag <= USED 0
LteMacutDumpUlDataFromIPC <= USED 0
LteMacutEnableIPTraceOnly <= USED 0
LteMacutEnableMacInternalSig <= USED 0
LteMacutEnableUplaneDiag <= USED 0
LteMacutGsmVoiceCallInform <= USED 0
LteMacutHighSpeedDiagControlDisable <= USED 0
LteMacutHighSpeedDiagControlEnable <= USED 0
LteMacutSetBSRValueInGsmVoiceCall <= USED 0
LteMacutSetL2PendingLenToDefault <= USED 0
LteMacutSetL2PendingLenToMax <= USED 0
LteMacutSetWarningHandleLevel <= USED 0
LteMacutTerminateConnection <= USED 0
isBadQualityDetected <= USED 0
isTherePendingData <= USED 0
;FILE emm_errc.o
EmmEncodeDecode <= USED 0
MmDualGetDisacardErrPageInd <= USED 0
MmDualSetDisacardErrPageInd <= USED 0
;FILE emm_esm.o
;FILE emm_mmr.o
;FILE emm_psms.o
;FILE emm_rxmsg.o
;FILE emm_security.o
DerivateCkIkAtHandover <= USED 0
DerivateCkIkAtIdleMobility <= USED 0
DerivateGprsGsmKc <= USED 0
DerivateGsmKc <= USED 0
DerivateKasmeDuringHandover <= USED 0
DerivateNasToken <= USED 0
DerivateSrvccCkIkAtHandover <= USED 0
;FILE emm_sim.o
;FILE emm_timer.o
EmmImsServiceEndProtectionTimerExpiry <= USED 0
EmmStartT3423 <= USED 0
EmmStopAuthTimers <= USED 0
;FILE emm_txmsg.o
;FILE emm_utils.o
EmmCheckIfMmrNregIndNeeded <= USED 0
EmmIsCurrentCardInNormalService <= USED 0
EmmReduceServiceType <= USED 0
EmmSendMmsiStoreLocationInfoReq <= USED 0
EmmServiceRejectReceived <= USED 0
EmmSimDeleteCipherKeyData <= USED 0
MmIsMmrRegReqInQueue <= USED 0
;FILE enc.o
;FILE enc_lag3.o
;FILE enc_lag6.o
;FILE entropy.o
mbedtls_entropy_gather <= USED 0
mbedtls_entropy_update_manual <= USED 0
;FILE epdcp.o
LteBlockAlloc <= USED 0
LteBlockRelease <= USED 0
LteDeleteUlBlockStub <= USED 0
LtePdcpGetPendingUplinkData <= USED 0
LtePdcpGetPendingUplinkDataNum <= USED 0
LtePdcpInit2 <= USED 0
tLtePdcpTask <= USED 0
;FILE epdcpcipherandintegrity.o
DtcIsLteMode <= USED 0
LteDtcCh0InfoInit <= USED 0
LteHandlePdicHisrCh1 <= USED 0
LteHandlePdicHisrCh1AboveY0 <= USED 0
LteHisrConstructRlcDataListCh1 <= USED 0
LteHisrConstructRrcDataInd <= USED 0
LteHisrConstructRrcIntegrityCalcCnf <= USED 0
LteHisrConstructRrcIntegrityCheckErrorInd <= USED 0
LteHisrConstructSnDataInd <= USED 0
LteHisrConstructSnDataReq <= USED 0
LteHisrHandleSrbDataInd <= USED 0
LtePdcpDlTraceIpPkt <= USED 0
LtePdcpEia <= USED 0
LtePdcpReUpdateSrbDecipherInfo <= USED 0
LtePdcpReverseMacI <= USED 0
LtePdcpSendSrbCipherReq <= USED 0
LtePdcpSrbSoftEea <= USED 0
LtePdcpUpdataDeintegrityInfo <= USED 0
LtePdcpUpdateSrbDecipherInfo <= USED 0
LtePdicHandleLoopbackModeB <= USED 0
LtePdicSendLteLoopBackSnDataReq <= USED 0
LteSendEpdcpDlRohcDecipheredInd <= USED 0
SetDtcNwMode <= USED 0
;FILE epdcplinklist.o
LteTraceDataList <= USED 0
;FILE epdcprlcsap.o
LtePdcpAddSnDataIndLList <= USED 0
LtePdcpConstructIpDlDataIndNode <= USED 0
LtePdcpConstructIpDlDataIndNodeForLoop <= USED 0
LtePdcpConstructRrcIntegrityCheckErrorInd <= USED 0
LtePdcpConstructSnUlDataReqNode <= USED 0
LtePdcpFreeDlIpMem <= USED 0
LtePdcpLoopBackOnDKBTest <= USED 0
LtePdcpMaciCompare <= USED 0
LtePdcpRlcTriggerPdcpReportInd <= USED 0
LtePdcpTriggerStatisticReportInd <= USED 0
;FILE epdcprrcsap.o
LtePdcpAddPduHeaderForRrcData <= USED 0
;FILE epdcpupsap.o
LtePdcpAddPduHeaderForUserData <= USED 0
LtePdcpHandleUlPdicDoneInd <= USED 0
LtePdcpSetUlPdcpDataLengthToZero <= USED 0
LtePdcpSnDataReq <= USED 0
LtePdcpSubUlPdcpDataLength <= USED 0
LtePdcpUlDiscardSnSdu <= USED 0
enablePdcpDiag <= USED 0
;FILE err.o
lwip_strerr <= USED 0
;FILE esmab.o
SendAbgpSmApnReadRsp <= USED 0
;FILE esmdevfeature.o
HandleEsmActDedEPSBearerContextReq <= USED 0
HandleEsmSnsmActivateRsp <= USED 0
;FILE esmemm.o
;FILE esmmain.o
;FILE esmreg.o
SendSmregPdpModInd <= USED 0
;FILE esmsn.o
;FILE esmtimer.o
;FILE esmutil.o
EsmComparePdpAddresses <= USED 0
SmIsEplmn <= USED 0
;FILE etharp.o
etharp_add_static_entry <= USED 0
etharp_find_addr <= USED 0
etharp_remove_static_entry <= USED 0
ethernet_copy_data_all <= USED 0
ethernet_copy_data_ref <= USED 0
ip_mac_stat_bytes <= USED 0
lwip_dump_arp_table <= USED 0
lwip_ethaddr_ntoa <= USED 0
lwip_macaddr_ntoa <= USED 0
lwip_wan_delete_device <= USED 0
lwip_wan_delete_device_r <= USED 0
mac_header_validcheck <= USED 0
;FILE ethip6.o
;FILE ets_to_if2.o
;FILE ets_to_wmf.o
;FILE evs_payload.o
unpackEvsFrame <= USED 0
;FILE ex_ctrl.o
;FILE extract_h.o
;FILE extract_l.o
;FILE fatwk_psm.o
get_psm_data_addr <= USED 0
;FILE feedback_create.o
;FILE feedback_parse.o
;FILE fft.o
_ZN4CFFT5ScaleEP7complexj <= USED 0
_ZN4CFFT7ForwardEPK7complexPS0_j <= USED 0
_ZN4CFFT7InverseEP7complexjb <= USED 0
_ZN4CFFT7InverseEPK7complexPS0_jb <= USED 0
_ZN4CFFT9RearrangeEPK7complexPS0_j <= USED 0
;FILE find_min_max.o
FindMaxMin <= USED 0
;FILE firmware.o
;FILE flushnvm_shutdown.o
;FILE fs_api.o
FS_Abort <= USED 0
FS_ChangeSize <= USED 0
FS_Close_DIR <= USED 0
FS_Copy <= USED 0
FS_CopyOneFile <= USED 0
FS_Copyfiles <= USED 0
FS_Create <= USED 0
FS_DirCount <= USED 0
FS_DirCountEx <= USED 0
FS_FATFindFirstFile <= USED 0
FS_FATFindNextFile <= USED 0
FS_FATIsExist <= USED 0
FS_FATMakeDir <= USED 0
FS_FATRemoveDir <= USED 0
FS_FATRename <= USED 0
FS_FATTimeInfo2FileTime <= USED 0
FS_FDIFindFirstFile <= USED 0
FS_FDIFindNextFile <= USED 0
FS_FDIIsExist <= USED 0
FS_FDIMakeDir <= USED 0
FS_FDIRemoveDir <= USED 0
FS_FindClose <= USED 0
FS_FindFirstFile <= USED 0
FS_FindNextFile <= USED 0
FS_Flush <= USED 0
FS_Format <= USED 0
FS_GetCurrentDir <= USED 0
FS_GetDeviceInfo <= USED 0
FS_GetDirSize <= USED 0
FS_GetFSInfo <= USED 0
FS_GetFileInfo <= USED 0
FS_GetFileName <= USED 0
FS_GetFileSizeByName <= USED 0
FS_GetFullPath <= USED 0
FS_GetVersion <= USED 0
FS_Get_Abort <= USED 0
FS_Get_Fat_Findfd <= USED 0
FS_Get_FindData <= USED 0
FS_Get_Next_File <= USED 0
FS_HasFormatted <= USED 0
FS_IsDirFile <= USED 0
FS_IsEndOfFile <= USED 0
FS_IsExist <= USED 0
FS_IsValidFileName <= USED 0
FS_MakeDir <= USED 0
FS_Mount <= USED 0
FS_Move <= USED 0
FS_Open_DIR <= USED 0
FS_PowerOff <= USED 0
FS_PowerOn <= USED 0
FS_ReleaseDev <= USED 0
FS_RemoveDir <= USED 0
FS_Rename <= USED 0
FS_ScanDisk <= USED 0
FS_SetCurrentDir <= USED 0
FS_TFCardAvailable <= USED 0
FS_TFCardExist <= USED 0
FS_Tell <= USED 0
FS_Truncate <= USED 0
Fs_RestoreFactorySettings <= USED 0
Fs_sdcard_enter_c1_cb <= USED 0
Fs_sdcard_exit_c1_cb <= USED 0
GetDirSize <= USED 0
VDS_SyncFlush <= USED 0
fs_fclose <= USED 0
fs_feof <= USED 0
fs_fopen <= USED 0
fs_fread <= USED 0
fs_fseek <= USED 0
fs_ftell <= USED 0
fs_fwrite <= USED 0
resource_data_load <= USED 0
resource_get_filesize <= USED 0
rsld_check_filepointer <= USED 0
rsld_fclose <= USED 0
rsld_feof <= USED 0
rsld_fopen <= USED 0
rsld_fread <= USED 0
rsld_fseek <= USED 0
rsld_ftell <= USED 0
rsld_fwrite <= USED 0
rsld_get_fileid <= USED 0
rsld_register_file_ops <= USED 0
;FILE fs_base.o
FS_SystemTime2FileTime <= USED 0
fs_GetFileTypeGroup <= USED 0
fs_strncpy <= USED 0
is_file_media <= USED 0
is_file_multimedia <= USED 0
;FILE fsm_app.o
fsmAppGetCurrentEvent <= USED 0
fsmAppProcessEvent <= USED 0
fsmAppRelease <= USED 0
fsmAppSetTraceInfo <= USED 0
fsmGetVersion <= USED 0
;FILE fstdio_wrap.o
WRAP_fclose <= USED 0
WRAP_fopen <= USED 0
WRAP_fprintf <= USED 0
WRAP_fread <= USED 0
WRAP_fwrite <= USED 0
fstdio_bindDefault <= USED 0
;FILE ftp_client.o
api_ftp_query_config <= USED 0
api_ftp_reset_init <= USED 0
api_ftp_send_msgq <= USED 0
;FILE g_adapt.o
;FILE g_code.o
;FILE g_pitch.o
;FILE gain_q.o
;FILE gatt_client.o
gatt_client_cancel_write <= USED 0
gatt_client_discover_characteristics_for_handle_range_by_uuid128 <= USED 0
gatt_client_discover_characteristics_for_handle_range_by_uuid16 <= USED 0
gatt_client_discover_characteristics_for_service_by_uuid128 <= USED 0
gatt_client_discover_characteristics_for_service_by_uuid16 <= USED 0
gatt_client_discover_primary_services_by_uuid128 <= USED 0
gatt_client_discover_primary_services_by_uuid16 <= USED 0
gatt_client_discover_secondary_services <= USED 0
gatt_client_execute_write <= USED 0
gatt_client_find_included_services_for_service <= USED 0
gatt_client_get_mtu <= USED 0
gatt_client_is_ready <= USED 0
gatt_client_mtu_enable_auto_negotiation <= USED 0
gatt_client_prepare_write <= USED 0
gatt_client_read_characteristic_descriptor <= USED 0
gatt_client_read_characteristic_descriptor_using_descriptor_handle <= USED 0
gatt_client_read_long_characteristic_descriptor <= USED 0
gatt_client_read_long_characteristic_descriptor_using_descriptor_handle <= USED 0
gatt_client_read_long_characteristic_descriptor_using_descriptor_handle_with_offset <= USED 0
gatt_client_read_long_value_of_characteristic <= USED 0
gatt_client_read_multiple_characteristic_values <= USED 0
gatt_client_read_value_of_characteristic <= USED 0
gatt_client_read_value_of_characteristic_using_value_handle <= USED 0
gatt_client_read_value_of_characteristics_by_uuid128 <= USED 0
gatt_client_read_value_of_characteristics_by_uuid16 <= USED 0
gatt_client_reliable_write_long_value_of_characteristic <= USED 0
gatt_client_send_mtu_negotiation <= USED 0
gatt_client_set_required_security_level <= USED 0
gatt_client_write_characteristic_descriptor <= USED 0
gatt_client_write_characteristic_descriptor_using_descriptor_handle <= USED 0
gatt_client_write_long_characteristic_descriptor <= USED 0
gatt_client_write_long_characteristic_descriptor_using_descriptor_handle <= USED 0
gatt_client_write_long_characteristic_descriptor_using_descriptor_handle_with_offset <= USED 0
gatt_client_write_long_value_of_characteristic <= USED 0
gatt_client_write_long_value_of_characteristic_with_offset <= USED 0
;FILE gatt_client_asr.o
;FILE gba.o
;FILE gc_pred.o
;FILE gcm.o
;FILE get_pred_adv_b_add.o
;FILE get_pred_outside.o
;FILE glonass.o
;FILE gmed_n.o
;FILE gnss.o
;FILE gnss_2_mcu_ipc.o
;FILE goep_client.o
goep_client_body_add_static <= USED 0
goep_client_get_request_opcode <= USED 0
goep_client_header_add_byte <= USED 0
goep_client_header_add_variable <= USED 0
goep_client_header_add_word <= USED 0
goep_client_request_create_put <= USED 0
goep_client_request_get_max_body_size <= USED 0
;FILE goep_client_extern.o
goep_client_get_request_opcode_extern <= USED 0
goep_client_header_add_byte_extern <= USED 0
goep_client_header_add_challenge_response_extern <= USED 0
goep_client_header_add_srm_enable_extern <= USED 0
goep_client_header_add_variable_extern <= USED 0
goep_client_header_add_word_extern <= USED 0
goep_client_version_20_or_higher_extern <= USED 0
;FILE goep_server_extern.o
goep_server_decline_connection_extern <= USED 0
;FILE gpenc_ie.o
GpEncR97MsRadAccCapb <= USED 0
GpEncR99MsRadAccCapb <= USED 0
GpLenFirstMrac <= USED 0
;FILE gpio_api.o
drv_gipo_handler <= USED 0
gpio_direction_output <= USED 0
gpio_disable_edge_detection <= USED 0
gpio_enable_edge_detection <= USED 0
gpio_get_directions <= USED 0
gpio_hal_config <= USED 0
gpio_irq_config <= USED 0
gpio_set_value <= USED 0
;FILE gpio_cus.o
BtLdoEnonoff <= USED 0
CameraFlashPowerOnOff <= USED 0
CameraPwdn0OnOff <= USED 0
CameraPwdn1OnOff <= USED 0
CameraPwdn2OnOff <= USED 0
CameraRstOnOff <= USED 0
ChargerEnableGpioonoff <= USED 0
ChargerEnonoff <= USED 0
ChargerStatusPinCfg <= USED 0
EnableSpeakerPA <= USED 0
Evb1601_Pm802Backlightonoff <= USED 0
Evb1601_Pm802ChargerEnonoff <= USED 0
ForceChargerFinished <= USED 0
GpioChargerInit <= USED 0
GpioCusConfig <= USED 0
KeypadBlackLightSwitchOff <= USED 0
KeypadBlackLightSwitchOn <= USED 0
KeypadLedonoff <= USED 0
LcdBacklightSwitchOff <= USED 0
LcdBacklightSwitchOn <= USED 0
NFC_DAT_IRQ_READ <= USED 0
SpeakerExtPaEnable <= USED 0
SpeakerExtPaInit <= USED 0
TorchlightSwitch <= USED 0
TorchlightSwitchOff <= USED 0
TorchlightSwitchOn <= USED 0
VibratorSetLevel <= USED 0
get_keypadbacklight_status <= USED 0
get_lcdbacklight_status <= USED 0
get_torchlight_status <= USED 0
xf_nfc_cp_pin_cfg <= USED 0
xf_nfc_cp_ven_pin <= USED 0
xf_nfc_dwl_pin <= USED 0
xf_nfc_get_dwl_pin_num <= USED 0
xf_nfc_get_irq_pin <= USED 0
xf_nfc_get_ven_pin_num <= USED 0
xf_nfc_gui_pin_cfg <= USED 0
xf_nfc_hw_iic_pin_cfg <= USED 0
xf_nfc_irq_enable <= USED 0
xf_nfc_sw_iic_pin_cfg <= USED 0
;FILE gplc_stub_ds3.o
ConfigRfContMux <= USED 0
DlBbcGetBbcMirrorState <= USED 0
DlBbcGetBbcWriteHistory <= USED 0
DlScInformMsaAboutD2 <= USED 0
DlScRecoveryFromD2 <= USED 0
GSMInit <= USED 0
Get1920FFFlag <= USED 0
Get_Dcxo_Add_Config <= USED 0
GplcSpyGetBufferSize <= USED 0
HWWakeupInterruptHandler <= USED 0
L1BgGsmIsInOOS <= USED 0
L1FrSendGmphPktOpCtrlInd <= USED 0
L1GetFrameNumber <= USED 0
L1GetState <= USED 0
L1SqNbCanTurnSpeechOn <= USED 0
RfBindInit <= USED 0
dlEnableAudioInts <= USED 0
l1MnAllowSleep <= USED 0
plgBindGl1CipherDataCnf <= USED 0
plgBindGmphDlSbConfigCnf <= USED 0
plgBindGmphDlTbfConfigCnf <= USED 0
plgBindGmphDynTsReconfigCnf <= USED 0
plgBindGmphExtMeasCnf <= USED 0
plgBindGmphFxdTsReconfigCnf <= USED 0
plgBindGmphIntMeasInd <= USED 0
plgBindGmphPccchConfigCnf <= USED 0
plgBindGmphPdchReleaseCnf <= USED 0
plgBindGmphPrachCnf <= USED 0
plgBindGmphPtmEnterInd <= USED 0
plgBindGmphPtmExitInd <= USED 0
plgBindGmphPtmMeasInd <= USED 0
plgBindGmphUlDynTbfConfigCnf <= USED 0
plgBindGmphUlFxdAllocEndInd <= USED 0
plgBindGmphUlFxdTbfConfigCnf <= USED 0
plgBindGmphUlSbConfigCnf <= USED 0
plgBindGmphUnitDataInd <= USED 0
plgBindMacControlAckReq <= USED 0
plgBindMacDlConfigReq <= USED 0
plgBindMacDlFlushReq <= USED 0
plgBindMacDlStartInd <= USED 0
plgBindMacDlTimeslotAllocInd <= USED 0
plgBindMacL1Sequencer <= USED 0
plgBindMacRemoveUlSblockReq <= USED 0
plgBindMacRxDecodeErrorInd <= USED 0
plgBindMacRxRadioBlockInd <= USED 0
plgBindMacTxBlockInfoReq <= USED 0
plgBindMacTxBlockSentInd <= USED 0
plgBindMacTxDataBlocksPurgedInd <= USED 0
plgBindMacTxPayloadReq <= USED 0
plgBindMacUlConfigReq <= USED 0
plgBindMacUlFlushReq <= USED 0
plgBindMacUlSblockReq <= USED 0
plgBindMacUlStartInd <= USED 0
plgBindMacUlTimeslotAllocInd <= USED 0
plgBindMphBcchMeasInd <= USED 0
plgBindMphBchConfigCnf <= USED 0
plgBindMphBsicDecodeCnf <= USED 0
plgBindMphBsicDecodeInd <= USED 0
plgBindMphChanAssignmentCnf <= USED 0
plgBindMphChanAssignmentFailCnf <= USED 0
plgBindMphChannelModeCnf <= USED 0
plgBindMphCipherModeCnf <= USED 0
plgBindMphDeactivateCnf <= USED 0
plgBindMphDedicatedMeasInd <= USED 0
plgBindMphDownlinkSignalFailInd <= USED 0
plgBindMphErrorInd <= USED 0
plgBindMphExtMeasurementCnf <= USED 0
plgBindMphFrequencyChangeCnf <= USED 0
plgBindMphHandoverCnf <= USED 0
plgBindMphHandoverFailCnf <= USED 0
plgBindMphHandoverStartInd <= USED 0
plgBindMphIdleNcellMeasInd <= USED 0
plgBindMphIdleScellMeasInd <= USED 0
plgBindMphImmAssignmentCnf <= USED 0
plgBindMphMonitorPlmnCnf <= USED 0
plgBindMphMonitorPlmnInd <= USED 0
plgBindMphRadioLinkTimeoutInd <= USED 0
plgBindMphRandomAccessCnf <= USED 0
plgBindMphUnitDataInd <= USED 0
plgBindPhConnectInd <= USED 0
plgBindPhDataInd <= USED 0
plgBindPhReadyToSendInd <= USED 0
plgBindRtl1DownlinkDataInd <= USED 0
plgCalDevGsmBurstReq <= USED 0
plgCalDevGsmGainProgramReq <= USED 0
plgCalDevGsmRampScaleReq <= USED 0
plgCalDevGsmReq <= USED 0
plgCalDevGsmRssiReq <= USED 0
plgCalDevGsmSetAfcDacReq <= USED 0
plgCalDevGsmSetBandModeReq <= USED 0
plgCalDevRssiDbmResult <= USED 0
plgGl1CipherDataReq <= USED 0
plgGmphDlSbConfigReq <= USED 0
plgGmphDlTbfConfigReq <= USED 0
plgGmphDlTbfShutdownReq <= USED 0
plgGmphDynTsReconfigReq <= USED 0
plgGmphExtMeasReq <= USED 0
plgGmphFxdTsReconfigReq <= USED 0
plgGmphIntMeasReq <= USED 0
plgGmphNetCtrlMeasReq <= USED 0
plgGmphPbcchDecodeReq <= USED 0
plgGmphPccchConfigReq <= USED 0
plgGmphPdchReleaseReq <= USED 0
plgGmphPrachFormatReq <= USED 0
plgGmphPrachReq <= USED 0
plgGmphPwrCtrlConfigReq <= USED 0
plgGmphStopPrachReq <= USED 0
plgGmphTimingPowerReq <= USED 0
plgGmphUlDynTbfConfigReq <= USED 0
plgGmphUlFxdTbfConfigReq <= USED 0
plgGmphUlSbAbortReq <= USED 0
plgGmphUlSbConfigReq <= USED 0
plgGmphUlTbfShutdownReq <= USED 0
plgMphAbortNcellOpReq <= USED 0
plgMphBcchDecodeReq <= USED 0
plgMphBchConfigReq <= USED 0
plgMphBsicDecodeReq <= USED 0
plgMphCbchControlReq <= USED 0
plgMphChanAssignmentFailReq <= USED 0
plgMphChanAssignmentReq <= USED 0
plgMphChannelModeReq <= USED 0
plgMphCipherModeReq <= USED 0
plgMphClassmarkReq <= USED 0
plgMphDeactivateReq <= USED 0
plgMphExtMeasurementReq <= USED 0
plgMphFindBcchReq <= USED 0
plgMphFrequencyChangeReq <= USED 0
plgMphHandoverFailReq <= USED 0
plgMphHandoverReq <= USED 0
plgMphImmAssignmentReq <= USED 0
plgMphMeasureAllReq <= USED 0
plgMphMonitorPlmnReq <= USED 0
plgMphNcellMeasReq <= USED 0
plgMphNextBcchReq <= USED 0
plgMphPageModeReq <= USED 0
plgMphRadioLinkTimeoutReq <= USED 0
plgMphRandomAccessReq <= USED 0
plgMphServingCellBcchReq <= USED 0
plgMphStopRachReq <= USED 0
plgMphTimingAdvReq <= USED 0
plgPhDataReq <= USED 0
plgPhEmptyFrameInd <= USED 0
plgRtl1UplinkDataReq <= USED 0
;FILE gprsal.o
GPRSAL_GetCASIMS <= USED 0
GPRSAL_GetCAVIMS <= USED 0
GPRSAL_IsServiceRegistered <= USED 0
GPRSAL_Release <= USED 0
GRPSAL_GETBANDIND <= USED 0
;FILE gps_api.o
GPS_getPostionInfo <= USED 0
GnssLocationProvider_cleanup <= USED 0
GnssLocationProvider_inject_location <= USED 0
GnssLocationProvider_stop <= USED 0
XfAsrGpsOpen <= USED 0
asr_gnss_check_agpsdata <= USED 0
asr_gnss_close <= USED 0
asr_gnss_download_agpsdata <= USED 0
asr_gnss_send_agpsdata <= USED 0
xf_gps_sleep_handle <= USED 0
;FILE gps_asr.o
GetGpsData <= USED 0
asr_aboot_dl_thread_delete <= USED 0
asr_gps_clear_fatalinfo <= USED 0
asr_gps_clear_packinfo <= USED 0
asr_gps_send_initc <= USED 0
asr_gps_send_mcc <= USED 0
asr_gps_set_pvtupdateflag <= USED 0
asr_gps_set_swRest_flag <= USED 0
asr_gps_set_updateflag <= USED 0
asr_gps_set_workmode2 <= USED 0
asr_gps_update_agpsdata_by_app <= USED 0
asr_gps_update_fatalinfo <= USED 0
asr_supl_update <= USED 0
get_gpsuart_dma_last_cnt <= USED 0
;FILE gps_atcmd.o
asr_gnss_get_mcc <= USED 0
gps_atcmd_thread <= USED 0
;FILE gps_firmware.o
dump_uart_register <= USED 0
;FILE gps_ipc.o
get_ringbuffer_len_by_idx <= USED 0
gps_module_clear_ipc_interrupt <= USED 0
;FILE gps_pvt.o
asr_gps_clear_pvtimage <= USED 0
asr_gps_save_pvt <= USED 0
asr_gps_update_pvtimage <= USED 0
;FILE guilin_lite.o
GuilinLiteDisableWDT <= USED 0
GuilinLite_Aditional_Workaround <= USED 0
GuilinLite_Dump_PMIC_Register <= USED 0
GuilinLite_INT_CALLBACK_REGISTER <= USED 0
GuilinLite_LDO_Set_Slpmode <= USED 0
GuilinLite_Ldo_11_set <= USED 0
GuilinLite_Ldo_11_set_1_2 <= USED 0
GuilinLite_Ldo_6_set_1_8 <= USED 0
GuilinLite_Ldo_7_set_1_2 <= USED 0
GuilinLite_Ldo_7_set_2_8 <= USED 0
GuilinLite_Ldo_7_set_3_0 <= USED 0
GuilinLite_Ldo_7_set_3_3 <= USED 0
GuilinLite_Ldo_8_set_1_9 <= USED 0
GuilinLite_Ldo_8_set_3_3 <= USED 0
GuilinLite_PowerSave_Config <= USED 0
GuilinLite_VBUCK1_Set_FPWM <= USED 0
GuilinLite_VBUCK1_Set_Skip_Mode <= USED 0
GuilinLite_VBUCK_Set_DVC_Enable <= USED 0
GuilinLite_VBUCK_Set_Enable <= USED 0
GuilinLite_VBUCK_Set_Slpmode <= USED 0
PM803_Buck1_Config0V90 <= USED 0
PM803_Buck1_Config0V95 <= USED 0
PM803_Buck1_Config1V00 <= USED 0
PM803_Buck1_Config1V05 <= USED 0
PM803_Buck1_Config1V10 <= USED 0
PM803_Buck1_Config1V15 <= USED 0
PM803_Buck1_Config1V20 <= USED 0
PM803_LcdBacklight_Level_Get <= USED 0
PM803_LcdBacklight_Status_Get <= USED 0
guilin_lite_enable_ldo5x <= USED 0
;FILE hacomtsk.o
HaCommsTask1 <= USED 0
;FILE hadsimhd.o
hadGetCardNum <= USED 0
hadSetUiccTransmissionProtocolT0Only <= USED 0
;FILE hadsimmn.o
L1SimDriverTask1 <= USED 0
;FILE hafailcf.o
di_LockDisplay <= USED 0
eeExtSuspendPsTasks <= USED 0
;FILE hal_timer.o
;FILE hci.o
gap_auto_connection_start <= USED 0
gap_auto_connection_stop <= USED 0
gap_auto_connection_stop_all <= USED 0
gap_bonded <= USED 0
gap_connect_with_whitelist <= USED 0
gap_enable_link_watchdog <= USED 0
gap_extended_advertising_remove <= USED 0
gap_get_bondable_mode <= USED 0
gap_get_link_key_for_bd_addr <= USED 0
gap_inquiry_periodic_start <= USED 0
gap_inquiry_set_lap <= USED 0
gap_le_connection_interval <= USED 0
gap_le_set_phy <= USED 0
gap_periodic_advertiser_list_add <= USED 0
gap_periodic_advertiser_list_clear <= USED 0
gap_periodic_advertiser_list_remove <= USED 0
gap_periodic_advertising_create_sync <= USED 0
gap_periodic_advertising_create_sync_cancel <= USED 0
gap_periodic_advertising_terminate_sync <= USED 0
gap_qos_set <= USED 0
gap_register_classic_connection_filter <= USED 0
gap_request_role <= USED 0
gap_set_allow_role_switch <= USED 0
gap_set_bondable_mode <= USED 0
gap_set_connection_parameter_range <= USED 0
gap_set_connection_parameters <= USED 0
gap_set_link_supervision_timeout <= USED 0
gap_set_max_number_peripheral_connections <= USED 0
gap_set_required_encryption_key_size <= USED 0
gap_set_scan_duplicate_filter <= USED 0
gap_set_secure_connections_only_mode <= USED 0
gap_set_security_level <= USED 0
gap_set_security_mode <= USED 0
gap_sniff_subrating_configure <= USED 0
gap_update_connection_parameters <= USED 0
gap_whitelist_remove <= USED 0
hci_deinit <= USED 0
hci_disconnect_all <= USED 0
hci_get_allow_role_switch <= USED 0
hci_get_manufacturer <= USED 0
hci_remove_event_handler <= USED 0
hci_request_sco_can_send_now_event <= USED 0
hci_set_control <= USED 0
hci_set_sco_voice_setting <= USED 0
;FILE hci_cmd.o
;FILE hci_dump.o
hci_dump_enable_log_level <= USED 0
hci_dump_set_max_packets <= USED 0
hci_dump_setup_header_bluez <= USED 0
hci_dump_setup_header_btsnoop <= USED 0
hci_dump_setup_header_packetlogger <= USED 0
;FILE hci_dump_asr.o
;FILE hci_transport_h4_asr.o
;FILE hci_transport_h5.o
hci_transport_h5_enable_bcsp_mode <= USED 0
hci_transport_h5_set_auto_sleep <= USED 0
;FILE hexdump.o
_ZN14streamingmedia7hexdumpEPKvjjPNS_7AStringE <= USED 0
;FILE hfp.o
get_hfp_connection_context_for_acl_handle <= USED 0
hfp_get_sco_packet_types <= USED 0
hfp_register_custom_ag_command <= USED 0
hfp_set_sco_packet_types <= USED 0
;FILE hfp_ag.o
hfp_ag_activate_voice_recognition <= USED 0
hfp_ag_clear_last_dialed_number <= USED 0
hfp_ag_deactivate_voice_recognition <= USED 0
hfp_ag_enhanced_voice_recognition_report_processing_input <= USED 0
hfp_ag_enhanced_voice_recognition_report_ready_for_audio <= USED 0
hfp_ag_enhanced_voice_recognition_report_sending_audio <= USED 0
hfp_ag_enhanced_voice_recognition_send_message <= USED 0
hfp_ag_notify_incoming_call_waiting <= USED 0
hfp_ag_register_custom_at_command <= USED 0
hfp_ag_register_custom_call_sm_handler <= USED 0
hfp_ag_reject_phone_number_for_voice_tag <= USED 0
hfp_ag_report_extended_audio_gateway_error_result_code <= USED 0
hfp_ag_send_command_result_code <= USED 0
hfp_ag_send_dtmf_code_done <= USED 0
hfp_ag_send_phone_number_for_voice_tag <= USED 0
hfp_ag_send_unsolicited_result_code <= USED 0
hfp_ag_set_battery_level <= USED 0
hfp_ag_set_last_dialed_number <= USED 0
hfp_ag_set_registration_status <= USED 0
hfp_ag_set_roaming_status <= USED 0
hfp_ag_set_signal_strength <= USED 0
hfp_ag_set_subcriber_number_information <= USED 0
hfp_ag_set_use_in_band_ring_tone <= USED 0
;FILE hfp_gsm_model.o
hfp_gsm_call_possible <= USED 0
hfp_gsm_clear_last_dialed_number <= USED 0
hfp_gsm_last_dialed_number <= USED 0
hfp_gsm_set_last_dialed_number <= USED 0
;FILE hfp_hf.o
hfp_hf_activate_call_waiting_notification <= USED 0
hfp_hf_activate_calling_line_notification <= USED 0
hfp_hf_deactivate_call_waiting_notification <= USED 0
hfp_hf_deactivate_calling_line_notification <= USED 0
hfp_hf_deactivate_echo_canceling_and_noise_reduction <= USED 0
hfp_hf_disable_report_extended_audio_gateway_error_result_code <= USED 0
hfp_hf_disable_status_update_for_all_ag_indicators <= USED 0
hfp_hf_enable_report_extended_audio_gateway_error_result_code <= USED 0
hfp_hf_enable_status_update_for_all_ag_indicators <= USED 0
hfp_hf_in_band_ringtone_active <= USED 0
hfp_hf_query_subscriber_number <= USED 0
hfp_hf_reject_incoming_call <= USED 0
hfp_hf_request_phone_number_for_voice_tag <= USED 0
hfp_hf_rrh_accept_held_call <= USED 0
hfp_hf_rrh_hold_call <= USED 0
hfp_hf_rrh_query_status <= USED 0
hfp_hf_rrh_reject_held_call <= USED 0
hfp_hf_send_at_command <= USED 0
hfp_hf_send_dtmf_code <= USED 0
hfp_hf_set_default_microphone_gain <= USED 0
hfp_hf_set_default_speaker_gain <= USED 0
hfp_hf_set_hf_indicator <= USED 0
hfp_hf_set_status_update_for_individual_ag_indicators <= USED 0
hfp_hf_terminate_held_calls <= USED 0
;FILE hid_device.o
hid_device_disconnect_control_channel <= USED 0
hid_device_disconnect_interrupt_channel <= USED 0
hid_device_register_report_data_callback <= USED 0
hid_device_register_report_request_callback <= USED 0
hid_device_register_set_report_callback <= USED 0
;FILE hp_max.o
;FILE hsp_ag.o
hsp_ag_connect <= USED 0
hsp_ag_deinit <= USED 0
hsp_ag_disconnect <= USED 0
hsp_ag_enable_custom_commands <= USED 0
hsp_ag_establish_audio_connection <= USED 0
hsp_ag_release_audio_connection <= USED 0
hsp_ag_send_result <= USED 0
hsp_ag_set_microphone_gain <= USED 0
hsp_ag_set_sco_packet_types <= USED 0
hsp_ag_set_speaker_gain <= USED 0
hsp_ag_start_ringing <= USED 0
hsp_ag_stop_ringing <= USED 0
;FILE http_client_base64.o
;FILE httpcon.o
;FILE httpencdec.o
Http_GetParam <= USED 0
;FILE i2c.o
get_timestamp <= USED 0
;FILE i2c_api.o
TWSI_Init <= USED 0
TWSI_REG_READ_CAM <= USED 0
TWSI_REG_READ_SHORT <= USED 0
TWSI_REG_READ_WORD <= USED 0
TWSI_REG_WRITE_CAM <= USED 0
TWSI_REG_WRITE_SHORT <= USED 0
TWSI_REG_WRITE_WORD <= USED 0
swap_n <= USED 0
;FILE icmp.o
;FILE icmp6.o
;FILE idct_vca.o
idctrow0 <= USED 0
idctrow0_intra <= USED 0
;FILE if2_to_ets.o
;FILE imsCapApi.o
;FILE imsConfEvent.o
;FILE imsMediaFsm.o
hdlCreateInRemoved <= USED 0
hdlRemovedInWFRnReady <= USED 0
imsRTPSession_SessionFeedback <= USED 0
;FILE imsSessApi.o
IMS_GetSessionInfo <= USED 0
IMS_SetReferToUser <= USED 0
;FILE imsSessConf.o
;FILE imsSessEarlyMedia.o
;FILE imsSessEvents.o
ims_FindSessEventDataSize <= USED 0
ims_SessHdlBitrateEvent <= USED 0
ims_SessPostMediaStartEvent <= USED 0
ims_SessPostRtpRtcpInactivityEvent <= USED 0
;FILE imsSessFsm.o
;FILE imsSessFsmModify.o
hdlOptionsStackInDialog <= USED 0
;FILE imsSessFsmOrig.o
hdlTerminateAppInResendInvite <= USED 0
;FILE imsSessFsmTerm.o
;FILE imsSessUtils.o
ims_EncodeMultiPartContent <= USED 0
ims_FreeBWInfoList <= USED 0
ims_FreeMediaStreamCapsMembers <= USED 0
ims_GetMediaIdFromMSRPId <= USED 0
ims_IsMediaDirUpgraded <= USED 0
ims_ParseCRList <= USED 0
ims_ReleaseAllRTPPort <= USED 0
ims_UpdateVideoCapsForDirChange <= USED 0
ims_findMediaIndexByMediaSessHd <= USED 0
;FILE imsUICC.o
IMSUICC_GetMSISDNBasedPublicUserIdentity <= USED 0
imsUICC_Release <= USED 0
;FILE imsUSSIFsmFunc.o
;FILE imsUssiXmlInterpreter.o
;FILE ims_Media.o
Media_AudioModeSet <= USED 0
Media_GetConfig <= USED 0
Media_GetSessionCodecConfig <= USED 0
Media_GetSessionConfig <= USED 0
Media_LinkAVSessions <= USED 0
Media_Release <= USED 0
Media_SetSession_Vowifi <= USED 0
Media_Vowifi_Handover <= USED 0
Media_isSession_Vowifi <= USED 0
RetreiveVideoBandwidth <= USED 0
_ZN18MediaEventListenerC1EPv <= USED 0
media_DirIdToString <= USED 0
media_VideoDefaultLevelDimension <= USED 0
media_VideoPlatformId2String <= USED 0
;FILE ims_api.o
IMS_CallDivert <= USED 0
IMS_Modify <= USED 0
IMS_QueryCallCaps <= USED 0
IMS_QueryFirstAvailableNetwork <= USED 0
IMS_QueryFirstCall <= USED 0
IMS_QueryNextAvailableNetwork <= USED 0
IMS_QueryNextCall <= USED 0
IMS_Reject <= USED 0
IMS_ReloadConfig <= USED 0
IMS_SMS_CancelOperation <= USED 0
IMS_Shutdown_force <= USED 0
;FILE ims_api_psram.o
GetImsEnable <= USED 0
imsGetATChannel <= USED 0
ims_set_at_channel <= USED 0
ims_set_cid <= USED 0
ims_task_resume <= USED 0
ims_task_suspend <= USED 0
restartImsTask <= USED 0
;FILE ims_ipsec.o
IPSec_CreateSP <= USED 0
IPSec_DeleteSP <= USED 0
ipsec_HdlDump <= USED 0
ipsec_release <= USED 0
;FILE ims_main.o
;FILE ims_syssocket.o
SysSocket_Attach <= USED 0
SysSocket_GetIoInterface <= USED 0
SysSocket_GetNameInfo <= USED 0
SysSocket_GetPeerName <= USED 0
SysSocket_GetSockName <= USED 0
SysSocket_Inet_Pton2 <= USED 0
SysSocket_SetPartialStreamSupportFlag <= USED 0
;FILE ims_test_main.o
ASSERT_EQ_imp <= USED 0
ASSERT_NE_imp <= USED 0
;FILE ims_ua_ipsec.o
;FILE ims_uaclient.o
;FILE imsd.o
thread_main <= USED 0
;FILE inet6.o
;FILE inet_chksum.o
;FILE init.o
BBU_UART_Open <= USED 0
BBU_puthexd <= USED 0
initGPIO <= USED 0
;FILE initTaskUtil.o
DIAGTHRPUTEnable <= USED 0
DSPResetAck <= USED 0
Delay <= USED 0
ICATTimerCountReadU64 <= USED 0
InitHSITimers <= USED 0
PMIC8211workaroundForFirstLDOoperation <= USED 0
PeripheralInits <= USED 0
PrintfResetStatus <= USED 0
StartDiagThroughPutTimer <= USED 0
SyncTimerHandler <= USED 0
SyncTimerInit <= USED 0
TCMStartFITimer <= USED 0
TCMTestTimerHandle <= USED 0
dumpTimerRegs <= USED 0
hwAcs_IPC_REG__READ <= USED 0
sendTestIPCcommand <= USED 0
startCommInit <= USED 0
timer_monitor <= USED 0
;FILE initatcmdsvr.o
initATCmdSvr <= USED 0
initATCmdSvr_Thread <= USED 0
;FILE int_lpc.o
;FILE int_lsf.o
;FILE intc_memRetain.o
INTCEnableInterruptOutput <= USED 0
;FILE intc_xirq.o
GPIOClearUsedForInterrupt <= USED 0
GPIOInterruptClear <= USED 0
GPIOSetUsedForInterrupt <= USED 0
Get_ICU_INTC_STATUS_0 <= USED 0
Get_ICU_INTC_STATUS_1 <= USED 0
INTCBindVirtualInit <= USED 0
INTCConfigurationGet <= USED 0
INTCConfigurationGet_XIRQ <= USED 0
INTCConfigureFiq <= USED 0
INTCConfigureSwi <= USED 0
INTCD2rmD2Register <= USED 0
INTCDisableAllInts <= USED 0
INTCISRGet <= USED 0
INTCISRGet_XIRQ <= USED 0
INTCIdleMaskDisable <= USED 0
INTCIdleMaskEnable <= USED 0
INTCInterruptHandlerFIQ_XIRQ <= USED 0
INTCInterruptHandlerIRQ <= USED 0
INTCPhase1Init <= USED 0
INTCPhase1Init_XIRQ <= USED 0
INTCSwIntCrossAssert <= USED 0
INTCXswiAssert <= USED 0
INTC_P_ICU_ConfigurationGet <= USED 0
INTC_P_ICU_Init <= USED 0
XIRQ_init <= USED 0
get_int_status_0 <= USED 0
get_int_status_1 <= USED 0
get_int_status_2 <= USED 0
;FILE inter_36.o
;FILE interval.o
rohc_interval_get_rfc5225_id_id_p <= USED 0
rohc_interval_get_rfc5225_msn_p <= USED 0
;FILE inv_sqrt.o
;FILE ip.o
;FILE ip4.o
ip_output_raw <= USED 0
ip_route <= USED 0
query_lte_pkt_lost <= USED 0
;FILE ip4_addr.o
ip4_addr_netmask_valid <= USED 0
;FILE ip6.o
ip6_output_raw <= USED 0
ip6_route <= USED 0
ip6_route_opt <= USED 0
;FILE ip6_addr.o
ip6addr_print_withnote <= USED 0
;FILE ip6_frag.o
;FILE ip_filter.o
_ip_filter_ip_layer_icmp <= USED 0
ip_filter_mode_set_ex <= USED 0
ip_filter_set_rules <= USED 0
ip_filter_set_rules_ex <= USED 0
port_filter_set_flag <= USED 0
port_filter_set_rules <= USED 0
;FILE ip_frag.o
ip_frag <= USED 0
;FILE ip_nat.o
_ip_nat_mangle_ftp <= USED 0
app_nat_info_print <= USED 0
dongle_trigger_nat_input <= USED 0
dongle_trigger_nat_output <= USED 0
ip_nat_info_print <= USED 0
ip_port_fwd_rule_add <= USED 0
lwip_dump_app_nat_hash_table <= USED 0
lwip_dump_ip_nat_hash_table <= USED 0
lwip_dump_proxy_set <= USED 0
lwip_set_proxy_addr <= USED 0
lwip_set_proxy_flag <= USED 0
lwip_set_proxy_func <= USED 0
lwip_set_proxy_port <= USED 0
;FILE ip_numbers.o
rohc_get_ip_proto_descr <= USED 0
;FILE ipcp.o
IpcpSendConfigReq <= USED 0
IpcpUpdateIpParamsTest <= USED 0
;FILE ipnetbuf.o
ACIPC_PS_uplink_indication <= USED 0
IpNetLwipGetUlPacketPoll <= USED 0
PlatformPsUlPathLogDisable <= USED 0
PlatformPsUlPathLogEnable <= USED 0
TgsinkPacketinSend <= USED 0
ipnet_uplink_indication <= USED 0
ipnet_uplink_resume <= USED 0
;FILE ipsec.o
ipsec_in_sa <= USED 0
;FILE ipsec_aes.o
;FILE ipsec_ah.o
;FILE ipsec_des.o
;FILE ipsec_esp.o
ipsec_esp_decapsulate_tunel <= USED 0
;FILE ipsec_md5.o
;FILE ipsec_sa.o
ipsec_sad_flush <= USED 0
ipsec_sad_print <= USED 0
ipsec_sad_print_single <= USED 0
ipsec_spd_flush <= USED 0
ipsec_spd_print <= USED 0
ipsec_spd_print_single <= USED 0
ipsec_spd_release_dbs <= USED 0
;FILE ipsec_sha1.o
;FILE ipsec_util.o
ipsec_check_replay_window <= USED 0
ipsec_inet_addr <= USED 0
ipsec_inet_aton <= USED 0
ipsec_inet_ntoa <= USED 0
ipsec_print_ip <= USED 0
ipsec_update_replay_window <= USED 0
;FILE ipsecdev.o
ipsec_set_tunnel <= USED 0
ipsecdev_netlink_output <= USED 0
ipsecdev_service <= USED 0
;FILE jet.o
JET_Clear_Queue <= USED 0
JET_CloseFile <= USED 0
JET_GetAppData <= USED 0
JET_GetEvent <= USED 0
JET_Init <= USED 0
JET_OpenFile <= USED 0
JET_Pause <= USED 0
JET_Play <= USED 0
JET_QueueSegment <= USED 0
JET_SetMuteFlag <= USED 0
JET_SetMuteFlags <= USED 0
JET_Shutdown <= USED 0
JET_Status <= USED 0
JET_TriggerClip <= USED 0
;FILE kinu.o
KiPoolBlockInUse <= USED 0
;FILE kiosfail.o
warnAssertPrint <= USED 0
warnCheckParam <= USED 0
;FILE kiosinit.o
KiOsSystemIsInitialised <= USED 0
;FILE kioslow.o
KiOsIsMemorySignal <= USED 0
;FILE kiosmem.o
KiOsAllocPollMemory <= USED 0
KiOsReallocMemoryEx <= USED 0
KiOsRerequestMemoryEx <= USED 0
KiOsResizeMemory <= USED 0
;FILE kiosq.o
KiOsFlushBlockQueue <= USED 0
;FILE kiossem.o
KiOsIncIntSemaphore <= USED 0
KiOsPollSemaphore <= USED 0
;FILE kiossig.o
KiOsCreateNonTraceIntSignal <= USED 0
KiOsReceiveNonTraceSignal <= USED 0
KiOsRequestSignal <= USED 0
KiOsRequestZeroSignal <= USED 0
KiOsSendNonTraceIntSignal <= USED 0
KiOsSendSignalPoll <= USED 0
PoolRuningUsage <= USED 0
;FILE kiosstat.o
KiCheckStackOverFlow <= USED 0
KiSendDevAssertInd <= USED 0
KiSendDevCheckInd <= USED 0
KiSendDevFailInd <= USED 0
KiSendDumpMemReq <= USED 0
;FILE kiostask.o
KiGetErrnoAddr <= USED 0
KiOsIsSim1Task <= USED 0
KiSuspendAllGkiRegisteredTasks <= USED 0
;FILE kiostim.o
KiOsRestartTimer <= USED 0
KiTimGetActiveListHeadAddr <= USED 0
KiTimGetCurrentTickTimeAddr <= USED 0
KiTimGetTimerArrayAddr <= USED 0
;FILE kiostti.o
;FILE kiprintf.o
_printf <= USED 0
vprintf <= USED 0
;FILE kmdynmem.o
KmMemoryCreatePool <= USED 0
KmMemoryFree <= USED 0
KmMemoryGet <= USED 0
KmMemoryGetFail <= USED 0
KmMemoryGetSize <= USED 0
KmMemoryReSize <= USED 0
KmMemoryWalk <= USED 0
KmMemoryWalkStart <= USED 0
;FILE l1a_cal.o
GetLtePmaxReductionFlag <= USED 0
L1aAcatTriggerAdcQueryReq <= USED 0
L1aCheckIfNvmIsCorrupted <= USED 0
L1aDisableD2BeforeCalStart <= USED 0
L1aGetCalibrationModeState <= USED 0
L1aGetSimIdOnRat <= USED 0
L1aGetSimIdOnRatActive <= USED 0
L1aGetSimIdToHandlePlatQuery <= USED 0
L1aHandleAdditionalMprTableData <= USED 0
L1aHandleDcxoCalData <= USED 0
L1aHandleLteQtEtFreqAdjTableData <= USED 0
L1aHandlePaCtrlTableData <= USED 0
L1aHandleRfConfigTableData <= USED 0
L1aHandleRfTuningToolData <= USED 0
L1aSendDefaultTableWithZero <= USED 0
L1aSendPureCalibrationDataToDsp <= USED 0
L1aSendPureCalibrationDataToDspByShareMemory <= USED 0
L1aSendRfTuningToolSuperCmd <= USED 0
L1aStartDigRf4BusLoopbackTest <= USED 0
L1aTempReadingCallback <= USED 0
RspGet4GCalibrationStatus <= USED 0
getAdcValueForRtn <= USED 0
getTxPowerBackoffMode <= USED 0
setRfTemperatureCelsiusValue <= USED 0
setRfTemperatureValue <= USED 0
;FILE l1a_drat.o
L1aCheckHasRxModeReport <= USED 0
L1aCheckIfDsDs <= USED 0
L1aDisplaySpyDebug <= USED 0
L1aGetL1RAT <= USED 0
L1aGetSimGsmActivated <= USED 0
L1aGetSimLteActivated <= USED 0
L1aMultiIratL1GetRAT <= USED 0
L1aResetIratInfoBeforePowerOffHandled <= USED 0
L1aSaveSpyToNvm <= USED 0
L1aSaveSpyToNvmEE <= USED 0
L1aSetL1aDspDuringSleep <= USED 0
L1aSetSimGsmActivated <= USED 0
L1aSetSimLteActivated <= USED 0
L1aStartSpyDebug <= USED 0
L1aStopSpyDebug <= USED 0
L1aTestCancelCellSearchAlwaysFail <= USED 0
L1aTestCellSearchAlwaysFail <= USED 0
LteL1aDisableL1aDetailDiag <= USED 0
LteL1aEnableL1aDetailDiag <= USED 0
LtePmaxReductionConfigFile_CreateNVMFile <= USED 0
LteWakeupIntHISRHandler <= USED 0
plDratsendGsmMeasureParams <= USED 0
;FILE l1a_main.o
L1aGetIpcShareMemoryBaseAddress <= USED 0
L1aHandleTimerExpiryL1AssertDebugInd <= USED 0
L1aSetLteCalQtEtFreqAdjTableDataShareMemoryAddress <= USED 0
L1aSetLteCalRfConfigTableDataShareMemoryAddress <= USED 0
;FILE l1a_meas.o
L1aAdaptForEcphyInterFreqInfoReq <= USED 0
L1aAdaptForEcphyIntraFreqInfoReq <= USED 0
L1aAdaptForEcphyMonitorInterFreqCellReq <= USED 0
L1aGetIpcStructureData <= USED 0
L1aHandleDetectNcellInd <= USED 0
L1aHandleInterNcellMeasInd <= USED 0
L1aHandleIntraNcellMeasInd <= USED 0
L1aHandleScellMeasInd <= USED 0
L1ahandleEcphyEngInfoInd <= USED 0
;FILE l1a_phy.o
L1aAdaptForEcphyHandoverReq <= USED 0
L1aAdaptForEcphyRlCommonConfigReq <= USED 0
L1aAdaptForEcphyRlCommonSib1ConfigReq <= USED 0
L1aAdaptForEcphyRlDedicatedConfigReq <= USED 0
L1aCheckCurrentStateNeedCancelSleep <= USED 0
L1aCheckIfDirectSendDeactiveReq <= USED 0
L1aCheckL1IsScoreMultiple32 <= USED 0
L1aGetCpTimerSleepInd <= USED 0
L1aHandleDrxCellInfoInd <= USED 0
L1aHandleEcphyEcgiBchInd <= USED 0
L1aHandleLteSleepEndInd <= USED 0
L1aHandleLteSleepInd <= USED 0
L1aNormalDeactivate <= USED 0
L1aPermittSleepInd <= USED 0
L1aSendEcphyFlashDspReq <= USED 0
L1aSendEcphyLongEdrxSleepReq <= USED 0
L1aSetL1aHandleSleepIndIsOnGoing <= USED 0
L1aTestStubEarlyWakeup <= USED 0
L1aTestStubTriggerEarlyWakeup <= USED 0
etiHandoverToLteFailReq <= USED 0
etiReselectToLteFailReq <= USED 0
etiReselectToTddUtraCnf <= USED 0
ewiHandoverToLteFailReq <= USED 0
fliScanGapFinishInd <= USED 0
fliScanGapReqInd <= USED 0
;FILE l1a_response.o
L1aGetPlatCommonRspType <= USED 0
L1aHandleCalResultDataAck <= USED 0
L1aHandleEcphyCommonRsp <= USED 0
L1aHandleEcphyL1AssertDebugInd <= USED 0
L1aHandleEcphySacErrcL1aCommonRes <= USED 0
L1aHandleL1LogInfoInd <= USED 0
L1aHandleLteBackupL1DataInd <= USED 0
L1aHandlePlatCommonIpcRsp <= USED 0
L1aHandleReadAdcRtpValueCnf <= USED 0
L1aHandleReadAdcValueCnf <= USED 0
L1aHandleReadRfTempCnf <= USED 0
L1aHandleSomeIpcMsgsInHisr <= USED 0
L1aIpcResponseEntrance <= USED 0
PlatToL1aCommonIpcReq <= USED 0
;FILE l1a_sys.o
L1aAdaptForEcphyClassmarkReq <= USED 0
L1aCalcTimestampOutRemain <= USED 0
L1aCheckIfNeedCancelSleep <= USED 0
L1aCheckMacResetBufferNeeded <= USED 0
L1aCheckShareMemoryOverlay <= USED 0
L1aGetAddressHighPart <= USED 0
L1aGetAddressLowPart <= USED 0
L1aGetCurrentServingEuarfcn <= USED 0
L1aIratIntermediateState <= USED 0
L1aIsInConnectedStatus <= USED 0
L1aMemCpyInt16 <= USED 0
L1aMemCpyInt32 <= USED 0
;FILE l1a_task.o
LteL1aTask1 <= USED 0
;FILE l1cellularPowerApplication.o
CPACheckNeedSendIPCDirectly <= USED 0
CPAStateChangeFromGsmActive <= USED 0
CPAStateChangeFromGsmDrx <= USED 0
CPAStateChangeFromGsmOos <= USED 0
CPAStateChangeFromUMTSActive <= USED 0
CPAStateChangeFromUMTSDrx <= USED 0
CPAStateChangeFromUMTSOos <= USED 0
CPAStateChangeGsmActiveToGsmDrx <= USED 0
CPAStateChangeGsmActiveToGsmOos <= USED 0
CPAStateChangeGsmActiveToLteActive <= USED 0
CPAStateChangeGsmActiveToNoActiveRat <= USED 0
CPAStateChangeGsmActiveToUMTSActive <= USED 0
CPAStateChangeGsmDrxToGsmActive <= USED 0
CPAStateChangeGsmOosToGsmActive <= USED 0
CPAStateChangeLGOosToWOos <= USED 0
CPAStateChangeLWOosToGsmOos <= USED 0
CPAStateChangeLteActiveToGsmActive <= USED 0
CPAStateChangeLteActiveToUMTSActive <= USED 0
CPAStateChangeNoActiveRatToGsmActive <= USED 0
CPAStateChangeNoActiveRatToUMTSActive <= USED 0
CPAStateChangeUMTSActiveToGsmActive <= USED 0
CPAStateChangeUMTSActiveToLteActive <= USED 0
CPAStateChangeUMTSActiveToNoActiveRat <= USED 0
CPAStateChangeUMTSActiveToUMTSDrx <= USED 0
CPAStateChangeUMTSActiveToUMTSOos <= USED 0
CPAStateChangeUMTSDrxToUMTSActive <= USED 0
CPAStateChangeUMTSOosToUMTSActive <= USED 0
CellularPowerAppD2Prepare <= USED 0
CellularPowerAppGetCpaState <= USED 0
CellularPowerAppIsOOSState <= USED 0
CellularPowerAppLteD2Prepare <= USED 0
CellularPowerAppLteD2Recovery <= USED 0
CellularPowerAppLteWakeupCallback <= USED 0
CellularPowerAppRMGSMSCWakeupCallback <= USED 0
CellularPowerAppRMWBSMWakeup <= USED 0
L1CGetCPAState <= USED 0
L1cCellularPowerAppDisableD2 <= USED 0
L1cCellularPowerAppEnableD2 <= USED 0
RMTCUD2Prepare <= USED 0
RMTCUD2Recovery <= USED 0
UMTSResetStickBit <= USED 0
disableCpaFunc <= USED 0
disableD2InWb <= USED 0
enableCpaFunc <= USED 0
enableD2InWb <= USED 0
l1GsmCheckDisableD2inSetGsmSimb <= USED 0
l1GsmCheckEnableD2inSetGsmSimb <= USED 0
l1GsmRebindTCUinSetGsmSimb <= USED 0
;FILE l2cap.o
l2cap_accept_ertm_connection <= USED 0
l2cap_add_event_handler <= USED 0
l2cap_cbm_accept_connection <= USED 0
l2cap_cbm_create_channel <= USED 0
l2cap_cbm_decline_connection <= USED 0
l2cap_cbm_provide_credits <= USED 0
l2cap_cbm_register_service <= USED 0
l2cap_cbm_unregister_service <= USED 0
l2cap_create_ertm_channel <= USED 0
l2cap_ertm_set_busy <= USED 0
l2cap_ertm_set_ready <= USED 0
l2cap_le_accept_connection <= USED 0
l2cap_le_can_send_now <= USED 0
l2cap_le_create_channel <= USED 0
l2cap_le_decline_connection <= USED 0
l2cap_le_disconnect <= USED 0
l2cap_le_provide_credits <= USED 0
l2cap_le_register_service <= USED 0
l2cap_le_request_can_send_now_event <= USED 0
l2cap_le_send_data <= USED 0
l2cap_le_unregister_service <= USED 0
l2cap_remove_event_handler <= USED 0
l2cap_require_security_level_2_for_outgoing_sdp <= USED 0
l2cap_send_echo_request <= USED 0
;FILE l2cap_signaling.o
;FILE l3dec_ie.o
DecodeAlertingPatternIE <= USED 0
DecodeAuxiliaryStatesIE <= USED 0
DecodeBackupBearerCapabilityIE <= USED 0
DecodeBackupBearerCapabilityIEBody <= USED 0
DecodeBearerCapabilityIE <= USED 0
DecodeBearerCapabilityIEBody <= USED 0
DecodeCallStateIE <= USED 0
DecodeCalledPartyBCDNumIE <= USED 0
DecodeCalledPartySubaddressIE <= USED 0
DecodeCallingPartyBCDNumIE <= USED 0
DecodeCallingPartySubaddressIE <= USED 0
DecodeCauseOfNoCliIE <= USED 0
DecodeCipheringKeySeqNumIE <= USED 0
DecodeCongestionLevelIE <= USED 0
DecodeConnectedNumberIE <= USED 0
DecodeConnectedSubaddressIE <= USED 0
DecodeFacilityIE <= USED 0
DecodeHighLayerCompatibilityIE <= USED 0
DecodeIdTypeIE <= USED 0
DecodeKeypadFacilityIE <= USED 0
DecodeLSAIdentityIE <= USED 0
DecodeLowLayerCompatibilityIE <= USED 0
DecodeMmTimer <= USED 0
DecodeNetworkCallControlCapabilitiesIE <= USED 0
DecodeNotificationIndicatorIE <= USED 0
DecodePriorityLevelIE <= USED 0
DecodeProgressIndicatorIE <= USED 0
DecodeRedirPartyBCDNumIE <= USED 0
DecodeRedirPartySubaddressIE <= USED 0
DecodeRejectCauseIE <= USED 0
DecodeRepeatIndicatorIE <= USED 0
DecodeShiftIE <= USED 0
DecodeSignalIE <= USED 0
DecodeSmallUserUserIE <= USED 0
DecodeSubaddressIE <= USED 0
DecodeUserUserIE <= USED 0
;FILE l3decgm.o
DecodeBitRateExtended <= USED 0
DecodeEgprsModeFlagIE <= USED 0
DecodeModeFlagIE <= USED 0
DecodePduDescriptionIE <= USED 0
L3DecodeGprsMessage <= USED 0
;FILE l3declow.o
AssertBitValue <= USED 0
AssertFixedContentLength <= USED 0
AssertFixedIELength <= USED 0
AssertValue <= USED 0
AssertVarIELength <= USED 0
DecodeFrameNumber <= USED 0
DecodeFreqListRangeInfo <= USED 0
Extract3ByteBitField <= USED 0
ExtractFreqListInfoFields <= USED 0
ExtractNibble <= USED 0
GpExtractBit <= USED 0
GpExtractBitField16 <= USED 0
GpExtractBitField32 <= USED 0
GpExtractBitField8 <= USED 0
GpExtractRestOctetBit <= USED 0
ProcessUnrecognisedIE <= USED 0
;FILE l3declte.o
DecodeCsfbResponseIE <= USED 0
DecodeEmmExServiceTypeIE <= USED 0
DecodeEpsAttachTypeIE <= USED 0
DecodeEpsAuthParamResIE <= USED 0
DecodeEpsMoDetachTypeIE <= USED 0
DecodeEpsUpdateTypeIE <= USED 0
DecodeKsiAndSnIE <= USED 0
DecodeSpareHalfOctetIE <= USED 0
DecodeSupportedCodecsListIE <= USED 0
DecodeTrackingAreaIdentityIE <= USED 0
DecodeUeNetworkCapabilityIE <= USED 0
DecodeUeRadioCapabilityInfoUpdateNeededIE <= USED 0
;FILE l3decmn.o
DecodeSsMessage <= USED 0
;FILE l3enc_ie.o
EncodeAdditionalUpdateParametersIE <= USED 0
EncodeAuthParamSresExtIE <= USED 0
EncodeAuthParamSresIE <= USED 0
EncodeAuxiliaryStatesIE <= USED 0
EncodeBearerCapabilityIE <= USED 0
EncodeCMServiceTypeIE <= USED 0
EncodeCallStateIE <= USED 0
EncodeCalledPartyBCDNumIE <= USED 0
EncodeCalledPartySubaddressIE <= USED 0
EncodeCallingPartyBCDNumIE <= USED 0
EncodeCallingPartySubaddressIE <= USED 0
EncodeCauseIE <= USED 0
EncodeCcCapabilitiesIE <= USED 0
EncodeClirInvocationIE <= USED 0
EncodeClirSuppressionIE <= USED 0
EncodeCongestionLevelIE <= USED 0
EncodeConnectedSubaddressIE <= USED 0
EncodeEmergencyServiceCategoryIE <= USED 0
EncodeFacilityIE <= USED 0
EncodeHighLayerCompatibilityIE <= USED 0
EncodeImmediateModificationIndicatorIE <= USED 0
EncodeKeypadFacilityIE <= USED 0
EncodeLaiWith3DigitMncIE <= USED 0
EncodeLocationUpdatingTypeIE <= USED 0
EncodeLowLayerCompatibilityIE <= USED 0
EncodeMoreDataIE <= USED 0
EncodeNotificationIndicatorIE <= USED 0
EncodePriorityIE <= USED 0
EncodeRejectCauseIE <= USED 0
EncodeRepeatIndicatorIE <= USED 0
EncodeReverseCallSetupDirectionIE <= USED 0
EncodeSmallUserUserIE <= USED 0
EncodeSsVersionIndicatorIE <= USED 0
EncodeStreamIdentifierIE <= USED 0
EncodeUserUserIE <= USED 0
L3EncodeHeader <= USED 0
;FILE l3encgm.o
EncodeGprsTimer2IE <= USED 0
EncodeGprsTimerIE <= USED 0
EncodePdpAddressIE <= USED 0
EncodePtmsiSignature2IE <= USED 0
EncodeR99MsRadAccCapbIE <= USED 0
EncodeSmCauseIE <= USED 0
L3EncodeGprsMessage <= USED 0
cancelRel5QoS <= USED 0
setRel5QoS <= USED 0
;FILE l3enclte.o
EncodeAPNAMaxBitRateIE <= USED 0
EncodeAdditionalUpdateResultIE <= USED 0
EncodeCliIE <= USED 0
EncodeControlPlaneServiceTypeIE <= USED 0
EncodeEmergencyNumberListIE <= USED 0
EncodeEpsAttachResultIE <= USED 0
EncodeEpsNetworkFeatureSupportIE <= USED 0
EncodeEpsUpdateResultIE <= USED 0
EncodeLcsClientIdentityIE <= USED 0
EncodeLcsIndicatorIE <= USED 0
EncodeNasSecurityAlgorithmsIE <= USED 0
EncodePagingIdentityIE <= USED 0
EncodeSpareHalfOctetIE <= USED 0
EncodeSsCodeIE <= USED 0
EncodeTaiListIE <= USED 0
EncodeUeSecurityCapabilityIE <= USED 0
;FILE l3encode.o
GpEncodeOptField7 <= USED 0
GpEncodeOptField8 <= USED 0
L3EncodeAnyMessage <= USED 0
L3EncodeMessage <= USED 0
;FILE l_abs.o
;FILE l_comp.o
;FILE l_deposit_h.o
;FILE l_deposit_l.o
;FILE l_extract.o
;FILE l_negate.o
;FILE l_shr_r.o
;FILE lag_wind.o
;FILE lcp.o
LcpKickoffEchoRequest <= USED 0
;FILE le_asr.o
;FILE le_device_db_tlv.o
le_device_db_dump <= USED 0
;FILE levinson.o
;FILE lfs.o
lfs_access <= USED 0
lfs_dir_rewind <= USED 0
lfs_dir_seek <= USED 0
lfs_dir_tell <= USED 0
lfs_file_rewind <= USED 0
lfs_file_sync <= USED 0
lfs_file_truncate <= USED 0
lfs_getattr <= USED 0
lfs_removeattr <= USED 0
lfs_setattr <= USED 0
lfs_stat <= USED 0
lfs_unmount <= USED 0
;FILE lfs_api.o
lfs_get_prefix <= USED 0
lfs_io_Access <= USED 0
lfs_io_dir_make <= USED 0
lfs_io_eof <= USED 0
lfs_io_stat <= USED 0
lfs_io_used <= USED 0
lfs_to_fatstat <= USED 0
;FILE lfs_cache.o
LfsMsgQRemove <= USED 0
lfs_cache_task_resume <= USED 0
lfs_cache_task_suspend <= USED 0
lfs_erase_all <= USED 0
lfs_erase_block <= USED 0
;FILE lfs_util.o
;FILE libhttpclient.o
http_client_perform_car <= USED 0
http_client_stop <= USED 0
string_HighToLow <= USED 0
user_token <= USED 0
;FILE loadTable.o
StrtupIsPowerup <= USED 0
commImageTableInit <= USED 0
getAppComShareMemAddr <= USED 0
getAppCom_RDATA_Bak_MemAddr <= USED 0
getCommImageBaseAddr <= USED 0
getCommNumOfLife <= USED 0
get_apn_begin_address <= USED 0
get_apn_bin_size <= USED 0
get_apn_end_address <= USED 0
get_apn_ex_begin_address <= USED 0
get_apn_ex_bin_size <= USED 0
get_apn_ex_end_address <= USED 0
get_btbin_end_address <= USED 0
get_btlst_end_address <= USED 0
get_cp_binary_size <= USED 0
get_ddr_heap_size <= USED 0
get_dsp_backup_addr <= USED 0
get_dsp_backup_size <= USED 0
get_dsp_bin_size <= USED 0
get_dsp_copy_size <= USED 0
get_dsp_partition_size <= USED 0
get_factory_a_bin_size <= USED 0
get_factory_b_end_address <= USED 0
get_factory_b_start_address <= USED 0
get_gpsfw_begin_addr <= USED 0
get_gpsfw_bin_size <= USED 0
get_gpspvt_begin_addr <= USED 0
get_gpspvt_bin_size <= USED 0
get_legabin_begin_address <= USED 0
get_legabin_end_address <= USED 0
get_mmipool_end_address <= USED 0
get_mmipool_start_address <= USED 0
get_nvm_bin_size <= USED 0
get_reserved_end_address <= USED 0
get_reserved_start_address <= USED 0
get_rf_bin_size <= USED 0
get_rf_partition_size <= USED 0
get_system_end_address <= USED 0
get_system_start_address <= USED 0
get_updater_backup_end_address <= USED 0
get_updater_backup_start_address <= USED 0
get_updater_copy_size <= USED 0
get_updater_end_address <= USED 0
incrementCommNumOfLife <= USED 0
whether_RW_region_compressed <= USED 0
;FILE log.o
_log_output_ICAT <= USED 0
_log_output_ICAT_ln <= USED 0
_log_output_empty <= USED 0
_log_output_uart <= USED 0
log_dump <= USED 0
log_flush <= USED 0
log_get_module_level <= USED 0
log_init <= USED 0
log_module_get <= USED 0
log_set_trace_level <= USED 0
;FILE log2.o
;FILE log2_norm.o
;FILE lowpower.o
test_c1_enter <= USED 0
test_c1_exit <= USED 0
test_d2_enter <= USED 0
test_d2_exit <= USED 0
uiC1CallbackRegister <= USED 0
uiC1CallbackunRegister <= USED 0
uiD2CallbackRegister <= USED 0
uiD2CallbackunRegister <= USED 0
uiEnterC1Callback <= USED 0
uiEnterD2Callback <= USED 0
uiExitC1Callback <= USED 0
uiExitD2Callback <= USED 0
uiLowpowerInit <= USED 0
uiSetSuspendFlag <= USED 0
uiSuspend <= USED 0
;FILE lowtasks.o
LowEventActivate <= USED 0
LowEventBind <= USED 0
LowEventUnBind <= USED 0
;FILE lpc.o
;FILE lrm.o
Lrm_CheckLocationInfoValid <= USED 0
Lrm_GetPosBaseInfo <= USED 0
Lrm_RegisterLocationMonitor <= USED 0
Lrm_UnRegisterLocationMonitor <= USED 0
Lrm_UpdateLocationInfo <= USED 0
;FILE lrm_gml.o
_gml_format_pidf_lo_circle <= USED 0
_gml_format_pidf_lo_ellipse <= USED 0
_gml_format_pidf_lo_ellipsoid <= USED 0
_gml_format_pidf_lo_sphere <= USED 0
lrmGml_Destroy <= USED 0
lrmGml_Init <= USED 0
;FILE lsfwt.o
;FILE lsp.o
;FILE lsp_avg.o
;FILE lsp_az.o
_Z19Get_lsp_pol_wrapperPsPiS0_ <= USED 0
;FILE lsp_lsf.o
;FILE ltebm.o
LteBMInitRxSB <= USED 0
LteBmHandleUlUmDataHarqCnf <= USED 0
LteBmProcessDataAbortInd <= USED 0
LteIPCReleaseRxSBBlock <= USED 0
LteMacKeepReservedRxBlock <= USED 0
LteMacSFAllocateRxL2BBlock <= USED 0
LteMacSfSendIPCDlBlockMove <= USED 0
UMTSAllocateTxL2Buffer <= USED 0
UMTSMacAllocateL1Buffer <= USED 0
UMTSMacAllocateL2RxBuffer <= USED 0
UMTSMacReleaseL1Buffer <= USED 0
UMTSMacSetL1BufferNum <= USED 0
UMTSReleaseTxL2Buffer <= USED 0
UMTSRlcReleaseL2RxBuffer <= USED 0
;FILE ltenetworkcard.o
LteUsbDataTxReq <= USED 0
checksum <= USED 0
usbNetTaskInit <= USED 0
usbRxEnable <= USED 0
usbRxUnBlocked <= USED 0
;FILE lterabmgmm.o
;FILE lterabmisc.o
isLteInLoopbackMode <= USED 0
;FILE lterabmmain.o
;FILE lterabmpdp.o
ImsDefaultPdnDataReceived <= USED 0
LteRabmGetAllPdcpPendingUplinkData <= USED 0
LteRabmReleasePendingSnDataNewReq <= USED 0
LteRabmReleaseSnDataList <= USED 0
LteRabmSendSignalToPdp <= USED 0
LteRabmSnLteSnDataRsp <= USED 0
LteSnTraceDL <= USED 0
LteSnTraceNone <= USED 0
LteSnTraceStatus <= USED 0
LteSnTraceUL <= USED 0
LteSnTraceULDL <= USED 0
RabmProcessWaitToReestExpireInd <= USED 0
checkRabmPendingData <= USED 0
;FILE lterabmrrc.o
LteRabmRabmRrcEstablishRej <= USED 0
LteRabmRabmRrcEstablishRes <= USED 0
LteRabmRabmRrcReleaseRes <= USED 0
;FILE lterabmsm.o
LteRabmSnSmDeactivateReq <= USED 0
;FILE lterabmtimers.o
LteRabmStartLoopBackTimer <= USED 0
LteRabmStartWaitToReestTimer <= USED 0
LteRabmStopAllLteRabmTimers <= USED 0
LteRabmStopWaitToReestTimer <= USED 0
;FILE lterabmutil.o
LteRabmCTFlagDisable <= USED 0
LteRabmCTFlagEnable <= USED 0
LteRabmCTFlagShow <= USED 0
LteRabmDiagFlagOff <= USED 0
LteRabmDiagFlagOn <= USED 0
LteRabmDiagFlagShow <= USED 0
LteRabmFillStatistics <= USED 0
LteRabmReleaseEpsCommon <= USED 0
LteRabmReleaseLtePdcpSduList <= USED 0
LteRabmReleaseSnDataReq <= USED 0
LteRabmReplacePacketFilter <= USED 0
;FILE lterlc.o
LteMacDataInd <= USED 0
LteMacDataReq <= USED 0
LteRlcControlMaxAmPduNumToDlAssemble <= USED 0
LteRlcControlTraceFlag <= USED 0
LteRlcGetUplaneData <= USED 0
LteRlcProcessPdcpDiscardReq <= USED 0
LteRlcUMTransmitIndSetData <= USED 0
lteRlcHeaderMinLen <= USED 0
;FILE lterlcam.o
LteDiscardUlPacketsDueToHwm <= USED 0
LteRlcMemoryCheck <= USED 0
;FILE lterlccommon.o
ErlcPrintf <= USED 0
LteRlcGetIdxFromSn <= USED 0
LteRlcGetSnFromIdx <= USED 0
LteRlcSetHeader <= USED 0
;FILE lterlcum.o
LteUmRlcAddPdcpData <= USED 0
LteUmRlcProcessPdcpDiscardReq <= USED 0
;FILE lterrc_decb_r1551.o
PerDec_EuAMF_Identifier_r15 <= USED 0
PerDec_EuAUL_Config_r15_setup_aul_Subframes_r15_str <= USED 0
PerDec_EuAbsoluteTimeInfo_r10_str <= USED 0
PerDec_EuAntennaInfoDedicatedSTTI_r15_codebookSubsetRestriction_n4TxAntenna_tm4_r15_str <= USED 0
PerDec_EuAntennaInfoDedicatedSTTI_r15_codebookSubsetRestriction_n4TxAntenna_tm8_r15_str <= USED 0
PerDec_EuAntennaInfoDedicatedSTTI_r15_codebookSubsetRestriction_n4TxAntenna_tm9and10_r15_str <= USED 0
PerDec_EuAntennaInfoDedicatedSTTI_r15_codebookSubsetRestriction_n8TxAntenna_tm9and10_r15_str <= USED 0
PerDec_EuBCCH_DL_SCH_MessageType_BR_r13_messageClassExtension_str <= USED 0
PerDec_EuBCCH_DL_SCH_MessageType_MBMS_r14_messageClassExtension_str <= USED 0
PerDec_EuCSFBParametersResponseCDMA2000_criticalExtensions_criticalExtensionsFuture_str <= USED 0
PerDec_EuCSFBParametersResponseCDMA2000_v8a0_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuCSG_Identity <= USED 0
PerDec_EuC_RNTI <= USED 0
PerDec_EuCellGlobalIdCDMA2000_cellGlobalId1XRTT_str <= USED 0
PerDec_EuCellGlobalIdCDMA2000_cellGlobalIdHRPD_str <= USED 0
PerDec_EuCellIdentity <= USED 0
PerDec_EuDL_CCCH_MessageType_messageClassExtension_messageClassExtensionFuture_r15_str <= USED 0
PerDec_EuHandoverFromEUTRAPreparationRequest_criticalExtensions_criticalExtensionsFuture_str <= USED 0
PerDec_EuHandoverFromEUTRAPreparationRequest_v1020_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuIDC_SubframePattern_r11_subframePatternTDD_r11_subframeConfig0_r11_str <= USED 0
PerDec_EuIDC_SubframePattern_r11_subframePatternTDD_r11_subframeConfig6_r11_str <= USED 0
PerDec_EuIP_Address_r13_ipv6_r13_str <= USED 0
PerDec_EuInitialUE_Identity_5GC_ng_5G_S_TMSI_Part1_str <= USED 0
PerDec_EuInitialUE_Identity_5GC_randomValue_str <= USED 0
PerDec_EuInitialUE_Identity_randomValue_str <= USED 0
PerDec_EuLoggedMeasurementConfiguration_r10_criticalExtensions_criticalExtensionsFuture_str <= USED 0
PerDec_EuLoggedMeasurementConfiguration_v1530_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuMBMSCountingRequest_r10_nonCriticalExtension_str <= USED 0
PerDec_EuMBSFNAreaConfiguration_v1430_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuMMEC <= USED 0
PerDec_EuMobilityFromEUTRACommand_criticalExtensions_criticalExtensionsFuture_str <= USED 0
PerDec_EuMobilityFromEUTRACommand_v1530_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuMobilityFromEUTRACommand_v8d0_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuNeighCellConfig <= USED 0
PerDec_EuP_C_AndCBSR_r15_codebookSubsetRestriction4_r15_str <= USED 0
PerDec_EuPosSystemInformation_r15_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuRAND_CDMA2000 <= USED 0
PerDec_EuRRCConnectionReconfiguration_v13c0_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuRRCConnectionReconfiguration_v1530_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuRRCEarlyDataComplete_r15_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuRRCEarlyDataComplete_r15_criticalExtensions_criticalExtensionsFuture_str <= USED 0
PerDec_EuRRC_InactiveConfig_r15_nonCriticalExtension_str <= USED 0
PerDec_EuSCPTMConfiguration_BR_r14_nonCriticalExtension_str <= USED 0
PerDec_EuSCPTMConfiguration_v1340_nonCriticalExtension_str <= USED 0
PerDec_EuSPDCCH_Elements_r15_resourceBlockAssignment_r15_resourceBlockAssignment_r15_str <= USED 0
PerDec_EuSSB_ToMeasure_r15_longBitmap_r15_str <= USED 0
PerDec_EuSS_RSSI_Measurement_r15_measurementSlots_r15_str <= USED 0
PerDec_EuShortI_RNTI_r15 <= USED 0
PerDec_EuShortMAC_I <= USED 0
PerDec_EuSubframeBitmapSL_r12_bs40_r12_str <= USED 0
PerDec_EuSubframeBitmapSL_r12_bs42_r12_str <= USED 0
PerDec_EuSubframeBitmapSL_r14_bs100_r14_str <= USED 0
PerDec_EuSubframeBitmapSL_r14_bs40_r14_str <= USED 0
PerDec_EuSubframeBitmapSL_r14_bs50_r14_str <= USED 0
PerDec_EuSubframeBitmapSL_r14_bs60_r14_str <= USED 0
PerDec_EuSupportedBandwidthCombinationSet_r10_str <= USED 0
PerDec_EuSystemInformationBlockType1_MBMS_r14_nonCriticalExtension_str <= USED 0
PerDec_EuSystemInformationBlockType1_v1540_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuSystemInformationBlockType2_v13c0_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuSystemInformationBlockType3_v10l0_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuSystemInformationBlockType6_v8h0_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuTrackingAreaCode <= USED 0
PerDec_EuUECapabilityEnquiry_v1550_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuUEInformationRequest_r9_criticalExtensions_criticalExtensionsFuture_str <= USED 0
PerDec_EuUEInformationRequest_v1530_IEs_nonCriticalExtension_str <= USED 0
;FILE lterrc_decc_r1551.o
PerDec_EuAUL_Config_r15 <= USED 0
PerDec_EuAntennaInfoDedicatedSTTI_r15 <= USED 0
PerDec_EuAntennaInfoDedicatedSTTI_r15_codebookSubsetRestriction <= USED 0
PerDec_EuAntennaInfoDedicated_v1530 <= USED 0
PerDec_EuAntennaInfoDedicated_v1530_setup <= USED 0
PerDec_EuAreaConfiguration_r10 <= USED 0
PerDec_EuBCCH_DL_SCH_MessageType_BR_r13 <= USED 0
PerDec_EuBCCH_DL_SCH_MessageType_BR_r13_c1 <= USED 0
PerDec_EuCQI_ReportAperiodicHybrid_r14_triggers_r14 <= USED 0
PerDec_EuCSI_RS_ConfigZP_ApList_r14 <= USED 0
PerDec_EuCellGlobalIdCDMA2000 <= USED 0
PerDec_EuCellIdentity_5GC_r15 <= USED 0
PerDec_EuDRB_ToAddModSCG_r12_drb_Type_r12 <= USED 0
PerDec_EuDelayBudgetReport_r14 <= USED 0
PerDec_EuEnable256QAM_r14 <= USED 0
PerDec_EuEnable256QAM_r14_setup <= USED 0
PerDec_EuHandoverFromEUTRAPreparationRequest_c1 <= USED 0
PerDec_EuHandoverFromEUTRAPreparationRequest_criticalExtensions <= USED 0
PerDec_EuIDC_SubframePattern_r11 <= USED 0
PerDec_EuIDC_SubframePattern_r11_subframePatternTDD_r11 <= USED 0
PerDec_EuIP_Address_r13 <= USED 0
PerDec_EuInitialUE_Identity <= USED 0
PerDec_EuInitialUE_Identity_5GC <= USED 0
PerDec_EuLoggedMeasurementConfiguration_r10_c1 <= USED 0
PerDec_EuLoggedMeasurementConfiguration_r10_criticalExtensions <= USED 0
PerDec_EuMeasConfig_heightThreshRef_r15 <= USED 0
PerDec_EuOtherConfig_r9_delayBudgetReportingConfig_r14 <= USED 0
PerDec_EuOtherConfig_r9_measConfigAppLayer_r15 <= USED 0
PerDec_EuOtherConfig_r9_overheatingAssistanceConfig_r14 <= USED 0
PerDec_EuOtherConfig_r9_rlm_ReportConfig_r14 <= USED 0
PerDec_EuPLMN_IdentityInfo2_r12 <= USED 0
PerDec_EuPLMN_IdentityInfo_r15_plmn_Identity_5GC_r15 <= USED 0
PerDec_EuPMCH_Config_r12_dataMCS_r12 <= USED 0
PerDec_EuPUCCH_ConfigDedicated_v13c0_n1PUCCH_AN_CS_v13c0 <= USED 0
PerDec_EuPUSCH_ConfigDedicatedScell_v1530_uci_OnPUSCH_r15 <= USED 0
PerDec_EuPUSCH_ConfigDedicated_v1530_ce_PUSCH_FlexibleStartPRB_AllocConfig_r15 <= USED 0
PerDec_EuPUSCH_ConfigDedicated_v1530_ce_PUSCH_SubPRB_Config_r15 <= USED 0
PerDec_EuPUSCH_EnhancementsConfig_r14 <= USED 0
PerDec_EuPUSCH_EnhancementsConfig_r14_interval_ULHoppingPUSCH_Enh_r14 <= USED 0
PerDec_EuPhysicalConfigDedicatedSCell_v13c0_pucch_SCell_v13c0 <= USED 0
PerDec_EuPowerPrefIndicationConfig_r11 <= USED 0
PerDec_EuRACH_Skip_r14_targetTA_r14 <= USED 0
PerDec_EuRLF_TimersAndConstantsSCG_r12 <= USED 0
PerDec_EuRN_SubframeConfig_r10_demodulationRS_r10 <= USED 0
PerDec_EuRN_SubframeConfig_r10_pucch_Config_r10 <= USED 0
PerDec_EuRN_SubframeConfig_r10_pucch_Config_r10_tdd <= USED 0
PerDec_EuRN_SubframeConfig_r10_resourceBlockAssignment_r10 <= USED 0
PerDec_EuRN_SubframeConfig_r10_resourceBlockAssignment_r10_type01_r10 <= USED 0
PerDec_EuRN_SubframeConfig_r10_resourceBlockAssignment_r10_type2_r10 <= USED 0
PerDec_EuRN_SubframeConfig_r10_subframeConfigPattern_r10 <= USED 0
PerDec_EuSCG_Configuration_r12 <= USED 0
PerDec_EuSCG_Configuration_v12f0 <= USED 0
PerDec_EuSCG_Configuration_v13c0 <= USED 0
PerDec_EuSI_OrPSI_GERAN <= USED 0
PerDec_EuSSB_ToMeasure_r15 <= USED 0
PerDec_EuS_NSSAI_r15 <= USED 0
PerDec_EuSecurityConfigHO_v1530_handoverType_v1530 <= USED 0
PerDec_EuSystemInformationBlockType1_v1530_IEs_crs_IntfMitigConfig_r15 <= USED 0
PerDec_EuTDD_PUSCH_UpPTS_r14 <= USED 0
PerDec_EuTMGI_r9_plmn_Id_r9 <= USED 0
PerDec_EuUAC_BarringPerPLMN_r15_uac_AC_BarringListType_r15 <= USED 0
PerDec_EuUEInformationRequest_r9_c1 <= USED 0
PerDec_EuUEInformationRequest_r9_criticalExtensions <= USED 0
;FILE lterrc_dece_r1551.o
PerDec_EuAUL_Config_r15_aul_RetransmissionTimer_r15 <= USED 0
PerDec_EuAUL_Config_r15_aul_StartingPartialBW_InsideMCOT_r15 <= USED 0
PerDec_EuAUL_Config_r15_aul_StartingPartialBW_OutsideMCOT_r15 <= USED 0
PerDec_EuAUL_Config_r15_contentionWindowSizeTimer_r15 <= USED 0
PerDec_EuAUL_Config_r15_transmissionModeUL_AUL_r15 <= USED 0
PerDec_EuAccessStratumRelease <= USED 0
PerDec_EuAntennaInfoDedicatedSTTI_r15_maxLayersMIMO_STTI_r15 <= USED 0
PerDec_EuAntennaInfoDedicatedSTTI_r15_transmissionModeDL_MBSFN_r15 <= USED 0
PerDec_EuAntennaInfoDedicatedSTTI_r15_transmissionModeDL_nonMBSFN_r15 <= USED 0
PerDec_EuAntennaInfoDedicated_v1430_ce_UE_TxAntennaSelection_config_r14 <= USED 0
PerDec_EuAntennaInfoDedicated_v1530_ue_TxAntennaSelection_SRS_2T4R_NrOfPairs_r15 <= USED 0
PerDec_EuAntennaInfoUL_STTI_r15_transmissionModeUL_STTI_r15 <= USED 0
PerDec_EuCA_BandwidthClass_r10 <= USED 0
PerDec_EuCQI_ReportConfigSCell_r15_altCQI_Table_1024QAM_r15 <= USED 0
PerDec_EuCQI_ReportConfig_r15_altCQI_Table_1024QAM_r15 <= USED 0
PerDec_EuCQI_ReportConfig_v1530_altCQI_Table_1024QAM_r15 <= USED 0
PerDec_EuCQI_ReportPeriodicSCell_r15_subbandCQI_r15_periodicityFactor_r15 <= USED 0
PerDec_EuCQI_ReportPeriodicSCell_r15_widebandCQI_r15_csi_ReportMode_r15 <= USED 0
PerDec_EuCQI_ShortConfigSCell_r15_subbandCQI_Short_r15_periodicityFactor_r15 <= USED 0
PerDec_EuCQI_ShortConfigSCell_r15_widebandCQI_Short_r15_csi_ReportModeShort_r15 <= USED 0
PerDec_EuCRS_AssistanceInfo_r15_crs_IntfMitigEnabled_15 <= USED 0
PerDec_EuCSI_RS_ConfigBeamformed_r14_alternativeCodebookEnabledBeamformed_r14 <= USED 0
PerDec_EuCSI_RS_ConfigBeamformed_r14_channelMeasRestriction_r14 <= USED 0
PerDec_EuCSI_RS_ConfigNZP_Activation_r14_csi_RS_NZP_mode_r14 <= USED 0
PerDec_EuCSI_RS_ConfigNZP_EMIMO_v1430_cdmType_v1430 <= USED 0
PerDec_EuCSI_RS_ConfigNonPrecoded_v1430_codebookConfigN1_v1430 <= USED 0
PerDec_EuCSI_RS_ConfigNonPrecoded_v1430_codebookConfigN2_v1430 <= USED 0
PerDec_EuCSI_RS_ConfigNonPrecoded_v1480_codebookConfigN1_v1480 <= USED 0
PerDec_EuCSI_RS_ConfigNonPrecoded_v1480_codebookConfigN2_r1480 <= USED 0
PerDec_EuCellReselectionInfoCommon_v1460_s_SearchDeltaP_r14 <= USED 0
PerDec_EuCellSelectionInfoCE_v1530_powerClass14dBm_Offset_r15 <= USED 0
PerDec_EuCellSelectionInfoNFreq_r13_q_Hyst_r13 <= USED 0
PerDec_EuDRB_ToAddMod_lwa_WLAN_AC_r14 <= USED 0
PerDec_EuDRX_Config_r15_drx_RetransmissionTimerShortTTI_r15 <= USED 0
PerDec_EuDRX_Config_r15_drx_UL_RetransmissionTimerShortTTI_r15 <= USED 0
PerDec_EuDataInactivityTimer_r14 <= USED 0
PerDec_EuDelayBudgetReport_r14_type1 <= USED 0
PerDec_EuDelayBudgetReport_r14_type2 <= USED 0
PerDec_EuDeltaFList_SPUCCH_r15_deltaF_slotSPUCCH_Format1_r15 <= USED 0
PerDec_EuDeltaFList_SPUCCH_r15_deltaF_slotSPUCCH_Format1a_r15 <= USED 0
PerDec_EuDeltaFList_SPUCCH_r15_deltaF_slotSPUCCH_Format1b_r15 <= USED 0
PerDec_EuDeltaFList_SPUCCH_r15_deltaF_slotSPUCCH_Format3_r15 <= USED 0
PerDec_EuDeltaFList_SPUCCH_r15_deltaF_slotSPUCCH_RM_Format4_r15 <= USED 0
PerDec_EuDeltaFList_SPUCCH_r15_deltaF_slotSPUCCH_TBCC_Format4_r15 <= USED 0
PerDec_EuDeltaFList_SPUCCH_r15_deltaF_subslotSPUCCH_Format1and1a_r15 <= USED 0
PerDec_EuDeltaFList_SPUCCH_r15_deltaF_subslotSPUCCH_Format1b_r15 <= USED 0
PerDec_EuDeltaFList_SPUCCH_r15_deltaF_subslotSPUCCH_RM_Format4_r15 <= USED 0
PerDec_EuDeltaFList_SPUCCH_r15_deltaF_subslotSPUCCH_TBCC_Format4_r15 <= USED 0
PerDec_EuDeltaTxD_OffsetListSPUCCH_r15_deltaTxD_OffsetSPUCCH_Format1_r15 <= USED 0
PerDec_EuDeltaTxD_OffsetListSPUCCH_r15_deltaTxD_OffsetSPUCCH_Format1a_r15 <= USED 0
PerDec_EuDeltaTxD_OffsetListSPUCCH_r15_deltaTxD_OffsetSPUCCH_Format1b_r15 <= USED 0
PerDec_EuDeltaTxD_OffsetListSPUCCH_r15_deltaTxD_OffsetSPUCCH_Format3_r15 <= USED 0
PerDec_EuEDT_PRACH_ParametersCE_r15_prach_StartingSubframe_r15 <= USED 0
PerDec_EuEstablishmentCause <= USED 0
PerDec_EuFlightPathInfoReportConfig_r15_includeTimeStamp_r15 <= USED 0
PerDec_EuGNSS_ID_r15_gnss_id_r15 <= USED 0
PerDec_EuHandoverFromEUTRAPreparationRequest_v1020_IEs_dualRxTxRedirectIndicator_r10 <= USED 0
PerDec_EuHandover_targetRAT_Type <= USED 0
PerDec_EuIDC_Config_r11_autonomousDenialSubframes_r11 <= USED 0
PerDec_EuIDC_Config_r11_autonomousDenialValidity_r11 <= USED 0
PerDec_EuIDC_Config_r11_idc_HardwareSharingIndication_r13 <= USED 0
PerDec_EuIDC_Config_r11_idc_Indication_UL_CA_r11 <= USED 0
PerDec_EuIDC_Config_r11_idc_Indication_r11 <= USED 0
PerDec_EuLAA_SCellConfiguration_v1430_absenceOfAnyOtherTechnology_r14 <= USED 0
PerDec_EuLoggingDuration_r10 <= USED 0
PerDec_EuLoggingInterval_r10 <= USED 0
PerDec_EuLogicalChannelConfig_bitRateQueryProhibitTimer_r14 <= USED 0
PerDec_EuLogicalChannelConfig_logicalChannelSR_Restriction_r15_setup <= USED 0
PerDec_EuMAC_MainConfig_rai_Activation_r14 <= USED 0
PerDec_EuMAC_MainConfig_setup_dormantSCellDeactivationTimer_r15 <= USED 0
PerDec_EuMAC_MainConfig_setup_periodicBSR_Timer_r15 <= USED 0
PerDec_EuMAC_MainConfig_setup_proc_Timeline_r15 <= USED 0
PerDec_EuMAC_MainConfig_setup_sCellHibernationTimer_r15 <= USED 0
PerDec_EuMAC_MainConfig_setup_skipUplinkTxDynamic_r14 <= USED 0
PerDec_EuMAC_MainConfig_setup_skipUplinkTxSPS_r14 <= USED 0
PerDec_EuMBMS_CarrierType_r14_carrierType_r14 <= USED 0
PerDec_EuMeasGapSharingConfig_r14_measGapSharingScheme_r14 <= USED 0
PerDec_EuMeasIdleCarrierEUTRA_r15_reportQuantities <= USED 0
PerDec_EuMeasIdleConfigDedicated_r15_measIdleDuration_r15 <= USED 0
PerDec_EuMeasSensing_Config_r15_sensingPeriodicity_r15 <= USED 0
PerDec_EuMobilityControlInfoSCG_r12_makeBeforeBreakSCG_r14 <= USED 0
PerDec_EuMobilityControlInfoSCG_r12_t307_r12 <= USED 0
PerDec_EuMobilityControlInfo_handoverWithoutWT_Change_r14 <= USED 0
PerDec_EuMobilityControlInfo_makeBeforeBreak_r14 <= USED 0
PerDec_EuMobilityControlInfo_sameSFN_Indication_r14 <= USED 0
PerDec_EuNZP_FrequencyDensity_r14 <= USED 0
PerDec_EuObtainLocationConfig_r11_obtainLocation_r11 <= USED 0
PerDec_EuOtherConfig_r9_bw_PreferenceIndicationTimer_r14 <= USED 0
PerDec_EuOtherConfig_r9_setup_delayBudgetReportingProhibitTimer_r14 <= USED 0
PerDec_EuOtherConfig_r9_setup_overheatingIndicationProhibitTimer_r14 <= USED 0
PerDec_EuOtherConfig_r9_setup_rlmReportRep_MPDCCH_r14 <= USED 0
PerDec_EuOtherConfig_r9_setup_rlmReportTimer_r14 <= USED 0
PerDec_EuOtherConfig_r9_setup_serviceType <= USED 0
PerDec_EuPDCCH_CandidateReductionValue_r14 <= USED 0
PerDec_EuPDCCH_ConfigLAA_r14_maxNumberOfSchedSubframes_Format0B_r14 <= USED 0
PerDec_EuPDCCH_ConfigLAA_r14_maxNumberOfSchedSubframes_Format4B_r14 <= USED 0
PerDec_EuPDCCH_ConfigLAA_r14_skipMonitoringDCI_Format0A_r14 <= USED 0
PerDec_EuPDCCH_ConfigLAA_r14_skipMonitoringDCI_Format4A_r14 <= USED 0
PerDec_EuPDCP_Config_bufferSize_r15 <= USED 0
PerDec_EuPDCP_Config_dictionary_r15 <= USED 0
PerDec_EuPDCP_Config_setup_pdcp_Duplication_r15 <= USED 0
PerDec_EuPDCP_Config_setup_ul_LWA_DataSplitThreshold_r14 <= USED 0
PerDec_EuPDSCH_ConfigDedicatedSCell_v1430_tbsIndexAlt2_r14 <= USED 0
PerDec_EuPDSCH_ConfigDedicated_v1430_ce_HARQ_AckBundling_r14 <= USED 0
PerDec_EuPDSCH_ConfigDedicated_v1430_ce_PDSCH_MaxBandwidth_r14 <= USED 0
PerDec_EuPDSCH_ConfigDedicated_v1430_ce_PDSCH_TenProcesses_r14 <= USED 0
PerDec_EuPDSCH_ConfigDedicated_v1430_ce_SchedulingEnhancement_r14 <= USED 0
PerDec_EuPDSCH_ConfigDedicated_v1430_tbsIndexAlt2_r14 <= USED 0
PerDec_EuPDSCH_ConfigDedicated_v1530_altMCS_TableScalingConfig_r15 <= USED 0
PerDec_EuPDSCH_ConfigDedicated_v1530_ce_CQI_AlternativeTableConfig_r15 <= USED 0
PerDec_EuPDSCH_ConfigDedicated_v1530_ce_PDSCH_64QAM_Config_r15 <= USED 0
PerDec_EuPDSCH_ConfigDedicated_v1530_ce_PDSCH_FlexibleStartPRB_AllocConfig_r15 <= USED 0
PerDec_EuPDSCH_ConfigDedicated_v1530_qcl_Operation_v1530 <= USED 0
PerDec_EuPDSCH_ConfigDedicated_v1530_tbs_IndexAlt3_r15 <= USED 0
PerDec_EuPDSCH_RE_MappingQCL_Config_r11_setup_crs_PortsCount_v1530 <= USED 0
PerDec_EuPDSCH_RE_MappingQCL_Config_r11_setup_pdsch_Start_v1530 <= USED 0
PerDec_EuPLMN_IdentityInfo_r15_cellReservedForOperatorUse_CRS_r15 <= USED 0
PerDec_EuPLMN_IdentityInfo_r15_cellReservedForOperatorUse_r15 <= USED 0
PerDec_EuPLMN_IdentityInfo_v1530_cellReservedForOperatorUse_CRS_r15 <= USED 0
PerDec_EuPLMN_Info_r15_upperLayerIndication_r15 <= USED 0
PerDec_EuPMCH_Config_r9_mch_SchedulingPeriod_r9 <= USED 0
PerDec_EuPUCCH_ConfigCommon_v1430_pucch_NumRepetitionCE_Msg4_Level3_r14 <= USED 0
PerDec_EuPUCCH_ConfigDedicated_v1430_pucch_NumRepetitionCE_format1_r14 <= USED 0
PerDec_EuPUCCH_ConfigDedicated_v1530_codebooksizeDeterminationSTTI_r15 <= USED 0
PerDec_EuPUSCH_ConfigDedicated_v1430_ce_PUSCH_MaxBandwidth_r14 <= USED 0
PerDec_EuPUSCH_ConfigDedicated_v1430_ce_PUSCH_NB_MaxTBS_r14 <= USED 0
PerDec_EuPUSCH_EnhancementsConfig_r14_interval_ULHoppingPUSCH_Enh_r14_interval_FDD_PUSCH_Enh_r14 <= USED 0
PerDec_EuPUSCH_EnhancementsConfig_r14_interval_ULHoppingPUSCH_Enh_r14_interval_TDD_PUSCH_Enh_r14 <= USED 0
PerDec_EuPerCC_GapIndication_r14_gapIndication_r14 <= USED 0
PerDec_EuPhysicalConfigDedicatedSCell_r10_setup_k_max_r14 <= USED 0
PerDec_EuPhysicalConfigDedicatedSCell_r10_setup_maxNumber_SlotSubslotPDSCH_Repetitions_r15 <= USED 0
PerDec_EuPhysicalConfigDedicatedSCell_r10_setup_maxNumber_SubframePDSCH_Repetitions_r15 <= USED 0
PerDec_EuPhysicalConfigDedicatedSCell_r10_setup_mcs_restrictionSlotSubslotPDSCH_Repetitions_r15 <= USED 0
PerDec_EuPhysicalConfigDedicatedSCell_r10_setup_mcs_restrictionSubframePDSCH_Repetitions_r15 <= USED 0
PerDec_EuPhysicalConfigDedicatedSCell_r10_setup_p_a_must_r14 <= USED 0
PerDec_EuPhysicalConfigDedicatedSCell_r10_setup_rv_SlotsublotPDSCH_Repetitions_r15 <= USED 0
PerDec_EuPhysicalConfigDedicatedSCell_r10_setup_rv_SubframePDSCH_Repetitions_r15 <= USED 0
PerDec_EuPhysicalConfigDedicated_ce_pdsch_pusch_EnhancementConfig_r14 <= USED 0
PerDec_EuPhysicalConfigDedicated_setup_k_max_r14 <= USED 0
PerDec_EuPhysicalConfigDedicated_setup_maxNumber_SlotSubslotPDSCH_Repetitions_r15 <= USED 0
PerDec_EuPhysicalConfigDedicated_setup_maxNumber_SubframePDSCH_Repetitions_r15 <= USED 0
PerDec_EuPhysicalConfigDedicated_setup_mcs_restrictionSlotSubslotPDSCH_Repetitions_r15 <= USED 0
PerDec_EuPhysicalConfigDedicated_setup_mcs_restrictionSubframePDSCH_Repetitions_r15 <= USED 0
PerDec_EuPhysicalConfigDedicated_setup_p_a_must_r14 <= USED 0
PerDec_EuPhysicalConfigDedicated_setup_rv_SlotsublotPDSCH_Repetitions_r15 <= USED 0
PerDec_EuPhysicalConfigDedicated_setup_rv_SubframePDSCH_Repetitions_r15 <= USED 0
PerDec_EuPollByte_r14 <= USED 0
PerDec_EuPollPDU_r15 <= USED 0
PerDec_EuPosSIB_Type_r15_encrypted_r15 <= USED 0
PerDec_EuPosSIB_Type_r15_posSibType_r15 <= USED 0
PerDec_EuPosSchedulingInfo_r15_posSI_Periodicity_r15 <= USED 0
PerDec_EuPowerPrefIndicationConfig_r11_powerPrefIndicationTimer_r11 <= USED 0
PerDec_EuProcessingTimelineSet_r15 <= USED 0
PerDec_EuRACH_CE_LevelInfo_r13_edt_TBS_r15 <= USED 0
PerDec_EuRACH_CE_LevelInfo_r13_mac_ContentionResolutionTimer_r15 <= USED 0
PerDec_EuRACH_ConfigCommon_edt_SmallTBS_Subset_r15 <= USED 0
PerDec_EuRACH_Skip_r14_ul_SchedInterval_r14 <= USED 0
PerDec_EuRF_Parameters_v12b0_maxLayersMIMO_Indication_r12 <= USED 0
PerDec_EuRLC_Config_r15_reestablishRLC_r15 <= USED 0
PerDec_EuRLC_Config_r15_rlc_OutOfOrderDelivery_r15 <= USED 0
PerDec_EuRLC_Config_v1510_reestablishRLC_r15 <= USED 0
PerDec_EuRLC_Config_v1530_rlc_OutOfOrderDelivery_r15 <= USED 0
PerDec_EuRLF_TimersAndConstantsSCG_r12_n313_r12 <= USED 0
PerDec_EuRLF_TimersAndConstantsSCG_r12_n314_r12 <= USED 0
PerDec_EuRLF_TimersAndConstantsSCG_r12_t313_r12 <= USED 0
PerDec_EuRN_SubframeConfig_r10_demodulationRS_r10_interleaving_r10 <= USED 0
PerDec_EuRN_SubframeConfig_r10_demodulationRS_r10_noInterleaving_r10 <= USED 0
PerDec_EuRN_SubframeConfig_r10_resourceAllocationType_r10 <= USED 0
PerDec_EuRRCConnectionReconfiguration_v1250_IEs_setup_t350_r12 <= USED 0
PerDec_EuRRCConnectionReconfiguration_v1430_IEs_perCC_GapIndicationRequest_r14 <= USED 0
PerDec_EuRRCConnectionRelease_v1530_IEs_cn_Type_r15 <= USED 0
PerDec_EuRRCConnectionRelease_v1530_IEs_drb_ContinueROHC_r15 <= USED 0
PerDec_EuRRC_InactiveConfig_r15_periodic_RNAU_timer_r15 <= USED 0
PerDec_EuRRC_InactiveConfig_r15_ran_PagingCycle_r15 <= USED 0
PerDec_EuRSS_Config_r15_duration_r15 <= USED 0
PerDec_EuRSS_Config_r15_periodicity_r15 <= USED 0
PerDec_EuRSS_Config_r15_powerBoost_r15 <= USED 0
PerDec_EuRadioResourceConfigCommonSCell_r10_harq_ReferenceConfig_r14 <= USED 0
PerDec_EuRadioResourceConfigCommonSCell_r10_soundingRS_FlexibleTiming_r14 <= USED 0
PerDec_EuRadioResourceConfigCommonSCell_r10_ul_FreqInfo_r14_ul_Bandwidth_r14 <= USED 0
PerDec_EuReportConfigEUTRA_purpose_r15 <= USED 0
PerDec_EuReportConfigEUTRA_purpose_v1430 <= USED 0
PerDec_EuReportConfigInterRAT_reportSFTD_Meas_r15 <= USED 0
PerDec_EuReportQuantityWLAN_r13_availableAdmissionCapacityRequestWLAN_r13 <= USED 0
PerDec_EuReportQuantityWLAN_r13_backhaulDL_BandwidthRequestWLAN_r13 <= USED 0
PerDec_EuReportQuantityWLAN_r13_backhaulUL_BandwidthRequestWLAN_r13 <= USED 0
PerDec_EuReportQuantityWLAN_r13_bandRequestWLAN_r13 <= USED 0
PerDec_EuReportQuantityWLAN_r13_carrierInfoRequestWLAN_r13 <= USED 0
PerDec_EuReportQuantityWLAN_r13_channelUtilizationRequestWLAN_r13 <= USED 0
PerDec_EuReportQuantityWLAN_r13_stationCountRequestWLAN_r13 <= USED 0
PerDec_EuReselectionInfoRelay_r13_minHyst_r13 <= USED 0
PerDec_EuSBAS_ID_r15_sbas_id_r15 <= USED 0
PerDec_EuSL_DiscConfigOtherInterFreq_r13_refCarrierCommon_r13 <= USED 0
PerDec_EuSL_DiscConfigRelayUE_r13_hystMax_r13 <= USED 0
PerDec_EuSL_DiscConfigRelayUE_r13_hystMin_r13 <= USED 0
PerDec_EuSL_DiscConfigRemoteUE_r13_hystMax_r13 <= USED 0
PerDec_EuSN_FieldLength_r15 <= USED 0
PerDec_EuSPDCCH_Config_r15_spdcch_L1_ReuseIndication_r15 <= USED 0
PerDec_EuSPDCCH_Elements_r15_rateMatchingMode_r15 <= USED 0
PerDec_EuSPDCCH_Elements_r15_spdcch_SetReferenceSig_r15 <= USED 0
PerDec_EuSPDCCH_Elements_r15_subframeType_r15 <= USED 0
PerDec_EuSPDCCH_Elements_r15_transmissionType_r15 <= USED 0
PerDec_EuSchedulingRequestConfig_v1530_dssr_TransMax_r15 <= USED 0
PerDec_EuShortTTI_Length_r15 <= USED 0
PerDec_EuSlotOrSubslotPDSCH_Config_r15_altCQI_Table1024QAM_STTI_r15 <= USED 0
PerDec_EuSlotOrSubslotPDSCH_Config_r15_altCQI_TableSTTI_r15 <= USED 0
PerDec_EuSlotOrSubslotPDSCH_Config_r15_resourceAllocation_r15 <= USED 0
PerDec_EuSlotOrSubslotPDSCH_Config_r15_tbsIndexAlt2_STTI_r15 <= USED 0
PerDec_EuSlotOrSubslotPDSCH_Config_r15_tbsIndexAlt3_STTI_r15 <= USED 0
PerDec_EuSlotOrSubslotPDSCH_Config_r15_tbsIndexAlt_STTI_r15 <= USED 0
PerDec_EuSubframeAssignment_r15 <= USED 0
PerDec_EuSupportedBandEUTRA_v1250_dl_256QAM_r12 <= USED 0
PerDec_EuSupportedBandEUTRA_v1250_ul_64QAM_r12 <= USED 0
PerDec_EuSupportedBandEUTRA_v1310_ue_PowerClass_5_r13 <= USED 0
PerDec_EuSupportedBandEUTRA_v1320_intraFreq_CE_NeedForGaps_r13 <= USED 0
PerDec_EuSupportedBandEUTRA_v1320_ue_PowerClass_N_r13 <= USED 0
PerDec_EuSystemInformationBlockType1_v1430_IEs_eCallOverIMS_Support_r14 <= USED 0
PerDec_EuSystemInformationBlockType1_v1530_IEs_cellBarred_CRS_r15 <= USED 0
PerDec_EuSystemInformationBlockType1_v1530_IEs_crs_IntfMitigNumPRBs_r15 <= USED 0
PerDec_EuSystemInformationBlockType1_v1530_IEs_hsdn_Cell_r15 <= USED 0
PerDec_EuSystemInformationBlockType1_v1540_IEs_si_posOffset_r15 <= USED 0
PerDec_EuSystemInformationBlockType26_r15_slss_TxMultiFreq_r15 <= USED 0
PerDec_EuSystemInformationBlockType2_cp_EDT_r15 <= USED 0
PerDec_EuSystemInformationBlockType2_idleModeMeasurements_r15 <= USED 0
PerDec_EuSystemInformationBlockType2_reducedCP_LatencyEnabled_r15 <= USED 0
PerDec_EuSystemInformationBlockType2_up_EDT_r15 <= USED 0
PerDec_EuSystemInformationBlockType2_videoServiceCauseIndication_r14 <= USED 0
PerDec_EuSystemInformationBlockType3_crs_IntfMitigNeighCellsCE_r15 <= USED 0
PerDec_EuTDD_ConfigSL_r12_subframeAssignmentSL_r12 <= USED 0
PerDec_EuTDD_Config_v1430_specialSubframePatterns_v1430 <= USED 0
PerDec_EuTDD_Config_v1450_specialSubframePatterns_v1450 <= USED 0
PerDec_EuTDD_PUSCH_UpPTS_r14_dmrs_LessUpPTS_Config_r14 <= USED 0
PerDec_EuTDD_PUSCH_UpPTS_r14_symPUSCH_UpPTS_r14 <= USED 0
PerDec_EuTimeReferenceInfo_r15_timeInfoType_r15 <= USED 0
PerDec_EuUAC_AC1_SelectAssistInfo_r15 <= USED 0
PerDec_EuUAC_BarringInfoSet_r15_uac_BarringFactor_r15 <= USED 0
PerDec_EuUAC_BarringInfoSet_r15_uac_BarringTime_r15 <= USED 0
PerDec_EuUECapabilityEnquiry_v1530_IEs_requestSTTI_SPT_Capability_r15 <= USED 0
PerDec_EuUEInformationRequest_v1020_IEs_logMeasReportReq_r10 <= USED 0
PerDec_EuUEInformationRequest_v1130_IEs_connEstFailReportReq_r11 <= USED 0
PerDec_EuUEInformationRequest_v1250_IEs_mobilityHistoryReportReq_r12 <= USED 0
PerDec_EuUEInformationRequest_v1530_IEs_idleModeMeasurementReq_r15 <= USED 0
PerDec_EuUE_TimersAndConstants_t300_r15 <= USED 0
PerDec_EuUL_AM_RLC_r15_maxRetxThreshold_r15 <= USED 0
PerDec_EuUL_PDCP_DelayResult_r13_qci_Id_r13 <= USED 0
PerDec_EuUplinkPowerControlCommonPSCell_r12_deltaF_PUCCH_Format1bCS_r12 <= USED 0
PerDec_EuUplinkPowerControlCommonPSCell_r12_deltaF_PUCCH_Format3_r12 <= USED 0
PerDec_EuWLAN_BandIndicator_r13 <= USED 0
PerDec_EuWLAN_CarrierInfo_r13_countryCode_r13 <= USED 0
PerDec_EuWLAN_RTT_r15_rttUnits_r15 <= USED 0
PerDec_EuWLAN_Status_r13 <= USED 0
PerDec_EuWLAN_Status_v1430 <= USED 0
PerDec_EuWLAN_backhaulRate_r12 <= USED 0
PerDec_EuWUS_Config_r15_freqLocation_r15 <= USED 0
PerDec_EuWUS_Config_r15_maxDurationFactor_r15 <= USED 0
PerDec_EuWUS_Config_r15_numPOs_r15 <= USED 0
PerDec_EuWUS_Config_r15_timeOffsetDRX_r15 <= USED 0
PerDec_EuWUS_Config_r15_timeOffset_eDRX_Long_r15 <= USED 0
PerDec_EuWUS_Config_r15_timeOffset_eDRX_Short_r15 <= USED 0
;FILE lterrc_deco_r1551.o
PerDec_EuBT_Name_r15_str <= USED 0
PerDec_EuCellInfoUTRA_TDD_r10_utra_BCCH_Container_r10_str <= USED 0
PerDec_EuCellInfoUTRA_TDD_r9_utra_BCCH_Container_r9_str <= USED 0
PerDec_EuHandoverFromEUTRAPreparationRequest_v890_IEs_lateNonCriticalExtension_str <= USED 0
PerDec_EuHandover_nas_SecurityParamFromEUTRA_str <= USED 0
PerDec_EuHandover_targetRAT_MessageContainer_str <= USED 0
PerDec_EuIKE_Identity_r13_idI_r13_str <= USED 0
PerDec_EuLWA_Config_r13_wt_MAC_Address_r14_str <= USED 0
PerDec_EuLoggedMeasurementConfiguration_r10_IEs_tce_Id_r10_str <= USED 0
PerDec_EuLoggedMeasurementConfiguration_r10_IEs_traceRecordingSessionRef_r10_str <= USED 0
PerDec_EuLoggedMeasurementConfiguration_v1080_IEs_lateNonCriticalExtension_r10_str <= USED 0
PerDec_EuMBSFNAreaConfiguration_v930_IEs_lateNonCriticalExtension_str <= USED 0
PerDec_EuMobilityFromEUTRACommand_v8a0_IEs_lateNonCriticalExtension_str <= USED 0
PerDec_EuMobilityFromEUTRACommand_v930_IEs_lateNonCriticalExtension_str <= USED 0
PerDec_EuMobilityParametersCDMA2000_str <= USED 0
PerDec_EuOtherConfig_r9_setup_measConfigAppLayerContainer_r15_str <= USED 0
PerDec_EuPosSystemInformation_r15_IEs_lateNonCriticalExtension_str <= USED 0
PerDec_EuRRCConnectionReconfiguration_v10l0_IEs_lateNonCriticalExtension_str <= USED 0
PerDec_EuRRCConnectionReconfiguration_v12f0_IEs_lateNonCriticalExtension_str <= USED 0
PerDec_EuRRCConnectionReconfiguration_v1430_IEs_systemInformationBlockType2Dedicated_r14_str <= USED 0
PerDec_EuRRCConnectionReconfiguration_v8m0_IEs_lateNonCriticalExtension_str <= USED 0
PerDec_EuSCPTMConfiguration_BR_r14_lateNonCriticalExtension_str <= USED 0
PerDec_EuSCPTMConfiguration_r13_lateNonCriticalExtension_str <= USED 0
PerDec_EuSecurityConfigHO_v1530_epc_To5GC_r15_nas_Container_r15_str <= USED 0
PerDec_EuSecurityConfigHO_v1530_intra5GC_r15_nas_Container_r15_str <= USED 0
PerDec_EuSystemInformationBlockPos_r15_assistanceDataSIB_Element_r15_str <= USED 0
PerDec_EuSystemInformationBlockPos_r15_lateNonCriticalExtension_str <= USED 0
PerDec_EuSystemInformationBlockType12_r9_warningAreaCoordinatesSegment_r15_str <= USED 0
PerDec_EuSystemInformationBlockType2_v10n0_IEs_lateNonCriticalExtension_str <= USED 0
PerDec_EuTMGI_r9_serviceId_r9_str <= USED 0
PerDec_EuTraceReference_r10_traceId_r10_str <= USED 0
PerDec_EuUEInformationRequest_v930_IEs_lateNonCriticalExtension_str <= USED 0
PerDec_EuWLAN_Identifiers_r12_bssid_r12_str <= USED 0
PerDec_EuWLAN_Identifiers_r12_hessid_r12_str <= USED 0
PerDec_EuWLAN_Identifiers_r12_ssid_r12_str <= USED 0
PerDec_EuWLAN_Name_r15_str <= USED 0
;FILE lterrc_decs_r1551.o
PerDec_EuAUL_Config_r15_setup <= USED 0
PerDec_EuAntennaInfoDedicatedSTTI_r15_setup <= USED 0
PerDec_EuAntennaInfoDedicated_v1430 <= USED 0
PerDec_EuAntennaInfoUL_STTI_r15 <= USED 0
PerDec_EuAreaConfiguration_v1130 <= USED 0
PerDec_EuBCCH_DL_SCH_Message_BR <= USED 0
PerDec_EuBandCombinationListEUTRA_r10 <= USED 0
PerDec_EuBandCombinationList_r14 <= USED 0
PerDec_EuBandCombination_r14 <= USED 0
PerDec_EuBandIndication_r14 <= USED 0
PerDec_EuBandInfoEUTRA <= USED 0
PerDec_EuCQI_ReportAperiodicHybrid_r14 <= USED 0
PerDec_EuCQI_ReportAperiodicHybrid_r14_oneBit_r14 <= USED 0
PerDec_EuCQI_ReportAperiodicHybrid_r14_threeBit_r14 <= USED 0
PerDec_EuCQI_ReportAperiodicHybrid_r14_twoBit_r14 <= USED 0
PerDec_EuCSI_RS_ConfigNZPToReleaseList_r15 <= USED 0
PerDec_EuCSI_RS_ConfigNZP_EMIMO_v1430 <= USED 0
PerDec_EuCSI_RS_ConfigNZP_EMIMO_v1430_nzp_resourceConfigListExt_r14 <= USED 0
PerDec_EuCSI_RS_ConfigZP_ApList_r14_setup <= USED 0
PerDec_EuCandidateServingFreqListNR_r15 <= USED 0
PerDec_EuCarrierFreqInfoUTRA_FDD_v8h0 <= USED 0
PerDec_EuCarrierFreqInfoUTRA_FDD_v8h0_multiBandInfoList <= USED 0
PerDec_EuCellGlobalIdEUTRA <= USED 0
PerDec_EuCellGlobalIdGERAN <= USED 0
PerDec_EuCellGlobalIdList_r10 <= USED 0
PerDec_EuCellGlobalIdUTRA <= USED 0
PerDec_EuCellInfoListUTRA_TDD_r10 <= USED 0
PerDec_EuCellInfoListUTRA_TDD_r9 <= USED 0
PerDec_EuCellInfoUTRA_TDD_r10 <= USED 0
PerDec_EuCellInfoUTRA_TDD_r9 <= USED 0
PerDec_EuCellSelectionInfoCE_v1530 <= USED 0
PerDec_EuCellSelectionInfoNFreq_r13 <= USED 0
PerDec_EuCellsToAddModListUTRA_TDD <= USED 0
PerDec_EuCellsToAddModUTRA_TDD <= USED 0
PerDec_EuCountingRequestInfo_r10 <= USED 0
PerDec_EuCountingRequestList_r10 <= USED 0
PerDec_EuCrossCarrierSchedulingConfigLAA_UL_r14 <= USED 0
PerDec_EuDL_CCCH_Message <= USED 0
PerDec_EuDL_DCCH_Message <= USED 0
PerDec_EuDRB_CountInfo <= USED 0
PerDec_EuDRB_CountInfoList <= USED 0
PerDec_EuDRB_CountInfoListExt_r15 <= USED 0
PerDec_EuDRB_ToAddModListSCG_r12 <= USED 0
PerDec_EuDRB_ToAddModListSCG_r15 <= USED 0
PerDec_EuDRB_ToAddModList_r15 <= USED 0
PerDec_EuDRB_ToAddModSCG_r12 <= USED 0
PerDec_EuDRB_ToAddModSCG_r12_scg_r12 <= USED 0
PerDec_EuDeltaTxD_OffsetListSPUCCH_r15 <= USED 0
PerDec_EuEnable256QAM_r14_tpc_SubframeSet_Configured_r14 <= USED 0
PerDec_EuEnable256QAM_r14_tpc_SubframeSet_NotConfigured_r14 <= USED 0
PerDec_EuFreqPriorityListUTRA_TDD <= USED 0
PerDec_EuFreqPriorityUTRA_TDD <= USED 0
PerDec_EuHandover <= USED 0
PerDec_EuHandoverFromEUTRAPreparationRequest <= USED 0
PerDec_EuHandoverFromEUTRAPreparationRequest_r8_IEs <= USED 0
PerDec_EuHandoverFromEUTRAPreparationRequest_v1020_IEs <= USED 0
PerDec_EuHandoverFromEUTRAPreparationRequest_v890_IEs <= USED 0
PerDec_EuHandoverFromEUTRAPreparationRequest_v920_IEs <= USED 0
PerDec_EuIDC_Config_r11 <= USED 0
PerDec_EuIDC_Config_r11_autonomousDenialParameters_r11 <= USED 0
PerDec_EuIKE_Identity_r13 <= USED 0
PerDec_EuInterFreqBandInfo <= USED 0
PerDec_EuInterFreqBandList <= USED 0
PerDec_EuInterFreqCarrierFreqInfo_v1530 <= USED 0
PerDec_EuInterFreqCarrierFreqListExt_v1530 <= USED 0
PerDec_EuInterFreqCarrierFreqList_v1530 <= USED 0
PerDec_EuInterFreqNeighHSDN_CellList_r15 <= USED 0
PerDec_EuInterRAT_BandInfo <= USED 0
PerDec_EuInterRAT_BandList <= USED 0
PerDec_EuIntraFreqNeighHSDN_CellList_r15 <= USED 0
PerDec_EuLoggedMeasurementConfiguration_r10 <= USED 0
PerDec_EuLoggedMeasurementConfiguration_r10_IEs <= USED 0
PerDec_EuLoggedMeasurementConfiguration_v1080_IEs <= USED 0
PerDec_EuLoggedMeasurementConfiguration_v1130_IEs <= USED 0
PerDec_EuLoggedMeasurementConfiguration_v1250_IEs <= USED 0
PerDec_EuMobilityControlInfoSCG_r12 <= USED 0
PerDec_EuMobilityControlInfo_v10l0 <= USED 0
PerDec_EuObtainLocationConfig_r11 <= USED 0
PerDec_EuOtherConfig_r9_setup <= USED 0
PerDec_EuOtherConfig_r9_setup_1 <= USED 0
PerDec_EuOtherConfig_r9_setup_2 <= USED 0
PerDec_EuOtherConfig_r9_setup_3 <= USED 0
PerDec_EuPCI_ARFCN_r13 <= USED 0
PerDec_EuPDSCH_ConfigDedicatedSCell_v1430 <= USED 0
PerDec_EuPDSCH_ConfigDedicated_v1430 <= USED 0
PerDec_EuPDSCH_ConfigDedicated_v1530 <= USED 0
PerDec_EuPLMN_IdentityInfo_r15 <= USED 0
PerDec_EuPLMN_IdentityInfo_v1530 <= USED 0
PerDec_EuPLMN_IdentityList2 <= USED 0
PerDec_EuPLMN_IdentityList3_r11 <= USED 0
PerDec_EuPLMN_IdentityList4_r12 <= USED 0
PerDec_EuPLMN_IdentityList_r15 <= USED 0
PerDec_EuPLMN_IdentityList_v1530 <= USED 0
PerDec_EuPLMN_InfoList_r15 <= USED 0
PerDec_EuPLMN_Info_r15 <= USED 0
PerDec_EuPRACH_Config_v1430 <= USED 0
PerDec_EuPSCellToAddMod_r12 <= USED 0
PerDec_EuPSCellToAddMod_r12_cellIdentification_r12 <= USED 0
PerDec_EuPSCellToAddMod_v12f0 <= USED 0
PerDec_EuPSCellToAddMod_v1440 <= USED 0
PerDec_EuPUCCH_ConfigDedicated_v13c0 <= USED 0
PerDec_EuPUCCH_ConfigDedicated_v13c0_channelSelection_v13c0 <= USED 0
PerDec_EuPUCCH_ConfigDedicated_v13c0_n1PUCCH_AN_CS_v13c0_setup <= USED 0
PerDec_EuPUCCH_ConfigDedicated_v13c0_setup_n1PUCCH_AN_CS_ListP1_v13c0 <= USED 0
PerDec_EuPUCCH_ConfigDedicated_v1430 <= USED 0
PerDec_EuPUCCH_ConfigDedicated_v1530 <= USED 0
PerDec_EuPUSCH_ConfigDedicatedSCell_v1430 <= USED 0
PerDec_EuPUSCH_ConfigDedicatedScell_v1530 <= USED 0
PerDec_EuPUSCH_ConfigDedicatedScell_v1530_setup <= USED 0
PerDec_EuPUSCH_ConfigDedicated_v1430 <= USED 0
PerDec_EuPUSCH_ConfigDedicated_v1530 <= USED 0
PerDec_EuPUSCH_ConfigDedicated_v1530_setup <= USED 0
PerDec_EuPUSCH_ConfigDedicated_v1530_setup_1 <= USED 0
PerDec_EuPUSCH_EnhancementsConfig_r14_setup <= USED 0
PerDec_EuPhysicalConfigDedicatedSCell_v13c0 <= USED 0
PerDec_EuPhysicalConfigDedicatedSCell_v13c0_setup <= USED 0
PerDec_EuPhysicalConfigDedicated_v1370 <= USED 0
PerDec_EuPhysicalConfigDedicated_v13c0 <= USED 0
PerDec_EuPowerCoordinationInfo_r12 <= USED 0
PerDec_EuPowerPrefIndicationConfig_r11_setup <= USED 0
PerDec_EuRACH_Skip_r14 <= USED 0
PerDec_EuRACH_Skip_r14_ul_ConfigInfo_r14 <= USED 0
PerDec_EuRLF_TimersAndConstantsSCG_r12_setup <= USED 0
PerDec_EuRN_SubframeConfig_r10_channelSelectionMultiplexingBundling_n1PUCCH_AN_List_r10 <= USED 0
PerDec_EuRN_SubframeConfig_r10_pucch_Config_r10_fdd <= USED 0
PerDec_EuRN_SubframeConfig_r10_rpdcch_Config_r10 <= USED 0
PerDec_EuRN_SubframeConfig_r10_tdd_channelSelectionMultiplexingBundling <= USED 0
PerDec_EuRN_SubframeConfig_r10_tdd_fallbackForFormat3 <= USED 0
PerDec_EuROHC_ProfileSupportList <= USED 0
PerDec_EuRRCConnectionReconfiguration_v10i0_IEs <= USED 0
PerDec_EuRRCConnectionReconfiguration_v10l0_IEs <= USED 0
PerDec_EuRRCConnectionReconfiguration_v12f0_IEs <= USED 0
PerDec_EuRRCConnectionReconfiguration_v1370_IEs <= USED 0
PerDec_EuRRCConnectionReconfiguration_v13c0_IEs <= USED 0
PerDec_EuRRCConnectionReconfiguration_v8m0_IEs <= USED 0
PerDec_EuRSRQ_Type_r12 <= USED 0
PerDec_EuRadioResourceConfigCommonPSCell_r12 <= USED 0
PerDec_EuRadioResourceConfigCommonPSCell_v12f0 <= USED 0
PerDec_EuRadioResourceConfigCommonPSCell_v1440 <= USED 0
PerDec_EuRadioResourceConfigCommonSCell_v10l0 <= USED 0
PerDec_EuRadioResourceConfigCommonSCell_v10l0_ul_Configuration_v10l0 <= USED 0
PerDec_EuRadioResourceConfigCommonSCell_v1440 <= USED 0
PerDec_EuRadioResourceConfigCommonSCell_v1440_ul_Configuration_v1440 <= USED 0
PerDec_EuRadioResourceConfigCommonSCell_v1440_ul_FreqInfo_v1440 <= USED 0
PerDec_EuRadioResourceConfigDedicatedPSCell_r12 <= USED 0
PerDec_EuRadioResourceConfigDedicatedPSCell_v1370 <= USED 0
PerDec_EuRadioResourceConfigDedicatedPSCell_v13c0 <= USED 0
PerDec_EuRadioResourceConfigDedicatedSCG_r12 <= USED 0
PerDec_EuRadioResourceConfigDedicatedSCell_v13c0 <= USED 0
PerDec_EuRadioResourceConfigDedicated_dummy <= USED 0
PerDec_EuRadioResourceConfigDedicated_v1370 <= USED 0
PerDec_EuRadioResourceConfigDedicated_v13c0 <= USED 0
PerDec_EuRegisteredMME <= USED 0
PerDec_EuReportQuantityNR_r15 <= USED 0
PerDec_EuReportQuantityWLAN_r13 <= USED 0
PerDec_EuSBAS_ID_r15 <= USED 0
PerDec_EuSCG_ConfigPartSCG_r12 <= USED 0
PerDec_EuSCG_ConfigPartSCG_v12f0 <= USED 0
PerDec_EuSCG_ConfigPartSCG_v13c0 <= USED 0
PerDec_EuSCG_Configuration_r12_scg_ConfigPartMCG_r12 <= USED 0
PerDec_EuSCG_Configuration_r12_setup <= USED 0
PerDec_EuSCG_Configuration_v12f0_setup <= USED 0
PerDec_EuSCG_Configuration_v13c0_setup <= USED 0
PerDec_EuSCPTM_NeighbourCellList_r13 <= USED 0
PerDec_EuSCellConfigCommon_r15 <= USED 0
PerDec_EuSCellGroupToReleaseList_r15 <= USED 0
PerDec_EuSCellToAddModExt_r13 <= USED 0
PerDec_EuSCellToAddModExt_r13_cellIdentification_r13 <= USED 0
PerDec_EuSCellToAddModExt_v1370 <= USED 0
PerDec_EuSCellToAddModListExt_r13 <= USED 0
PerDec_EuSCellToAddModListExt_v1370 <= USED 0
PerDec_EuSCellToAddModListExt_v13c0 <= USED 0
PerDec_EuSCellToAddModList_v10l0 <= USED 0
PerDec_EuSCellToAddModList_v13c0 <= USED 0
PerDec_EuSCellToAddMod_v10l0 <= USED 0
PerDec_EuSCellToAddMod_v13c0 <= USED 0
PerDec_EuSCellToReleaseListExt_r13 <= USED 0
PerDec_EuSS_RSSI_Measurement_r15 <= USED 0
PerDec_EuSecurityConfigHO_v1530 <= USED 0
PerDec_EuSecurityConfigHO_v1530_epc_To5GC_r15 <= USED 0
PerDec_EuSecurityConfigHO_v1530_fivegc_ToEPC_r15 <= USED 0
PerDec_EuSecurityConfigHO_v1530_intra5GC_r15 <= USED 0
PerDec_EuSoundingRS_UL_ConfigDedicatedAperiodic_v1430_setup <= USED 0
PerDec_EuSupportedBandEUTRA <= USED 0
PerDec_EuSupportedBandEUTRA_v1250 <= USED 0
PerDec_EuSupportedBandEUTRA_v1310 <= USED 0
PerDec_EuSupportedBandEUTRA_v1320 <= USED 0
PerDec_EuSupportedBandEUTRA_v9e0 <= USED 0
PerDec_EuSystemInformationBlockType1_v1540_IEs <= USED 0
PerDec_EuSystemInformationBlockType2_v10m0_IEs <= USED 0
PerDec_EuSystemInformationBlockType2_v10m0_IEs_freqInfo_v10l0 <= USED 0
PerDec_EuSystemInformationBlockType2_v10m0_IEs_multiBandInfoList_v10l0 <= USED 0
PerDec_EuSystemInformationBlockType2_v10n0_IEs <= USED 0
PerDec_EuSystemInformationBlockType2_v13c0_IEs <= USED 0
PerDec_EuSystemInformationBlockType3_v10j0_IEs <= USED 0
PerDec_EuSystemInformationBlockType3_v10l0_IEs <= USED 0
PerDec_EuSystemInformationBlockType6_v8h0_IEs <= USED 0
PerDec_EuSystemInformationBlockType6_v8h0_IEs_carrierFreqListUTRA_FDD_v8h0 <= USED 0
PerDec_EuTDD_ConfigSL_r12 <= USED 0
PerDec_EuTDD_Config_v1430 <= USED 0
PerDec_EuTDD_Config_v1450 <= USED 0
PerDec_EuTDD_PUSCH_UpPTS_r14_setup <= USED 0
PerDec_EuTMGI_r9 <= USED 0
PerDec_EuTargetMBSFN_AreaList_r12 <= USED 0
PerDec_EuTargetMBSFN_Area_r12 <= USED 0
PerDec_EuThresholdListNR_r15 <= USED 0
PerDec_EuTraceReference_r10 <= USED 0
PerDec_EuTrackingAreaCodeList_r10 <= USED 0
PerDec_EuTrackingAreaCodeList_v1130 <= USED 0
PerDec_EuTrackingAreaCodeList_v1130_plmn_Identity_perTAC_List_r11 <= USED 0
PerDec_EuTx_ResourcePoolMeasList_r14 <= USED 0
PerDec_EuUAC_BarringInfoSetList_r15 <= USED 0
PerDec_EuUAC_BarringInfoSet_r15 <= USED 0
PerDec_EuUAC_BarringPerCatList_r15 <= USED 0
PerDec_EuUAC_BarringPerCat_r15 <= USED 0
PerDec_EuUAC_BarringPerPLMN_List_r15 <= USED 0
PerDec_EuUAC_BarringPerPLMN_r15 <= USED 0
PerDec_EuUAC_BarringPerPLMN_r15_uac_AC_BarringListType_r15_uac_ImplicitAC_BarringList_r15 <= USED 0
PerDec_EuUEInformationRequest_r9 <= USED 0
PerDec_EuUEInformationRequest_r9_IEs <= USED 0
PerDec_EuUEInformationRequest_v1020_IEs <= USED 0
PerDec_EuUEInformationRequest_v1130_IEs <= USED 0
PerDec_EuUEInformationRequest_v1250_IEs <= USED 0
PerDec_EuUEInformationRequest_v930_IEs <= USED 0
PerDec_EuUL_PDCP_DelayResultList_r13 <= USED 0
PerDec_EuUL_PDCP_DelayResult_r13 <= USED 0
PerDec_EuUplinkPUSCH_LessPowerControlDedicated_v1430 <= USED 0
PerDec_EuUplinkPowerControlCommonPSCell_r12 <= USED 0
PerDec_EuUplinkPowerControlCommonPUSCH_LessCell_v1430 <= USED 0
PerDec_EuUplinkPowerControlDedicatedSTTI_r15 <= USED 0
PerDec_EuUplinkPowerControlDedicated_v1530 <= USED 0
PerDec_EuWLAN_CarrierInfo_r13 <= USED 0
PerDec_EuWLAN_ChannelList_r13 <= USED 0
PerDec_EuWLAN_OffloadConfig_r12 <= USED 0
PerDec_EuWLAN_OffloadConfig_r12_thresholdBackhaul_Bandwidth_r12 <= USED 0
PerDec_EuWLAN_OffloadConfig_r12_thresholdChannelUtilization_r12 <= USED 0
PerDec_EuWLAN_OffloadConfig_r12_thresholdRSRP_r12 <= USED 0
PerDec_EuWLAN_OffloadConfig_r12_thresholdRSRQ_OnAllSymbolsWithWB_r12 <= USED 0
PerDec_EuWLAN_OffloadConfig_r12_thresholdRSRQ_OnAllSymbols_r12 <= USED 0
PerDec_EuWLAN_OffloadConfig_r12_thresholdRSRQ_WB_r12 <= USED 0
PerDec_EuWLAN_OffloadConfig_r12_thresholdRSRQ_r12 <= USED 0
PerDec_EuWLAN_OffloadConfig_r12_thresholdWLAN_RSSI_r12 <= USED 0
PerDec_EuWLAN_RTT_r15 <= USED 0
;FILE lterrc_encb_r1551.o
PerEnc_EuAbsoluteTimeInfo_r10_str <= USED 0
PerEnc_EuBandCombinationParameters_v1430_v2x_SupportedRxBandCombListPerBC_r14_str <= USED 0
PerEnc_EuBandCombinationParameters_v1430_v2x_SupportedTxBandCombListPerBC_r14_str <= USED 0
PerEnc_EuCSG_Identity <= USED 0
PerEnc_EuC_RNTI <= USED 0
PerEnc_EuCellIdentity <= USED 0
PerEnc_EuFailureInformation_r15_nonCriticalExtension_str <= USED 0
PerEnc_EuFlightPathInfoReport_r15_nonCriticalExtension_str <= USED 0
PerEnc_EuIDC_SubframePattern_r11_subframePatternTDD_r11_subframeConfig0_r11_str <= USED 0
PerEnc_EuIDC_SubframePattern_r11_subframePatternTDD_r11_subframeConfig6_r11_str <= USED 0
PerEnc_EuI_RNTI_r15_str <= USED 0
PerEnc_EuInitialUE_Identity_5GC_ng_5G_S_TMSI_Part1_str <= USED 0
PerEnc_EuInitialUE_Identity_5GC_randomValue_str <= USED 0
PerEnc_EuMMEC <= USED 0
PerEnc_EuNG_5G_S_TMSI_r15_str <= USED 0
PerEnc_EuNeighCellConfig <= USED 0
PerEnc_EuRRCConnectionReconfigurationComplete_v1530_IEs_nonCriticalExtension_str <= USED 0
PerEnc_EuRRCConnectionReestablishmentComplete_v1530_IEs_nonCriticalExtension_str <= USED 0
PerEnc_EuRRCConnectionSetupComplete_v1540_IEs_nonCriticalExtension_str <= USED 0
PerEnc_EuResumeIdentity_r13_str <= USED 0
PerEnc_EuShortMAC_I <= USED 0
PerEnc_EuTrackingAreaCode <= USED 0
PerEnc_EuUEAssistanceInformation_r11_criticalExtensions_criticalExtensionsFuture_str <= USED 0
PerEnc_EuUEAssistanceInformation_v1530_IEs_nonCriticalExtension_str <= USED 0
PerEnc_EuUECapabilityInformation_v1250_IEs_nonCriticalExtension_str <= USED 0
PerEnc_EuUEInformationResponse_r9_criticalExtensions_criticalExtensionsFuture_str <= USED 0
PerEnc_EuUEInformationResponse_v1530_IEs_nonCriticalExtension_str <= USED 0
PerEnc_EuUEInformationResponse_v9e0_IEs_nonCriticalExtension_str <= USED 0
PerEnc_EuUE_EUTRA_Capability_v10j0_IEs_nonCriticalExtension_str <= USED 0
;FILE lterrc_encc_r1551.o
PerEnc_EuBandCombinationParameters_r13_supportedCellGrouping_r13 <= USED 0
PerEnc_EuIDC_SubframePattern_r11 <= USED 0
PerEnc_EuIDC_SubframePattern_r11_subframePatternTDD_r11 <= USED 0
PerEnc_EuInitialUE_Identity_5GC <= USED 0
PerEnc_EuLocationInfo_r10_locationCoordinates_r10 <= USED 0
PerEnc_EuTDM_AssistanceInfo_r11 <= USED 0
PerEnc_EuTMGI_r9_plmn_Id_r9 <= USED 0
PerEnc_EuUEAssistanceInformation_r11_c1 <= USED 0
PerEnc_EuUEAssistanceInformation_r11_criticalExtensions <= USED 0
PerEnc_EuVisitedCellInfo_r12_visitedCellId_r12 <= USED 0
;FILE lterrc_ence_r1551.o
PerEnc_EuAffectedCarrierFreq_r11_interferenceDirection_r11 <= USED 0
PerEnc_EuAlpha_r12 <= USED 0
PerEnc_EuBandCombinationParameters_r13_asynchronous_r13 <= USED 0
PerEnc_EuCA_BandwidthClass_r10 <= USED 0
PerEnc_EuCipheringAlgorithm <= USED 0
PerEnc_EuDC_Parameters_r12_drb_TypeSCG_r12 <= USED 0
PerEnc_EuDC_Parameters_r12_drb_TypeSplit_r12 <= USED 0
PerEnc_EuEstablishmentCause_5GC <= USED 0
PerEnc_EuFailedLogicalChannelInfo_r15_failureType <= USED 0
PerEnc_EuFailureReportSCG_NR_r15_failureType_r15 <= USED 0
PerEnc_EuFailureReportSCG_r12_failureType_r12 <= USED 0
PerEnc_EuFailureReportSCG_r12_failureType_v1290 <= USED 0
PerEnc_EuIRAT_ParametersGERAN_v920_dtm_r9 <= USED 0
PerEnc_EuIRAT_ParametersGERAN_v920_e_RedirectionGERAN_r9 <= USED 0
PerEnc_EuLogMeasInfo_r10_inDeviceCoexDetected_r13 <= USED 0
PerEnc_EuMIMO_CapabilityUL_r10 <= USED 0
PerEnc_EuMIMO_NonPrecodedCapabilities_r13_config1_r13 <= USED 0
PerEnc_EuMIMO_NonPrecodedCapabilities_r13_config2_r13 <= USED 0
PerEnc_EuMIMO_NonPrecodedCapabilities_r13_config3_r13 <= USED 0
PerEnc_EuMIMO_NonPrecodedCapabilities_r13_config4_r13 <= USED 0
PerEnc_EuMIMO_UE_BeamformedCapabilities_r13_altCodebook_r13 <= USED 0
PerEnc_EuMeasCycleSCell_r10 <= USED 0
PerEnc_EuMeasReportAppLayer_r15_IEs_serviceType <= USED 0
PerEnc_EuMeasScaleFactor_r12 <= USED 0
PerEnc_EuMobilityStateParameters_t_Evaluation <= USED 0
PerEnc_EuMobilityStateParameters_t_HystNormal <= USED 0
PerEnc_EuP_a <= USED 0
PerEnc_EuPeriodicBSR_Timer_r12 <= USED 0
PerEnc_EuPhyLayerParameters_v1280_alternativeTBS_Indices_r12 <= USED 0
PerEnc_EuPhyLayerParameters_v1310_pdcch_CandidateReductions_r13 <= USED 0
PerEnc_EuPhyLayerParameters_v1310_skipMonitoringDCI_Format0_1A_r13 <= USED 0
PerEnc_EuPhysCellIdRange_range <= USED 0
PerEnc_EuPreambleTransMax <= USED 0
PerEnc_EuQ_OffsetRange <= USED 0
PerEnc_EuRF_Parameters_v10j0_multiNS_Pmax_r10 <= USED 0
PerEnc_EuRRCConnectionSetupComplete_v1530_IEs_connectTo5GC_r15 <= USED 0
PerEnc_EuRRCConnectionSetupComplete_v1530_IEs_flightPathInfoAvailable_r15 <= USED 0
PerEnc_EuRRCConnectionSetupComplete_v1530_IEs_idleMeasAvailable_r15 <= USED 0
PerEnc_EuRRCConnectionSetupComplete_v1530_IEs_logMeasAvailableBT_r15 <= USED 0
PerEnc_EuRRCConnectionSetupComplete_v1530_IEs_logMeasAvailableWLAN_r15 <= USED 0
PerEnc_EuRRCConnectionSetupComplete_v1540_IEs_guami_Type_r15 <= USED 0
PerEnc_EuRRCConnectionSetupComplete_v1540_IEs_gummei_Type_v1540 <= USED 0
PerEnc_EuRRCEarlyDataRequest_r15_IEs_establishmentCause_r15 <= USED 0
PerEnc_EuReleaseCause <= USED 0
PerEnc_EuRetxBSR_Timer_r12 <= USED 0
PerEnc_EuSL_CP_Len_r12 <= USED 0
PerEnc_EuSL_DiscResourcePool_r12_discPeriod_r12 <= USED 0
PerEnc_EuSL_DiscResourcePool_r12_freqInfo_ul_Bandwidth <= USED 0
PerEnc_EuSL_DiscResourcePool_r12_setup <= USED 0
PerEnc_EuSL_DiscResourcePool_r12_ue_SelectedResourceConfig_r12_txProbability_r12 <= USED 0
PerEnc_EuSL_DiscSysInfoReport_r13_q_Hyst_r13 <= USED 0
PerEnc_EuSL_DiscSysInfoReport_r13_ul_Bandwidth_r13 <= USED 0
PerEnc_EuSL_GapPattern_r13_gapPeriod_r13 <= USED 0
PerEnc_EuSL_Parameters_r12_commSimultaneousTx_r12 <= USED 0
PerEnc_EuSL_Parameters_r12_discScheduledResourceAlloc_r12 <= USED 0
PerEnc_EuSL_Parameters_r12_discSupportedProc_r12 <= USED 0
PerEnc_EuSL_Parameters_r12_disc_SLSS_r12 <= USED 0
PerEnc_EuSL_Parameters_r12_disc_UE_SelectedResourceAlloc_r12 <= USED 0
PerEnc_EuSL_PeriodComm_r12 <= USED 0
PerEnc_EuSL_SyncConfigNFreq_r13_discSyncWindow_r13 <= USED 0
PerEnc_EuSL_SyncConfigNFreq_r13_syncTxPeriodic_r13 <= USED 0
PerEnc_EuSRS_AntennaPort <= USED 0
PerEnc_EuSidelinkUEInformation_r12_IEs_discRxInterest_r12 <= USED 0
PerEnc_EuSidelinkUEInformation_v1310_IEs_ue_Type_r13 <= USED 0
PerEnc_EuSidelinkUEInformation_v1430_IEs_p2x_CommTxType_r14 <= USED 0
PerEnc_EuSupportedBandInfo_r12_support_r12 <= USED 0
PerEnc_EuTDD_ConfigSL_r12_subframeAssignmentSL_r12 <= USED 0
PerEnc_EuTDD_Config_specialSubframePatterns <= USED 0
PerEnc_EuTDD_Config_subframeAssignment <= USED 0
PerEnc_EuTDD_Config_v1130_specialSubframePatterns_v1130 <= USED 0
PerEnc_EuTDD_Config_v1430_specialSubframePatterns_v1430 <= USED 0
PerEnc_EuTDD_Config_v1450_specialSubframePatterns_v1450 <= USED 0
PerEnc_EuTDM_AssistanceInfo_r11_drx_ActiveTime_r11 <= USED 0
PerEnc_EuTDM_AssistanceInfo_r11_drx_CycleLength_r11 <= USED 0
PerEnc_EuUEAssistanceInformation_r11_IEs_powerPrefIndication_r11 <= USED 0
PerEnc_EuUE_BasedNetwPerfMeasParameters_r10_loggedMeasurementsIdle_r10 <= USED 0
PerEnc_EuUE_BasedNetwPerfMeasParameters_r10_standaloneGNSS_Location_r10 <= USED 0
PerEnc_EuUE_BasedNetwPerfMeasParameters_v1250_loggedMBSFNMeasurements_r12 <= USED 0
PerEnc_EuUL_CyclicPrefixLength <= USED 0
PerEnc_EuVictimSystemType_r11_bds_r11 <= USED 0
PerEnc_EuVictimSystemType_r11_bluetooth_r11 <= USED 0
PerEnc_EuVictimSystemType_r11_galileo_r11 <= USED 0
PerEnc_EuVictimSystemType_r11_glonass_r11 <= USED 0
PerEnc_EuVictimSystemType_r11_gps_r11 <= USED 0
PerEnc_EuVictimSystemType_r11_wlan_r11 <= USED 0
PerEnc_EuWLAN_IW_Parameters_r12_wlan_IW_ANDSF_Policies_r12 <= USED 0
PerEnc_EuWLAN_IW_Parameters_r12_wlan_IW_RAN_Rules_r12 <= USED 0
;FILE lterrc_enco_r1551.o
PerEnc_EuHandover_nas_SecurityParamFromEUTRA_str <= USED 0
PerEnc_EuHandover_targetRAT_MessageContainer_str <= USED 0
PerEnc_EuLocationInfo_r10_gnss_TOD_msec_r10_str <= USED 0
PerEnc_EuLocationInfo_r10_horizontalVelocity_r10_str <= USED 0
PerEnc_EuLocationInfo_r10_locationCoordinates_r10_ellipsoidArc_r11_str <= USED 0
PerEnc_EuLocationInfo_r10_locationCoordinates_r10_ellipsoidPointWithAltitudeAndUncertaintyEllipsoid_r11_str <= USED 0
PerEnc_EuLocationInfo_r10_locationCoordinates_r10_ellipsoidPointWithAltitude_r10_str <= USED 0
PerEnc_EuLocationInfo_r10_locationCoordinates_r10_ellipsoidPointWithUncertaintyCircle_r11_str <= USED 0
PerEnc_EuLocationInfo_r10_locationCoordinates_r10_ellipsoidPointWithUncertaintyEllipse_r11_str <= USED 0
PerEnc_EuLocationInfo_r10_locationCoordinates_r10_ellipsoid_Point_r10_str <= USED 0
PerEnc_EuLocationInfo_r10_locationCoordinates_r10_polygon_r11_str <= USED 0
PerEnc_EuLocationInfo_r10_verticalVelocityInfo_r15_verticalVelocityAndUncertainty_r15_str <= USED 0
PerEnc_EuLocationInfo_r10_verticalVelocityInfo_r15_verticalVelocity_r15_str <= USED 0
PerEnc_EuMeasReportAppLayer_r15_IEs_measReportAppLayerContainer_r15_str <= USED 0
PerEnc_EuOtherConfig_r9_setup_measConfigAppLayerContainer_r15_str <= USED 0
PerEnc_EuTMGI_r9_serviceId_r9_str <= USED 0
PerEnc_EuUEAssistanceInformation_r11_IEs_lateNonCriticalExtension_str <= USED 0
PerEnc_EuUEInformationResponse_v930_IEs_lateNonCriticalExtension_str <= USED 0
;FILE lterrc_encs_r1551.o
PerEnc_EuAffectedCarrierFreqCombList_r11 <= USED 0
PerEnc_EuAffectedCarrierFreqCombList_r13 <= USED 0
PerEnc_EuAffectedCarrierFreqComb_r11 <= USED 0
PerEnc_EuAffectedCarrierFreqComb_r13 <= USED 0
PerEnc_EuAffectedCarrierFreqList_r11 <= USED 0
PerEnc_EuAffectedCarrierFreqList_v1310 <= USED 0
PerEnc_EuAffectedCarrierFreq_r11 <= USED 0
PerEnc_EuAffectedCarrierFreq_v1310 <= USED 0
PerEnc_EuBLER_Result_r12 <= USED 0
PerEnc_EuBLER_Result_r12_blocksReceived_r12 <= USED 0
PerEnc_EuBandParametersDL_r10 <= USED 0
PerEnc_EuBandParametersUL_r10 <= USED 0
PerEnc_EuCA_MIMO_ParametersDL_r10 <= USED 0
PerEnc_EuCA_MIMO_ParametersDL_r13_intraBandContiguousCC_InfoList_r13 <= USED 0
PerEnc_EuCA_MIMO_ParametersUL_r10 <= USED 0
PerEnc_EuCE_Parameters_r13 <= USED 0
PerEnc_EuCE_Parameters_v1320 <= USED 0
PerEnc_EuCellIndexList <= USED 0
PerEnc_EuDC_Parameters_r12 <= USED 0
PerEnc_EuFormat4_resource_r13 <= USED 0
PerEnc_EuFormat5_resource_r13 <= USED 0
PerEnc_EuFreqBandIndicatorListEUTRA_r12 <= USED 0
PerEnc_EuIDC_SubframePatternList_r11 <= USED 0
PerEnc_EuIMSI <= USED 0
PerEnc_EuMIMO_NonPrecodedCapabilities_r13 <= USED 0
PerEnc_EuMeasResult2EUTRA_r9 <= USED 0
PerEnc_EuMeasResult2EUTRA_v1250 <= USED 0
PerEnc_EuMeasResult2EUTRA_v9e0 <= USED 0
PerEnc_EuMeasResultList2EUTRA_r9 <= USED 0
PerEnc_EuMeasResultList2EUTRA_v1250 <= USED 0
PerEnc_EuMeasResultList2EUTRA_v9e0 <= USED 0
PerEnc_EuMeasResults_measResultPCell_v1310 <= USED 0
PerEnc_EuMobilityStateParameters <= USED 0
PerEnc_EuMultiBandInfoList <= USED 0
PerEnc_EuMultiBandInfoList_r11 <= USED 0
PerEnc_EuMultiBandInfoList_v10j0 <= USED 0
PerEnc_EuMultiBandInfoList_v10l0 <= USED 0
PerEnc_EuMultiBandInfoList_v9e0 <= USED 0
PerEnc_EuMultiBandInfo_v9e0 <= USED 0
PerEnc_EuNS_PmaxList_r10 <= USED 0
PerEnc_EuNS_PmaxList_v10l0 <= USED 0
PerEnc_EuNS_PmaxValue_r10 <= USED 0
PerEnc_EuNS_PmaxValue_v10l0 <= USED 0
PerEnc_EuPLMN_IdentityList3_r11 <= USED 0
PerEnc_EuPhyLayerParameters_v1280 <= USED 0
PerEnc_EuPhyLayerParameters_v1320 <= USED 0
PerEnc_EuPhysCellIdRange <= USED 0
PerEnc_EuPhysCellIdRangeUTRA_FDDList_r9 <= USED 0
PerEnc_EuPhysCellIdRangeUTRA_FDD_r9 <= USED 0
PerEnc_EuRF_Parameters_v10j0 <= USED 0
PerEnc_EuRSRQ_Type_r12 <= USED 0
PerEnc_EuSL_CommTxResourceReq_r12 <= USED 0
PerEnc_EuSL_DestinationInfoList_r12 <= USED 0
PerEnc_EuSL_DiscTxResourceReqPerFreqList_r13 <= USED 0
PerEnc_EuSL_DiscTxResourceReq_r13 <= USED 0
PerEnc_EuSL_Parameters_r12 <= USED 0
PerEnc_EuSL_TxParameters_r12 <= USED 0
PerEnc_EuSupportedBandInfoList_r12 <= USED 0
PerEnc_EuSupportedBandInfo_r12 <= USED 0
PerEnc_EuTDD_Config <= USED 0
PerEnc_EuTDD_ConfigSL_r12 <= USED 0
PerEnc_EuTDD_Config_v1130 <= USED 0
PerEnc_EuTDD_Config_v1430 <= USED 0
PerEnc_EuTDD_Config_v1450 <= USED 0
PerEnc_EuTDM_AssistanceInfo_r11_drx_AssistanceInfo_r11 <= USED 0
PerEnc_EuTMGI_r9 <= USED 0
PerEnc_EuTargetMBSFN_Area_r12 <= USED 0
PerEnc_EuUEAssistanceInformation_r11 <= USED 0
PerEnc_EuUEAssistanceInformation_r11_IEs <= USED 0
PerEnc_EuUE_BasedNetwPerfMeasParameters_r10 <= USED 0
PerEnc_EuUE_BasedNetwPerfMeasParameters_v1250 <= USED 0
PerEnc_EuUE_EUTRA_CapabilityAddXDD_Mode_v1180 <= USED 0
PerEnc_EuUE_EUTRA_Capability_v10j0_IEs <= USED 0
PerEnc_EuVictimSystemType_r11 <= USED 0
PerEnc_EuVisitedCellInfoList_r12 <= USED 0
PerEnc_EuVisitedCellInfo_r12 <= USED 0
PerEnc_EuVisitedCellInfo_r12_pci_arfcn_r12 <= USED 0
PerEnc_EuWLAN_IW_Parameters_r12 <= USED 0
;FILE lterrc_psm.o
ErrcCerReadPsmContext <= USED 0
ErrcCerWritePsmContext <= USED 0
ErrcFreePsmLocalMem <= USED 0
;FILE lterrcais.o
LteAisDecodeDlDcchMsgRrcReconfigFromOtherRat <= USED 0
LteAisEncodeUeCapabilityRatContainer <= USED 0
;FILE lterrcband.o
;FILE lterrccell.o
LteRrcCellMgrCalcEutraNcellNum <= USED 0
LteRrcCellMgrCheckCellPool <= USED 0
LteRrcCellMgrCheckDb <= USED 0
LteRrcCellMgrCheckFreqPool <= USED 0
LteRrcCellMgrCheckRliPool <= USED 0
LteRrcCellMgrClearFreq <= USED 0
LteRrcCellMgrDebugAssert466 <= USED 0
LteRrcCellMgrExtractCell <= USED 0
lteRrcCellSwapIntraInterFreq <= USED 0
;FILE lterrccer.o
LteCerCheckAndSendEcphyDsRcvGsmPchInLtePsReq <= USED 0
LteCerConstructAndSendEutraProximityInd <= USED 0
LteCerDamResetParams <= USED 0
LteCerGetHoNotAlloweFlag <= USED 0
LteCerGetRplmn <= USED 0
LteCerHandleIfCsCallIgnoredByNW <= USED 0
LteCerHandleT301Expiry <= USED 0
LteCerInitialReestRecord <= USED 0
LteCerIsCdrxConfigured <= USED 0
LteCerSortFreqRsrp <= USED 0
LtePdcpGmmReestablishReqForTest <= USED 0
LteRrcBuildMeasResultList2EUTRA_v9e0 <= USED 0
LteRrcBuildMeasResultListEUTRA_v1130 <= USED 0
LteRrcCerSetAutoRedirect <= USED 0
LteRrcCheckMeasResultListEUTRA <= USED 0
LteRrcClearHandoverProtectTimer <= USED 0
LteRrcClearRbEstablishProtectTimer <= USED 0
LteRrcClearVoiceBadReestStart <= USED 0
LteRrcDisableDirectCampWhenLeavingConnected <= USED 0
LteRrcEnableDirectCampWhenLeavingConnected <= USED 0
LteRrcFreeMemEpsAsSecurityContextBeforeHandover <= USED 0
LteRrcSendEcphyMeasSubframePatternPCellConfigReq <= USED 0
LteRrcSendRrcConnectionReleaseR12 <= USED 0
LteRrcSetHandoverProtectTimer <= USED 0
LteRrcSetRbEstablishProtectTimer <= USED 0
LteRrcTranslatePresentNeedOP <= USED 0
QueryConnectState <= USED 0
;FILE lterrccerrb.o
LteCerRbGetNotSuspendedSrbs <= USED 0
;FILE lterrccsr.o
GsmGetBandWidthOfSertvingCell <= USED 0
GsmGetSetLteCellStatus <= USED 0
LteBandGetBandsInformForPlmnSearch <= USED 0
LteCheckLockOnLteFreq <= USED 0
LteCsrAddCurrentCellToEarfcnList <= USED 0
LteCsrB41SupportedAndRequested <= USED 0
LteCsrCheckIfDrxFindCellisNeed <= USED 0
LteCsrCheckIsSpecialTacForGuangzhou <= USED 0
LteCsrFillGsmCellListInformation <= USED 0
LteCsrHandleCachedSib1ForLongEdrxExit <= USED 0
LteCsrHandleEcphySwitchRatToUmtsCnf <= USED 0
LteCsrIsCmccEsrvccTac <= USED 0
LteCsrIsRequestPlmnListContainCMCC <= USED 0
LteCsrProcessIfSib5Change <= USED 0
LteCsrRemoveCellFromBchCrcErrorBarList <= USED 0
LteCsrSetLtePlmnStateForIcsSelection <= USED 0
LteCsrSetSib5 <= USED 0
LteCsrStartTreselection <= USED 0
LteRrcCheckIfLowThanMinSuitableCellRsrp <= USED 0
LteRrcCheckIfPendingErrcPlmnListAbortReq <= USED 0
LteRrcCsrCheckCellOtherThanB39Exists <= USED 0
LteRrcCsrCheckIfBandInMultiBandListSupported <= USED 0
LteRrcCsrCheckIfNcellOnIntendedBand <= USED 0
LteRrcCsrPlmnSearchContextAllocBand <= USED 0
LteRrcCsrPlmnSearchContextInit <= USED 0
LteRrcCsrSaveRsrpRsrq <= USED 0
LteRrcCsrSaveRssiC1Average <= USED 0
LteRrcDisableRandomReestablish <= USED 0
LteRrcDsdsBufferActReq <= USED 0
LteRrcEnableRandomReestablish <= USED 0
LteRrcManualReestablish <= USED 0
LteRrcQueryTddConfig <= USED 0
SetLteInterFreq <= USED 0
SetLteInterFreqInConnected <= USED 0
SetPagingisMismatch <= USED 0
TriggerLteOos <= USED 0
;FILE lterrccsrics.o
LteCsrIcsAddFreqToListFromMm <= USED 0
LteCsrIcsHandleEcphyDeactiveCnf <= USED 0
LteCsrIcsResetMacToIdle <= USED 0
;FILE lterrccsrplms.o
LteGetRatFromErrcNasNetworkBand <= USED 0
;FILE lterrccsrutils.o
;FILE lterrcdsds.o
ErrcCleargrrErrcReselecting <= USED 0
ErrcGeterrcActive <= USED 0
ErrcGetgrrErrcIRatReselecting <= USED 0
ErrcGetgrrErrcReselecting <= USED 0
ErrcSeterrcActive <= USED 0
ErrcSetgrrErrcIRatReselecting <= USED 0
ErrcSetgrrErrcReselecting <= USED 0
LteRrcDsdsGetErrcActive <= USED 0
LteRrcDsdsHandleBufferedIratDsReq <= USED 0
LteRrcDsdsIsHighPriorityEstablish <= USED 0
LteRrcDsdsRequestSib1 <= USED 0
LteRrcDsdsSetIsDsds <= USED 0
;FILE lterrcete.o
LteEteFreeMobiFromEutranCmdNonCriticalExtension <= USED 0
LteEteHandleErrcActivateTestModeReq <= USED 0
LteEteHandleErrcDeactivateTestModeReq <= USED 0
LteEteHandleErrcLteModeReq <= USED 0
LteEteHandleUnitaryTestInit <= USED 0
LteEteSendEcphyEngInfoReq <= USED 0
LteEteSendEcphyStopEngInfoReq <= USED 0
LteRrcCopyAsn1Bits <= USED 0
LteRrcCopyLteCrsAssistanceInfo <= USED 0
LteRrcCopyLteMbsfnSubframeConfig <= USED 0
LteRrcDamDisable <= USED 0
LteRrcDamEnable <= USED 0
LteRrcDamSetCrrc <= USED 0
LteRrcDamSetTrrc <= USED 0
LteRrcGetTddFddMode <= USED 0
LteRrcHandleTimerExpiryTFastReturnGuard <= USED 0
LteRrcPhyConfigDedParaFitForNonCA <= USED 0
LteRrcReoderSignalSequence <= USED 0
LteRrcSendErrcDeactReq <= USED 0
LteRrcSetMobilityType <= USED 0
LteRrcSetSpeedType <= USED 0
LteRrcSetTxPowerType <= USED 0
LteRrcVarMemInit <= USED 0
;FILE lterrclogcxt.o
LteRrcHandleCellInfoLogConfig <= USED 0
LteRrcHandleStatisticsLogConfig <= USED 0
LteRrcUpdateChangeCellReestSuccessStatistics <= USED 0
LteRrcUpdateEstFailureStatistics <= USED 0
LteRrcUpdateEstStartStatistics <= USED 0
LteRrcUpdateEstSuccessStatistics <= USED 0
LteRrcUpdateNotChangeCellReestSuccessStatistics <= USED 0
LteRrcUpdateReconfCompleteStatistics <= USED 0
LteRrcUpdateReconfStartStatistics <= USED 0
LteRrcUpdateReestFailureStatistics <= USED 0
LteRrcUpdateReestStartStatistics <= USED 0
LteRrcUpdateReselFailureStatistics <= USED 0
LteRrcUpdateReselStartStatistics <= USED 0
LteRrcUpdateRlFailureStatistics <= USED 0
;FILE lterrcmcr.o
LteMcrChangeA3ConditionTimeToTrigger <= USED 0
LteMcrCheckConfigFakeMeasGap <= USED 0
LteMcrCheckIfNeedStartIratMeas <= USED 0
LteMcrCheckIfNeedTriggerA5 <= USED 0
LteMcrCheckIfNeedTriggerB2 <= USED 0
LteMcrCheckIfScellBad <= USED 0
LteMcrCheckIsCmccEsrvccImsStartForSpecialTac <= USED 0
LteMcrCheckMoreThan1Db <= USED 0
LteMcrCheckMoreThan1DbForIrat <= USED 0
LteMcrCheckProximityEnteredForEutra <= USED 0
LteMcrConvertEcN0ToRxlev <= USED 0
LteMcrConvertRscpToRxlev <= USED 0
LteMcrInitYotaRxSib1Info <= USED 0
LteMcrIsBadQuality <= USED 0
LteMcrSetMinTimeToTriggerLeftInMs <= USED 0
LteMcrTriggerIntraInterFreqMeasReq <= USED 0
LteMcrTriggerIntraInterFreqMeasStopReq <= USED 0
;FILE lterrcsecurity.o
LteCerHandleSecurityConfigForHOFromUtra <= USED 0
LteCerUpdatesecurityConfigIOForHOFromUtra <= USED 0
;FILE lterrcsir.o
LteSirCopySib6V8h0Data <= USED 0
LteSirCopySib8Data <= USED 0
;FILE lterrctask.o
LteRrcTask1 <= USED 0
;FILE lterrcutils.o
LteRrcCheckEnterConnected <= USED 0
LteRrcCheckIfCurrentTacMatched <= USED 0
LteRrcGetLogInfo <= USED 0
LteRrcGetLogInit <= USED 0
LteRrcGetSystemInformationBlock <= USED 0
LteRrcIsSelectionContextLte <= USED 0
;FILE lterrcwifi.o
;FILE lwip_api.o
lwip_debug_dumppbufs <= USED 0
lwip_enable_all_debug_switch <= USED 0
lwip_enable_ip_debug_switch <= USED 0
lwip_enable_iptrace_debug_switch <= USED 0
lwip_enable_memory_debug_switch <= USED 0
lwip_enable_tcp_debug_switch <= USED 0
lwip_get_adjust_pp_flag <= USED 0
lwip_get_debug_setting_by_name <= USED 0
lwip_get_default_nw6_ifname <= USED 0
lwip_get_dns_api_cache_flag <= USED 0
lwip_get_dns_bind_port <= USED 0
lwip_get_dns_max_retries <= USED 0
lwip_get_dns_relay_server <= USED 0
lwip_get_dns_wait_tmr <= USED 0
lwip_get_ip_default_ttl <= USED 0
lwip_get_ip_default_ttl_in <= USED 0
lwip_get_ip_reass_maxage <= USED 0
lwip_get_ipnet_mcpy_shift <= USED 0
lwip_get_loopif_input_flag <= USED 0
lwip_get_nat_ttl_tcp_set <= USED 0
lwip_get_netif_by_hostname <= USED 0
lwip_get_netif_eth_ifname <= USED 0
lwip_get_netif_lte6_ifname <= USED 0
lwip_get_netif_lte_ifname <= USED 0
lwip_get_netif_pc_ifname <= USED 0
lwip_get_netif_uap_ifname <= USED 0
lwip_get_netif_usb_ifname <= USED 0
lwip_get_netif_wifi_ifname <= USED 0
lwip_get_nw_status <= USED 0
lwip_get_nw_status_ip6 <= USED 0
lwip_get_rndis_hotplug_flag <= USED 0
lwip_get_rs_backoff <= USED 0
lwip_get_send_ra_all_flag <= USED 0
lwip_get_tcp_backoff <= USED 0
lwip_get_tcp_msl_val <= USED 0
lwip_get_tcp_persist_backoff <= USED 0
lwip_get_tcp_rcv_scale <= USED 0
lwip_get_tcp_rcv_win <= USED 0
lwip_get_tcp_rto_init <= USED 0
lwip_get_tcp_snd_win <= USED 0
lwip_get_tcp_sync_backoff <= USED 0
lwip_get_wan_access_port <= USED 0
lwip_ifconfig_value <= USED 0
lwip_iflist_value <= USED 0
lwip_manul_adjust_pp <= USED 0
lwip_nwst_clear_used_data <= USED 0
lwip_nwst_get_current_speed <= USED 0
lwip_nwst_get_cutoff_data <= USED 0
lwip_nwst_get_reminder_data <= USED 0
lwip_nwst_get_used_data <= USED 0
lwip_nwst_set_callback <= USED 0
lwip_nwst_set_cutoff_data <= USED 0
lwip_nwst_set_reminder_data <= USED 0
lwip_port_fwd_rule_add <= USED 0
lwip_reset_dns_relay_server <= USED 0
lwip_rndis_hotplug_break <= USED 0
lwip_rndis_hotplug_link <= USED 0
lwip_set_adjust_pp_flag <= USED 0
lwip_set_app_tcpdump_flag <= USED 0
lwip_set_check_192_168_dongle <= USED 0
lwip_set_debug_setting <= USED 0
lwip_set_default_debug_setting <= USED 0
lwip_set_dhcpd_server_flag <= USED 0
lwip_set_dns_api_cache_flag <= USED 0
lwip_set_dns_bind_port <= USED 0
lwip_set_dns_relay_port <= USED 0
lwip_set_dns_relay_server <= USED 0
lwip_set_dns_wait_tmr <= USED 0
lwip_set_dns_wait_tmr_default <= USED 0
lwip_set_dnsr_hijack_flag <= USED 0
lwip_set_dongle_detect_cnt <= USED 0
lwip_set_ethip6_ns_pool_first <= USED 0
lwip_set_iot_test_mode <= USED 0
lwip_set_ip_default_ttl <= USED 0
lwip_set_ip_default_ttl_in <= USED 0
lwip_set_ip_reass_maxage <= USED 0
lwip_set_ipnet_mcpy_shift <= USED 0
lwip_set_loopif_input_flag <= USED 0
lwip_set_mifi_flag <= USED 0
lwip_set_nat_ttl_tcp_set <= USED 0
lwip_set_ota_ip6addr_rsra_flag <= USED 0
lwip_set_ota_ip6addr_using_flag <= USED 0
lwip_set_reuseaddr_sp1_flag <= USED 0
lwip_set_rndis_hotplug_flag <= USED 0
lwip_set_rs_backoff <= USED 0
lwip_set_rs_backoff_default <= USED 0
lwip_set_rs_retry_counter <= USED 0
lwip_set_send_ra_all_flag <= USED 0
lwip_set_solinger_sp1_flag <= USED 0
lwip_set_tcp_ack_now_ <= USED 0
lwip_set_tcp_backoff <= USED 0
lwip_set_tcp_backoff_default <= USED 0
lwip_set_tcp_max_rtx <= USED 0
lwip_set_tcp_persist_backoff <= USED 0
lwip_set_tcp_persist_backoff_default <= USED 0
lwip_set_tcp_rcv_scale <= USED 0
lwip_set_tcp_rcv_win <= USED 0
lwip_set_tcp_rto_init <= USED 0
lwip_set_tcp_rto_syn_factor <= USED 0
lwip_set_tcp_rto_update_flag <= USED 0
lwip_set_tcp_rto_update_value <= USED 0
lwip_set_tcp_snd_win <= USED 0
lwip_set_tcp_syn_max_rtx <= USED 0
lwip_set_tcp_sync_backoff <= USED 0
lwip_set_tcp_sync_backoff_default <= USED 0
lwip_set_wan_access_port <= USED 0
reset_usbQCntMax <= USED 0
;FILE lwip_atctl.o
lwip_atctl_dump_info <= USED 0
lwip_atctl_dump_query <= USED 0
lwip_atctl_netif_info <= USED 0
lwip_atctl_netif_query <= USED 0
lwip_atctl_ping_process <= USED 0
lwip_atctl_ping_query <= USED 0
lwip_atctl_sysnvm_help <= USED 0
lwip_atctl_thread_delete <= USED 0
lwip_atctl_thread_post <= USED 0
;FILE lwip_customer.o
ip_pbuf_rti_dump <= USED 0
ip_pbuf_rti_log <= USED 0
lwip_hook_packet_captuer <= USED 0
lwip_lte_require_check_defined <= USED 0
lwip_parameters_customer_defined <= USED 0
lwip_rate_control_buildup <= USED 0
;FILE lwip_init.o
;FILE lwip_minis.o
ip_filter_ip_layer_icmp <= USED 0
ip_flow_stats_er <= USED 0
ip_nat_mangle_ftp <= USED 0
lwip_do_dns_query <= USED 0
lwip_do_dns_query_ex <= USED 0
lwip_get_dns_max_wait_time <= USED 0
lwip_get_ota_ip6addr_rsra_flag <= USED 0
lwip_get_ota_ip6addr_using_flag <= USED 0
lwip_get_tcp_max_rtx <= USED 0
lwip_get_tcp_rto_syn_factor <= USED 0
lwip_get_tcp_syn_max_rtx <= USED 0
lwip_get_wan_access_web_flag <= USED 0
lwip_lte_require_check <= USED 0
lwip_set_arp_mask_match_flag <= USED 0
lwip_set_dhcp6d_statefull_en <= USED 0
lwip_set_icmp_dont_frag_flag <= USED 0
lwip_set_mac_filter_flag <= USED 0
lwip_set_tcp_msl_val <= USED 0
lwip_set_wan_access_web_flag <= USED 0
net_pool_findall_by_nwif <= USED 0
;FILE lwip_stats.o
_ip_flow_stats_er <= USED 0
get_ip_flow_stats <= USED 0
lte_data_statistic_clear_event <= USED 0
lte_data_statistic_reset <= USED 0
lte_data_statistic_set_event <= USED 0
netif_get_all_packets <= USED 0
netif_get_uap_packets <= USED 0
netif_get_usb_packets <= USED 0
netif_get_wifi_packets <= USED 0
;FILE main.o
AIBAuxInit <= USED 0
Application_Initialize <= USED 0
Config_RF_CONT_MUX <= USED 0
DeleteInitPhase2Task <= USED 0
GetHslL1AcatLogFlag <= USED 0
GetIAptStatus <= USED 0
GetIMLConfigStatus <= USED 0
GetIMLSettingFromNVM <= USED 0
InitSulogConfigToDSP <= USED 0
MSAInit <= USED 0
MSAPhase1Init <= USED 0
SET_DCDC_EN_HIGH <= USED 0
SetHSLPinMux <= USED 0
SetIMLSettingToNVM <= USED 0
YmodemCfgSet <= USED 0
boot_x1_from_ptcm <= USED 0
boot_x1_from_ptcm_new <= USED 0
bspSwResetReasonSet <= USED 0
copy_dsp_from_flash_to_psram <= USED 0
getAptFlagInMsaCfgNvm <= USED 0
getUsbUserMode <= USED 0
isLPMEnabled <= USED 0
nvm_rfs_sync_status <= USED 0
platNvmPmuFlagRead <= USED 0
platformemptyTask_0 <= USED 0
platformemptyTask_1 <= USED 0
platformemptyTask_2 <= USED 0
save_bx_Itcm_to_heap <= USED 0
setAptFlagInMsaCfgNvm <= USED 0
setDiagDevType <= USED 0
setEehDumpDevType <= USED 0
setGLFeatureFlag <= USED 0
setLogSdcardPartitionType <= USED 0
setStatisticValueInAcStatOptionNvm <= USED 0
setSulogDevType <= USED 0
set_dsp_d2_reset_address <= USED 0
;FILE map_client.o
map_client_deinit <= USED 0
map_set_flow_control_mode <= USED 0
;FILE mat_response.o
_MATConfIndCB <= USED 0
_MATGetMaxParamNum <= USED 0
_MATGetSimIdFromSvcId <= USED 0
;FILE mb_motion_comp.o
;FILE mb_utils.o
;FILE mci_audio.o
MCI_AUD_StreamPlay <= USED 0
MCI_AUD_StreamPlayPCM <= USED 0
MCI_AddedData <= USED 0
MCI_AifSetPhoneStatus <= USED 0
MCI_AudioClosePath <= USED 0
MCI_AudioFinished <= USED 0
MCI_AudioGetDurationTime <= USED 0
MCI_AudioGetDurationTime_curplay <= USED 0
MCI_AudioGetFileInformation <= USED 0
MCI_AudioGetOpenedPath <= USED 0
MCI_AudioOpenPath <= USED 0
MCI_AudioPauseTone <= USED 0
MCI_AudioPlayBufferWithBtConnected <= USED 0
MCI_AudioPlayBufferWithType <= USED 0
MCI_AudioPlayLoop <= USED 0
MCI_AudioPlayPause <= USED 0
MCI_AudioPlayResume <= USED 0
MCI_AudioPlayTone <= USED 0
MCI_AudioPlayWithBtConnected <= USED 0
MCI_AudioPlayWithOffloadMode <= USED 0
MCI_AudioPlayWithType <= USED 0
MCI_AudioResumeTone <= USED 0
MCI_AudioSeekTo <= USED 0
MCI_AudioSetMute <= USED 0
MCI_AudioSetPlayInPauseState <= USED 0
MCI_AudioSetVolume_Bt <= USED 0
MCI_AudioStopBuffer <= USED 0
MCI_AudioStopStreaming <= USED 0
MCI_AudioStopTone <= USED 0
MCI_AudioStreamingGetDuration <= USED 0
MCI_AudioStreamingGetHeaderSize <= USED 0
MCI_AudioStreamingIsPlaying <= USED 0
MCI_AudioStreamingPlay <= USED 0
MCI_AudioStreamingPlayWithBtConnected <= USED 0
MCI_AudioStreamingSeek <= USED 0
MCI_AudioStreamingSeekWithBtConnected <= USED 0
MCI_AudioTerminateCurrentStreaming <= USED 0
MCI_DataFinished <= USED 0
MCI_DeviceSetForceUse <= USED 0
MCI_GetBufPosition <= USED 0
MCI_GetRemain <= USED 0
MCI_GetRoute <= USED 0
MCI_GetWriteBuffer <= USED 0
MCI_RingFinished <= USED 0
MCI_SetBuffer <= USED 0
MCI_SetDeviceConnectionState <= USED 0
MCI_SetDeviceMute <= USED 0
MCI_SetVoiceState <= USED 0
MCI_init_btVolLevel_table <= USED 0
_Z15MCI_AudioGetID3Pc <= USED 0
_Z19MCI_GetMciAudioPathv <= USED 0
_Z29MCI_AudioStreamingGetPausePosv <= USED 0
;FILE mci_recorder.o
MCI_AudioBufferRecordStart <= USED 0
MCI_AudioBufferRecordStop <= USED 0
MCI_AudioRecordFinished <= USED 0
MCI_AudioRecordGetRecordInfo <= USED 0
MCI_AudioRecordGetRecordsize <= USED 0
MCI_DisplayVideoInterface <= USED 0
MCI_VideoRecordAdjustSetting <= USED 0
MCI_VideoRecordPause <= USED 0
MCI_VideoRecordPreviewStart <= USED 0
MCI_VideoRecordPreviewStop <= USED 0
MCI_VideoRecordResume <= USED 0
MCI_VideoRecordStart <= USED 0
MCI_VideoRecordStart2 <= USED 0
MCI_VideoRecordStop <= USED 0
_Z24MCI_VideoRecordFinishIndl <= USED 0
;FILE mcu_2_btdm_ipc.o
;FILE mcu_2_gnss_ipc.o
;FILE mcu_address_init.o
;FILE mcu_btdm_ipc.o
McuBtdm_IPC_BIT_7_0_ISR <= USED 0
;FILE mcu_dsp_common.o
WebRtcNetEQ_DSP2MCUinterrupt <= USED 0
;FILE mcu_gnss_ipc.o
McuGnss_IPC_BIT_7_0_ISR <= USED 0
gnss_2_mcu_ipc_write_index_get <= USED 0
mcu_2_gnss_ipc_read_index_get <= USED 0
;FILE mcu_reset.o
;FILE md.o
mbedtls_md_clone <= USED 0
mbedtls_md_get_name <= USED 0
mbedtls_md_info_from_string <= USED 0
mbedtls_md_init_ctx <= USED 0
mbedtls_md_list <= USED 0
;FILE md5.o
mbedtls_md5 <= USED 0
mbedtls_md5_process <= USED 0
;FILE media_format_type.o
_ZN14streamingmedia19transformToMimeTypeE15MediaFormatType <= USED 0
_ZN14streamingmedia23transformToVideoEncoderE15MediaFormatType <= USED 0
;FILE media_log.o
_Z23media_log_filter_removeN7android7String8E <= USED 0
__media_log_print <= USED 0
;FILE memory_recorder.o
Sys_ims_memory_used <= USED 0
release_ims_memory <= USED 0
sys_set_force_dump_mem_use_once <= USED 0
;FILE mfpr.o
mfp_dump <= USED 0
mfp_dump_grp <= USED 0
mfp_init <= USED 0
;FILE min_max.o
max16 <= USED 0
max8 <= USED 0
min16 <= USED 0
min8 <= USED 0
;FILE mm.o
MmTask1 <= USED 0
;FILE mm_api.o
MM_AbortBandScan <= USED 0
MM_GetCfgRpmClr <= USED 0
MM_GetCfgRpmCounter <= USED 0
MM_GetCfgRpmPara <= USED 0
MM_GetCfgRpmSwitch <= USED 0
MM_GetIndStatus <= USED 0
MM_GetLteCurrOperator <= USED 0
MM_GetNASIntegrityCheckFlag <= USED 0
MM_GetRpm <= USED 0
MM_GetTimeZoneReportOption <= USED 0
MM_QuerySuppLteOpeInfo <= USED 0
MM_QuerySuppLteOperator <= USED 0
MM_SetCfgRpmClr <= USED 0
MM_SetCfgRpmPara <= USED 0
MM_SetCfgRpmSwitch <= USED 0
MM_SetLteCurrOperator <= USED 0
MM_SetNASIntegrityCheckFlag <= USED 0
getSavedSignal <= USED 0
getSavedSysMode <= USED 0
getTotalSuppOpeNum <= USED 0
resetTotalSuppOpeNum <= USED 0
;FILE mm_api_mini.o
resetMmParas <= USED 0
;FILE mm_as.o
;FILE mm_mmr.o
MmSendMmrFlushFlashEnd <= USED 0
MmSendMmrFlushFlashReq <= USED 0
;FILE mm_sim.o
;FILE mm_utils.o
MmGetCurrentMobileId <= USED 0
MmIsCallOngoing <= USED 0
MmIsInCUCCNetWork <= USED 0
MmIsIratToLteAllowed <= USED 0
MmIsPendingPlmnSearch <= USED 0
mmEmeiRead <= USED 0
nvm_flush_ps_release <= USED 0
nvm_flush_ps_req <= USED 0
;FILE mns_server.o
;FILE modem_controller.o
modem_init_internal <= USED 0
pppSetInProcessSizeMax <= USED 0
ppp_fallback_control_interval_ticks <= USED 0
ppp_handle_at <= USED 0
ppp_handle_pdp_params <= USED 0
send_creg <= USED 0
;FILE modem_mgr.o
;FILE modem_platform.o
ppp_get_instant_statistics <= USED 0
ppp_get_statistics <= USED 0
ppp_get_statistics_rx <= USED 0
ppp_get_statistics_tx <= USED 0
ppp_in_active_stage <= USED 0
ppp_load_statistics <= USED 0
ppp_save_statistics <= USED 0
ppp_set_connect_flag <= USED 0
wait_for_statistics_timer_cb <= USED 0
;FILE modemlwip.o
dump_lwip_memory_info <= USED 0
ims_get_register_flag <= USED 0
ipnet_downlink_get_allmem <= USED 0
ipnet_downlink_get_usemem <= USED 0
ipnet_uplink_get_allmem <= USED 0
ipnet_uplink_get_usemem <= USED 0
lwipDownMemTest <= USED 0
lwipUpMemTest <= USED 0
lwip_downlink_is_reach_hwm <= USED 0
usb_uplink_is_reach_hwm <= USED 0
;FILE modemvolte.o
;FILE mpu.o
;FILE msg_api.o
MSG_NewMsgAck <= USED 0
MSG_RSTMemFull <= USED 0
libDecodeGsm7BitData <= USED 0
libEncodeGsm7BitData <= USED 0
parseCBM <= USED 0
resetMsgOperFlag <= USED 0
resetMsgParas <= USED 0
;FILE msg_api_mini.o
telMsgIsSmsDataModeOn <= USED 0
;FILE mult_r.o
;FILE multi_irat_common.o
multiIratGetModeReportSourceAppId <= USED 0
multiIratL1GetRAT <= USED 0
;FILE multipart.o
MultiPart_GetDiscardText <= USED 0
;FILE multipart_utils.o
multiPart_FormatDiscardText <= USED 0
;FILE mvUsbDevCh9.o
ch9Class <= USED 0
mvUsbCh9ProcessVendorRequest <= USED 0
;FILE mvUsbDevMain.o
_usb_device_deinit_endpoint <= USED 0
_usb_device_get_handle <= USED 0
_usb_device_get_max_endpoint <= USED 0
_usb_device_get_transfer_details <= USED 0
_usb_device_reset_data_toggle <= USED 0
_usb_device_shutdown <= USED 0
_usb_device_trace_dtd_information <= USED 0
;FILE mvUsbDevRecv.o
;FILE mvUsbDevSend.o
;FILE mvUsbDevUtl.o
_usb_debug_get_flags <= USED 0
_usb_debug_init_trace_log <= USED 0
_usb_debug_print_trace_log <= USED 0
_usb_debug_set_flags <= USED 0
_usb_device_assert_resume <= USED 0
_usb_device_unstall_endpoint <= USED 0
_usb_dump_regs <= USED 0
_usb_ep_status <= USED 0
_usb_regs <= USED 0
_usb_stats <= USED 0
_usb_status <= USED 0
;FILE mvUsbHsDevCncl.o
;FILE mvUsbHsDevMain.o
_usb_dci_vusb20_deinit_endpoint <= USED 0
_usb_dci_vusb20_diag_tx_isr <= USED 0
_usb_dci_vusb20_get_transfer_details <= USED 0
_usb_dci_vusb20_remote_wakeup <= USED 0
_usb_dci_vusb20_reset_ep_data_toggle <= USED 0
_usb_dci_vusb20_shutdown <= USED 0
_usb_dci_vusb20_trace_dtd_information <= USED 0
;FILE mvUsbHsDevUtl.o
_usb_dci_vusb20_assert_resume <= USED 0
_usb_dci_vusb20_reset_data_toggle <= USED 0
;FILE mvUsbLog.o
mvUsbLogGetInfo <= USED 0
mvUsbLogSaveToFileSystem <= USED 0
mvUsbLoggingUninit <= USED 0
;FILE mvUsbMemory.o
mvUsbAvailableMemoryCnt <= USED 0
;FILE mvUsbModem.o
mvUsbModemClearDummyFlag_remotekwakeup <= USED 0
mvUsbSetModemParameters <= USED 0
;FILE mvUsbNet.o
MbimConnectionSpeedChangeNotification <= USED 0
MbimNetworkConnectionNotification <= USED 0
MbimSendRspAvailableNotification <= USED 0
mvUsbCIDRespFromMbim <= USED 0
mvUsbNetMacCmp <= USED 0
mvUsbNetPrintMemInfo <= USED 0
mvUsbSetNetType <= USED 0
;FILE nas_cfg.o
;FILE nd6.o
lwip_dump_ns_ip6_pool <= USED 0
nd6_dump_neighbor_cache_probe <= USED 0
nd6_get_prefix_length <= USED 0
nd6_trigger_neighbor_cache_probe <= USED 0
ns_ip6_pool_get_linkaddr <= USED 0
reset_netif_ipv6_flg_all <= USED 0
reset_netif_ipv6_flg_part <= USED 0
;FILE netIntfListener.o
NetIf_RemoveRouteEx <= USED 0
NetIntf_StartListenOn <= USED 0
NetIntf_StopListenOn <= USED 0
;FILE net_bridge.o
lwip_dump_netmac_table <= USED 0
lwip_dump_stable_nat_table <= USED 0
lwip_set_mac_nwif_table <= USED 0
net_pool_fetch_head <= USED 0
net_pool_fetch_size <= USED 0
;FILE net_pkt.o
;FILE net_sockets.o
mbedtls_net_accept <= USED 0
mbedtls_net_bind <= USED 0
mbedtls_net_connect <= USED 0
mbedtls_net_poll <= USED 0
mbedtls_net_recv_timeout <= USED 0
mbedtls_net_set_block <= USED 0
mbedtls_net_set_nonblock <= USED 0
mbedtls_net_usleep <= USED 0
;FILE netbuf.o
netbuf_alloc <= USED 0
netbuf_chain <= USED 0
netbuf_data <= USED 0
netbuf_first <= USED 0
netbuf_new <= USED 0
netbuf_next <= USED 0
;FILE netdb.o
lwip_get_dns_ttl <= USED 0
lwip_getaddrinfo_with_pcid <= USED 0
lwip_gethostbyname <= USED 0
lwip_gethostbyname_r <= USED 0
lwip_gethostbyname_r_with_netif <= USED 0
lwip_gethostbyname_r_with_pcid <= USED 0
lwip_gethostbyname_with_netif <= USED 0
lwip_gethostbyname_with_pcid <= USED 0
lwip_set_dns_ttl <= USED 0
;FILE neteq_rtp.o
;FILE netif.o
_lwip_set_netif_tune <= USED 0
lwip_build_netif_tune_ifname <= USED 0
lwip_get_netif_pdp_if <= USED 0
lwip_set_netif_tune <= USED 0
netif_check_nw_status <= USED 0
netif_check_nw_status_ip6 <= USED 0
netif_find_by_cid <= USED 0
netif_find_by_name <= USED 0
netif_find_by_scid <= USED 0
netif_find_by_scid_dsim <= USED 0
netif_get_status <= USED 0
netif_ip6_addr_set <= USED 0
netif_ip6_addr_set_parts <= USED 0
netif_local_if_exist <= USED 0
netif_nw_router_check <= USED 0
netif_pc_get_mac <= USED 0
netif_ppp_get_status <= USED 0
netif_ppp_set_status <= USED 0
netif_remove_bypass <= USED 0
netif_remove_config <= USED 0
netif_remove_config_ip6 <= USED 0
netif_set_bypass <= USED 0
netif_set_client_mode <= USED 0
netif_set_hdhcp_mode <= USED 0
netif_set_host_mode <= USED 0
netif_set_link_down <= USED 0
netif_set_mac <= USED 0
netif_set_remove_callback <= USED 0
netif_share_nw_exist <= USED 0
netif_tune_buf_bind <= USED 0
netif_tune_input <= USED 0
netif_tune_output_ipv4 <= USED 0
netif_tune_output_ipv6 <= USED 0
netif_uap_get_mac <= USED 0
netif_uap_get_status <= USED 0
netif_uap_set_status <= USED 0
netif_wifi_status <= USED 0
;FILE netif_pc.o
netifapi_set_pc_macaddr <= USED 0
;FILE netif_ppp.o
netif_ppp_reset_ip <= USED 0
pppnet_input_bypass <= USED 0
;FILE netif_td.o
lte_construct_data_for_lte <= USED 0
lte_dl_mem_free_for_wifi <= USED 0
lwip_preset_pdpif <= USED 0
lwip_set_scid_table_dsim <= USED 0
netif_get_default_nw_ip6addr <= USED 0
netif_td_send_data <= USED 0
td_get_packet <= USED 0
td_get_rx_speed <= USED 0
td_get_tx_speed <= USED 0
;FILE netif_wifi_uap.o
netifapi_set_wifi_macaddr <= USED 0
;FILE netifapi.o
netifapi_netif_add <= USED 0
netifapi_netif_common <= USED 0
netifapi_netif_set_addr <= USED 0
netifapi_netif_set_mac <= USED 0
;FILE netifconfig.o
_Z16EnableInfterfaceb <= USED 0
_Z20addInferfaceBearerIdi <= USED 0
_Z23removeInferfaceBearerIdi <= USED 0
_Z24net_add_ipv4_filter_rulehjtjt <= USED 0
_Z24net_add_ipv6_filter_rulehPhtS_t <= USED 0
_Z25net_remove_ip_filter_rulei <= USED 0
;FILE network_selection.o
;FILE nfc_sdk_drv.o
;FILE ntp.o
GetNtpResult <= USED 0
NTP_Init_Internal <= USED 0
SetNTPStatus <= USED 0
;FILE nv_property.o
NvProp_PrintData <= USED 0
;FILE nvram_api.o
NVRAM_GetInfo <= USED 0
ReadByteValue <= USED 0
ReadRecordInt <= USED 0
ReadRecordIntAsync <= USED 0
ReadShortValue <= USED 0
ReadValueInt <= USED 0
UI_NvramCacheTask <= USED 0
UI_NvramTask <= USED 0
UartCmdLineDisable <= USED 0
UartCmdLineEnable <= USED 0
WriteByteValue <= USED 0
WriteRecordInt <= USED 0
WriteRecordIntAsync <= USED 0
WriteShortValue <= USED 0
WriteValueInt <= USED 0
get_nvram_status <= USED 0
initNvramFolder <= USED 0
isSystemFirstTrunOn <= USED 0
nvram_read_cache <= USED 0
nvram_write_cache <= USED 0
;FILE nvram_core.o
ChangeNvramChecksum <= USED 0
CheckNvramFileCRCIsValid <= USED 0
NvramFileOpen <= USED 0
NvramGetParam <= USED 0
WriteNvramCRCInfo <= USED 0
init_slim_nvram <= USED 0
isSlimNvramFileID <= USED 0
nv_do_factory_reset <= USED 0
nvram_CRC32 <= USED 0
nvram_checksum <= USED 0
nvram_get_read_handle <= USED 0
nvram_get_write_handle <= USED 0
nvram_read <= USED 0
nvram_readv <= USED 0
nvram_write <= USED 0
nvram_writev <= USED 0
;FILE nvram_restore.o
do_delete_file <= USED 0
do_restore_factory <= USED 0
;FILE obex_message_builder.o
obex_message_builder_body_add_static <= USED 0
obex_message_builder_get_message_length <= USED 0
obex_message_builder_request_create_put <= USED 0
obex_message_builder_response_update_code <= USED 0
;FILE obex_parser.o
obex_app_param_parser_init <= USED 0
obex_app_param_parser_process_data <= USED 0
obex_app_param_parser_tag_store <= USED 0
obex_parser_init_for_request <= USED 0
;FILE obex_server.o
;FILE oid.o
mbedtls_oid_get_attr_short_name <= USED 0
mbedtls_oid_get_certificate_policies <= USED 0
mbedtls_oid_get_cipher_alg <= USED 0
mbedtls_oid_get_extended_key_usage <= USED 0
mbedtls_oid_get_md_alg <= USED 0
mbedtls_oid_get_md_hmac <= USED 0
mbedtls_oid_get_numeric_string <= USED 0
mbedtls_oid_get_oid_by_ec_grp <= USED 0
mbedtls_oid_get_oid_by_pk_alg <= USED 0
mbedtls_oid_get_oid_by_sig_alg <= USED 0
mbedtls_oid_get_sig_alg_desc <= USED 0
;FILE ol_ltp.o
;FILE osa_common_init.o
OSAMailboxQCreate <= USED 0
OSAMemPoolCreateExt <= USED 0
OSASemaphoreCreateExt <= USED 0
OsaGetMaxThreadCount <= USED 0
;FILE osa_common_run.o
;FILE osa_mem.o
OsaListAllCreatedMemBytesPoolsStatus <= USED 0
OsaMemAllocAgain <= USED 0
OsaMemCheckPool <= USED 0
OsaMemGetFirstPoolRef <= USED 0
OsaMemGetUserParam <= USED 0
OsaMemPoolsStatus <= USED 0
OsaMemResizeAlloc <= USED 0
OsaMemSetUserParam <= USED 0
OsaMem_InitPools <= USED 0
int_memmory_record <= USED 0
;FILE osa_net_if.o
OSANetif_GetIPv6Info <= USED 0
OSA_Arp_Add <= USED 0
OSA_Arp_Remove <= USED 0
OSA_NetIF_Register_Cb <= USED 0
OSA_NetIf_Details_Get <= USED 0
OSA_NetIf_Details_Set <= USED 0
OSA_NetIf_Disable <= USED 0
OSA_NetIf_Down <= USED 0
OSA_NetIf_Enumerate <= USED 0
OSA_NetIf_Get2AdaptName <= USED 0
OSA_NetIf_Get_IPv4_Address <= USED 0
OSA_NetIf_Init <= USED 0
OSA_NetIf_Is_Interface_Up <= USED 0
OSA_NetIf_Set_IPv4_Address <= USED 0
OSA_NetIf_Set_IPv6_Address <= USED 0
OSA_NetIf_Set_MTU <= USED 0
OSA_NetIf_Shudown <= USED 0
OSA_NetIf_StatusToStr <= USED 0
OSA_NetIf_Status_Get <= USED 0
OSA_NetIf_Up <= USED 0
OSA_Netif_Get_IPv6_Address <= USED 0
OSA_Route_Add <= USED 0
OSA_Route_Remove <= USED 0
;FILE osa_tx_init.o
OsaCriticalSectionDelete <= USED 0
OsaHISRGetPriority <= USED 0
OsaInit <= USED 0
OsaMailboxQCreate <= USED 0
OsaMailboxQDelete <= USED 0
OsaRun <= USED 0
OsaTaskStackMagic <= USED 0
;FILE osa_tx_run.o
OsaContextLockExt <= USED 0
OsaContextRestoreExt <= USED 0
OsaControlInterrupts <= USED 0
OsaMailboxQRecv <= USED 0
OsaMailboxQSend <= USED 0
OsaSemaphoreReleaseExt <= USED 0
OsaTaskYield <= USED 0
Osa_Init <= USED 0
;FILE osa_tx_utils.o
OSAHISRGetAppParam1 <= USED 0
OSAHISRGetEntry <= USED 0
OSAHISRGetName <= USED 0
OSAHISRGetStackSize <= USED 0
OSAHISRGetStackStart <= USED 0
OSAHISRList <= USED 0
OSAHISRSetAppParam1 <= USED 0
OSAMsgQFrontSend <= USED 0
OSAPartitionInUse <= USED 0
OSAPartitionPoolGetAllocated <= USED 0
OSAPartitionPoolGetAvailble <= USED 0
OSAPartitionPoolGetName <= USED 0
OSATaskGetEntry <= USED 0
OSATaskGetEntryParam <= USED 0
OSATaskGetStackSize <= USED 0
OSATaskGetSysParam1 <= USED 0
OSATaskGetSysParam2 <= USED 0
OSATaskList <= USED 0
OSATaskSetSysParam2 <= USED 0
OsaGetCreatedThreadCount <= USED 0
OsaGetThreadListHead <= USED 0
OsaListAllCreatedEventFlags <= USED 0
OsaListAllCreatedMemBlockPools <= USED 0
OsaListAllCreatedMsgQs <= USED 0
OsaListAllCreatedMutexs <= USED 0
OsaListAllCreatedSemaphores <= USED 0
OsaListAllCreatedTasks <= USED 0
OsaListAllCreatedTimers <= USED 0
OsaMailboxQPoll <= USED 0
OsaMsgQFreeRate <= USED 0
OsaThreadList <= USED 0
;FILE other_call.o
isp_set_rawdump_flag <= USED 0
;FILE p_ol_wgh.o
;FILE packet_buffer.o
WebRtcNetEQ_PacketBufferExtract <= USED 0
WebRtcNetEQ_PacketBufferFindLowestTimestamp <= USED 0
WebRtcNetEQ_PacketBufferGetDuration <= USED 0
;FILE packet_util.o
;FILE pap.o
;FILE partition_ops.o
partition_ops_erase <= USED 0
partition_ops_write <= USED 0
;FILE pb_api.o
PB_SQueryRead <= USED 0
PB_SQueryWrite <= USED 0
PB_SWriteEntry <= USED 0
convertCpbwCmdStr <= USED 0
decode_alpha_tag <= USED 0
encode_alpha_tag <= USED 0
encode_character <= USED 0
libGetSubaddrInfo <= USED 0
resetPbParas <= USED 0
;FILE pb_api_mini.o
initPbParas <= USED 0
;FILE pbap_client.o
pbap_pull_vcard_entry <= USED 0
pbap_pull_vcard_listing <= USED 0
pbap_set_property_selector <= USED 0
pbap_set_vcard_selector <= USED 0
pbap_set_vcard_selector_operator <= USED 0
;FILE pbuf.o
lwip_ip_in_pbuf_alloc <= USED 0
lwip_wifi_in_buf_alloc <= USED 0
lwip_wifi_in_buf_free <= USED 0
lwip_wifi_in_buf_pmsg <= USED 0
pbuf_add_header <= USED 0
pbuf_coalesce <= USED 0
pbuf_dechain <= USED 0
pbuf_fill_chksum <= USED 0
pbuf_get_at <= USED 0
pbuf_memcmp <= USED 0
pbuf_memfind <= USED 0
pbuf_strstr <= USED 0
pbuf_take <= USED 0
;FILE pcscf_discovery.o
;FILE pdcpgki.o
;FILE pem.o
;FILE ph_disp.o
;FILE pin_cfg.o
BTPinConfig <= USED 0
CameraFlashPinmuxcfg <= USED 0
CameraI2CPinmuxCfg <= USED 0
Camera_Pinmuxcfg <= USED 0
ChargerStsEvb1601_Pm802PinmuxCfg <= USED 0
Dump_LCD_Pinmuxcfg <= USED 0
EarphonePinConfig <= USED 0
FmI2CPinConfig <= USED 0
KeypadPinmuxCfg <= USED 0
LCD_Pinmuxcfg <= USED 0
;FILE ping.o
api_lwip_do_ping <= USED 0
api_lwip_do_ping2 <= USED 0
api_lwip_do_ping3 <= USED 0
api_lwip_do_ping_ex <= USED 0
api_lwip_stop_ping <= USED 0
ping_send_msgq <= USED 0
;FILE pitch_fr.o
;FILE pitch_ol.o
_Z15Lag_max_wrapperP9vadState1PiPssssssS2_iS1_ <= USED 0
;FILE pk.o
mbedtls_pk_check_pair <= USED 0
mbedtls_pk_debug <= USED 0
mbedtls_pk_get_name <= USED 0
mbedtls_pk_init <= USED 0
mbedtls_pk_setup_rsa_alt <= USED 0
;FILE pkparse.o
mbedtls_pk_parse_key <= USED 0
mbedtls_pk_parse_public_key <= USED 0
;FILE platform.o
PlatformCSDEnable <= USED 0
PlatformChargeIsEnable <= USED 0
PlatformGetI2CType <= USED 0
PlatformGetPMICType <= USED 0
PlatformI2CInit <= USED 0
PlatformIsLtgVersion <= USED 0
PlatformIsMinSystem <= USED 0
PlatformIsNezhaC <= USED 0
PlatformIsSDKVersion <= USED 0
PlatformIsTPMifi <= USED 0
PlatformIsWukong <= USED 0
PlatformKeypadIsEnable <= USED 0
PlatformOledIsEnable <= USED 0
PlatformRTCIsEnable <= USED 0
PlatformSDCardEnable <= USED 0
PlatformSetCustomData <= USED 0
PlatformSetPMICType <= USED 0
PlatformWapiIsEnable <= USED 0
PlatformWebIsEnable <= USED 0
PlatformWifiIsEnable <= USED 0
Platform_sdio_config_pin <= USED 0
mbedtls_platform_set_calloc_free <= USED 0
mbedtls_platform_setup <= USED 0
mbedtls_platform_teardown <= USED 0
;FILE platform_intf.o
;FILE platform_nvm.o
PlatformNvm_CreateAciCfgFile <= USED 0
PlatformNvm_Init <= USED 0
PlatformNvm_setUARTroute <= USED 0
hal_factory_xf_partition_erase <= USED 0
hal_factory_xf_partition_read <= USED 0
hal_factory_xf_partition_write <= USED 0
;FILE platform_pmu.o
;FILE platform_shim_api.o
BipCmdTaskInit <= USED 0
CsdQueryTestStatus <= USED 0
CsdTurnOffTest <= USED 0
CsdTurnOnTest <= USED 0
EraseFlash <= USED 0
FAT_Format <= USED 0
FAT_GetCurrentDir <= USED 0
FAT_Getfree <= USED 0
FAT_IsFormatted <= USED 0
FAT_MakeDir <= USED 0
FAT_MakeDir_M <= USED 0
FAT_RemoveDir <= USED 0
FAT_SetCurrentDir <= USED 0
FAT_eof <= USED 0
FAT_findfileclose <= USED 0
FAT_findfirst <= USED 0
FAT_findnext <= USED 0
FAT_rename <= USED 0
FDI_Factory_Reset <= USED 0
GetBacklightStatusBeforeReset <= USED 0
GetCurrentCompTime <= USED 0
GetFlashLayoutConfig <= USED 0
GetLocalTime <= USED 0
GetWiFiType <= USED 0
GetWifiStatus <= USED 0
Get_backtime_frompsm <= USED 0
Giga_Disable4BytesMode <= USED 0
Giga_Enable4BytesMode <= USED 0
GuilinLite2_PowerSave_Config <= USED 0
Guilin_PowerSave_Config <= USED 0
IsGsmRatForMIFI <= USED 0
Is_dedicateAPN <= USED 0
LteConstructSnDataNodeEForMifi <= USED 0
NTP_Init <= USED 0
NingboKeypadBackLightDisable <= USED 0
NingboKeypadBackLightEnable <= USED 0
NingboLcdBackLightCtrl <= USED 0
NingboLcdBackLightDisable <= USED 0
NingboLcdBackLightEnable <= USED 0
NingboTorchBackLightCtrl <= USED 0
NingboTorchLightDisable <= USED 0
NingboTorchLightEnable <= USED 0
NingboVibratorCtrl <= USED 0
NingboVibratorDisable <= USED 0
NingboVibratorEnable <= USED 0
Ningbo_ENABLE_BATID_MEAS <= USED 0
Ningbo_ENABLE_BATTEMP_MEAS <= USED 0
Ningbo_Get_Meas_Factor <= USED 0
Ningbo_INT_CALLBACK_REGISTER <= USED 0
Ningbo_INT_ENABLE <= USED 0
Ningbo_LDO_Set_Enable <= USED 0
Ningbo_LDO_Set_VOUT <= USED 0
Ningbo_Ldo_11_set <= USED 0
Ningbo_Ldo_11_set_1_2 <= USED 0
Ningbo_Ldo_12_set <= USED 0
Ningbo_Ldo_12_set_1_8 <= USED 0
Ningbo_Ldo_12_set_2_8 <= USED 0
Ningbo_Ldo_12_set_3_3 <= USED 0
Ningbo_Ldo_1_set <= USED 0
Ningbo_Ldo_1_set_2_8 <= USED 0
Ningbo_PowerSave_Config <= USED 0
OLED_WIFITransferStatusChange <= USED 0
OsaTimerExcludedUnRegister <= USED 0
PM812_BATTERY_BIND_INTC <= USED 0
PM812_BATTERY_INTC_ENABLE <= USED 0
PM812_BATTERY_STATUS <= USED 0
PM812_CHARGER_BIND_INTC <= USED 0
PM812_CHARGER_INTC_ENABLE <= USED 0
PM812_ENABLE_BAT_DET <= USED 0
Rdisk_FlashRead <= USED 0
ReadFlash <= USED 0
SaveCalDataFromMfg <= USED 0
SulogSDMakeDir <= USED 0
Update_RTC_Time <= USED 0
VgRtfdpTask <= USED 0
VgUfdpTask <= USED 0
WIFI_Sendto_Wapi <= USED 0
WIFI_Sendto_Wps <= USED 0
WriteFlash <= USED 0
asr_gnss_open_stub <= USED 0
cmux_startup <= USED 0
cmux_write <= USED 0
eeSDDumpPrepare <= USED 0
eeSDDump_status_get <= USED 0
emac_start_xmit <= USED 0
embms_data_input <= USED 0
getUSBConnectStatus <= USED 0
get_d2_nvm_flush <= USED 0
get_download_flag_for_tigx <= USED 0
get_log_sd_unused <= USED 0
get_network_mode <= USED 0
get_uart2_baudrate_for_tigx <= USED 0
get_wifi_mac <= USED 0
initMESmsInfo <= USED 0
init_atnet <= USED 0
init_sgLed <= USED 0
isAutoApn <= USED 0
is_aes_cipher <= USED 0
is_uart_to_uart <= USED 0
lwip_clat_config_ex <= USED 0
lwip_clat_ip4route <= USED 0
mbim_rx_cb <= USED 0
mbim_rx_from_lwip <= USED 0
modem_init <= USED 0
mp3Start <= USED 0
netif_send_to_wifi_uap_nocpy <= USED 0
pm813_cc_current_set <= USED 0
pm813_cccv_timer_set <= USED 0
pm813_charge_termination_current_set <= USED 0
pm813_charger_is_usb <= USED 0
pm813_fault_clear <= USED 0
pm813_get_Icharge_meas_cur_mA <= USED 0
pm813_get_Vcharge_meas_vol_mv <= USED 0
pm813_get_batid_meas_vol_mv <= USED 0
pm813_get_battemp_meas_vol_mv <= USED 0
pm813_get_battery_status <= USED 0
pm813_if_charge_in_cv_mode <= USED 0
pm813_keypadbacklight_status_get <= USED 0
pm813_lcdbacklight_status_get <= USED 0
pm813_measured_current_means_charging <= USED 0
pm813_precharge_current_set <= USED 0
pm813_precharge_timer_set <= USED 0
pm813_set_max_cc_current <= USED 0
pm813_torchlight_status_get <= USED 0
pm813_trickle_timer_set <= USED 0
pm813_vbat_set <= USED 0
pm813_vibrator_status_get <= USED 0
pm813s_mppt_restart <= USED 0
processWifiHotspotInfoInd <= USED 0
psNvmCheckAPNInfo <= USED 0
record_flush_write <= USED 0
sdcard_fat_is_ok <= USED 0
sdcard_get_blknum <= USED 0
sdcard_init <= USED 0
sdcard_is_ready <= USED 0
sdcard_need_format <= USED 0
sdcard_not_need_format <= USED 0
sdcard_read_multiblks <= USED 0
sdcard_read_singleblk <= USED 0
sdcard_transmit_status <= USED 0
sdcard_write_multiblks <= USED 0
sdcard_write_singleblk <= USED 0
setUartDevType <= USED 0
set_download_flag_for_tigx <= USED 0
set_platform_version <= USED 0
set_sdcard_power_gpio <= USED 0
set_sg_led_status <= USED 0
set_uart2_baudrate_for_tigx <= USED 0
set_uart_dev_diag <= USED 0
set_uart_dev_uart <= USED 0
set_uart_selfadapt <= USED 0
uap_packet_free <= USED 0
usb_net_init <= USED 0
usb_net_rx <= USED 0
usb_net_tx <= USED 0
vgRlpSetRlpInitiateXidNeg <= USED 0
wan_delete_device <= USED 0
watchdog_reset <= USED 0
woal_uap_do_ioctl <= USED 0
;FILE platform_util.o
;FILE plms.o
PlmsCheckIfFgPlmnOngoing <= USED 0
PlmsCheckIsChina <= USED 0
PlmsConvertMibBW <= USED 0
PlmsPrintCheckSearchInfoDb <= USED 0
PlmsSetOptimizations <= USED 0
PlmsTask1 <= USED 0
;FILE plmsdb.o
PlmsDbInitInterferenceDetection <= USED 0
PlmsDbStoreJammingDetectionParams <= USED 0
PlmsStoreSeqNum <= USED 0
;FILE plmsfnd.o
PlmsFndCalcInterferedCarriersNum <= USED 0
PlmsFndExcludeEarfcnFromSearch <= USED 0
;FILE plmsscn.o
PlmsGetOperator <= USED 0
PlmsLogResultTable <= USED 0
PlmsScnAllocationWbScoreResultTable <= USED 0
PlmsScnCheckIfArfcnBelongToRequestPlmn <= USED 0
PlmsScnDetermineL1ForFGRssi <= USED 0
PlmsScoreCompare <= USED 0
;FILE plmssi.o
;FILE pm.o
;FILE pm_debug.o
MeasureFreq <= USED 0
PMLogICATDisplay <= USED 0
PMLogStartCyclic <= USED 0
PMLogStartOneshot <= USED 0
PMLogStop <= USED 0
ReConfigureTestTimer1 <= USED 0
ReConfigureTestTimer2 <= USED 0
callbackTestTimer1 <= USED 0
callbackTestTimer2 <= USED 0
pmLogInit <= USED 0
;FILE pmic.o
Camera_Avdd_Power_set <= USED 0
Camera_Dvdd_Power_set <= USED 0
Camera_IOvdd_Power_set <= USED 0
I2CDeviceCheckStatus <= USED 0
I2CRecordDeviceStatus <= USED 0
PM812SetLDO3OffOnSleep <= USED 0
PM812_32K_OUT2_Enable <= USED 0
PM812_APT_enable <= USED 0
PM812_BATTERY_IS_DETECTED <= USED 0
PM812_BD_DISABLE <= USED 0
PM812_BD_ENABLE <= USED 0
PM812_BIAS_DISABLE <= USED 0
PM812_BIAS_ENABLE <= USED 0
PM812_BIAS_OUT_OFF <= USED 0
PM812_BIAS_OUT_ON <= USED 0
PM812_BUCK5_Enable <= USED 0
PM812_Buck1_Config <= USED 0
PM812_Buck2_Config <= USED 0
PM812_DELAY_SW_PDOWN <= USED 0
PM812_DUMP_STATE <= USED 0
PM812_GET_LONKEY_PRESS_TIME <= USED 0
PM812_GET_POWER_DOWN_REASON <= USED 0
PM812_GET_POWER_DOWN_REASON2 <= USED 0
PM812_GET_POWER_UP_REASON <= USED 0
PM812_GET_VCHG_LOW_TH <= USED 0
PM812_GET_VCHG_UPP_TH <= USED 0
PM812_GPADC_NONSTOP_TRIG_DISABLE <= USED 0
PM812_GPADC_NONSTOP_TRIG_ENABLE <= USED 0
PM812_GPADC_READ_VOL_MEAS <= USED 0
PM812_GPADC_SW_TRIG <= USED 0
PM812_INT_CALLBACK_REGISTER <= USED 0
PM812_INT_DISABLE <= USED 0
PM812_INT_ENABLE <= USED 0
PM812_LONKEY_ENABLE_GET <= USED 0
PM812_LONKEY_ENABLE_SET <= USED 0
PM812_Ldo_10_set <= USED 0
PM812_Ldo_10_set_1_8 <= USED 0
PM812_Ldo_10_set_3_0 <= USED 0
PM812_Ldo_11_set <= USED 0
PM812_Ldo_11_set_2_8 <= USED 0
PM812_Ldo_12_set <= USED 0
PM812_Ldo_12_set_1_8 <= USED 0
PM812_Ldo_12_set_2_8 <= USED 0
PM812_Ldo_12_set_3_0 <= USED 0
PM812_Ldo_13_set <= USED 0
PM812_Ldo_13_set_2_8 <= USED 0
PM812_Ldo_15_set <= USED 0
PM812_Ldo_15_set_2_8 <= USED 0
PM812_Ldo_16_set <= USED 0
PM812_Ldo_16_set_2_8 <= USED 0
PM812_Ldo_17_set <= USED 0
PM812_Ldo_17_set_2_8 <= USED 0
PM812_Ldo_18_set <= USED 0
PM812_Ldo_18_set_1_8 <= USED 0
PM812_Ldo_19_Config <= USED 0
PM812_Ldo_3_set <= USED 0
PM812_Ldo_3_set_2_8 <= USED 0
PM812_Ldo_4_set <= USED 0
PM812_Ldo_4_set_1_8 <= USED 0
PM812_Ldo_4_set_3_0 <= USED 0
PM812_Ldo_5_set <= USED 0
PM812_Ldo_5_set_3_1 <= USED 0
PM812_Ldo_6_set <= USED 0
PM812_Ldo_6_set_1_8 <= USED 0
PM812_Ldo_6_set_2_8 <= USED 0
PM812_Ldo_8_set <= USED 0
PM812_Ldo_8_set_2_8 <= USED 0
PM812_Ldo_9_set <= USED 0
PM812_Ldo_9_set_1_8 <= USED 0
PM812_MEAS_DISABLE <= USED 0
PM812_MEAS_ENABLE <= USED 0
PM812_ONKEY_IS_DETECTED <= USED 0
PM812_PowerSave_Config <= USED 0
PM812_PwmSetLevel <= USED 0
PM812_REG_DUMP <= USED 0
PM812_RESET_ALL <= USED 0
PM812_RTC_SET_CONTROL_REG <= USED 0
PM812_RTC_USE_XO <= USED 0
PM812_SET_LONKEY_PRESS_TIME <= USED 0
PM812_SET_VCHG_LOW_TH <= USED 0
PM812_SET_VCHG_UPP_TH <= USED 0
PM812_SET_WD_TIMEOUT_ACT <= USED 0
PM812_SW_PDOWN_SLT <= USED 0
PM812_VBUCK1_CFG <= USED 0
PM812_VBUCK2_CFG <= USED 0
PM812_VBUS_IS_AVAILABLE <= USED 0
PM812_VLDO7_SWITCH <= USED 0
PM812_WD_ENABLE <= USED 0
PM812_WD_MODE <= USED 0
PM812_WD_RESET <= USED 0
PM812_bk4clkfll_set_2m <= USED 0
PM812_vbuck1_set_fpwm <= USED 0
PM812_vbuck3_set_1_25 <= USED 0
PM812_vbuck4_set_1_85 <= USED 0
PM812_vbuck4_set_2_00 <= USED 0
PM812_vbuck4_set_fpwm <= USED 0
PMCGetGPADCValue <= USED 0
PMCNotifyEventBind <= USED 0
PMCWriteRegister <= USED 0
PMIC_IS_PM812 <= USED 0
PMIC_IS_PM813S_A0 <= USED 0
PMIC_IS_PM813_A2 <= USED 0
PMIC_IS_PM813_A3 <= USED 0
PMIC_Phase1Init <= USED 0
PMIC_PowerDown <= USED 0
PMIC_READ_REG_AUDIO <= USED 0
PMIC_READ_REG_TEST <= USED 0
PMIC_RTC_GET_EXPIR_1_ENABLE_STATE <= USED 0
PMIC_Vbat <= USED 0
PMIC_WRITE_REG_AUDIO <= USED 0
PMIC_WRITE_REG_TEST <= USED 0
PMIC_readVbat <= USED 0
PlatformVcoreConfigForDro <= USED 0
SysRestartReasonGetGlobal <= USED 0
isLcdEverInitedUponBooting <= USED 0
isSysRestartByAlarm <= USED 0
isSysRestartByCharging <= USED 0
isSysRestartByError <= USED 0
isSysRestartByNormal <= USED 0
pmic_lcdbacklight_current_status_get <= USED 0
pmic_lcdbacklight_level_get <= USED 0
pmic_set_sys_restart_reason <= USED 0
pmic_user_defined_flags_get <= USED 0
pmic_user_defined_flags_set <= USED 0
pmic_userdata_get_app_lcd_ever_configed <= USED 0
pmic_userdata_set_app_lcd_ever_configed <= USED 0
set_hp1n2 <= USED 0
usticaInit <= USED 0
ustica_I2CEnableclockandPin <= USED 0
ustica_I2CInit <= USED 0
ustica_USB_shutdown <= USED 0
ustica_USB_turnOn <= USED 0
ustica_vbuck1_0_set <= USED 0
;FILE pmic_drv.o
GetBacklightStatusBeforeSilentreset <= USED 0
GuilinLcdBackLightStatusRecord <= USED 0
GuilinRead <= USED 0
GuilinWrite <= USED 0
Guilin_Ldo_1_set <= USED 0
Guilin_Ldo_1_set_2_8 <= USED 0
Guilin_Ldo_6_set <= USED 0
Guilin_Ldo_6_set_2_8 <= USED 0
Lcd_Mipi_Avdd12_On <= USED 0
PM802_BATTERY_GET_VBAT_MEAS_VOL_MV <= USED 0
PM802_LcdBacklight_Status_Get <= USED 0
PM813_get_Vbus_ov_uv_status <= USED 0
PMIC_LONKEY_EN <= USED 0
PMIC_LONKEY_PRESS_TIME_SET <= USED 0
PmicLcdBackLightCtrl <= USED 0
PmicLcdBackLightDisable <= USED 0
PmicLcdBackLightEnable <= USED 0
PmicTorchLightCtrl <= USED 0
PmicTorchLightDisable <= USED 0
PmicTorchLightEnable <= USED 0
PmicVibrateDisable <= USED 0
PmicVibrateEnable <= USED 0
PmicVibrateSetLevel <= USED 0
Pmic_get_battemp_kohm <= USED 0
Pmic_is_pm803L <= USED 0
Pmic_is_pm812 <= USED 0
Pmic_is_pm813_a1 <= USED 0
Pmic_is_pm813_a2 <= USED 0
Pmic_is_pm813_a3 <= USED 0
PmickeypadbacklightDisable <= USED 0
PmickeypadbacklightEnable <= USED 0
bt_fm_power_on <= USED 0
camera_ldo_cfg <= USED 0
charger_is_usb <= USED 0
fm_pmic_power_down <= USED 0
fm_pmic_power_up <= USED 0
fm_rda5802_power_up <= USED 0
get_BackLightStatus <= USED 0
get_pmic_keypadbacklight_status <= USED 0
get_pmic_lcdbacklight_status <= USED 0
get_pmic_torchlight_status <= USED 0
get_pmic_vibrator_status <= USED 0
get_usb_connect_status <= USED 0
isLcdEverInitedUponBoot <= USED 0
isRestartByCharger <= USED 0
lcd_pmic_power_up <= USED 0
pm812_get_battert_status <= USED 0
pm812_get_charger_status <= USED 0
pm813_a3_get_charger_cur_mA <= USED 0
pm813_auto_set_max_cc_current <= USED 0
pm813_battery_current_direction <= USED 0
pm813_charger_simulate_out_in <= USED 0
pm813_get_batid_meas_voltage_mv <= USED 0
pm813_get_battemp_meas__voltage_mv <= USED 0
pm813_get_batteryId_vol <= USED 0
pm813_get_charger_cv_mode <= USED 0
pm813_get_single_vbat <= USED 0
pm813_get_tint <= USED 0
pm813_get_vpwr <= USED 0
pm813s_get_Icharge_meas_cur_mA <= USED 0
pm813s_get_charger_cur_mA <= USED 0
pm813s_set_IR_compensate <= USED 0
pm813s_set_charge_termination_current <= USED 0
pm813s_set_mppt_max_current <= USED 0
pmic_cc_current_set <= USED 0
pmic_cccv_timer_set <= USED 0
pmic_charge_termination_current_set <= USED 0
pmic_charger_simulate_out_in <= USED 0
pmic_fault_clear <= USED 0
pmic_get_powerdown_reason <= USED 0
pmic_get_powerup_reason <= USED 0
pmic_get_single_vbat <= USED 0
pmic_get_tint <= USED 0
pmic_get_tp_x <= USED 0
pmic_get_tp_y <= USED 0
pmic_get_vbus_vol <= USED 0
pmic_get_vpwr <= USED 0
pmic_ldo_disable <= USED 0
pmic_ldo_enable <= USED 0
pmic_onkey_is_detected <= USED 0
pmic_power_down <= USED 0
pmic_precharge_current_set <= USED 0
pmic_precharge_timer_set <= USED 0
pmic_set_IR_compensate <= USED 0
pmic_set_ldo_vol <= USED 0
pmic_set_mppt_max_current <= USED 0
pmic_sw_reset <= USED 0
pmic_tp_gpadc_enable <= USED 0
pmic_trickle_timer_set <= USED 0
pmic_userdata_reg_clear <= USED 0
pmic_userdata_reg_read <= USED 0
pmic_userdata_reg_write <= USED 0
pmic_vbat_set <= USED 0
pmic_wd_kick <= USED 0
pmic_wdg_disable <= USED 0
pmic_wdg_enable <= USED 0
set_BackLightStatus <= USED 0
startup_is_silent_reset <= USED 0
;FILE pmic_onkey.o
ONKey_system_poweroff <= USED 0
OnKeyPoweroffDone <= USED 0
OnKeyPoweroffStateGet <= USED 0
OnKeyPoweroff_Prepare <= USED 0
OnKeyPoweroff_Start <= USED 0
PM812_GET_ONKEY_STATUS <= USED 0
PM812_LONKEY_1_EN_16s <= USED 0
PM812_LONKEY_EN <= USED 0
PM812_LONKEY_PRESS_TIME_SET <= USED 0
PM812_ONKEY_BIND_INTC <= USED 0
PM812_ONKEY_DUMP <= USED 0
PM812_ONKEY_INTC_ENABLE <= USED 0
PM812_onkey_int_callback <= USED 0
PM812_onkey_int_test <= USED 0
PM812_onkey_int_test_callback <= USED 0
Poweroff_disable_Int <= USED 0
SysPowerOffInit <= USED 0
;FILE pmic_rtc.o
ContinuousRTCGetTimeSecond <= USED 0
PMIC_RTC_GetTime_Count_Without_Timezone <= USED 0
PMIC_RTC_GetUTCTime <= USED 0
PMIC_RTC_IS_ALARM_POWERUP <= USED 0
PMIC_RTC_IS_SYS_RTC_PEER_SYNCED <= USED 0
PMIC_RTC_init <= USED 0
PMIC_RTC_test_0 <= USED 0
PMIC_RTC_test_1 <= USED 0
PMIC_RTC_test_1_callback <= USED 0
PMIC_RTC_test_2 <= USED 0
PMIC_RTC_test_3 <= USED 0
PMIC_RTC_test_3_callback <= USED 0
PMIC_RTC_test_app_set <= USED 0
PMIC_RTC_test_app_synced_with_sys <= USED 0
PMIC_RTC_test_sys_set <= USED 0
PMIC_RTC_tm_to_str <= USED 0
pmic_rtc_INIT_FLAG_SET <= USED 0
pmic_rtc_offset_storage_type_detect <= USED 0
pmic_rtc_print_tm <= USED 0
pmic_rtc_setting_dump <= USED 0
pmic_userdata_unset_app_rtc_ever_configed <= USED 0
try_time_func <= USED 0
;FILE pmic_wdt.o
PMIC_WD_ENABLE <= USED 0
PMIC_WD_KICK <= USED 0
PMIC_WD_TIMER_SET <= USED 0
test_PMIC_WD_DISABLE <= USED 0
test_PMIC_WD_ENABLE <= USED 0
test_PMIC_WD_KICK <= USED 0
test_PMIC_WD_TIMEOUT_16s <= USED 0
test_PMIC_WD_TIMEOUT_32s <= USED 0
test_PMIC_WD_TIMEOUT_64s <= USED 0
;FILE pmu_stub.o
PMUDSPReset <= USED 0
;FILE pmu_test.o
test_battery_temperature <= USED 0
test_charger <= USED 0
;FILE post_filter.o
PostFilter <= USED 0
;FILE post_pro.o
;FILE post_proc_semaphore.o
;FILE pow2.o
;FILE power_charger.o
ChargerManager <= USED 0
Charger_check_State <= USED 0
Get_Battery_temp <= USED 0
IS_charger_high_temperature_abnomal <= USED 0
PM812_BATTERY_Handler <= USED 0
SetBatteryStatusHandler <= USED 0
battery_voltage_rise_up <= USED 0
charger_dump_config <= USED 0
get_battery_full_status <= USED 0
pmic_BatteryLevelToPercent <= USED 0
pmic_ChargerChargingState <= USED 0
pmic_GetBatteryLevel <= USED 0
pmic_GetBatteryTemperature <= USED 0
pmic_GetChargerStatus <= USED 0
pmic_GetGpadcBatteryLevel <= USED 0
pmic_GetPowerDownVolt <= USED 0
pmic_InitCharger <= USED 0
pmic_SetChargerStatusHandler <= USED 0
pmic_set_mppt <= USED 0
;FILE power_mmi.o
ChangeVoltageToBatteryRate <= USED 0
ChargerCallbackRegister <= USED 0
GetAverageBatteryLevel <= USED 0
IsBatteryInCharge <= USED 0
PM_GetBatteryInfo <= USED 0
PM_GetBatteryState <= USED 0
PM_GetGpadcValue <= USED 0
charger_get_Time_To_MMI_Evt <= USED 0
get_true_bat <= USED 0
pm_BattChargeRemind <= USED 0
pm_BatteryInit <= USED 0
pm_BatteryMonitorProc <= USED 0
pm_ChargerHandler <= USED 0
pm_Charger_check_XF <= USED 0
pm_ForceUpdateChargerStatus <= USED 0
pm_GetUsbDownLoadStatus <= USED 0
save_battery_data <= USED 0
;FILE power_nvm.o
IschargerDebugEn <= USED 0
chargerCfgGetSetting <= USED 0
;FILE pp_semaphore_chroma_inter.o
;FILE pp_semaphore_luma.o
;FILE ppp.o
PppRelayMessageFromComm <= USED 0
setup_callback_funcs <= USED 0
;FILE ppp_platform.o
ppp_init_mutex <= USED 0
ppp_lock_mutex <= USED 0
ppp_set_trace <= USED 0
ppp_unlock_mutex <= USED 0
;FILE pre_big.o
;FILE pre_proc.o
;FILE pred_lt.o
;FILE preemph.o
;FILE prm2bits.o
;FILE prm_hrbl.o
GetExceptionSrvc <= USED 0
PRMResourceStatusGet <= USED 0
PRMSWReset <= USED 0
PRMServiceFreqGet <= USED 0
PRMServiceFreqSet <= USED 0
PRMTurnOnAndIgnoreLpmSrvc <= USED 0
SetExceptionSrvc <= USED 0
prmFreqRetCode <= USED 0
prmRetCode <= USED 0
prmSetResourceState <= USED 0
;FILE process_dialogmsg.o
sipDM_GetDialogInState <= USED 0
;FILE property.o
Prop_Release <= USED 0
;FILE ps_api.o
PS_Get4GSysInfoRegStatus <= USED 0
PS_GetCapsVoiceMode <= USED 0
PS_GetQualityOfServiceList <= USED 0
PS_GetSysGPRSRegStatus <= USED 0
PS_GetTFTCapsREQ <= USED 0
PS_SetGsmGprsClass <= USED 0
PS_SetPsPlusPaging <= USED 0
PS_SetQualityOfService <= USED 0
addToList <= USED 0
atcmdSimStatusChanged <= USED 0
clearErrorIndFlagAll <= USED 0
deactive_ps_connection <= USED 0
getCeregStatus <= USED 0
getCgregStatus <= USED 0
getPSNwReg <= USED 0
getQueryTftCtxInProcess <= USED 0
getSimIdForPS <= USED 0
loadPSRegOptionSetting <= USED 0
parseAddr <= USED 0
processGetSysInfoCnf <= USED 0
removeNodeByCid <= USED 0
resetCidList <= USED 0
resetPsParas <= USED 0
searchListByCid <= USED 0
sendDefineDefaultPdpContext <= USED 0
setupNetworkCbs <= USED 0
showPDP <= USED 0
subnetmask_str2len <= USED 0
telGetDefaultPdpApn <= USED 0
telGetPdpCtxAuth <= USED 0
telGetPdpCtxAuthDS <= USED 0
telGetPdpInfo <= USED 0
telMatchDefaultApn <= USED 0
updateCregReady <= USED 0
updateRoamingStatus <= USED 0
;FILE ps_init.o
ICATexportCipheringInfo <= USED 0
ICATtestCipheringInfo <= USED 0
PSInformationDelete <= USED 0
ipcTriggerInit <= USED 0
psInit_PS_INIT_ACAT <= USED 0
psKiOsMaximumSleep <= USED 0
psKiOsTick <= USED 0
psNvmAccessIs_ReducedOnlyCfun0Cfun1 <= USED 0
setAttachAtPwrOn <= USED 0
setNetworkMode <= USED 0
setServiceType <= USED 0
setTxInitPower <= USED 0
setUserMode <= USED 0
;FILE ps_itcm.o
LteHandlePdicHisrCh0 <= USED 0
LteHandlePdicHisrCh0AboveY0 <= USED 0
;FILE ps_nasnoncache.o
AbNonCacheCheckisRplmnCampOn <= USED 0
AbNonCacheContextIsPsmReCovery <= USED 0
AbNonCacheContextIsSilentReCovery <= USED 0
AbNonCacheGetRecovryFlag <= USED 0
AbNonCacheSetRecovryFlag <= USED 0
MmNonCacheGetRecovryState <= USED 0
MmNonCacheSetRecovryState <= USED 0
NasNonCacheSimNetworkDataContextStore <= USED 0
NasNonCacheSimNetworkDataTrace <= USED 0
;FILE ps_nvm.o
psNvmAciGkiIsWorking <= USED 0
psNvmCreate <= USED 0
psNvmCreate2GROMING <= USED 0
psNvmCreateATT <= USED 0
psNvmCreateCMCC <= USED 0
psNvmCreateCTA <= USED 0
psNvmCreateH3G <= USED 0
psNvmCreateIMSNWReportInfo <= USED 0
psNvmCreateMANUFACTURESILVER <= USED 0
psNvmCreateNull <= USED 0
psNvmCreateOptVersion <= USED 0
psNvmCreateTELCE <= USED 0
psNvmCreateTermProfile <= USED 0
psNvmCreateVDF <= USED 0
psNvmCreateVS <= USED 0
psNvmEnableDisablePsInitAuto <= USED 0
psNvmGetIMSNWReportInfo <= USED 0
psNvmReadIMSNWReportInfo <= USED 0
psNvmReadVendorSpecificInfo <= USED 0
psNvmSetIMSNWReportNvmDefaultParams <= USED 0
psNvmVendorSpecificEnabled <= USED 0
psVendorPrintf <= USED 0
;FILE ps_psm.o
NasNonCacheContexInitPsmContex <= USED 0
PsmErrcInitContext <= USED 0
PsmInitAllContext <= USED 0
PsmInitPsmContext <= USED 0
PsmNasUsimInitContext <= USED 0
saveContext <= USED 0
saveContextForPowerOffOnKey <= USED 0
systemHotResetInit <= USED 0
;FILE psm.o
psm_close <= USED 0
psm_get_int <= USED 0
psm_get_namespace <= USED 0
psm_pop_subspace <= USED 0
psm_safe_set <= USED 0
psm_set_int <= USED 0
;FILE psm_wrapper.o
clear_need_ascii_to_html <= USED 0
set_need_ascii_to_html <= USED 0
;FILE psram.o
crane_psram_init <= USED 0
psram_wr_testmode <= USED 0
psram_write_testmode <= USED 0
wb32Mb_psram_fast_refresh <= USED 0
wb32Mb_psram_halfsleep_config <= USED 0
winbond_psram_fast_refresh_entry <= USED 0
winbond_psram_fast_refresh_entry_test <= USED 0
winbond_psram_fast_refresh_exit <= USED 0
winbond_psram_fast_refresh_exit_test <= USED 0
;FILE pstfilt.o
;FILE ptable.o
ptable_dump <= USED 0
ptable_init <= USED 0
;FILE pvdec_api.o
IsIntraFrame <= USED 0
PVDecPostProcess <= USED 0
PVDecSetEnhReference <= USED 0
PVDecSetReference <= USED 0
PVDecodeVideoFrame <= USED 0
PVExtractVolHeader <= USED 0
PVGetDecBitrate <= USED 0
PVGetDecFramerate <= USED 0
PVGetDecMemoryUsage <= USED 0
PVGetDecOutputFrame <= USED 0
PVGetLayerID <= USED 0
PVGetVideoTimeStamp <= USED 0
PVGetVolInfo <= USED 0
PVResetVideoDecoder <= USED 0
;FILE pvmp3_alias_reduction.o
;FILE pvmp3_crc.o
;FILE pvmp3_dct_6.o
;FILE pvmp3_decode_header.o
;FILE pvmp3_decode_huff_cw.o
;FILE pvmp3_dequantize_sample.o
;FILE pvmp3_equalizer.o
;FILE pvmp3_framedecoder.o
;FILE pvmp3_get_main_data_size.o
;FILE pvmp3_get_scale_factors.o
;FILE pvmp3_get_side_info.o
;FILE pvmp3_getbits.o
;FILE pvmp3_huffman_decoding.o
;FILE pvmp3_huffman_parsing.o
;FILE pvmp3_imdct_synth.o
;FILE pvmp3_mdct_6.o
;FILE pvmp3_mpeg2_get_scale_data.o
;FILE pvmp3_mpeg2_get_scale_factors.o
;FILE pvmp3_mpeg2_stereo_proc.o
;FILE pvmp3_poly_phase_synthesis.o
;FILE pvmp3_reorder.o
;FILE pvmp3_seek_synch.o
pvmp3_frame_synch <= USED 0
;FILE pvmp3_stereo_proc.o
;FILE q_gain_c.o
;FILE q_gain_p.o
;FILE q_plsf.o
;FILE q_plsf_3.o
_Z15Test_Vq_subvec3PsPKsS_siPi <= USED 0
_Z15Test_Vq_subvec4PsPKsS_sPi <= USED 0
;FILE q_plsf_5.o
;FILE qgain475.o
;FILE qgain795.o
;FILE qmgr.o
qBlockGet <= USED 0
qBlockPut <= USED 0
qDelete <= USED 0
qGetRef <= USED 0
qRoom <= USED 0
;FILE qspi_core.o
buf_dump <= USED 0
flash_erase_nvm <= USED 0
flash_init_apn <= USED 0
flash_init_apn_ex <= USED 0
flash_init_factory <= USED 0
flash_init_fota <= USED 0
flash_init_nvm <= USED 0
flash_partion_init <= USED 0
flash_read_btbin_rom_version <= USED 0
flash_read_nvm <= USED 0
flash_write_nvm <= USED 0
qspi_nor_erase_all <= USED 0
qspi_rx_mode_name <= USED 0
qspi_tx_mode_name <= USED 0
spi_nor_do_read_id <= USED 0
spi_nor_do_read_uid <= USED 0
;FILE qspi_dma.o
_dma_m2m_xfer <= USED 0
dma_clr_irq <= USED 0
dma_config_desc <= USED 0
dma_config_descriptor <= USED 0
dma_disconnect_irq_handler <= USED 0
dma_enable <= USED 0
dma_get_des_num <= USED 0
dma_load_descriptor <= USED 0
dma_read_status <= USED 0
dma_reg_printf <= USED 0
dma_set_dcsr <= USED 0
dma_set_des <= USED 0
dma_stop_irq_dis <= USED 0
dma_stop_irq_en <= USED 0
dma_wait_done <= USED 0
test_dma_complete_handler <= USED 0
;FILE qspi_host.o
qspi_enable_sw_handle_xip <= USED 0
qspi_wait_bus_ready <= USED 0
;FILE qspi_nor.o
get_spi_flash_chip <= USED 0
spi_nor_init2 <= USED 0
spi_nor_register_xip_task_cb <= USED 0
spi_nor_resume_task_xip <= USED 0
spi_nor_suspend_task_xip <= USED 0
;FILE qua_gain.o
;FILE raw.o
raw_bind_netif <= USED 0
raw_disconnect <= USED 0
raw_new_ip6 <= USED 0
;FILE recin.o
;FILE reorder.o
;FILE residu.o
;FILE rfcomm.o
rfcomm_create_channel_with_initial_credits <= USED 0
rfcomm_grant_credits <= USED 0
rfcomm_query_port_configuration <= USED 0
rfcomm_register_service_with_initial_credits <= USED 0
rfcomm_release_packet_buffer <= USED 0
rfcomm_send_local_line_status <= USED 0
rfcomm_send_modem_status <= USED 0
rfcomm_send_port_configuration <= USED 0
rfcomm_set_required_security_level <= USED 0
rfcomm_unregister_service <= USED 0
;FILE ripc.o
RIPCPhase1Init <= USED 0
ripc_interrupt_clear <= USED 0
ripc_interrupt_set <= USED 0
;FILE rm.o
CloseAllClk <= USED 0
EnableGPBbusclose <= USED 0
RMBeforeIntDisRegister <= USED 0
RMD2PrepareWithFastClk <= USED 0
RMEventsGet <= USED 0
RMIsResourceFree <= USED 0
RMIsResourceMulti <= USED 0
RMLPMexcpetion <= USED 0
RMPhase1Init <= USED 0
RMSWReset <= USED 0
RMServiceFreqGet <= USED 0
RMServiceFreqSelect <= USED 0
RMwuEnabled <= USED 0
TWSIClockOnOff <= USED 0
XIRQClockOnOff <= USED 0
dumpRMCount <= USED 0
;FILE rm_memRetain.o
RMD2RecoverINTC <= USED 0
;FILE rndis.o
;FILE rohc_add_cid.o
;FILE rohc_comp.o
rohc_comp_deliver_feedback2 <= USED 0
rohc_comp_disable_profile <= USED 0
rohc_comp_disable_profiles <= USED 0
rohc_comp_enable_profile <= USED 0
rohc_comp_enable_profiles <= USED 0
rohc_comp_force_contexts_reinit <= USED 0
rohc_comp_get_cid_type <= USED 0
rohc_comp_get_general_info <= USED 0
rohc_comp_get_last_packet_info2 <= USED 0
rohc_comp_get_max_cid <= USED 0
rohc_comp_get_mrru <= USED 0
rohc_comp_get_segment2 <= USED 0
rohc_comp_get_state_descr <= USED 0
rohc_comp_pad <= USED 0
rohc_comp_profile_enabled <= USED 0
rohc_comp_reinit_context <= USED 0
rohc_comp_set_features <= USED 0
rohc_comp_set_mrru <= USED 0
rohc_comp_set_traces_cb2 <= USED 0
;FILE rohc_comp_rfc3095.o
;FILE rohc_decomp.o
rohc_decomp_disable_profile <= USED 0
rohc_decomp_disable_profiles <= USED 0
rohc_decomp_enable_profile <= USED 0
rohc_decomp_enable_profiles <= USED 0
rohc_decomp_get_cid_type <= USED 0
rohc_decomp_get_context_info <= USED 0
rohc_decomp_get_general_info <= USED 0
rohc_decomp_get_last_packet_info <= USED 0
rohc_decomp_get_max_cid <= USED 0
rohc_decomp_get_mrru <= USED 0
rohc_decomp_get_prtt <= USED 0
rohc_decomp_get_rate_limits <= USED 0
rohc_decomp_get_state_descr <= USED 0
rohc_decomp_set_features <= USED 0
rohc_decomp_set_mrru <= USED 0
rohc_decomp_set_traces_cb2 <= USED 0
;FILE rohc_decomp_detect_packet.o
;FILE rohc_decomp_rfc3095.o
;FILE rohc_list.o
;FILE rohc_packets.o
rohc_get_ext_descr <= USED 0
rohc_get_packet_descr <= USED 0
rohc_get_packet_type <= USED 0
;FILE rohc_profiles.o
rohc_get_profile_descr <= USED 0
rohc_profile_get_other_version <= USED 0
rohc_profile_is_rohcv1 <= USED 0
;FILE rohc_traces_internal.o
;FILE rohc_utils.o
;FILE root.o
dummyRootFunction <= USED 0
;FILE round.o
;FILE rrcdsutils.o
rrDsChangePagingDrxLength <= USED 0
rrDsGetActiveSubstituted <= USED 0
rrDsGetGsmPagingDrxLength <= USED 0
rrDsGetUmtsPagingDrxLength <= USED 0
rrDsSendIratDsPagingDrxLengthChangeInd <= USED 0
rrDsSendIratDsVoiceBadInd <= USED 0
rrDsSendIratDsWifiAbortCnf <= USED 0
rrDsSendIratDsWifiStartReq <= USED 0
rrDsSetBtLteCoExist <= USED 0
rrDsSetWifiLteCoExist <= USED 0
;FILE rrcomdb.o
RrComDbCheckRequestPlmnOfTwoSimCard <= USED 0
RrComDbGetManuallyTriggered <= USED 0
RrComDbGetMncLengthKnown <= USED 0
RrComDbGetSimPresent <= USED 0
RrComDbSetManuallySelectedPlmn <= USED 0
RrComdbCheckIsInChina <= USED 0
;FILE rrutility.o
IsCuccPlmn <= USED 0
IsDeutscheTelekomInRequestedPlmnList <= USED 0
IsProximusPlmn <= USED 0
IsRedirectionFailureHandlingEnable <= USED 0
IsSoftbankPlmn <= USED 0
IsVietelPlmn <= USED 0
MustSearchBandWhenRedirectFromLTE <= USED 0
RrIsTestSimGcfMode <= USED 0
;FILE rsa.o
mbedtls_rsa_copy <= USED 0
mbedtls_rsa_export <= USED 0
mbedtls_rsa_export_crt <= USED 0
mbedtls_rsa_export_raw <= USED 0
mbedtls_rsa_import <= USED 0
mbedtls_rsa_set_padding <= USED 0
;FILE rsa_internal.o
;FILE rt_ringbuffer.o
rt_ringbuffer_destroy <= USED 0
rt_ringbuffer_get_size <= USED 0
rt_ringbuffer_getchar <= USED 0
rt_ringbuffer_put <= USED 0
rt_ringbuffer_putchar <= USED 0
rt_ringbuffer_putchar_force <= USED 0
rt_ringbuffer_reset <= USED 0
;FILE s10_8pf.o
;FILE sac_api.o
Send_HWM_SHMEM <= USED 0
Send_LWM_SHMEM <= USED 0
ciShDeregisterReq <= USED 0
sacDlTransmitEnable <= USED 0
sacSendGkiAudioEcallToApInfoInd <= USED 0
;FILE sac_cc.o
;FILE sac_cfg.o
;FILE sac_dat.o
sacGetCurrentCid <= USED 0
sacGetCurrentNsapi <= USED 0
;FILE sac_dev.o
sacCiDevSetLpmEnabledFunc <= USED 0
sacDevGetSimCfunState <= USED 0
sacDevSetDipOption <= USED 0
setSacDevLpmFlag <= USED 0
;FILE sac_libs.o
sacShCheckSpecPendingCiReq <= USED 0
sacShCheckWaitCiReq <= USED 0
sacShConvertIpToLong <= USED 0
sacShDelayCiReq <= USED 0
sacShDequeue <= USED 0
sacShGetOpShHandle <= USED 0
sacShGetPrevFsmState <= USED 0
sacShKeepCurSignal <= USED 0
sacShReset <= USED 0
sacShSendCiAccessDeniedCnf <= USED 0
sacShSendCiAccessDeniedCnfWithInfo <= USED 0
sacShSendCiHasNoSupportCnfWithInfo <= USED 0
sacShStoreUserVal <= USED 0
;FILE sac_mm.o
SacClearAnayArfcnNvm <= USED 0
SacGetArfcnFromNvm <= USED 0
SacInsertArfcnNvm <= USED 0
SacUpdateAllArfcnNvm <= USED 0
sacMmGetCurrentNetworkMode <= USED 0
sacMmIsTestNetwork <= USED 0
sacMmResetRegMode <= USED 0
;FILE sac_msg.o
SacMsgGetCurrentMtMsgConfigFunc <= USED 0
;FILE sac_pb.o
;FILE sac_ps.o
sacPsGetDeniedNwMode <= USED 0
sacPsGetEmergencyNwMode <= USED 0
sacPsGetEutranAct <= USED 0
sacPsGetPdpCtxInfo <= USED 0
sacPsGetPsState <= USED 0
sacPsGetSim4GQciValue <= USED 0
sacPsLteDefaultApnDefined <= USED 0
sacPsProcessApexMmBandInd <= USED 0
sacPsSetForcedReportNwRegInd <= USED 0
;FILE sac_queues.o
sacSgQueueReadFirstSignal <= USED 0
sacSgQueueRemoveFirstSignal <= USED 0
;FILE sac_ref.o
;FILE sac_shell.o
sacShRegisterTask <= USED 0
sacShSetKeepControlMask <= USED 0
sacShSgHasNeededControl <= USED 0
;FILE sac_shell_engine.o
VgCiTask1 <= USED 0
;FILE sac_sim.o
isTestSimFromSac <= USED 0
sacSimIsSimReady <= USED 0
;FILE sac_ss.o
;FILE sac_trace.o
sacDataTrace <= USED 0
;FILE sbc.o
sbc_get_frame_duration <= USED 0
sbc_get_implementation_info <= USED 0
sbc_init_a2dp <= USED 0
sbc_init_msbc <= USED 0
sbc_parse <= USED 0
sbc_reinit <= USED 0
sbc_reinit_a2dp <= USED 0
;FILE sbc_primitives.o
;FILE sbc_primitives_armv6.o
;FILE scc.o
Scc_CallConference <= USED 0
Scc_Media_Cancel <= USED 0
Scc_Media_CreateChannel <= USED 0
Scc_Media_DeleteChannel <= USED 0
Scc_Media_GetIoInterface <= USED 0
Scc_Media_Recv <= USED 0
Scc_Media_Send <= USED 0
Scc_QueryAccessStatus <= USED 0
;FILE scc_access_sm.o
scc_AccessSM_RestartAllAccessOnCondition <= USED 0
;FILE scc_access_utils.o
scc_GetAccessCtx <= USED 0
scc_handle_suspend_state <= USED 0
;FILE scc_call_sm.o
;FILE scc_call_utils.o
scc_GetAltAccess <= USED 0
scc_GetDlgMediaCount <= USED 0
;FILE scc_pref_eval.o
;FILE scc_ps_intf.o
scc_IMS_ModifyPresService <= USED 0
scc_IMS_RefreshPresService <= USED 0
scc_IMS_StartPresService <= USED 0
scc_ps_is_mo_call_continue_reject_503_action_meet <= USED 0
;FILE scc_sms.o
;FILE scc_utils.o
scc_FreeScratchBuf <= USED 0
;FILE sdp.o
;FILE sdp_base_grammer.o
;FILE sdp_client.o
sdp_client_packet_handler <= USED 0
sdp_client_query_uuid128 <= USED 0
sdp_client_reset <= USED 0
sdp_parser_handle_chunk <= USED 0
sdp_parser_handle_done <= USED 0
;FILE sdp_client_rfcomm.o
sdp_client_query_rfcomm_channel_and_name_for_search_pattern <= USED 0
sdp_client_query_rfcomm_channel_and_name_for_uuid128 <= USED 0
;FILE sdp_formatter.o
format_acap_attribute_fields <= USED 0
format_acfg_attribute_fields <= USED 0
format_attrib <= USED 0
format_attrib_a <= USED 0
format_attrib_c <= USED 0
format_attrib_d <= USED 0
format_attrib_e <= USED 0
format_attrib_g <= USED 0
format_attrib_i <= USED 0
format_attrib_l <= USED 0
format_attrib_m <= USED 0
format_attrib_n <= USED 0
format_attrib_o <= USED 0
format_attrib_p <= USED 0
format_attrib_q <= USED 0
format_attrib_r <= USED 0
format_attrib_s <= USED 0
format_attrib_t <= USED 0
format_connection_field <= USED 0
format_email_fields <= USED 0
format_fmtp_attribute_fields <= USED 0
format_framesize_attribute_field <= USED 0
format_group_attribute_fields <= USED 0
format_image_attribute_fields <= USED 0
format_information_field <= USED 0
format_key_field <= USED 0
format_pcfg_attribute_fields <= USED 0
format_phone_fields <= USED 0
format_property_attrib <= USED 0
format_repeat_interval_fields <= USED 0
format_rtpmap_attribute_fields <= USED 0
format_tcap_attribute_fields <= USED 0
format_time_zone_fields <= USED 0
format_uri_field <= USED 0
format_valid_atttib_value <= USED 0
sdp_format_precond_confirmstatus <= USED 0
sdp_format_precond_currentstatus <= USED 0
sdp_format_precond_desiredstatus <= USED 0
sdp_validate_precond_directiontag <= USED 0
sdp_validate_precond_statustype <= USED 0
sdp_validate_precond_strengthtag <= USED 0
validate_acap <= USED 0
validate_acfg <= USED 0
validate_bandwidth <= USED 0
validate_byte_string <= USED 0
validate_creq <= USED 0
validate_csup <= USED 0
validate_fmtp <= USED 0
validate_group <= USED 0
validate_pcfg <= USED 0
validate_rtpmap <= USED 0
validate_tcap <= USED 0
validate_time_field <= USED 0
;FILE sdp_parser_utils.o
parse_IP6addr <= USED 0
parse_mid_attrib <= USED 0
;FILE sdp_server.o
sdp_create_service_record_handle <= USED 0
sdp_get_record_for_handle <= USED 0
;FILE sdp_util.o
SDP_IsMediaTypePresent <= USED 0
de_dump_data_element <= USED 0
de_get_string <= USED 0
des_iterator_get_size <= USED 0
sdp_append_attributes_in_attributeIDList <= USED 0
sdp_service_search_pattern_for_uuid128 <= USED 0
sdp_set_attribute_value_for_attribute_id <= USED 0
sdp_traversal_match_pattern <= USED 0
transfer_buffer <= USED 0
;FILE sdvl.o
;FILE set_fs.o
;FILE set_sign.o
;FILE set_zero.o
;FILE sha1.o
mbedtls_sha1 <= USED 0
mbedtls_sha1_process <= USED 0
;FILE sha256.o
mbedtls_sha256 <= USED 0
mbedtls_sha256_finish <= USED 0
mbedtls_sha256_process <= USED 0
mbedtls_sha256_starts <= USED 0
mbedtls_sha256_update <= USED 0
;FILE sha512.o
mbedtls_sha512 <= USED 0
mbedtls_sha512_finish <= USED 0
mbedtls_sha512_process <= USED 0
mbedtls_sha512_starts <= USED 0
mbedtls_sha512_update <= USED 0
;FILE shr.o
;FILE shr_r.o
;FILE sid_sync.o
sid_sync_set_handover_debt <= USED 0
;FILE signal_mcu.o
WebRtcNetEQ_SignalMcu <= USED 0
;FILE simPin.o
assert_cnt_flag_init <= USED 0
sim_pin_fota_clear <= USED 0
sim_pin_fota_restore <= USED 0
;FILE sim_api.o
MEPReadCodes <= USED 0
isTestCard <= USED 0
libConvertNumToHexString <= USED 0
;FILE sim_api_mini.o
checkSIMRet <= USED 0
getSimType <= USED 0
resetSimParas <= USED 0
telSimAbsent <= USED 0
telSimInitDone <= USED 0
telSimRemoveSuppress <= USED 0
;FILE simatdec.o
SimatDecodeAddress <= USED 0
SimatDecodeItem <= USED 0
SimatDecodeItemList <= USED 0
SimatDecodeMenuItems <= USED 0
SimatDecodeResponseLength <= USED 0
SimatDecodeSmsTpdu <= USED 0
SimatDecodeTextAttributeList <= USED 0
SimatDecodeTextString <= USED 0
SimatDecodeToneType <= USED 0
;FILE simatenc.o
SimEncodeTerminalProfile <= USED 0
SimatEncodeBearerCap <= USED 0
SimatEncodeCause <= USED 0
SimatEncodeCps <= USED 0
SimatEncodeTextString <= USED 0
SimatEncodeTransId <= USED 0
SimatEncodeUssdString <= USED 0
UsimEncodeTerminalProfile <= USED 0
;FILE simdec.o
SimDecodeCps <= USED 0
SimDecodeCpsValue <= USED 0
SimDecodeExt <= USED 0
SimDecodePhase <= USED 0
SimDecodeSst <= USED 0
USIMOemFlagClear <= USED 0
USIMOemFlagGet <= USED 0
USIMOemFlagSet <= USED 0
;FILE simenc.o
SimEncodeAaem <= USED 0
SimEncodeAccessClass <= USED 0
SimEncodeCps <= USED 0
SimEncodeCpsValue <= USED 0
SimEncodeDck <= USED 0
SimEncodeElp <= USED 0
SimEncodeExt <= USED 0
SimEncodeLp <= USED 0
;FILE simproc.o
SimManagerTask1 <= USED 0
SimManagerTaskExitRoutine <= USED 0
;FILE simsig.o
SimSendL1siSwitchReq <= USED 0
;FILE singleToneDect.o
L_mls <= USED 0
;FILE sipResolver.o
SIPResolver_GetConfig <= USED 0
;FILE sipUaRefer.o
;FILE sipUaReferDialog.o
SipUAS_UpdateReferDialog <= USED 0
;FILE sipUaReferFsmFunc.o
;FILE sipUaUtils.o
API_SIP_UA_FreeOtherHeader <= USED 0
FreeResponseParameters <= USED 0
sipUAC_AddDiversionHeader <= USED 0
sipUA_CloneFeatureParam <= USED 0
sipUA_SetPMediaAuthorizationHdr <= USED 0
;FILE sipUacAppReq.o
SipUACSendInviteSessTimer <= USED 0
SipUACSendUpdateSessTimer <= USED 0
;FILE sipUacStackResp.o
;FILE sipUasAppResp.o
SipUASSendInvite5XX <= USED 0
SipUASSendPrack4XXConfirmed <= USED 0
SipUASSendPrack4XXEarly <= USED 0
;FILE sipUasStackReq.o
SIP_UAS_ProcessRefer <= USED 0
SIP_UAS_ProcessSubscribe <= USED 0
;FILE sipUseragent.o
SIPUAC_UnSubscribe <= USED 0
SIPUA_GetFqdn <= USED 0
;FILE sipUseragentReg.o
;FILE sipUseragentRegFsmFunc.o
SipUaGotChallengeDeRegisterNotifyEn <= USED 0
SipUaGotChallengeReRegisterNotifyEn <= USED 0
;FILE sip_auth.o
;FILE sip_formatter.o
;FILE sip_os_utils.o
;FILE sip_transceiver.o
SIPTransceiver_GetConfig <= USED 0
SIP_Get_Received <= USED 0
;FILE sipen.o
;FILE sipenfsmfunc.o
SipEnTerminatingNotifyStack <= USED 0
;FILE siptransaction.o
SIPTransaction_SendRequest <= USED 0
sipTrxn_GenerateViaBranch <= USED 0
;FILE siptransactioninvclifsm.o
;FILE siptransactioninvclifsmfunc.o
;FILE siptransactioninvsrvfsm.o
;FILE siptransactioninvsrvfsmfunc.o
;FILE siptransactionlist.o
SipTransactionListPrint <= USED 0
;FILE siptransactionnoninvclifsm.o
;FILE siptransactionnoninvclifsmfunc.o
;FILE siptransactionnoninvsrvfsm.o
;FILE siptransactionnoninvsrvfsmfunc.o
;FILE sipuri.o
SIP_FreeDisplayUri <= USED 0
SIP_GetDisplayUriName <= USED 0
SIP_GetTelUriParamsVerstatType <= USED 0
;FILE siputil.o
;FILE sm.o
gap_delete_bonding <= USED 0
gap_get_persistent_irk <= USED 0
gap_random_address_get_mode <= USED 0
gap_random_address_set_update_period <= USED 0
sm_address_resolution_lookup <= USED 0
sm_allow_ltk_reconstruction_without_le_device_db_entry <= USED 0
sm_authorization_decline <= USED 0
sm_authorization_grant <= USED 0
sm_bonding_decline <= USED 0
sm_identity_resolving_state <= USED 0
sm_keypress_notification <= USED 0
sm_passkey_input <= USED 0
sm_register_ltk_callback <= USED 0
sm_register_oob_data_callback <= USED 0
sm_register_sc_oob_data_callback <= USED 0
sm_remove_event_handler <= USED 0
sm_send_security_request <= USED 0
sm_set_accepted_stk_generation_methods <= USED 0
sm_set_encryption_key_size_range <= USED 0
sm_set_er <= USED 0
sm_set_ir <= USED 0
sm_set_request_security <= USED 0
sm_set_secure_connections_only_mode <= USED 0
sm_test_set_irk <= USED 0
sm_test_use_fixed_local_csrk <= USED 0
sm_use_fixed_passkey_in_display_role <= USED 0
;FILE smcmprot.o
SmcmTask1 <= USED 0
;FILE smencdec.o
;FILE smip_common.o
smip_check_if_admin_sms <= USED 0
;FILE smip_storage.o
smip_IsSMSStorageRequired <= USED 0
smip_IsSMSStored <= USED 0
smip_IsStatusReportStored <= USED 0
smip_IsVoiceMail <= USED 0
;FILE smmain.o
GpSmTask1 <= USED 0
;FILE smrdwr.o
;FILE smrlprot.o
SmrlTask1 <= USED 0
;FILE smtlprot.o
SmtlTask1 <= USED 0
SmtlTaskExitRoutine <= USED 0
;FILE snow_3g.o
MULxPOW <= USED 0
;FILE sockets.o
lwip_close2 <= USED 0
lwip_close_socket <= USED 0
lwip_close_tcppcb <= USED 0
lwip_debug_dump_sockets <= USED 0
lwip_eventfd <= USED 0
lwip_get_sock_isipv6 <= USED 0
lwip_get_sock_localipXaddr <= USED 0
lwip_get_sock_localport <= USED 0
lwip_get_sock_pdu_offset <= USED 0
lwip_get_sock_remoteipXaddr <= USED 0
lwip_get_sock_remoteport <= USED 0
lwip_getpeername <= USED 0
lwip_getsockname <= USED 0
lwip_getsocktcppcb <= USED 0
lwip_getsocktype <= USED 0
lwip_getsockudppcb <= USED 0
lwip_getthreaderrno <= USED 0
lwip_readv <= USED 0
lwip_shutdown2 <= USED 0
lwip_socket_with_callback <= USED 0
lwip_trigger_select_cb <= USED 0
lwip_writev <= USED 0
readline <= USED 0
readn <= USED 0
recv_peek <= USED 0
writen <= USED 0
;FILE sp_dec.o
;FILE sp_enc.o
Speech_Encode_Frame_First <= USED 0
;FILE split_and_insert.o
;FILE spp_server.o
spp_create_custom_sdp_record <= USED 0
;FILE spreproc.o
;FILE spstproc.o
;FILE sqrt_l.o
;FILE ss_api.o
SS_LocationVerificationRsp <= USED 0
resetSsParas <= USED 0
;FILE ss_api_mini.o
;FILE ssl_ciphersuites.o
mbedtls_ssl_ciphersuite_from_string <= USED 0
mbedtls_ssl_get_ciphersuite_id <= USED 0
mbedtls_ssl_get_ciphersuite_name <= USED 0
;FILE ssl_cli.o
;FILE ssl_http_client.o
ssl_client_handshake <= USED 0
;FILE ssl_srv.o
;FILE ssl_tls.o
mbedtls_ssl_check_pending <= USED 0
mbedtls_ssl_conf_cert_profile <= USED 0
mbedtls_ssl_conf_cert_req_ca_list <= USED 0
mbedtls_ssl_conf_ciphersuites <= USED 0
mbedtls_ssl_conf_ciphersuites_for_version <= USED 0
mbedtls_ssl_conf_curves <= USED 0
mbedtls_ssl_conf_dh_param <= USED 0
mbedtls_ssl_conf_dh_param_ctx <= USED 0
mbedtls_ssl_conf_dhm_min_bitlen <= USED 0
mbedtls_ssl_conf_endpoint <= USED 0
mbedtls_ssl_conf_handshake_timeout <= USED 0
mbedtls_ssl_conf_legacy_renegotiation <= USED 0
mbedtls_ssl_conf_max_version <= USED 0
mbedtls_ssl_conf_min_version <= USED 0
mbedtls_ssl_conf_own_cert <= USED 0
mbedtls_ssl_conf_psk <= USED 0
mbedtls_ssl_conf_psk_cb <= USED 0
mbedtls_ssl_conf_read_timeout <= USED 0
mbedtls_ssl_conf_renegotiation <= USED 0
mbedtls_ssl_conf_renegotiation_enforced <= USED 0
mbedtls_ssl_conf_renegotiation_period <= USED 0
mbedtls_ssl_conf_session_cache <= USED 0
mbedtls_ssl_conf_sig_hashes <= USED 0
mbedtls_ssl_conf_transport <= USED 0
mbedtls_ssl_conf_verify <= USED 0
mbedtls_ssl_get_bytes_avail <= USED 0
mbedtls_ssl_get_ciphersuite <= USED 0
mbedtls_ssl_get_peer_cert <= USED 0
mbedtls_ssl_get_session <= USED 0
mbedtls_ssl_get_version <= USED 0
mbedtls_ssl_send_fatal_handshake_failure <= USED 0
mbedtls_ssl_session_copy <= USED 0
mbedtls_ssl_session_reset <= USED 0
mbedtls_ssl_set_datagram_packing <= USED 0
mbedtls_ssl_set_hs_psk <= USED 0
mbedtls_ssl_set_mtu <= USED 0
mbedtls_ssl_set_read_buf_hook <= USED 0
mbedtls_ssl_set_session <= USED 0
mbedtls_ssl_set_timer_cb <= USED 0
mbedtls_ssl_set_verify <= USED 0
mbedtls_ssl_tls_prf <= USED 0
;FILE stddbg.o
__aeabi_assert <= USED 0
__assert <= USED 0
;FILE stream_db.o
WebRtcNetEQ_StreamRemove <= USED 0
;FILE streamingmedia.o
_Z17media_service_runv <= USED 0
streamingmedia_init <= USED 0
;FILE stubs.o
L1FrGetUsedBbcTdsIfCount <= USED 0
;FILE sul_sms.o
SUL_ASCII2GSM <= USED 0
SUL_CharacterLen <= USED 0
SUL_ConvertPDUToHex <= USED 0
SUL_Decode7Bit <= USED 0
SUL_DecodePdu <= USED 0
SUL_Encode7Bit <= USED 0
SUL_EncodePDU <= USED 0
SUL_EncodePDU_No_Smsc <= USED 0
SUL_GSM2ASCII <= USED 0
SUL_GSM2UCS2 <= USED 0
SUL_GetLastError <= USED 0
SUL_GetSCAStruct <= USED 0
SUL_SerializeNumbers <= USED 0
SUL_SetLastError <= USED 0
SUL_String2Bytes <= USED 0
;FILE sul_string.o
IsCharAlpha <= USED 0
IsCharAlphaNumeric <= USED 0
IsCharLower <= USED 0
IsCharUpper <= USED 0
SUL_CharLower <= USED 0
SUL_CharLowerBuff <= USED 0
SUL_CharNext <= USED 0
SUL_CharPrev <= USED 0
SUL_CharUpper <= USED 0
SUL_CharUpperBuff <= USED 0
SUL_CountChar <= USED 0
SUL_IsStringAscii <= USED 0
SUL_Itoa <= USED 0
SUL_Ltoa <= USED 0
SUL_MemChr <= USED 0
SUL_MemCompare <= USED 0
SUL_MemCopy32 <= USED 0
SUL_MemCopy8 <= USED 0
SUL_MemCopyEx <= USED 0
SUL_MemDMACopy <= USED 0
SUL_MemMove <= USED 0
SUL_MemSet32 <= USED 0
SUL_MemSet8 <= USED 0
SUL_SeekCodeTable <= USED 0
SUL_StrAToI <= USED 0
SUL_StrCaselessCompare <= USED 0
SUL_StrCat <= USED 0
SUL_StrChr <= USED 0
SUL_StrCompare <= USED 0
SUL_StrCompareAscii <= USED 0
SUL_StrCopy <= USED 0
SUL_StrIsAscii <= USED 0
SUL_StrNCaselessCompare <= USED 0
SUL_StrNCat <= USED 0
SUL_StrNCompare <= USED 0
SUL_StrNCopy <= USED 0
SUL_StrPrint <= USED 0
SUL_StrTrim <= USED 0
SUL_StrTrimAll <= USED 0
SUL_StrTrimEx <= USED 0
SUL_StrTrimLeft <= USED 0
SUL_StrTrimLeftChar <= USED 0
SUL_StrTrimRight <= USED 0
SUL_StrTrimRightChar <= USED 0
SUL_StrTrimSpace <= USED 0
SUL_StrVPrint <= USED 0
SUL_Strlen <= USED 0
SUL_Strnlen <= USED 0
SUL_Strrchr <= USED 0
SUL_Strtok <= USED 0
SUL_Vsprintf <= USED 0
SUL_ZeroMemory32 <= USED 0
SUL_ZeroMemory8 <= USED 0
_SUL_tolower <= USED 0
_SUL_toupper <= USED 0
;FILE sulog.o
SulogDmaCopy <= USED 0
SulogSDcardSend <= USED 0
;FILE supp_service.o
CB_Free <= USED 0
CDIV_Free <= USED 0
Xdmc_Parse_Supp_CDIV_Status <= USED 0
;FILE syn_filt.o
;FILE sys.o
system_power_down <= USED 0
system_reboot <= USED 0
ui_delay_ms <= USED 0
;FILE sys_arch.o
LWIP_LOG <= USED 0
lwip_assert_now_callback <= USED 0
sys_thread_delete <= USED 0
;FILE sysapi.o
SysDebug_DumpBytes_Print <= USED 0
SysDebug_DumpVector_Print <= USED 0
SysDebug_Flush <= USED 0
SysDebug_GetLogName <= USED 0
SysDebug_LogBuf_Cleanup <= USED 0
Sys_IsInit <= USED 0
Sys_SmartBuf_IncrRefImpl <= USED 0
sys_printnl_buf <= USED 0
;FILE sysbuf.o
SysBuffer_Bufcpy <= USED 0
SysBuffer_CopyToMem <= USED 0
SysVector2Buffer_Set <= USED 0
;FILE syscache.o
;FILE syscrypt.o
SysCrypt_DigestGetHashLength <= USED 0
;FILE sysevent.o
SysEvent_Reset <= USED 0
;FILE sysfile.o
SysFile_Flush <= USED 0
SysFile_Write <= USED 0
;FILE sysio.o
SysGenericIo_Cancel <= USED 0
SysGenericIo_Create <= USED 0
SysGenericIo_Delete <= USED 0
SysGenericIo_GetBuf <= USED 0
SysGenericIo_GetFlags <= USED 0
SysGenericIo_GetIoInterface <= USED 0
SysGenericIo_GetVector <= USED 0
SysGenericIo_Recv <= USED 0
SysGenericIo_Register <= USED 0
SysGenericIo_Reset <= USED 0
SysGenericIo_Send <= USED 0
SysGenericIo_SendMessage <= USED 0
SysGenericIo_SetIoResult <= USED 0
SysGenericIo_SetIoState <= USED 0
SysIo_SetErrorState <= USED 0
SysLoopbackIo_Create <= USED 0
SysLoopbackIo_Delete <= USED 0
SysLoopbackIo_GetIoInterface <= USED 0
;FILE syslist.o
SysList_Cycle <= USED 0
;FILE sysmod.o
SysModule_CleanupThread <= USED 0
SysModule_Dump <= USED 0
SysModule_GetCurrModule <= USED 0
SysObject_Dump <= USED 0
my_SysModule_ThreadIDMatch <= USED 0
;FILE sysport.o
SysTimer_Time_To_Ticks <= USED 0
;FILE sysqueue.o
;FILE syssocket.o
SysGetSockName <= USED 0
SysGetSockopt <= USED 0
SysInet_pton4 <= USED 0
SysInet_pton6 <= USED 0
Sys_MapErrCode <= USED 0
;FILE systhread.o
;FILE systimer.o
SysTimer_SetLocalTimer <= USED 0
;FILE sysutil.o
SysUtil_MapEventCode <= USED 0
;FILE sysutils.o
;FILE syswait.o
checkObj <= USED 0
;FILE task_init.o
get_dev_mon_task <= USED 0
get_gui_dummy_task <= USED 0
get_gui_playring_task <= USED 0
get_usb_mon_task <= USED 0
gui_dummy_task_entry <= USED 0
hal_task_create <= USED 0
;FILE tavor_packages.o
plValTSInit <= USED 0
tavorPackagesGet <= USED 0
;FILE tbl.o
ConvertGSM7bitToAscii8bit_iso8859 <= USED 0
;FILE tcp.o
tcp_bind_netif <= USED 0
tcp_debug_state_str <= USED 0
tcp_fasttmr <= USED 0
tcp_listen_pcb_exist <= USED 0
tcp_new_ip6 <= USED 0
tcp_process_pcb_exist <= USED 0
tcp_setprio <= USED 0
tcp_slowtmr <= USED 0
tcp_tmr <= USED 0
;FILE tcp_in.o
tcp_get_tcp_urgp <= USED 0
;FILE tcp_out.o
tcp_keepalive <= USED 0
tcp_zero_window_probe <= USED 0
;FILE tcpip.o
lwip_mq_left_query <= USED 0
lwip_mq_size_query <= USED 0
lwip_set_ack_process_thresh <= USED 0
lwip_set_ack_rate <= USED 0
lwip_set_ack_skip_thresh <= USED 0
lwip_set_ackopt_thresh_rate <= USED 0
lwip_set_ackopt_time_number <= USED 0
lwip_set_rx_ims_rate <= USED 0
lwip_set_thresh_rate <= USED 0
lwip_set_tx_rate <= USED 0
tcpip_api_call <= USED 0
tcpip_callbackmsg_delete <= USED 0
tcpip_callbackmsg_new <= USED 0
tcpip_flowctrl_pcb_free_msg <= USED 0
tcpip_flowctrl_pcb_msg_dump <= USED 0
tcpip_man_trigger_assert <= USED 0
tcpip_man_trigger_kill <= USED 0
tcpip_netifapi <= USED 0
tcpip_timeout <= USED 0
tcpip_trycallback <= USED 0
tcpip_untimeout <= USED 0
;FILE telatci.o
CiInitCallBack <= USED 0
createIndForProxy <= USED 0
;FILE telaudio.o
;FILE telcc.o
ciSyncAudio <= USED 0
;FILE telcmux.o
;FILE telcmux_mini.o
fota_firmware_pkgDwl <= USED 0
;FILE telcontroller.o
ATGetCmdIndex <= USED 0
ATGetCmdName <= USED 0
DumpCtrl <= USED 0
EE_ResponsePendingAsync <= USED 0
MAT_STRESS_TEST <= USED 0
Switch_Modem_State <= USED 0
at_send_csq <= USED 0
ciTestTimeout <= USED 0
getChannelInfo <= USED 0
getChsetType <= USED 0
initAtPara <= USED 0
initChannelInfo <= USED 0
reportAtCmdReady <= USED 0
tcCreateMSGQ <= USED 0
tcInit <= USED 0
tcOpenDevice <= USED 0
tcOpenExtSerialPort <= USED 0
;FILE teldat.o
initDataPPP <= USED 0
initDirectIP <= USED 0
;FILE teldat_mini.o
Set_sATP_Mode <= USED 0
insert_node_into_PS_sink_Queue <= USED 0
search_handlelist_sequence <= USED 0
;FILE teldbg.o
ciBTA2DP <= USED 0
ciBTHF <= USED 0
dbg_dump_buffer <= USED 0
gps_usr_callback_hdl <= USED 0
set_pp_flag <= USED 0
telGetLwipCtrlMode <= USED 0
;FILE teldev.o
GripNotifyAP <= USED 0
ciIMLCONFIG <= USED 0
ciMyDownload <= USED 0
ciRSSI <= USED 0
ciStarMODEMRESET <= USED 0
playMP3 <= USED 0
;FILE teldev_mini.o
;FILE telmm.o
ciOperSelLte <= USED 0
ciStarNASCHK <= USED 0
;FILE telmm_mini.o
;FILE telmsg.o
;FILE telpb.o
ciSWritePB <= USED 0
;FILE telps.o
Encode3GBitrate <= USED 0
Encode3GMaxSduSize <= USED 0
Encode3GTransDelay <= USED 0
Encode4GBitrate <= USED 0
ciPSAttachWithCause <= USED 0
ciPsPlusPaging <= USED 0
ciTELCGCONT <= USED 0
getAtQosParams <= USED 0
;FILE telps_mini.o
getRegOptionPersist <= USED 0
;FILE telsim.o
ciBindSimId <= USED 0
ciGetCardMode <= USED 0
ciSetDualSimType <= USED 0
ciSwitchSim <= USED 0
;FILE telsim_mini.o
;FILE telss.o
;FILE telutl.o
GenerateAddressType <= USED 0
GetOperatorNameByMCCMNC <= USED 0
HexStringToInt <= USED 0
IsInValidRange <= USED 0
PrintNumericList <= USED 0
StringToInt <= USED 0
addDoubleQuotatiaon <= USED 0
atParamToCiEnum <= USED 0
displayHexValue <= USED 0
dump_string <= USED 0
getErrorRatioParam <= USED 0
hex_to_bin <= USED 0
isDialNumber <= USED 0
str2hex32 <= USED 0
string_tok_hasmore <= USED 0
string_tok_nexthexint <= USED 0
string_tok_nextint <= USED 0
string_tok_nextulong <= USED 0
string_tok_start <= USED 0
telUtlTranslateUnpackedGsm7ToText <= USED 0
translateUnpackedGsm7ToText <= USED 0
;FILE tft.o
tft_get_ip_info <= USED 0
;FILE tick_manager.o
TickPhase1Init <= USED 0
;FILE tim.o
SendLteActTestModeCmp <= USED 0
SendLteCloseTestModeCmp <= USED 0
SendLteDeactTestModeCmp <= USED 0
SendLteOpenTestModeCmp <= USED 0
TimTask1 <= USED 0
;FILE timeM.o
;FILE timer.o
AccTimerCreate <= USED 0
AccTimerDelete <= USED 0
AccTimerStart <= USED 0
AccTimerStartEx <= USED 0
AccTimerStop <= USED 0
GetTimerStatus <= USED 0
apb_timer_get_count <= USED 0
apb_timer_init <= USED 0
apb_timer_start <= USED 0
disable_all_interrupts <= USED 0
getTime_us <= USED 0
restore_all_interrupts <= USED 0
timerDidOSTimerExpire <= USED 0
timerElapsedTimeGet <= USED 0
timerEnableClock <= USED 0
timerPhase1Init <= USED 0
timerReschedule <= USED 0
timerResume <= USED 0
timerVersionGet <= USED 0
;FILE timers.o
lwip_1s_cb <= USED 0
lwip_60s_cb <= USED 0
lwip_timer_1s <= USED 0
lwip_timer_60s <= USED 0
lwip_timer_init <= USED 0
tcp_tmr_timer <= USED 0
tcpip_timer_sleep_long <= USED 0
tcpip_timer_sleep_short <= USED 0
;FILE tm_time.o
TM_FileTimeToSystemTime <= USED 0
TM_GetDayNoInYear <= USED 0
TM_GetDayOfWeek <= USED 0
TM_GetDaysOneMonth <= USED 0
TM_GetDaysOneYear <= USED 0
TM_GetSystemFileTime <= USED 0
TM_GetSystemTime <= USED 0
TM_GetTime <= USED 0
TM_GetTimeZone <= USED 0
TM_NTPSyncRtcTimeFlag <= USED 0
TM_PmicRtcInitFlag <= USED 0
TM_SetSystemTime <= USED 0
TM_SystemTimeToFileTime <= USED 0
TM_autoSyncSystemTime <= USED 0
;FILE ton_stab.o
;FILE tsip_common.o
;FILE tsip_free.o
SIP_FreeAuthenticationInfo <= USED 0
SIP_FreeDigestChallenge <= USED 0
;FILE tsip_parser.o
SIP_GetToFromTagPrintStr <= USED 0
;FILE tsip_parser_uri.o
;FILE tx_block_allocate.o
;FILE tx_block_pool_cleanup.o
;FILE tx_block_pool_create.o
;FILE tx_block_pool_prioritize.o
;FILE tx_block_release.o
;FILE tx_event_flags_cleanup.o
;FILE tx_event_flags_create.o
;FILE tx_event_flags_delete.o
;FILE tx_event_flags_get.o
;FILE tx_event_flags_set.o
;FILE tx_hisr.o
;FILE tx_initialize_high_level.o
_tx_initialize_high_level <= USED 0
;FILE tx_initialize_kernel_enter.o
_tx_initialize_kernel_enter <= USED 0
tx_application_define <= USED 0
;FILE tx_queue_cleanup.o
;FILE tx_queue_create.o
;FILE tx_queue_delete.o
;FILE tx_queue_front_send.o
_tx_queue_front_send <= USED 0
;FILE tx_queue_prioritize.o
;FILE tx_queue_receive.o
;FILE tx_queue_send.o
;FILE tx_semaphore_cleanup.o
;FILE tx_semaphore_create.o
;FILE tx_semaphore_delete.o
;FILE tx_semaphore_get.o
;FILE tx_semaphore_prioritize.o
;FILE tx_semaphore_put.o
;FILE tx_thread_create.o
;FILE tx_thread_delete.o
;FILE tx_thread_info_get.o
;FILE tx_thread_initialize.o
_tx_thread_initialize <= USED 0
tx_current_verison <= USED 0
;FILE tx_thread_priority_change.o
;FILE tx_thread_relinquish.o
_tx_thread_relinquish <= USED 0
;FILE tx_thread_reset.o
;FILE tx_thread_resume.o
;FILE tx_thread_shell_entry.o
;FILE tx_thread_suspend.o
;FILE tx_thread_system_preempt_check.o
;FILE tx_thread_system_resume.o
;FILE tx_thread_system_suspend.o
;FILE tx_thread_terminate.o
;FILE tx_thread_time_slice.o
;FILE tx_thread_timeout.o
;FILE tx_timer_activate.o
;FILE tx_timer_change.o
;FILE tx_timer_create.o
;FILE tx_timer_deactivate.o
;FILE tx_timer_delete.o
;FILE tx_timer_expiration_process.o
;FILE tx_timer_info_get.o
_tx_timer_info_get <= USED 0
_tx_timer_nearest_count_ <= USED 0
;FILE tx_timer_initialize.o
_tx_timer_initialize <= USED 0
;FILE tx_timer_system_activate.o
;FILE tx_timer_system_deactivate.o
;FILE tx_timer_thread_entry.o
_tx_timer_thread_register_hook <= USED 0
;FILE txe_queue_front_send.o
_txe_queue_front_send <= USED 0
;FILE txe_thread_relinquish.o
_txe_thread_relinquish <= USED 0
;FILE uac_dialogmgr.o
;FILE uas_dialogmgr.o
;FILE udc_driver.o
UDCDriverActivateHardware <= USED 0
UDCDriverClearEndpointInterrupt <= USED 0
UDCDriverClearEventInterrupt <= USED 0
UDCDriverConfigureEndpoints <= USED 0
UDCDriverDatabaseReset <= USED 0
UDCDriverDeactivateHardware <= USED 0
UDCDriverDisableEndpointInterrupt <= USED 0
UDCDriverDisableEventInterrupt <= USED 0
UDCDriverEnableEndpointInterrupt <= USED 0
UDCDriverEnableEventInterrupt <= USED 0
UDCDriverEndpointActivate <= USED 0
UDCDriverEndpointClearReceiveStatus <= USED 0
UDCDriverEndpointClearStall <= USED 0
UDCDriverEndpointDeactivate <= USED 0
UDCDriverEndpointFlush <= USED 0
UDCDriverEndpointSetupIN <= USED 0
UDCDriverEndpointSetupINMultiTransmitWithDma <= USED 0
UDCDriverEndpointSetupOUT <= USED 0
UDCDriverEndpointStall <= USED 0
UDCDriverForceHostResume <= USED 0
UDCDriverGetCurrentConfigurationSettings <= USED 0
UDCDriverIsDeviceControllerEnabled <= USED 0
UDCDriverIsHostEnabledRemoteWakeup <= USED 0
UDCDriverPhase1Init <= USED 0
UDCDriverPhase2Init <= USED 0
UDCDriverReadFromFifo <= USED 0
UDCDriverResume <= USED 0
UDCDriverSuspend <= USED 0
UDCDriverWriteToFifo <= USED 0
UDCPadActivate <= USED 0
;FILE udp.o
udp_bind_netif <= USED 0
udp_new_ip_type <= USED 0
udp_send <= USED 0
;FILE ui_log_hal.o
__ui_log_mutex <= USED 0
is_app_log_enable <= USED 0
is_hal_log_enable <= USED 0
is_raw_log_enable <= USED 0
log_hal_printf <= USED 0
ui_log_disable <= USED 0
ui_log_enable <= USED 0
ui_log_get_flag <= USED 0
ui_log_get_trace_level <= USED 0
ui_log_init <= USED 0
ui_log_rel_proc <= USED 0
ui_log_set_filter <= USED 0
ui_log_set_module_level <= USED 0
ui_log_set_trace_level <= USED 0
ui_log_xprintf <= USED 0
;FILE ui_os_entry.o
is_mUI_enabled <= USED 0
mHAL_AudioHAL_AifBindHeadsetDetectionCB <= USED 0
mHAL_AudioHAL_AifGetVolume <= USED 0
mHAL_AudioHAL_AifHeadsetDetection <= USED 0
mHAL_AudioHAL_Get_Hp_currentState <= USED 0
mHAL_AudioHAL_Is_Earphone_In <= USED 0
mHAL_AudioHAL_SetResBufCnt <= USED 0
mHAL_AudioHAL_set_close_delay <= USED 0
mHAL_DumpSilentEnable <= USED 0
mHAL_IsDataConnectOK <= USED 0
mHAL_IsDumpSilentEnable <= USED 0
mHAL_IsSilentResetEnable <= USED 0
mHAL_SilentResetDisable <= USED 0
mHAL_SilentResetEnable <= USED 0
mHAL_USBDeviceSelect <= USED 0
mHAL_appbt_config_get_hci_mode <= USED 0
mHAL_appbt_config_hci_mode <= USED 0
mHAL_calloc_ex <= USED 0
mHAL_cpcore_fc_config <= USED 0
mHAL_diag_output_dev_is_sd <= USED 0
mHAL_diag_output_dev_is_uart <= USED 0
mHAL_diag_output_dev_is_usb <= USED 0
mHAL_get_build_time <= USED 0
mHAL_get_platform_new_version <= USED 0
mHAL_get_software_version <= USED 0
mHAL_http_client_stop <= USED 0
mHAL_is_ril_ready <= USED 0
mHAL_is_shutdown_OK <= USED 0
mHAL_is_shutdown_ps_OK <= USED 0
mHAL_is_uart_to_diag <= USED 0
mHAL_is_uart_to_uart <= USED 0
mHAL_platform_start_ota_bypsram <= USED 0
mHAL_set_build_time <= USED 0
mHAL_set_cp_uart_log_off <= USED 0
mHAL_set_cp_uart_log_on <= USED 0
mHAL_set_cprunmode <= USED 0
mHAL_set_diag_dev_sd <= USED 0
mHAL_set_diag_dev_usb <= USED 0
mHAL_set_diag_log_off <= USED 0
mHAL_set_diag_log_on <= USED 0
mHAL_set_uart_dev_diag <= USED 0
mHAL_set_uart_dev_uart <= USED 0
mHAL_set_uart_log_off <= USED 0
mHAL_set_uart_log_on <= USED 0
mHAL_set_ui_uart_log_off <= USED 0
mHAL_set_ui_uart_log_on <= USED 0
mHAL_setlanguageforfota <= USED 0
mHAL_shutdown_ps_request <= USED 0
mHAL_shutdown_request <= USED 0
mUI_get_adp_task_handle <= USED 0
mUI_get_call_service_status <= USED 0
mUI_get_mmi_task_handle <= USED 0
mUI_is_bootup_standby_done <= USED 0
mUI_is_shutdown_status <= USED 0
mUI_send_msg_for_usb <= USED 0
mUI_send_msg_for_usb_tip <= USED 0
mUI_set_pwronoff_lowpower_diable <= USED 0
mUI_ui_task_init <= USED 0
ui_get_udid <= USED 0
ui_main <= USED 0
ui_os_api_init <= USED 0
ui_set_udid <= USED 0
;FILE ui_os_flag.o
UOS_FlagInit <= USED 0
;FILE ui_os_message.o
UOS_GetMessageQueueSize <= USED 0
UOS_IsEventAvailable <= USED 0
UOS_MessageQueueInit <= USED 0
UOS_MsgQAvailableSize <= USED 0
UOS_MsgQEnqueued <= USED 0
UOS_SendEvent <= USED 0
UOS_SendMsg <= USED 0
UOS_UIMsgQEnqueued <= USED 0
UOS_WaitEvent <= USED 0
UOS_WaitMsg <= USED 0
hwtest_send_msg <= USED 0
;FILE ui_os_mutex.o
UOS_MutexInit <= USED 0
;FILE ui_os_semaphore.o
UOS_Semaphore_init <= USED 0
;FILE ui_os_task.o
UOS_ChangeCoreFreqTo624M <= USED 0
UOS_DelayTaskRunning <= USED 0
UOS_DisableFrequencyConversion <= USED 0
UOS_EnableFrequencyConversion <= USED 0
UOS_GetCurrentStackInfo <= USED 0
UOS_GetCurrentTaskHandle <= USED 0
UOS_GetCurrentTaskId <= USED 0
UOS_GetCurrentTaskName <= USED 0
UOS_GetMMICurrentHeapSize <= USED 0
UOS_GetTaskRef <= USED 0
UOS_GetUpTime <= USED 0
UOS_ResumeTask_Ex <= USED 0
UOS_SleepMs <= USED 0
UOS_SleepSeconds <= USED 0
UOS_SuspendTask <= USED 0
UOS_SuspendTaskEx <= USED 0
UOS_TaskInit <= USED 0
;FILE ui_os_timer.o
UOS_InitTimer <= USED 0
UOS_KillFunctionTimer <= USED 0
UOS_KillTimerEX <= USED 0
UOS_PeriodTimerHandleRsp <= USED 0
UOS_StartFunctionTimer_periodic <= USED 0
UOS_StartFunctionTimer_single <= USED 0
UOS_StartTimerEX <= USED 0
UOS_get_FunctionTimer <= USED 0
;FILE umm_psms.o
MmSearchUmmConnections <= USED 0
UmmReleasePsmsConnections <= USED 0
UmmSendPsmsErrorIndWithValues <= USED 0
;FILE umm_sim.o
;FILE uos.o
uos_chgpriority_task <= USED 0
uos_create_flag <= USED 0
uos_create_task <= USED 0
uos_delete_flag <= USED 0
uos_delete_task <= USED 0
uos_flag_init <= USED 0
uos_free_msg <= USED 0
uos_free_mutex <= USED 0
uos_get_current_task_handle <= USED 0
uos_get_current_task_id <= USED 0
uos_get_current_task_name <= USED 0
uos_get_msgcnt <= USED 0
uos_get_ticks <= USED 0
uos_in_urgent_section <= USED 0
uos_init <= USED 0
uos_is_event_available <= USED 0
uos_message_queue_init <= USED 0
uos_mutex_init <= USED 0
uos_new_message_queue <= USED 0
uos_new_mutex <= USED 0
uos_release_mutex <= USED 0
uos_resume_task <= USED 0
uos_send_event <= USED 0
uos_send_msg <= USED 0
uos_set_flag <= USED 0
uos_sleep <= USED 0
uos_sleep_ms <= USED 0
uos_suspend_task <= USED 0
uos_suspend_task_ex <= USED 0
uos_take_mutex <= USED 0
uos_task_init <= USED 0
uos_timer_delete <= USED 0
uos_timer_start_ms <= USED 0
uos_try_take_mutex <= USED 0
uos_wait_event <= USED 0
uos_wait_flag <= USED 0
uos_wait_msg <= USED 0
;FILE usb1_device.o
USB1DeviceCableDetectionNotify <= USED 0
USB1DeviceEP0InterruptHandler <= USED 0
USB1DeviceEP0ProcessSetupCmd <= USED 0
USB1DeviceEP0ProcessSetupState <= USED 0
USB1DeviceEP0TransferCompleted <= USED 0
USB1DeviceEndpointAbort <= USED 0
USB1DeviceEndpointClose <= USED 0
USB1DeviceEndpointGetHWCfg <= USED 0
USB1DeviceEndpointMultiTransmit <= USED 0
USB1DeviceEndpointOpen <= USED 0
USB1DeviceEndpointReceive <= USED 0
USB1DeviceEndpointReceiveCompleted <= USED 0
USB1DeviceEndpointReceiveCompletedExt <= USED 0
USB1DeviceEndpointStall <= USED 0
USB1DeviceEndpointTransmit <= USED 0
USB1DeviceEventNotify <= USED 0
USB1DeviceIsControllerEnabled <= USED 0
USB1DeviceMultiTransmitNotifyFn <= USED 0
USB1DevicePhase1Init <= USED 0
USB1DevicePhase2Init <= USED 0
USB1DeviceReceiveNotifyFn <= USED 0
USB1DeviceRegister <= USED 0
USB1DeviceRegisterPatchTempFunc <= USED 0
USB1DeviceTransmitNotifyFn <= USED 0
USB1DeviceVendorClassResponse <= USED 0
;FILE usb2_device.o
Check_AP2CP_Conflict <= USED 0
ClearResumePending <= USED 0
EnablePhy28 <= USED 0
GPIOED_LISR <= USED 0
GPIO_InitWakeup <= USED 0
GetResumePending <= USED 0
GetUsbDDRLock <= USED 0
Get_GPIO_WakeupLevel <= USED 0
OSCR0IntervalInMicro_self <= USED 0
PauseMsec <= USED 0
SendUsbRemoteWakeup <= USED 0
SetUsbDDRLock <= USED 0
SetUsbPmLock <= USED 0
USB2DeviceDisableUsbInterrupt <= USED 0
USB2DevicePhase1Init <= USED 0
USB2GetDTDEntry <= USED 0
USB_GPIO_WK <= USED 0
USB_WAKEUP_CALLBACK_REGISTER <= USED 0
_usb_cancel_all_transfer <= USED 0
usbDeviceEndpoint_init <= USED 0
usb_get_dtd_nums <= USED 0
;FILE usb_descriptor.o
USB2ConfigureCdromDescriptor <= USED 0
USB2IsCdromOnlyDescriptor <= USED 0
USB2MgrChangeUsbMode <= USED 0
USB2MgrDeviceUnplugPlug_SD <= USED 0
USB2MgrDeviceUnplugPlug_WebDav <= USED 0
USB2MgrMassStorageEnable <= USED 0
USB2ReEnumerate <= USED 0
USBIsConnected <= USED 0
get_current_usb_app_mask <= USED 0
;FILE usb_device.o
USBCableDetectionNotify <= USED 0
USBDeviceGetUSBSpeedInUse <= USED 0
USBDeviceGetUSBVersionInUse <= USED 0
USBDevicePhase1Init <= USED 0
USBDeviceRegister <= USED 0
USBDeviceRegisterPatchTempFunc <= USED 0
USBDeviceSetDescriptors <= USED 0
USBDeviceStatusGet <= USED 0
;FILE usb_init.o
mvUsbCheckProdctionMode <= USED 0
;FILE usbcomdv.o
usbCommDevAppClassDevicePayload <= USED 0
usbCommDevAppClassDeviceRequest <= USED 0
usbCommDevInitialise <= USED 0
usbCommDevMaxOutTransfer <= USED 0
;FILE usbcomnotify.o
usbCommNotifyConfigure <= USED 0
usbCommNotifyInitialise <= USED 0
usbCommNotifyTxSerialState <= USED 0
;FILE usbmgrttpal.o
USBMgrTTPALGetFromQ <= USED 0
USBMgrTTPALInsertToQ <= USED 0
USBMgrTTPALUpdateEnabledApps <= USED 0
UsbGetRequestID <= USED 0
UsbMgrTTPALInit <= USED 0
UsbMgrTTPALPhysical2logicalEP <= USED 0
UsbMgrTTPALRegister <= USED 0
UsbMgrTTPALSetMuxUART <= USED 0
UsbMgrTTPALSetMuxUART_INT <= USED 0
UsbMgrTTPALSetMuxUSB <= USED 0
UsbMgrTTPALSetMuxUSB_INT <= USED 0
UsbMgrTTPALlogical2PhysicalEP <= USED 0
usbAppifDefaultRxBufferStructure <= USED 0
usbAppifDefaultTransmitRequest <= USED 0
usbAppifDynamicSelectionValid <= USED 0
usbAppifFlushRxBuffer <= USED 0
usbAppifFlushTransmitRequestQueue <= USED 0
usbAppifFreeRxBuffer <= USED 0
usbAppifGetConfiguration <= USED 0
usbAppifGetDefaultConfiguration <= USED 0
usbAppifInitRxBuffer <= USED 0
usbAppifInitTxQueue <= USED 0
usbAppifPauseRxFlow <= USED 0
usbAppifRequestReceiveBufferFreeSpace <= USED 0
usbAppifRequestReceiveData <= USED 0
usbAppifSelectDynamicConfiguration <= USED 0
usbAppifStallInEndpoint <= USED 0
usbAppifStallOutEndpoint <= USED 0
usbAppifTransmit <= USED 0
usbDclControlTransferStatusStage <= USED 0
usbDclEnableEndpointZeroEvents <= USED 0
usbDevAppGetInterfaceNumber <= USED 0
usbHandleError <= USED 0
usbNotifyError <= USED 0
usbStackStallEndpointAddress <= USED 0
usbTargetTask <= USED 0
;FILE usbnet.o
ModeVolteDLParseCidToPacketType <= USED 0
PlatformPsDlPathLogDisable <= USED 0
PlatformPsDlPathLogEnable <= USED 0
usbnet_allocmem_extend <= USED 0
usbnet_putmem <= USED 0
;FILE usim.o
GPIO33High <= USED 0
GPIO33Low <= USED 0
GPIO35High <= USED 0
GPIO35Low <= USED 0
GetUSIMDDRLock <= USED 0
HotplugTestGPIOInit <= USED 0
TestUsimGetStatus <= USED 0
USIMATRGet <= USED 0
USIMPhase1Init <= USED 0
USIMRxFIFO_Clear <= USED 0
USIMStatusGet <= USED 0
USIMVersionGet <= USED 0
USIMWarmReset <= USED 0
USIM_DetectFoundByHW <= USED 0
disable_usim_swap <= USED 0
enable_usim_swap <= USED 0
get__USIMDLstates_string <= USED 0
get_sim_swap_flag <= USED 0
get_usim_2_insert_status <= USED 0
usim_2_det_lisr <= USED 0
usim_2_det_task_entry <= USED 0
usim_2_det_timer_handler <= USED 0
usim_2_detect_init <= USED 0
usim_2_hotplug_trigger <= USED 0
usim_2_op_enter <= USED 0
usim_2_op_exit <= USED 0
;FILE usim_d2.o
USIMD2Prepare <= USED 0
USIMD2Recover <= USED 0
;FILE usim_dl.o
;FILE usim_hw.o
USIM_HW_RXRead_Dummy <= USED 0
USIM_HW_init <= USED 0
;FILE usim_transport.o
;FILE usimcfg.o
;FILE usimcmd.o
SimUiccExecuteActivateFileCommand <= USED 0
SimUiccExecuteDeactivateFileCommand <= USED 0
SimUiccExecuteGetChallengeCommand <= USED 0
SimUiccExecuteIncreaseCommand <= USED 0
SimUiccExecuteRunGsmAlgCommand <= USED 0
;FILE usimdec.o
SimUiccDecodeCallDuration <= USED 0
;FILE usimenc.o
SimUiccEncodeAct <= USED 0
SimUiccEncodeEst <= USED 0
SimUiccEncodeLi <= USED 0
SimUiccEncodeStartHfn <= USED 0
;FILE usiminit.o
;FILE usimmmreq.o
;FILE usimreqp1.o
SimUiccIsReadPlmnMore <= USED 0
;FILE usimreqp2.o
;FILE usimreqp3.o
;FILE usimrqp2p.o
;FILE usimstate.o
SetSimManagerExternalTasks <= USED 0
SimSendGLSwapFlag <= USED 0
;FILE usimutil.o
SimUiccSetHomezoneDir <= USED 0
;FILE ut_mcc_mnc.o
utmmPlPlmnsMatch <= USED 0
;FILE ut_sm.o
SmReadSemiOctetData <= USED 0
SmWriteSemiOctetData <= USED 0
utsmConvertOctetsToChars <= USED 0
utsmConvertUCS2ArrayToInt8Array <= USED 0
utsmGetHeaderLengthLen <= USED 0
utsmGetUserMessage <= USED 0
utsmGetUserMsgCharLen <= USED 0
utsmReadAddressData <= USED 0
utsmWriteAddressData <= USED 0
;FILE utbitfnc.o
;FILE utebmdyn.o
utEbmmAllocMemory <= USED 0
utEbmmFreeAllTaskMemory <= USED 0
utEbmmFreeMemory <= USED 0
;FILE util.o
util_del_var <= USED 0
;FILE utilities.o
BoardInfoForTracking <= USED 0
DbgArrayPrintf <= USED 0
DbgEnableUart <= USED 0
DbgInitFifo <= USED 0
DbgInitUart <= USED 0
DbgLogTask <= USED 0
DbgPrintf_sim <= USED 0
DbgShow <= USED 0
DelayInMilliSecond <= USED 0
DisableModeChange <= USED 0
EnableModeChange <= USED 0
GetAuxAdcRtnVbat_customized <= USED 0
GetCraneLVbatNocali <= USED 0
GetGpioGroup <= USED 0
GetGpioLvl <= USED 0
GetPm803RtnAdc <= USED 0
GetPm803RtpAdc <= USED 0
GetTimer0CNT <= USED 0
Get_Chip_Fuse_Info <= USED 0
LogFlush <= USED 0
NVMFlashErase <= USED 0
OSAListAllOsaModuleInfo <= USED 0
RPCFuncRegister <= USED 0
ReadCCNT_with_PP <= USED 0
Read_Crane_Flash_UID <= USED 0
SetCraneLAdcWork <= USED 0
SetGpioLvl <= USED 0
StopIMLLog <= USED 0
TestCraneLAdc <= USED 0
TestCraneLAdcAPI <= USED 0
Timer0IntervalInMicro <= USED 0
Timer0_Switch <= USED 0
WDTTest <= USED 0
alios_print <= USED 0
check_stack <= USED 0
cp_clear_gpio_ed <= USED 0
cp_gpio_init <= USED 0
cp_init_gpio_ed <= USED 0
date_to_utc <= USED 0
dump_stack_info <= USED 0
dump_task_info <= USED 0
getPsramType <= USED 0
get_cranel_adc_for_tsc <= USED 0
get_cranel_vbat <= USED 0
get_sdcard_log_partition_status <= USED 0
gps_bin_print <= USED 0
hsic_pm_gpio_setting <= USED 0
isChip_3601S <= USED 0
isChip_3621S <= USED 0
keypadConfigure <= USED 0
keypadNotifyEventBind <= USED 0
log_sdcard_partition_is_multi <= USED 0
log_sdcard_set_partition <= USED 0
mmi_printf <= USED 0
nvm_printf <= USED 0
recordPreExcepInfo <= USED 0
rf_rtn_adc_calib_init <= USED 0
rti_SendQueue_record <= USED 0
rti_SetEvent_record <= USED 0
rti_dump_ipc_hisr <= USED 0
rti_dump_ipc_lisr <= USED 0
rti_ee_save <= USED 0
rti_get_eehandler_magic <= USED 0
rti_icu_switch_to <= USED 0
rti_init <= USED 0
rti_init_mode <= USED 0
rti_pm_sleep_state <= USED 0
rti_pm_sleep_step <= USED 0
rti_pm_sleep_time <= USED 0
rti_printf <= USED 0
rti_record_aam_status <= USED 0
rti_rt_test <= USED 0
rti_set_CPMnewPowerState <= USED 0
rti_set_wakeupBits <= USED 0
rti_thread_dump_buf <= USED 0
sdcard_fat_fs_is_ready <= USED 0
sdcard_log_single_partition <= USED 0
sdcard_log_two_partition <= USED 0
seagull_uart_init_diag <= USED 0
sw_jtag_cmd <= USED 0
switch_to <= USED 0
thread_measurement_disable <= USED 0
thread_measurement_enable <= USED 0
timerTCRconfigureForSync <= USED 0
timerTMRsetForSync <= USED 0
u_readUINT16 <= USED 0
u_readUINT32 <= USED 0
u_readUINT8 <= USED 0
u_writeUINT16 <= USED 0
u_writeUINT32 <= USED 0
u_writeUINT8 <= USED 0
uart_dump <= USED 0
vbat_pm803_init <= USED 0
ws_printf <= USED 0
;FILE utils_c.o
GetCpuFamily <= USED 0
GetCpuVersion <= USED 0
GetCpuVersion_ICAT <= USED 0
;FILE utl_trmsg.o
SetutlTraceLevel <= USED 0
;FILE utlconvert.o
;FILE utllinkedList.o
utlDoGetTailNode <= USED 0
utlPutHeadList <= USED 0
utlPutTailList <= USED 0
;FILE utlsemaphore.o
utlAcquireSharedAccess <= USED 0
utlInitSemaphore_queue <= USED 0
utlReleaseSharedAccess <= USED 0
;FILE utltime.o
utlCurrentTime <= USED 0
utlFormatDate <= USED 0
utlFormatDateTime <= USED 0
utlFormatTime <= USED 0
utlPause <= USED 0
;FILE utltimer.o
utlIsTimerRunning <= USED 0
utlQueryTimer <= USED 0
utlStartAbsoluteTimer <= USED 0
;FILE utltrace.o
utlAddTraceCriteria <= USED 0
utlDeleteTraceCriteria <= USED 0
utlDumpTracePegs <= USED 0
utlSetProcessName <= USED 0
;FILE utper.o
PerDecEndOfBitAlignedDataWithLength <= USED 0
PerDecSignedInt32 <= USED 0
PerDecStartOfBitAlignedDataWithLength <= USED 0
PerDecVariableOctetStringWithoutLength <= USED 0
PerEncConstrainedLengthWithoutMax <= USED 0
PerEncEndOfBitAlignedDataWithLength <= USED 0
PerEncEndOfBitAlignedDataWithLengthCap <= USED 0
PerEncSignedInt16 <= USED 0
PerEncSignedInt32 <= USED 0
PerEncStartOfBitAlignedDataWithLength <= USED 0
PerEncStartOfBitAlignedDataWithLengthCap <= USED 0
PerEncUnconstrainedBitString <= USED 0
PerEncVariableOctetString <= USED 0
PerEncVariableOctetStringWithoutMax <= USED 0
PerReturnLteCapExceed <= USED 0
PerReturnLteCapExceed1 <= USED 0
PerReturnLteCapExceed2 <= USED 0
PerReturnLteCapExceed2Initialized <= USED 0
PerSetLteCapExceed <= USED 0
PerSetLteCapExceed1 <= USED 0
PerSetLteCapExceed2 <= USED 0
PerSetLteCapExceedInit <= USED 0
PerSkipUnconstrainedBitString <= USED 0
;FILE vad1.o
;FILE version.o
ExGetRFBinFWDate <= USED 0
ExGetRFBinFWVersion <= USED 0
GetSeagullFWVersionAndDate <= USED 0
GetUUID <= USED 0
diagGetCPInterfaceVersion <= USED 0
diagGetOSVersion <= USED 0
psIsNVMAvailable <= USED 0
psIsPS_Running <= USED 0
;FILE vgmxusbnull.o
usbCommDevAppInitialise <= USED 0
;FILE vlc_decode.o
;FILE vlc_dequant.o
;FILE vopDec.o
;FILE vpath_ctrl.o
CraneCodecADCOnOff_internal <= USED 0
voicePathRateSet <= USED 0
vpathCTMControl <= USED 0
vpathCodecClockSet <= USED 0
vpathCodecDBConfig <= USED 0
vpathCodecIfChange <= USED 0
vpathCodecIfChange_Env <= USED 0
vpathDTMFControl_Env <= USED 0
vpathDTMFDetectionControl_Env <= USED 0
vpathDebugCmd <= USED 0
vpathDmaIntInd <= USED 0
vpathECallDataGet <= USED 0
vpathECallDataSet <= USED 0
vpathECallVoiceGet <= USED 0
vpathECallVoiceSet <= USED 0
vpathFMControl <= USED 0
vpathGSSPControl <= USED 0
vpathGSSPRead <= USED 0
vpathIsPcmSelfInvoked <= USED 0
vpathKWSControl <= USED 0
vpathMp3HpfControl <= USED 0
vpathMuteControl_Env <= USED 0
vpathPcmStreamControl_Env <= USED 0
vpathPlayStream <= USED 0
vpathPrintVoiceTestControl <= USED 0
vpathRecordStream <= USED 0
vpathRxTxPacketForDebugSet <= USED 0
vpathRxbControl <= USED 0
vpathSelfInvocation_Env <= USED 0
vpathSetSelfInvocation <= USED 0
vpathSetSspaTestMode <= USED 0
vpathSideToneControl_Env <= USED 0
vpathStartDefault_Env <= USED 0
vpathVadDumpControl <= USED 0
vpathVibrationControl <= USED 0
vpathVocoderControl_Env <= USED 0
vpathVocoderRate <= USED 0
vpathVocoderType <= USED 0
vpathVoiceControl <= USED 0
vpathVoiceControl_Env <= USED 0
vpathVoiceHandover_Env <= USED 0
vpathVoiceTestControl_Env <= USED 0
vpathVolumeControl_Env <= USED 0
wbSpeechSupportedQuery <= USED 0
;FILE vpath_data.o
;FILE vpath_handler.o
DecoderDelay10ms <= USED 0
DecoderDelay15ms <= USED 0
DisableTimer <= USED 0
EnableTimer <= USED 0
IMSBindRxFrame <= USED 0
amrBindRxPcmVoLTE <= USED 0
amrBindRxRequest <= USED 0
handleAudioIslandCompanderReport <= USED 0
handleAudioIslandRxAMR <= USED 0
handleAudioIslandTxAMR <= USED 0
;FILE vpath_mgr.o
audioGsmResumeCB_L1G <= USED 0
audioGsmResumeCB_internal <= USED 0
audioGsmSuspendCB_L1G <= USED 0
audioGsmSuspendCB_internal <= USED 0
audioGsmTestStartCB <= USED 0
audioGsmTestStopCB <= USED 0
audioRATResume <= USED 0
audioRATSuspend <= USED 0
lteRATResume <= USED 0
lteRATSuspend <= USED 0
vpathOsResourcesCreate <= USED 0
;FILE vpath_proxy.o
vpathConfig <= USED 0
vpathHook <= USED 0
vpathRegister <= USED 0
vpathRegisterRx <= USED 0
vpathRegisterTx <= USED 0
vpathSetMode <= USED 0
vpathTick <= USED 0
vpathTickRx <= USED 0
vpathTickTx <= USED 0
;FILE wamr_rate.o
IMSSourceRateControl <= USED 0
set_AMR_Rate <= USED 0
;FILE wamr_rx.o
;FILE wamr_services.o
GetState <= USED 0
TxPocAMRGet <= USED 0
amrBindConnectionClose <= USED 0
amrBindConnectionEstablish <= USED 0
amrResume_internal <= USED 0
amrServiceActivate_Envelope <= USED 0
amrServiceDeActivate <= USED 0
amrSuspend_internal <= USED 0
ctmNegotiationReportBind <= USED 0
getGlobalDTMFCode <= USED 0
;FILE wamr_tx.o
IMSBindTxFrame <= USED 0
amrBindTxBitstreamFrameVoLTE <= USED 0
amrBindTxFrame <= USED 0
;FILE watchdog.o
PMIC_WD_TIMER_SET_DYNAMIC <= USED 0
watchdogActivate_ICAT <= USED 0
watchdogConfigINT_ICAT <= USED 0
watchdogConfigRST_ICAT <= USED 0
watchdogConfigurationGet <= USED 0
watchdogDeactivate_ICAT <= USED 0
watchdogDebugModeSET_ICAT <= USED 0
watchdogIndicationClear <= USED 0
watchdogKickOFF_ICAT <= USED 0
watchdogKick_ICAT <= USED 0
watchdogReset <= USED 0
watchdogReset_ICAT <= USED 0
watchdogStatusGet <= USED 0
watchdogStatusGet_ICAT <= USED 0
;FILE wb4m_psram_lpm_table.o
WB4M_psram_lpm_table <= USED 0
;FILE webrtc_neteq.o
WebRtcNetEQ_CodecDbAdd <= USED 0
WebRtcNetEQ_CodecDbGetCodecInfo <= USED 0
WebRtcNetEQ_CodecDbGetSizeInfo <= USED 0
WebRtcNetEQ_CodecDbRemove <= USED 0
WebRtcNetEQ_CodecDbReset <= USED 0
WebRtcNetEQ_ContextDestroy <= USED 0
WebRtcNetEQ_GetErrorCode <= USED 0
WebRtcNetEQ_GetErrorName <= USED 0
WebRtcNetEQ_StreamDBReset <= USED 0
WebRtcNetEQ_StreamDbRemove <= USED 0
WebRtcNetEQ_strncpy <= USED 0
;FILE weight_a.o
;FILE wificontroll.o
FakeMM2WifiStartScan2FTReq <= USED 0
FakeMM2WifiStartScanReqExt1 <= USED 0
FakeMM2WifiStartScanReqExt2 <= USED 0
FakeMM2WifiStartScanReqExt3 <= USED 0
FakeResetAbortByRRC <= USED 0
FakeResetAbortTimer <= USED 0
FakeResetScanTimer <= USED 0
FakeResetgetIratDsWifiStartCnfcause1 <= USED 0
FakeResetgetIratDsWifiStartCnfcause2 <= USED 0
FakeResetgetIratDsWifiStartCnfcause3 <= USED 0
FakeSetRejectByRRC <= USED 0
FakegetIratDsWifiAbortReq <= USED 0
FakegetIratDsWifiStartCnf <= USED 0
FakegetIratDsWifiStartCnfcause1 <= USED 0
FakegetIratDsWifiStartCnfcause2 <= USED 0
FakegetIratDsWifiStartCnfcause3 <= USED 0
FakesetAbortTimer <= USED 0
FakesetScanTimer <= USED 0
L1IpcDspMsgIpcWIFIHandler <= USED 0
MM2WifiStartScan2FTModeReqProcess <= USED 0
MM2WifiStartScanReqProcess <= USED 0
MM2WifiStopScanReqProcess <= USED 0
PreCpCoreFreqChangeTo416 <= USED 0
PreprocessWifiHotspotInfoInd <= USED 0
PrintLeftTimeInfor <= USED 0
Timerexpire2Wifi2rrDsSendIratDsWifiStartReq <= USED 0
Timerexpire2Wifi2rrDsSendIratDsWifiStartReqProcess <= USED 0
WFSendRssiCorrectIPC2PLP <= USED 0
Wifi2rrDsSendIratDsWifiStartReq <= USED 0
WifiBgStartScanReqProcess <= USED 0
WifiPostFreePoolMain <= USED 0
WifiScanCheckVersionEnable <= USED 0
WifiSpyrrDsSendIratDsWifiAbortCnf <= USED 0
WifiSpyrrDsSendIratDsWifiStartReq <= USED 0
bubbleSort <= USED 0
getIratDsWifiStartCnfProcess <= USED 0
getPhyGapFinishIndProcess <= USED 0
getPhyScanIndProcess <= USED 0
getPhyStopScanCnfProcess <= USED 0
getWifiAbortReq <= USED 0
getWifiAbortReqProcess <= USED 0
gfiScanGapStartInd <= USED 0
gfiWifiScanAbortCnf <= USED 0
gwfiScanGapStartIndProcess <= USED 0
judgeBgPreprocessWifiHotspotInfoInd <= USED 0
judgeLeftTimetoWifiScanReq <= USED 0
memBssidcmpNotEqual <= USED 0
memBssidcmpWithZero <= USED 0
memcmp_1 <= USED 0
plWIFIMainLoop <= USED 0
plWIFIMainLoopMsgHandler <= USED 0
plWIFIlowMainLoop <= USED 0
plWIFIlowMainLoopMsgHandler <= USED 0
setTimer2Wifi2rrDsSendIratDsWifiStartReq <= USED 0
setTimer2WifiBg2rrDsSendIratDsWifiStartReq <= USED 0
;FILE wifispy.o
wifiSpyInit <= USED 0
;FILE wmf_to_ets.o
;FILE x509.o
mbedtls_x509_dn_gets <= USED 0
mbedtls_x509_get_alg_null <= USED 0
mbedtls_x509_key_size_helper <= USED 0
mbedtls_x509_serial_gets <= USED 0
mbedtls_x509_sig_alg_gets <= USED 0
;FILE x509_crt.o
mbedtls_x509_crt_info <= USED 0
mbedtls_x509_crt_parse_der_nocopy <= USED 0
mbedtls_x509_crt_verify <= USED 0
mbedtls_x509_crt_verify_with_profile <= USED 0
mbedtls_x509_subjectid_gets <= USED 0
;FILE xcapclient.o
;FILE xdmc_api.o
Xdmc_Close <= USED 0
Xdmc_Print_Intf_Details <= USED 0
;FILE xdmc_common.o
Xdmc_Generate_CmdIdentifier <= USED 0
Xdmc_Generate_Listname <= USED 0
Xdmc_Get_Header <= USED 0
allocXdmcData <= USED 0
xdmc_random <= USED 0
;FILE xdmc_core.o
Xdmc_CleanNc <= USED 0
Xdmc_Init_DNS <= USED 0
Xdmc_release <= USED 0
;FILE xdmc_dns.o
;FILE xdmc_selector.o
addDocSelectorStr <= USED 0
;FILE xf_device_fun.o
CRC16_1021 <= USED 0
GetRevCrc_16 <= USED 0
crc16_ccitt <= USED 0
drv_CheckSum <= USED 0
drv_HexStrToByte <= USED 0
drv_device_bin <= USED 0
drv_get_char_vale <= USED 0
drv_get_form <= USED 0
drv_get_str_value <= USED 0
drv_get_uint16_vale <= USED 0
;FILE xf_test.o
Set_air_calibrate <= USED 0
Set_heartrate <= USED 0
Set_sar_capacitor_detection <= USED 0
app_adaptor_get_version <= USED 0
hwtest_get_air_calibrate_test <= USED 0
hwtest_get_heartrate_test <= USED 0
hwtest_get_sar_capacitor_detection <= USED 0
hwtest_gps_is_ok <= USED 0
hwtest_is_ready <= USED 0
hwtest_lcd_is_ok <= USED 0
hwtest_mode_set <= USED 0
hwtest_nfc_is_ok <= USED 0
hwtest_set_alipay_ok <= USED 0
hwtest_set_gps_os_htask <= USED 0
hwtest_set_gps_type <= USED 0
hwtest_set_gsensor_ok <= USED 0
hwtest_set_hrs_ok <= USED 0
hwtest_set_lcd_ok <= USED 0
hwtest_set_mac <= USED 0
hwtest_set_nfc_ok <= USED 0
hwtest_set_press_key <= USED 0
hwtest_set_ready <= USED 0
hwtest_set_sw_version <= USED 0
hwtest_set_tp_ok <= USED 0
hwtest_set_wifi_ok <= USED 0
hwtest_tp_is_ok <= USED 0
is_hwtest_mode <= USED 0
;FILE xllp_dma.o
XllpDmacClearDmaInterrupt <= USED 0
XllpDmacDescriptorFetch <= USED 0
XllpDmacDisableEORInterrupt <= USED 0
XllpDmacEnableEORInterrupt <= USED 0
XllpDmacEnableStopIrqInterrupt <= USED 0
XllpDmacStartTransfer <= USED 0
configDescriptor <= USED 0
loadDescriptor <= USED 0
readDmaStatusRegister <= USED 0
;FILE xmlConfInfoParser.o
;FILE xmlConfRecipientListParser.o
XmlCRList_ParseDoc <= USED 0
;FILE xrabmmain.o
GpLlc2Task <= USED 0
GpLlc2Task1 <= USED 0
GpLlcTask <= USED 0
GpLlcTask1 <= USED 0
UpDlbg2Task <= USED 0
UpDlbg2Task1 <= USED 0
UpDlbgTask1 <= USED 0
;FILE xrabmutil.o
XRabmUpdatePdpState <= USED 0
;FILE xscale_stubs.o
ACDAccessoryDetectInitialise <= USED 0
ACDAccessoryDetectStatus <= USED 0
ACDBind <= USED 0
PMUPhase2Init <= USED 0
bspBTClockReqEnable <= USED 0
bspShutDown <= USED 0
;FILE xsmmain.o
;FILE ymodem.o
;FILE yxml.o
yxml_eof <= USED 0

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 2006 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/api/inc/urralloctyp.h#3 $
 *   $Revision: #3 $
 *   $DateTime: 2006/11/09 15:19:07 $
 **************************************************************************
 *  File Description :
 **************************************************************************/

#if !defined (URRALLOCTYP_H)
#define       URRALLOCTYP_H

#if !defined (DS3_LTE_ONLY) && !defined(ENABLE_CAT1_LG)


/***************************************************************************
 * Nested Include Files
 **************************************************************************/
#include <system.h>

#if defined(UPGRADE_DSDSWB)
#include <urrtypes.h>
#endif/*UPGRADE_DSDSWB*/

#if !defined (URRC_DISABLE_LOCAL_MEMORY_MANAGER)

/***************************************************************************
 * Manifest Constants
 **************************************************************************/


/***************************************************************************
 *  Build defines
 **************************************************************************/
#if defined (DEVELOPMENT_VERSION)
/* Enable the allocation block list if DEVELOPMENT_VERSION is enabled */
#   if !defined (URRC_ENABLE_ALLOC_BLOCK_LIST)
#   define URRC_ENABLE_ALLOC_BLOCK_LIST
#   endif

/* Check if we need to verify the integrity of the allocated blocks */
#   if defined (URRC_ALLOC_LIST_INTEGRITY_CHECK) && !defined (URRC_ENABLE_ALLOC_BLOCK_LIST)
#   define URRC_ENABLE_ALLOC_BLOCK_LIST
#   endif
#endif


/***************************************************************************
 * Type Definitions
 **************************************************************************/
/* Each allocated block contains this structure at the head of the block. */
typedef struct UrrcMemBlkHeaderTag
{
#if defined (URRC_ENABLE_ALLOC_BLOCK_LIST)
    /* This is used in a linked list, so that a list of allocated blocks
     * can be obtained for debug purposes. These fields must be first in
     * the structure to aid initialisation. */
    struct UrrcMemBlkHeader *nextBlock_p;
    struct UrrcMemBlkHeader *prevBlock_p;
#endif

    Int8 poolId;
    Int8 groupIndex;
    Int16 blockIndex;

#if defined (DEVELOPMENT_VERSION)
    /* This is the address of the owner of this memory block */
    Int32 callerAddress;

    /* This is the actual number of bytes requested */
    Int32 blockSize;
#if defined (DEBUG_MEMORY_LEAK_ON_PC)
    char file_p[16];
    Int32 line;
    char file_p1[16];
    Int32 line1;
#endif

    /* The overrunHeaderCheck array is populated by a fixed sequence of bytes.
     * This is used to check that the end of the header has not been overwritten */
    Int32 overrunHeaderCheck;

#endif /* DEVELOPMENT_VERSION */
}
UrrcMemBlkHeader;

#if defined(UPGRADE_DSDSWB)
/* The pool IDs */
typedef enum MemPoolIdTag
{
    #define MEM_CFG_GET_POOL_ID
    #include <urrallocmem.h>

    NUM_MEM_POOLS,

    /* This is the last pool in the list. This means that the blocks originate from
     * the GKI dynamic memory manager rather than from the local pool */
    DYNAMIC_MEMORY_POOL = NUM_MEM_POOLS
}
MemPoolId;
#endif/*UPGRADE_DSDSWB*/
extern const Int32 urrcMemBlockSize [];

#if defined (DEVELOPMENT_VERSION)
extern const Int32 urrcMemNumPools;
extern const Int32 urrcMemNumBlocks [];
#endif

#if defined (URRC_ALLOC_STATISTICS)
#if defined(UPGRADE_DSDSWB)
extern Int32 urrcMemAllocCount1 [URRC_TASK_NUM][NUM_MEM_POOLS];
extern Int32 urrcMemMaxAllocCount1 [URRC_TASK_NUM][NUM_MEM_POOLS];
extern Int32 urrcMemAllocMisses1 [URRC_TASK_NUM][NUM_MEM_POOLS];

#define urrcMemAllocCount urrcMemAllocCount1[URRC_DSDS_TASK]
#define urrcMemMaxAllocCount urrcMemMaxAllocCount1[URRC_DSDS_TASK]
#define urrcMemAllocMisses urrcMemAllocMisses1[URRC_DSDS_TASK]
#else/*UPGRADE_DSDSWB*/
extern Int32 urrcMemAllocCount [];
extern Int32 urrcMemMaxAllocCount [];
extern Int32 urrcMemAllocMisses [];
#endif/*UPGRADE_DSDSWB*/
#endif

#if defined (URRC_ALLOC_SIZE_DEBUG)
#if defined(UPGRADE_DSDSWB)
extern Int32 urrcMemAllocSize1 [URRC_TASK_NUM][NUM_SIZE_COUNTERS];
extern Int32 urrcMemAllocSizeCount1 [URRC_TASK_NUM][NUM_SIZE_COUNTERS];
extern Int32 urrcMemAllocOtherCount1[URRC_TASK_NUM];
#define urrcMemAllocSize urrcMemAllocSize1[URRC_DSDS_TASK]
#define urrcMemAllocSizeCount urrcMemAllocSizeCount1[URRC_DSDS_TASK]
#define urrcMemAllocOtherCount urrcMemAllocOtherCount1[URRC_DSDS_TASK]
#else/*UPGRADE_DSDSWB*/
extern Int32 urrcMemAllocSize [];
extern Int32 urrcMemAllocSizeCount [];
extern Int32 urrcMemAllocOtherCount;
#endif/*UPGRADE_DSDSWB*/
extern const Int32 urrcMemNumSizeCounters;
#endif

#if defined (URRC_ENABLE_ALLOC_BLOCK_LIST)
#if defined(UPGRADE_DSDSWB)
extern UrrcMemBlkHeader urrcMemBlkListHead1[URRC_TASK_NUM];
#else/*UPGRADE_DSDSWB*/
extern UrrcMemBlkHeader urrcMemBlkListHead;
#endif/*UPGRADE_DSDSWB*/
#endif


/***************************************************************************
 *  Macros
 **************************************************************************/


/***************************************************************************
 *  Function Prototypes
 **************************************************************************/

#endif /* !URRC_DISABLE_LOCAL_MEMORY_MANAGER */
#endif
#endif
/* END OF FILE */

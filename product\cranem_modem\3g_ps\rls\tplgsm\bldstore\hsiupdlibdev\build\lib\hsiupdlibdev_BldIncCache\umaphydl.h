/*------------------------------------------------------------
(C) Copyright [2006-2010] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/
/**\file umaphydl.h
Contains the declarations for UMAC Physical Downlink entity.*/


#if !defined (UMAPHYDL_H)
#define       UMAPHYDL_H
/***************************************************************************
* Nested Include Files
***************************************************************************/
#include <system.h>
#include <umautl.h>

/******************************************************************************
 * Constants
 *****************************************************************************/
    /** Definition of the number of toggle buffers for DL PDU, used for allowing MAC handling delays.
     * Important Note: This value should be a power of 2. Otherwise, the
     * UMAPHYDL_INCREMENT_TOGGLE_INDEX macro below should be changed, because it uses
     * (UMAPHYDL_TOGGLE_PDU_LIST_SIZE-1) for the modulus function */
#define UMAPHYDL_TOGGLE_PDU_LIST_SIZE (UPS_MAX_OVERLAPPING_PHY_DATA_IND)

/******************************************************************************
 * Type Definitions
 *****************************************************************************/
/** Structure that defines the state of a ccTrCh data downlink */
typedef struct PhyDataIndScenarioStateTag
{
    /** if idle = TRUE, indicates no KI signal created and not storing data from layer 1 */
    Boolean     idle;
    Int8        numTrChWaitingForDataInd;
    Int8        numTrChWaitingForTransfer;

    /** store the current Frame number to clarify the state value for debugging. */
    Int16       currentFrameNm;

    SignalBuffer signal;

#if defined (DATA_IN_SIGNAL)
    UDlPduList                              pduList;
#else /* DATA_IN_SIGNAL */
    UDlPduList                            * pduList_p;
#endif /* DATA_IN_SIGNAL */

    Int16        offsetToNextTB;
#if !defined (PS_L2_R8_API)
    Int8        *nextTBS_p;
#else
	Int16		totalBlocksLengthBits;
#endif

}PhyDataIndScenarioState;

/* Structure that defines the state of a ccTrCh data downlink */
typedef struct TogglePhyDataIndScenarioStateTag
{
    /** Indicates if data should be passed to stack or discarded because release in progress */
    Boolean     active;
    
    PhyDataIndScenarioState     toggleState[UMAPHYDL_TOGGLE_PDU_LIST_SIZE];
    Int8                        currPlwPhyDataIndToggleIndex;
#if !defined (PS_L2_R8_API)
    Int8                        currDataTransferEndToggleIndex;
#endif
}
TogglePhyDataIndScenarioState;

/***************************************************************************
 * Extern Variables
 ***************************************************************************/

/******************************************************************************
 * Macros
 *****************************************************************************/

/******************************************************************************
 * Function Prototypes
 *****************************************************************************/
size_t UmaPhyDlInit (UmacEntity *umac_p);
/*void UmaPhyDlResetPhyDataIndScenarioState(TogglePhyDataIndScenarioState *toggleState_p,
                                     Boolean fullReset);*/
void UmaPhyDlDisableBch(void);
void UmaPhyDlEnableBch(void);
void UmaPhyDlInitPhyDataIndScenarioState(Boolean taskInit);


#if defined(UPGRADE_DSDSWB_L2) && !defined (MATCH_LGDS_L1) //********** 
UINT8 *UmaPhyDlProcessPlwPhyDataInd( plDataIndHeader_ts *plDataIndHeader,Int8 sim_id);
void UmaPhyDlProcessPlwPhyDownlinkDataTransferEnd( dataTransferEnd_ts *dataTransferEnd,Int8 sim_id );
void UmaPhyDlPassOnDelayedPhyDataInd(Int8 sim_id);
#else
UINT8 *UmaPhyDlProcessPlwPhyDataInd( plDataIndHeader_ts *plDataIndHeader);
void UmaPhyDlProcessPlwPhyDownlinkDataTransferEnd( dataTransferEnd_ts *dataTransferEnd );
void UmaPhyDlPassOnDelayedPhyDataInd(void);
#endif
#if defined(ENABLE_PS_L2_TOOL)
void L2ToolUmaPhyDlProcessPlwPhyDataInd(SignalBuffer *sigBuff_p);
#endif


 
#endif	//UMAPHYDL_H

/* END OF FILE */



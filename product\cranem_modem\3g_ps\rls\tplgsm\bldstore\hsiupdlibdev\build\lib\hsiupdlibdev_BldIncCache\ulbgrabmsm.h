/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/3g.mod/lib/src/ulbgrabmsm.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2011/03/27 20:17:00 $
 **************************************************************************
 * File Description:
 *
 * RABM SM functions to process received SM signals and send signals
 * to SM
 **************************************************************************/

#if !(defined ULBGRABMSM_H)
#define       ULBGRABMSM_H

/**** INCLUDE FILES ********************************************************/

#include <system.h>
#include <kernel.h>
#include <xrabmdata.h>


/**** FUNCTION DECLARATIONS ************************************************/
/* Added by zuohuaxu for ********** 20120627, begin */
extern void UlbgRabmSnSmFastDormantInd(XRabmEntity *sn);
extern void UlbgRabmSnSmFastDormantRsp(XRabmEntity *sn,Boolean success);
extern void UlbgRabmSendGmmRabmAbortFastDormantReq(XRabmEntity *sn);
/* Added by zuohuaxu for ********** 20120627, end */
extern void UlbgRabmSnSmActivateInd(XRabmEntity *sn);
extern void UlbgRabmSnSmActivateRsp(XRabmEntity *sn,Nsapi nsapi);
extern void UlbgRabmSnSmDeactivateInd (XRabmEntity *sn);
extern void UlbgRabmSnSmDeactivateRsp(XRabmEntity *sn,Nsapi nsapi,Int16 pdpIndex);
extern void UlbgRabmSnSmDeactivateRspForNullPdp (XRabmEntity *sn, Nsapi nsapi);
extern void UlbgRabmSnSmDeactivateReq(XRabmEntity *sn,Nsapi nsapi);
extern void UlbgRabmSnSmModifyInd(XRabmEntity *sn);
extern void UlbgRabmSnSmModifyRsp(XRabmEntity *sn,Nsapi modifiedNsapi,Boolean success);
extern void UlbgRabmSnSmResumeInd(XRabmEntity *sn);
extern void UlbgRabmSnSmSuspendInd(XRabmEntity *sn);
extern void UlbgRabmSnSmInfoInd(XRabmEntity *sn); 
extern void UlbgRabmSnSmPdpStateInactiveInd(XRabmEntity *sn);
/* Counter functions */
#if 0
extern void UlbgRabmSnSmReportCounterReq (UlbgSndcpEntity *sn);
extern void UlbgRabmSnSmResetCounterReq (UlbgSndcpEntity *sn);
extern void UlbgRabmSnSmCounterInd (UlbgSndcpEntity *sn, Nsapi releasedNsapi);
#endif
/* Added by zuohuaxu for CQ00019274 20120606, begin */
#if defined (UPGRADE_LTE)// && defined (ENABLE_USER_PLANE_IRAT_DATA_SHIFT)          
extern void UlbgRabmSnSmSyncInd(XRabmEntity *sn);
#endif
/* Added by zuohuaxu for CQ00019274 20120606, begin */

#endif


/* END OF FILE */

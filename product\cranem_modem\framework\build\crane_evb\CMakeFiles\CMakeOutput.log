The system is: Windows - 10.0.22631 - AMD64
Checking whether the ASM compiler is ARMCC using "" matched "(ARM Compiler)|(ARM Assembler)":
Product: DS-5 Professional 5.26.2
Component: ARM Compiler 5.06 update 4 (build 422)
Tool: armasm [4d35cf]
For Educational purposes only
Software supplied by: ARM Limited

Usage:      armasm [options] sourcefile

Options:
--list       listingfile   Write a listing file (see manual for options)
 -o          outputfile    Name the final output file
--depend     dependfile    Save 'make' source file dependencies
--errors     errorsfile    Put stderr diagnostics to errorsfile
 -I          dir[,dir]     Add dirs to source file search path
--pd
--predefine  directive     Pre-execute a SET{L,A,S} directive
--maxcache   <n>           Maximum cache size    (default 8MB)
--no_esc                   Ignore C-style (\c) escape sequences
--no_warn                  Turn off Warning messages
 -g                        Output debugging tables
--apcs       /<quals>      Make pre-definitions to match the
                           chosen procedure-call standard
--checkreglist             Warn about out of order LDM/STM register lists
--help                     Print this information
--li                       Little-endian ARM
--bi                       Big-endian ARM
 -M                        Write source file dependency lists to stdout
--MD                       Write source file dependency lists to inputfile.d
--keep                     Keep local labels in symbol table of object file
--regnames none            Do not predefine register names
--split_ldm                Fault long LDM/STM
--unsafe                   Downgrade certain errors to warnings
--via        <file>        Read further arguments from <file>
--cpu        <target-cpu>  Set the target ARM core type
--cpu list                 Output a list of all the selectable CPUs
--fpu        <target-arch> Set target FP architecture version
--fpu list                 Output a list of all selectable FP architectures
--thumb                    Assemble Thumb instructions
--arm                      Assemble ARM instructions
Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
Compiler: C:/Program Files/DS-5 v5.26.2/sw/ARMCompiler5.06u4/bin/armcc.exe 
Build flags: 
Id flags:  

The output was:
0


Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.o"

Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "__image.axf"

The C compiler identification is ARMCC, found in "X:/framework/build/crane_evb/CMakeFiles/3.14.5/CompilerIdC/CMakeCCompilerId.o"

Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler: C:/Program Files/DS-5 v5.26.2/sw/ARMCompiler5.06u4/bin/armcc.exe 
Build flags: 
Id flags:  

The output was:
0


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.o"

Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "__image.axf"

The CXX compiler identification is ARMCC, found in "X:/framework/build/crane_evb/CMakeFiles/3.14.5/CompilerIdCXX/CMakeCXXCompilerId.o"

Determining if the C compiler works passed with the following output:
Change Dir: X:/framework/build/crane_evb/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Project_area/SVN/7_kxs52_spain/prebuilts/misc/windows-x86/ninja.exe cmTC_d6100 
[1/2] Building C object CMakeFiles\cmTC_d6100.dir\testCCompiler.o

[2/2] Linking C executable cmTC_d6100.exe



Detecting C compiler ABI info compiled with the following output:
Change Dir: X:/framework/build/crane_evb/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Project_area/SVN/7_kxs52_spain/prebuilts/misc/windows-x86/ninja.exe cmTC_cb2cb 
[1/2] Building C object CMakeFiles\cmTC_cb2cb.dir\CMakeCCompilerABI.o

[2/2] Linking C executable cmTC_cb2cb.exe



Determining if the CXX compiler works passed with the following output:
Change Dir: X:/framework/build/crane_evb/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Project_area/SVN/7_kxs52_spain/prebuilts/misc/windows-x86/ninja.exe cmTC_695fb 
[1/2] Building CXX object CMakeFiles\cmTC_695fb.dir\testCXXCompiler.o

[2/2] Linking CXX executable cmTC_695fb.exe



Detecting CXX compiler ABI info compiled with the following output:
Change Dir: X:/framework/build/crane_evb/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Project_area/SVN/7_kxs52_spain/prebuilts/misc/windows-x86/ninja.exe cmTC_f21c5 
[1/2] Building CXX object CMakeFiles\cmTC_f21c5.dir\CMakeCXXCompilerABI.o

[2/2] Linking CXX executable cmTC_f21c5.exe



The system is: Windows - 10.0.22631 - AMD64
Checking whether the ASM compiler is ARMCC using "" matched "(ARM Compiler)|(ARM Assembler)":
Product: DS-5 Professional 5.26.2
Component: ARM Compiler 5.06 update 4 (build 422)
Tool: armasm [4d35cf]
For Educational purposes only
Software supplied by: ARM Limited

Usage:      armasm [options] sourcefile

Options:
--list       listingfile   Write a listing file (see manual for options)
 -o          outputfile    Name the final output file
--depend     dependfile    Save 'make' source file dependencies
--errors     errorsfile    Put stderr diagnostics to errorsfile
 -I          dir[,dir]     Add dirs to source file search path
--pd
--predefine  directive     Pre-execute a SET{L,A,S} directive
--maxcache   <n>           Maximum cache size    (default 8MB)
--no_esc                   Ignore C-style (\c) escape sequences
--no_warn                  Turn off Warning messages
 -g                        Output debugging tables
--apcs       /<quals>      Make pre-definitions to match the
                           chosen procedure-call standard
--checkreglist             Warn about out of order LDM/STM register lists
--help                     Print this information
--li                       Little-endian ARM
--bi                       Big-endian ARM
 -M                        Write source file dependency lists to stdout
--MD                       Write source file dependency lists to inputfile.d
--keep                     Keep local labels in symbol table of object file
--regnames none            Do not predefine register names
--split_ldm                Fault long LDM/STM
--unsafe                   Downgrade certain errors to warnings
--via        <file>        Read further arguments from <file>
--cpu        <target-cpu>  Set the target ARM core type
--cpu list                 Output a list of all the selectable CPUs
--fpu        <target-arch> Set target FP architecture version
--fpu list                 Output a list of all selectable FP architectures
--thumb                    Assemble Thumb instructions
--arm                      Assemble ARM instructions
Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
Compiler: C:/Program Files/DS-5 v5.26.2/sw/ARMCompiler5.06u4/bin/armcc.exe 
Build flags: 
Id flags:  

The output was:
0


Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.o"

Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "__image.axf"

The C compiler identification is ARMCC, found in "X:/framework/build/crane_evb/CMakeFiles/3.14.5/CompilerIdC/CMakeCCompilerId.o"

Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler: C:/Program Files/DS-5 v5.26.2/sw/ARMCompiler5.06u4/bin/armcc.exe 
Build flags: 
Id flags:  

The output was:
0


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.o"

Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "__image.axf"

The CXX compiler identification is ARMCC, found in "X:/framework/build/crane_evb/CMakeFiles/3.14.5/CompilerIdCXX/CMakeCXXCompilerId.o"

Determining if the C compiler works passed with the following output:
Change Dir: X:/framework/build/crane_evb/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Project_area/SVN/7_kxs52_spain/prebuilts/misc/windows-x86/ninja.exe cmTC_f66b5 
[1/2] Building C object CMakeFiles\cmTC_f66b5.dir\testCCompiler.o

[2/2] Linking C executable cmTC_f66b5.exe



Detecting C compiler ABI info compiled with the following output:
Change Dir: X:/framework/build/crane_evb/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Project_area/SVN/7_kxs52_spain/prebuilts/misc/windows-x86/ninja.exe cmTC_87e0d 
[1/2] Building C object CMakeFiles\cmTC_87e0d.dir\CMakeCCompilerABI.o

[2/2] Linking C executable cmTC_87e0d.exe



Determining if the CXX compiler works passed with the following output:
Change Dir: X:/framework/build/crane_evb/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Project_area/SVN/7_kxs52_spain/prebuilts/misc/windows-x86/ninja.exe cmTC_25a54 
[1/2] Building CXX object CMakeFiles\cmTC_25a54.dir\testCXXCompiler.o

[2/2] Linking CXX executable cmTC_25a54.exe



Detecting CXX compiler ABI info compiled with the following output:
Change Dir: X:/framework/build/crane_evb/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Project_area/SVN/7_kxs52_spain/prebuilts/misc/windows-x86/ninja.exe cmTC_74832 
[1/2] Building CXX object CMakeFiles\cmTC_74832.dir\CMakeCXXCompilerABI.o

[2/2] Linking CXX executable cmTC_74832.exe



The system is: Windows - 10.0.22631 - AMD64
Checking whether the ASM compiler is ARMCC using "" matched "(ARM Compiler)|(ARM Assembler)":
Product: DS-5 Professional 5.26.2
Component: ARM Compiler 5.06 update 4 (build 422)
Tool: armasm [4d35cf]
For Educational purposes only
Software supplied by: ARM Limited

Usage:      armasm [options] sourcefile

Options:
--list       listingfile   Write a listing file (see manual for options)
 -o          outputfile    Name the final output file
--depend     dependfile    Save 'make' source file dependencies
--errors     errorsfile    Put stderr diagnostics to errorsfile
 -I          dir[,dir]     Add dirs to source file search path
--pd
--predefine  directive     Pre-execute a SET{L,A,S} directive
--maxcache   <n>           Maximum cache size    (default 8MB)
--no_esc                   Ignore C-style (\c) escape sequences
--no_warn                  Turn off Warning messages
 -g                        Output debugging tables
--apcs       /<quals>      Make pre-definitions to match the
                           chosen procedure-call standard
--checkreglist             Warn about out of order LDM/STM register lists
--help                     Print this information
--li                       Little-endian ARM
--bi                       Big-endian ARM
 -M                        Write source file dependency lists to stdout
--MD                       Write source file dependency lists to inputfile.d
--keep                     Keep local labels in symbol table of object file
--regnames none            Do not predefine register names
--split_ldm                Fault long LDM/STM
--unsafe                   Downgrade certain errors to warnings
--via        <file>        Read further arguments from <file>
--cpu        <target-cpu>  Set the target ARM core type
--cpu list                 Output a list of all the selectable CPUs
--fpu        <target-arch> Set target FP architecture version
--fpu list                 Output a list of all selectable FP architectures
--thumb                    Assemble Thumb instructions
--arm                      Assemble ARM instructions
Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
Compiler: C:/Program Files/DS-5 v5.26.2/sw/ARMCompiler5.06u4/bin/armcc.exe 
Build flags: 
Id flags:  

The output was:
0


Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.o"

Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "__image.axf"

The C compiler identification is ARMCC, found in "X:/framework/build/crane_evb/CMakeFiles/3.14.5/CompilerIdC/CMakeCCompilerId.o"

Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler: C:/Program Files/DS-5 v5.26.2/sw/ARMCompiler5.06u4/bin/armcc.exe 
Build flags: 
Id flags:  

The output was:
0


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.o"

Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "__image.axf"

The CXX compiler identification is ARMCC, found in "X:/framework/build/crane_evb/CMakeFiles/3.14.5/CompilerIdCXX/CMakeCXXCompilerId.o"

Determining if the C compiler works passed with the following output:
Change Dir: X:/framework/build/crane_evb/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Project_area/SVN/7_kxs52_spain/prebuilts/misc/windows-x86/ninja.exe cmTC_f81eb 
[1/2] Building C object CMakeFiles\cmTC_f81eb.dir\testCCompiler.o

[2/2] Linking C executable cmTC_f81eb.exe



Detecting C compiler ABI info compiled with the following output:
Change Dir: X:/framework/build/crane_evb/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Project_area/SVN/7_kxs52_spain/prebuilts/misc/windows-x86/ninja.exe cmTC_ca955 
[1/2] Building C object CMakeFiles\cmTC_ca955.dir\CMakeCCompilerABI.o

[2/2] Linking C executable cmTC_ca955.exe



Determining if the CXX compiler works passed with the following output:
Change Dir: X:/framework/build/crane_evb/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Project_area/SVN/7_kxs52_spain/prebuilts/misc/windows-x86/ninja.exe cmTC_50484 
[1/2] Building CXX object CMakeFiles\cmTC_50484.dir\testCXXCompiler.o

[2/2] Linking CXX executable cmTC_50484.exe



Detecting CXX compiler ABI info compiled with the following output:
Change Dir: X:/framework/build/crane_evb/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Project_area/SVN/7_kxs52_spain/prebuilts/misc/windows-x86/ninja.exe cmTC_51ad5 
[1/2] Building CXX object CMakeFiles\cmTC_51ad5.dir\CMakeCXXCompilerABI.o

[2/2] Linking CXX executable cmTC_51ad5.exe



The system is: Windows - 10.0.22631 - AMD64
Checking whether the ASM compiler is ARMCC using "" matched "(ARM Compiler)|(ARM Assembler)":
Product: DS-5 Professional 5.26.2
Component: ARM Compiler 5.06 update 4 (build 422)
Tool: armasm [4d35cf]
For Educational purposes only
Software supplied by: ARM Limited

Usage:      armasm [options] sourcefile

Options:
--list       listingfile   Write a listing file (see manual for options)
 -o          outputfile    Name the final output file
--depend     dependfile    Save 'make' source file dependencies
--errors     errorsfile    Put stderr diagnostics to errorsfile
 -I          dir[,dir]     Add dirs to source file search path
--pd
--predefine  directive     Pre-execute a SET{L,A,S} directive
--maxcache   <n>           Maximum cache size    (default 8MB)
--no_esc                   Ignore C-style (\c) escape sequences
--no_warn                  Turn off Warning messages
 -g                        Output debugging tables
--apcs       /<quals>      Make pre-definitions to match the
                           chosen procedure-call standard
--checkreglist             Warn about out of order LDM/STM register lists
--help                     Print this information
--li                       Little-endian ARM
--bi                       Big-endian ARM
 -M                        Write source file dependency lists to stdout
--MD                       Write source file dependency lists to inputfile.d
--keep                     Keep local labels in symbol table of object file
--regnames none            Do not predefine register names
--split_ldm                Fault long LDM/STM
--unsafe                   Downgrade certain errors to warnings
--via        <file>        Read further arguments from <file>
--cpu        <target-cpu>  Set the target ARM core type
--cpu list                 Output a list of all the selectable CPUs
--fpu        <target-arch> Set target FP architecture version
--fpu list                 Output a list of all selectable FP architectures
--thumb                    Assemble Thumb instructions
--arm                      Assemble ARM instructions
Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
Compiler: C:/Program Files/DS-5 v5.26.2/sw/ARMCompiler5.06u4/bin/armcc.exe 
Build flags: 
Id flags:  

The output was:
0


Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.o"

Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "__image.axf"

The C compiler identification is ARMCC, found in "X:/framework/build/crane_evb/CMakeFiles/3.14.5/CompilerIdC/CMakeCCompilerId.o"

Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler: C:/Program Files/DS-5 v5.26.2/sw/ARMCompiler5.06u4/bin/armcc.exe 
Build flags: 
Id flags:  

The output was:
0


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.o"

Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "__image.axf"

The CXX compiler identification is ARMCC, found in "X:/framework/build/crane_evb/CMakeFiles/3.14.5/CompilerIdCXX/CMakeCXXCompilerId.o"

Determining if the C compiler works passed with the following output:
Change Dir: X:/framework/build/crane_evb/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Project_area/SVN/7_kxs52_spain/prebuilts/misc/windows-x86/ninja.exe cmTC_31ac9 
[1/2] Building C object CMakeFiles\cmTC_31ac9.dir\testCCompiler.o

[2/2] Linking C executable cmTC_31ac9.exe



Detecting C compiler ABI info compiled with the following output:
Change Dir: X:/framework/build/crane_evb/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Project_area/SVN/7_kxs52_spain/prebuilts/misc/windows-x86/ninja.exe cmTC_40125 
[1/2] Building C object CMakeFiles\cmTC_40125.dir\CMakeCCompilerABI.o

[2/2] Linking C executable cmTC_40125.exe



Determining if the CXX compiler works passed with the following output:
Change Dir: X:/framework/build/crane_evb/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Project_area/SVN/7_kxs52_spain/prebuilts/misc/windows-x86/ninja.exe cmTC_ced09 
[1/2] Building CXX object CMakeFiles\cmTC_ced09.dir\testCXXCompiler.o

[2/2] Linking CXX executable cmTC_ced09.exe



Detecting CXX compiler ABI info compiled with the following output:
Change Dir: X:/framework/build/crane_evb/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Project_area/SVN/7_kxs52_spain/prebuilts/misc/windows-x86/ninja.exe cmTC_d51e1 
[1/2] Building CXX object CMakeFiles\cmTC_d51e1.dir\CMakeCXXCompilerABI.o

[2/2] Linking CXX executable cmTC_d51e1.exe



The system is: Windows - 10.0.22631 - AMD64
Checking whether the ASM compiler is ARMCC using "" matched "(ARM Compiler)|(ARM Assembler)":
Product: DS-5 Professional 5.26.2
Component: ARM Compiler 5.06 update 4 (build 422)
Tool: armasm [4d35cf]
For Educational purposes only
Software supplied by: ARM Limited

Usage:      armasm [options] sourcefile

Options:
--list       listingfile   Write a listing file (see manual for options)
 -o          outputfile    Name the final output file
--depend     dependfile    Save 'make' source file dependencies
--errors     errorsfile    Put stderr diagnostics to errorsfile
 -I          dir[,dir]     Add dirs to source file search path
--pd
--predefine  directive     Pre-execute a SET{L,A,S} directive
--maxcache   <n>           Maximum cache size    (default 8MB)
--no_esc                   Ignore C-style (\c) escape sequences
--no_warn                  Turn off Warning messages
 -g                        Output debugging tables
--apcs       /<quals>      Make pre-definitions to match the
                           chosen procedure-call standard
--checkreglist             Warn about out of order LDM/STM register lists
--help                     Print this information
--li                       Little-endian ARM
--bi                       Big-endian ARM
 -M                        Write source file dependency lists to stdout
--MD                       Write source file dependency lists to inputfile.d
--keep                     Keep local labels in symbol table of object file
--regnames none            Do not predefine register names
--split_ldm                Fault long LDM/STM
--unsafe                   Downgrade certain errors to warnings
--via        <file>        Read further arguments from <file>
--cpu        <target-cpu>  Set the target ARM core type
--cpu list                 Output a list of all the selectable CPUs
--fpu        <target-arch> Set target FP architecture version
--fpu list                 Output a list of all selectable FP architectures
--thumb                    Assemble Thumb instructions
--arm                      Assemble ARM instructions
Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
Compiler: C:/Program Files/DS-5 v5.26.2/sw/ARMCompiler5.06u4/bin/armcc.exe 
Build flags: 
Id flags:  

The output was:
0


Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.o"

Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "__image.axf"

The C compiler identification is ARMCC, found in "X:/framework/build/crane_evb/CMakeFiles/3.14.5/CompilerIdC/CMakeCCompilerId.o"

Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler: C:/Program Files/DS-5 v5.26.2/sw/ARMCompiler5.06u4/bin/armcc.exe 
Build flags: 
Id flags:  

The output was:
0


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.o"

Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "__image.axf"

The CXX compiler identification is ARMCC, found in "X:/framework/build/crane_evb/CMakeFiles/3.14.5/CompilerIdCXX/CMakeCXXCompilerId.o"

Determining if the C compiler works passed with the following output:
Change Dir: X:/framework/build/crane_evb/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Project_area/SVN/7_kxs52_spain/prebuilts/misc/windows-x86/ninja.exe cmTC_07224 
[1/2] Building C object CMakeFiles\cmTC_07224.dir\testCCompiler.o

[2/2] Linking C executable cmTC_07224.exe



Detecting C compiler ABI info compiled with the following output:
Change Dir: X:/framework/build/crane_evb/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Project_area/SVN/7_kxs52_spain/prebuilts/misc/windows-x86/ninja.exe cmTC_8599e 
[1/2] Building C object CMakeFiles\cmTC_8599e.dir\CMakeCCompilerABI.o

[2/2] Linking C executable cmTC_8599e.exe



Determining if the CXX compiler works passed with the following output:
Change Dir: X:/framework/build/crane_evb/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Project_area/SVN/7_kxs52_spain/prebuilts/misc/windows-x86/ninja.exe cmTC_5d285 
[1/2] Building CXX object CMakeFiles\cmTC_5d285.dir\testCXXCompiler.o

[2/2] Linking CXX executable cmTC_5d285.exe



Detecting CXX compiler ABI info compiled with the following output:
Change Dir: X:/framework/build/crane_evb/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Project_area/SVN/7_kxs52_spain/prebuilts/misc/windows-x86/ninja.exe cmTC_cffd0 
[1/2] Building CXX object CMakeFiles\cmTC_cffd0.dir\CMakeCXXCompilerABI.o

[2/2] Linking CXX executable cmTC_cffd0.exe




/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urlumrx.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2007/03/19 13:59:30 $
 **************************************************************************
 * File Description:
 *
 *      Header file for UrlUmRx.c.
 *      Contains function call declarations, constants and types for use
 *      by other URLC modules.
 **************************************************************************/

#if !defined (URLUMRX_H)
#define       URLUMRX_H

/***************************************************************************
* Nested Include Files
***************************************************************************/
#include <urlumtyp.h>

/***************************************************************************
 * Typed Constants
 ***************************************************************************/
/* In some GCF tests (e.g. GCF ********.2) the downlink SDU in loopback mode
 * can be defined to be larger than UPS_MAX_RLC_SDU_DATA_OCTETS. The following
 * size was adjusted according to the GCFs value we encountered and it relevant
 * only for downlink loopback UM SDUs.
 */
/*fix CQ23758 taoye modify begin 20121029 */
#define URL_UMRX_LOOPBACK_SDU_BYTES         6000//5000//4000
/*fix CQ23758 taoye modify end 20121029 */


/***************************************************************************
*   RX Macro Functions
***************************************************************************/

/* Macro to determine if a bearer is configured for loopback mode, either
 * active or pending
 */
#define URL_UMRX_LOOPBACK(entity_p)         ((entity_p)->loopback)

/* Macro to determine if the DL PDU list of a bearer needs parsing */
#define URL_UMRX_PARSE_PDU_LIST(entity_p)   ((entity_p)->parsePduList)

/* Macro to access the COUNT-C info */
#define URL_UMRX_DL_COUNTC_INFO(entity_p)   ((entity_p)->dlCountcInfo)


/***************************************************************************
 * Function Prototypes
 ***************************************************************************/

extern  void
UrlUmRxHandler
    (UrlUmRxEntity                                    * entityData_p,
     SignalBuffer                                     * rxSignal_p
    );
extern  void
UrlUmRxGetPduInfo
    (UrlUmRxEntity                                    * entityData_p,
     UmacDlPduListInfo                                * pduListInfo_p,
     Int8                                               pduNum,
     dtcF8DescriptorInfo                              * cipherInfo_p,
     UFrameNumber                                     * frameNumber,
     cipherType_te									  * cipherAlgorithm_p
    );
extern  UrlcDebugDlPduStatus
UrlUmRxUmacDataInd
    (UrlUmRxEntity                                    * entityData_p,
     UmacDlPduListInfo                                * pduListInfo_p,
     Int32                                               pduNum,
     dtcF8DescriptorInfo                              * cipherInfo_p,
     UrlSequenceNumber                                  sn,
     KeySeqId                                           ksi,
     UFrameNumber                                       frameNumber,
     UUARFCN                                            arfcn,
     UPrimaryScramblingCode                             scramblingCode
    );
extern  void
UrlUmRxCrlcCloseTestLoopReq
    (UrlUmRxEntity                                    * entityData_p,
     Int16                                              rlcSduBits
    );
extern  void
UrlUmRxLoopbackUrlcUmDataInd
    (UrlUmRxEntity                                    * entityData_p,
     SignalBuffer                                     * loopbackSignal_p
    );
extern  void
UrlUmRxAboveHighWaterMark
    (UrlUmRxEntity                                    * entityData_p
    );
extern  void
UrlUmRxDiagnosticsReq
    (UrlUmRxEntity                                    * entityData_p
    );

#if defined(UMTS7_PRE_R8)&& !defined(ENABLE_URLC_UNIT_TEST)

extern Boolean DlbgProcessUrlcUmDataInd(UrlcPsUmDataInd *urlcPsUmDataInd);
#endif
#endif /* URLUMRX_H */

/* END OF UrlUmRx.h */

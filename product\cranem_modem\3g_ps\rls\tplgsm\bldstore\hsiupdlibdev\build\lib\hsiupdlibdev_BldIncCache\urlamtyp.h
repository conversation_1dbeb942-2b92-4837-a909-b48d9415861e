/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/api/inc/urlamtyp.h#3 $
 *   $Revision: #3 $
 *   $DateTime: 2007/03/30 13:53:25 $
 **************************************************************************
 *  File Description :
 *
 * Types and constants required for the URLC AM module
 *
 **************************************************************************/

#if !defined (URLAMTYP_H)
#define       URLAMTYP_H

/***************************************************************************
 * Nested Include Files
 **************************************************************************/

#include <system.h>
#include <ki_typ.h>
#include <urlalloc.h>
#include <urltypes.h>

#if defined (ENABLE_URLC_UNIT_TEST)
#include <crlc_sig.h>
#endif /* ENABLE_URLC_UNIT_TEST */

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/

/* Uplink control PDU statusFlags, used in conjunction with UmacTxStatusInd */
#define URL_AM_STATUS_NEW            0x01   /* Indicates the start of a report */
#define URL_AM_STATUS_WINDOW         0x02   /* Indicates if a status PDU contains
                                             * a WINDOW SUFI
                                             */
#define URL_AM_STATUS_ACK            0x04   /* Indicates if a status PDU contains
                                             * ACK SUFI, and is used to start the
                                             * status prohibit timer when the
                                             * last segment of an ack status
                                             * report is transmitted and indicates
                                             * that no more SUFIs can be appended
                                             * to this status PDU
                                             */
#define URL_AM_STATUS_NACK           0x08   /* Indicates if a status PDU contains
                                             * a LIST,BITMAP or RLIST and is used
                                             * to start the status prohibit timer
                                             * when the last segment of an ack
                                             * status report is transmitted
                                             */
#define URL_AM_STATUS_MRW            0x10   /* Indicates if a status PDU contains
                                             * a MRW SUFI
                                             */
#define URL_AM_STATUS_MRW_ACK        0x20   /* Indicates if a status PDU contains
                                             * a MRW_ACK SUFI
                                             */
/*********** merged for FRBD 20140806*/
#define URL_AM_STATUS_POLL           0x40   /* Indicates if a status PDU contains
                                             * a POLL SUFI
                                             */
#define URL_AM_STATUS_END            0x80   /* Indicates the end of a report */

/* To improve performance, by reducing the number of alloc's/free's for UL/DL
 * data PDUs, a fixed number of segments will be statically allocated as part
 * of the PDU structure; all extra segments will by allocated when required.
 * This value MUST be > 0.
 */
#define URL_AM_STATIC_DATA_SEGMENTS     1

/* Out Of Sequence (OOS) Confiuration and markers */
#define URL_AM_OOS_NUM_LISTS            16
#define URL_AM_OOS_NUM_PDUS_PER_ENTRY   8

#define URL_AM_OOS_NUM_PDUS_PER_ENTRY_NORM  (URL_AM_OOS_NUM_PDUS_PER_ENTRY-1)
#if (URL_AM_OOS_NUM_LISTS == 2)
# define URL_AM_OOS_LIST_DOWNSHIFT      11
#elif (URL_AM_OOS_NUM_LISTS == 4)
# define URL_AM_OOS_LIST_DOWNSHIFT      10
#elif (URL_AM_OOS_NUM_LISTS == 8)
# define URL_AM_OOS_LIST_DOWNSHIFT      9
#elif (URL_AM_OOS_NUM_LISTS == 16)
# define URL_AM_OOS_LIST_DOWNSHIFT      8
#elif (URL_AM_OOS_NUM_LISTS == 32)
# define URL_AM_OOS_LIST_DOWNSHIFT      7
#elif (URL_AM_OOS_NUM_LISTS == 64)
# define URL_AM_OOS_LIST_DOWNSHIFT      6
#elif (URL_AM_OOS_NUM_LISTS == 128)
# define URL_AM_OOS_LIST_DOWNSHIFT      5
#elif (URL_AM_OOS_NUM_LISTS == 256)
# define URL_AM_OOS_LIST_DOWNSHIFT      4
#else
# error URL_AM_OOS_NUM_LISTS must be one of above options
#endif /* URL_AM_OOS_NUM_LISTS == ? */

#if (URL_AM_OOS_NUM_PDUS_PER_ENTRY != 8)
# if (URL_AM_OOS_NUM_PDUS_PER_ENTRY != 16)
#  if (URL_AM_OOS_NUM_PDUS_PER_ENTRY != 24)
#   if (URL_AM_OOS_NUM_PDUS_PER_ENTRY != 32)
#error URL_AM_OOS_NUM_PDUS_PER_ENTRY must be 8, 16, 24 or 32
#   endif
#  endif
# endif
#endif /* URL_AM_OOS_NUM_PDUS_PER_ENTRY != 8, 16, 24 or 32 */

#if defined (ENABLE_NEW_URLC_OPTIMISATIONS)
/* Defines the number of Int32s required to hold a bitmap of the
 * maximum PDUs possible. */
#define URL_AM_PDU_BITMAP_LENGTH (URL_AM_SN_MODULUS / BITS_PER_INT32)

/* Memory is allocated in chunks to store a block of PDUs. This defines the
 * number of PDUs that are contained within a block */
#define URL_AM_NUM_PDUS_PER_TABLE_BLOCK 128

/* This is the number of blocks required to hold the maximum number of
 * PDUs that is possible */
#define URL_AM_NUM_PDU_TABLE_BLOCKS (URL_AM_SN_MODULUS / URL_AM_NUM_PDUS_PER_TABLE_BLOCK)
#endif /* ENABLE_NEW_URLC_OPTIMISATIONS */

/* Macro to define the of octets needed to hold a packed array of flags, one for
 * each AM sequence number
 */
#define URL_AM_SN_FLAG_OCTETS               BITS_TO_INT8S (URL_AM_SN_MODULUS)


/***************************************************************************
 * Types
 ***************************************************************************/

//ICAT EXPORTED ENUM
typedef enum UrlAmStateTag
{
    URLAM_STATE_NULL                      = 0,
    URLAM_STATE_READY                     = 1,
    URLAM_STATE_SUSPEND                   = 2,
    URLAM_STATE_RESET_PENDING             = 3,
    URLAM_STATE_RESET_AND_SUSPEND         = 4,
    URLAM_STATE_UNRECOVERABLE             = 5,
    URLAM_STATE_RESET_WAIT_TX             = 6,
    URLAM_STATE_RESET_WAIT_TX_AND_SUSPEND = 7
} UrlAmState;

//ICAT EXPORTED ENUM
typedef enum UrlAmTxStateTag
{
    URLAM_TX_ENABLED                    = 0,
    URLAM_TX_STOPPED                    = 1,
    URLAM_TX_HALTED                     = 2
} UrlAmTxState;

typedef enum UrlAmResetTypeTag
{
    URL_AM_RESET_ESTABLISH              = 0,
    URL_AM_RESET_RE_ESTABLISH           = 1,
    URL_AM_RESET_ERROR                  = 2,
    URL_AM_RESET_RELEASE                = 3,
    URL_AM_TEST_MODE_SN                 = 4,
    URL_AM_RESET_UL_ONLY_RE_ESTABLISH   = 5, /*********** merged for FRBD 20140806*/
    //start:modify for CQ 000 on 2012.12.4
	URL_AM_RESET_RRC_RESELECTION		= 6
	//end:modify for CQ 000 on 2012.12.4
} UrlAmResetType;

/* ==========================================================================
 * Bearer configuration data
 * ========================================================================== */
typedef struct UrlAmUlConfigTag
{
    UTransmissionRLC_Discard            discard;
    UTransmissionWindowSize             windowSize;
    UTimerRST                           timerRST;
    UMaxRST                             maxRST;
    Boolean                             pollingInfoPresent;
    UPollingInfo                        pollingInfo;
    Boolean                             twoLogicalChannels;
    Int16                               dataPduOctets;
    Int16                               minControlPduOctets;
	Boolean                             useHeSpecialValue;
    Boolean                             losslessSrnsRelocation; /* Send MRW */
	/*********** merged for FRBD 20140806 start*/
    Int8                                liOctets;
#if defined(UPGRADE_3G_UL_EL2)
    Boolean                             flexiblePduSize;
    /**< Minimum and maximum RLC PDU size (16 - 12040 by step of 8).
     * Relevant only when flexiblePduSize is TRUE */
    Int16                               minimumPduBits;
    Int16                               maximumPduBits;
#endif
/*********** merged for FRBD 20140806 end*/
}
UrlAmUlConfig;

typedef struct UrlAmDlConfigTag
{
    Boolean                             inSequenceDelivery;
    UReceivingWindowSize                windowSize;
    UDL_RLC_StatusInfo                  statusInfo;
    Int16                               dataPduOctets;
    Int8                                sduStartOctetOffset;
    Boolean                             flexiblePduSize;
    Boolean                             useHeSpecialValue;
    Int8                                liOctets;
}
UrlAmDlConfig;

typedef struct UrlAmConfigTag
{
    UrlAmUlConfig                       ul;
    UrlAmDlConfig                       dl;
} UrlAmConfig;

/* ==========================================================================
 * Uplink SDU data types.
 *
 * Note that an UL SDU list entry can only be deleted when it is not referenced
 * by any UL PDUs.
 * ========================================================================== */
#if defined (ENABLE_PS_L2_TOOL)
// On PC, this enum's size should be Int8
enum
#else
typedef enum UrlAmTxSduStatusTag
#endif
{
    URL_AM_TX_SDU_UNTRANSMITTED         = 0,
    URL_AM_TX_SDU_UNACKNOWLEDGED        = 1,
    URL_AM_TX_SDU_ACKNOWLEDGED          = 2,
    URL_AM_TX_SDU_TIMER_DISCARD         = 3,
    URL_AM_TX_SDU_MAX_ATTEMPTS          = 4,
    URL_AM_TX_SDU_RESET                 = 5,
    URL_AM_TX_SDU_RE_ESTABLISH          = 6,
    URL_AM_TX_SDU_RELEASE               = 7
}
#if defined (ENABLE_PS_L2_TOOL)
; typedef Int8 UrlAmTxSduStatus;
#else
UrlAmTxSduStatus;
#endif

typedef struct UrlAmTxSduTag
{
    UrlcMessageUnitIdentifier           mui;
    Boolean                             confirmationRequired;

    /* An UL SDU can only be deleted when not referenced by any UL data PDU, as
     * its data may be needed to when assembling a PDU for transmission, hence
     * the need to record:
     *  - The number of PDU referencing this SDU
     *  - The status allows UrlcUlSduDiscardInd or UrlcAmDataCnf to be sent when
     *    no longer referenced.
     */
    UrlAmTxSduStatus                    status;
    Int8                                numRefs;

    /* As SDUs are deleted as a low priority background operation, its necessary
     * to record its status so that either a UrlcAmDatCnf or UrlcUlSduDiscardInd
     * can be sent up the stack. To facilitate this, it is necessary to remember
     * how many referencing PDUs were discarded (due to TIMER, MAX_ATTEMPTS or
     * RESET) to ensure that when younger PDUs are acknowledged, the whole SDU
     * doesn't get seen as ACK'd :
     *      SDU        PDU
     *     -----      -----
     *     |   |      |   |  Discarded
     *     |   |      -----
     *     |   |      |   |  Acknowledged
     *     -----      -----
     */
    Int8                                numDiscardedRefs;

    /* When timer based SDU discard is enabled, discardTick indicates the kernel
     * tick when the SDU is due to expire, and is only set when all of its data
     * has been segmented/concatenated
     */
    KernelTicks                         discardTick;
#if defined (ENABLE_UPLANE_STATISTICS)
    /* To be able to get some throughput statistics receivedTick indicates the
     * kernel tick when the SDU was received from the upper layer and sentTick 
     * indicates the kernel tick when the last segment of the SDU was sent to
     * the lower layer the first time.
     */
    KernelTicks                         receivedTick;
    KernelTicks                         sentTick;
#endif /* ENABLE_UPLANE_STATISTICS */
    /* The following parameters define the location/type of SDU data.
     * When a new SDU record is created, sduBits indicates the original SDU size
     * and bitOffset/bitLength indicate the location of the usable data within
     * the data block.
     * Once any part of the SDU has been parsed (represented in TX data PDUs),
     * bitOffset and bitlength are adjusted until all data is represented in UL
     * data PDUs
     */
    UrlcDataUsage                       dataUsage;
    Int8                              * data_p;

    /* Initial offset/length (used for re-segmenting after peer-peer reset) */
    Int16                               sduOffset;
    Int16                               sduLength;

    /* Current offset/length */
    Int16                               bitOffset;
    Int16                               bitLength;

    /* First and last  PDU SN */
    Int16                               firstSn;
    Int16                               lastSn;
/*Fixed CQ00024251 xyzheng for U+D optimistion start 2012-11-07*/
	Int32								dwTcpAckFlag;
/*Fixed CQ00024251 xyzheng for U+D optimistion end 2012-11-07*/
    Boolean                             discardable;                           //-+ CQ00238599 11-Oct-2012 +-
} UrlAmTxSdu;

/* Declare typedef for doubly linked list carrying UrlAmTxSdu */
UT_DOUBLE_LINK_LIST_DECLARE (UrlAmTxSduList, UrlAmTxSdu)

/* ==========================================================================
 * Uplink (tx) CONTROL PDU types
 * ========================================================================== */
typedef enum UrlAmCtrlPduTypeTag
{
    URL_AM_STATUS_PDU                   = 0,
    URL_AM_RESET_PDU                    = 1,
    URL_AM_RESET_ACK_PDU                = 2
} UrlAmCtrlPduType;

typedef struct UrlAmTxCtrlPduTag
{
    UrlAmCtrlPduType                    type;       /* Type of control PDU */
    Int8                                flags;      /* IF type is STATUS_PDU:
                                                     *  URL_AM_STATUS_NEW
                                                     *  URL_AM_STATUS_END
                                                     *  URL_AM_STATUS_ACK_OR_NACK
                                                     *  URL_AM_STATUS_MRW
                                                     */
    Int8                                txCount;    /* TX counter, used for :
                                                     *   MRW and RESET
                                                     */
    UrlAmTxCause                        cause;      /* Reason for creation */
    Int16                               bitLength;

    /* To enhance performance, PDU data is allocated as an integral part of the
     * PDU structure to avoid the need for an extra alloc/free per PDU, however
     * as the size of this is configurable, the array is only defined large
     * enough to hold the header (1 byte), and extra bytes will allocated to
     * contain the remainder of the PDU data, so that the data array will
     * actually be the size of config.ul.minControlPduOctets
     */
    Int8                                pduData [URL_PDU_MINUMUM_USABLE_OCTETS];
} UrlAmTxCtrlPdu;

/* Declare typedef for doubly linked list carrying UrlAmTxCtrlPdu */
UT_DOUBLE_LINK_LIST_DECLARE (UrlAmTxCtrlList, UrlAmTxCtrlPdu)


typedef struct MappingRbTxReqIdTag
{
    Boolean                     IsTrafficIndConsumed;
    Boolean                     pollTransmitted;
    UrlAmTxCause                pollTriggerCause;
    Boolean                     statusProhibitTriggered;
    Boolean/*mrwState*/         MRW_Transmitted;
    Boolean                     resetTransmitted;
    Boolean                     windowTransmitted;
} MappingRbTxReqId;


/* ==========================================================================
 * Uplink (tx) DATA PDU types
 * ========================================================================== */
typedef enum UrlAmTxDataPduStatusTag
{
    URL_AM_TX_PDU_UNTRANSMITTED         = 0,        /* Never transmitted */
    URL_AM_TX_PDU_TRANSMITTED           = 1,        /* Waiting for ACK or NACK */
    URL_AM_TX_PDU_ACKED                 = 2,        /* ACK  received */
    URL_AM_TX_PDU_NACKED                = 3,        /* NACK received */
    URL_AM_TX_PDU_RETRANSMIT            = 4,        /* Awaiting retransmission */
    URL_AM_TX_PDU_TIMER_DISCARD         = 5,        /* SDU timer expiry */
    URL_AM_TX_PDU_MAX_ATTEMPTS          = 6         /* Maximum transmissions */
#if defined (ENABLE_NEW_URLC_OPTIMISATIONS)
   ,URL_AM_TX_PDU_FREE                  = 0xFF
#endif
} UrlAmTxDataPduStatus;

typedef union UrlAmTxDataSegmentRefTag
{
    UT_DOUBLE_LINK_LIST_NODE_TYPE(UrlAmTxSduList)  *sdu_p;
    UT_DOUBLE_LINK_LIST_NODE_TYPE(UrlAmTxCtrlList) *ctrl_p;
} UrlAmTxDataSegmentRef;

typedef struct UrlAmTxDataPduSegmentTag
{
    Int8                                segNum;     /* Segment number */
    UrlDataSegmentType                  type;       /* Segment type */
    Int16                               bitOffset;  /* SDU data bit offset */
    Int16                               bitLength;  /* SDU data bit length */

    /* refNode, when not PNULL, points to a node in one of two different
     * linked lists, defined by the segment type:
     *
     * If type has URL_SEG_LI_PIGGYBACK_STATUS present
     *      refNode.ctrl_p points to an entry in ulCtrlList
     * Else
     *      refNode.sdu_p  points to an entry in ulSduList
     */
    UrlAmTxDataSegmentRef               refNode;

    struct UrlAmTxDataPduSegmentTag   * next_p;  /* Pointer to next segment */
} UrlAmTxDataPduSegment;

typedef struct UrlAmTxDataPduTag
{
    UCOUNT_C                            countC;     /* HFN and SN */
    KeySeqId                            ksi;        /* Cipher key identity :
                                                     *  Unciphered: UPS_INVALID_KSI
                                                     *  Ciphered  : 0-6 or 8-14
                                                     */
    cipherType_te						cipherAlgorithm; /* DTC cipher algorithm*/
    Int16                               padBits;    /* Padding length (bits) */
    UrlDataSegmentType                  type;       /* Type of final segment */
    UrlAmTxDataPduStatus                status;     /* TX status */
#if !defined (ENABLE_NEW_URLC_OPTIMISATIONS)
    UrlAmTxDataPduStatus                ackStatus;  /* Temp ACK status (used
                                                     * when parsing DL status
                                                     */
    void                              * rtxNode_p;  /* Pointer to relevant PDU in
                                                     * retransmission PDUs list
                                                     */
#endif /* !ENABLE_NEW_URLC_OPTIMISATIONS */
    UrlAmTxCause                        txCause;    /* Transmit cause */
    Int8                                vtDAT;      /* The number of times the
                                                     * PDU had been transmitted,
                                                     * used with MaxDAT (1-40)
                                                     */
    Int8                                numLIs;     /* Number of LI's */
    Boolean                             poll;       /* Poll flag */

    Int16                               pduBitLen;   /* PDU bit length */ /*********** merged for FRBD 20140806*/

    /* To improve performance, by reducing the number of alloc's/free's for UL/DL
     * data PDUs, a fixed number of segments will be statically allocated as part
     * of the PDU structure; all extra segments will by allocated when required.
     */
    UrlAmTxDataPduSegment               segment [URL_AM_STATIC_DATA_SEGMENTS];
} UrlAmTxDataPdu;

#if !defined (ENABLE_NEW_URLC_OPTIMISATIONS)
/* Declare typedef for doubly linked list carrying UrlAmTxDataPdu */
UT_DOUBLE_LINK_LIST_DECLARE (UrlAmTxDataList, UrlAmTxDataPdu)
#endif


#if !defined (ENABLE_NEW_URLC_OPTIMISATIONS)
/* ==========================================================================
 * Uplink (tx) PDUs for retransmission
 * ========================================================================== */
typedef struct UrlAmRtxDataPduTag
{
    UCOUNT_C                                            countC; /* HFN and SN */
    UT_DOUBLE_LINK_LIST_NODE_TYPE(UrlAmTxDataList)    * dataNode_p;
} UrlAmRtxDataPdu;

/* Declare typedef for doubly linked list carrying UrlAmRtxDataPdu */
UT_DOUBLE_LINK_LIST_DECLARE (UrlAmRtxDataList, UrlAmRtxDataPdu)
#endif

/* ==========================================================================
 * Downlink (rx) SDU types
 * ========================================================================== */
typedef struct UrlAmRxLiPduInfoTag
{
    Boolean                                             liPresent;
    UrlSequenceNumber                                   sn;
    Int8                                              * pdu_p;
} UrlAmRxLiPduInfo;

//CQ00049795: Merge "Modify CQ00049523 start 2013.12.04" on 20131206
#if 0
#if defined(UMTS7_PRE_R8)

typedef struct UrlAmOosPduTag
{
	Int16												bitOffset;
	Int16												bitLength;
	Int8												*data_p;
	Int32												preSduBitoffset;
	Int8												end_flag;
	Int16												sn;
}UrlAmOosPdu;
#endif
#endif
//CQ00049795: Merge "Modify CQ00049523 end 2013.12.04" on 20131206

typedef struct UrlAmRxSduTag
{
    /*               |<----Current SDU----->|
     * ---------------------------------------------------------/ .. /------
     * |   |   |   |   |   |        ...   |   |           |   | / .. / |   |
     * ---------------------------------------------------------/ .. /------
     *   |           |                      |               |            |
     * snBase      start.sn               end.sn          snHigh       snMax
     *             start.dOctetOffset     end.numLIs
     *                                    end.liNum
     *                                    end.totalDataOctets
     *                                    end.dOctetOffset
     *
     *
     */
    UrlSequenceNumber                   snBase;       /* Sequence number of PDU at
                                                       * base of block
                                                       */
    UrlSequenceNumber                   snHigh;       /* Highest available sequence
                                                       * number in this SDU
                                                       */
    UrlSequenceNumber                   snHighPreCopy;/* Highest SDU scheduled
                                                       * for copying into this SDU
                                                       */
    UrlSequenceNumber                   snMax;        /* Maximum sequence number
                                                       * that could fit into SDU
                                                       */
    Int16                               nextPduBitOffset; /* Offset from SDU beggining
                                                           * for next PDU
                                                           */
    Boolean                             isLastSduOctet;   /* Indicate if this SDU terminated
                                                           * using last SDU octets HE
                                                           */
    UrlAmRxLiPduInfo                    liPduInfo;  /* LI PDU information */

    struct
    {
        UrlSequenceNumber   sn;                     /* Sequence number of first
                                                     * PDU in current SDU
                                                     */
        Int16               dOctetOffset;           /* Octet offset to start of
                                                     * data within first PDU
                                                     */
    }                                   start;
    struct
    {
        UrlSequenceNumber   sn;                     /* Sequence number of last
                                                     * PDU in current SDU or
                                                     * URL_SN_UNDEFINED
                                                     */
        Int8                liNum;                  /* Number of LIs processed
                                                     * in last PDU of currenr
                                                     * SDU
                                                     */
        Int8                numLIs;                 /* Number of LIs in last PDU
                                                     * of current SDU
                                                     */
        Int16               dataOctets;             /* Total number of octets of
                                                     * data (excluding LIs and
                                                     * padding) in last PDU of
                                                     * current SDU
                                                     */
        Int16               dOctetOffset;           /* Octet offset to start of
                                                     * data within last PDU
                                                     */
    }                                   end;
    Boolean                             isInvalid;
    Boolean                             terminated; /* TRUE  : PDU data ends in
                                                     *         this PDU
                                                     * FALSE : Last data segment
                                                     *         is unterminated
                                                     *         and continues in
                                                     *         next PDU(s)
                                                     */
    Int16                          numPdusThisTti;  /* Indicates the number of
                                                     * PDUs created/added to
                                                     * this SDU in the current
                                                     * TTI being processed.
                                                     */
    Int16                               sduBlockOctets;
    Int8                               *pduData_p;
    //CQ00049795: Merge "Modify CQ00049523 start 2013.12.04" on20131206
#if 0
#if defined(UMTS7_PRE_R8)

	Int8                               numOos;
	UrlAmOosPdu						   oos_rlcPsSdu[16];
#endif
#endif
//CQ00049795: Merge "Modify CQ00049523 end 2013.12.04" on20131206
} UrlAmRxSdu;

/* Declare typedef for doubly linked list carrying UrlAmRxSdu */
UT_DOUBLE_LINK_LIST_DECLARE (UrlAmRxSduList, UrlAmRxSdu)

/* ==========================================================================
 * Downlink (rx) Control PDU type
 * ========================================================================== */
typedef struct UrlAmRxCtrlPduTag
{
    Int16                               bitLength;
    Int8                                *pduData_p;
} UrlAmRxCtrlPdu;

/* Declare typedef for singly linked list carrying UrlAmTxCtrlPdu */
UT_SINGLE_LINK_LIST_DECLARE (UrlAmRxCtrlList, UrlAmRxCtrlPdu)

/* Out Of Sequence (OOS) */
typedef struct UrlAmRxOosLinkedListEntryTag
{
    UrlSequenceNumber                   firstSn;
    UrlSequenceNumber                   lastSnStored;
    Int16                               totalPdusSize;
    Int16                               remainPdusSize;
    Int16                               nextPduOffset;
    Int8                                numPdusStored;
    Int8                              * data_p;
    Int8                                storedPdus
                                [URL_AM_OOS_NUM_PDUS_PER_ENTRY / BITS_PER_INT8];
    Int16                               pduOffset[URL_AM_OOS_NUM_PDUS_PER_ENTRY];
} UrlAmRxOosLinkedListEntry;

UT_DOUBLE_LINK_LIST_DECLARE (UrlAmRxOosList, UrlAmRxOosLinkedListEntry)

typedef struct UrlAmRxOosEntityTag
{
    UrlAmRxOosList                      oosListRoots [URL_AM_OOS_NUM_LISTS];
#if defined (DEVELOPMENT_VERSION)
    Int32                               totalPdusStored;
    Int8                                snPresentFlags [URL_AM_SN_FLAG_OCTETS];
#endif /* DEVELOPMENT_VERSION */
} UrlAmRxOosEntity;

/* DL Flow Control Module */
typedef enum UrlAmDfcStateTag
{
    URL_DFC_STATE_OFF       = 0,
    URL_DFC_STATE_TO_OFF    = 1,
    URL_DFC_STATE_ON        = 2,
    URL_DFC_STATE_TO_ON     = 3
} UrlAmDfcState;
#if defined(UMTS7_PRE_R8)
typedef struct UrlAmLiRecordTag
{
	Int8 bPreCopy;//0xff exact li,0xf padding,0xfe 15bit one short,0x1f oos processd,0x2f many li in one pdu and pre li was processed in last round of oos
	Int8 bLiNum;
	Int16 wLiValue;//0xff: special HE, contain complete SDU
	Int8* pbNext;
	Int8* pbLiStartAddr;
    Int8* pbLiEndAddr;

}UrlAmLiRecord;
#endif

/* Declare typedef for single linked list carrying UrlcAmDataIndSdu */
UT_SINGLE_LINK_LIST_DECLARE (UrlcAmDataIndSduList, UrlcAmDataIndSdu)

/* ==========================================================================
 * Entity data type
 * ========================================================================== */
typedef struct UrlAmTxRxEntityTag
{
    BearerIdentity                  bearerId;
    TaskId                          higherTaskId;
    Boolean                         loopback;       /* Configured for loopback */
    UrlAmConfig                     config;         /* Configuration data */
    UrlAmState                      ulState;        /* State of UL entity */
    UrlAmTxState                    txState;        /* Transmitter state */
    UrlAmState                      dlState;        /* State of DL entity */

    Boolean                         sentDataPending; /* Flag control SIG_CRLC_DATA_PENDING_IND */

	/*Fix ********** start 20150312 jianghaifang*/
	Boolean dlHsdataFlag;
	Boolean reesttablishDiscardHsdataind;
	/*Fix ********** end 20150312 jianghaifang*/

    /* Transmitter state variables */
    UrlSequenceNumber               vtNew;          /* SN of next new tx data
                                                     * PDU to be created
                                                     */

    UrlSequenceNumber               vtS;            /* SN of next PDU to be TX'd
                                                     * for the first time
                                                     */
    UrlSequenceNumber               vtA;            /* SN of next in-sequence
                                                     * PDU expected to be ACK'd,
                                                     * thus forming the lower
                                                     * edge of the acceptable
                                                     * acknowledgedments
                                                     */
#if defined (UMTS7_PRE_R8)
    UrlAmLiRecord* stLiPosList;
    Int32* pbDlPduList;    /*The list point to dl rlc pdu*/
    /*Fix ********** 20131021 begin*/
   /* Int32 pbDlPduSizeList[4096];*/        /*duplicate with dlDataPdus.pduSize*/
    Int8 pbDlPduLiNum[4096];
    Int8 pbDlPduEndFlag[URL_AM_SN_FLAG_OCTETS];
    /*Fix ********** 20131021 end*/
    Int8* pbLstPos;
    Int32 dwLiNum;
    Boolean bDiscardBeyondSdu;
    UrlSequenceNumber lstSn;
    Int8 bLiOct;
#endif
#if !defined (ENABLE_NEW_URLC_OPTIMISATIONS)
    UT_DOUBLE_LINK_LIST_NODE_TYPE(UrlAmTxDataList)
                                  * vtA_p;          /* Pointer to VT(A) node
                                                     * (next unacknowledged UL
                                                     *  data PDU)
                                                     * PNULL when list empty
                                                     */
    UT_DOUBLE_LINK_LIST_NODE_TYPE(UrlAmTxDataList)
                                  * vtS_p;          /* Pointer to VT(S) node
                                                     * (next untransmitted UL
                                                     *  data PDU)
                                                     * PNULL when list empty
                                                     */
#endif

    UrlSequenceNumber               vtMS;           /* SN of the first PDU not
                                                     * allowed by the peer RX
                                                     * entity, thus forming the
                                                     * upper edge of the TX
                                                     * window
                                                     */
    Int8                            vtPDU;          /* TX'd PDU counter, used
                                                     * with Poll_PU (1-128)
                                                     */
    Int8                            vtSDU;          /* TX'd SDU counter, used
                                                     * with Poll_SDU (1-64)
                                                     */
    UrlSequenceNumber               vtWS;           /* TX window size */
    Int8                            vtMRW;          /* Transmitted MRW counter,
                                                     * used with MaxMRW (1-32)
                                                     */
    Int8                            vtRST;          /* TX'd RESET PDU counter,
                                                     * used with MaxRST (1-32)
                                                     */

    /* Receiver state variables */
    UrlSequenceNumber               vrR;            /* SN of next in-sequence
                                                     * PDU to be RX'd
                                                     */
    UrlSequenceNumber               vrH;            /* SN of highest PDU
                                                     * expected to be RX'd
                                                     */
    UrlSequenceNumber               vrWS;           /* RX window size */
    UrlSequenceNumber               vrMR;           /* SN of first PDU not
                                                     * allowed by the receiver
                                                     */

    /* Structure containing uplink SDUs */
    struct
    {
        /* Total size in bits of the unsegmented SDUs in the UL SDUs list */
        Int32                                           unsegmentedSdusTotalBits;
        /* List of UL SDUs, carrying payload of type: UrlAmTxSdu */
        UrlAmTxSduList                                  list;
        SignalBuffer                                    dataCnf;
        SignalBuffer                                    discardInd;

        /* Pointer to next UL SDU node to be segmented */
        UT_DOUBLE_LINK_LIST_NODE_TYPE(UrlAmTxSduList) * segment_p;

        /* Pointer to next UL SDU node to be deleted */
        UT_DOUBLE_LINK_LIST_NODE_TYPE(UrlAmTxSduList) * delete_p;
    }                               ulSdus;

#if defined (ENABLE_NEW_URLC_OPTIMISATIONS)
    /* This points to a table of PDU blocks. Each block is a set of PDUs of type
     * UrlAmTxDataPdu. The table is indexed as a function of SN and should be
     * accessed only via functions in urlamwin.c.
     *
     * Memory for the PDU table is allocated in multiples of
     * (URL_AM_NUM_PDUS_PER_TABLE_BLOCK PDUs * sizeof (UrlAmTxDataPdu)). The memory
     * is allocated in one large allocation to save multiple allocations.
     * Each entry in txPduBlockTable_p[] is then updated to point to the correct
     * locations in this allocation.
     */
    UrlAmTxDataPdu      *txPduBlockTable_p [URL_AM_NUM_PDU_TABLE_BLOCKS];

    /* This stores the size of the TX PDU table
     */
    Int16               txPduTableSize;

    /* This table contains the indexes into txPduBlockTable_p[] that contain the
     * start of allocated memory blocks. This table is only used to record memory
     * blocks that have been allocated when the window size is increased. This table
     * is used when destroying the txPduBlockTable_p. txPduTableAllocEntries contains
     * the number of entries used in txPduTableResizeAllocStart[].
     */
    Int16               txPduTableResizeAllocStart [URL_AM_NUM_PDU_TABLE_BLOCKS];

    /* This is the number of entries in txPduTableResizeAllocStart
     */
    Int8                txPduTableAllocEntries;

    /* This is a bitmap of PDUs to be retransmitted. Bits set represent PDUs
     * to retransmit. PDUs are indexed via sequence number. The rightmost
     * bit represents the lowest SN. The lowest index is the lowest SN
     */
    Int32               retransmitBitmap [URL_AM_PDU_BITMAP_LENGTH];

    /* This is the number of PDUs waiting to be retransmitted. numPdusForRetransmit
     * is the same as the number of bits set in retransmitBitmap.
     */
    Int32               numPdusForRetransmit;

    /* This is a bitmap of PDUs that are currently active. Bits set represent PDUs
     * that are currently active (i.e. untransmitted, transmitted but not acked,
     * awaiting retransmission etc..). PDUs are indexed via sequence number. The
     * rightmost bit represents the lowest SN. The lowest index is the lowest SN
     */
    Int32               txPduActiveBitmap [URL_AM_PDU_BITMAP_LENGTH];

    /* Indicates if there is data waiting to be segmented, but there is no space
     * in the transmit window to accommodate the data. If this is the case, then
     * wait for vtA to move before attempting to segment new data
     */
    Boolean             newDataWaitingToBeSegmented;

    /* This contains a new PDU that has a LI. It is generated by the
     * UrlAmTxNewSduSegment() and is only placed in this structure if
     * there is no space left in the transmit window. This can occur
     * if UrlAmTxNewSduSegment() creates 2 PDUs and only there is only
     * space in the transmit window for 1 new PDU. In this case, the
     * PDU is stored here temporarily until space becomes available in
     * the transmit window.
     */
    UrlAmTxDataPdu      newAmTxLiPdu;

    /* This flag indicates if newAmTxLiPdu is used to store a LI PDU
     */
    Boolean             newAmTxLiPduPresent;
    Int32               txSufiNackedBitmap [URL_AM_PDU_BITMAP_LENGTH];
    Int32               txSufiAckedBitmap [URL_AM_PDU_BITMAP_LENGTH];
#else
    /* List of uplink untransmitted, transmited and retransmit data PDUs,
     * carrying payload of type: UrlAmTxDataPdu
     */
    UrlAmTxDataList                 ulDataPdus;

    /* List of uplink data PDUs for retransission, carrying pointer to entries in
     * ulDataPdus
     */
    UrlAmRtxDataList                ulRetransmitDataPdus;

#endif /* ENABLE_NEW_URLC_OPTIMISATIONS */

    /* List of uplink untransmitted control PDUs, carrying payload of type:
     *      UrlAmTxCtrlPdu
     */
    UrlAmTxCtrlList                 ulCtrlPdus;
/*********** merged for FRBD 20140806 start*/
    /* Number of PDU bits in the retransmission PDUs list */
    Int32                           ulRtxDataPdusBits;

    /* Number of PDU bits in the uplink PDUs list that were segmented but were
     * not transmitted for the first time yet.
     */
    Int32                           ulUnTxDataPdusBits;

    /* Array to hold each UL PDU size according to SN (bytes) */
    //Int16                           ulPdusSize[URL_AM_SN_MODULUS];
/*********** merged for FRBD 20140806 end*/
    struct
    {
        /* Packed array of flags indicating which DL PDUs are present */
        Int8                                            snPresentFlags[URL_AM_SN_FLAG_OCTETS];

        /* Packed array of flags indicating which DL PDUs contain length indicators */
        Int8                                            liPresentFlags[URL_AM_SN_FLAG_OCTETS];

        /* Array to hold each PDU size according to SN */
        Int16                                           pdusSize[URL_AM_SN_MODULUS];

        /* Flag indicating that DL SDU reassembly should be attampted */
        Boolean                                         reassemble;

        /* List of downlink PDUs, carrying payload of type:
         *      UrlAmRxSdu
         * Where each list entry is large enough to carry a maximum sized SDU
         */
        UrlAmRxSduList                                  sduList;

        /* Flag indicating that DL SDU are ready to be sent to higher layer.
         * Can be FALSE when there are not SDUs to deliver to higher layer
         * or when DL delivery is suspended due to flow control mechanism
         */
        Boolean                                         sduDeliverReady;

        /* List of downlink SDUs ready to be delivered to higher layer
         * after performing UrlAmRxReassembleSdu.
         * Will be delivered to HL according to downlink flow control state.
         */
        UrlcAmDataIndSduList                            sdusToDeliverList;
    
        /* Buffered AmDataInd signal. Buffered until the signal is carying its
         * full quota of SDUs or there are no SDUs left to process.
         */
        SignalBuffer                                    reassembledSdus;
#if defined(UMTS7_PRE_R8)
		Int8											sduStored;
#endif
    }                               dlDataPdus;

    /* List of downlink unprocessed control PDUs, carrying payload of type:
     *      UrlAmRxCtrlPdu
     */
    UrlAmRxCtrlList                 dlCtrlPdus;

    /* Transmitter run-time variables */
    Boolean                         pollTriggered;
    UrlAmTxCause                    pollTriggerCause;
    Boolean                         pollTransmitted;

    /* MRW related data */
    enum
    {
        URL_AM_MRW_STATE_IDLE           = 0,    /* Inactive */
        URL_AM_MRW_STATE_PENDING        = 1,    /* MRW pending */
        URL_AM_MRW_STATE_TRANSMITTED    = 2,    /* Waiting for UmacTxStatusInd */
        URL_AM_MRW_STATE_UNACKNOWLEDGED = 3,    /* Waiting for MRW_ACK */
        URL_AM_MRW_STATE_ACKNOWLEDGED   = 4     /* Deleting PDUs and SDUs */
    }                               mrwState;
    /* mrwNode_p is dependent on mrwState :
     * IDLE:            NULL
     * PENDING:         Pointer to entry in ulCtrlPdus containing an MRW
     *                  waiting to be transmitted (there can only be one)
     * TRANSMITTED:     Submitted to UMAC for transmission, waiting for
     *                  UmacTxStatusInd
     * UNACKNOWLEDGED:  Waiting for MRW_ACK
     * ACKNOWLEDGED:    NULL
     */
    UT_DOUBLE_LINK_LIST_NODE_TYPE(UrlAmTxCtrlList) *mrwNode_p;

    /* Previously received MRW SUFI stored to check against furture MRWs.
     * If a received MRW is deemed a retransmission it is ignored
     */
    struct
    {
        UrlSequenceNumber                               mrwSn;
        Int8                                            mrwNLength;
    }                               dlPrevMrw;

    /* DL MRW stored for processing when the related PDU is received */
    struct
    {
        UrlSequenceNumber                               mrwSn;
        Int8                                            mrwNLength;
    }                               dlStoredMrw;

    /* Reset related data */
    Boolean                         ulRsn;          /* Uplink   RSN */
    UrlHyperFrameNumber             ulResetHfn;     /* Uplink   HFN for retry */
    Boolean                         dlRsn;          /* Downlink RSN */
    Boolean                         firstResetPdu;
    Boolean                         resetDuringEstablish;
    Boolean                         resetTransmitted;
    Boolean                         windowTransmitted;

    Boolean                         statusTriggered;
    UrlAmTxCause                    statusTriggerCause;
    Boolean                         statusProhibitTriggered;
    UrlSequenceNumber               pollTimerVTS;   /* VT(S)-1 when last poll
                                                     * was transmitted, used
                                                     * due to timerPoll
                                                     */
    MappingRbTxReqId                MapRbTxReqId[MAX_TX_REQ_ID];


    UrlBufferInfo                   bufferInfo;

    /* Receiver run-time variables */
    Boolean                         pollDetected;
#if defined (ENABLE_NEW_URLC_OPTIMISATIONS)
    UrlSequenceNumber               ackNackLastSnProcessed;
#else
    UT_DOUBLE_LINK_LIST_NODE_TYPE(UrlAmTxDataList)
                                  * nackNode_p;
#endif
    /* Timers */
    KiTimer                         timerPoll;
    KiTimer                         timerPollProhibit;
    KiTimer                         timerPollPeriodic;
    KiTimer                         timerStatusProhibit;
    KiTimer                         timerStatusPeriodic;
    KiTimer                         timerMRW;
    KiTimer                         timerRST;

    UrlLoopbackInfo               * loopbackInfo_p; /* PNULL unless test loop
                                                     * is closed
                                                     */
    Boolean                         ulSegmentSdu;   /* Set whenever the UL SDU
                                                     * list needs parsing
                                                     */
    Boolean                    ulDeletePdusAndSdus; /* Set whenever an UL PDU
                                                     * is acknowledged and
                                                     * remains set until all
                                                     * ack'd PDUs and SDUs
                                                     * have been deleted
                                                     */

    UrlCipherConfigList             ulCipherConfigList;
    UrlHyperFrameNumber             ulHfn;          /* HFN of VT(S)-1 */
    UrlCountcInfo                   ulCountcInfo;   /* COUNT-C of VT(S)-1 */

    UrlCipherConfigList             dlCipherConfigList;
    UrlHyperFrameNumber             dlHfnLo;        /* HFN of VR(R)-1 */
    UrlCountcInfo                   dlCountcInfoHi; /* COUNT-C of VR(H)-1 */

    UrlAmRxOosEntity                dlOosEntity;

    /* DL Flow Control Module */
    UrlAmDfcState                   dlFlowCntrlState;
    Int8                            dlFlowCntrlWindowTxCnt;
    Int16                           dlFlowCntrlNumPdusRxedInTti;

    /* RB throughput statistics */
    Int32                           totalDataSent;
    Int32                           totalDataReceived;

#if defined (ENABLE_URLC_UNIT_TEST)
    CrlcTestModeF8                  dlf8mode;       /* UE or UTRAN */
    BearerIdentity                  dlf8bearer;     /* Bearer when in UTRAN mode */
#endif /* ENABLE_URLC_UNIT_TEST */

#if defined (DEVELOPMENT_VERSION)
    Int16                           errorTxCount;
    Int16                           errorRxCount;
#endif /* DEVELOPMENT_VERSION */

    /* Statistic values */
#if defined (ENABLE_UPLANE_STATISTICS)
    UrlUplinkStatistics             ulStats;
    UrlDownlinkStatistics           dlStats;
#endif /* ENABLE_UPLANE_STATISTICS */

#if defined (ENABLE_UL_THROUGHPUT_IND)
    UrlUlTpStatistics               ulTpStats;
#endif /* ENABLE_UL_THROUGHPUT_IND */

    /* Inidicates if the bearer has DL SDUs buffered for delivery to upper layers. */
    Boolean                         dlSdusReassembled;
	/*modified ********** start*/
	Boolean 						maintainSRBDataReceive;
	/*modified ********** end*/
#if defined(RLC_LOW_PRIOR_OPT)
	void *activeAmBearerNode_p;
#endif
#if !defined(ON_PC)
	/* Engineering Mode PS domain UL/DL statistics report */
	UrlcEngModeUlStatistics			engModeUlStatistics; 
	UrlcEngModeDlStatistics			engModeDlStatistics;
#endif /* !ON_PC */

} UrlAmTxRxEntity;

/***************************************************************************
 * Typed Constants
 ***************************************************************************/

typedef enum UrlAmTimerIdentitiesTag
{
    URL_AM_TIMER_POLL               = 0,
    URL_AM_TIMER_POLL_PROHIBIT      = 1,

    /* The EPC mechansim has been removed from 25.322, however to avoid unit
     * tests failing, reserve this timer value so that all subsequent timer
     * values remain unchanged
     */
    URL_AM_TIMER_UNUSED             = 2,

    /* URL_AM_TIMER_SDU_DISCARD is implemented by polling kernel ticks */
    URL_AM_TIMER_PERIODIC_POLL      = 3,
    URL_AM_TIMER_STATUS_PROHIBIT    = 4,
    URL_AM_TIMER_PERIODIC_STATUS    = 5,
    URL_AM_TIMER_RESET              = 6,
    URL_AM_TIMER_MRW                = 7,

    /* Ensure this entry is last */
    URL_AM_NUM_TIMERS               = 8
} UrlAmTimerIdentities;

/* Enum used to define if a data PDU is retransmittable */
typedef enum UrlAmRetransmitStatusTag
{
    URL_AM_PDU_RETRANSMIT           ,                   /* Can be retransmited */
    URL_AM_PDU_MAXDAT_MINUS_1       ,                   /* VT(DAT)=MAXDAT-1 */
    URL_AM_PDU_MAXDAT               ,                   /* VT(DAT)=MAXDAT */
    URL_AM_PDU_OUTSIDE_WINDOW                           /* Outside TX window */
} UrlAmRetransmitStatus;
#endif

/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/2g.mod/api/inc/tl_typ.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/26 20:15:58 $
 **************************************************************************
 *  File Description :
 *      types common to ts_sig.h and smrl_sig.h
 **************************************************************************/

#ifndef TL_TYP_H
#define TL_TYP_H

/****************************************************************************
 * Nested Include Files
 ****************************************************************************/

#if !defined (SML3_TYP_H)
#include     <sml3_typ.h>
#endif

#if !defined (SITL_TYP_H)
#include     <sitl_typ.h>
#endif

/****************************************************************************
 * Macros
 ****************************************************************************/

#define     NO_RP_CAUSE_DIAGNOSTIC      0

#define     RP_CAUSE_DIAGNOSTIC      1
/* MAX_TPDU_INFO_LENGTH
**
** Maximum amount of user data associated with an RP message. GSM 24.011,
** section 7.3 gives the following sizes for the various RP message types:
**
**    RP-DATA   233
**    RP-ACK    234
**    RP-ERROR  234
**
** One byte is IE identifier second byte is length indicator
*/
#define     MAX_TPDU_INFO_LENGTH        232

/* SMS_MAX_RAW_ADDRESS_DATA
**
** This is the maximum amount of data that can be transfered in either an
** RP-Originator Address or RP-Destination Address (excluding the length
** byte).
*/
#define     SMS_MAX_RAW_ADDRESS_DATA   11

/****************************************************************************
 * Types
 ****************************************************************************/

typedef enum SmsStatusOfReportTag
{
   TRANSFER_OK                  =   0,
   TRANSFER_ERROR
}
SmsStatusOfReport;

typedef struct RpCauseElementTag
{
    GsmCause                        cause;
    Int8                            diagnostic;
}
RpCauseElement;

typedef struct RpUserDataElementTag
{
    Int16                           tpduInfoLength;
    Int8                            tpduInformation[MAX_TPDU_INFO_LENGTH];
}
RpUserDataElement;

typedef struct RpUserDataTag
{
    Int8                            length;
    Int8                            data[MAX_TPDU_INFO_LENGTH - 2];
}
RpUserData;

typedef struct RawSmsAddressTag
{
    Int8     length;
    Int8     data[SMS_MAX_RAW_ADDRESS_DATA];

} RawSmsAddress;

#endif
/* END OF FILE */








































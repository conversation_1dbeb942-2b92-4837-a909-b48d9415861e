/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/devices/transports/usb.mod/api/inc/usbselection.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/24 10:16:13 $
 ***************************************************************************
 * File Description:
 * Contains the device configuration data required by the remote-controlling
 * entity.  As this is likely to be an MMI related task, it should minimise
 * the amount of USB baggage required.
 **************************************************************************/

#if defined (UPGRADE_USB)

#ifndef USBSELECTION_H
#define USBSELECTION_H

/* The set of USB functions that we support. */
typedef enum UsbFunctionTag
{
  USB_FUNCTION_MAST = 0,
  USB_FUNCTION_EMMI,
  USB_FUNCTION_MODEM,
  USB_FUNCTION_DIAG,

  USB_NUMBER_OF_USB_FUNCTIONS

} UsbFunction;


/* Set of possible configurations to select from.  This includes combinations
 * of Functions as Composites. */
typedef enum UsbDeviceSelectionTag
{
  /* Note that ALL combinations are defined irrespective of configuration
   * (_INTERFACE) build switches.  This makes the code considerably clearer
   * and the use of illegal selections is detected by the usbAppif function,
   * so there's no problem in defining all the enum members.
   *
   * It also means that application code that will need to include this header
   * is independent of the _INTERFACE build switches.
   */
  USB_DYNAMIC_SELECTION_MAST = 0, /* Mass Storage */
  USB_DYNAMIC_SELECTION_EMMI,     /* Genie */
  USB_DYNAMIC_SELECTION_MODEM,    /* Modem */
  USB_DYNAMIC_SELECTION_DIAG,     /* Proprietary interface */

  /* Composites */
  USB_DYNAMIC_SELECTION_C_MAST_MODEM, /* Mass Storage + Modem */
  USB_DYNAMIC_SELECTION_C_MAST_MODEM_EMMI, /* Mass Storage + Modem + EMMI */

  USB_NUMBER_OF_DYNAMIC_SELECTIONS
} UsbDeviceSelection;


/* Structure for passing information about the USB build configuration to
 * the MMI */

#define USB_CONFIG_QUERY_CNF_MAX_N_FUNCTIONS 8
#define USB_CONFIG_QUERY_CNF_MAX_N_CONFIGS   8

typedef struct UsbConfigQueryStructTag
{
  Int8 numDynamicConfigurations; /* 1 implies non-dynamic configuration */
  struct
  {
    Int8               numFunctions;
    UsbFunction        func[ USB_CONFIG_QUERY_CNF_MAX_N_FUNCTIONS ];
    UsbDeviceSelection selectionCode;
    Boolean            isDefault;
  }
  config[ USB_CONFIG_QUERY_CNF_MAX_N_CONFIGS ];

} UsbConfigQueryStruct;



/* Function Prototypes */

/* Function for 'MMI' selection request */
extern void usbAppifSelectDynamicConfiguration( UsbDeviceSelection newConfiguration,
                                                TaskId replyTo );

/* Function to query the validity of a selection value */
extern Boolean usbAppifDynamicSelectionValid( UsbDeviceSelection newConfiguration );

/* Function to query the configuration options */
extern void usbAppifGetConfiguration( UsbConfigQueryStruct* response );

/* Function to query the default power-up configuration */
extern UsbDeviceSelection usbAppifGetDefaultConfiguration( void );

/* USBSELECTION_H ends */
#endif
/* UPGRADE_USB ends */
#endif
/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/3g.mod/lib/src/usimemu.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2007/01/12 13:10:41 $
 **************************************************************************
 *  File Description :
 *
 *  types for sim emulator
 **************************************************************************/
  //t
#if !defined (USIMEMU_H)
#define USIMEMU_H

/***************************************************************************
 * Nested Include Files
 **************************************************************************/
#ifdef SIM_EMULATION_ON
#if defined (UPGRADE_3G)
#if !defined (SYSTEM_H)
#include "system.h"
#endif

#if !defined(SIMDATA_H)
#include "simdata.h"
#endif

#if !defined(L1SI_SIG_H)
#include "l1si_sig.h"
#endif

/***************************************************************************
 * Manifest Constants
 **************************************************************************/

/* Number of records for LF and C type files      */
/* All of these values may change from SIM to SIM */

#if defined (MULTI_USIM_APPLICATIONS)
#define SIM_EMU_NUM_DIR_RECS           3
#else
#define SIM_EMU_NUM_DIR_RECS           1
#endif


#define SIM_EMU_NUM_USIM_ARR_RECS      6

#if defined (USIM_EMU_USE_EXTENDED_ECC_CODES)
/* testing for extended ECC codes */
#define SIM_EMU_NUM_USIM_ECC_RECS     11
#else
#define SIM_EMU_NUM_USIM_ECC_RECS      3
#endif

#define SIM_EMU_NUM_ICI_RECS           1
#define SIM_EMU_NUM_OCI_RECS           1
#define SIM_EMU_NUM_USIM_BDN_RECS      20
#define SIM_EMU_NUM_USIM_ACM_RECS      2
#define SIM_EMU_NUM_USIM_FDN_RECS      3
#define SIM_EMU_NUM_USIM_SMS_RECS      5
#define SIM_EMU_NUM_USIM_SMSR_RECS     10   /*job 100781*/
#define SIM_EMU_NUM_USIM_SMSP_RECS     5


#define SIM_EMU_NUM_IMG_RECS           5


#define SIM_EMU_NUM_USIM_ICI_RECS      7
#define SIM_EMU_NUM_USIM_OCI_RECS      6
#define SIM_EMU_NUM_USIM_MSISDN_RECS   3
#define SIM_EMU_NUM_USIM_EXT3_RECS     10
#define SIM_EMU_NUM_USIM_EXT4_RECS     12
#define SIM_EMU_NUM_USIM_EXT5_RECS     12
#define SIM_EMU_NUM_USIM_EXT6_RECS     10
#define SIM_EMU_NUM_USIM_EXT7_RECS     12
#define SIM_EMU_NUM_USIM_CFIS_RECS     4
#define SIM_EMU_NUM_USIM_MBDN_RECS     4
#define SIM_EMU_NUM_USIM_MWIS_RECS     2
#define SIM_EMU_NUM_USIM_CCP2_RECS     4
#define SIM_EMU_NUM_USIM_PNN_RECS      12
#define SIM_EMU_NUM_USIM_OPL_RECS      15
#define SIM_EMU_NUM_USIM_ORPK_RECS     2
#define SIM_EMU_NUM_USIM_ARPK_RECS     2
#define SIM_EMU_NUM_USIM_TPRPK_RECS    2

/* Size of records for LF and C type files */

#define SIM_EMU_NUM_USIM_EXT2_RECS     11
#define SIM_EMU_NUM_USIM_SDN_RECS      8
#define SIM_EMU_SIZE_USIM_SMSR_RECS    10     /*job 100781*/
#define SIM_EMU_SIZE_USIM_KEYS_FILE    33
#define SIM_EMU_SIZE_USIM_AAS_FILE     12
#define SIM_EMU_SIZE_USIM_CCP2_FILE    14
#define SIM_EMU_SIZE_USIM_OPL_FILE     8
#define SIM_EMU_SIZE_USIM_PNN_FILE     32
#define SIM_EMU_SIZE_USIM_ARR_FILE      37      /* Variable    */
#define SIM_EMU_SIZE_USIM_ECC_FILE      10
#define SIM_EMU_SIZE_USIM_BDN_FILE      46
#define SIM_EMU_SIZE_USIM_FDN_FILE      46
#define SIM_EMU_SIZE_USIM_SMSS_FILE      2
#define SIM_EMU_SIZE_USIM_SMS_FILE      176
#define SIM_EMU_SIZE_USIM_SMSP_FILE     31
#define SIM_EMU_SIZE_USIM_ICI_FILE         59
#define SIM_EMU_SIZE_USIM_OCI_FILE         58
#define SIM_EMU_SIZE_USIM_OCT_FILE         3
#define SIM_EMU_SIZE_USIM_ICT_FILE         3
#define SIM_EMU_SIZE_USIM_MSISDN_FILE      46
#define SIM_EMU_SIZE_USIM_EXT3_FILE        13
#define SIM_EMU_SIZE_USIM_EXT4_FILE        13
#define SIM_EMU_SIZE_USIM_EXT5_FILE        13
#define SIM_EMU_SIZE_USIM_EXT6_FILE        13
#define SIM_EMU_SIZE_USIM_EXT7_FILE        13
#define SIM_EMU_SIZE_USIM_CFIS_FILE        16
#define SIM_EMU_SIZE_USIM_MBDN_FILE        45
#define SIM_EMU_SIZE_USIM_GID1_FILE        20
#define SIM_EMU_SIZE_USIM_GID2_FILE        20
#define SIM_EMU_SIZE_USIM_EXT1_FILE        13
#define SIM_EMU_SIZE_USIM_EXT2_FILE        13
#define SIM_EMU_SIZE_USIM_SDN_FILE         46
/* CPHS specific - for Mailbox Number file */
#define SIM_EMU_SIZE_CPHS_MN_RECS       46      /* Variable as per ADN */
#define SIM_EMU_SIZE_CPHS_INFO_NUM_RECS 35      /* Variable */


/* Size of T type files */
#define SIM_EMU_SIZE_ICCID_FILE        10
#define SIM_EMU_SIZE_LP_FILE            5
#define SIM_EMU_SIZE_KC_FILE            9
#define SIM_EMU_SIZE_TST_FILE           8
#define SIM_EMU_SIZE_ACTING_HPLMN_FILE  3
#define SIM_EMU_SIZE_RAT_FILE           1
#define SIM_EMU_SIZE_CSP_FILE           16
#define SIM_EMU_SIZE_GID1_FILE         20
#define SIM_EMU_SIZE_GID2_FILE         20
#define SIM_EMU_SIZE_USIM_SPDI_FILE         19

/* CPHS specific - for CPHS_INFO, ON, ONS, CFF and VMWF */
#define SIM_EMU_SIZE_CPHS_INFO_FILE     3      /* Fixed */
#define SIM_EMU_SIZE_CPHS_ON_FILE      20      /* Variable */
#define SIM_EMU_SIZE_CPHS_ONS_FILE     10      /* Fixed */
#define SIM_EMU_SIZE_CPHS_CFF_FILE      2      /* Variable */
#define SIM_EMU_SIZE_CPHS_VMWF_FILE     2      /* Variable */
#define SIM_EMU_SIZE_CPHS_CSP_FILE     16      /* Variable */
#if defined (UPGRADE_GPRS)
#define SIM_EMU_SIZE_KC_GPRS_FILE      9    /* F */
#define SIM_EMU_SIZE_LOCI_GPRS_FILE    14   /* F */
#endif
#define SIM_EMU_SIZE_PL_FILE                10
#define SIM_EMU_SIZE_USIM_LI_FILE           22
#define SIM_EMU_SIZE_DIR_FILE               38
#define SIM_EMU_SIZE_IMG_FILE               19
/*Modification for CQ00003401, cgLiu,Feb.21 2010, begin*/
/*#define SIM_EMU_SIZE_USIM_UST_FILE          7*/
#define SIM_EMU_SIZE_USIM_UST_FILE          11
/*Modification for CQ00003401, cgLiu,Feb.21 2010, end  */

#define SIM_EMU_SIZE_USIM_EST_FILE          1
#define SIM_EMU_SIZE_USIM_KEYS_FILE         33
#define SIM_EMU_SIZE_USIM_FPLMN_FILE        12
#define SIM_EMU_SIZE_USIM_CNL_FILE          24
#define SIM_EMU_SIZE_USER_PLMNWACT_FILE     40
#define SIM_EMU_SIZE_OPERATOR_PLMNWACT_FILE 40
#define SIM_EMU_SIZE_HPLMNWACT_FILE         40
#define SIM_EMU_SIZE_USIM_DCK_FILE          16
#define SIM_EMU_SIZE_USIM_IMSI_FILE         9
#define SIM_EMU_SIZE_USIM_ACC_FILE          2
#define SIM_EMU_SIZE_USIM_LOCI_FILE         11
#define SIM_EMU_SIZE_USIM_HPLMN_FILE        1
#define SIM_EMU_SIZE_USIM_CBMID_FILE        10
#define SIM_EMU_SIZE_USIM_HPLMN_FILE        1
#if defined (SIM_EMU_DISABLE_MNC_LENGTH)
#define SIM_EMU_SIZE_USIM_AD_FILE           3
#else
#define SIM_EMU_SIZE_USIM_AD_FILE           4
#endif
#define SIM_EMU_SIZE_USIM_ACM_FILE          3
#define SIM_EMU_SIZE_USIM_ACM_MAX_FILE      3
#define SIM_EMU_SIZE_USIM_HPLMN_FILE        1
#define SIM_EMU_SIZE_USIM_PUCT_FILE         5
#define SIM_EMU_SIZE_USIM_EST_FILE          1
#define SIM_EMU_SIZE_USIM_DCK_FILE         16
#define SIM_EMU_SIZE_USIM_CBMID_FILE       10
#define SIM_EMU_SIZE_USIM_CBMI_FILE        10
/*Chengguang modify for unit test,2008-8-22, begin*/
#define SIM_EMU_SIZE_USIM_CBMIR_FILE       12
#define SIM_EMU_SIZE_USIM_AAEM_FILE        1
#define SIM_EMU_SIZE_USIM_EMLPP_FILE       2
/*Chengguang modify for unit test,2008-8-22, end  */


#define SIM_EMU_SIZE_USIM_SPN_FILE         17
#define SIM_EMU_SIZE_USIM_ICI_FILE         59
#define SIM_EMU_SIZE_USIM_OCI_FILE         58
#define SIM_EMU_SIZE_USIM_OCT_FILE         3
#define SIM_EMU_SIZE_USIM_ICT_FILE         3
#define SIM_EMU_SIZE_USIM_MSISDN_FILE      46
#define SIM_EMU_SIZE_USIM_EXT3_FILE        13
#define SIM_EMU_SIZE_USIM_EXT4_FILE        13
#define SIM_EMU_SIZE_USIM_EXT5_FILE        13
#define SIM_EMU_SIZE_USIM_EXT6_FILE        13
#define SIM_EMU_SIZE_USIM_EXT7_FILE        13
#define SIM_EMU_SIZE_USIM_MBDN_FILE        45
#define SIM_EMU_SIZE_USIM_MWIS_FILE         5
#define SIM_EMU_SIZE_USIM_GID1_FILE        20
#define SIM_EMU_SIZE_USIM_GID2_FILE        20
#define SIM_EMU_SIZE_USIM_EXT1_FILE        13
#define SIM_EMU_SIZE_USIM_EXT2_FILE        13
#define SIM_EMU_SIZE_USIM_SDN_FILE         46
#define SIM_EMU_SIZE_USIM_KC_FILE          9
#define SIM_EMU_SIZE_USIM_LOCI_PS_FILE     14
#define SIM_EMU_SIZE_USIM_THRESHOLD_FILE   3
#define SIM_EMU_SIZE_USIM_NETPAR_FILE      74
#if defined  (SIM_EMU_BIG_ACL_FILE)
#define SIM_EMU_SIZE_USIM_ACL_FILE         187
#else
#define SIM_EMU_SIZE_USIM_ACL_FILE         64
#endif
#define SIM_EMU_SIZE_USIM_START_HFN_FILE    6

#define SIM_EMU_SIZE_USIM_SMSR_FILE        30       /*job 100781*/
#define SIM_EMU_SIZE_USIM_HIDDEN_KEY_FILE       4
/*Modification for USIM it test. CQ00001116, Chengguang Liu, June.09 09,Begin*/

#define SIM_EMU_SIZE_USIM_CPBCCH_FILE           4
#define SIM_EMU_SIZE_USIM_INVSCAN_FILE          1          
#define SIM_EMU_SIZE_USIM_SUME_FILE             10
#define SIM_EMU_SIZE_USIM_CMI_FILE              21     
#define SIM_EMU_SIZE_USIM_RPLMN_FILE            15   
#define SIM_EMU_SIZE_USIM_MBI_FILE              5     
#define SIM_EMU_SIZE_USIM_MMSN_FILE             10    
#define SIM_EMU_SIZE_USIM_EXT8_FILE             10    
#define SIM_EMU_SIZE_USIM_MMSICP_FILE           10 
#define SIM_EMU_SIZE_USIM_MMSUP_FILE            10   
#define SIM_EMU_SIZE_USIM_MMSUCP_FILE           10  


/*Modification for USIM it test. CQ00001116, Chengguang Liu, June.09 09,End  */
/*Modification for CQ00003401, cgLiu,Feb.21 2010, begin*/
#define SIM_EMU_SIZE_USIM_EHPLMN_FILE           30
#define SIM_EMU_SIZE_USIM_EHPLMNPI_FILE         1
/*Modification for CQ00003401, cgLiu,Feb.21 2010, end  */
/*Added by zhjzhao for CQ00013463 Add a set of default USIM files value for LTE simulation on PC 10-10-11, Begin*/
#if defined(UPGRADE_LTE)
#define SIM_EMU_SIZE_USIM_LRPLMNSI_FILE         1
#define SIM_EMU_NUM_USIM_NIA_RECS      1
#define SIM_EMU_SIZE_USIM_NIA_FILE            10
#define SIM_EMU_NUM_USIM_VGCS_RECS      1
#define SIM_EMU_SIZE_USIM_VGCS_FILE          200
#define SIM_EMU_NUM_USIM_VGCSS_RECS      1
#define SIM_EMU_SIZE_USIM_VGCSS_FILE          7
#define SIM_EMU_NUM_USIM_VBS_RECS      1
#define SIM_EMU_SIZE_USIM_VBS_FILE          200
#define SIM_EMU_SIZE_USIM_VBSS_FILE          7
#define SIM_EMU_SIZE_USIM_VGCSCA_FILE   2
#define SIM_EMU_SIZE_USIM_VBSCA_FILE        2
#define SIM_EMU_SIZE_USIM_GBABP_FILE        16
#define SIM_EMU_SIZE_USIM_MSK_FILE      20
#define SIM_EMU_SIZE_USIM_MUK_FILE      48
#define SIM_EMU_SIZE_USIM_GBANL_FILE        48
#define SIM_EMU_SIZE_USIM_NAFKCA_FILE       48
#define SIM_EMU_SIZE_USIM_SPNI_FILE     31
#define SIM_EMU_SIZE_USIM_PNNI_FILE     31

/*EFs at the DF SoLSA level*/
#define SIM_EMU_SIZE_USIM_SAI_FILE      31
#define SIM_EMU_SIZE_USIM_SLL_FILE      22
#endif
/*Added by zhjzhao for CQ00013463 Add a set of default USIM files value for LTE simulation on PC 10-10-11, End*/

#define CHV_ATTEMPTS                    3
#define CHV_UNBLOCK_ATTEMPTS           10

#define PIN_1_VALUE          "1111"
#define PIN_2_VALUE          "2222"    /* PIN2 = 1 1 1 1*/

#define UNBLOCK_PIN_1_VALUE          "11111111"
#define UNBLOCK_PIN_2_VALUE          "22222222"

#define UNIVERSAL_PIN_VALUE          "1234"
#define UNBLOCK_UNIVERSAL_PIN_VALUE  "12345678"
/*Modification for USIM it test. CQ00001116, Chengguang Liu, June.09 09,Begin*/

#define CCSA_PIN_1_VALUE          "2468"
#define CCSA_PIN_2_VALUE          "3579"    /* PIN2 = 1 1 1 1*/
#define CCSA_NEW_PIN1_VALUE       "01234567" /*Zhiwei add for change PIN case of the CCSA 6.1.2*/
#define CCSA_NEW_PIN2_VALUE        "12345678" /*Zhiwei add for change PIN case of the CCSA 6.1.5*/
#define CCSA_NEW_PIN2_CHANGE_NEW_VALUE     "3579" /*Zhiwei add for change PIN case of the CCSA 6.1.5*/

#define CCSA_UNBLOCK_PIN_1_VALUE          "13243546"
#define CCSA_UNBLOCK_PIN_2_VALUE          "08978675"

#define CCSA_UNIVERSAL_PIN_VALUE          "2839"
#define  CCSA_UNIVERSAL_UPIN_VALUE          "01234567" //Zhiwei change for 6.1.8

#define CCSA_UNBLOCK_UNIVERSAL_PIN_VALUE  "02030405"

/*Modification for USIM it test. CQ00001116, Chengguang Liu, June.09 09,End  */
/*Modfication by zhjzhao for CQ00013425 Merge from LTE_PS_DEV to NeZha_DEV_Daily 10-09-11, Begin*/
/*Modification of SIM emulator for CQ00004507, cgLiu,Jun.17 2010, begin*/
#if defined (UPGRADE_LTE)
#define SIM_EMU_SIZE_NCPIP_RECORD_LENGTH                96
#define SIM_EMU_SIZE_NCPIP_RECORD_NUMS              0x05

#define SIM_EMU_SIZE_USIM_EPSLOCI_FILE      0x12

#define SIM_EMU_SIZE_EPSNSC_RECORD_LENGTH               0x36
#define SIM_EMU_SIZE_EPSNSC_RECORD_NUMS             0x01


#endif /*end UPGRADE_LTE*/
/*Modification of SIM emulator for CQ00004507, cgLiu,Jun.17 2010, end  */
/*Modfication by zhjzhao for CQ00013425 Merge from LTE_PS_DEV to NeZha_DEV_Daily 10-09-11, End*/
/***************************************************************************
 * Type Definitions
 **************************************************************************/


typedef enum SimUiccFileIdTag
{
    SIM_UICC_DIR_ID =0,
    SIM_UICC_ECC_ID,
    SIM_UICC_PL_ID,
    SIM_UICC_LI_ID,
    SIM_UICC_IMSI_ID,
    SIM_UICC_ACC_ID,
    SIM_UICC_HPLMN_ID,
    SIM_UICC_LOCI_ID,
    SIM_UICC_FPLMN_ID,
/*CQ00135656, Cgliu, 2022-02-25,Begin*/
#if defined (ON_PC)
    SIM_UICC_CNL_ID,
    SIM_UICC_CBMID_ID,
    SIM_UICC_DCK_ID,
    SIM_UICC_BDN_ID,
    SIM_UICC_IMG_ID,
    SIM_UICC_IMGINST1_ID,
    SIM_UICC_IMGINST2_ID,
    SIM_UICC_IMGINST3_ID,
    SIM_UICC_IMGINST4_ID,
    SIM_UICC_IMGINST5_ID,
#endif 
    /*CQ00135656, Cgliu, 2022-02-25,End  */    
    
    SIM_UICC_ICCID_ID,
/*CQ00135656, Cgliu, 2022-02-25,Begin*/
#if defined (ON_PC)
    
    SIM_UICC_CSP_ID,
    SIM_UICC_TST_ID,
    SIM_UICC_ACTING_HPLMN_ID,
    SIM_UICC_RAT_ID,
#endif 
/*CQ00135656, Cgliu, 2022-02-25,End  */    
    SIM_UICC_AD_ID,
/*CQ00135656, Cgliu, 2022-02-25,Begin*/
#if defined (ON_PC)
    SIM_UICC_ACM_ID,
    SIM_UICC_PUCT_ID,
#endif 
/*CQ00135656, Cgliu, 2022-02-25,End  */    
    SIM_UICC_UST_ID,
    SIM_UICC_EST_ID,
/*CQ00135656, Cgliu, 2022-02-25,Begin*/
#if defined (ON_PC)    
    SIM_UICC_ACM_MAX_ID,
    SIM_UICC_FDN_ID,
    SIM_UICC_CBMI_ID,
    SIM_UICC_SMSR_ID,      /*job 100781*/
    SIM_UICC_SMSS_ID,
    SIM_UICC_SMS_ID,
    SIM_UICC_SMSP_ID,
    SIM_UICC_SPN_ID,
    SIM_UICC_OCI_ID,
    SIM_UICC_ICI_ID,
    SIM_UICC_ICT_ID,
    SIM_UICC_OCT_ID,
    SIM_UICC_MSISDN_ID,
    SIM_UICC_EXT3_ID,
    SIM_UICC_EXT4_ID,
    SIM_UICC_EXT5_ID,
    SIM_UICC_EXT6_ID,
    SIM_UICC_EXT7_ID,
    SIM_UICC_CFIS_ID,
    SIM_UICC_MBDN_ID,
    SIM_UICC_MWIS_ID,
    SIM_UICC_GID1_ID,
    SIM_UICC_GID2_ID,
    SIM_UICC_EXT2_ID,
    SIM_UICC_SDN_ID,
#endif 
/*CQ00135656, Cgliu, 2022-02-25,End  */      
    SIM_UICC_KC_ID,
    SIM_UICC_KC_GPRS_ID,
    SIM_UICC_KEYS_ID,
    SIM_UICC_KEYS_PS_ID,
    SIM_UICC_LOCI_PS_ID,
    SIM_UICC_START_HFN_ID,
    SIM_UICC_THRESHOLD_ID,
    SIM_UICC_NETPAR_ID,
    SIM_UICC_ACL_ID,
    SIM_UICC_USER_PLMN_SEL_ID,
/*CQ00135656, Cgliu, 2022-02-25,Begin*/
#if defined (ON_PC)    
    SIM_UICC_OPL_ID,
    SIM_UICC_PNN_ID,
#endif 
/*CQ00135656, Cgliu, 2022-02-25,End  */    
    SIM_UICC_OPERATOR_PLMN_SEL_ID,
    SIM_UICC_HPLMN_SEL_ID,
    SIM_UICC_ARR_ID,
/*CQ00135656, Cgliu, 2022-02-25,Begin*/
#if defined (ON_PC)     
    SIM_UICC_HIDDEN_KEY_ID,
    SIM_UICC_CCP2_ID,
    SIM_UICC_SPDI_ID,
/*LOCAL PHONEBOOK files*/
    SIM_UICC_EXT1_ID,
    SIM_UICC_PBC_ID,
    SIM_UICC_PBC1_ID,
    SIM_UICC_AAS_ID,
    SIM_UICC_PBR_ID,
    SIM_UICC_ADN_ID,
    SIM_UICC_IAP_ID,
    SIM_UICC_IAP1_ID,
    SIM_UICC_ADN1_ID,
    SIM_UICC_CCP1_ID,
    SIM_UICC_GRP_ID,
    SIM_UICC_GRP1_ID,
    SIM_UICC_PSC_ID,
    SIM_UICC_PUID_ID,
    SIM_UICC_CC_ID,
    SIM_UICC_UID_ID,
    SIM_UICC_UID1_ID,
    SIM_UICC_EMAIL_ID,
    SIM_UICC_EMAIL1_ID,
    SIM_UICC_GAS_ID,
    SIM_UICC_SNE_ID,
    SIM_UICC_SNE1_ID,
    SIM_UICC_ANRA_ID,
    SIM_UICC_ANRB_ID,
    SIM_UICC_ANRC_ID,
    SIM_UICC_ANRA1_ID,
    SIM_UICC_ANRB1_ID,
    SIM_UICC_ANRC1_ID,

/*GLOBAL PHONEBOOK files*/
    SIM_UICC_GB_EXT1_ID,
    SIM_UICC_GB_PBC_ID,
    SIM_UICC_GB_PBC1_ID,
    SIM_UICC_GB_AAS_ID,
    SIM_UICC_GB_PBR_ID,
    SIM_UICC_GB_ADN_ID,
    SIM_UICC_GB_ADN1_ID,
    SIM_UICC_GB_CCP1_ID,
    SIM_UICC_GB_GRP_ID,
    SIM_UICC_GB_GRP1_ID,
    SIM_UICC_GB_PSC_ID,
    SIM_UICC_GB_PUID_ID,
    SIM_UICC_GB_CC_ID,
    SIM_UICC_GB_UID_ID,
    SIM_UICC_GB_UID1_ID,
    SIM_UICC_GB_EMAIL_ID,
    SIM_UICC_GB_EMAIL1_ID,
    SIM_UICC_GB_GAS_ID,
    SIM_UICC_GB_SNE_ID,
    SIM_UICC_GB_SNE1_ID,
    SIM_UICC_GB_ANRA_ID,
    SIM_UICC_GB_ANRB_ID,
    SIM_UICC_GB_ANRC_ID,
    SIM_UICC_GB_ANRA1_ID,
    SIM_UICC_GB_ANRB1_ID,
    SIM_UICC_GB_ANRC1_ID,
   /*Chengguang modify for unit test, 2008-8-22, begin*/
    SIM_UICC_CBMIR_ID,
    SIM_UICC_AAEM_ID,
    SIM_UICC_EMLPP_ID,
   /*Chengguang modify for unit test, 2008-8-22,end   */
   /*Modification for USIM it test. CQ00001116, Chengguang Liu, June.09 09,Begin*/
    SIM_UICC_CPBCCH_ID,
    SIM_UICC_INVSCAN_ID,
    SIM_UICC_SUME_ID,   
    SIM_UICC_CMI_ID,    
    SIM_UICC_RPLMN_ID,  
    SIM_UICC_MBI_ID,    
    SIM_UICC_MMSN_ID,   
    SIM_UICC_EXT8_ID,   
    SIM_UICC_MMSICP_ID, 
    SIM_UICC_MMSUP_ID,  
    SIM_UICC_MMSUCP_ID, 
   /*Modification for USIM it test. CQ00001116, Chengguang Liu, June.09 09,End  */
#endif 
    /*CQ00135656, Cgliu, 2022-02-25,End  */   

    /*Modification for CQ00003401, cgLiu,Feb.21 2010, begin*/
    SIM_UICC_EHPLMN_ID,
    SIM_UICC_EHPLMNPI_ID,
    /*Modification for CQ00003401, cgLiu,Feb.21 2010, end  */

/*Modfication by zhjzhao for CQ00013425 Merge from LTE_PS_DEV to NeZha_DEV_Daily 10-09-11, Begin*/
 /*Modification of SIM emulator for CQ00004507, cgLiu,Jun.17 2010, begin*/  
 #if defined (UPGRADE_LTE)
    SIM_UICC_EPSNCPIP_ID,
    SIM_UICC_EPSLOCI_ID,
    SIM_UICC_EPSNSC_ID,
 #endif /*end UPGRADE_LTE*/
 /*Modification of SIM emulator for CQ00004507, cgLiu,Jun.17 2010, end  */
 /*Modfication by zhjzhao for CQ00013425 Merge from LTE_PS_DEV to NeZha_DEV_Daily 10-09-11, End*/
 /*CQ00135656, Cgliu, 2022-02-25,Begin*/
#if defined (ON_PC)
/* CSG files under DF_CSG in ADF_USIM */
    SIM_UICC_HNB_ACSGL,
    SIM_UICC_HNB_CSGT,
    SIM_UICC_HNB_HNBN,
    SIM_UICC_HNB_OCSGL,
    SIM_UICC_HNB_OCSGT,
    SIM_UICC_HNB_OHNBN,
#endif 
     /*CQ00135656, Cgliu, 2022-02-25,End  */

    NUM_EMU_EF_FILES
}
SimUiccFileId;

typedef enum SimEmuEfIdTag
{
    SIM_EMU_EF_DIR  = 0x2f00,
    SIM_EMU_EF_ECC  = 0x6fb7,
    SIM_EMU_EF_PL   = 0x2f05,
    SIM_EMU_EF_LI   = 0x6f05,
    SIM_EMU_EF_IMSI  = 0x6f07,
    SIM_EMU_EF_ACC  = 0x6f78,
    SIM_EMU_EF_HPLMN  = 0x6f31,
    SIM_EMU_EF_LOCI  = 0x6f7e,
    SIM_EMU_EF_FPLMN  = 0x6f7b,
    SIM_EMU_EF_CNL   = 0x6f32,
    SIM_EMU_EF_CBMID  = 0x6f48,
    SIM_EMU_EF_DCK  = 0x6f2c,
    SIM_EMU_EF_BDN  = 0x6f4d,
    SIM_EMU_EF_IMG  = 0x4f20,
    SIM_EMU_EF_IMGINST1 = 0x4f01,
    SIM_EMU_EF_IMGINST2 = 0x4f02,
    SIM_EMU_EF_IMGINST3 = 0x4f03,
    SIM_EMU_EF_IMGINST4 = 0x4f04,
    SIM_EMU_EF_IMGINST5 = 0x4f05,
    SIM_EMU_EF_ICCID  = 0x2fe2,
    SIM_EMU_EF_TST = 0x6fd2,
    SIM_EMU_EF_ACT_HPLMN = 0x4f34,
    SIM_EMU_EF_RAT = 0x4f36,
    SIM_EMU_EF_CSP = 0x6f15,
    SIM_EMU_EF_AD  = 0x6fad,
    SIM_EMU_EF_ACM  = 0x6f39,
    SIM_EMU_EF_PUCT  = 0x6f41,
    SIM_EMU_EF_UST  = 0x6f38,
    SIM_EMU_EF_EST  = 0x6f56,
    SIM_EMU_EF_ACM_MAX  = 0x6f37,
    SIM_EMU_EF_FDN  = 0x6f3b,
    SIM_EMU_EF_CBMI  = 0x6f45,
    SIM_EMU_EF_SMSS  = 0x6f43,
    SIM_EMU_EF_SMSR  = 0x6f47,
    SIM_EMU_EF_SMS  = 0x6f3c,
    SIM_EMU_EF_SMSP = 0x6f42,
    SIM_EMU_EF_PBR  = 0x4f30,
    SIM_EMU_EF_ADN  = 0x4f3a,
    SIM_EMU_EF_ADN1 = 0x4f3b,
    SIM_EMU_EF_IAP  = 0x4f97,
    SIM_EMU_EF_IAP1 = 0x4f98,
    SIM_EMU_EF_CCP1 = 0x4f99,
    SIM_EMU_EF_GRP  = 0x4f83,
    SIM_EMU_EF_GRP1 = 0x4f84,
    SIM_EMU_EF_PSC  = 0x4f22,
    SIM_EMU_EF_CC   = 0x4f23,
    SIM_EMU_EF_PUID = 0x4f24,
    SIM_EMU_EF_UID  = 0x4f81,
    SIM_EMU_EF_UID1 = 0x4f82,
    SIM_EMU_EF_EMAIL = 0x4f50,
    SIM_EMU_EF_EMAIL1 = 0x4f51,
    SIM_EMU_EF_GAS = 0x4f4c,
    SIM_EMU_EF_SNE  = 0x4f19,
    SIM_EMU_EF_SNE1  = 0x4f1a,
    SIM_EMU_EF_ANRA = 0x4f11,
    SIM_EMU_EF_ANRB = 0x4f13,
    SIM_EMU_EF_ANRC = 0x4f15,
    SIM_EMU_EF_ANRA1 = 0x4f12,
    SIM_EMU_EF_ANRB1 = 0x4f14,
    SIM_EMU_EF_ANRC1 = 0x4f16,
    SIM_EMU_EF_PBC   = 0x4f09,
    SIM_EMU_EF_PBC1  = 0x4f0A,
    SIM_EMU_EF_SPN = 0x6f46,
    SIM_EMU_EF_OCI  = 0x6f81,
    SIM_EMU_EF_ICI  = 0x6f80,
    SIM_EMU_EF_ICT  = 0x6f82,
    SIM_EMU_EF_OCT  = 0x6f83,
    SIM_EMU_EF_MSISDN  = 0x6f40,
    SIM_EMU_EF_EXT3  = 0x6f4c,
    SIM_EMU_EF_EXT4  = 0x6f55,
    SIM_EMU_EF_EXT5  = 0x6f4e,
    SIM_EMU_EF_EXT6 = 0x6fc8,
    SIM_EMU_EF_CFIS = 0x6fcb,
    SIM_EMU_EF_EXT7 = 0x6fcc,
    SIM_EMU_EF_MBDN = 0x6fc7,
    SIM_EMU_EF_MWIS = 0x6fca,
    SIM_EMU_EF_GID1 = 0x6f3e,
    SIM_EMU_EF_GID2 =0x6f3f,
    SIM_EMU_EF_EXT1 =0x4f4a,
    SIM_EMU_EF_EXT2 =0x6f4b,
    SIM_EMU_EF_SDN = 0x6f49,
    SIM_EMU_EF_KC =0x4f20,
    SIM_EMU_EF_KC_GPRS =0x4f52,
    SIM_EMU_EF_KEYS =0x6f08,
    SIM_EMU_EF_KEYS_PS =0x6f09,
    SIM_EMU_EF_PS_LOCI =0x6f73,
    SIM_EMU_EF_THRESHOLD = 0x6f5c,
    SIM_EMU_EF_NETPAR  = 0x6fc4,
    SIM_EMU_EF_ACL = 0x6f57,
    SIM_EMU_EF_START_HFN = 0x6f5b,
    SIM_EMU_EF_USER_PLMN_SEL = 0x6f30,
    SIM_EMU_EF_OPLMNW_ACT = 0x6f61,
    SIM_EMU_EF_HPLMNW_ACT = 0x6f62,
    SIM_EMU_EF_PLMNW_ACT = 0x6F60,
    SIM_EMU_EF_ARR       = 0x6f06,
    SIM_EMU_EF_AAS       = 0x4f4b,
    SIM_EMU_EF_HIDDEN_KEY = 0x6fc3,
    SIM_EMU_EF_CCP2      = 0x6f4f,
    SIM_EMU_EF_PNN          = 0x6fc5,
    SIM_EMU_EF_OPL        = 0x6fc6,
    SIM_EMU_EF_SPDI       = 0x6fcd,
    /*Chengguang modify for unit test,2008-8-22,begin*/
    SIM_EMU_EF_CBMIR =0x6f50,
    SIM_EMU_EF_AAEM = 0x6fb6,
    SIM_EMU_EF_EMLPP = 0x6fb5,
    /*Chengguang modify for unit test,2008-8-22,end  */
    /*Modification for USIM it test. CQ00001116, Chengguang Liu, June.09 09,Begin*/
    SIM_EUM_EF_CPBCCH     = 0x4f63,
    SIM_EMU_EF_INVSCAN    = 0x4f64,
    SIM_EMU_EF_SUME       = 0x6f54,
    SIM_EMU_EF_CMI        = 0x6f58,
    SIM_EMU_EF_RPLMN      = 0x6F65,
    SIM_EMU_EF_MBI        = 0x6fc9,
    SIM_EMU_EF_MMSN       = 0x6fce,
    SIM_EMU_EF_EXT8       = 0x6fcf,
    SIM_EMU_EF_MMSICP     = 0x6fd0,
    SIM_EMU_EF_MMSUP      = 0x6fd1,
    SIM_EMU_EF_MMSUCP     = 0x6fd2,
    /*Modification for USIM it test. CQ00001116, Chengguang Liu, June.09 09,End  */

    /*Modification for CQ00003401, cgLiu,Feb.21 2010, begin*/
    SIM_EMU_EF_EHPLMN     = 0x6fd9,
    SIM_EMU_EF_EHPLMNPI   = 0x6fdb,
    /*Modification for CQ00003401, cgLiu,Feb.21 2010, end  */
/*Modfication by zhjzhao for CQ00013425 Merge from LTE_PS_DEV to NeZha_DEV_Daily 10-09-11,  Begin*/ 
 #if defined (UPGRADE_LTE)
 /*Added by zhjzhao for CQ00013463 Add a set of default USIM files value for LTE simulation on PC 10-10-11, Begin*/
    SIM_EMU_EF_HPPLMN = 0x6f31,
    SIM_EMU_EF_LRPLMNSI   = 0x6fdc,
    SIM_EMU_EF_NIA = 0x6fd3,
    SIM_EMU_EF_VGCS = 0x6fb1,
    SIM_EMU_EF_VGCSS = 0x6fb2,
    SIM_EMU_EF_VBS = 0x6fb3,
    SIM_EMU_EF_VBSS = 0x6fb4,
    SIM_EMU_EF_VGCSCA = 0x6fd4,
    SIM_EMU_EF_VBSCA = 0x6fd5,
    SIM_EMU_EF_GBABP = 0x6fd6,
    SIM_EMU_EF_MSK = 0x6fd7,
    SIM_EMU_EF_MUK = 0x6fd8,
    SIM_EMU_EF_GBANL = 0x6fd8,
    SIM_EMU_EF_NAFKCA = 0x6fdd,
    SIM_EMU_EF_SPNI = 0x6fde,
    SIM_EMU_EF_PNNI = 0x6fdf,

/*EFs at the DF SoLSA level*/
    SIM_EMU_EF_SAI = 0x4f30,
    SIM_EMU_EF_SLL = 0x4f31,
/*Added by zhjzhao for CQ00013463 Add a set of default USIM files value for LTE simulation on PC 10-10-11, End*/

/*Modification of SIM emulator for CQ00004507, cgLiu,Jun.17 2010, begin*/
 
    SIM_EMU_EF_NCPIP      = 0x6fe2,
    SIM_EMU_EF_EPSLOCI    = 0x6fe3,
    SIM_EMU_EF_EPSNSC     = 0x6fe4,
 #endif /*end UPGRADE_LTE*/
/*Modification of SIM emulator for CQ00004507, cgLiu,Jun.17 2010, end  */
/*Modfication by zhjzhao for CQ00013425 Merge from LTE_PS_DEV to NeZha_DEV_Daily 10-09-11, End*/    

    SIM_EMU_EF_ACSGL    = 0x4f81,
    SIM_EMU_EF_CSGT     = 0x4f82,
    SIM_EMU_EF_HNBN     = 0x4f83,
    SIM_EMU_EF_OCSGL    = 0x4f84,
    SIM_EMU_EF_OCSGT    = 0x4f85,
    SIM_EMU_EF_OHNBN    = 0x4f86,

    SIM_EMU_EF_INVALID = 0

}
SimEmuEfId;


/*-----------------12/11/01 10:34-------------------------------------------------------
 * This is used to define access conditions for a Dir file, see ISO 7816-9 for details
 * -------------------------------------------------------------------------------------*/
typedef struct SimUiccDirCompactFormatTag
{
    SimUiccAccessCondition    deleteSelf;   /* delete self */
    SimUiccAccessCondition    terminate;
    SimUiccAccessCondition    activate;
    SimUiccAccessCondition    deactivate;
    SimUiccAccessCondition    createEf;
    SimUiccAccessCondition    createDf;
    SimUiccAccessCondition    deleteChild;  /* delete child file */
} SimUiccDirCompactFormat;


typedef struct SimMfDfDataTag
{
    SimDirId                dirId;
    SimDirId                parentId;
    Int16                   totalMemAvailable;
    SimFileType             fileType;
    SimUiccState            fileState;
    SimClockStopMode        clockMode;
    Int8                    appMinClockFreq;
    SimVoltageCapab         simVoltageCapab;
    SimUiccDirCompactFormat accessData;              /* Security attributes for Directories */
    Int8                    appPowerConsumption;
    Int8                    refFrequency;
    SimUiccKeyRefValue      applicationPin;                /*PIN2: local PIN*/
    SimUiccKeyRefValue      localPin;                /*PIN2: local PIN*/
    SimUiccPinStatus        localPinStatus;
    SimUiccPinValue         localPinValue;
    SimUiccPinStatus        unblockLocalPinStatus;
    SimUiccPinValue         unblockLocalPinValue;
    SimUiccAid              aid;                     /* Only used for ADF_USIM */
   }
SimMfDfData;

typedef struct SimEfDataTag
{
    SimEmuEfId              fileId;
    SimEfStructure          fileStructure;
    SimDirId                dirId;
    Int16                   numRecords;  /*If the file is transparent, numRecords = 1*/
    Int16                   recordLength; /*If the file is transparent, recordLength = size of file*/
    Int16                   currentRecord;
    Int16                   cyclicRecOne;
    SimUiccEfCompactFormat  accessData;
    SimUiccState            fileState;
    Boolean                 sfiSupported;
    Int8                    sfi;        /*Short File Indentifier: b1-b5*/
    Boolean                 underUsim;  /*Is the file under USIM dir or not...*/
}
SimEfData;

/***************************************************************************
 *  Function Prototypes
 **************************************************************************/
#ifdef NAS_UNIT_TEST
void SimEmuHandleL1siResetReq ( SignalBuffer  *signal, TaskId myTaskId);
#else
void SimEmuHandleL1siResetReq ( SignalBuffer  *signal);
#endif
void SimEmuHandleL1siConfigReq (SignalBuffer  *signal);
/*Modification for SIM test envirement. **********, Chengguang Liu, Dec.07 2010,Begin*/
void SimEmuHandleL1siDataReq (SignalBuffer  *signal,
                                 SignalBuffer  *respSignal,
                                 SimManagerData *simData);
/*Modification for SIM test envirement. **********, Chengguang Liu, Dec.07 2010,End  */
void SimEmuHandleL1siRejectReq ( SignalBuffer  *signal);

/****************************************************
Zhiwei add for TDD IT Test No need USIM card in USIM EMU Start
*****************************************************/

/* Mod by jungle for ********** on 2015-01-26 Begin */
void SimEmuHandleNullCardRemoved (TaskId myTaskId);
/* Mod by jungle for ********** on 2015-01-26 End */

/****************************************************
Zhiwei add for TDD IT Test No need USIM card in USIM EMU end
*****************************************************/
/*Modification by zhjzhao for add SIM send cardRemovedInd to SIM when initilization 08-29-11, Begin */
void SimemuSendCardRemoveInd(TaskId myTaskId);
/*Modification by zhjzhao for add SIM send cardRemovedInd to SIM when initilization 08-29-11,  End*/
/****************************************************
Zhiwei add for TDD IT Test MM auth MAC and SQN failer in USIM EMU Start
*****************************************************/
extern Boolean simAuthMacFailFlag ;
extern Boolean simAuthSqnFailFlag ;
/****************************************************
Zhiwei add for TDD IT Test MM auth MAC and SQN failer in USIM EMU End
*****************************************************/
/*************************************************************
Zhiwei add for TDD IT Test Seting ACC is TYPE A or TYPE B Start
*************************************************************/
extern Boolean simAccTypeAFlag ; 
extern Boolean simAccTypeBFlag ; 

/*************************************************************
Zhiwei add for TDD IT Test Seting ACC is TYPE A or TYPE B End
**************************************************************/
/*Modification for USIM it test. CQ00001116, Chengguang Liu, June.09 09,Begin*/
/*Modification for SIM test envirement. **********, Chengguang Liu, Dec.07 2010,Begin*/
#ifdef ON_PC
/*Modification for SIM test envirement. **********, Chengguang Liu, Dec.07 2010,End  */
void SimemuChgInitialise(L1siChgEmuReq  *reqData);
void SimemuChgInitialisePin(L1siChgEmuReq *reqData); //zhiwei add for SAC check
/*Modification for USIM it test. CQ00006150, Chengguang Liu, Sept.17 2010,Begin*/
void SimEmuForRemovedCard();
/*Modification for USIM it test. CQ00006150, Chengguang Liu, Sept.17 2010,End  */
#endif
/*Modification for USIM it test. CQ00001116, Chengguang Liu, June.09 09,End  */



void SimEmuSetImageFile (void);
void SimEmuSaveImage (void);
void SimEmuCloseImageFile (void);
void SimEmuCloseImageFile (void);
#endif
#endif /* UPGRADE_3G */
#endif /* SIM_EMULATION_ON */
/* END OF FILE */






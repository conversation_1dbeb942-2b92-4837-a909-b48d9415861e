/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/


/******************************************************************************
 * File Name:	umaxhsphy.h
 *
 *
 * File Description: UMAC-ehs and UMAC-hs  - PHY interface header file
 *
 *****************************************************************************/
#if !defined (UMAXHS_PHY_H)
#define UMAXHS_PHY_H


#include <system.h>


#if !defined (PS_L2_R8_API)
/* fixed CQ23391 taoye modify begin 20121019 */
/* fixed CQ43955 taoye modify begin 20130913 */
#define UMA_PHY_HSDCH_IF_NUM_PHY_POINTERS	12//9///3
/* fixed CQ43955 taoye modify begin 20120913 */
/* fixed CQ23391 taoye modify end 20121019 */
#else
#define UMA_PHY_HSDCH_IF_NUM_PHY_POINTERS	24
#endif

///The Physical layer header which is written just before the mac hs/ehs pdu 
typedef struct PhyHsDataIndHeaderTag
{
	/** NOTE: Pointer assigned to PHY, aligned to 32-bit boundary 
	  */
    Int8  		*macHsData_p;
	/** Indicates the current system frame number. */
    Int16		sfn; 
	/** Indicates the 2ms TTI within the \c dlSfn in which data was received.
     	  * It is in the range 0 to 4 
     	  */
    Int8  		subFrameCounter;
	/** Num of bits between MAC-hs/Mac-ehs header and its payload 
         */
    Int8  		byteAlignOffset;
	/** Indicates the length of the buffer eferred to by macHsData_p.
         */
    Int16 		bitLength;
	/* Used in ECF, to indicate weather the reception was BCCH(1) or PCCH(0)
         */
    Int8  		hrntiReceivedInd;
	/* For L1V debug
         */
    Int8   		numOfTransmission;
	/* Indicates to which hs entity the data is designated: MAC-HS or MAC-EHS*/
	Int8		macType;
	/* Reserved for future use, also alignment to 32 bits*/
	Int8		reserved[3]; 
}PhyHsDataIndHeader;

#if defined (PS_L2_R8_API)

#if defined (UMAC_UT_TEST) || defined (UMACE_UNIT_TEST) || defined (W_L2_UNIT_TEST)


typedef struct
{
	//The address of each Mac-hs/ehs PDU 
	UINT32			dataBlockStartAddr;
	//the bit length of Mac-hs or Mac-ehs PDU size 
	UINT16			bitlength;
	//Used for Umahs to align Mac-d data part to 8bits, Always set to 0 in Mac-ehs, Range 0..7 
	UINT8 			byteAlignOffset;
	// Flag to indicate this PDU is PCH or BCH as LogCH Id equal to 15, 1: BCH, 0 PCH 
	UINT8 			hrntiReceivedInd;
	//transmission numbers
	UINT8 			numOfTransmission;
	//TB received from primary cell or secondary cell: 0: primary cell, 1: secondary cell.
	UINT8			receivedFromCell;
	
	UINT16			reserved;
}HsTbInfo_ts;

#define MAX_HSDPA_DATA_BLOCK_NUM		2

typedef struct
{
	//index of the subframe or frame whose data is ready for reading 
    UINT16          sfn;
	//subframe sequence number
	UINT8           subframe;
	// Indicate if this PDU is Mac-hs or Mac-ehs type,0 : Mac-hs, 1: Mac-ehs 
  	UINT8           macType;
	
	UINT8			reserved[3];
	//max 2 block per TTI 
   	UINT8           dataBlockNum;

    HsTbInfo_ts     HsTbInfo[MAX_HSDPA_DATA_BLOCK_NUM];
} plHsDataIndHeader_ts;

#endif
#ifdef UPGRADE_DSDSWB_L2
void UmaPhyHsdschIfDataIndCallback(plHsDataIndHeader_ts *phyDpaDataRxInd_p,Int8 simId);
#else
void UmaPhyHsdschIfDataIndCallback(plHsDataIndHeader_ts *phyDpaDataRxInd_p);
#endif
void UmaPhyHsdschIfAssignPointers(Int8 numBuffers, Int8 **data_pp);
void UmaPhyHsdschIfHandlePhyHsDataInd(SignalBuffer * rxSignal_p);
#else
/* fixed CQ23391 taoye modify begin 20121019 */
void UmaPhyHsdschIfAssignPointer(Int8 *data_p, Int8 sendtoDSP);
/* fixed CQ23391 taoye modify end 20121019 */
#ifdef UPGRADE_DSDSWB_L2
void UmaPhyHsdschIfDataIndCallback(Int8 *phyHsData_p,Int8 simId);
#else
void UmaPhyHsdschIfDataIndCallback(Int8 *phyHsData_p);
#endif
#endif
void UmaPhyHsdschIfHandlePhyHsDataInd(SignalBuffer * rxSignal_p);

#endif /* UMAXHS_PHY_H */

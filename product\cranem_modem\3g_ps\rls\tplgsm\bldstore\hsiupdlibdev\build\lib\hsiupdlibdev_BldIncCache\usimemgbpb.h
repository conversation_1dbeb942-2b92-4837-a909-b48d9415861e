/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/3g.mod/lib/src/usimemgbpb.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/26 20:17:00 $
 **************************************************************************
 *  File Description :
 *
 *  File definition for the UICC emulator (global phonebook)
 **************************************************************************/

#if !defined (USIMEMGBPB_H)
#define USIMEMGBPB_H

/***************************************************************************
 * Nested Include Files
 **************************************************************************/

#if !defined (ALSI_SIG_H)
#include "alsi_sig.h"
#endif

#if !defined (SIMDATA_H)
#include "simdata.h"
#endif

#if !defined (USIMEMU_H)
#include "usimemu.h"
#endif

/***************************************************************************
 * Manifest Constants
 **************************************************************************/
/*-----------------21/07/2003 14:20-----------------
 * global phonebook
 * Note: only type 1 file linking is used in this
 * global phonebook.
 * --------------------------------------------------*/
/*type 1 files have same number of records as ADN*/
/*-----------------22/07/2003 11:22-----------------
 * Number of records
 * --------------------------------------------------*/
#define SIM_EMU_NUM_PBR_RECS      2
#define SIM_EMU_NUM_ADN_RECS      10
#define SIM_EMU_NUM_ADN1_RECS     10
#define SIM_EMU_NUM_CCP1_RECS     4
#define SIM_EMU_NUM_ANRA_RECS     SIM_EMU_NUM_ADN_RECS
#define SIM_EMU_NUM_ANRB_RECS     SIM_EMU_NUM_ADN_RECS
#define SIM_EMU_NUM_ANRC_RECS     SIM_EMU_NUM_ADN_RECS
#define SIM_EMU_NUM_SNE_RECS      SIM_EMU_NUM_ADN_RECS
#define SIM_EMU_NUM_EMAIL_RECS    SIM_EMU_NUM_ADN_RECS
#define SIM_EMU_NUM_UID_RECS      SIM_EMU_NUM_ADN_RECS
#define SIM_EMU_NUM_GRP_RECS      SIM_EMU_NUM_ADN_RECS
#define SIM_EMU_NUM_PBC_RECS      SIM_EMU_NUM_ADN_RECS
#define SIM_EMU_NUM_SNE1_RECS     SIM_EMU_NUM_ADN1_RECS
#define SIM_EMU_NUM_ANRA1_RECS    SIM_EMU_NUM_ADN1_RECS
#define SIM_EMU_NUM_ANRB1_RECS    SIM_EMU_NUM_ADN1_RECS
#define SIM_EMU_NUM_ANRC1_RECS    SIM_EMU_NUM_ADN1_RECS
#define SIM_EMU_NUM_EMAIL1_RECS   SIM_EMU_NUM_ADN1_RECS
#define SIM_EMU_NUM_UID1_RECS     SIM_EMU_NUM_ADN1_RECS
#define SIM_EMU_NUM_GRP1_RECS     SIM_EMU_NUM_ADN1_RECS
#define SIM_EMU_NUM_PBC1_RECS     SIM_EMU_NUM_ADN1_RECS
#define SIM_EMU_NUM_AAS_RECS          5
#define SIM_EMU_NUM_GAS_RECS          14
#define SIM_EMU_NUM_EXT1_RECS    12

/*-----------------22/07/2003 11:22-----------------
 * Size of records/file
 * --------------------------------------------------*/
#define SIM_EMU_SIZE_UID_FILE         2
#define SIM_EMU_SIZE_UID1_FILE        2
#define SIM_EMU_SIZE_CC_FILE          2
#define SIM_EMU_SIZE_PUID_FILE        2
#define SIM_EMU_SIZE_PSC_FILE         4
#define SIM_EMU_SIZE_ADN_FILE         46
#define SIM_EMU_SIZE_ADN1_FILE        46
#define SIM_EMU_SIZE_ANRA_FILE        15
#define SIM_EMU_SIZE_ANRB_FILE        15
#define SIM_EMU_SIZE_ANRC_FILE        15
#define SIM_EMU_SIZE_ANRA1_FILE       15
#define SIM_EMU_SIZE_ANRB1_FILE       15
#define SIM_EMU_SIZE_ANRC1_FILE       15
#define SIM_EMU_SIZE_EMAIL_FILE       15
#define SIM_EMU_SIZE_EMAIL1_FILE      15
#define SIM_EMU_SIZE_SNE_FILE         10
#define SIM_EMU_SIZE_SNE1_FILE        10
#define SIM_EMU_SIZE_PBC_FILE         2
#define SIM_EMU_SIZE_PBC1_FILE        2
#define SIM_EMU_SIZE_CCP1_FILE        14
#define SIM_EMU_SIZE_PBR_FILE         54
#define SIM_EMU_SIZE_AAS_FILE         12
#define SIM_EMU_SIZE_GRP_FILE         3
#define SIM_EMU_SIZE_GRP1_FILE        3
#define SIM_EMU_SIZE_GAS_FILE         15
#define SIM_EMU_SIZE_EXT1_FILE   13


/***************************************************************************
 * Typed Constants
 **************************************************************************/

 /*CQ00135656, Cgliu, 2022-02-25,Begin*/
#if defined (ON_PC)
 /*-----------------27/03/02 09:28-------------------------------------------
  * EF PBR  (Phone book reference) example copied from the 131 102 spec (Annex G)
  * Note: some file ID have been modified.
  * -------------------------------------------------------------------------*/
Int8    defPbrData[] = {
                             /*record number 1*/
                             0xa8, 0x26,                    /*type 1 files*/
                             0xc0, 0x03, 0x4f, 0x3a, 0x01,  /*ADN*/
                             0xc5, 0x03, 0x4f, 0x09, 0x02,  /*PBC*/
                             0xc6, 0x02, 0x4f, 0x83,        /*GRP*/
                             0xc4, 0x02, 0x4f, 0x11,        /*ANR*/
                             0xc4, 0x02, 0x4f, 0x13,        /*ANR*/
                             0xc4, 0x02, 0x4f, 0x15,        /*ANR*/
                             0xc3, 0x02, 0x4f, 0x19,        /*SNE*/
                             0xc9, 0x02, 0x4f, 0x81,        /*UID*/
                             0xca, 0x02, 0x4f, 0x50,        /*EMAIL*/
                             0xaa, 0x0c,                    /*type 3 files */
                             0xc2, 0x02, 0x4f, 0x4a,        /*EXT1*/
                             0xc7, 0x02, 0x4f, 0x4b,        /*AAS*/
                             0xc8, 0x02, 0x4f, 0x4c,        /*GAS*/

                             /*record number 2*/
                             0xa8, 0x24,
                             0xc0, 0x02, 0x4f, 0x3b,        /*ADN*/
                             0xc5, 0x02, 0x4f, 0x0a,        /*PBC*/
                             0xc6, 0x02, 0x4f, 0x84,        /*GRP*/
                             0xc4, 0x02, 0x4f, 0x12,        /*ANR*/
                             0xc4, 0x02, 0x4f, 0x14,        /*ANR*/
                             0xc4, 0x02, 0x4f, 0x16,        /*ANR*/
                             0xc3, 0x02, 0x4f, 0x1a,        /*SNE*/
                             0xc9, 0x02, 0x4f, 0x82,        /*UID*/
                             0xca, 0x02, 0x4f, 0x51,        /*EMAIL*/
                             0xaa, 0x0c,
                             0xc2, 0x02, 0x4f, 0x4a,
                             0xc7, 0x02, 0x4f, 0x4b,
                             0xc8, 0x02, 0x4f, 0x4c,        /*GAS*/
                             0xff, 0xff

                             };

SimEfData    defPbrEfData =
{
    SIM_EMU_EF_PBR,                 /* Id           */
    SIM_EFSTRUCT_LF,              /* Type         */
#if defined (SIM_UICC_NO_GLOBAL_PHONEBOOK)
    0,
#else
    SIM_DIR_DF_PHONEBOOK,        /* Dir          */
#endif
    SIM_EMU_NUM_PBR_RECS,   /* Num Recs     */
    SIM_EMU_SIZE_PBR_FILE,  /* Rec Len     */
    (0),                         /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_ADM,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi */
    FALSE                      /*Under USIM */
};

/*-----------------05/04/02 16:13-------------------
 * EF PBC: phonebook control   (file listed in PBR record 1)
 * see 131 102 section 4.4.2.5 for coding
 * --------------------------------------------------*/
Int8    defPbcData[] = {     0x00, 0x01,     /*first byte: entry control info, second byte: hidden info*/
                             0x00, 0x00,
                             0x00, 0x00,
                             0x00, 0x00,
                             0x00, 0x01,
                             0x00, 0x00,
                             0x00, 0x00,
                             0x00, 0x00,
                             0x00, 0x00,
                             0x00, 0x00   };

SimEfData    defPbcEfData =
{

    SIM_EMU_EF_PBC,            /* Id           */
    SIM_EFSTRUCT_LF,        /* Type         */
    SIM_DIR_DF_PHONEBOOK,     /* Dir          */
    SIM_EMU_NUM_PBC_RECS, /* Num Rec      */
    SIM_EMU_SIZE_PBC_FILE, /*  Rec  len   */
  (0),                    /* curr Rec     */
  (0),
  {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
   },
   SIM_UICC_ACTIVATED_STATE, /*file State */
   FALSE,                    /*sfi supported  */
   0,                        /*sfi */
   FALSE                      /*Under USIM */
};

/*-----------------05/04/02 16:13-----------------------------------
 * EF PBC 1: phonebook control   (file listed in PBR record 2)
 * -----------------------------------------------------------------*/
Int8    defPbc1Data[] = {    0x00, 0x03,
                             0x00, 0x00,
                             0x00, 0x00,
                             0x00, 0x00,
                             0x00, 0x03,
                             0x00, 0x00,
                             0x00, 0x00,
                             0x00, 0x00,
                             0x00, 0x00,
                             0x00, 0x00,
                             0x00, 0x00   };

SimEfData    defPbc1EfData =
{

    SIM_EMU_EF_PBC1,            /* Id           */
    SIM_EFSTRUCT_LF,        /* Type         */
    SIM_DIR_DF_PHONEBOOK,     /* Dir          */
    SIM_EMU_NUM_PBC1_RECS, /* Num Rec      */
    SIM_EMU_SIZE_PBC1_FILE, /*  Rec  len   */
  (0),                    /* curr Rec     */
  (0),
  {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
   },
   SIM_UICC_ACTIVATED_STATE, /*file State */
   FALSE,                    /*sfi supported  */
   0,                        /*sfi */
   FALSE                      /*Under USIM */
};


/*-----------------05/04/02 16:13-------------------
 * EF EXT1: used for ADN and ANR (additional numbers)
 * --------------------------------------------------*/
Int8    defExt1Data[] = {
/*1*/   0x02,
        0x16, 0x21, 0x43, 0x65, 0x87, 0x09, 0x21, 0x43, 0x65, 0x87, 0x09,
        0x2,

/*2*/   0x02,
        0x0c, 0x21, 0x43, 0x65, 0x87, 0x09, 0x21, 0x43, 0x65, 0x87, 0x09,
        0x04,

/*3*/   0x02,
        0x12, 0x21, 0x43, 0x65, 0x87, 0x09, 0x21, 0x43, 0x65, 0x87, 0x09,
        0x05,

/*4*/   0x02,
        0x02, 0x11, 0x22, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0x06,

/*5*/   0x02,
        0x08, 0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0xff, 0xff,
        0x07,

/*6*/   0x01,
        0x14, 0xa0, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0x00, 0x11,
        0x08,

/*7*/   0x01,
        0x14, 0xa0, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0x00, 0x11, 0x22,
        0x09,

/*8*/   0x01,
        0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0x00, 0x11,
        0xff,

/*9*/   0x01,
        0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0x00, 0x11, 0x22,
        0xff,

/*a*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*b*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*c*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff
};

SimEfData    defExt1EfData =
{

    SIM_EMU_EF_EXT1,            /* Id           */
    SIM_EFSTRUCT_LF,        /* Type         */
    SIM_DIR_DF_PHONEBOOK,     /* Dir          */
    SIM_EMU_NUM_EXT1_RECS, /* Num Rec      */
    SIM_EMU_SIZE_EXT1_FILE, /*  Rec  len   */
  (0),                    /* curr Rec     */
  (0),
  {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
   },
   SIM_UICC_ACTIVATED_STATE, /*file State */
   TRUE,                     /*sfi supported  */
   0x4a,                     /*sfi */
   FALSE                      /*Under USIM */
};


/*-----------------05/04/02 16:13-------------------
 * EF CCP1 (capability configuration parameters)
 * 131 102 section 4.4.2.11
 * --------------------------------------------------*/
Int8    defCcp1Data[] = {
    8, 0x83, 0x84, 0x85, 0x06, 0x07, 0x08, 0x89, 0x80, 0xff, 0xff, 0xff, 0xff, 0xff,
    8, 0x82, 0x82, 0x83, 0x03, 0x04, 0x04, 0x85, 0x85, 0xff, 0xff, 0xff, 0xff, 0xff,
    8, 0x87, 0x87, 0x88, 0x08, 0x09, 0x09, 0x80, 0x80, 0xff, 0xff, 0xff, 0xff, 0xff,
    3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff };

SimEfData    defCcp1EfData =
{

    SIM_EMU_EF_CCP1,            /* Id           */
    SIM_EFSTRUCT_LF,        /* Type         */
    SIM_DIR_DF_PHONEBOOK,     /* Dir          */
    SIM_EMU_NUM_CCP1_RECS, /* Num Rec      */
    SIM_EMU_SIZE_CCP1_FILE, /*  Rec  len   */
  (0),                    /* curr Rec     */
  (0),
  {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
   },
   SIM_UICC_ACTIVATED_STATE, /*file State */
   TRUE,                     /*sfi supported  */
   0x4a,                     /*sfi */
   FALSE                      /*Under USIM */
};


/*-----------------27/03/02 09:25-------------------
 * EF ADN Abreviated dial numbers
 *
 * --------------------------------------------------*/

Int8    defAdnData[] =
{
#if defined (SIM_EMU_EMPTY_GB_ADN1)
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
#else
        'G' , 'L', 'O', 'B', 'A', 'L', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0x03,0x81,0x21,0xf3,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
#endif
#if defined (SIM_EMU_EMPTY_GB_ADN2)
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
#else
        'H' , 'A', 'M', 'L', 'E', 'T',0xff, 0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,0xa8,0x07,0x63,0x26,
        0x26,0x26,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
#endif
#if defined (SIM_EMU_EMPTY_GB_ADN3)
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
#else
        'R' , 'O', 'M', 'E', 'O', 0xff, 0xff, 0xff, 0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x08,0x98,0x01,0x01,0x20,
        0x44,0x88,0x83,0x36,0xff,0xff,0xff,0xff,0xff,
#endif
#if defined (SIM_EMU_EMPTY_GB_ADN4)
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
#else
        'J' , 'U', 'L', 'I', 'E', 'T', 0xff, 0xff, 0xff, 0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x08,0x98,0x01,0x01,0x20,
        0x44,0x88,0x83,0x36,0xff,0xff,0xff,0xff,0xff,
#endif
#if defined (SIM_EMU_EMPTY_GB_ADN5)
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
#else
        'M' , 'A', 'C', 'B', 'E', 'A', 'T', 'H', 0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x08,0x98,0x01,0x01,0x20,
        0x44,0x88,0x83,0x36,0xff,0xff,0xff,0xff,0xff,
#endif

#if defined (SIM_EMU_EMPTY_GB_ADN6)
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
#else
        'C' , 'A', 'I', 'U', 'S', 0xff, 0xff, 0xff, 0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x08,0x98,0x01,0x01,0x20,
        0x44,0x88,0x83,0x36,0xff,0xff,0xff,0xff,0xff,
#endif
#if defined (SIM_EMU_EMPTY_GB_ADN7)
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
#else
        'S' , 'H', 'A', 'K', 'E', 'S', 'P', 'E', 'A', 'R','E',0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x08,0x98,0x01,0x01,0x20,
        0x44,0x88,0x83,0x36,0xff,0xff,0xff,0xff,0xff,
#endif

#if defined (SIM_EMU_EMPTY_GB_ADN8)
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
#else
        'H' , 'E', 'N', 'R', 'Y', 0xff, 0xff, 0xff, 0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x08,0x98,0x01,0x01,0x20,
        0x44,0x88,0x83,0x36,0xff,0xff,0xff,0xff,0xff,
#endif

#if defined (SIM_EMU_EMPTY_GB_ADN9)
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
#else
         'O' , 'T', 'H', 'E', 'L', 'L', 'O', 0xff, 0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x08,0x98,0x01,0x01,0x20,
        0x44,0x88,0x83,0x36,0xff,0xff,0xff,0xff,0xff,
#endif
#if defined (SIM_EMU_EMPTY_GB_ADN10)
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff
#else
        'C' , 'A', 'L', 'P', 'U', 'R', 'N', 'I', 'A',0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x08,0x98,0x01,0x01,0x20,
        0x44,0x88,0x83,0x36,0xff,0xff,0xff,0xff,0xff
#endif

};

SimEfData    defAdnEfData =
{
    SIM_EMU_EF_ADN,           /* Id           */
    SIM_EFSTRUCT_LF,          /* Type         */
    SIM_DIR_DF_PHONEBOOK,     /* Dir          */
    SIM_EMU_NUM_ADN_RECS,     /* num Rec      */
    SIM_EMU_SIZE_ADN_FILE,    /* Recs  len   */
    (0),                      /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    TRUE,                     /*sfi supported  */
    0x01,                     /*sfi */
    FALSE                      /*Under USIM */
};

/*------------------------------------------------------
 *
 * ADN1 Abreviated dial numbers   (11 records)
 *
 * --------------------------------------------------*/

Int8    defAdn1Data[] =
{

#if defined (SIM_EMU_EMPTY_GB_ADN11)
        0xff , 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
#else
        'R' , 'O', 'S', 'A', 'L', 'I', 'N', 'D', 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0x04,0x81,0x21,0x43,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
#endif

#if defined (SIM_EMU_EMPTY_GB_ADN12)
        0xff , 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
#else
         'A' , 'G', 'R', 'I', 'P', 'P', 'A', 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0x04,0x81,0x21,0x43,0xf5,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
#endif
#if defined (SIM_EMU_EMPTY_GB_ADN13)
        0xff , 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
#else
         'A' , 'L', 'I', 'C', 'E', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0x04,0x81,0x21,0x43,0x65,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
#endif

#if defined (SIM_EMU_EMPTY_GB_ADN14)
        0xff , 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
#else
         'H' , 'E', 'L', 'E', 'N', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0x03,0x81,0x21,0xf3,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
#endif
#if defined (SIM_EMU_EMPTY_GB_ADN15)
        0xff , 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
#else
         'K' , 'I', 'N', 'G', ' ', 'L', 'E', 'A', 'R', 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0x03,0x81,0x21,0x43,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
#endif
#if defined (SIM_EMU_EMPTY_GB_ADN16)
        0xff , 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
#else
        'F' , 'A', 'B', 'I', 'A', 'N', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0x03,0x81,0x21,0xf3,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
#endif
#if defined (SIM_EMU_EMPTY_GB_ADN17)
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
#else
        'F' , 'L', 'A', 'V', 'I', 'U', 'S', 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0x03,0x81,0x21,0xf3,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
#endif

#if defined (SIM_EMU_EMPTY_GB_ADN18)
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
#else
       'R' , 'O', 'B', 'I', 'N', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0x03,0x81,0x21,0xf3,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
#endif

#if defined (SIM_EMU_EMPTY_GB_ADN19)
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
#else
        'R' , 'O', 'B', 'E', 'R', 'T', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0x03,0x81,0x21,0xf3,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
#endif

#if defined (SIM_EMU_EMPTY_GB_ADN20)
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff
#else
        'R' , 'O', 'M', 'E', 'O', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0x03,0x81,0x21,0xf3,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff
#endif

};

SimEfData    defAdn1EfData =
{
    SIM_EMU_EF_ADN1,            /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_ADN1_RECS,  /* num Rec      */
    SIM_EMU_SIZE_ADN1_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi */
    FALSE                      /*Under USIM */
};



/*-----------------27/03/02 09:25-------------------
 * EF SNE second name entry
 * --------------------------------------------------*/

Int8    defSneData[] =
{
        'S' , 'N', 'E', '1', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        'S' , 'N', 'E', '2', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        'S' , 'N', 'E', '3', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        'S' , 'N', 'E', '4', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        'S' , 'N', 'E', '5', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        'S' , 'N', 'E', '6', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        'S' , 'N', 'E', '7', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        'S' , 'N', 'E', '8', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        'S' , 'N', 'E', '9', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        'S' , 'N', 'E', '1', '0', 0xff, 0xff, 0xff, 0xff, 0xff,

};


SimEfData    defSneEfData =
{
    SIM_EMU_EF_SNE,             /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_SNE_RECS,  /* num Rec      */
    SIM_EMU_SIZE_SNE_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                     /*sfi supported  */
    0x00,                     /*sfi */
    FALSE                      /*Under USIM */
};


/*-----------------27/03/02 09:25---------------------------
 * SNE1 second name entry  (listed in PBR record number 2)
 * ---------------------------------------------------------*/

Int8    defSne1Data[] =
{
        'S' , 'N', 'E', '1', '1', 0xff, 0xff, 0xff, 0xff, 0xff,
        'S' , 'N', 'E', '1', '2', 0xff, 0xff, 0xff, 0xff, 0xff,
        'S' , 'N', 'E', '1', '3', 0xff, 0xff, 0xff, 0xff, 0xff,
        'S' , 'N', 'E', '1', '4', 0xff, 0xff, 0xff, 0xff, 0xff,
        'S' , 'N', 'E', '1', '5', 0xff, 0xff, 0xff, 0xff, 0xff,
        'S' , 'N', 'E', '1', '6', 0xff, 0xff, 0xff, 0xff, 0xff,
        'S' , 'N', 'E', '1', '7', 0xff, 0xff, 0xff, 0xff, 0xff,
        'S' , 'N', 'E', '1', '8', 0xff, 0xff, 0xff, 0xff, 0xff,
        'S' , 'N', 'E', '1', '9', 0xff, 0xff, 0xff, 0xff, 0xff,
        'S' , 'N', 'E', '2', '0', 0xff, 0xff, 0xff, 0xff, 0xff

};

SimEfData    defSne1EfData =
{
    SIM_EMU_EF_SNE1,             /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_SNE1_RECS,  /* num Rec      */
    SIM_EMU_SIZE_SNE1_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi */
    FALSE                      /*Under USIM */
};

/*-----------------18/07/2003 09:37-----------------
 *
 * EF AAS: Additional Alpha String
 *
 * --------------------------------------------------*/

Int8    defAasData[] =
{
        'W' , 'O', 'R', 'K', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
         0xff, 0xff,

        'H' , 'O', 'M', 'E', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
         0xff, 0xff,

        'M' , 'O', 'B', 'I', 'L', 'E', 0xff, 0xff, 0xff, 0xff,
         0xff, 0xff,

         0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
         0xff, 0xff,

         0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
         0xff, 0xff
};

SimEfData    defAasEfData =
{
    SIM_EMU_EF_AAS,             /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_AAS_RECS,  /* num Rec      */
    SIM_EMU_SIZE_AAS_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                     /*sfi supported  */
    0x00,                     /*sfi */
    FALSE                      /*Under USIM */
};


/*-----------------27/03/02 09:25-------------------
 * EF GRP (grouping file)
 *  section 4.4.2.6
 * --------------------------------------------------*/

Int8    defGrpData[] =
{
        0x01, 0x03, 0x0a,   /* belongs to group 1 and 3*/
        0x02, 0x01, 0x03,   /* belongs to group 2, 1 and 3*/
        0x03, 0x00, 0x00,
        0x04, 0x01, 0x03,
        0x05, 0x02, 0x03,
        0x06, 0x00, 0x00,
        0x0a, 0x00, 0x00,
        0x0c, 0x00, 0x00,
        0x0a, 0x00, 0x00,
        0x0a, 0x02, 0x00
};

SimEfData    defGrpEfData =
{
    SIM_EMU_EF_GRP,             /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_GRP_RECS,  /* num Rec      */
    SIM_EMU_SIZE_GRP_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                     /*sfi supported  */
    0x00,                     /*sfi */
    FALSE                      /*Under USIM */
};

/*-----------------27/03/02 09:25-------------------
 * EF GRP1 Grouping info (for PBR record number 2)
 * --------------------------------------------------*/

Int8    defGrp1Data[] =
{
        0x01, 0x03, 0x00,
        0x02, 0x01, 0x03,
        0x03, 0x00, 0x00,
        0x04, 0x01, 0x03,
        0x05, 0x0a, 0x03,
        0x06, 0x00, 0x00,
        0x05, 0x00, 0x00,
        0x04, 0x00, 0x00,
        0x03, 0x00, 0x00,
        0x02, 0x02, 0x00

};

SimEfData    defGrp1EfData =
{
    SIM_EMU_EF_GRP1,             /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_GRP1_RECS, /* num Rec      */
    SIM_EMU_SIZE_GRP1_FILE,/* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                     /*sfi supported  */
    0x00,                     /*sfi */
    FALSE                      /*Under USIM */
};



/*-----------------17/06/2003 10:54-----------------
 * EMAIL (type 1)
 * --------------------------------------------------*/

Int8    defEmailData[] =
{
        'G' , 'L', 'O', '@', 'T', 'T', 'P', 'C', 'O', 'M', '.', 'C', 'O', 'M', 0xff,
        'H' , 'A', 'M', '@', 'T', 'T', 'P', 'C', 'O', 'M', '.', 'C', 'O', 'M', 0xff,
        'R' , 'O', 'M', '@', 'T', 'T', 'P', 'C', 'O', 'M', '.', 'C', 'O', 'M', 0xff,
        'J' , 'U', 'L', '@', 'T', 'T', 'P', 'C', 'O', 'M', '.', 'C', 'O', 'M', 0xff,
        'M' , 'A', 'C', '@', 'T', 'T', 'P', 'C', 'O', 'M', '.', 'C', 'O', 'M', 0xff,
        'C' , 'A', 'I', '@', 'T', 'T', 'P', 'C', 'O', 'M', '.', 'C', 'O', 'M', 0xff,
        'S' , 'H', 'A', '@', 'T', 'T', 'P', 'C', 'O', 'M', '.', 'C', 'O', 'M', 0xff,
        'H' , 'E', 'N', '@', 'T', 'T', 'P', 'C', 'O', 'M', '.', 'C', 'O', 'M', 0xff,
        'O' , 'T', 'H', '@', 'T', 'T', 'P', 'C', 'O', 'M', '.', 'C', 'O', 'M', 0xff,
        'C' , 'A', 'L', '@', 'T', 'T', 'P', 'C', 'O', 'M', '.', 'C', 'O', 'M', 0xff

};
SimEfData    defEmailEfData =
{
    SIM_EMU_EF_EMAIL,             /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_EMAIL_RECS,  /* num Rec      */
    SIM_EMU_SIZE_EMAIL_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                     /*sfi supported  */
    0x00,                     /*sfi */
    FALSE                      /*Under USIM */
};



/*-----------------17/06/2003 10:54-----------------
 * EMAIL1 (type 1)
 * --------------------------------------------------*/
Int8    defEmail1Data[] =
{
        'R' , 'O', 'S', '@', 'T', 'T', 'P', 'C', 'O', 'M', '.', 'C', 'O', 'M', 0xff,
        'A' , 'G', 'R', '@', 'T', 'T', 'P', 'C', 'O', 'M', '.', 'C', 'O', 'M', 0xff,
        'A' , 'L', 'I', '@', 'T', 'T', 'P', 'C', 'O', 'M', '.', 'C', 'O', 'M', 0xff,
        'H' , 'E', 'L', '@', 'T', 'T', 'P', 'C', 'O', 'M', '.', 'C', 'O', 'M', 0xff,
        'K' , 'I', 'N', '@', 'T', 'T', 'P', 'C', 'O', 'M', '.', 'C', 'O', 'M', 0xff,
        'F' , 'A', 'B', '@', 'T', 'T', 'P', 'C', 'O', 'M', '.', 'C', 'O', 'M', 0xff,
        'F' , 'L', 'A', '@', 'T', 'T', 'P', 'C', 'O', 'M', '.', 'C', 'O', 'M', 0xff,
        'R' , 'O', 'B', '@', 'T', 'T', 'P', 'C', 'O', 'M', '.', 'C', 'O', 'M', 0xff,
        'R' , 'O', 'B', '@', 'T', 'T', 'P', 'C', 'O', 'M', '.', 'C', 'O', 'M', 0xff,
        'R' , 'O', 'M', '@', 'T', 'T', 'P', 'C', 'O', 'M', '.', 'C', 'O', 'M', 0xff

};

SimEfData    defEmail1EfData =
{
    SIM_EMU_EF_EMAIL1,             /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_EMAIL1_RECS,  /* num Rec      */
    SIM_EMU_SIZE_EMAIL1_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                     /*sfi supported  */
    0x00,                     /*sfi */
    FALSE                      /*Under USIM */
};


/*-----------------17/06/2003 10:54-------------------------------
 * GAS (type 3 file) Lists the different existing group names
 * --------------------------------------------------------------*/
Int8    defGasData[] =
{
        'G' , 'R', 'O', 'U', 'P', ' ', 'N', 'U', 'M', 'B', 'E', 'R', '1', 0xff, 0xff,
        'G' , 'R', 'O', 'U', 'P', ' ', 'N', 'U', 'M', 'B', 'E', 'R', '2', 0xff, 0xff,
        'C' , 'L', 'I', 'E', 'N', 'T', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        'F' , 'R', 'I', 'E', 'N', 'D','S', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        'F' , 'A', 'M', 'I', 'L', 'Y', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        'B' , 'U', 'S', 'I', 'N', 'E', 'S', 'S', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
         0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
         0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
         0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        'C' , 'O', 'L', 'L', 'E', 'A', 'G', 'U', 'E', 'S', 0xff, 0xff, 0xff, 0xff, 0xff,
         0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        'F' , 'A', 'M', 'I', 'L', 'L', 'E', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
         0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
         0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff

};

SimEfData    defGasEfData =
{
    SIM_EMU_EF_GAS,             /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_GAS_RECS,  /* num Rec      */
    SIM_EMU_SIZE_GAS_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                     /*sfi supported  */
    0x00,                     /*sfi */
    FALSE                      /*Under USIM */
};

/*-----------------17/06/2003 10:54-----------------
 * EF UID used to uniquely identify each ADN record
 * --------------------------------------------------*/
Int8    defUidData[] =
{
      0x01, 0x02,
      0x01, 0x56,
      0x01, 0x58,
      0x01, 0x63,
      0x01, 0x31,
      0x01, 0x35,
      0x01, 0x30,
      0x01, 0x79,
      0x01, 0x90,
      0x01, 0x15

};

SimEfData    defUidEfData =
{
    SIM_EMU_EF_UID,             /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_UID_RECS,  /* num Rec      */
    SIM_EMU_SIZE_UID_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                     /*sfi supported  */
    0x00,                     /*sfi */
    FALSE                      /*Under USIM */
};

/*-----------------17/06/2003 10:54-----------------
 * UID1  (this belongs to PBR record number 2)
 * --------------------------------------------------*/
Int8    defUid1Data[] =
{
      0x01, 0x01,
      0x01, 0x99,
      0x01, 0x23,
      0x01, 0x67,
      0x01, 0x34,
      0x01, 0x36,
      0x01, 0x32,
      0x01, 0x78,
      0x01, 0x98,
      0x01, 0x21

};

SimEfData    defUid1EfData =
{
    SIM_EMU_EF_UID1,             /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_UID1_RECS,  /* num Rec      */
    SIM_EMU_SIZE_UID1_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                     /*sfi supported  */
    0x00,                     /*sfi */
    FALSE                      /*Under USIM */
};



/*-----------------17/06/2003 10:54-----------------
 * EF PSC: phonebook synchronization counter
 * see section 4.4.2.12.2 in TS 131 102
 * --------------------------------------------------*/
Int8    defPscData[] =
{
      0x00, 0x12, 0x34, 0x56

};

SimEfData    defPscEfData =
{
    SIM_EMU_EF_PSC,             /* Id           */
    SIM_EFSTRUCT_T,            /* Type         */
#if defined (SIM_UICC_NO_GLOBAL_PHONEBOOK)
    0,
#else
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
#endif
    1 ,                         /* num Rec      */
    SIM_EMU_SIZE_PSC_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                     /*sfi supported  */
    0x00,                     /*sfi */
    FALSE                      /*Under USIM */
};

/*-----------------17/06/2003 10:54-----------------
 * EF CC (Change Counter)
 * see TS 131 102 section 4.4.2.12.3
 *--------------------------------------------------*/
Int8    defCcData[] =
{
      0x00, 0x12

};

SimEfData    defCcEfData =
{
    SIM_EMU_EF_CC,             /* Id           */
    SIM_EFSTRUCT_T,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    1 ,                         /* num Rec      */
    SIM_EMU_SIZE_CC_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                     /*sfi supported  */
    0x00,                     /*sfi */
    FALSE                      /*Under USIM */
};

/*-----------------17/06/2003 10:54-----------------
 * EF PUID   (previous UID)
 * section 4.4.2.12.4
 *--------------------------------------------------*/
Int8    defPuidData[] =
{
      0x01, 0x99

};

SimEfData    defPuidEfData =
{
    SIM_EMU_EF_PUID,           /* Id           */
    SIM_EFSTRUCT_T,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    1 ,                         /* num Rec      */
    SIM_EMU_SIZE_PUID_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                     /*sfi supported  */
    0x00,                     /*sfi */
    FALSE                      /*Under USIM */
};

/*-----------------27/03/02 09:25-------------------
 * ANR A   (first additional number)
 *--------------------------------------------------*/
Int8    defAnrAData[] =
{
        0x01,   /*Additional number identifier*/
        0x03,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x11, 0xf1,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */


        0x01,   /*Additional number identifier*/
        0x03,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x22, 0xf2,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0x01,   /*Additional number identifier*/
        0x03,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x33, 0xf3,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0x01,   /*Additional number identifier*/
        0x03,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x44, 0xf4,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff   /*ext 1 */

};

SimEfData    defAnrAEfData =
{
    SIM_EMU_EF_ANRA,             /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_ANRA_RECS,  /* num Rec      */
    SIM_EMU_SIZE_ANRA_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi */
    FALSE                      /*Under USIM */
};

/*-----------------27/03/02 09:25-------------------
 * ANR B   (second additional number)
 *--------------------------------------------------*/


Int8    defAnrBData[] =
{
        0x01,   /*Additional number identifier*/
        0x03,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x21, 0xf3,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */


        0x01,   /*Additional number identifier*/
        0x03,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x54, 0xf6,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0x01,   /*Additional number identifier*/
        0x03,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x21, 0xf3,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0x01,   /*Additional number identifier*/
        0x03,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x54, 0xf6,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff   /*ext 1 */

};


SimEfData    defAnrBEfData =
{
    SIM_EMU_EF_ANRB,             /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_ANRB_RECS,  /* num Rec      */
    SIM_EMU_SIZE_ANRB_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi */
    FALSE                      /*Under USIM */
};

/*-----------------27/03/02 09:25-------------------
 * ANR C  (type 1 or 2) third additional number
 *--------------------------------------------------*/

Int8    defAnrCData[] =
{
        0x01,   /*Additional number identifier*/
        0x05,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x11, 0x11,0xf1, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */


        0x01,   /*Additional number identifier*/
        0x05,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x22, 0x22,0xf2, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0x01,   /*Additional number identifier*/
        0x05,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x33, 0x33,0xf3, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0x01,   /*Additional number identifier*/
        0x05,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x44, 0x44,0xf4, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

};
SimEfData    defAnrCEfData =
{
    SIM_EMU_EF_ANRC,             /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_ANRC_RECS,  /* num Rec      */
    SIM_EMU_SIZE_ANRC_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi */
    FALSE                      /*Under USIM */
};

/*------------------------------------------------------
 * ANR A1   (type 1 / 2)  first additional number
 *--------------------------------------------------*/

Int8    defAnrA1Data[] =
{
        0x01,   /*Additional number identifier*/
        0x03,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x11, 0xf1,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */


        0x01,   /*Additional number identifier*/
        0x03,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x22, 0xf2,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0x01,   /*Additional number identifier*/
        0x03,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x33, 0xf3,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0x01,   /*Additional number identifier*/
        0x03,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x44, 0xf4,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */



};

SimEfData    defAnrA1EfData =
{
    SIM_EMU_EF_ANRA1,             /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_ANRA1_RECS,  /* num Rec      */
    SIM_EMU_SIZE_ANRA1_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi */
    FALSE                      /*Under USIM */
};

/*-----------------27/03/02 09:25-------------------
 * ANR B1    (type 1 /2)  second additional number
 *--------------------------------------------------*/

Int8    defAnrB1Data[] =
{
        0x01,   /*Additional number identifier*/
        0x03,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x21, 0xf3,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */


        0x01,   /*Additional number identifier*/
        0x03,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x54, 0xf6,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0x01,   /*Additional number identifier*/
        0x03,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x21, 0xf3,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0x01,   /*Additional number identifier*/
        0x03,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x54, 0xf6,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff   /*ext 1 */




};

SimEfData    defAnrB1EfData =
{
    SIM_EMU_EF_ANRB1,             /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_ANRB1_RECS,  /* num Rec      */
    SIM_EMU_SIZE_ANRB1_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi */
    FALSE                      /*Under USIM */
};

/*-----------------27/03/02 09:25-------------------
 * ANR C1  (type 1 / 2)  third additional number
 *--------------------------------------------------*/

Int8    defAnrC1Data[] =
{
        0x01,   /*Additional number identifier*/
        0x05,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x11, 0x11,0xf1, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */


        0x01,   /*Additional number identifier*/
        0x05,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x22, 0x22,0xf2, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0x01,   /*Additional number identifier*/
        0x05,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x33, 0x33,0xf3, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0x01,   /*Additional number identifier*/
        0x05,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x44, 0x44,0xf4, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff, 0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff   /*ext 1 */


};

SimEfData    defAnrC1EfData =
{
    SIM_EMU_EF_ANRC1,             /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_ANRC1_RECS,  /* num Rec      */
    SIM_EMU_SIZE_ANRC1_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi */
    FALSE                      /*Under USIM */
};
#endif 
 /*CQ00135656, Cgliu, 2022-02-25,End  */

/*Modification for USIM it test. CQ00001116, Chengguang Liu, June.09 09,Begin*/
#ifdef ON_PC

Int8    defExt1Data_ccsa[] = {
/*1*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*2*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*3*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*4*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*5*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*6*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*7*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*8*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*9*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*a*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*b*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*c*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff
};

Int8    defAdnData_ccsa[] =
{
        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,

        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,

        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        
        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        
        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        
        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        
        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        
        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        
        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        
        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
};
Int8    defPbrData_ccsa[] = {
                             /*record number 1*/
                             0xa8, 0x26,                    /*type 1 files*/
                             0xc0, 0x03, 0x4f, 0x3a, 0x01,  /*ADN*/
                             0xc5, 0x03, 0x4f, 0x09, 0x02,  /*PBC*/
                             0xc6, 0x02, 0x4f, 0x83,        /*GRP*/
                             0xc4, 0x02, 0x4f, 0x11,        /*ANR*/
                             0xc4, 0x02, 0x4f, 0x13,        /*ANR*/
                             0xc4, 0x02, 0x4f, 0x15,        /*ANR*/
                             0xc3, 0x02, 0x4f, 0x19,        /*SNE*/
                             0xc9, 0x02, 0x4f, 0x81,        /*UID*/
                             0xca, 0x02, 0x4f, 0x50,        /*EMAIL*/
                             0xaa, 0x0c,                    /*type 3 files */
                             0xc2, 0x02, 0x4f, 0x4a,        /*EXT1*/
                             0xc7, 0x02, 0x4f, 0x4b,        /*AAS*/
                             0xc8, 0x02, 0x4f, 0x4c,        /*GAS*/

                             };
SimEfData    defPbrEfData_ccsa =
{
    SIM_EMU_EF_PBR,                 /* Id           */
    SIM_EFSTRUCT_LF,              /* Type         */
    SIM_DIR_DF_PHONEBOOK,        /* Dir          */
    1,   /* Num Recs     */
    SIM_EMU_SIZE_PBR_FILE,  /* Rec Len     */
    (0),                         /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_ADM,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi */
    FALSE                      /*Under USIM */
};

/* add by zhanghao for USIM it test. CQ00001334 20090703 Begin */
Int8    defExt1Data_31121[] = {
/*1*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*2*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*3*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*4*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*5*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*6*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*7*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*8*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*9*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*a*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*b*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*c*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff
};

Int8    defAdnData_31121[] =
{
        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,

        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,

        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        
        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        
        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        
        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        
        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        
        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        
        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        
        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
};
Int8    defPbrData_31121[] = {
                             /*record number 1*/
                             0xa8, 0x26,                    /*type 1 files*/
                             0xc0, 0x03, 0x4f, 0x3a, 0x01,  /*ADN*/
                             0xc5, 0x03, 0x4f, 0x09, 0x02,  /*PBC*/
                             0xc6, 0x02, 0x4f, 0x83,        /*GRP*/
                             0xc4, 0x02, 0x4f, 0x11,        /*ANR*/
                             0xc4, 0x02, 0x4f, 0x13,        /*ANR*/
                             0xc4, 0x02, 0x4f, 0x15,        /*ANR*/
                             0xc3, 0x02, 0x4f, 0x19,        /*SNE*/
                             0xc9, 0x02, 0x4f, 0x81,        /*UID*/
                             0xca, 0x02, 0x4f, 0x50,        /*EMAIL*/
                             0xaa, 0x0c,                    /*type 3 files */
                             0xc2, 0x02, 0x4f, 0x4a,        /*EXT1*/
                             0xc7, 0x02, 0x4f, 0x4b,        /*AAS*/
                             0xc8, 0x02, 0x4f, 0x4c,        /*GAS*/

                             };
SimEfData    defPbrEfData_31121 =
{
    SIM_EMU_EF_PBR,                 /* Id           */
    SIM_EFSTRUCT_LF,              /* Type         */
    SIM_DIR_DF_PHONEBOOK,        /* Dir          */
    1,   /* Num Recs     */
    SIM_EMU_SIZE_PBR_FILE,  /* Rec Len     */
    (0),                         /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_ALWAYS,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi */
    FALSE                      /*Under USIM */
};
/* add by zhanghao for USIM it test. CQ00001334 20090703 End */
/*Added by zhjzhao for CQ00013463 Add a set of default USIM files value for LTE simulation on PC 10-10-11, Begin*/
#if defined(UPGRADE_LTE)
Int8    defExt1Data_LTE[] = {
/*1*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*2*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*3*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*4*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*5*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*6*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*7*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*8*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*9*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*a*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*b*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*c*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff
};

Int8    defPbrData_LTE[] = {
                             /*record number 1*/
                             0xa8, 0x26,                    /*type 1 files*/
                             0xc0, 0x03, 0x4f, 0x3a, 0x01,  /*ADN*/
                             0xc5, 0x03, 0x4f, 0x09, 0x02,  /*PBC*/
                             0xc6, 0x02, 0x4f, 0x83,        /*GRP*/
                             0xc4, 0x02, 0x4f, 0x11,        /*ANR*/
                             0xc4, 0x02, 0x4f, 0x13,        /*ANR*/
                             0xc4, 0x02, 0x4f, 0x15,        /*ANR*/
                             0xc3, 0x02, 0x4f, 0x19,        /*SNE*/
                             0xc9, 0x02, 0x4f, 0x81,        /*UID*/
                             0xca, 0x02, 0x4f, 0x50,        /*EMAIL*/
                             0xaa, 0x0c,                    /*type 3 files */
                             0xc2, 0x02, 0x4f, 0x4a,        /*EXT1*/
                             0xc7, 0x02, 0x4f, 0x4b,        /*AAS*/
                             0xc8, 0x02, 0x4f, 0x4c        /*GAS*/

                             };
SimEfData    defPbrEfData_LTE =
{
    SIM_EMU_EF_PBR,                 /* Id           */
    SIM_EFSTRUCT_LF,              /* Type         */
    SIM_DIR_DF_PHONEBOOK,        /* Dir          */
    1,   /* Num Recs     */
    SIM_EMU_SIZE_PBR_FILE,  /* Rec Len     */
    (0),                         /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_ALWAYS,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi */
    FALSE                      /*Under USIM */
};
#endif
/*Added by zhjzhao for CQ00013463 Add a set of default USIM files value for LTE simulation on PC 10-10-11, End*/

#endif
/*Modification for USIM it test. CQ00001116, Chengguang Liu, June.09 09,End  */



#endif


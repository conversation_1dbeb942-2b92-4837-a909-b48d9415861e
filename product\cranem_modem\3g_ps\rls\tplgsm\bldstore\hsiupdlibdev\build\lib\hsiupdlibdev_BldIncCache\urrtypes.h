/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/api/inc/urrtypes.h#9 $
 *   $Revision: #9 $
 *   $DateTime: 2007/03/30 13:53:25 $
 **************************************************************************
 * File Description:
 *
 *    Contains types for use by URRC modules.
 **************************************************************************/

#if !defined (URRTYPES_H)
#define       URRTYPES_H
/*********** START */
#if !defined ERRC_UNIT_TEST
/*********** END */

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <system.h>
#include <uas_asn.h>
#include <urlc_sig.h>
#include <rrc_sig.h>
#if !defined (NODEBSIM)
#include <cmac_sig.h>
#include <grrmrtyp.h>
#endif
#include <utper.h>
#if !defined (NODEBSIM)
#include <grrrcsig.h>
#endif
#include <ki_unitq.h>
#include <urrstates.h>
#include <release_api.h> /* CQ00062449 change Release_api.h to release_api.h */
#include <urrstates.h>
/* CQ00025639 : Upgrade to LTE+WCDMA feature : begin */
//#if !defined (EXCLUDE_GSM_PS_AS) || !defined (EXCLUDE_GSM_L1)
#include "lteUtraGsmIratItf.h"
//#endif
/* CQ00025639 : Upgrade to LTE+WCDMA feature : begin */

/*CQ00070208 add begin*/
//#if defined(UPGRADE_PLMS)//CQ00107188 removed
#if !defined (ENABLE_GRR_UNIT_TESTING) /* CQ00072256 START */
#include <ebndcfg.h>
#endif /* CQ00072256 END */
//#endif/*UPGRADE_PLMS*/ //CQ00107188 removed
/*CQ00070208 add end*/
//#if defined(UPGRADE_COMMON_STORE)
#include <rrcdstypes.h>
//#endif/*UPGRADE_COMMON_STORE*/


/***************************************************************************
 * Manifest Constants
 ***************************************************************************/
#define NO_OF_INT16S_IN_INT64     4
#define MAX_SIB_SCHEDULE_SIZE     29
#define MAX_EXT_SIB_SCHEDULE_SIZE 8
#define NUM_SUB_PROCESSES         9
#define MAX_SIBS_REQUESTED        32
#define MAX_EXT_SIBS_REQUESTED    8
#define MAX_EXTEXT2_SIBS_REQUESTED  16


#define INVALID_PRIMARY_SCRAMBLING_CODE 0xffff

#define INVALID_EARFCN            0
#define INVALID_PHYSICAL_CELL_ID  0xffff
#define INVALID_RSRP              -32000

#define DEFAULT_EUTRA_THRESHOLD_HIGH    0   /* value: 0 - 31 */
#define DEFAULT_EUTRA_THRESHOLD_HIGH2   0   /* value: 0 - 31 */
#define DEFAULT_EUTRA_RSRP_THRESHOLD   24   /* -117dbm CQ00036338/CQ00035322) */
#define MAX_SRB_ID                  4 /* RBs 0-4 are for signalling */
#define MAX_MANDATORY_SRB_ID        3 /* RBs 0-3 are mandatory SRBs */

/* Set to 40 to allow for a 800ms PHY config window,
 * with BCCH data being received every 20ms */

#define NUMBER_OF_SIGNALS_TO_DEQUEUE_LIMIT  400 /*CQ00043083: Change from 40*/

#define TRANSACTION_ID_MULTIPLIER 4 /* Described in 25.331 ********* */
#define MAX_GSM_TARGET_CELLS 32

/* CQ00094253 added begin */
#define DUE_TO_PHY_CONFIG 0x01
#define DUE_TO_LTE_PLMN   0x02
/* CQ00094253 added end */

#if defined(UPGRADE_DSDSWB)
#define URRC_TASK_NUM 2
#else
#define URRC_TASK_NUM 1
#endif
#if !defined (UPGRADE_DSDS)
#define URRC_DSDS_TASK 0
#endif

/***************************************************************************
*   Macro Functions
***************************************************************************/

/***************************************************************************
 * Types
 ***************************************************************************/

typedef enum UraUpdateConfirmTypeTag
{
    URA_UPDATE_CONFIRM_CCCH,
    URA_UPDATE_CONFIRM_DCCH
}
UraUpdateConfirmType;

typedef enum UrrModeStateTag
{
    URR_NO_MODES_OR_STATES,
    URR_MODE_IDLE,
    URR_MODE_CONNECTED,
    URR_STATE_URA_PCH,
    URR_STATE_CELL_PCH,
    URR_STATE_CELL_FACH,
    URR_STATE_CELL_DCH,
    URR_ALL_MODES_AND_STATES,
    URR_ALL_MODES_EXCEPT_CELL_DCH,
    URR_ALL_MODES_EXCEPT_IDLE_AND_CELL_DCH
}
UrrModeState;
/*********** START */
#endif //!defined ERRC_UNIT_TEST
/*********** END */
/* Modification of this enum also requires update of urrSubProcessIdString in urrutm.c */
//ICAT EXPORTED ENUM
typedef enum UrrSubProcessIdTag
{
    URRC_NULL_SUB_PROCESS   = 0x0000,
    URRC_MTC_SUB_PROCESS    = 0x0001,
    URRC_SMC_SUB_PROCESS    = 0x0002,
    URRC_CSR_SUB_PROCESS    = 0x0003,
    URRC_SIR_SUB_PROCESS    = 0x0004,
    URRC_CER_SUB_PROCESS    = 0x0005,
    URRC_RBC_SUB_PROCESS    = 0x0006,
    URRC_CMR_SUB_PROCESS    = 0x0007,
    URRC_AIS_SUB_PROCESS    = 0x0008,
    URRC_MCR_SUB_PROCESS    = 0x0009,
    URRC_UTM_SUB_PROCESS    = 0x000A
    ,
    URRC_LTE_SUB_PROCESS    = 0x000A,
    URRC_SUB_PROCESS_DUMMY  = 0x000B
}
UrrSubProcessId;

/*********** START */
#if !defined ERRC_UNIT_TEST
/*********** END */


//ICAT EXPORTED ENUM
typedef enum UrrShortSubProcessIdTag
{
    SUB_NULL   = 0x0000,
    MTC    = 0x0001,
    SMC    = 0x0002,
    CSR    = 0x0003,
    SIR    = 0x0004,
    CER    = 0x0005,
    RBC    = 0x0006,
    CMR    = 0x0007,
    AIS    = 0x0008,
    MCR    = 0x0009
    ,
    LTE    = 0x000A,
    SUB_DUMMY  = 0x000B
}
UrrShortSubProcessId;
/*********** START */
#endif //!defined ERRC_UNIT_TEST
/*********** END */
typedef enum UrrTimerBaseTag
{
    URRC_MTC_TIMER_BASE    = URRC_MTC_SUB_PROCESS * 0x1000,
    URRC_SMC_TIMER_BASE    = URRC_SMC_SUB_PROCESS * 0x1000,
    URRC_CSR_TIMER_BASE    = URRC_CSR_SUB_PROCESS * 0x1000,
    URRC_SIR_TIMER_BASE    = URRC_SIR_SUB_PROCESS * 0x1000,
    URRC_CER_TIMER_BASE    = URRC_CER_SUB_PROCESS * 0x1000,
    URRC_RBC_TIMER_BASE    = URRC_RBC_SUB_PROCESS * 0x1000,
    URRC_CMR_TIMER_BASE    = URRC_CMR_SUB_PROCESS * 0x1000,
    URRC_AIS_TIMER_BASE    = URRC_AIS_SUB_PROCESS * 0x1000,
    URRC_MCR_TIMER_BASE    = URRC_MCR_SUB_PROCESS * 0x1000
}
UrrTimerBase;
/*********** START */
#if !defined ERRC_UNIT_TEST
/*********** END */
/* Urr Mode & State information */
/* Note: If the enumerated values for UrrSmcMode and UrrSmcConnectedState are
 * updated the lookup tables in URRCMR also need to be updated */

/* Modification of this enum also requires update of urrSmcConnectedStateString in urrutm.c */
//ICAT EXPORTED ENUM
typedef enum UrrSmcConnectedStateTag
{
    URR_SMC_STATE_CELL_DCH = 0,
    URR_SMC_STATE_CELL_FACH = 1,
    URR_SMC_STATE_CELL_PCH = 2,
    URR_SMC_STATE_URA_PCH = 3
}
UrrSmcConnectedState;

//ICAT EXPORTED ENUM
typedef enum UrrSmcDeactivatedStateTag
{
    URR_SMC_STATE_UDEACT_NORMAL = 0,
    URR_SMC_STATE_UDEACT_POWER_OFF = 1
}
UrrSmcDeactivatedState;

//ICAT EXPORTED ENUM
typedef enum UrrSmcPhyStateTag   // for tracking state of physical layer //
{
    URR_SMC_PHY_STATE_NULL,
    URR_SMC_PHY_STATE_CELL_SEARCH,
    URR_SMC_PHY_STATE_IDLE,
    URR_SMC_PHY_STATE_FACH,
    URR_SMC_PHY_STATE_DCH
}
UrrSmcPhyState;
typedef enum UrrSirSibTypeTag
{
    URR_SIR_masterInformationBlock,
    URR_SIR_systemInformationBlockType1,
    URR_SIR_systemInformationBlockType2,
    URR_SIR_systemInformationBlockType3,
    URR_SIR_systemInformationBlockType4,
    URR_SIR_systemInformationBlockType5,
    URR_SIR_systemInformationBlockType6,
    URR_SIR_systemInformationBlockType7,
    URR_SIR_dummy,
    URR_SIR_dummy2,
    URR_SIR_dummy3,
    URR_SIR_systemInformationBlockType11,
    URR_SIR_systemInformationBlockType12,
    URR_SIR_systemInformationBlockType13,
    URR_SIR_systemInformationBlockType13_1,
    URR_SIR_systemInformationBlockType13_2,
    URR_SIR_systemInformationBlockType13_3,
    URR_SIR_systemInformationBlockType13_4,
    URR_SIR_systemInformationBlockType14,
    URR_SIR_systemInformationBlockType15,
    URR_SIR_systemInformationBlockType15_1,
    URR_SIR_systemInformationBlockType15_2,
    URR_SIR_systemInformationBlockType15_3,
    URR_SIR_systemInformationBlockType16,
    URR_SIR_systemInformationBlockType17,
    URR_SIR_systemInformationBlockType15_4,
    URR_SIR_systemInformationBlockType18,
    URR_SIR_schedulingBlock1,
    URR_SIR_schedulingBlock2,
    URR_SIR_systemInformationBlockType15_5,
    URR_SIR_systemInformationBlockType5bis,
    URR_SIR_systemInformationBlockType19,
    URR_SIR_systemInformationBlockType15_2ter,
    URR_SIR_systemInformationBlockType20
}
UrrSirSibType;

//CQ00055585, move enum UrrSirSibTypeExtExt2 declaration to rrc_sig.h
/*
typedef enum UrrSirSibTypeExtExt2Tag
{
    URR_SIB_TypeExt_systemInfoType11bis = 0,
    URR_SIB_TypeExt_systemInfoType15bis = 1,
    URR_SIB_TypeExt_systemInfoType15_1bis = 2,
    URR_SIB_TypeExt_systemInfoType15_2bis = 3,
    URR_SIB_TypeExt_systemInfoType15_3bis = 4,
    URR_SIB_TypeExt_systemInfoType15_6 = 5,
    URR_SIB_TypeExt_systemInfoType15_7 = 6,
    URR_SIB_TypeExt_systemInfoType15_8 = 7,
    URR_SIB_TypeExt2_systemInfoType19 = 8,
    URR_SIB_TypeExt2_systemInfoType15_2ter = 9,
    URR_SIB_TypeExt2_systemInfoType20 = 10,
    URR_SIB_TypeExt2_spare5 = 11,
    URR_SIB_TypeExt2_spare4 = 12,
    URR_SIB_TypeExt2_spare3 = 13,
    URR_SIB_TypeExt2_spare2 = 14,
    URR_SIB_TypeExt2_spare1 = 15,
}
UrrSirSibTypeExtExt2;
*/
/* **********, added begin */
#if defined (DEBUG_MEMORY_LEAK_ON_PC)
typedef struct aboutMemTag
{
    Int32 memAddress;
    Int32 memSize;
    char *file_p;
    Int32 line;
    char *file_p1;
    Int32 line1;
}aboutMem;
#endif
/* **********, added end */


/* Modification of this enum also requires update of urrSmcModeString in urrutm.c */
//ICAT EXPORTED ENUM
typedef enum UrrSmcModeTag
{
    URR_SMC_MODE_DEACTIVATED = 0,
    URR_SMC_MODE_SUSPENDED = 1,
    URR_SMC_MODE_IDLE = 2,
    URR_SMC_MODE_UTRA_CONNECTED = 3
}
UrrSmcMode;

//ICAT EXPORTED STRUCT
typedef struct UrrSmcModeAndStateTag
{
    UrrSmcMode       mode;
    //ICAT EXPORTED UNION : UrrSmcStateTag
    union UrrSmcStateTag
    {
        // for use if mode == URR_SMC_MODE_UTRA_CONNECTED //
        UrrSmcConnectedState  connectedState;

        // for use if mode == URR_SMC_MODE_DEACTIVATED //
        UrrSmcDeactivatedState  deactivatedState;
    } state;
}
UrrSmcModeAndState;

//ICAT EXPORTED ENUM
typedef enum McrStateTag
{
    MCR_NORMAL,
    MCR_AWAIT_TRANSIT_TO_FACH,  
    MCR_AWAIT_CELL_UPDATE_OCCURED,
    MCR_CELL_CHANGED,
    MCR_DELETE_INTRA_INTER_GSM_MEAS
#if defined(UPGRADE_UL_ECF) // ********** begin
    ,MCR_AWAIT_TRANSIT_TO_FACH_WITHOUT_CELL_UPDATE
#endif /* UPGRADE_UL_ECF  */ // ********** end  	
}
McrState;

/* Modification of this enum also requires update of urrCmrStateString in urrutm.c */
//ICAT EXPORTED ENUM
typedef enum UrrCmrStateTag
{
    CMR_IDLE,                 /* 0 CMR is IDLE */
    CMR_NEW_STATE_WAIT_CAMP,  /* Waiting for cell selection after UE state change */
    CMR_NEW_STATE_WAIT_SIBS,  /* Waiting for SIBs after UE state change */
    CMR_RESEL_WAIT_SIBS,      /* Waiting for SIBs after cell reselection */
    CMR_RECONFIG_WAIT_SIBS,   /* Waiting for SIBs after reconfiguration */
    CMR_WAIT_MAC_TX,          /* 5 Waiting for tx status indication from MAC */
    CMR_WAIT_UPDATE_CONFIRM,  /* Waiting for CELL/URA UPDATE CONFIRM message */
    CMR_WAIT_RACH_MEAS_RESULTS, /* Waiting for RACH meassurement */
    CMR_LINK_FAIL_WAIT_CAMP,    /* Waiting for cell selection after radio link failure or unrecoverable error */
    CMR_LINK_FAIL_WAIT_SIBS,    /* Waiting for SIBs after radio link failure or unrecoverable error */
    CMR_WAIT_FOR_SIB7_BEFORE_CELL_UPDATE, /* 10 Waiting for SIB7 before sending CELL UPDATE */
    CMR_WAIT_FOR_SIB7_BEFORE_URA_UPDATE,  /* Waiting for SIB7 before sending URA UPDATE */
    CMR_WAIT_RBC_PROCESS_UPDATE_CONFIRM,  /* Waiting for RBC to finish processing CELL/URA UPDATE CONFIRM */
    CMR_WAIT_MOVE_TO_CELL_FACH_STATE,     /* Waiting for state transition to CELL_FACH before sending CELL/URA UPDATE */
    CMR_NO_CELL                           /* 14 Waiting for serving cell to come back */
}
UrrCmrState;

/* Note: Any updates to this enumeration should also be reflected in */
/*       UrrRbcCellUpdateStateText in urrrbcmg.c                     */
//ICAT EXPORTED ENUM
typedef enum UrrRbcCellUpdateStateTag
{
    RBC_CELL_CHANGE_IDLE = 0,
    RBC_CELL_CHANGE_RECONFIG_FACH_WAIT_FOR_CAMPED_ON_CELL = 1,
    RBC_CELL_CHANGE_RRC_CONN_SETUP_FACH_WAIT_FOR_CAMPED_ON_CELL = 2,
    RBC_CELL_CHANGE_DCH_FAILURE_WAIT_FOR_CAMPED_ON_CELL = 3,
    RBC_CELL_CHANGE_WAIT_FOR_CELL_UPDATE_CONFIRM = 4,
    RBC_CELL_CHANGE_WAIT_FOR_CU_CNF_ACK = 5,
    RBC_CELL_CHANGE_WAIT_FOR_SIB_RECVD = 6
}
UrrRbcCellUpdateState;

//ICAT EXPORTED ENUM
typedef enum UrrAisStatesTag
{
    URRAIS_STATE_IDLE,
    URRAIS_STATE_WAIT_RACH_TX,   
    URRAIS_STATE_WAIT_UM_TX_NOTIFY,     /* only for RRC connection release */
    URRAIS_STATE_WAIT_AM_CNF
#if defined(UPGRADE_UL_ECF) // ********** begin
    ,URRAIS_STATE_WAIT_EDCH_RACH_TX
#endif /* UPGRADE_UL_ECF  */ // ********** end 	
}
UrrAisStates;

//ICAT EXPORTED ENUM
typedef enum UrrSirCellSelectionStateTag
{
    SIR_STATE_CELL_NOT_SELECTED,
    SIR_STATE_CELL_SELECTED
}
UrrSirCellSelectionState;

//ICAT EXPORTED ENUM
typedef enum SibStorageStateTag
{
    SIB_STORAGE_IDLE,                       /* no stored SIB activity         */
    SIB_STORAGE_CELL_SELECTED,              /* acquiring serving cell SIBs    */
    SIB_STORAGE_CELL_SELECTED_WAIT_CAMP,    /* serv SIBs acquired, wait camp  */
    SIB_STORAGE_NCELL_SIBS,                 /* acquiring ncell SIBs           */
    SIB_STORAGE_NCELL_SIBS_WAIT_CAMP,       /* ncell SIBs acquired, wait camp */
    SIB_STORAGE_DISTRIBUTE_STORED_SIBS      /* distributing stored sys info   */
}
SibStorageState;

/* Modification of this enum also requires update of bchOnOffStateString in urrutm.c */
//ICAT EXPORTED ENUM
typedef enum BchOnOffStateTag
{
    BCH_SCELL_OFF_NCELL_OFF,
    BCH_SCELL_ON_NCELL_OFF,
    BCH_SCELL_OFF_NCELL_WAIT,
    BCH_SCELL_OFF_NCELL_ON,
    BCH_SCELL_PEND_NCELL_WAIT,
    BCH_SCELL_PEND_NCELL_ON,
    BCH_NO_CELL,
    BCH_NO_CELL_NCELL_WAIT,
    BCH_NO_CELL_NCELL_ON
}
BchOnOffState;



typedef enum UrrAisSendStatusTag // URR_INTERNAL_SIGNAL_AIR_SIGNAL_SEND_STATUS //
{
    URR_AIS_SEND_STATUS_SUCCESS,
    URR_AIS_SEND_STATUS_FAIL_NO_REPLY_TO_RACH,
    URR_AIS_SEND_STATUS_FAIL_RACH_CONFIG_ERROR,
    URR_AIS_SEND_STATUS_ERROR_NESTED_RACH_NOT_ALLOWED,
    URR_AIS_SEND_STATUS_FAIL_DISCARD_TIMER_EXPIRY,
    URR_AIS_SEND_STATUS_FAIL_DISCARD_MAX_ATTEMPTS,
    URR_AIS_SEND_STATUS_FAIL_DISCARD_RLC_RESET_OCCURED,
    URR_AIS_SEND_STATUS_FAIL_DISCARD_RE_ESTABLISH,
    URR_AIS_SEND_STATUS_FAIL_DISCARD_RELEASE,
    URR_AIS_SEND_STATUS_FAIL_DISCARD_BUFFER_FULL,
    URR_AIS_SEND_STATUS_FAIL_DISCARD_NO_SERVICE
}
UrrAisSendStatus;

//ICAT EXPORTED ENUM
typedef enum UrrModeTransitionTag
{
    IDLE_TO_IDLE,
    IDLE_TO_CONNECTED,
    IDLE_TO_SUSPENDED,
    IDLE_TO_DEACTIVATED,
    CONNECTED_TO_IDLE,
    CONNECTED_TO_CONNECTED,
    CONNECTED_TO_SUSPENDED,
    CONNECTED_TO_DEACTIVATED,
    SUSPENDED_TO_IDLE,
    SUSPENDED_TO_CONNECTED,
    SUSPENDED_TO_SUSPENDED,
    SUSPENDED_TO_DEACTIVATED,
    DEACTIVATED_TO_IDLE,
    DEACTIVATED_TO_CONNECTED,
    DEACTIVATED_TO_SUSPENDED,
    DEACTIVATED_TO_DEACTIVATED
}
UrrModeTransition;

//ICAT EXPORTED ENUM
typedef enum UrrStateTransitionTag
{
    CELL_DCH_TO_CELL_DCH,
    CELL_DCH_TO_CELL_FACH,
    CELL_DCH_TO_CELL_PCH,
    CELL_DCH_TO_URA_PCH,
    CELL_FACH_TO_CELL_DCH,
    CELL_FACH_TO_CELL_FACH,
    CELL_FACH_TO_CELL_PCH,
    CELL_FACH_TO_URA_PCH,
    CELL_PCH_TO_CELL_DCH,
    CELL_PCH_TO_CELL_FACH,
    CELL_PCH_TO_CELL_PCH,
    CELL_PCH_TO_URA_PCH,
    URA_PCH_TO_CELL_DCH,
    URA_PCH_TO_CELL_FACH,
    URA_PCH_TO_CELL_PCH,
    URA_PCH_TO_URA_PCH
}
UrrStateTransition;

typedef enum UrrAisAirSignalTypeTag
{
    URR_AIS_AIR_SIGNAL_CCCH_MESSAGE,
    URR_AIS_AIR_SIGNAL_DCCH_MESSAGE
}
UrrAisAirSignalType;

typedef enum UrrAisTransactionAcceptRejectTag
{
    URRAIS_TRANSACTION_ACCEPTED,
    URRAIS_TRANSACTION_REJECTED,
    URRAIS_TRANSACTION_NEITHER
}
UrrAisTransactionAcceptReject;

typedef enum UrrTimerConstStatusTag
{
    URR_TIMER_CONST_DEFAULT = 0,  /* set to default value */
    URR_TIMER_CONST_FROM_SIB1,    /* the value was set by SIB1 */
    URR_TIMER_CONST_FROM_UMI      /* the value was set by UTRAN MOBILITY INFORMATION */
}
UrrTimerConstStatus;

typedef enum UrrConnTimerSourceTag
{
    URR_CONN_TIMER_SOURCE_SIB1, /* info is from SIB1 */
    URR_CONN_TIMER_SOURCE_UMI   /* info is from UTRAN MOBILITY INFORMATION */
}
UrrConnTimerSource;

#if defined (ENABLE_URRC_UNIT_TESTING)
typedef enum UtmSibTypeTag
{
		Sib_Type_MIB = 0x0,
		Sib_Type_SIB1,
		Sib_Type_SIB2,
		Sib_Type_SIB3,
		Sib_Type_SIB4,
		Sib_Type_SIB5,
		Sib_Type_SIB6,
		Sib_Type_SIB7,
		Sib_Type_dummy,
		Sib_Type_dummy2,
		Sib_Type_dummy3,
		Sib_Type_SIB11,
		Sib_Type_SIB12,
		Sib_Type_SIB13,
		Sib_Type_SIB13_1,
		Sib_Type_SIB13_2,
		Sib_Type_SIB13_3,
		Sib_Type_SIB13_4,
		Sib_Type_SIB14,
		Sib_Type_SIB15,
		Sib_Type_SIB15_1,
		Sib_Type_SIB15_2,
		Sib_Type_SIB15_3,
		Sib_Type_SIB16,
		Sib_Type_SIB17,
		Sib_Type_SIB15_4,
		Sib_Type_SIB18,
		Sib_Type_schedulingBlock1,
		Sib_Type_schedulingBlock2,
		Sib_Type_SIB15_5,
		Sib_Type_SIB5bis,
		Sib_TypeExt_SIB11bis = 0x1000,
		Sib_TypeExt_SIB15bis = 0x1040,
		Sib_TypeExt_SIB15_1bis = 0x1080,
		Sib_TypeExt_SIB15_2bis = 0x10C0,
		Sib_TypeExt_SIB15_3bis = 0x1100,
		Sib_TypeExt_SIB15_6 = 0x1140,
		Sib_TypeExt_SIB15_7 = 0x1180,
		Sib_TypeExt_SIB15_8 = 0x11C0,
		Sib_TypeExt2_SIB19 = 0x1001,
		Sib_TypeExt2_SIB15_2ter = 0x1041,
		Sib_TypeExt2_SIB20 = 0x1081,
		Sib_TypeExt2_spare5 = 0x10C1,
		Sib_TypeExt2_spare4 = 0x1101,
		Sib_TypeExt2_spare3 = 0x1141,
		Sib_TypeExt2_spare2 = 0x1181,
		Sib_TypeExt2_spare1 = 0x11C1
} UtmSibType;
#endif //(ENABLE_URRC_UNIT_TESTING)

typedef struct UrrAisRachTtiTag
{
    Boolean                     valid;      /* tti is valid */
    UTransmissionTimeInterval   tti;        /* TTI value */
}
UrrAisRachTti;

typedef enum InterRATInfoTag
{
    InterRATInfo_gsm,
    InterRATInfo_eutra
}
InterRATInfo;

typedef struct EutraCellTag
{
//#if defined (UPGRADE_URR_CSG_USING_PLMS)      // CQ00107230 deleted
    Earfcn                              earfcn;
//#else                                         // CQ00107230 deleted
    //UEARFCN 							earfcn; // CQ00107230 deleted
//#endif                                        // CQ00107230 deleted
    UEUTRA_PhysicalCellIdentity         physicalCellIdentity;
/*CQ00070208 add begin*/
#if defined (UPGRADE_PLMS)
    Int8                                freqBandIndicator;
#if !defined (ENABLE_GRR_UNIT_TESTING) /* CQ00072256 START */
    EutraBandWidth 		                bandWidth;
#endif /* CQ00072256 END */
	Boolean                             sib1Rcvd;
	Boolean                             notSentListIndBecauseOfMib;
#endif/*UPGRADE_PLMS*/
/*CQ00070208 add end*/
}
EutraCell;

typedef struct UrrInterRATRedirectionTag
{
    InterRATInfo  interRATInfoType;

    Int8 numInterRATRedirectionInfoElements;
    Boolean interRATRedirectionPresent;
    UGSM_TargetCellInfo interRATRedirectionInfo[MAX_GSM_TARGET_CELLS];
    UEUTRA_TargetFreqInfo       cellsListRedirectToLTE[maxEUTRATargetFreqs];
}
UrrInterRATRedirection;

typedef struct UrrReleaseRedirectionInfoTag
{
    T_URedirectionInfo_latest tag;
    union
    {
        UFrequencyInfoFDD       fddRedirectionInfo;
        UrrInterRATRedirection  interRATRedirectionInfo;
    }
    choice;
}
UrrReleaseRedirectionInfo;

//ICAT EXPORTED STRUCT
typedef struct UUMTS_CellInformationTag
{
    UUARFCN                             uarfcn_dl;
    UPrimaryScramblingCode              primaryScramblingCode;
}
UUMTS_CellInformation;

typedef struct CellAreaIdentityTag
{
    Plmn                        plmn;
    Lac                         lac;
    Rac                         rac;
}
CellAreaIdentity;

typedef struct UrrIdentityValue64BitTag
{
    Int16   value [NO_OF_INT16S_IN_INT64];
}
UrrIdentityValue64Bit;

typedef struct UrrExternalSignalBuffersTag
{
    const SignalBuffer  *tx_p;            /* buffer for Tx external messages */
    SignalBuffer        rx;             /* buffer for Rx external messages */
}
UrrExternalSignalBuffers;

typedef struct UrrDebugFlagsTag
{
    Boolean     enableExternalSignalTrace;
    Boolean     enableInternalSignalTrace;
    Boolean     enableTimerTrace;
}
UrrDebugFlags;

typedef enum UrrActiveSetStatusTag
{
    ACTIVE_SET_ADDED,
    ACTIVE_SET_REMOVED
}
UrrActiveSetStatus;

//ICAT EXPORTED STRUCT
typedef struct UrrActiveSetEntryTag
{
    UPrimaryScramblingCode  primaryScramblingCode;
    UrrActiveSetStatus      status;
}
UrrActiveSetEntry;

//-+ UID START  CQ00210397 29-Mar-2012  +-
/* Type for reporting BCCH ERRORs to CSR */
typedef enum BcchErrorTag
{
    BCCH_ERROR_MIB_TIMEOUT,
    BCCH_ERROR_BAD_CRC,
    BCCH_ERROR_IGNORED_BCCH
}
BcchError;
//-+ UID END  CQ00210397 29-Mar-2012  +-

/* Type for reporting result of PRACH/FACH setup requests */
typedef enum UrrRbcPrachFachSetupResultTag
{
    RBC_RACH_FACH_FAIL_CONFIG_ERROR,
    RBC_RACH_FACH_FAIL_UNCONFIGURED,
    RBC_RACH_FACH_FAIL_SIB7_TIMEOUT,
    RBC_RACH_FACH_SUCCESS,
    RBC_RACH_FACH_SUCCESS_ALREADY_CONFIGURED
}
UrrRbcPrachFachSetupResult;

/* Enum to signal the actually received message version to the remainder of
 * the RRC and stack.
 *
 * In some cases we can't tell the difference between R4 and R5 (because the
 * message has not been critically extended). We could receive either an R3
 * or a LATER_THAN_R3 message in which case we return R5.
 *
 * Increasing values, so we can easily test for earlier versions using "<"
 * and ">" operators.
 */

typedef enum UrrRxMsgVersionTag
{
    URR_RX_MSG_VERSION_R3       = 0,
    URR_RX_MSG_VERSION_R4       = 1,
    URR_RX_MSG_VERSION_R5       = 2,
    URR_RX_MSG_VERSION_R6       = 3,
    URR_RX_MSG_VERSION_R7       = 4,
    URR_RX_MSG_VERSION_R8       = 5,
    URR_RX_MSG_VERSION_R9       = 6,
    URR_RX_MSG_VERSION_UNKNOWN  = 7
}
UrrRxMsgVersion;

#if !defined (NON_NEZHA)
typedef struct UrrMeDataReqTag
{
	//Boolean 						    isLteDualLink;
    NetworkMode                         supportOperatorMode;
    UGSM_Classmark2                     gsmClassmark2;
    UGSM_Classmark3                     gsmClassmark3;
	UGSM_Classmark3                     gsmClassmark3_PCS;	//MM will send now 2 classmark3.
    MobileIdElement                     imei;
#if !defined (UPGRADE_EXCLUDE_2G)
    UMaxGsmTxPower                      gsmMaxTxPower[NUM_BANDS];
    /* Raw GSM Mobile Equipment data also included */
    MsClassmarkElement                  classmark;
    MsCapability                        msCapability;
    GprsCapability                      gprsCapability;
    MsNetworkCapability                 msNetworkCapability;
    EgprsCapability                     egprsCapability;
#endif

    UmtsMobileEquipmentData             umtsMobileEquipmentData;
}
UrrMeDataReq;
#endif

//ICAT EXPORTED ENUM
typedef enum RrcConnSetupSuccessTag
{
    RRC_CONN_SETUP_SUCCESS,
    RRC_CONN_SETUP_NOT_VALID,
    RRC_CONN_SETUP_PHY_ESTAB_FAIL,
    RRC_CONN_SETUP_CELL_RESYNC_FAIL,
    RRC_CONN_SETUP_FACH_SELECTION_FAIL,
    RRC_CONN_SETUP_CELL_SELECTION_FAIL
}
RrcConnSetupSuccess;

//ICAT EXPORTED STRUCT
typedef struct UrrTmHfnInfoTag
{
    Boolean     isIncrementing; // the HFN is incremented on CFN wrap //
    Boolean     isReset; // the HFN has changed for a reason other than CFN wrap //
    UCOUNT_C    hfn;    // the 24 bit HFN //
} UrrTmHfnInfo;

typedef struct UrrUeIdentityTag
{
    UU_RNTI                     uRnti;      /* UTRAN Radio Network Temp-Id */
    Boolean                     cRntiValid; /* C-RNTI is valid */
    UC_RNTI                     cRnti;      /* Cell Radio Network Temp-Id */
    UCN_DomainIdentity          ueDomainId; /* CS or PS Id */

    Boolean                     tmsiPresent;/* Parts of the Initial Ue Id */
    Boolean                     pTmsiPresent;
    Boolean                     imeiPresent;
    Boolean                     imsiCsDomainPresent;
    Boolean                     imsiPsDomainPresent;
    UIMSI_GSM_MAP               imsi;
    UIMEI                       imei;
    UTMSI_and_LAI_GSM_MAP       tmsiAndLai;
    UP_TMSI_and_RAI_GSM_MAP     p_tmsiAndRai;

        /* INITIAL_UE_IDENTITY variable of 25.331 from the above information */
    UInitialUE_Identity         storedInitialUeId;
/* ********** add begin */
    Boolean                     isPTmsiMapped;/* PTMSI mapped from a valid GUTI */
/* ********** add end */        
}
UrrUeIdentity;

typedef struct UrrTimersTag
{
    UrrTimerConstStatus               t_300_status;
    UT_300                            t_300;
    UrrTimerConstStatus               t_301_status;
    UT_301                            t_301;
    UrrTimerConstStatus               t_302_status;
    UT_302                            t_302;
    UrrTimerConstStatus               t_304_status;
    UT_304                            t_304;
    UrrTimerConstStatus               t_305_status;
    UT_305                            t_305;
    UrrTimerConstStatus               t_307_status;
    UT_307                            t_307;
    UrrTimerConstStatus               t_308_status;
    UT_308                            t_308;
    UrrTimerConstStatus               t_309_status;
    UT_309                            t_309;
    UrrTimerConstStatus               t_310_status;
    UT_310                            t_310;
    UrrTimerConstStatus               t_311_status;
    UT_311                            t_311;
    UrrTimerConstStatus               idle_t_312_status;
    UT_312                            idle_t_312;
    UrrTimerConstStatus               conn_t_312_status;
    UT_312                            conn_t_312;
    UrrTimerConstStatus               t_312_status;
    UT_312                            t_312;
    UrrTimerConstStatus               t_313_status;
    UT_313                            t_313;
    UrrTimerConstStatus               t_314_status;
    UT_314                            t_314;
    UrrTimerConstStatus               t_315_status;
    UT_315                            t_315;
    UrrTimerConstStatus               t_316_status;
    UT_316                            t_316;
    UrrTimerConstStatus               t_320_status;
    Int16                             t_320;
    Boolean                           t323Valid;
    UrrTimerConstStatus               t_323_status;
    Int16                             t_323;
}
UrrTimers;

typedef struct UrrConstsTag
{
    UrrTimerConstStatus               n_300_status;
    UN_300                            n_300;
    UrrTimerConstStatus               n_301_status;
    UN_301                            n_301;
    UrrTimerConstStatus               n_302_status;
    UN_302                            n_302;
    UrrTimerConstStatus               n_304_status;
    UN_304                            n_304;
    UrrTimerConstStatus               n_310_status;
    UN_310                            n_310;
    UrrTimerConstStatus               idle_n_312_status;
    Int16                             idle_n_312;
    UrrTimerConstStatus               conn_n_312_status;
    Int16                             conn_n_312;
    UrrTimerConstStatus               n_312_status;
    Int16                             n_312;
    UrrTimerConstStatus               n_313_status;
    UN_313                            n_313;
    UrrTimerConstStatus               n_315_status;
    Int16                             n_315;
}
UrrConsts;

#if defined (ENABLE_WAIT_ABORT_SEARCH_CNF)
typedef enum UWaitAbortSearchCnfTag
{
    WAIT_ABORT_SEARCH_CNF_NO_WAIT        = 0,
    WAIT_ABORT_SEARCH_CNF_RELEASE_MSG    = 1,
    WAIT_ABORT_SEARCH_CNF_RRC_EST_CSCALL = 2,
    WAIT_ABORT_SEARCH_CNF_RRC_RESUME_REQ = 3,
    /* ********** added begin */
    WAIT_ABORT_SEARCH_CNF_BCCH_MODIFY     = 4,
    WAIT_ABORT_SEARCH_CNF_AIS_SEND_CU    = 5
    /* ********** added end */
}
UWaitAbortSearchCnf;
#endif /* ENABLE_WAIT_ABORT_SEARCH_CNF */

typedef struct RadioResourceControlEntityTag
{
    UrrDebugFlags               debugFlags;
    UrrExternalSignalBuffers    signal;

    KiUnitQueue                 internalSignalsQueue;
    UrrSubProcessId             currentSubProcess;
    UrrUeIdentity               ueId;
    Int16                       accessClass;
    UGSM_Classmark2             gsmClassmark2;      /* stores GSM classmark 2 */
    UGSM_Classmark3             gsmClassmark3;      /* stores GSM classmark 3 */
    Boolean                     usimPresent;
	Boolean 					isTestSim;
    UrrTimers                   timers;             /* global timer values */
    UrrConsts                   consts;             /* global constant values */

#if 0 /*CQ00113219 remove*/
#ifndef IRAT_MAX_UECAP_LEN //jpingma for HAW UT TBD
#define IRAT_MAX_UECAP_LEN      640                 /*CQ00096651 change to 512*/ /*CQ00104580 change to 640*/
#endif
#endif
    Int16                       lteCaplength; /*CQ00104580 change to Int16*/
    Int8                        lteueCapByteString[IRAT_MAX_UECAP_LEN];

    NetworkMode                 networkMode;

    /* Flag to enable debug signals for Air interface signals */
    Boolean                     debugAirInterfaceInfo;
#if defined (DEVELOPMENT_VERSION)
    URrcMscDebug                debugSendMscInfo;   /* control MSC info  */
#endif
    /* flag to allow integrity protection to be disabled */
    Boolean                     integrityProtectionDisabled;

#if defined (ON_PC)
    Boolean                     ptkCq89863Recur;
    Boolean                     disableSetNetworkmode;
#endif/*ON_PC*/
    Int8                        testT305value;      /* override value for testing */
    Boolean                     umtsOnlyMode;       /* Single mode UMTS */

	Boolean						dataIndicationRequested; /* Flag if we need to update MM end of data session */

    /*CQ00040121 add begin*/
    Boolean                     isPcsNWPresent;
    Boolean                     isPcsNW;
    /*CQ00040121 add begin*/
#if defined (UPGRADE_DSDS)
    Boolean                     suspendFlag;        /* for CQ00009240 suspend due to service of GSM */
    Boolean                     suspendDuePSFlag;   /* for CQ00012560 suspend due to PS service of GSM */
#if defined(ENABLE_WB_PCH_SIM2_MEASUREMENT)
    Boolean                     resumeDuePch;
#endif/*ENABLE_WB_PCH_SIM2_MEASUREMENT*/
#if !defined(UPGRADE_COMMON_STORE)
        Boolean                     sendCphyResumeReq;  /* Add for CQ00009240 */
#endif/*UPGRADE_COMMON_STORE*/
    Boolean                     sentCphyDsCtrlPchOn;
#if !defined(UPGRADE_COMMON_STORE)
    Boolean                     waitGrrrcResumeCnf; /* for CQ00014518 */
#endif/*UPGRADE_COMMON_STORE*/
    Boolean                     receivedRrcDeactReqWaitResume;  /* for CQ00015480 */
    Boolean                     grrRcSuspendCnfDelayed;/* CQ00027246 */
    Int8                        delayedReasonBits;/* CQ00094253 added */
    Boolean                     csCallRequested;       /*CQ00021272*/
    Boolean                     grrRcAbortPsReqSent;   /*CQ00024790*/
    Boolean                     isPagingFailureInProgress;/*CQ00031245*/
    Boolean                     sentCphyResumeByPch;   /* CQ00038099, added */
    Boolean                     cancelServiceReqReceived;   /* CQ00083411 add */
    Boolean                     waitForResumeByPchFinish; //CQ00111275 added
#if defined(UPGRADE_COMMON_STORE)
    Boolean                     hasSendIratSuspendReq;
#endif/*UPGRADE_COMMON_STORE*/
#if defined (UPGRADE_PLMS)
#if defined(UPGRADE_COMMON_STORE)
    IratSuspendCause            ongoingSim2cause; /*CQ00070208 add */
#else
    GrrRcSuspendCause           ongoingSim2cause; /*CQ00070208 add */
#endif/*UPGRADE_COMMON_STORE*/
#endif

#if defined (ENABLE_WAIT_ABORT_SEARCH_CNF)
    /* indicate RRC sub-task is waiting for iratDsAbortSearchCnf or not  */
    UWaitAbortSearchCnf         waitAbortSearchCnf;
#endif /* ENABLE_WAIT_ABORT_SEARCH_CNF */
/* CQ00111360 added begin */
#if defined(UPGRADE_DSDSWB)
    Boolean           pendingRrcResumeReq;
    Boolean           pendingResumeByPchReq; //CQ00111565 added
#endif  /*UPGRADE_DSDSWB*/
/* CQ00111360 added end */
#endif/*UPGRADE_DSDS*/
}
RadioResourceControlEntity;

#if defined(UPGRADE_DSDSWB)
extern RadioResourceControlEntity urr1[URRC_TASK_NUM];
#define urr urr1[URRC_DSDS_TASK]
/* CQ00108426 added begin */
//#if defined (ENABLE_URRC_UNIT_TESTING)
extern Int8 rrcActiveSim;
//#endif
/* CQ00108426 added end */
#else /*UPGRADE_DSDSWB*/
extern RadioResourceControlEntity urr;
#endif  /*UPGRADE_DSDSWB*/

/* CQ00115870 added begin */
#if defined(UPGRADE_DSDSWB)
extern struct UEngModeInfoTag   gEngModeInfo1[URRC_TASK_NUM];
#define gEngModeInfo gEngModeInfo1[URRC_DSDS_TASK]
#else/*UPGRADE_DSDSWB*/
extern struct UEngModeInfoTag  gEngModeInfo;
#endif/*UPGRADE_DSDSWB*/
/* CQ00115870 added end */

#if !defined(UPGRADE_COMMON_STORE)
/* Add for CQ00009240 begin*/
#if defined (UPGRADE_DSDS) && defined(ON_PC)
Boolean grrRcReselecting;
Boolean grrRcIRatReselecting;
#endif
#if defined (UPGRADE_DSDS)
extern Boolean rrcActive;     /* active mode is GRR or RRC */
#endif
/* Add for CQ00009240 end*/
#endif/*UPGRADE_COMMON_STORE*/

/*CQ00075122 add begin*/
#if !defined (ON_PC) || defined (PC_TTCN_INT_TEST )  
extern Boolean abcfVendorSpecificInfoAuto3GFRSupportEnabled;// CQ00078494 changed name
#endif
/*CQ00075122 add end*/


#if !defined (ON_PC) || defined (PC_TTCN_INT_TEST ) 
extern Boolean abcfSpecificInfoIOtTestEnabled; 
//#if defined (UPGRADE_DSDS)
extern Boolean abcfVendorSpecificInfoAttEnabled;
//#endif
extern Boolean abccIsInCall (Int8 SimId); /*CQ00053267 add*//*CQ00096031 add SimId*/
extern Boolean SSIsRunning(Int8 SimId); /*CQ00075037 add*/ /* CQ00096943 add SimId*/

extern Boolean abcfSpecificInfoDeutsheTeleEnabled;/*CQ00085294 add*/

extern Boolean abccCheckCurrentIsEccCall(Int8 SimId); /*CQ00097566 add*/
extern Int8 get_commonReserved_value(Int8 positionToGet,Int8 SimId); //CQ00115870 added //CQ00116899 add simid
extern Int8 get_commonReserved_1_value(Int8 positionToGet,Int8 SimId); //CQ00116233 added //CQ00116899 add simid
#endif 

/***************************************************************************
 * Typed Constants
 ***************************************************************************/
/*********** START */
#endif //!defined ERRC_UNIT_TEST
/*********** END */
#endif
/* END OF FILE */

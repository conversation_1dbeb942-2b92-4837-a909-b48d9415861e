#ifndef UMACE_DEBUG_INFO_H
#define UMACE_DEBUG_INFO_H

#include "tmm.h"
#include <umaecpc.h>

#define FT_TRANSMIT    (0)
#define FT_SGU          (1)
#define FT_SITRIG       (2)
#define FT_HAPPYBIT     (3)
#define FT_TFCSELECT    (4)
#define FT_CPC           (5)
#ifdef  maxE_DCHMACdFlow
	#define FT_MAX_TFCSELECT maxE_DCHMACdFlow   // (8)    //  maxE_DCHMACdFlow value is from rrcXXX_asn.h
#else
	#define FT_MAX_TFCSELECT  (8)    //  maxE_DCHMACdFlow value is from rrcXXX_asn.h
#endif
#ifdef UMACE_MAX_NUM_OF_HARQ_PROCESSES
	#define MAX_PROCESSES     UMACE_MAX_NUM_OF_HARQ_PROCESSES
#else
	#define MAX_PROCESSES (8)
#endif
#define FT_MAX  (FT_TFCSELECT + FT_MAX_TFCSELECT)
#define MAX_DEBUG_LOGICAL_CHANNEL (4)  //At the moment it is 4 should be UMAC_EDCH_MAX_LOGICAL_CHANNELS (15). lc identity is  (1-15)


//ICAT EXPORTED STRUCT
typedef struct UmacEdchLogicalChannelDebugRecordTag
{
	Int8	lcIdentity; //UlogicalChannelIdentity[1-15]  Remark to danny: we should save it otherwise the array of LC should be 14.
	Int8  	lcTsn;
	Int32 	lcBufferTxBits;  // buffer status
	Int16 	lcUserDefined16;
	Int32 	lcUserDefined32;
	//Int8  	lchUserDefined08[4];
}UmacEdchLogicalChannelDebugRecord;

//ICAT EXPORTED STRUCT
typedef struct UmacEdchDebugRecordTag
{
	Int8  processState; //0- dtx 1-initial tx 2- retransmission
	Int8  cfn;
	Int8  subFrame;
	Int8  processActive; //0-not active 1-active
	Int32 tebsBits; // umaEdchDb_g.siDb.tebsBits
	Int32 uph; //// umaEdchDb_g.siDb.uph
	Int32 error;//bitmask 
	Int32 ticksCounter;  //UmaEdchCfnManager

	Int16 servingGrantLimitBlockSize; //Bits
	Int16 etfcRestrictionBlockSize; //Bits  etfc table[restriction]
	Int16 transmittedTransportBlockSize; //Bits umaEdchDb_g.phyEdchTtiInfo.trBkSize
	Int8  servingRlsGrantStatus;			//0..3 : NO_RECEPTION(0) DOWN(1) HOLD(2) UP (3)
	Int8  nonServingRlsGrantStatus;			//0..3: NO_RECEPTION(0) DOWN(1) HOLD(2) UP (3)
	Int8  absoluteGrantValue; 				//0..37 
	Int8  absGrantScope; //1-current process  0-all processes
	Int8  erntiCrc;//0-none 1,3 primary 2-secondary
	Int32 txPower;
	Int8  happyBit;//umaEdchDb_g.phyEdchTtiInfo.happyBit
	Int8  currentServingGrant; 
	//	The index of the logical channel in the array is the number of the lc from the configuration ( not sorted by priority)
	UmacEdchLogicalChannelDebugRecord logicalChannelRecord [ MAX_DEBUG_LOGICAL_CHANNEL];
	//
	Int8            dtxReason;
	TmmPoolState    dlTmmPoolState;
	TmmPoolState    ulTmmPoolState;
	Int8            etfcRestrictionResultIndex;
	Int8            maxValidEtfciIndex;
	Int8            refEtfciIndex;  
	Int16           quantizationLoss;
	Int32           reference_ETPR;
	Int32           rlcTebsBits;
	Int32           si;
	Int32           nonScheduledPayload;
	Int32           siNoGrantTimerValue;
	UmaECpcDtxCycleStateEnum cpcState;

	//====================================================
	//from here the data is mace debug data
	//
	//mac-mace data
	Int16   cmGapsBitmap;
	Int8    numCmGaps;
	Int8    dchReadyToSend; 
	Int8    isSrbDelayPeriodOver;	
	//
	//Internal data
	//
	Int32 rxDataFormat[2];  //shmem RX
	Int32 flowTrace[FT_MAX]; 
} UmacEdchDebugRecord;

//ICAT EXPORTED STRUCT
typedef struct UmacEdchDebugTag
{
	Int8 tti;	//UmaEdchCfnManager 0-none,2, 10
	Int8 processIndex;	//UmaEdchCfnManager
	UmacEdchDebugRecord umacEdchDebugRecord[MAX_PROCESSES];
} UmacEdchDebug;

#endif //UMACE_DEBUG_INFO_H


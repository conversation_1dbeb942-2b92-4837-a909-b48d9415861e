/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/devices/transports/usb.mod/pub/src/usbdevreq.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/24 10:16:13 $
 **************************************************************************
 *  File Description :
 *    Data types etc for USB device requests.
 **************************************************************************/
#if defined(UPGRADE_USB)

#ifndef USBDEVREQ_H
#define USBDEVREQ_H

/*******************************************************************************
 * Manifest Constants
 ******************************************************************************/

#define USB_SETUP_BMREQUEST_INDEX                0
#define USB_SETUP_BREQUEST_INDEX                 1
#define USB_SETUP_WVALUE_INDEX                   2
#define USB_SETUP_DESCRIPTOR_INDEX_WVALUE_INDEX  2
#define USB_SETUP_DESCRIPTOR_TYPE_WVALUE_INDEX   3
#define USB_SETUP_WINDEX_INDEX                   4
#define USB_SETUP_WLENGTH_INDEX                  6

#define USB_SETUP_REQUEST_DIRECTION_MASK          0x80
#define USB_SETUP_REQUEST_DIRECTION_TO_HOST       0x80
#define USB_SETUP_REQUEST_DIRECTION_TO_DEVICE     0x00

#define USB_SETUP_REQUEST_TYPE_MASK               0x60
#define USB_SETUP_REQUEST_TYPE_STANDARD           0x00
#define USB_SETUP_REQUEST_TYPE_CLASS              0x20
#define USB_SETUP_REQUEST_TYPE_VENDOR             0x40
#define USB_SETUP_REQUEST_TYPE_RESERVED           0x60

#define USB_SETUP_REQUEST_RECIPIENT_MASK          0x1F
#define USB_SETUP_REQUEST_RECIPIENT_DEVICE        0x00
#define USB_SETUP_REQUEST_RECIPIENT_INTERFACE     0x01
#define USB_SETUP_REQUEST_RECIPIENT_ENDPOINT      0x02
#define USB_SETUP_REQUEST_RECIPIENT_OTHER         0x03

/*******************************************************************************
 * Types used in Prototypes and Globals
 ******************************************************************************/

/* Standard request codes as defined by the USB Specication. */
typedef enum UsbStandardRequestCodesTag
{
  USB_STANDARD_REQUEST_GET_STATUS           = 0,
  USB_STANDARD_REQUEST_CLEAR_FEATURE        = 1,
  USB_STANDARD_REQUEST_RESERVED_2           = 2,
  USB_STANDARD_REQUEST_SET_FEATURE          = 3,
  USB_STANDARD_REQUEST_RESERVED_4           = 4,
  USB_STANDARD_REQUEST_SET_ADDRESS          = 5,
  USB_STANDARD_REQUEST_GET_DESCRIPTOR       = 6,
  USB_STANDARD_REQUEST_SET_DESCRIPTOR       = 7,
  USB_STANDARD_REQUEST_GET_CONFIGURATION    = 8,
  USB_STANDARD_REQUEST_SET_CONFIGURATION    = 9,
  USB_STANDARD_REQUEST_GET_INTERFACE        = 10,
  USB_STANDARD_REQUEST_SET_INTERFACE        = 11,
  USB_STANDARD_REQUEST_SYNCH_FRAME          = 12
} UsbStandardRequestCodes;

/* Standard feature selectors as defined by the USB Specication. */
typedef enum UsbStandardFeatureSelectorsTag
{
  USB_STANDARD_FEATURE_DEVICE_REMOTE_WAKEUP   = 1,
  USB_STANDARD_FEATURE_ENDPOINT_HALT          = 0
} UsbStandardFeatureSelectors;

/* Validity indicator assigned to device requests. */
typedef enum UsbDeviceRequestValidityTag
{
  USB_DEVICE_REQUEST_VALID,
  USB_DEVICE_REQUEST_BEHAVIOUR_NOT_SPECIFIED,
  USB_DEVICE_REQUEST_REQUEST_ERROR
} UsbDeviceRequestValidity;

/* Parameters in the bmRequestType field. */
typedef struct UsbbmRequestTypeFieldsTag
{
  Int8  dataTransferDirection;
  Int8  type;
  Int8  recipient;
} UsbbmRequestTypeFields;

/* Fields in a SETUP packet. */
typedef struct UsbSetupPacketFieldsTag
{
  Int8                    bmRequestType;
  UsbbmRequestTypeFields  bmRequestTypeFields;
  Int8                    bRequest;
  Int16                   wValue;
  Int16                   wIndex;
  Int16                   wLength;
} UsbSetupPacketFields;


/*******************************************************************************
 * Macros
 ******************************************************************************/

/* Gets the bmRequestType field from a SETUP packet. */
#define USB_SETUP_GET_BMREQUEST(pACKET)                                           \
    ((pACKET)[USB_SETUP_BMREQUEST_INDEX])

/* Gets the data transfer direction from the bmRequestType field. */
#define USB_SETUP_TRANSFER_IS_TO_HOST(pACKET)                                     \
    (((pACKET)[USB_SETUP_BMREQUEST_INDEX] & USB_SETUP_REQUEST_DIRECTION_MASK) !=  \
                   USB_SETUP_REQUEST_DIRECTION_TO_DEVICE)

/* Non-zero if device request data transfer is host-to-device. */
#define USB_SETUP_TRANSFER_IS_TO_DEVICE(pACKET)                                   \
    (((pACKET)[USB_SETUP_BMREQUEST_INDEX] & USB_SETUP_REQUEST_DIRECTION_MASK) !=  \
                   USB_SETUP_REQUEST_DIRECTION_TO_HOST)

/* Gets the request type from the bmRequestType field. */
#define USB_SETUP_GET_REQUEST_TYPE(pACKET)                                        \
    ((pACKET)[USB_SETUP_BMREQUEST_INDEX] & USB_SETUP_REQUEST_TYPE_MASK)

/* Gets the recipient from the bmRequestType field. */
#define USB_SETUP_GET_REQUEST_RECIPIENT(pACKET)                                   \
    ((pACKET)[USB_SETUP_BMREQUEST_INDEX] & USB_SETUP_REQUEST_RECIPIENT_MASK)

/* Gets the bmRequest field from a SETUP packet. */
#define USB_SETUP_GET_BREQUEST(pACKET)                                            \
    ((pACKET)[USB_SETUP_BREQUEST_INDEX])

/* Gets the wValue field from a SETUP packet. */
#define USB_SETUP_GET_WVALUE(pACKET)                                              \
    ((Int16) (((Int16) ((pACKET)[USB_SETUP_WVALUE_INDEX])) +                      \
              (((Int16) ((pACKET)[USB_SETUP_WVALUE_INDEX + 1])) << 8)))

/* Gets the wIndex field from a SETUP packet. */
#define USB_SETUP_GET_WINDEX(pACKET)                                              \
    ((Int16) (((Int16) ((pACKET)[USB_SETUP_WINDEX_INDEX])) +                      \
              (((Int16) ((pACKET)[USB_SETUP_WINDEX_INDEX + 1])) << 8)))

/* Gets an endpoint address from the wIndex field. */
#define USB_SETUP_GET_ENDPOINT_ADDRESS(pACKET)                                    \
    ((Int16) (USB_SETUP_GET_WINDEX(pACKET)))

/* Gets an interface alternate from the wValue field. */
#define USB_SETUP_GET_ALTERNATE(pACKET)                                           \
    ((Int16) (USB_SETUP_GET_WVALUE(pACKET)))

/* Gets a configuration from the wIndex field. */
#define USB_SETUP_GET_CONFIGURATION(pACKET)                                       \
    ((Int16) (USB_SETUP_GET_WINDEX(pACKET)))

/* Gets a descriptor index from the wValue field. */
#define USB_SETUP_GET_DESCRIPTOR_INDEX(pACKET)                                    \
    ((pACKET)[USB_SETUP_DESCRIPTOR_INDEX_WVALUE_INDEX])

/* Gets a descriptor type from the wValue field. */
#define USB_SETUP_GET_DESCRIPTOR_TYPE(pACKET)                                     \
    ((pACKET)[USB_SETUP_DESCRIPTOR_TYPE_WVALUE_INDEX])

/* Gets an interface from the wIndex field. */
#define USB_SETUP_GET_INTERFACE(pACKET)                                           \
    ((Int16) (USB_SETUP_GET_WINDEX(pACKET)))

/* Gets the wLength field from a SETUP packet. */
#define USB_SETUP_GET_WLENGTH(pACKET)                                             \
    ((Int16) (((Int16) ((pACKET)[USB_SETUP_WLENGTH_INDEX])) +                     \
              (((Int16) ((pACKET)[USB_SETUP_WLENGTH_INDEX + 1])) << 8)))

/* Expands a SETUP packet in to its constituent fields. */
#define USB_SETUP_EXTRACT_FIELDS(pACKET, fIELDS)                                  \
    fIELDS.bmRequestType = USB_SETUP_GET_BMREQUEST(pACKET);                       \
    fIELDS.bmRequestTypeFields.type =                                             \
                          (fIELDS.bmRequestType                                   \
                           & USB_SETUP_REQUEST_TYPE_MASK);                        \
    fIELDS.bmRequestTypeFields.dataTransferDirection =                            \
                          (fIELDS.bmRequestType                                   \
                           & USB_SETUP_REQUEST_DIRECTION_MASK);                   \
    fIELDS.bmRequestTypeFields.recipient =                                        \
                          (fIELDS.bmRequestType                                   \
                           & USB_SETUP_REQUEST_RECIPIENT_MASK);                   \
    fIELDS.bRequest      = USB_SETUP_GET_BREQUEST(pACKET);                        \
    fIELDS.wValue        = USB_SETUP_GET_WVALUE(pACKET);                          \
    fIELDS.wIndex        = USB_SETUP_GET_WINDEX(pACKET);                          \
    fIELDS.wLength       = USB_SETUP_GET_WLENGTH(pACKET)

/***************************************************************************
 * Functions internal to the Stack used by DeviceRequest handlers, which
 * may be client modules so these need to be exported.
 ***************************************************************************/

extern void usbDclControlTransferStatusStage( Int8 logicalEndpoint );
extern void usbDclEnableEndpointZeroEvents( void );


#endif /* (USBDEVREQ_H) */
#endif /* (UPGRADE_USB) */
/* END OF FILE */

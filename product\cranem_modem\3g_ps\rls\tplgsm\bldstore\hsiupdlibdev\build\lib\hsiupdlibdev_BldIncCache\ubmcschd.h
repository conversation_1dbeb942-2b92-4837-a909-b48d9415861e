/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/3g.mod/api/shinc/ubmcSchd.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2007/01/12 13:10:41 $
 **************************************************************************
 *  File Description :
 *      types used in CB short message module
 **************************************************************************/
#if !defined(DS3_LTE_ONLY)

#if !defined (UBMCSCHD_H)
#define       UBMCSCHD_H

/***************************************************************************
 * Nested Include Files
 ***************************************************************************/

#include <system.h>


/**** CONSTANTS ************************************************************/



#define UBMC_MAX_CBS_MSG_SIZE  (1+1+1+32+255) /* = 290 Bytes in case of SM*/
#define UBMC_MAX_CBS_SCHEDULE_PERIOD  256
#define UBMC_MAX_STORE_MAPS 1    /*we need to store the next schedule period
                                   map while still using the current one*/


/**** DATA STRUCTURES ******************************************************/

/*25.324, 11.9*/

//ICAT EXPORTED ENUM
typedef enum MsgDescripTypeTag
{
    UBMC_NEW_REPETITION,    /* 0 Repetition of new BMC CBS message within schedule period*/
    UBMC_NEW_MESSAGE,       /* 1 New BMC CBS message (a BMC CBS message never previously sent)*/
    UBMC_READING_ADVISED,   /* 2 Reading advised*/
    UBMC_READING_OPTIONAL,  /* 3 Reading optional*/
    UBMC_OLD_REPETITION,    /* 4 Repetition of old BMC CBS message within schedule period*/
    UBMC_OLD_MESSAGE,       /* 5 Old BMC CBS message (repetition of a BMC CBS message sent in a previous schedule period)*/
    UBMC_SCHEDULE_MESSAGE,  /* 6 Schedule message*/
    UBMC_CBS41_MESSAGE,     /* 7 CBS41 message*/
    UBMC_NO_MESSAGE         /* 8 no message*/
} MsgDescripType;

//ICAT EXPORTED STRUCT
typedef struct ScheduleCTCHBlockSetTag
{
    Int16           messageType; /* message Id */
    MsgDescripType  msgDescripType;
    Boolean         messageIsNew;
    Boolean         messageIsWanted;
    Boolean         messageIsaRepeat;
    Int8            originalMsgCtchBsIx;
}
ScheduleCTCHBlockSet;

//ICAT EXPORTED STRUCT
typedef struct UbmcScheduleMapTag
{
    ScheduleCTCHBlockSet    elements[UBMC_MAX_CBS_SCHEDULE_PERIOD];
    Int8                    cbsSchedulePeriodLen;
    Int8                    bsIndexOffset;
    Int16                   firstCtchBsSfn;
    Int16                   lastWantedSfn;
    Int16                   firstWantedSfn;
    Int16                   referenceSfn;
}
UbmcScheduleMap ;

//ICAT EXPORTED STRUCT
typedef struct UbmcMessageDebugTag
{
    Int8 msg[UBMC_MAX_CBS_MSG_SIZE];
}
UbmcMessageDebug ;


#endif
#endif
/* END OF FILE */

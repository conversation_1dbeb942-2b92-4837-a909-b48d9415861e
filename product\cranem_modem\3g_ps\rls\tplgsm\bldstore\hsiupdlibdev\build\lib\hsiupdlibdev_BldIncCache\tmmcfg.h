/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/***************************************************************************
 * TTPCom Software Copyright (c) TTP Communications Ltd 1997-2004
 ***************************************************************************
 * $Id: //central/main/wsd/sys/tmm.mod/pub/src/tmmcfg.h#1 $
 * $Revision: #1 $
 * $DateTime: 2006/10/24 11:37:14 $
 ***************************************************************************
 * File Description: Traffic Memory Manager. Memory Manager abstraction
 *************************************************************************/

#ifndef TMMCFG_H
#define TMMCFG_H

/***************************************************************************
 * Nested Include Files
 **************************************************************************/
#include <tmm.h>
#include <kicachedep.h>

#if defined (ENABLE_TMM)

#if defined (KI_USE_FAST_MEMORY_POOL)
#define KI_FAST_MEMORY_POOL_BYTE_SIZE	(0x2000)	
#endif /* KI_USE_FAST_MEMORY_POOL */

#if defined (KI_USE_NON_CACHED_DL_POOL)
/* assuming 3G mapped pool size has the biggest pool size */
#define KI_NON_CACHE_DL_BUFFER_BYTE_SIZE    (TMM_3G_DYN_DL_POOL_SIZE_BYTES + TMM_3G_TMDL_POOL_SIZE_BYTES)
#else
#define KI_NON_CACHE_DL_BUFFER_BYTE_SIZE    (0)
#endif /* KI_USE_NON_CACHED_DL_POOL */

#if defined (KI_USE_NON_CACHED_UL_POOL)
/* assuming 3G mapped pool size has the biggest pool size */
#define KI_NON_CACHE_UL_BUFFER_BYTE_SIZE    (TMM_3G_DYN_UL_POOL_SIZE_BYTES)
#else
#define KI_NON_CACHE_UL_BUFFER_BYTE_SIZE    (0)
#endif /* KI_USE_NON_CACHED_UL_POOL */


/***************************************************************************
 *  Function Prototypes
 **************************************************************************/
extern Boolean TmmCfgAllocMemory (TmmMemoryManagerId memMgrId, void **mem_pp, Int32 requestedBytes, Int32 callerAddress);
extern void TmmCfgFreeMemory (TmmMemoryManagerId memMgrId, void **mem_pp);
extern Boolean TmmCfgResizeMemory (TmmMemoryManagerId memMgrId, void **mem_pp, Int32 newLength);
extern Boolean TmmCfgSetCurrentProfile (TmmProfileData *profData_p, TmmProfileId profileId);
extern void TmmCfgInitOsaMemoryPools(void);

#endif /* ENABLE_TMM */
#endif /* TMMCFG_H */

/* END OF FILE */

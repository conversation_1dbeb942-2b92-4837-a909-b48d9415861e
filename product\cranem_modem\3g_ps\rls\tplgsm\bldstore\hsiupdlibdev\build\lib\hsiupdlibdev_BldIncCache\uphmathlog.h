/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/phy/3g.mod/api/inc/uphmathlog.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/11/09 16:04:03 $
 **************************************************************************
 * File Description: This header file contains mathematical macros, constants
 * and function declarations required by actions in the 3G PHY.
 **************************************************************************/

#ifndef UPHMATH_H
#define UPHMATH_H

/*******************************************************************************
** Includes
*******************************************************************************/

#include <system.h>

/*******************************************************************************
** Constants
*******************************************************************************/

/*******************************************************************************
* Define      : UPH_DB2LIN_TABLE_SIZE
* Group       : UPH
* Description : Size of db2lin table in uphDb2Lin
* function.
*******************************************************************************/
#define UPH_DB2LIN_TABLE_SIZE ((Int16)(14))

/*******************************************************************************
* Define      : UPH_DB2LIN_TABLE_MAXVAL
* Group       : UPH
* Description : Maximum input that has value stored in db2lin table in uphDb2Lin
* function.
*******************************************************************************/
#define UPH_DB2LIN_TABLE_MAXVAL ((Int16)(13))

/*******************************************************************************
* Define      : UPH_DB2LIN_TABLE_MINVAL
* Group       : UPH
* Description : Minimum input that has value stored in db2lin table in uphDb2Lin
*  function.
*******************************************************************************/
#define UPH_DB2LIN_TABLE_MINVAL ((Int16)(0))

/*******************************************************************************
* Define      : UPH_DB2LIN_BITS
* Group       : UPH
* Description : Number of bits to drop from lin result plus interpolation offset
* in uphDb2Lin function.
*******************************************************************************/
#define UPH_DB2LIN_BITS ((Int16)(10))

/*******************************************************************************
* Define      : UPH_DB2LIN_MAXINPUT
* Group       : UPH
* Description : Maximum allowed input at 6 bit fractional part in uphDb2Lin
* function.
*******************************************************************************/
#define UPH_DB2LIN_MAXINPUT ((Int16)(49312))

/*******************************************************************************
* Define      : UPH_DB2LIN_MAXVAL
* Group       : UPH
* Description : Maximum output value in uphDb2Lin function.
*******************************************************************************/
#define UPH_DB2LIN_MAXVAL ((Int16)(65535))

/*******************************************************************************
* Define      : UPH_DB2LIN_TABLE_OFFSET
* Group       : UPH
* Description : Offset on top of all values stored in db2lin table in uphDb2Lin
* function.
*******************************************************************************/
#define UPH_DB2LIN_TABLE_OFFSET ((Int16)(32768))

/*******************************************************************************
* Define      : UPH_LIN2DB_TABLE_SIZE
* Group       : UPH
* Description : Size of lin2db table in uphDb2Lin function.
*******************************************************************************/
#define UPH_LIN2DB_TABLE_SIZE ((Int16)(17))

/*******************************************************************************
* Define      : UPH_LIN2DB_TABLE_MINVAL
* Group       : UPH
* Description : Minimum input that has log value stored in lin2db table in
* uphDb2Lin function.
*******************************************************************************/
#define UPH_LIN2DB_TABLE_MINVAL ((Int16)(16))

/*******************************************************************************
* Define      : UPH_LIN2DB_TABLE_OFFSET
* Group       : UPH
* Description : offset on top of all values stored in lin2db table in uphDb2Lin
* function.
*******************************************************************************/
#define UPH_LIN2DB_TABLE_OFFSET ((Int16)(46245))

/*******************************************************************************
* Define      : UPH_LIN2DB_BITS
* Group       : UPH
* Description : Number of bits to drop from log result to get 1 db accuracy
*  plus interpolation offset in uphDb2Lin function.
*******************************************************************************/
#define UPH_LIN2DB_BITS ((Int16)(9))

/*******************************************************************************
* Define      : UPH_LIN2DB_3DB_MULT
* Group       : UPH
* Description : 3 dB multiplier - i.e. (10 * log10 (2) * 1024)
*******************************************************************************/
#define UPH_LIN2DB_3DB_MULT ((Int16)(3083))

/*******************************************************************************
* Define      : UPH_DB_MINUS_INF
* Group       : UPH
* Description : Minus infinite expressed in dB.
*******************************************************************************/
#define UPH_DB_MINUS_INF  ((Int16)( -16384))

/*******************************************************************************
* Define      : UPH_DB_UNIT_BIT_POS
* Group       : UPH
* Description : Bit position in the output beyond the decimal comma. Used in
* linear to dB conversion.
*******************************************************************************/
#define UPH_DB_UNIT_BIT_POS             (5)

/*******************************************************************************
* Define      : UPH_DB_PER_SHIFT_FACTOR
* Group       : UPH
* Description : Used to calculate 10 * log10 (2^n) in the linear to dB
*               conversion.
*******************************************************************************/
#define UPH_DB_PER_SHIFT_FACTOR                 (771)

/*******************************************************************************
* Define      : UPH_DB_PER_SHIFT_SHIFT
* Group       : UPH
* Description : Used to calculate 10 * log10 (2^n) in the linear to dB
*               conversion.
*******************************************************************************/
#define UPH_DB_PER_SHIFT_SHIFT                  (3)

/*******************************************************************************
** Macros
*******************************************************************************/

/*******************************************************************************
** Typedefs
*******************************************************************************/

/*******************************************************************************
** Global Data
*******************************************************************************/

/*******************************************************************************
** Global Function Prototypes
*******************************************************************************/

Int16 uphFindMsbInt32 (
    const Int32);

Int16 uphFindMsbInt16 (
          Int16);

Int16 uphFindLsbInt16 (
          Int16);

SignedInt16 uphLinTodBInt16 (
    const Int16,
    const SignedInt16);

SignedInt16 uphLinTodBInt32 (
    const Int32,
    const SignedInt16);





     
void uphAddLogInLin (
          SignedInt16*,
          SignedInt16,
          Int16);
      

SignedInt16 uphLin2dB (
          Int16,
          SignedInt16,
          SignedInt16);

Int16 uphdB2Lin (
          SignedInt16,
          SignedInt16,
          SignedInt16);

#endif
/* END OF FILE */

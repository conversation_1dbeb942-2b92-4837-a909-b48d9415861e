/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/api/inc/ucsamrmo.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/27 15:10:43 $
 **************************************************************************/
/**
 * \file
 * Types and function prototypes for AMR mode and rate conversion utilities.
 */

#if !defined (UCSAMRMO_H)
#define       UCSAMRMO_H

/***************************************************************************
* Nested Include Files
***************************************************************************/
#include <auc_ps_if.h>
#include <ccsd_sig.h>

/***************************************************************************
*   Manifest Constants
***************************************************************************/

/***************************************************************************
*   Macro Functions
***************************************************************************/

/***************************************************************************
*   Types
***************************************************************************/

/** Values are 0..8 as specified in 26.101-320; mode 0 is 4.75 kbit/s,
 *  mode 7 is 12.2 kbit/s; mode 8 is a SID frame. */
typedef Int8 UcsAmrMode;

/***************************************************************************
*   Function declarations
***************************************************************************/



/** Convert a rate (number of bits per class) to a RateIndex according to vocoder type .
 * If the conversion succeeds,
 * the index(0..9) is returned
 * else RATE_SPEECH_LOST(14) is returned.
 */
void UcsAmrCsRateToMode (
  CcsdCsRate  csRate,     /**< [in] The AMR rate definition. */
  UcsAmrMode *amrMode_p,  /**< [out] An AMR mode. */
  Boolean    *found_p     /**< [out] TRUE if conversion succeeded, FALSE otherwise. */
);


Int8 UcsAmrCsRateToRateIndex (Int16 *bitsPerTti,VocoderType vt);

/** Convert a RateIndex to rate (number of bits per class) to a RateIndex according to vocoder type.
 * If the index (0..9) the rate is 0 for each bit type
 * else the table bits is fill in rate.
 */

void UcsAmrRateIndexToCsRate (VocoderType vt,Int8 rateIndex, Int16 *bitsPerTti  );


/** Given a particular number of class B bits, this function specifies if
 * any class C bits are to be included.  If an invalid number of class B bits is
 * passed then the result is meaningless.
 *
 * \return TRUE if class B bits are required, FALSE otherwise. Valid for AMR only
 */
Boolean UcsAmrBBitsNeedCBits (
  Int16 numBBits          /**< [in] Number of class B bits. */
  
);
/** Return rate table size (number of entries according to vocoder type
 */
  Int8 UcsAmrGetRateTableSize(VocoderType vt);

CsCodecType UcsAmrGetCodecType (CcsdCsRate rate);

#endif

/* END OF FILE */

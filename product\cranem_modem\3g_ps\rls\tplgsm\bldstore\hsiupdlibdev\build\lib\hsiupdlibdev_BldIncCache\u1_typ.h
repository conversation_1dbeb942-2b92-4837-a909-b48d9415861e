/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/l1/3g.typ/api/inc/u1_typ.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2007/03/30 13:53:25 $
 *************************************************************************/
/** \file
 * 3G L1 Primitive Data Type definitions for the UPHY Interface
 *************************************************************************/

#if !defined (U1_TYP_H)
#define       U1_TYP_H

#include <system.h>
#include <u1_cfg.h>
#include <uas_asn.h>
#if defined(UMTS7_PRE_R8)
#include <bearer_sig.h>
#endif
/***************************************************************************
* Nested Include Files
***************************************************************************/


/***************************************************************************
*   Macro Functions
***************************************************************************/

/** Defines the maximum number of octets required to hold an uplink transport
 * block list, allowing optional alignment padding per transport block, to
 * ensure that the start of each transport block is word aligned, so that the
 * physical layer can avoid bit copies.
 */
#define UUL_MAX_TB_LIST_DATA_OCTETS     \
    (BITS_TO_INT8S(UPS_MAX_UL_TB_BITS) + \
     (UPS_TRANSPORT_BLOCK_ALIGNMENT_IN_OCTETS * \
        UPS_MAX_UL_NO_OF_TB_PER_TTI \
      ) \
    )

/** Defines the maximum number of octets required to hold a downlink transport
 * block list, allowing optional alignment padding per transport block, to
 * ensure that the start of each transport block is word aligned, so that the
 * physical layer can avoid bit copies. Note that in DL we also add CRC bits
 * per TB, as in test loop mode 2 these may be appended to each TB.
 */
#define UDL_MAX_TB_LIST_DATA_OCTETS     \
    (BITS_TO_INT8S(UPS_MAX_DL_TB_BITS) + \
     ((UPS_TRANSPORT_BLOCK_ALIGNMENT_IN_OCTETS + \
       UPS_MAX_CRC_SIZE_IN_OCTETS) * \
        UPS_MAX_DL_NO_OF_TB_PER_TTI \
      ) \
    )

#if defined(UPGRADE_3G_RELEASE_5)
# if defined(UPGRADE_3G_HSDPA)
/** Defines the maximum number of MAC-hs PDUs in octets. */
#  define UDL_HS_MAX_MAC_HS_PDU_DATA_OCTETS  \
      (BITS_TO_INT8S(UPS_HS_DSCH_MAX_BITS_PER_TTI))
# endif /* UPGRADE_3G_HSDPA */
#endif /* UPGRADE_3G_RELEASE_5 */

/** Defines the maximum number of bearers which can be put into loopback mode.
 *  (Moved from Manifest Constant area so could be assigned to RB_NUM_BEARERS)
 */
#define UPS_MAX_LOOPBACK_BEARERS    RB_NUM_BEARERS

/***************************************************************************
*   Types
***************************************************************************/

#if !defined(UMTS7_PRE_R8)
/** Indicates the radio bearer identifier. RB1 to RB32 is configured by the
 * network.
 */
//ICAT EXPORTED ENUM
typedef enum BearerIdentityTag
{
    RB_0            =  0, /**< SRB0 on CCCH (UL:TM DL:UM) */
    RB_1            =  1, /**< SRB1 on (UL:UM DL:UM) */
    RB_2            =  2, /**< SRB2 on (UL:AM DL:AM) */
    /** SRB3 on (UL:AM DL:AM) for high priority NAS signalling */
    RB_3            =  3,
    /** SRB4 on (UL:AM DL:AM) for low priority NAS signalling, which is optional */
    RB_4            =  4,
    RB_5            =  5, /**< Radio Bearer 5, configured by the network. */
    RB_6            =  6, /**< Radio Bearer 6, configured by the network. */
    RB_7            =  7, /**< Radio Bearer 7, configured by the network. */
    RB_8            =  8, /**< Radio Bearer 8, configured by the network. */
    RB_9            =  9, /**< Radio Bearer 9, configured by the network. */
    RB_10           = 10, /**< Radio Bearer 10, configured by the network. */
    RB_11           = 11, /**< Radio Bearer 11, configured by the network. */
    RB_12           = 12, /**< Radio Bearer 12, configured by the network. */
    RB_13           = 13, /**< Radio Bearer 13, configured by the network. */
    RB_14           = 14, /**< Radio Bearer 14, configured by the network. */
    RB_15           = 15, /**< Radio Bearer 15, configured by the network. */
    RB_16           = 16, /**< Radio Bearer 16, configured by the network. */
    RB_17           = 17, /**< Radio Bearer 17, configured by the network. */
    RB_18           = 18, /**< Radio Bearer 18, configured by the network. */
    RB_19           = 19, /**< Radio Bearer 19, configured by the network. */
    RB_20           = 20, /**< Radio Bearer 20, configured by the network. */
    RB_21           = 21, /**< Radio Bearer 21, configured by the network. */
    RB_22           = 22, /**< Radio Bearer 22, configured by the network. */
    RB_23           = 23, /**< Radio Bearer 23, configured by the network. */
    RB_24           = 24, /**< Radio Bearer 24, configured by the network. */
    RB_25           = 25, /**< Radio Bearer 25, configured by the network. */
    RB_26           = 26, /**< Radio Bearer 26, configured by the network. */
    RB_27           = 27, /**< Radio Bearer 27, configured by the network. */
    RB_28           = 28, /**< Radio Bearer 28, configured by the network. */
    RB_29           = 29, /**< Radio Bearer 29, configured by the network. */
    RB_30           = 30, /**< Radio Bearer 30, configured by the network. */
    RB_31           = 31, /**< Radio Bearer 31, configured by the network. */
    RB_32           = 32, /**< Radio Bearer 32, configured by the network. */

    /** Internal Bearer for BCCH, which can't be referenced by the network. */
    RB_BCCH         = 33,
    /** Internal Bearer for PCCH, which can't be referenced by the network. */
    RB_PCCH         = 34,
    /** Internal Bearer for CTCH, which can't be referenced by the network. */
    RB_CTCH         = 35,

    RB_NUM_BEARERS  = 36, /**< Number of Radio Bearers */

    /** RB_INVALID is used by MAC to indicate a transport block whose CRC was
     * ok, but couldn't be parsed.
     */
    RB_INVALID      = 0xFF
}
BearerIdentity;
#endif
/** Defines GSM System information message type number.  */
typedef Int8        GsmControlBlockType;

/** Defines the GSM physical block as an array of octets. */
typedef Int8        GsmPhysicalBlock [CPHY_GSM_L1_PHYSICAL_BLOCK_SIZE];

/** <tt>GSM BSIC</tt> is used by UEs to distinguish different Base Stations, and
 * it is composed of 3-bit <tt>Network Colour Code (NCC)</tt> and 3-bit <tt>
 * Base station Colour Code (BCC)</tt>. <tt>NCC</tt> occupies the upper nibble
 * of an octet whereas the lower nibble is occupied by <tt>BCC</tt>.
 */
typedef Int8        GsmBsic;

/** Defines the RSSI (received signal strength) of the signal in GSM. */
typedef SignedInt16 GsmRssiLevel;

/** Defines the Level 2 scheduling information, which is also referred to as
 * <tt> inband scheduling message </tt>. This inband scheduling message is sent
 * on the CTCH together with the actual CB messages (as in CBS-GSM).
 * \note For more information refer to <tt> [3G TS 25.925 section 6.3.6.4]</tt>
 */
typedef struct UCBS_DRX_Level2InformationTag
{
    /** Indicates the length of the next period in Block Sets (BS)
     */
    Int8        nextPeriodLength; /* 1..255 - as per 25.324, 11.7 */

    /** Indicates the start of the next period relative to the reference Sfn
     */
    Int16       nextPeriodOffset; /* [frame] */

    /** Indicates the SFN of when the last BMC scheduled message was received
     * and is used as a reference point for the \c receptionPeriodOffset.
     * \note If the TTI used was greater than 10ms then the SFN corresponds to
     * the first frame number that this message was received in.
     */
    Int16   /* 0 to 4095 */  referenceSfn;

    /** Indicates the number of reception periods with in a CBS Schedule Period.
        0 - stop L2 scheduling
     */
    Int16   /* 0 to 256 */   numberOfReceptionPeriods;

    /** Indicates the CTCH-BS indexes to be decoded. This is known after
     * reading the BMC Schedule Message.
     */
    Int8   /* 0 to 255 */   receptionPeriodOffset [CBS_RECEPTION_PERIOD_OFFSET_ARRAY_SIZE];
}
UCBS_DRX_Level2Information;

/** Defines the activation time. */
//ICAT EXPORTED STRUCT
typedef struct ActivationTimeTag
{
    /** A boolean flag to determine if it is an immediate activation time (TRUE)
     * or at a certain CFN (FALSE).
     */
    Boolean                    now;

    /** If \c now = FALSE then the activation time is at a specified CFN. */
    UActivationTime            cfn;
}
ActivationTime;

/** Defines either a SFN or a CFN. */
typedef union SfnOrCfnTag
{
    /** This member indicates the System Frame Number. */
    Int16                      sfn;

    /** This member indicates the Connection Frame Number. */
    Int8                       cfn;
}
SfnOrCfn;

/** Defines the frame number in idle/connected state. */
typedef struct UFrameNumberTag
{
    /** A boolean flag to define if the frame number should be given in SFN/CFN.
     * If it is TRUE then the frame number shall be given in CFN, and if it
     * FALSe then the frame number shall be given in SFN.
     */
    Boolean                    connected;

    /** This member indicates the frame number in CFN/SFN. */
    SfnOrCfn                   sfnOrCfn;
}
UFrameNumber;

/** Indicates if the CRC of a received PDU was verified successfully or not.
 * It may also indicate is the associated structure is used or not.
 */
typedef enum UDlPduStatusTag
{
    /** CRC for this PDU okay. */
    UDL_PDU_STATUS_CRC_OK               = 0,
    /** CRC for this PDU failed. */
    UDL_PDU_STATUS_CRC_ERROR            = 1,
    /** Error at the MAC level. */
    UDL_PDU_STATUS_MAC_ERROR            = 2,
    /** Error for entire frame. */
    UDL_PDU_STATUS_MAC_NO_FRAME         = 3,

    /* The following values are used by RLC when it rejects a PDU in
     * UmacDlPduListInfoRsp by setting cipherInfo.dstData_p to NULL
     */

    /** Unknown bearer */
    UDL_PDU_STATUS_INVALID_BEARER       = 4,
    /** RLC in wrong state */
    UDL_PDU_STATUS_WRONG_STATE          = 5,
    /** Bearer stopped */
    UDL_PDU_STATUS_STOPPED              = 6,
    /** Bearer halted */
    UDL_PDU_STATUS_HALTED               = 7,
    /** Zero length PDU */
    UDL_PDU_STATUS_PDU_EMPTY            = 8,
    /** SN already available */
    UDL_PDU_STATUS_SN_DUPLICATE         = 9,
    /** SN outside RX window */
    UDL_PDU_STATUS_SN_OUTSIDE_WINDOW    = 10,
    /** Loop not closed on loopback bearer */
    UDL_PDU_STATUS_LOOP_NOT_CLOSED      = 11,
    /** Buffer full */
    UDL_PDU_STATUS_BUFFER_FULL          = 12,
    /** MAX SDU size exceeded */
    UDL_PDU_STATUS_MAX_SDU_EXCEEDED     = 13,
    /* Invalid PDU size */
    UDL_PDU_STATUS_SIZE_CHANGED         = 14,
    /* UM Out Of Sequence */
    UDL_PDU_STATUS_UM_OUT_OF_SEQUENCE   = 15,
    /* No SDU buffer */
    UDL_PDU_STATUS_NO_SDU_BUFFER        = 16,
    /* A buffer was allocated for the pdu in PduListInfoInd,
        *  However, a reset pdu preceded it and it was freed
        *  during the reset procedure while handling UmacDataInd */
    UDL_PDU_STATUS_DELETED_AFTER_RESET  = 17,
    /*Error RLC HEADER*/
	UDL_PDU_STATUS_ERROR_RLC_HEADER = 18,

    /** The associated structure is not being used. */
    UDL_PDU_STATUS_UNUSED
} UDlPduStatus;

/** Indicates the type of transport channel. */
typedef enum UTransportChannelTypeTag
{
    /** Broadcast Transport Channel */
    UBCH    = 0,
    /** Paging Transport Channel */
    UPCH    = 1,
    /** FACH Transport Channel */
    UFACH   = 2,
    /** Dedicated Transport Channel */
    UDCH    = 3,
    /** Random Access Tranport Channel */
    URACH   = 4,
    /** High Speed Downlink Shared Transport Channel */
    UHSDSCH = 5,
#if defined (UPGRADE_3G_EDCH)
    /** E-DCH Transport Channel */
    UEDCH = 6,
#endif /* UPGRADE_3G_EDCH */
    /** FACH transport Channel which carries CBS Data */
    UFACH_FOR_CBS = 7,
    /** Total number of transport channel types */
    NUM_TR_CHAN_TYPES
}
UTransportChannelType;

/** Defines the downlink pdu data as an array of octets to hold downlink
 * transport blocks.
* Indicates an array of data containing transport blocks. The size of the
 * array varies with the product data class and includes padding octet(s)
 * per transport block, to ensure that the start of each transport block is
 * word aligned at the UPHY interface according to the configurable
 * parameter \c TRANSPORT_BLOCK_ALIGNMENT_IN_OCTETS.
 *
 * <TABLE>
 * <TR><TD><B>Data Class (kbps)</B></TD> <TD><B>Max bits per TTI</B></TD> <TD><B>Max blocks per TTI</B></TD> <TD><B>Padding bits</B></TD> <TD><B>Total bits</B></TD></TR>
 * <TR><TD>32                      </TD> <TD>640                </TD>     <TD>4                    </TD>     <TD>4*8=32         </TD>     <TD>672          </TD></TR>
 * <TR><TD>64                      </TD> <TD>3840               </TD>     <TD>8                    </TD>     <TD>8*8=64         </TD>     <TD>3904         </TD></TR>
 * <TR><TD>128                     </TD> <TD>3840               </TD>     <TD>8                    </TD>     <TD>8*8=64         </TD>     <TD>3904         </TD></TR>
 * <TR><TD>384                     </TD> <TD>6400               </TD>     <TD>16                   </TD>     <TD>16*8=128       </TD>     <TD>6528         </TD></TR>
 * </TABLE>
 */ 
#if defined(UMAC_UT_TEST)
typedef Int8 UDlPduListData [200];

#else
typedef Int8 UDlPduListData [UDL_MAX_TB_LIST_DATA_OCTETS];
#endif

/** Defines the start and end of each transport block (or RLC PDU) and its
 * associated data (transport channel and TFI).
 */
typedef struct UDlPduListInfoTag
{
    /** Indicates UBCH, UPCH, UFACH or UDCH. */
    UTransportChannelType           trChType;

    /** Indicates a transport channel identity between MAC and PHY (and it is
     * in the range 1 to 32), but a radio bearer identity between RLC and MAC
     * (and is set to a value from the \c BearerIdentity enumeration). */
    Int8                            identity;

    /** Indicates an array of PDU bit offsets, from the end of the preceding
     * PDU in the list, where the most significant bit of used data starts, in
     * the range 0 to 255.
     */
    Int16                            bitOffset;

    /** Indicates an array of PDU bit lengths, in the range 0 to 65535. The bit
     * length for a transport block may only be zero in CELL_DCH. If this
     * transport channel has been specified as a channel requiring a quality
     * target, and no other transport channels have data to send, a "dummy TB"
     * may be specified to be transmitted by MAC, whose only useful information
     * is the CRC result.
     */
    Int16                           bitLength;

    /** Indicates the status of the PDU. */
    UDlPduStatus                    pduStatus;
}
UDlPduListInfo;






/** Defines the transport block set and contains a list of one or more
 * contiguous transport blocks, where each transport block must start on a
 * 16-bit word aligned address. To minimise copying of data this data type is
 * also used for RLC PDUs on the MAC interface, so that only the offsets and
 * lengths need to be modified.
 *
 * The below figure illustrates the PDU list on its journey up the protocol
 * stack:
 *
 * \note the alignment padding between transport blocks may be 8-bit, 16-bit or
 * 32-bit, depending on hardware requirements. The protocol stack and L1 use
 * the configurable parameter <tt> TRANSPORT_BLOCK_ALIGNMENT_IN_OCTETS </tt> to
 * define alignment.
 *
 */
typedef struct UDlPduListTag
{
    /** Indicates the number of transport blocks in this TTI. */
    Int8                            numberOfTransportBlocks;

    /** Indicates the start and end of each transport block (or RLC PDU) and
     * its associated data (transport channel and TFI).
     */
    UDlPduListInfo                  pduListInfo [UPS_MAX_DL_NO_OF_TB_PER_TTI];

    /* byteLength is to used as a Genie association, to enable it to display
     * only the used area of the pduList. For example, for a list containing
     * two PDU's, it would need to be set to :
     *        bitOffset[ 0 ] + bitLength[ 0 ]
     *      + bitOffset[ 1 ] + bitLength[ 1 ]
     * It is also used to copy the entire data block where required.
     */

#if !defined (DATA_IN_SIGNAL)
    /** Indicates that data is finished with and ready for reuse.
     * This allows safe recycling when the data pointer is statically
     * allocated.
     *
     * \note This only exists when compiling without  DATA_IN_SIGNAL defined.
     */
    Boolean                         isConsumed;
#endif /* !DATA_IN_SIGNAL */

    /** With DATA_IN_SIGNAL this limits teh data displayed by Genie.
     * Its value should be set to contain the value of
     * <tt> ((bitOffset+bitLength+7)/8). </tt>
     *
     * \note This also forces the next field to be word aligned as
     *       required by some Phy implementations.
     */
    Int16                           byteLength;

    /** Indicates an array of data containing transport blocks. The size of the
     * array varies with the product data class and includes padding octet(s)
     * per transport block, to ensure that the start of each transport block is
     * word aligned at the UPHY interface according to the configurable
     * parameter \c TRANSPORT_BLOCK_ALIGNMENT_IN_OCTETS.
     *
     * <TABLE>
     * <TR><TD><B>Data Class (kbps)</B></TD> <TD><B>Max bits per TTI</B></TD> <TD><B>Max blocks per TTI</B></TD> <TD><B>Padding bits</B></TD> <TD><B>Total bits</B></TD></TR>
     * <TR><TD>32                      </TD> <TD>640                </TD>     <TD>4                    </TD>     <TD>4*8=32         </TD>     <TD>672          </TD></TR>
     * <TR><TD>64                      </TD> <TD>3840               </TD>     <TD>8                    </TD>     <TD>8*8=64         </TD>     <TD>3904         </TD></TR>
     * <TR><TD>128                     </TD> <TD>3840               </TD>     <TD>8                    </TD>     <TD>8*8=64         </TD>     <TD>3904         </TD></TR>
     * <TR><TD>384                     </TD> <TD>6400               </TD>     <TD>16                   </TD>     <TD>16*8=128       </TD>     <TD>6528         </TD></TR>
     * </TABLE>
     */
#if defined (DATA_IN_SIGNAL)
	UDlPduListData                  data;
#endif
    UDlPduListData                  *data_p;

    /* The data array must be the *very* last element in this structure, so that
     * its size can be reduced to be as large as needed, not a default maximum;
     * it may not be allocated if no data is sent
     */
} UDlPduList;

/** Defines the start and end of each transport block (or RLC PDU) and its
 * associated data (transport channel and TFI).
 */
typedef struct UUlPduListInfoTag
{
    /** Indicates either UDCH (if associated with \c PhyDataReq primitive) or
     * URACH (if associated with \c PhyAccessReq primitive).
     */
    UTransportChannelType           trChType;

    /** Indicates a transport channel identity between MAC and PHY (and it
     * is in the range 1 to 32), but a radio bearer identity between RLC and
     * MAC (and is set to a value from the \c BearerIdentity enumeration). */
    Int8                            identity;

    /** Indicates an array of PDU bit offsets, from the end of the preceding
     * PDU in the list, where the most significant bit of used data starts, in
     * the range 0 to 255.
     */
    Int8                            bitOffset;

    /** Indicates an array of PDU bit lengths, in the range 0 to 65535. The bit
     * length for a transport block may only be zero in CELL_DCH. If this
     * transport channel has been specified as a channel requiring a quality
     * target, and no other transport channels have data to send, a "dummy TB"
     * may be specified to be transmitted by MAC, whose only useful information
     * is the CRC result.
     */
    Int16                           bitLength;

    /** Indicates the Transport Format Index in a Transport Format Set, and it
     * is in the range 0 to 31.
     */
    Int8                            transportFormatIndicator;
}
UUlPduListInfo;

/** Defines the uplink pdu data as an array of octets to hold uplink transport
 * blocks, allowing optional alignment padding per transport block, to ensure
 * that the start of each transport block is word aligned, so that the physical
 * layer can avoid bit copies.
 */
typedef Int8 UUlPduListData [UUL_MAX_TB_LIST_DATA_OCTETS];

/** Defines the transport block set and contains a list of one or more
 * contiguous transport blocks, where each transport block must start on a
 * 16-bit word aligned address. To minimise copying of data this data type is
 * also used for RLC PDUs on the MAC interface, so that only the offsets and
 * lengths need to be modified.
 *
 * The below figure illustrates the PDU list on its journey down the protocol
 * stack:
 *
 * \note the alignment padding between transport blocks may be 8-bit, 16-bit or
 * 32-bit, depending on hardware requirements. The protocol stack and L1 use
 * the configurable parameter <tt> TRANSPORT_BLOCK_ALIGNMENT_IN_OCTETS </tt> to
 * define alignment.
 */
typedef struct UUlPduListTag
{
    /** Indicates the number of transport blocks in this TTI. */
    Int8                            numberOfTransportBlocks;

    /** Indicates the start and end of each transport block (or RLC PDU) and
     * its associated data (transport channel and TFI).
     */
    UUlPduListInfo                  pduListInfo [UPS_MAX_UL_NO_OF_TB_PER_TTI];

#if !defined (DATA_IN_SIGNAL)
    /** Indicates that data is finished with and ready for another write by the
     * creator. This is useful when the data pointer is statically allocated
     */
    Boolean                         isConsumed;
#endif /* !DATA_IN_SIGNAL */

    /* byteLength is to used as a Genie association, to enable it to display
     * only the used area of the data. For example, for a list containing two
     * PDU's, it would need to be set to :
     *        bitOffset[ 0 ] + bitLength[ 0 ] + bitOffset[ 1 ] + bitLength[ 1 ]
     * It is also used to copy the entire data block where required.
     */
    /** This member only exists when compiled for DATA_IN_SIGNAL, and is used
     * by Genie to limit the amount of data displayed by Genie; it does not
     * stop Genie from displaying data before the offset. Its value should be
     * set to contain the value of <tt> ((bitOffset+bitLength+7)/8). </tt>
     */
    Int16                           byteLength;
	/** This field is required for cache alignment purposes.
	 * this sturcutre is cleaned/invalidated by UMAC before/after calling DTC.
	 * those operations should be aligned to a cache line, so we adjust this structure here
	 * to avoid cache coherency problems.
        */
    Int8                           reserved[26];
    /** Indicates an array of data containing transport blocks. The size of the
     * array varies with the product data class and includes padding octet(s)
     * per transport block, to ensure that the start of each transport block is
     * word aligned at the UPHY interface according to the configurable
     * parameter \c TRANSPORT_BLOCK_ALIGNMENT_IN_OCTETS.
     *
     * <TABLE>
     * <TR><TD><B>Data Class (kbps)</B></TD> <TD><B>Max bits per TTI</B></TD> <TD><B>Max blocks per TTI</B></TD> <TD><B>Padding bits</B></TD> <TD><B>Total bits</B></TD></TR>
     * <TR><TD>32                      </TD> <TD>640                </TD>     <TD>4                    </TD>     <TD>4*8=32         </TD>     <TD>672          </TD></TR>
     * <TR><TD>64                      </TD> <TD>3840               </TD>     <TD>8                    </TD>     <TD>8*8=64         </TD>     <TD>3904         </TD></TR>
     * <TR><TD>128                     </TD> <TD>3840               </TD>     <TD>8                    </TD>     <TD>8*8=64         </TD>     <TD>3904         </TD></TR>
     * <TR><TD>384                     </TD> <TD>6400               </TD>     <TD>16                   </TD>     <TD>16*8=128       </TD>     <TD>6528         </TD></TR>
     * </TABLE>
     */

    UUlPduListData                  data;

    /* The data array must be the *very* last element in this structure, so that
     * its size can be reduced to be as large as needed, not a default maximum;
     * it may not be allocated if no data is sent
     */
} UUlPduList;

/** Defines the TGPS parameters. */
typedef struct UTGPSActiveConfigParamsTag
{
    /** Indicates the CFN at which TGPS should be activated. */
    UTGCFN                          activateAtTgcfn;

    /** Indicates the Transmission Gap Patter Sequence Identifier. */
    UTGPSI                          tgpsi;

    /** Indicates the compressed mode configuration parameters. For more
     * information refer to <tt> [3G TS 25.331 section 10.3.6.33] </tt> */
    UTGPS_ConfigurationParams_r8    compressedModeConfig;
}
UTGPSActiveConfigParams;

#endif  /* U1_TYP_H */

/* END OF FILE */

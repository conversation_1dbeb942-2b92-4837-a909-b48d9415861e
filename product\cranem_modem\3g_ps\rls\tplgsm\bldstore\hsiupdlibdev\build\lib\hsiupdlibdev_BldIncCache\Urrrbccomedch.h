/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/***************************************************************************
 * 3GPP Protocol Stack Copyright (c) TTP Communications PLC 2001
 ***************************************************************************
 *
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urrrbccomedc.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2007/05/03 13:53:25 $
 *
 ***************************************************************************
 *
 * File Description:
 *
 *    Header file for RRC's Radio Bearer Control sub-process for Common E-DCH (UL ECF) sub-module.
 *    Contains definitions for use by RBC Common E-DCH module, and other RBC sub-modules.
 *
 ***************************************************************************
 *
 ***************************************************************************/
#if !defined (URRRBCCOMEDCH_H)
#define       URRRBCCOMEDCH_H

#if defined(UPGRADE_UL_ECF) // ********** begin

/***************************************************************************
* Nested Include Files
***************************************************************************/
#include <urrrbc.h>
#include <urrrbcl.h>
#include <cphy_sig.h>
#include <pl_w_globs.h>
#include <cmac_sig.h>
#include <urrrbcehs.h>

/***************************************************************************
* Constants
***************************************************************************/
#define COM_EDCH_CCCH_MAC_D_FLOW_ID 7

/***************************************************************************
* Macros
***************************************************************************/

/***************************************************************************
 * Types
 ***************************************************************************/


typedef struct UrrRbcComEdchDbInputsTag
{
    UrrRbcCommonEdchData *src_p;
    UrrRbcCommonEdchData *dest_p;
}
UrrRbcComEdchDbInputs; 



/***************************************************************************
* Enums
***************************************************************************/
/* Modification of this enum also requires update of EhsRequestTypesString in urrutm.c */
//ICAT EXPORTED ENUM


typedef enum ComEdchGlobalVariableValueTypesTag
{
    COM_EDCH_READY_FOR_COMMON_EDCH,
    COM_EDCH_COMMON_E_DCH_TRANSMISSION,
    COM_EDCH_HSPA_RNTI_STORED_CELL_PCH,
}
ComEdchGlobalVariableValueTypes;

/***************************************************************************
* Function Declarations
***************************************************************************/

/************************ External Interface Functions ***************************/

Boolean UrrRbcComEdchCondition1Fulfilled(void);

Boolean UrrRbcComEdchCondition2Fulfilled(void);

Boolean UrrRbcComEdchGetUlEcfReceptionValue(ComEdchGlobalVariableValueTypes type);

void UrrRbcComEdcSetSysInfoParamsValid(Boolean value);

Boolean UrrRbcComEdcGetSysInfoParamsValid(void);

Boolean UrrRbcComEdcStoreCommonEdchSysInfo(UCommonEDCHSystemInfoFDD *source_p, Boolean isCopyDb, UrrRbcCommonEdchData *comEdchData_p);

void UrrRbcComSetEdchSysInfoParamsValid(Boolean setValue);

void UrrRbcComHandleCommonEdchSystemInfoFdd(UCommonEDCHSystemInfoFDD * commonEDCHSystemInfoFDD_p);

void UrrRbcComBuildAndSendCmacEdchMacdFlowConfigReq(Int8 configId, UCommon_E_DCH_MAC_d_FlowListElement * macdFlowElem_p);

void UrrRbcComBuildAndSendCmacEdchMacdFlowConfigReqSet(Int8 configId);

void UrrRbcComBuildAndSendCmacEdchRachConfigReq(Int8 configId, Boolean initialFach);

void UrrRbcComBuildAndSendCmacEdchMacdFlowConfigRb0Req(Int8 configId, UCommon_E_DCH_MAC_d_FlowListElement * macdFlowElem_p);

void UrrRbcComBuildAndSendCmacEdchTfcsConfigReq (Int8 configId);

Boolean UrrRbcComEdchGetCommonEdchValue(ComEdchGlobalVariableValueTypes type);

void UrrRbcComEdchSetSysInfoParamsValid(Boolean value);

Boolean UrrRbcComEdchGetSysInfoParamsValid(void);

Boolean UrrRbcComEdchGetPrevReadyforCommonEdch(void);

Boolean UrrRbcComEdchIsFachToFachTransitionForReadyforCommonEdch(void);


void UrrRbcComEdchProcessCommonEdchValue(ComEdchGlobalVariableValueTypes type, UrrSmcModeAndState modeAndState);

void UrrRbcComEdchSetMacIReset (Boolean reset);

void RbcComEdchInitDatabase (UrrRbcCommonEdchData *db_p);

void RbcComEdchCopyDatabase (UrrRbcCommonEdchData *src_p, UrrRbcCommonEdchData *dst_p);

void RbcComEdchInit (void);

void RbcComEdcCopyDatabase (UrrRbcCommonEdchData *src_p, UrrRbcCommonEdchData *dst_p);

void UrrRbcComEdchConfigEcfUlPhy(Int8 configId, UrrRbcTransaction *trans_p);

void UrrRbcComEdchBuildRlSetupReq(CphyRlSetupReq *setupMsg_p);

void UrrRbcComEdchBuildRlCommonSetupReq(CphyRlCommonSetupReq  * rlCommonSetup_p);

void UrrRbcComEdchBuildCphyRachConfigReq(CphyRachConfigReq  * rachConfigReq_p);

void UrrRbcComEdchSetMacIReset (Boolean reset);

void RbcComEdcInitDatabase (UrrRbcCommonEdchData *db_p);

void UrrRbcComEdchProcessCommonEdchInfoToEdchInfoDb (UrrRbcTransaction *trans_p);

void UrrRbcComEdchProcessCommonEdchServingCell(UPrimaryScramblingCode  rl, UrrRbcTransaction *trans_p);

Boolean UrrRbcComEdchIsUlInterfPresent(void);

void UrrRbcComBackupDchHsupaInfoWhileOnEcfUl(void);

void UrrRbcComRevertDchHsupaInfoFromFachBackup(void);

void UrrRbcComEdchHandleCmacEdchRachTxStatusInd(RadioResourceControlEntity *urr_p);

void UrrRbcComEdchConfigMacUl(Boolean configId, Boolean initialFach);

void UrrRbcComEdchHandleCmacTransSuccess(void);

void UrrRbcComEdchHandleEdchResReleasedOrFailed(void);

void UrrRbcComEdchHandleCphyCommonEdchResReleasedInd(RadioResourceControlEntity *urr_p);

void UrrRbcComEdchProcessHsupaInfoForCommonEdch(UrrRbcTransaction *trans_p);

void UrrRbcComEdchSendCmacCommEdchResourceRelease(void);

Boolean UrrRbcComEdchIsCommonEdchConfigured(void);

void UrrRbcComEdchLeavingCellFachEcfUl(void);

void UrrRbcComEdchSetCommonEdchIsConfigured(Boolean value, Int8 index);


#endif /* UPGRADE_UL_ECF  */ // ********** end

/************************ External Interface Functions ***************************/

#endif


/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/pscommon/3g_asn1.mod/api/inc/utper.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2007/03/30 13:53:25 $
 **************************************************************************
 * File Description:
 *
 *    Header file for packed encoding rule functions
 **************************************************************************/
#ifndef UTPER_H
#define UTPER_H

/***************************************************************************
** Nested Include Files
***************************************************************************/

#include <system.h>

/***************************************************************************
 *   Manifest Constants
 ***************************************************************************/

#define DEFAULT_DECODE_BUFFER_SIZE       500
#define MAX_UNCONSTRAINED_OCTET_STRING  2048
#define MAX_UNCONSTRAINED_BIT_STRING   16384
#define MAX_ENCODE_DECODE_ERRORS_TO_REPORT 1
#define PER_MIN_DATA_ALLOC                 1

/***************************************************************************
 *   Macro Functions
 ***************************************************************************/

/***************************************************************************
 *   Types
 ***************************************************************************/

typedef struct PerDecodeBufferTag
{
    Int16 decodeBufferSize;            /* Allocated decode buffer size in bytes */
    Int16 decodedDataLength;           /* Used length of this buffer in bytes */
    struct PerDecodeBufferTag *next_p; /* Pointer to next decode buffer */
    /* data part Must be at the end of the structure */
    Int8   data [PER_MIN_DATA_ALLOC];  /* data size is allocated dynamically. */
}
PerDecodeBuffer;
#define MAX_DEC_EXTENSTION_NUM 8//CQ00072474
typedef struct PerBufferTag
{
    Int32 encodedDataBitOffset;/* Current bit offset into encodedDataPtr */
    Int32 previousBitOffset;   /* Previous bit offset into encodedDataPtr */
	Int8  decExtenstionOffsetIndex;//CQ00072474
	Int32 decExtenstionOffset[MAX_DEC_EXTENSTION_NUM];//CQ00072474
    Int32 maxEncodedBitLength;    /* Total length of encoded data buffer in bits */
    Int8 *encodedData_p;       /* Pointer to the start of encoded data buffer */
    PerDecodeBuffer *head_p;   /* Pointer to initial decode buffer space */
    PerDecodeBuffer *tail_p;   /* Pointer to last decode buffer space */
    Boolean getEncodeSizeOnly; /* TRUE if the user only wants to calculate the size
                                * of the encoded PER string */
    Boolean decodedSpareValue; /* TRUE if decoded a spare value with no DEFAULT */
    Boolean spareValueSubstituted; /* TRUE if decoded a spare value and it was
                                      substituted for a DEFAULT value */
#if defined (DEVELOPMENT_VERSION)
    Boolean errorCount;        /* Errors reported to TEST_FILE_OUT */
#endif
}
PerBuffer;

typedef enum PerErrorTag
{
    PER_NO_ERROR,
    PER_LENGTH_OUT_OF_RANGE,
    PER_VALUE_OUT_OF_RANGE,
    PER_ERROR_INVALID_ENUM,
    PER_INVALID_CHOICE_TAG,
    PER_UNSUPPORTED_EXTENSION, /* Decoded data had the extension bit set */
    PER_DECODE_BUFFER_OVERRUN,
    PER_UNSUPPORTED_TYPE
}
PerError;

/***************************************************************************
 *   Typed Constants
 ***************************************************************************/

/***************************************************************************
 *  Function Prototypes
 ***************************************************************************/

/***************************************************************************
 *  Encode Functions
 ***************************************************************************/
void PerEncBoolean (PerBuffer *perBuffer_p,
                    Boolean   *value_p);

void PerEncBooleanValue (PerBuffer *perBuffer_p,
                         Boolean   value);

void PerEncConstrainedLength (PerBuffer *perBuffer_p,
                              Int16     value,
                              Int16     min,
                              Int16     max);

void PerEncUnconstrainedLength (PerBuffer *perBuffer_p,
                                Int16     value);

void PerEncInt8 (PerBuffer *perBuffer_p,
                 Int8      *value_p,
                 Int8      min,
                 Int8      max);

void PerEncInt16 (PerBuffer *perBuffer_p,
                  Int16     *value_p,
                  Int16     min,
                  Int16     max);

void PerEncInt32 (PerBuffer *perBuffer_p,
                  Int32     *value_p,
                  Int32     min,
                  Int32     max);

void PerEncSignedInt8 (PerBuffer  *perBuffer_p,
                       SignedInt8 *value_p,
                       SignedInt8 min,
                       SignedInt8 max);

void PerEncSignedInt16 (PerBuffer   *perBuffer_p,
                        SignedInt16 *value_p,
                        SignedInt16 min,
                        SignedInt16 max);

void PerEncSignedInt32 (PerBuffer   *perBuffer_p,
                        SignedInt32 *value_p,
                        SignedInt32 min,
                        SignedInt32 max);

void PerEncFixedBitString (PerBuffer *perBuffer_p,
                           Int8      *value_p,
                           Int16     number);

void PerEncFixedOctetString (PerBuffer *perBuffer_p,
                             Int8      *value_p,
                             Int16     length);

void PerEncVariableBitString (PerBuffer *perBuffer_p,
                              Int8      *value_p,
                              Int16     length,
                              Int16     min,
                              Int16     max);

void PerEncVariableOctetString (PerBuffer *perBuffer_p,
                                Int8      *value_p,
                                Int16     length,
                                Int16     min,
                                Int16     max);

void PerEncUnconstrainedOctetString (PerBuffer *perBuffer_p,
                                     Int8      *value_p,
                                     Int16     length);

void PerEncUnconstrainedBitString (PerBuffer *perBuffer_p,
                                   Int8      *value_p,
                                   Int16     length);

void PerEncEnum (PerBuffer *perBuffer_p,
                 Int8      *value_p,
                 Int8      max);

void PerEncEnumWithExtension (PerBuffer *perBuffer_p,
                              Int8      *value_p,
                              Int8      maxValueInRoot);

void PerEncStartOfAlignedDataWithLength (PerBuffer *perBuffer_p);

void PerEncEndOfAlignedDataWithLength (PerBuffer *perBuffer_p);

void PerEncStartOfBitAlignedDataWithLength (PerBuffer *perBuffer_p);

#if defined(UPGRADE_LTE)
void PerEncStartOfBitAlignedDataWithLengthCap (PerBuffer *perBuffer_p, Boolean noNeedPlusInt8);/*********** add */
void PerEncEndOfBitAlignedDataWithLengthCap (PerBuffer *perBuffer_p, Boolean noNeedPlusInt8);/*********** add */
Boolean PerReturnLteCapExceed(void);
void PerEncConstrainedLengthWithoutMax (PerBuffer *perBuffer_p,
                              Int16     value);                              
void PerEncVariableOctetStringWithoutMax (PerBuffer *perBuffer_p,
                                Int8      *value_p,
                                Int16     length);
PerError PerDecVariableOctetStringWithoutLength (PerBuffer *perBuffer_p,
                                    Int8      *value_p,
                                    Int16     *length_p);
#endif

void PerEncEndOfBitAlignedDataWithLength (PerBuffer *perBuffer_p);

/***************************************************************************
 *  Decode Functions
 ***************************************************************************/
PerError PerDecBoolean (PerBuffer *perBuffer_p,
                        Boolean   *value_p);

PerError PerDecConstrainedLength (PerBuffer *perBuffer_p,
                                  Int16     *value_p,
                                  Int16     min,
                                  Int16     max);

PerError PerDecUnconstrainedLength (PerBuffer *perBuffer_p,
                                    Int16     *value_p);

PerError PerDecInt8 (PerBuffer *perBuffer_p,
                     Int8      *value_p,
                     Int8      min,
                     Int8      max);

PerError PerDecInt16 (PerBuffer *perBuffer_p,
                      Int16     *value_p,
                      Int16     min,
                      Int16     max);

PerError PerDecInt32 (PerBuffer *perBuffer_p,
                      Int32     *value_p,
                      Int32     min,
                      Int32     max);

PerError PerDecSignedInt8 (PerBuffer  *perBuffer_p,
                           SignedInt8 *value_p,
                           SignedInt8 min,
                           SignedInt8 max);

PerError PerDecSignedInt16 (PerBuffer   *perBuffer_p,
                            SignedInt16 *value_p,
                            SignedInt16 min,
                            SignedInt16 max);

PerError PerDecSignedInt32 (PerBuffer   *perBuffer_p,
                            SignedInt32 *value_p,
                            SignedInt32 min,
                            SignedInt32 max);

PerError PerDecFixedBitString (PerBuffer *perBuffer_p,
                               Int8      *value_p,
                               Int16     length);

PerError PerDecFixedOctetString (PerBuffer *perBuffer_p,
                                 Int8      *value_p,
                                 Int16     length);

PerError PerDecVariableBitString (PerBuffer *perBuffer_p,
                                  Int8      *value_p,
                                  Int16     *length_p,
                                  Int16     min,
                                  Int16     max);

PerError PerDecVariableOctetString (PerBuffer *perBuffer_p,
                                    Int8      *value_p,
                                    Int16     *length_p,
                                    Int16     min,
                                    Int16     max);

PerError PerDecUnconstrainedBitString (PerBuffer *perBuffer_p,
                                       Int8      **value_p,
                                       Int16     *length_p);

PerError PerDecUnconstrainedOctetString (PerBuffer *perBuffer_p,
                                         Int8      **value_p,
                                         Int16     *length_p);

PerError PerDecEnum (PerBuffer *perBuffer_p,
                     Int8      *value_p,
                     Int8      max);

PerError PerDecEnumWithExtension (PerBuffer *perBuffer_p,
                                  Int8      *value_p,
                                  Int8      maxValueInRoot);

void PerDecStartOfAlignedDataWithLength (PerBuffer *perBuffer_p);

void PerDecEndOfAlignedDataWithLength (PerBuffer *perBuffer_p);

void PerDecStartOfBitAlignedDataWithLength (PerBuffer *perBuffer_p);

void PerDecEndOfBitAlignedDataWithLength (PerBuffer *perBuffer_p);

PerError PerSkipUnconstrainedBitString (PerBuffer *perBuffer_p);

PerError PerDecPadding (PerBuffer *perBuffer_p,
                        Int8      **value_p,
                        Int16     *length_p);

/***************************************************************************
 *  Memory Allocation Functions
 ***************************************************************************/
void PerAllocMemory (PerBuffer *perBuffer_p,
                     Int16     numberOfBytes,
                     void      **buffer_p);

void PerFreeMemory (PerBuffer *perBuffer_p);


/***************************************************************************
 *  Initialisation Functions
 ***************************************************************************/
void PerInitEncode (PerBuffer *perBuffer_p,
                    Int8      *encodedData_p,
                    Int32     maxEncodedBitLength,
                    Int16     initialDecodeSize,
                    void      **decodedData_p);

void PerInitDecode (PerBuffer *perBuffer_p,
                    Int8      *encodedData_p,
                    Int32     maxEncodedBitLength,
                    Int16     initialDecodeSize,
                    void      **decodedData_p);

/*Modify for CQ00028462 by qhli begin*/
PerError PerDecNormallySmallLength (PerBuffer *perBuffer_p,Int8     *value_p);
void PerEncNormallySmallLength (PerBuffer *perBuffer_p,Int8     *value_p);
/*Modify for CQ00028462 by qhli end*/

void PerSetLteCapExceed (Boolean value);/*CQ00043252 add*/
/*CQ00043738 add begin*/
void PerSetLteCapExceedInit (void);
void PerSetLteCapExceed1 (Boolean value);
void PerSetLteCapExceed2 (Boolean value);
Boolean PerReturnLteCapExceed1(void);
Boolean PerReturnLteCapExceed2(void);
Boolean PerReturnLteCapExceed2Initialized(void);
/*CQ00043738 add end*/

#endif

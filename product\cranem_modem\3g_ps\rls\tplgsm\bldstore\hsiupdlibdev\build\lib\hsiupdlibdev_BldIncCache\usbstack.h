/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/devices/transports/usb.mod/pub/src/usbstack.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/24 10:16:13 $
 **************************************************************************
 *  File Description :
 *    Definition of the USB protocol stack context.
 **************************************************************************/
#if defined (UPGRADE_USB)

#ifndef USBSTACK_H
#define USBSTACK_H

/****************************************************************************
 * Essential Include Files
 ****************************************************************************/
#include <usbappif.h>     /* for public fn data types & structures */
#include <usbconst.h>     /* for constants. */
#include <usbselection.h> /* for Device Configuration type */

/****************************************************************************
 * Types used in Prototypes and Globals
 ****************************************************************************/

typedef enum UsbDeviceStateTag
{
  USB_DEVICE_STATE_DETACHED    = 0x01,
  USB_DEVICE_STATE_ATTACHED    = 0x02,
  USB_DEVICE_STATE_POWERED     = 0x04,
  USB_DEVICE_STATE_DEFAULT     = 0x08,
  USB_DEVICE_STATE_ADDRESS     = 0x10,
  USB_DEVICE_STATE_CONFIGURED  = 0x20,
  USB_DEVICE_STATE_SUSPENDED   = 0x40
} UsbDeviceState;

typedef enum UsbEndpointToggleStateTag
{
  USB_ENDPOINT_TOGGLE_DATA0,
  USB_ENDPOINT_TOGGLE_DATA1
} UsbEndpointToggleState;

typedef enum UsbEndpointTransferTypeTag
{
  USB_ENDPOINT_TRANSFER_TYPE_CONTROL,
  USB_ENDPOINT_TRANSFER_TYPE_ISOCHRONOUS,
  USB_ENDPOINT_TRANSFER_TYPE_BULK,
  USB_ENDPOINT_TRANSFER_TYPE_INTERRUPT
} UsbEndpointTransferType;

typedef struct UsbDeviceAddressTag
{
  Int8    address;
  Int8    newAddress;
  Boolean isSetAddressRequest;
} UsbDeviceAddress;

typedef struct UsbEndpointConfigurationTag
{
  Boolean                 isDefined;
  Int8                    interfaceNumber;
  Int8                    alternateNumber;
  UsbEndpointTransferType transferType;
  Int16                   maximumPacketSize;
  Boolean                 isStalled;
  UsbEndpointToggleState  toggleState;
} UsbEndpointConfiguration;

typedef struct UsbFrameNumberTag
{
  Int16   number;
  Boolean isValid;
} UsbFrameNumber;

typedef struct UsbInterfaceConfigurationTag
{
  Boolean isDefined;
  Int8    alternateNumber;
  Int16   getStatusInterface;
} UsbInterfaceConfiguration;

typedef enum UsbControlTransferStateTag
{
  CONTROL_TRANSFER_STATE_SETUP,
  CONTROL_TRANSFER_STATE_DATA_HOST_TO_DEVICE,
  CONTROL_TRANSFER_STATE_DATA_DEVICE_TO_HOST,
  CONTROL_TRANSFER_STATE_STATUS_HOST_TO_DEVICE,
  CONTROL_TRANSFER_STATE_STATUS_DEVICE_TO_HOST
} UsbControlTransferState;

typedef struct UsbStackContextTag
{
  TaskId                      devAppTaskID;

  Boolean                     deviceControllerIsPresent;

  UsbDeviceState              deviceState;

  UsbDeviceState              presuspensionDeviceState;

  Int16                       getStatusDevice;

  Int8                        configurationValue;

  Int8                        configurationIndex;

  UsbInterfaceConfiguration   interfaceConfiguration[USB_SPEC_MAX_NUMBER_INTERFACES];

  UsbEndpointConfiguration    inEndpointConfiguration[USB_SPEC_MAX_NUM_IN_ENDPOINTS];

  UsbEndpointConfiguration    outEndpointConfiguration[USB_SPEC_MAX_NUM_OUT_ENDPOINTS];

  UsbDeviceAddress            deviceAddress;

  UsbFrameNumber              frameNumber;

  UsbControlTransferState     usbControlTransferState[USB_SPEC_MAX_NUM_IN_ENDPOINTS];

  Boolean                     resetInProgress;

  UsbDeviceSelection      currentDeviceSelection;
  UsbDeviceSelection      pendingDeviceSelection;

} UsbStackContext;

extern UsbStackContext usbStackContext;
extern UsbStackContext *pUsbStackContext;

/*******************************************************************************
 * Global Function Prototypes
 ******************************************************************************/

extern void usbTaskInitialise(void);
extern void usbTaskExit(void);

extern TaskId usbGetRxBufferTaskId( Int8 endpointNumber );

extern void usbCreateDevAndDebug(Int8 code, Int8 endpointNumber, Int8* pointer1,
                                 Int8* pointer2, Int16 data1, Int16 data2,
                                 Int8* packet, Int8 packetLength,
                                 Boolean isISR);

/*******************************************************************************
 * Macros
 ******************************************************************************/

#define M_UsbStackGetDeviceState()                                   (pUsbStackContext->deviceState)
#define M_UsbStackSetDeviceState(dEVICEsTATE)                        (pUsbStackContext->deviceState = dEVICEsTATE)

#define M_UsbStackGetDeviceControllerIsPresent()                     (pUsbStackContext->deviceControllerIsPresent)

#define M_UsbStackGetInEndpointTransferType(eNDPOINTnUMBER)          (pUsbStackContext->inEndpointConfiguration[eNDPOINTnUMBER].transferType)
#define M_UsbStackGetOutEndpointTransferType(eNDPOINTnUMBER)         (pUsbStackContext->outEndpointConfiguration[eNDPOINTnUMBER].transferType)

#define M_UsbStackGetControlTransferState(eNDPOINTnUMBER)            (pUsbStackContext->usbControlTransferState[eNDPOINTnUMBER])

#define M_UsbStackSetResetInProgress(rESETiNpROGRESS)                (pUsbStackContext->resetInProgress = rESETiNpROGRESS)
#define M_UsbStackGetResetInProgress()                               (pUsbStackContext->resetInProgress)

#endif  /* !defined (USBSTACK_H) */

#endif /* defined(UPGRADE_USB) */
/* END OF FILE */

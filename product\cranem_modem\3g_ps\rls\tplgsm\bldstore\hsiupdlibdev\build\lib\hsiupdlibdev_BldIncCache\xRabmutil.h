/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/gp.mod/lib/src/xrabmutil.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2011/03/07 16:27:25 $
 **************************************************************************
 * File Description:
 *
 * Function declarations for xrabmutil.c
 **************************************************************************/

#if !defined (XRABMUTIL_H)
#define       XRABMUTIL_H

#include <system.h>
#if defined(UMTS7_PRE_R8)
#include <kernel.h>
#endif
#include <xrabmdata.h>
#include <gpnsapi.h>


void XRabmInitPdp(XRabmEntity *sn, Int16 i);
XRabmPdpEntity* XRabmCreateNewPdp(XRabmEntity *sn, Nsapi nsapi, Boolean *nsapiAlreadyExists);
XRabmPdpEntity* XRabmFindPdp(XRabmEntity *sn, Nsapi nsapi);
void XRabmReleasePdpCommon (XRabmEntity *sn, XRabmPdpEntity *pdp);
void XRabmReleasePdp (XRabmEntity *sn, XRabmPdpEntity *pdp);
void XRabmUpdatePdpState(XRabmEntity *sn,Int8* pXRabmState, Int8 nextState);
#if !defined(DS3_LTE_ONLY)
void XRabmReleaseIscData(XRabmEntity *sn,Int16 pdpIndex);
#endif

#endif

//END OF FILES

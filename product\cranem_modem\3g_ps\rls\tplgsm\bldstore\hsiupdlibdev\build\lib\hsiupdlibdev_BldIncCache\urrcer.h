/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/
 
/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urrcer.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/27 15:10:43 $
 **************************************************************************
 * File Description:
 *
 *    Header file for URRCER.C. Contains function call declarations
 *    for use by other URRC modules.
 **************************************************************************/

#if !defined (URRCER_H)
#define       URRCER_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <urrtypes.h>
#include <urr_sig.h>

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/

/***************************************************************************
*   Macro Functions
***************************************************************************/

/* Use this to reduce the length of the SendRrcStatus parameter */
#define RRC_STATUS_TYPE(A)    T_UProtocolErrorMoreInformation_type1_##A

#define SET_READING_SIBS(vALUE,iD)\
    RRC_TRACE_2(URRC_CER, SET_READING_SIBS_##iD, URRC_CER_DEFAULT, \
        "urrCer.readingSibs: %d -> %d", urrCer.readingSibs, vALUE);   \
    urrCer.readingSibs = vALUE;

/***************************************************************************
 * Types
 ***************************************************************************/

/***************************************************************************
 * Typed Constants
 ***************************************************************************/
#define DOMAIN_IS_NOT_BARRED  0xFFFF
/* **********, added begin */
#define LOCATION_REGISTRATION_IS_NOT_BARRED  0xFFFF
/* **********, added end */

/***************************************************************************
 * Function Prototypes
 ***************************************************************************/

void UrrCerInit (Boolean initialize);

void UrrCerSetReadingSibsFlag(Boolean value, Int8 id);

void GetInitialUeIdentity (UInitialUE_Identity *id_p);

void UrrCerActivate (void);

void UrrCerEstablishmentFailure (UEstablishStatus status, Boolean onlyDeferred, Boolean dontSetStateToIdle);

/* **********, added begin */
void UrrCerEstablishmentFailureForCsfbRaFailure (UEstablishStatus status);
/* **********, added end */

void UrrCerProcessInternalSignal (SignalBuffer *signal_p);

void UrrCerRrcConnRelReq (RadioResourceControlEntity *urr_p);

//void UrrCerNetworkAuthFailReq (RadioResourceControlEntity *urr_p);

void UrrCerRrcEstablishReq (RadioResourceControlEntity *urr_p);

void UrrCerHandleTimers (RadioResourceControlEntity *urr_p);

    /* ESTABLISHED_SIGNALLING_CONNECTIONS access functions */
Boolean UrrCerAddEstSigConnections (UCN_DomainIdentity newId);

Boolean UrrCerSearchEstSigConnections (UCN_DomainIdentity searchId);

Boolean UrrCerDeleteEstSigConnections (UCN_DomainIdentity deleteId);

void UrrCerCellAccessClass (UCellAccessRestriction cellAccessRestriction);

Boolean UrrCerAreAnyEstSigConnections (void);

void UrrCerSendRrcConnRelInd (Boolean releaseCsDomain,
                              Boolean releasePsDomain,
                              UReleaseCause releaseCause,
                              Boolean rrcConnectionExists);

void UrrCerReleaseRrcConnectionSetup (void);

    /* ESTABLISHMENT_CAUSE access functions */
void UrrCerSetEstablishmentCause (UEstablishmentCause establishmentCause);

void UrrCerClearEstablishmentCause (void);

Boolean UrrCerGetEstablishmentCause (UEstablishmentCause *establishmentCause_p);

void UrrCerConfigureInitialFachRach(Boolean isForRrcConnReq);   /* for ********** PTK_CQ00227845 */ 

Boolean UrrCerRejectedRedirectionOccurred(void);

Boolean UrrCerConnectionEstablishOnGoing(void);
#if defined (UPGRADE_DSDS)
Boolean UrrCerConnectionReleaseNonDchOnGoing(void);     /* ********** add */
#endif
Boolean UrrCerConnSetupCompleteIsSending(void); /************/
void UrrCerHandleRedirectionInfo(URRCConnectionRelease *sig_p, URRCConnectionRelease_CCCH *sig_CCCH_p);

UrrReleaseRedirectionInfo* UrrCerGetRedirectionInfo(Boolean *redirectionInfoPresent);

void UrrCerSetRedirectionInfoValidity(Boolean valid);

void UrrCerGetState (UrrCerState *cerState_p);
Boolean UrrCerCheckState (UrrCerState cerState);//********** added

UCN_DomainIdentity UrrCerGetCnDomainId (void);

CallTypeMM UrrCerGetCallType (void);

Boolean UrrCerPlmnIsSame (UPLMN_Identity *tmsiPlmn, Plmn cellPlmn);

void UrrCerSetDsacParam (Int16 csDomainBitmask,Int16 psDomainBitmask);

/* ********** begin */
Boolean UrrCerCheckRrcConnReqExist ( void );
/* ********** end */

/* **********, added begin */
/* ********** modified begin */ 
 void UrrCerSetPpacParam (
         UPagingPermissionWithAccessControlParameters_pagingResponseRestrictionIndicator         pagingResponseRestrictionIndicator,
         UPagingPermissionWithAccessControlParameters_locationRegistrationRestrictionIndicator   locationRegistrationRestrictionIndicator,
         Int16                                                                                   locationRegistrationRestrictionsBitmap);
/* ********** modified end */ 
/* **********, added end */

void UrrCerClearPpacParam(void); /* ********** added */


void UrrCerCrlcNotifyPsDataInd (RadioResourceControlEntity *urr_p);

Boolean CerIsFastDormancyInhibited (void);

void CerSendRrcFdEnabledInd (void);

void UrrCerForceEstablishFail (Boolean force); /* ********** added */

void CerHandleRrcFdEnabledInd (void);

void CerStopFastDormancyTimer(void);

void CerHandleResetFastDormancyCounters (Int8 index);  //********** modeification

void UrrHandleFastDormancyConfigReq(RadioResourceControlEntity *urr_p);

void CerHandleEcfModeChange (void);

void UrrCerCheckForDeferredEstablishmentRequest (void);

/* PTK_CQ00236871 begin */
Boolean UrrCerCheckDeferredAllowed (void);
/* PTK_CQ00236871 end */
void UrrCerCrlcNoPsDataInd(RadioResourceControlEntity *urr_p);/************/
void urrCerSetFirstSignallingByRedirecionFromLTE (Boolean flag);
/* ********** begin */
Boolean  urrCerGetFastReturnFlag (void);
void  urrCerSetFastReturnFlag (Boolean flag);
/* ********** end */
Boolean UrrCerScriWaitTimerIsRunning(void);/*********** add*/
#if defined (UPGRADE_DSDS)
Int8 GetNumberOfDeferredEstablishments (void);/*********** add*/
Boolean UrrCerCheckdeferredEstablishExist (UCN_DomainIdentity cnDomain); /*********** add */
void UrrCerCleardeferredEstablish (void); /*********** add */
Boolean UrrCerCheckdeferredEstablishExist (UCN_DomainIdentity cnDomain); /*********** add */
Boolean UrrCerWaitRbcConfigInitialFachRach(void);/*Added for ***********/
Boolean UrrCerGetReadingSibsFlag(void);    /*Add for ***********/
/* ********** add */
void UrrCerResetInterruptByHighPrioService (void);
Boolean UrrCerCheckInterruptByHighPrioService (void);
/* ********** end */
Boolean UrrCerGetRachFailer(void);/************/
/* ********** added begin */
void UrrCerSetInterruptByHighPrioService (Boolean set);
void UrrCerSetInterruptByHighPrioServiceNonDch (Boolean set);
Boolean UrrCerGetInterruptByHighPrioServiceNonDch (void);
/* ********** added end */
#if defined(UPGRADE_COMMON_STORE)
void UrrCerHandleIratSuspendCnf(RadioResourceControlEntity *urr_p);
void urrCerHandleSuspendConflict (void);
#endif/*UPGRADE_COMMON_STORE*/

#endif

Boolean UrrCerGetConRelReqDomains (UCN_DomainIdentity domain);/*********** add*/

void UrrCerUpdateCampedOnCellInfo(Int16 lac, Int16 rac); /************/

void UrrCerSetmultiplePlmnExistInMib(Boolean multiplePlmnExistInMib);/*********** add*/

void UrrCerInformSmcAbortInd (void);/*********** add*/

Boolean UrrCerReturnUeIsInCall (void);/*********** add*/
Boolean UrrCerChekPlmnIsTestplmn (void);/*********** add*/
void UrrCerSendRrcRelCnfAtDeactByMm(void);    /* ********** added */

/* ********** added begin */
void UrrCerClearDeferredRrcEstablishReq(UEstablishStatus cause);
/* ********** added end */
#if defined (ENABLE_WAIT_ABORT_SEARCH_CNF)
Boolean CerHandleIratAbortSearchCnf(void);
void HandleRrcConnectionReleaseWatiAbortSearchCnf(void);
void StartIdtAfterAbortSearchCnf(void);
#endif /* ENABLE_WAIT_ABORT_SEARCH_CNF */
/* ********** added begin */
#if defined (UPGRADE_DSDS)
Boolean UrrCerCheckEstSigConnectionsStatusWhenOOS (void);
#endif
/* ********** added end */
/* ********** added begin */
#if defined (UPGRADE_WIFI)
void urrCerHandleIratDsWifiAbortCnf(void);
#endif/*UPGRADE_WIFI*/
/* ********** added end */

#endif

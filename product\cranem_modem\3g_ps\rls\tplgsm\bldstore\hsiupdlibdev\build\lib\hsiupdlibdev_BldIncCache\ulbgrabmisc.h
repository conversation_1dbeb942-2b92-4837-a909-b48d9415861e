/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/3g.mod/lib/src/ulbgrabmisc.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2011/03/27 20:17:00 $
 **************************************************************************
 * File Description:
 *
 * RABMISC external functions for GSM to UMTS and UMTS to GSM inter-system
 * change
 **************************************************************************/

#if !defined (ULBGRABMISC_H)
#define       ULBGRABMISC_H

/***************************************************************************
 * Nested Include Files
 **************************************************************************/

#include <system.h>
#include <kernel.h>
#include <xrabmdata.h>

/* UMTS -> GSM ISC functions */
extern void
UlbgMappingGsmPdpsStateFromUmts (XRabmEntity *sn);

extern void
UlbgSnIscUmtsToGsm (XRabmEntity *sn, XRabmPdpEntity *pdp, Int16 ulNpduNum);

extern void
UlbgGsmIscResume (XRabmEntity *sn, Boolean fallback);

extern void
UlbgSnIscGmmRabmReestablishReq (XRabmEntity *sn);

extern void
UlbgSnIscGmmRabmReestablishRsp (XRabmEntity *sn); 

/* Added by zuohuaxu for CQ00019713 20120627, begin */
extern void 
UlbgSnIscGmmRabmFastDormancyRsp (XRabmEntity *sn);
/* Added by zuohuaxu for CQ00019713 20120627, end */

extern void
UlbgSnIscGmmRabmReestablishInd (XRabmEntity *sn); 
#if defined(UPGRADE_LTE)
/* GSM -> LTE ISC functions*/
void
UlbgMappingLtePdpsStateFromGsm (XRabmEntity *sn);
void
UlbgMappingLtePdpsStateFromUmts (XRabmEntity *sn);

#endif

/* GSM -> UMTS ISC functions */
extern void
UlbgMappingUmtsPdpsStateFromGsm (XRabmEntity *sn);

extern void
UlbgRabmIscGsmToUmts (XRabmEntity *sn,Nsapi nsapi, Int16 ulNpduNum);

extern void
UlbgRabmIscOldModeToUmtsUl (XRabmEntity *sn,Nsapi nsapi );


extern void
UlbgUmtsIscResume (XRabmEntity *sn, Boolean fallback);

extern void
UlbgRabmIscGmmRabmReestablishReq(XRabmEntity * sn);

extern void
UlbgUmtsIscEstablish (XRabmEntity *sn);

extern void
GmmIratDataTransferReq(XRabmEntity *sn);
#endif

/* END OF FILE */

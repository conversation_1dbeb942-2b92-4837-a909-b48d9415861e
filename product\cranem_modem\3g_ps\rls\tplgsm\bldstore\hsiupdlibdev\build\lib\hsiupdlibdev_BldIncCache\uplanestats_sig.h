/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2007 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/releases/development/branch_Marvell_Hermon_WW44_2007/tplgsm/modem/pscommon/3g_ut.mod/api/inc/uplanestats_sig.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2007/10/23 18:24:35 $
 *************************************************************************/
/** \file
 * File containing the structure of the 3G U-PLANE statistics signal
 *************************************************************************/
/** \mainpage User-Plan Statistics
 * Description of the 3G U-PLANE statistics.
 *
 * The 3G User-Plane statistics are introduced to aid investigations regarding
 * low throughput. It provided valuable insight into the entities involved in
 * data exchange (like PPP, RABM, PDCP, RLC, etc.).
 *
 * Each entity stores important statistic information in its internal
 * data structure. A signal is used to logg this information in Genie. This
 * approach uses the existing signal decoding facilities of Genie. So no
 * additional debug tool is needed.  The CPU-load overhaed of this approach is
 * also minimal because the data collection should be very straight forward and
 * the statistics signal is a relatively rare signal (compared to the signals
 * used for the actual data exchange).
 *
 * It is intended to have this User-Plane statistics logging always on to help
 * categorising issues once they occure.
 *************************************************************************/

#if !defined (UPLANE_STATS_SIG_H)
#define       UPLANE_STATS_SIG_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <system.h>
#include <gkisig.h>
#include <kitqid.h>
#include <ki_typ.h>
#include <ups_typ.h>
#include <uas_asn.h>
#include <cmac_sig.h>

/***************************************************************************
*   Manifest Constants
***************************************************************************/

/** Defines the maximum number of tasks in the u-plane chain covered by the
 * statistics signal.
 * \ingroup SigUPlane
 */
#define UPLANE_STATS_MAX_TASKS      10

/** Defines the maximum number of logged PDCP entities.
 * This number is the same as NUM_OF_RABS.
 * \ingroup PdcpStats
 */
#define MAX_LOGGED_UPDCP_ENTITIES   16
/** Defines the maximum number of logged RABM entities.
 * This number is the same as MAX_NUM_NSAPIS.
 * \ingroup PdcpStats
 */
#define MAX_LOGGED_RABM_ENTITIES    7

/** Defines the maximum number of logged bearers in the RLC statistics.
 * There are 36 bearers but usually only 10 are in use.
 * \ingroup RlcStats
 */
#define UPLANE_STATS_MAX_LOGGED_RLC_BEARERS     16

/***************************************************************************
*   Macro Functions
***************************************************************************/

/** Macro to forward statistics signal to the next task.
 * \ingroup SigUPlane
 */
#define UPLANE_STATS_FORWARD(sig_p)                                            \
    if (++((sig_p)->sig->uplaneStatisticsReport.currTask) >=                   \
            (sig_p)->sig->uplaneStatisticsReport.numTasks                      \
       )                                                                       \
    {                                                                          \
        (sig_p)->sig->uplaneStatisticsReport.currTask = 0;                     \
    }                                                                          \
    KiSendSignal ((sig_p)->sig->uplaneStatisticsReport.tasks                   \
                        [(sig_p)->sig->uplaneStatisticsReport.currTask],       \
                  sig_p                                                        \
                 );

/***************************************************************************
*   Types
***************************************************************************/

/** RLC statistic values of a transparent mode uplink bearer.
 * \ingroup RlcStats
 */
typedef struct UrlcBearerUplinkTmStatisticsTag
{
    /** Throughput (in bits per second).
     * Uplink throughput since establishment of the bearer entity.
     */
    Int32                           bps;
    /** Size (in bits) of smallest sent SDU. */
    Int32                           minSduBits;
    /** Average size (in bits) of all sent SDUs. */
    Int32                           averageSduBits;
    /** Size (in bits) of biggest sent SDU. */
    Int32                           maxSduBits;
    /** Number of sent SDUs with 0 <= size < 128 bytes. */
    Int32                           numSdusSize0000To0127;
    /** Number of sent SDUs with 128 <= size < 256 bytes. */
    Int32                           numSdusSize0128To0255;
    /** Number of sent SDUs with 256 <= size < 512 bytes. */
    Int32                           numSdusSize0256To0511;
    /** Number of sent SDUs with 512 <= size < 1024 bytes. */
    Int32                           numSdusSize0512To1023;
    /** Number of sent SDUs with size >= 1024  bytes. */
    Int32                           numSdusSize1024OrMore;
    /** Number of discarded uplink SDUs.
     * SDU discard could happen for example in case of an SDU discard timer expiry.
     */
    Int32                           numDiscardedSdus;
} UrlcBearerUplinkTmStatistics;


/** RLC statistic values of a transparent mode downlink bearer.
 * \ingroup RlcStats
 */
typedef struct UrlcBearerDownlinkTmStatisticsTag
{
    /** Throughput (in bits per second).
     * Downlink throughput since establishment of the bearer entity.
     */
    Int32                           bps;
    /** Size (in bits) of smallest received SDU. */
    Int32                           minSduBits;
    /** Average size (in bits) of all received SDUs. */
    Int32                           averageSduBits;
    /** Size (in bits) of biggest received SDU. */
    Int32                           maxSduBits;
    /** Number of received SDUs with 0 <= size < 128 bytes. */
    Int32                           numSdusSize0000To0127;
    /** Number of received SDUs with 128 <= size < 256 bytes. */
    Int32                           numSdusSize0128To0255;
    /** Number of received SDUs with 256 <= size < 512 bytes. */
    Int32                           numSdusSize0256To0511;
    /** Number of received SDUs with 512 <= size < 1024 bytes. */
    Int32                           numSdusSize0512To1023;
    /** Number of received SDUs with size >= 1024  bytes. */
    Int32                           numSdusSize1024OrMore;
} UrlcBearerDownlinkTmStatistics;


/** RLC statistic values of an unacknowledged mode uplink bearer.
 * \ingroup RlcStats
 */
typedef struct UrlcBearerUplinkUmStatisticsTag
{
    /** Throughput (in bits per second).
     * Uplink throughput since establishment of the bearer entity.
     */
    Int32                           bps;
    /** Size (in bits) of smallest sent SDU. */
    Int32                           minSduBits;
    /** Average size (in bits) of all sent SDUs. */
    Int32                           averageSduBits;
    /** Size (in bits) of biggest sent SDU. */
    Int32                           maxSduBits;
    /** Number of sent SDUs with 0 <= size < 128 bytes. */
    Int32                           numSdusSize0000To0127;
    /** Number of sent SDUs with 128 <= size < 256 bytes. */
    Int32                           numSdusSize0128To0255;
    /** Number of sent SDUs with 256 <= size < 512 bytes. */
    Int32                           numSdusSize0256To0511;
    /** Number of sent SDUs with 512 <= size < 1024 bytes. */
    Int32                           numSdusSize0512To1023;
    /** Number of sent SDUs with size >= 1024  bytes. */
    Int32                           numSdusSize1024OrMore;
    /** Number of discarded uplink SDUs.
     * SDU discard could happen for example in case of an SDU discard timer expiry.
     */
    Int32                           numDiscardedSdus;
} UrlcBearerUplinkUmStatistics;


/** RLC statistic values of an unacknowledged mode downlink bearer.
 * \ingroup RlcStats
 */
typedef struct UrlcBearerDownlinkUmStatisticsTag
{
    /** Throughput (in bits per second).
     * Downlink throughput since establishment of the bearer entity.
     */
    Int32                           bps;
    /** Size (in bits) of smallest received SDU. */
    Int32                           minSduBits;
    /** Average size (in bits) of all received SDUs. */
    Int32                           averageSduBits;
    /** Size (in bits) of biggest received SDU. */
    Int32                           maxSduBits;
    /** Number of received SDUs with 0 <= size < 128 bytes. */
    Int32                           numSdusSize0000To0127;
    /** Number of received SDUs with 128 <= size < 256 bytes. */
    Int32                           numSdusSize0128To0255;
    /** Number of received SDUs with 256 <= size < 512 bytes. */
    Int32                           numSdusSize0256To0511;
    /** Number of received SDUs with 512 <= size < 1024 bytes. */
    Int32                           numSdusSize0512To1023;
    /** Number of received SDUs with size >= 1024  bytes. */
    Int32                           numSdusSize1024OrMore;
} UrlcBearerDownlinkUmStatistics;


/** RLC statistic values of an acknowledged mode uplink bearer.
 * \ingroup RlcStats
 */
typedef struct UrlcBearerUplinkAmStatisticsTag
{
    /** Throughput (in bits per second).
     * Uplink throughput since establishment of the bearer entity.
     */
    Int32                           bps;
    /** Size (in bits) of smallest sent SDU. */
    Int32                           minSduBits;
    /** Average size (in bits) of all sent SDUs. */
    Int32                           averageSduBits;
    /** Size (in bits) of biggest sent SDU. */
    Int32                           maxSduBits;
    /** Number of sent SDUs with 0 <= size < 128 bytes. */
    Int32                           numSdusSize0000To0127;
    /** Number of sent SDUs with 128 <= size < 256 bytes. */
    Int32                           numSdusSize0128To0255;
    /** Number of sent SDUs with 256 <= size < 512 bytes. */
    Int32                           numSdusSize0256To0511;
    /** Number of sent SDUs with 512 <= size < 1024 bytes. */
    Int32                           numSdusSize0512To1023;
    /** Number of sent SDUs with size >= 1024  bytes. */
    Int32                           numSdusSize1024OrMore;
    /** Number of discarded uplink SDUs.
     * SDU discard could happen for example in case of an SDU discard timer expiry.
     */
    Int32                           numDiscardedSdus;
    /** Number of sent data PDUs. */
    Int32                           numDataPdus;
    /** Number of sent reset PDUs.
     * Reset PDUs are sent by RLC(UE) when the RLC in the UE and the RLC in
     * UTRAN are not sychronised any more. A high number indicates a weak air
     * interface or a bug in the RLC(UE)-RLC(UTRAN) link.
     */
    Int32                           numResetPdus;
    /** Number of sent reset-ack PDUs.
     * Reset-Ack PDUs are sent by RLC(UE) as response to a received Reset PDU.
     * RLC(UTRAN) sends a Reset PDU when it considers the link between the RLC
     * in the UE and the RLC in UTRAN as not sychronised any more. A high
     * number indicates a weak air interface or a bug in the
     * RLC(UE)-RLC(UTRAN) link.
     */
    Int32                           numResetAckPdus;
    /** Number of sent status PDUs.
     * Status PDUs are used for RLC(UE)-RLC(UTRAN) communication.
     */
    Int32                           numStatusPdus;
    /** Number of sent status PDUs containing only padding.
     * Status PDUs are used for RLC(UE)-RLC(UTRAN) communication. However,
     * empty Status PDUs are rare. A high number here might indicate a reason
     * for low throughput figures.
     */
    Int32                           numStatusPaddingPdus;
} UrlcBearerUplinkAmStatistics;


/** RLC statistic values of an acknowledged mode downlink bearer.
 * \ingroup RlcStats
 */
typedef struct UrlcBearerDownlinkAmStatisticsTag
{
    /** Throughput (in bits per second).
     * Downlink throughput since establishment of the bearer entity.
     */
    Int32                           bps;
    /** Size (in bits) of smallest received SDU. */
    Int32                           minSduBits;
    /** Average size (in bits) of all received SDUs. */
    Int32                           averageSduBits;
    /** Size (in bits) of biggest received SDU. */
    Int32                           maxSduBits;
    /** Number of received SDUs with 0 <= size < 128 bytes. */
    Int32                           numSdusSize0000To0127;
    /** Number of received SDUs with 128 <= size < 256 bytes. */
    Int32                           numSdusSize0128To0255;
    /** Number of received SDUs with 256 <= size < 512 bytes. */
    Int32                           numSdusSize0256To0511;
    /** Number of received SDUs with 512 <= size < 1024 bytes. */
    Int32                           numSdusSize0512To1023;
    /** Number of received SDUs with size >= 1024  bytes. */
    Int32                           numSdusSize1024OrMore;
    /** Number of received data PDUs. */
    Int32                           numDataPdus;
    /** Number of received reset PDUs.
     * RLC(UTRAN) sends a Reset PDU when it considers the link between the RLC
     * in the UE and the RLC in UTRAN as not sychronised any more. A high
     * number indicates a weak air interface or a bug in the
     * RLC(UE)-RLC(UTRAN) link.
     */
    Int32                           numResetPdus;
    /** Number of received reset-ack PDUs.
     * Reset-Ack PDUs are received as response to sent Reset PDUs.
     * Reset PDUs are sent by RLC(UE) when the RLC in the UE and the RLC in
     * UTRAN are not sychronised any more. A high number indicates a weak air
     * interface or a bug in the RLC(UE)-RLC(UTRAN) link.
     */
    Int32                           numResetAckPdus;
    /** Number of received status PDUs.
     * Status PDUs are used for RLC(UE)-RLC(UTRAN) communication.
     */
    Int32                           numStatusPdus;
    /** Number of received duplicated PDUs.
     * Duplicated PDUs are discarded by RLC (with exceptions). A high number
     * here might indicate a reason for low downlink throughput figures, a
     * weak air interface or a bug in the RLC(UE)-RLC(UTRAN) link.
     */
    Int32                           numDuplicatedPdus;
    /** Number of received inconsistent PDUs.
     * Inconsistent PDUs are discarded by RLC. A high number here might
     * indicate a reason for low downlink throughput figures, a
     * weak air interface or a bug in the RLC(UE)-RLC(UTRAN) link.
     */
    Int32                           numInconsistentPdus;
    /** Number of discarded PDUs which are neither inconsistent nor
     * duplications.
     * Reasons for such discards could be a reset, insufficient storage memory
     * or a PDU which is outside the receive window. A high number here might
     * indicate a reason for low downlink throughput figures, a
     * weak air interface or a bug in the RLC(UE)-RLC(UTRAN) link.
     */
    Int32                           numOtherDiscardedPdus;
} UrlcBearerDownlinkAmStatistics;


/** Union of RLC statistic values for each uplink bearer mode.
 * \ingroup RlcStats
 */
typedef union UrlcBearerUplinkStatisticsSetTag
{
    /** Transparent mode (TM) uplink statistics. */
    UrlcBearerUplinkTmStatistics    tm;
    /** Unacknowledged mode (UM) uplink statistics. */
    UrlcBearerUplinkUmStatistics    um;
    /** Acknowledged mode (AM) uplink statistics. */
    UrlcBearerUplinkAmStatistics    am;
} UrlcBearerUplinkStatisticsSet;


/** Union of RLC statistic values for each downlink bearer mode.
 * \ingroup RlcStats
 */
typedef union UrlcBearerDownlinkStatisticsSetTag
{
    /** Transparent mode (TM) downlink statistics. */
    UrlcBearerDownlinkTmStatistics  tm;
    /** Unacknowledged mode (UM) downlink statistics. */
    UrlcBearerDownlinkUmStatistics  um;
    /** Acknowledged mode (AM) downlink statistics. */
    UrlcBearerDownlinkAmStatistics  am;
} UrlcBearerDownlinkStatisticsSet;


/** RLC statistic values of an uplink bearer.
 * \ingroup RlcStats
 */
typedef struct UrlcBearerUplinkStatisticsTag
{
    /** Bearer mode.
     * Bearer mode determines the statistics set valid in \c set.
     * Bearer mode can be transparent (TM), unacknowlegded (UM) or acknowledged
     * (AM).
     * TM bearers exchange data without any RLC error detection or RLC error
     * correction. Higher layers can not be informed about erroneous PDUs or
     * SDUs.
     * UM bearers exchange data with RLC error detection but without RLC error
     * correction. Erroneous PDUs and SDU are discarded. Higher layers can be
     * informed about data gaps in downlink data.
     * AM bearers exchange data with RLC error correction (retransmission).
     * Therefore an AM bearer always combines uplink and downlink.
     */
    UrlBearerMode                   bearerMode;
    /** Union of uplink statistics sets. */
    UrlcBearerUplinkStatisticsSet   set;
} UrlcBearerUplinkStatistics;


/** RLC statistic values of a downlink bearer.
 * \ingroup RlcStats
 */
typedef struct UrlcBearerDownlinkStatisticsTag
{
    /** Bearer mode.
     * Bearer mode determines the statistics set valid in \c set.
     * Bearer mode can be transparent (TM), unacknowlegded (UM) or acknowledged
     * (AM).
     * TM bearers exchange data without any RLC error detection or RLC error
     * correction. Higher layers can not be informed about erroneous PDUs or
     * SDUs.
     * UM bearers exchange data with RLC error detection but without RLC error
     * correction. Erroneous PDUs and SDU are discarded. Higher layers can be
     * informed about data gaps in downlink data.
     * AM bearers exchange data with RLC error correction (retransmission).
     * Therefore an AM bearer always combines uplink and downlink.
     */
    UrlBearerMode                   bearerMode;
    /** Union of downlink statistics sets. */
    UrlcBearerDownlinkStatisticsSet set;
} UrlcBearerDownlinkStatistics;


/** RLC statistic values of a bearer.
 * \ingroup RlcStats
 */
typedef struct UrlcBearerStatisticsTag
{
    /** Bearer ID. */
    BearerIdentity                  bearerIdentity;

    /** Present flag for RLC uplink bearer statistics.
     * Indicates whether \c ul is semantically meaningful (TRUE) or not (FALSE).
     */
    Boolean                         ulPresent;
    /** RLC uplink bearer statistics. */
    UrlcBearerUplinkStatistics      ul;

    /** Present flag for RLC downlink bearer statistics.
     * Indicates whether \c dl is semantically meaningful (TRUE) or not (FALSE).
     */
    Boolean                         dlPresent;
    /** RLC downlink bearer statistics. */
    UrlcBearerDownlinkStatistics    dl;
} UrlcBearerStatistics;


/** \defgroup MacStats MAC Statistic Values
 *
 * This section describes statistic values of MAC.
 *
 * Statistic values for MAC are mainly the quality of the air interface.
 *
 * @{
 */

/** MAC statistic values.
 */
typedef struct UmacStatisticsTag
{
    /** Kernel tick when last updated. */
    KernelTicks                     timestamp;
    /** Current number of PhyFrameInd in queue. */
    Int32                           qFrameIndLength;
    /** Maximum number of PhyFrameInd in queue. */
    Int32                           maxqFrameIndLength;
    /** Mean Umac Data Tx Turnaround Time */
    KernelTicks                     meanUmacTxDataTime;
    /** Maximum Umac Data Tx Turnaround Time. */
    KernelTicks                     maxUmacTxDataTime;
    /** Number of valid \c qualityMeasurements blocks. */
    Int8                            nMeasurementBlocks;
    /** Quality measurement for each TrCH */
    CmacQualityMeasurement          qualityMeasurements[UPS_MAX_DL_NO_OF_TRCH];
} UmacStatistics;

/** @} */  /* End of MacStats group */


/** \defgroup RlcStats RLC Statistic Values
 *
 * This section describes statistic values of RLC.
 *
 * RLC statistic are bearer based. Each bearer can have uplink statistics or
 * downlink statistics or both.
 *
 * RLC exchanges SDUs with the higher layers and it exchanges PDUs with MAC.
 * The RLC statistics are mainly concerned with profiling the size of the SDUs
 * exchanged, throughput, discarded SDUs and PDUs and the condition of the air
 * interface.
 *
 * @{
 */

/** RLC statistic values.
 */
typedef struct UrlcStatisticsTag
{
    /** Kernel tick when last updated. */
    KernelTicks                     timestamp;
    /** Number of downlink PDUs with invalid bearer information.
     * Downlink PDUs should always be associated to an existing bearer. PDUs
     * which can not be associated to a bearer are silently discarded. A high
     * number here might indicate a reason for low downlink throughput figures,
     * a weak air interface or a bug in the RLC(UE)-RLC(UTRAN) link.
     */
    Int32                           invalidBearerDownlinkPdus;
    /** Number of bearer reports in \c bearerStat. */
    Int8                            numberBearerStats;
    /** Bearer statistic values. */
    UrlcBearerStatistics            bearerStat [UPLANE_STATS_MAX_LOGGED_RLC_BEARERS];
} UrlcStatistics;

/** @} */  /* End of RlcStats group */


/** \defgroup PdcpStats PDCP and RABM Statistic Values
 *
 * This section describes statistic values of PDCP and RABM.
 *
 * Statistic values for PDCP are, for example, the number of discarded NPDUs
 * in uplink and downlink direction.
 *
 * Statistic values for RABM are, for example, the number of discarded NPDUs
 * in uplink and downlink direction.
 *
 * @{
 */

/** PDCP statistic values.
 */
typedef struct UpdcpStatisticsTag
{
    /** Kernel tick when last updated. */
    KernelTicks                     timestamp;
    /* PDCP statistics */
    Boolean                         updcpRabPresent [MAX_LOGGED_UPDCP_ENTITIES];
    Int16                           updcpLlistLength [MAX_LOGGED_UPDCP_ENTITIES];
    Int16                           updcpNumUlPdusDiscarded;
    Int16                           updcpNumDlPdusDiscarded;
    /* RABM statistics */
    Int8                            rabmNumPdps;     
    Int16                           rabmNsapi [MAX_LOGGED_RABM_ENTITIES];
    Int16                           rabQueueLength [MAX_LOGGED_RABM_ENTITIES];
#if !defined (UPGRADE_EXCLUDE_2G)
    Int16                           rabmIscQueue1Length [MAX_LOGGED_RABM_ENTITIES];
    Int16                           rabmIscQueue2Length [MAX_LOGGED_RABM_ENTITIES];
#endif
    Int16                           rabmNumUlNpdusDiscarded;
    Int16                           rabmNumDlNpdusDiscarded;
} UpdcpStatistics;

/** @} */  /* End of PdcpStats group */


/** \defgroup PppStats PPP Statistic Values
 *
 * This section describes statistic values of PPP.
 *
 * PPP currenly does not have statistic values.
 *
 * @{
 */

/** PPP statistic values.
 */
typedef struct PppStatisticsTag
{
    /** Kernel tick when last updated. */
    KernelTicks                     timestamp;
    Int8                            dummy;
} PppStatistics;

/** @} */  /* End of PppStats group */


/** \defgroup CsdiStats CSDI Statistic Values
 *
 * This section describes statistic values of CSDI.
 *
 * CSDI currenly does not have statistic values.
 *
 * @{
 */

/** CSDI statistic values.
 */
typedef struct CsdiStatisticsTag
{
    /** Kernel tick when last updated. */
    KernelTicks                     timestamp;
    Int8                            dummy;
} CsdiStatistics;

/** @} */  /* End of CsdiStats group */


/** \defgroup RtfdpStats RTFDP Statistic Values
 *
 * This section describes statistic values of RTFDP.
 *
 * RTFDP currenly does not have statistic values.
 *
 * @{
 */

/** RTFDP statistic values.
 */
typedef struct RtfdpStatisticsTag
{
    /** Kernel tick when last updated. */
    KernelTicks                     timestamp;
    Int8                            dummy;
} RtfdpStatistics;

/** @} */  /* End of RtfdpStats group */


/** \defgroup MuxStats Multiplexer Statistic Values
 *
 * This section describes statistic values of the multiplexer.
 *
 * The multiplexer currenly does not have statistic values.
 *
 * @{
 */

/** MUX statistic values.
 */
typedef struct MuxStatisticsTag
{
    /** Kernel tick when last updated. */
    KernelTicks                     timestamp;
    Int8                            dummy;
} MuxStatistics;

/** @} */  /* End of MuxStats group */


/** \defgroup SigUPlane User-Plane Statistics Signalling
 *
 * This section defines the signal exchanged for user-plane statistics.
 *
 * At 3G activation, MAC creates an empty UplaneStatisticsReport, populates
 * the task routing list, eg:
 *     RLC, PDCP, PPP, CSDI, RTFTP, MUX ... MAC
 * sets thisTask=0 and numTasks=7, then sends the signal to itself, so that
 * handling is the same for new and recycled signals.
 *
 * When MAC receives a UplaneStatisticsReport, it sets thisTask=0  then
 * starts a timer (kernel or frame counter) to ensure that statistics
 * gathering doesn't take too many MIPS (perhaps once every 2s)
 *
 * When the timer expires, it clears the MAC data, including the present
 * flag, poplulates the MAC structure, sets the timestamp and present flag,
 * updates thisTask = (thisTask+1) % numTasks, then sends the signal to the
 * next task in the chain
 *
 * When any other task receives UplaneStatisticsReport, it clears its own
 * data, including the present flag, poplulates its structure, sets the
 * timestamp and present flag, updates thisTask = (thisTask+1) % numTasks,
 * then sends the signal to the next task in the chain
 *
 * \note UplaneStatisticsReport should never be destroyed, so that it is always
 * visible in a ramlog
 *
 * @{
 */

/** Signal struct of the statistics signal.
 */
typedef struct UplaneStatisticsReportTag
{
    /** Number of tasks in routing chain. */
    Int8                            numTasks;
    /** Index of current task in routing chain. */
    Int8                            currTask;
    /** Task routing chain. */
    TaskId                          tasks [UPLANE_STATS_MAX_TASKS];

    /** Present flag for MAC statistics.
     * Indicates whether \c mac is semantically meaningful (TRUE) or not (FALSE).
     */
    Boolean                         macPresent;
    /** MAC statistics. */
    UmacStatistics                  mac;

    /** Present flag for RLC statistics.
     * Indicates whether \c rlc is semantically meaningful (TRUE) or not (FALSE).
     */
    Boolean                         rlcPresent;
    /** RLC statistics */
    UrlcStatistics                  rlc;

    /** Present flag for PDCP statistics.
     * Indicates whether \c pdcp is semantically meaningful (TRUE) or not (FALSE).
     */
    Boolean                         pdcpPresent;
    /** PDCP statistics */
    UpdcpStatistics                 pdcp;

    /** Present flag for PPP statistics.
     * Indicates whether \c ppp is semantically meaningful (TRUE) or not (FALSE).
     */
    Boolean                         pppPresent;
    /** PPP statistics */
    PppStatistics                   ppp;

    /** Present flag for CSDI statistics.
     * Indicates whether \c csdi is semantically meaningful (TRUE) or not (FALSE).
     */
    Boolean                         csdiPresent;
    /** CSDI statistics */
    CsdiStatistics                  csdi;

    /** Present flag for RTFDP statistics.
     * Indicates whether \c rtfdp is semantically meaningful (TRUE) or not (FALSE).
     */
    Boolean                         rtfdpPresent;
    /** RTFDP statistics */
    RtfdpStatistics                 rtfdp;

    /** Present flag for MUX statistics.
     * Indicates whether \c mux is semantically meaningful (TRUE) or not (FALSE).
     */
    Boolean                         muxPresent;
    /** MUX statistics */
    MuxStatistics                   mux;

    /* Add new task structures here, adjusting UPLANE_STATS_MAX_TASKS accordingly */
} UplaneStatisticsReport;

/** @} */  /* End of SigUPlane group */


extern void UmacSetTimeSendPhyDataReqStats (void);
extern Int8 UmaDlStatsGetQualityMeasurement (CmacQualityMeasurement *qualityMeasurements);

#endif /* UPLANE_STATS_SIG_H */
/* End of uplanestats_sig.h */

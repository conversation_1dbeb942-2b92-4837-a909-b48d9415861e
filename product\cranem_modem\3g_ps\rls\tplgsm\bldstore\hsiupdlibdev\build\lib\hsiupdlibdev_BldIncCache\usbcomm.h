/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/devices/transports/usb.mod/pub/src/usbcomm.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/24 10:16:13 $
 **************************************************************************
 *  File Description :
 *    Data types etc for USB Communications Class interface application.
 **************************************************************************/
#if defined(UPGRADE_USB)
#if defined(USB_COMMS_DEVICE_INTERFACE)

#ifndef USB_COMM_H
#define USB_COMM_H

/*******************************************************************************
 * Functions exported from the CDC device modules; called by stack
 ******************************************************************************/
extern void usbCommDevInitialise( void );
extern void usbCommNotifyInitialise( void );
extern Boolean usbCommDevAppClassDevicePayload( UsbDataQuantum dataQuantum );


/*******************************************************************************
 * Functions exported from the DevApp layer; called by stack
 ******************************************************************************/
extern Boolean usbCommDevAppClassDeviceRequest( Int8 epNum, Int8* setupPkt );


/*******************************************************************************
 * Functions exported from the CDC device module; called by application
 ******************************************************************************/
extern void usbCommNotifyConfigure( TaskId respTaskId );

extern void usbCommNotifyTxSerialState( Boolean dsr, Boolean cts,
                                        Boolean ri,  Boolean dcd,
                                        Boolean ore, Boolean pe,
                                        Boolean fe,  Boolean brk,
                                        TaskId respTaskId );


/*******************************************************************************
 * Functions exported from the application; called by DevApp layer
 ******************************************************************************/
extern void usbCommDevAppInitialise(void);
extern Int32 usbCommDevMaxOutTransfer( Int8 physicalEndpoint );

#endif /* USB_COMM_H */
#endif /* USB_COMMS_DEVICE_INTERFACE */
#endif /* UPGRADE_USB */


/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/***************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 ***************************************************************************
 *
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urrriedd.h#3 $
 *   $Revision: #3 $
 *   $DateTime: 2007/03/19 13:59:30 $
 *
 ***************************************************************************
 *
 * File Description:
 *
 *    Header file for the DCCH message handling functionality of the RIE
 *    RRC module.
 *
 ***************************************************************************
 *
 ***************************************************************************/

#if !defined (URRRIEDD_H)
#define       URRRIEDD_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <uas_asn.h>
#include <utper.h>          /* For PerBuffer */
#include <urrtypes.h>       /* For UrrRxMsgVersion */

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/

/***************************************************************************
 *  Macro Functions
 ***************************************************************************/

/***************************************************************************
 * Types
 ***************************************************************************/

 /***************************************************************************
 * Typed Constants
 ***************************************************************************/

/***************************************************************************
 * Global Data declarations
 ***************************************************************************/

/***************************************************************************
 * Global Function Prototypes
 ***************************************************************************/

UrrRxMsgVersion UrrRieDdUActiveSetUpdate (
    PerBuffer * perBuffer_p,
    UActiveSetUpdate * msg_p);

void UrrRieDdUActiveSetUpdate_R7 (
    PerBuffer * perBuffer_p,
    UrrRxMsgVersion rxMsgVersion,
    UActiveSetUpdate *msg_p);

UrrRxMsgVersion UrrRieDdUCellUpdateConfirm (
    PerBuffer * perBuffer_p,
    UCellUpdateConfirm * msg_p);

void UrrRieDdUCellUpdateConfirm_R7 (
    PerBuffer * perBuffer_p,
    UrrRxMsgVersion rxMsgVersion,
    UCellUpdateConfirm *msg_p);

UrrRxMsgVersion UrrRieDdUMeasurementControl (
    PerBuffer * perBuffer_p,
    UMeasurementControl * msg_p);

void UrrRieDdUMeasurementControl_R7 (
    PerBuffer * perBuffer_p,
    UMeasurementControl * msg_p);


UrrRxMsgVersion UrrRieDdUPhysicalChannelReconfiguration (
    PerBuffer * perBuffer_p,
    UPhysicalChannelReconfiguration * msg_p);

UrrRxMsgVersion UrrRieDdURadioBearerReconfiguration (
    PerBuffer * perBuffer_p,
    URadioBearerReconfiguration * msg_p);

void UrrRieDdURadioBearerReconfiguration_r7(
    PerBuffer * perBuffer_p,
    URadioBearerReconfiguration * msg_p);

UrrRxMsgVersion UrrRieDdURadioBearerRelease (
    PerBuffer * perBuffer_p,
    URadioBearerRelease * msg_p);

void UrrRieDdURadioBearerRelease_R7 (
    PerBuffer * perBuffer_p,
    URadioBearerRelease * msg_p);

UrrRxMsgVersion UrrRieDdURadioBearerSetup (
    PerBuffer * perBuffer_p,
    URadioBearerSetup * msg_p);

void UrrRieDdURadioBearerSetup_R7 (
    PerBuffer * perBuffer_p,
    UrrRxMsgVersion rxMsgVersion,
    URadioBearerSetup *msg_p);

UrrRxMsgVersion UrrRieDdURRCConnectionRelease (
    PerBuffer * perBuffer_p,
    URRCConnectionRelease * msg_p);

UrrRxMsgVersion UrrRieDdUSecurityModeCommand(
    PerBuffer * perBuffer_p,
    USecurityModeCommand * msg_p);

void UrrRieDdUSecurityModeCommand_R7 (
    PerBuffer * perBuffer_p,
    UrrRxMsgVersion rxMsgVersion,
    USecurityModeCommand * msg_p);

UrrRxMsgVersion UrrRieDdUTransportChannelReconfiguration (
    PerBuffer * perBuffer_p,
    UTransportChannelReconfiguration * msg_p);

void UrrRieDdUTransportChannelReconfiguration_R7 (
    PerBuffer * perBuffer_p,
    UTransportChannelReconfiguration * msg_p);


UrrRxMsgVersion UrrRieDdUUeCapabilityEnquiry (
    PerBuffer * perBuffer_p,
    UUECapabilityEnquiry * msg_p);

UrrRxMsgVersion UrrRieDdUURAUpdateConfirm (
    PerBuffer * perBuffer_p,
    UURAUpdateConfirm * msg_p);

void UrrRieDdUURAUpdateConfirm_R7 (
    PerBuffer * perBuffer_p,
    UrrRxMsgVersion rxMsgVersion,
    UURAUpdateConfirm * msg_p);

UrrRxMsgVersion UrrRieDdUUTRANMobilityInformation (
    PerBuffer * perBuffer_p,
    UUTRANMobilityInformation * msg_p);

void UrrRieDdUUTRANMobilityInformation_R7 (
    PerBuffer * perBuffer_p,
    UUTRANMobilityInformation *msg_p);

UrrRxMsgVersion UrrRieDdUHandoverFromUTRANCommand_GSM (
    PerBuffer * perBuffer_p,
    UHandoverFromUTRANCommand_GSM * msg_p);

/*static void CopyUMeasurementControl_v6a0NonCriticalExtensions_to_r7(
    PerBuffer * perBuffer_p,
    UMeasurementControl_v6a0NonCriticalExtensions *v6a0NcExt_p,
    UMeasurementCommand_r7 *r7_p);

static void CopyUPeriodicReportingInfo_1b_to_modify_r7(
    PerBuffer * perBuffer_p,
    UIntraFreqReportingCriteria_1b_r5 *r5_p,
    UMeasurementCommand_r7_modify *r7_p);

static void CopyUPeriodicReportingInfo_1b_to_setup_r7(
    PerBuffer * perBuffer_p,
    UIntraFreqReportingCriteria_1b_r5 *r5_p,
    UMeasurementType_r7 *r7_p);

static void CopyUIntraFreqReportingCriteria_1b_r5_to_r7(
    PerBuffer *perBuffer_p,
    UIntraFreqReportingCriteria_1b_r5 *r5_p,
    UMeasurementCommand_r7 *r7_p); */

void UrrRieCopyUPeriodicReportingInfo_1b_to_UIntraFrequencyMeasurement_r7(
    PerBuffer * perBuffer_p,
    UIntraFreqReportingCriteria_1b_r5 * r5_p,
    UIntraFrequencyMeasurement_r7 * r7_p);

void UrrRieCopyUPeriodicReportingInfo_1b_to_UInterFrequencyMeasurement_r7(
    PerBuffer * perBuffer_p,
    UIntraFreqReportingCriteria_1b_r5 * r5_p,
    UInterFrequencyMeasurement_r7 * r7_p);


#endif /* !defined (URRRIEDD_H) */

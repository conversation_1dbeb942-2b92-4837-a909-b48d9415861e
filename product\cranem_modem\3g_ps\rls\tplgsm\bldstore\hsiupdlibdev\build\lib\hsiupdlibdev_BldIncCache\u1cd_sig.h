/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/l1/3g.mod/pub/src/u1cd_sig.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/11/09 16:03:40 $
 **************************************************************************
 * Group            : U1, Signals
 * File Description : CalDev signal structures for 3G Layer 1.
 **************************************************************************/

#ifndef U1CD_SIG_H
#define U1CD_SIG_H

/*******************************************************************************
** Includes
*******************************************************************************/

#include <system.h>
#include <gkitask.h>
#include <u1cd_msg.h>

/*******************************************************************************
** Constants
*******************************************************************************/

/*******************************************************************************
** Typedefs
*******************************************************************************/
/*******************************************************************************
* Typedef     : U1itDebug3ParamInd
* Group       : U1, Signals, IT
* Type        : U1Debug3ParamAds
* Description : This signal structure is used to provide a simple
*                   mechanism for dumping 3 Int32 parameters from one processor
*                   to another. This is used by the M_U1SyDbg3Param macro.
*******************************************************************************/

/*******************************************************************************
* Typedef     : U1cdEmptySignal
* Group       : U1, Signals, CD
* Type        : Int8
* Description : Empty Signal on the U1CD GKI interface.
*******************************************************************************/
typedef Int8 U1cdEmptySignal;

/*******************************************************************************
* Typedef     : U1cdEnterReq
* Group       : U1, Signals, CD
* Type        : struct
* Description : Signal to request 3G Layer 1 to go to Cal/Dev state.
*******************************************************************************/
typedef struct U1cdEnterReqTag
{
    TaskId     u1cdReturnTask;

} U1cdEnterReq;

/*******************************************************************************
* Typedef     : U1cdEnterCnf
* Group       : U1, Signals, CD
* Type        : U1cdEmptySignal
* Description : Signal to confirm that 3G Layer 1 is now in Cal/Dev state.
*******************************************************************************/
typedef U1cdEmptySignal U1cdEnterCnf;

/*******************************************************************************
* Typedef     : U1cdExitReq
* Group       : U1, Signals, CD
* Type        : U1cdEmptySignal
* Description : Signal to request 3G Layer 1 to exit Cal/Dev state to NULL state.
*******************************************************************************/
typedef U1cdEmptySignal U1cdExitReq;

/*******************************************************************************
* Typedef     : U1cdExitCnf
* Group       : U1, Signals, CD
* Type        : U1cdEmptySignal
* Description : Signal to confirm that 3G Layer 1 has exited Cal/Dev state to
*               NULL state.
*******************************************************************************/
typedef U1cdEmptySignal U1cdExitCnf;

#endif
/* END OF FILE */

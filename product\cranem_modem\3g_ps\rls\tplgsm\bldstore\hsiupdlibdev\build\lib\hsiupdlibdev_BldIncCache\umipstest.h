/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

#if !defined (UMIPSTEST_H)
#define       UMIPSTEST_H

/******************************************************************************
 * Include Files
 *****************************************************************************/
#if !defined (ON_PC)
#include <cpu_mips_test.h>
#endif
/******************************************************************************
 * Macros
 *****************************************************************************/
#if !defined (ON_PC)
#define UMIPS_TEST_DATA_MASK                0x007F
#define UMIPS_TEST_WITH_DATA(opcode, data)  //CPU_MIPS_TEST_R(opcode|((Int8)(data)&UMIPS_TEST_DATA_MASK))
#define UMIPS_TEST(opcode)                  //CPU_MIPS_TEST_R(opcode)
#else
#define UMIPS_TEST_WITH_DATA(opcode, data)
#define UMIPS_TEST(opcode)
#endif /* ON_PC */

 /******************************************************************************
 * Type Definitions
 *****************************************************************************/
typedef enum UmipsTestOpcodesTag
{
    UmacEdchUpdateTrafficIndFn_begin                = 0x8430,
    UmacEdchUpdateTrafficIndFn_end                  = 0x8431,
    UmacEdchTrafficIndFn_begin                      = 0x8432,
    UmacEdchTrafficIndFn_end                        = 0x8433,
    UmacEdchTxStatusIndFn_begin                     = 0x8434,
    UmacEdchTxStatusIndFn_end                       = 0x8435,
    UmacEdchHandlePhyRxChanData_begin               = 0x8436,
    UmacEdchHandlePhyRxChanData_end                 = 0x8437,
    UmaEPhyEdchTtiInfo_begin                        = 0x8438,
    UmaEPhyEdchTtiInfo_end                          = 0x8439,
    UmaEHarqProcessManage_begin                     = 0x843A,
    UmaEHarqProcessManage_end                       = 0x843B,
    UmaEdchReadRx_begin                             = 0x843C,
    UmaEdchReadRx_end                               = 0x843D,
    UrlAmRxProcessAckNack_begin                     = 0x843E,
    UrlAmRxProcessAckNack_end                       = 0x843F,
    UmaEMultiplexing_begin                          = 0x8440,
    UmaEMultiplexing_end                            = 0x8441,
    UmaEMuxReleaseRequestResources_begin            = 0x8442,
    UmaEMuxReleaseRequestResources_end              = 0x8443,
    UmaESguUpdate_begin                             = 0x8444,
    UmaESguUpdate_end                               = 0x8445,
    UmaESiAnalyzeRlcRequestParams_begin             = 0x8446,
    UmaESiAnalyzeRlcRequestParams_end               = 0x8447,
    UmaESiFindHlidAndHlbs_begin                     = 0x8448,
    UmaESiFindHlidAndHlbs_end                       = 0x8449,
    UrlAmTxNewSduSegment_begin                      = 0x844A,
    UrlAmTxNewSduSegment_end                        = 0x844B,
    UmaESiCalculate_begin                           = 0x844C,
    UmaESiCalculate_end                             = 0x844D,
    UmaETfcCalcScheduledGrantPayload_begin          = 0x844E,
    UmaETfcCalcScheduledGrantPayload_end            = 0x844F,
    UmaETfcSelection_begin                          = 0x8450,
    UmaETfcSelection_end                            = 0x8451,
    UmaEHappyBitSetting_begin                       = 0x8452,
    UmaEHappyBitSetting_end                         = 0x8453,
    UrlAmTxGetTransportBlockEdch_begin              = 0x8454,
    UrlAmTxGetTransportBlockEdch_end                = 0x8455,
    UrlAmTxGetNextDataPdu_begin                     = 0x8456,
    UrlAmTxGetNextDataPdu_end                       = 0x8457,
    UrlAmTxCheckDataPollTriggers_begin              = 0x8458,
    UrlAmTxCheckDataPollTriggers_end                = 0x8459,
    UrlAmTxDestroyUntransmittedAckNackReports_begin = 0x845A,
    UrlAmTxDestroyUntransmittedAckNackReports_end   = 0x845B,
    UrlAmTxUpdateBufferInfo_begin                   = 0x845C,
    UrlAmTxUpdateBufferInfo_end                     = 0x845D,
    UrlAmTxCheckSduDiscardTimer_begin               = 0x845E,
    UrlAmTxCheckSduDiscardTimer_end                 = 0x845F,
    UrlAmTxAssembleDataPduEdch_begin                = 0x8460,
    UrlAmTxAssembleDataPduEdch_end                  = 0x8461,
    UrlUmacEdchTrafficInd_begin                     = 0x8462,
    UrlUmacEdchTrafficInd_end                       = 0x8463,
    UrlUtilSetTxDataPduLengthIndicatorsEdch_begin   = 0x8464,
    UrlUtilSetTxDataPduLengthIndicatorsEdch_end     = 0x8465,
    UrlAmTxBackgroundDelete_begin                   = 0x8466,
    UrlAmTxBackgroundDelete_end                     = 0x8467,
    UmacHsPduListInfoIndFn_begin                    = 0x8468,
    UmacHsPduListInfoIndFn_end                      = 0x8469,
    UrlUmacDataInd_begin                            = 0x846A,
    UrlUmacDataInd_end                              = 0x846B,
    UrlAmRxReassembleSdu_begin                      = 0x846C,
    UrlAmRxReassembleSdu_end                        = 0x846D,
    UrlDeliverReassembledDlSdus_begin               = 0x846E,
    UrlDeliverReassembledDlSdus_end                 = 0x846F,
    Reserved                                        = 0x8470,

    /* With Data */
    UrlServiceLowPriorityOperations_begin           = 0xB500,
    UrlServiceLowPriorityOperations_end             = 0xB580,
    UrlProcessSignal_begin                          = 0xB600,
    UrlProcessSignal_end                            = 0xB680

} UmipsTestOpcodes;

#endif /* UMIPSTEST_H */

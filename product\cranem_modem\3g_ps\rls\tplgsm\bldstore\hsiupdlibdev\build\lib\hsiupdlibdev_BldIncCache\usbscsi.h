/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/devices/transports/usb.mod/pub/src/usbscsi.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/24 10:16:13 $
 **************************************************************************
 *  File Description :
 *    Data types etc for USB Mass Storage Class interface application.
 **************************************************************************/
#if defined(UPGRADE_USB)
#if defined(USB_MASS_STORAGE_BULK_ONLY_INTERFACE)

#ifndef USBSCSI_H
#define USBSCSI_H

/*****************************************************************************
 * Manifest Constants
 ****************************************************************************/
/* none */

/*****************************************************************************
 * Global Types
 ****************************************************************************/
typedef enum UsbScsiGenericErrorTag
{
  USBSCSI_NO_ERROR,
  USBSCSI_INVALID_COMMAND
}
UsbScsiGenericError;

typedef struct usbScsiFsDeviceTag
{
  Char           driveLetter;
  Boolean        removableMedia;
  Boolean        deviceProbablyRemoved;
  Int32          usbScsiBlockSize;
  Int32          usbScsiNumberOfBlocks;
} usbScsiFsDevice;


/*****************************************************************************
 * Global Function Prototypes
 ****************************************************************************/
void usbScsiInitialise( Int8* numberOfLogicalUnits );

Boolean usbScsiNeutralCommandInterpreter( Int8 *commandPtr, Int8 lun );

Boolean usbScsiReadCommandInterpreter(
                 Int8 *commandPtr,
                 Int8 lun,
                 Int32 dataLength,
                 Int8 *dataBuffer,
                 Int32 bufferLength,
                 Int32 *output_dataLength,
                 Int32 *output_firstBlock,
                 Int32 *output_numBlocks,
                 Boolean* ackTransmission );

Boolean usbScsiWriteCommandParser(
                 Int8 *commandPtr,
                 Int8 lun,
                 Int32 dataLength,
                 Int32 *output_dataLength,
                 Int32 *output_firstBlock,
                 Int32 *output_numBlocks,
                 Int32 *output_blockSize );

Boolean usbScsiWriteCommandPayload(
                 Int8 *commandPtr,
                 Int32 dataLength,
                 Int8 *dataBuffer );

Int32 usbScsiReadBlocks( Int32  numBlocksToRead,
                         Int32  startBlock,
                         Int8   *dataBuffer,
                         Int32  bufferLength,
                         Int32  *output_blocksize );

Boolean usbScsiWriteBlocks( Int32  startBlock,
                            Int8   blocks,
                            Int8 * dataBuffer,
                            Int32  bufferLength );

void usbScsiSetErrorState (UsbScsiGenericError newErrorState);

void usbScsiParseUnsupportedCommands( Int8 *commandPtr );

Int16 usbScsiGetFileSystemBlockSize( Int8 lun );

void usbScsiAckTx ( void );

#endif /* USBSCSI_H */

#endif /* defined(USB_MASS_STORAGE_BULK_ONLY_INTERFACE) */
#endif /* defined(UPGRADE_USB) */
/* END OF FILE */

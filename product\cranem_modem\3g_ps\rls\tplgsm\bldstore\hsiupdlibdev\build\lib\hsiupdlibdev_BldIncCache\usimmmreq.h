/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/3g.mod/lib/src/usimmmreq.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/26 20:17:00 $
 **************************************************************************
 * File Description
 * ----------------
 *
 * Contains declarations for functions in usimmmreq.c
 **************************************************************************/

#if !defined (USIMMMREQ_H)
#define       USIMMMREQ_H
#if defined (UPGRADE_3G)
/***************************************************************************
 * Nested Include Files
 **************************************************************************/

#if !defined (USIMCMD_H)
#include    "usimcmd.h"
#endif

#if !defined (SIMDEC_H)
#include    "simdec.h"
#endif

#if !defined (USIMDEC_H)
#include    "usimdec.h"
#endif

#if !defined (USIMREQ_H)
#include    "usimreq.h"
#endif

/***************************************************************************
 * Manifest Constants
 **************************************************************************/


/***************************************************************************
 * Type Definitions
 **************************************************************************/


/***************************************************************************
 *  Macros
 **************************************************************************/


/***************************************************************************
 *  Function Prototypes
 **************************************************************************/

void SimUiccTellMmSimRemoved  (SimManagerData  *simData);
void SimUiccTellMmAppStarted  (SimManagerData  *simData, AlsiAppInitialiseCnf *appInitCnf_p);  /*CQ00143681, Cgliu, 2023-05-22*/
void SimUiccGmmReadDataReq ( SimManagerData  *simData );
void SimUiccGmmWriteDataReq ( SimManagerData  *simData );
#if 0
void SimUiccGmmCsAuthReq ( SimManagerData  *simData );
void SimUiccGmmPsAuthReq ( SimManagerData  *simData );
#endif
/*Modfication by zhjzhao for ********** Merge from LTE_PS_DEV to NeZha_DEV_Daily 10-09-11, Begin*/
/*Modification for **********, cgLiu,Jun.02 2010, begin*/
#if defined (UPGRADE_LTE)
void SimUiccEmmAuthReq ( SimManagerData  *simData );
void SimUiccCopyEmmAuthSignal(SignalBuffer *emmSimAuthReq,SignalBuffer *pSignal);/*Cgliu,2021-01-25*/
void SimUiccEmmAuthReqIdle ( SimManagerData  *simData, SignalBuffer* emmSimAuthReq);     /*Cgliu,2021-01-25*/

#endif /*end UPGRADE_LTE*/
/*Modification for **********, cgLiu,Jun.02 2010, end  */
/*Modfication by zhjzhao for ********** Merge from LTE_PS_DEV to NeZha_DEV_Daily 10-09-11, End*/

#endif
#endif /* !SIMUICCMMREQ_H */

/* END OF FILE */

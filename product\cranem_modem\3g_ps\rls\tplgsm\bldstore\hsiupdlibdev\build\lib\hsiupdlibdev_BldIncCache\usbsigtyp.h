/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/devices/transports/usb.mod/api/inc/usbsigtyp.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/24 10:16:13 $
 **************************************************************************/
/** \file
 * USB Stack Signalling API
 *
 *           THIS CODE MUST BE KEPT PLATFORM INDEPENDENT
 *
 **************************************************************************/

#ifndef USBSIGTYP_H
#define USBSIGTYP_H






      

#include <usbconst.h>         /* for global USB constants */
#include <usbappif.h>         /* for dataQuantum type */
#include <usbselection.h>     /* for Device Configuration type */
#include <usberr.h>           /* for UsbErrorCode type */



/** \defgroup SigDevicesUsbAPI USB Signalling (1) - Outward-facing interface
 * \ingroup SigDevices
 * \ingroup PrdDevicesUsb
 * Signals related to managing the USB component.
 *
 * Signalling between external modules and the USB product.  These signals
 * are typically exchanged between the USB DevApp framework and the higher
 * layers that manage USB as part of the overall system management.
 *
 * This includes the ability for the 'system' to interrogate the USB product
 * as to it's configuration options, and if configurable, to configure it
 * at runtime according to the user's requirements.
 *
 * @{
 */

/** Interrogate USB for its configuration options. */
typedef struct UsbConfigQueryReqTag
{
  /** ID of task making the request (where to respond to). */
  TaskId             sender;
} UsbConfigQueryReq;

/** Provide USB configuration options. */
typedef struct UsbConfigQueryCnfTag
{
  /** Data structure containing a list of possible configurations. */
  UsbConfigQueryStruct response;
} UsbConfigQueryCnf;

/** Request a particular configuration setting */
typedef struct UsbDynamicConfigureReqTag
{
  /** Code specifying a USB configuration. */
  UsbDeviceSelection newDeviceSelection;
  /** ID of task making the request (where to respond to). */
  TaskId             sender;
} UsbDynamicConfigureReq;

/** Confirm acceptance of a configuration request */
typedef struct UsbDynamicConfigureCnfTag
{
  /** TRUE if the selection is valid */
  Boolean deviceSelectionOk;
} UsbDynamicConfigureCnf;


/** Request to enable Remote Wakeup. */
typedef struct UsbActivateRemoteWakeupReq
{
  /** ID of task making the request (where to respond to). */
  TaskId taskId;
} UsbActivateRemoteWakeupReq;

/** Acknowlegement of request to enable Remote Wakeup. */
typedef struct UsbActivateRemoteWakeupCnf
{
  /** TRUE if operation successful. */
  Boolean success;
} UsbActivateRemoteWakeupCnf;


/** @} */ /* End of group (external) */




/** \defgroup SigDevicesUsbStack USB Signalling (2) - Within USB framework
 * \ingroup SigDevices
 * \ingroup PrdDevicesUsb
 * Signals between target implementation and protocol stack.
 *
 * This group of signals are those between the
 * USB Device Application (DevApp) framework
 * and the USB stack.  The USB DevApp framework
 * are those modules that are wrapped around the USB Protocol Stack to
 * implement the functionality for USB mass storage, USB modem, etc.
 *
 * Also included in this group is the signalling between components
 * of the USB stack itself (explicitly, only those
 * sent from the USB_[STACK]_TASK to itself).  These are typically used to
 * schedule the handling of events outside of the ISR that is
 * responding to the hardware event.
 *
 * @{
 */

/*
 * Data transmission signals.
 */

/** Request to initialise the queue for transmit requests. */
typedef struct UsbInitialiseTransmitRequestQueueReqTag
{
  /** Logical Endpoint requiring a queue */
  Int8                     logicalEndpoint;
  /** Pointer to the start of memory resource allocated for this queue */
  UsbTransmitDataRequest*  startOfQueue;
  /** length of the queue required / resourced. */
  Int8                     lengthOfQueue;
  /** ID of task making the request (where to respond to). */
  TaskId                   taskID;
} UsbInitialiseTransmitRequestQueueReq;

/** Confirmation of transmit request queue initialisation. */
typedef struct UsbInitialiseTransmitRequestQueueCnfTag
{
  /** Logical Endpoint relating to this queue */
  Int8 logicalEndpoint;
} UsbInitialiseTransmitRequestQueueCnf;


/** Request to transmit data. */
typedef struct UsbTransmitDataReqTag
{
  /** Transmisson request 'package'. */
  UsbTransmitDataRequest  transmitDataRequest;
} UsbTransmitDataReq;

/** Acknowledgment of transmit request. */
typedef struct UsbTransmitDataCnfTag
{
  /** A unique ID for this request */
  Int16        requestID;
  /** Error code in the event of failure - USB_ERROR_NONE indicates success. */
  UsbErrorCode errorCode;
} UsbTransmitDataCnf;


/** Indication that the transmission of a packet has completed. */
typedef struct UsbTransmitPacketIndTag
{
  /** The unique ID provided previously. */
  Int16        requestID;
  /** The endpoint relevant to this event. */
  Int8         logicalEndpoint;
} UsbTransmitPacketInd;

/** Indication that the whole transmission request has completed. */
typedef struct UsbTransmitCompleteIndTag
{
  /** The unique ID provided previously. */
  Int16        requestID;
  /** The endpoint relevant to this event. */
  Int8         logicalEndpoint;
} UsbTransmitCompleteInd;


/** Flush a transmit-request queue. */
typedef struct UsbFlushTransmitRequestQueueReqTag
{
  /** The endpoint [queue] relevant to this flush. */
  Int8         logicalEndpoint;
  /** ID of task making the request (where to respond to). */
  TaskId       taskID;
} UsbFlushTransmitRequestQueueReq;

/** Confirmation of flushing a transmit-request queue. */
typedef struct UsbFlushTransmitRequestQueueCnfTag
{
  /** The endpoint [queue] relevant to this flush. */
  Int8         logicalEndpoint;
} UsbFlushTransmitRequestQueueCnf;

/*
 * Data reception signals.
 */

/** Request to initialise a receive buffer */
typedef struct UsbInitialiseReceiveBufferReqTag
{
  /** Logical Endpoint requiring a buffer */
  Int8                    logicalEndpoint;
  /** Pointer to the start of memory resource allocated for this buffer */
  Int8*                   startAddress;
  /** length of the buffer resourced. */
  Int16                   length;
  /** ID of task making the request (where to respond to). */
  TaskId                  taskID;
} UsbInitialiseReceiveBufferReq;

/** Confirmation of buffer initialisation */
typedef struct UsbInitialiseReceiveBufferCnfTag
{
  /** Logical Endpoint related to this buffer */
  Int8              logicalEndpoint;
  /** Error code in the event of failure - USB_ERROR_NONE indicates success. */
  UsbErrorCode      errorCode;
} UsbInitialiseReceiveBufferCnf;


/** Indication that new data has been received. */
typedef struct UsbReceiveDataIndTag
{
  /** Pointer to block of data in Rx buffer. */
  UsbDataQuantum    dataQuantum;
} UsbReceiveDataInd;

/** Indication from the device application that received data has been processed. */
typedef struct UsbReceiveDataRspTag
{
  /** Block of data buffer that is now free and may be overwritten. */
  UsbDataQuantum    dataQuantum;
} UsbReceiveDataRsp;


/** Request from Application asking for UsbReceiveDataInd signals to be suspended / resumed. */
typedef struct UsbPauseReceiveDataReqTag
{
  /** Endpoint that this applies to. */
  Int8              logicalEndpoint;
  /** Switch - TRUE to <b>suspend</b> Rx Data signals */
  Boolean           pause;
} UsbPauseReceiveDataReq;


/** Request to flush a receive buffer. */
typedef struct UsbFlushReceiveBufferReqTag
{
  /** Endpoint that this applies to. */
  Int8              logicalEndpoint;
} UsbFlushReceiveBufferReq;

/** Confirmation of flush */
typedef struct UsbFlushReceiveBufferCnfTag
{
  /** Endpoint that this applies to. */
  Int8              logicalEndpoint;
  /** Error code in the event of failure - USB_ERROR_NONE indicates success. */
  UsbErrorCode      errorCode;
} UsbFlushReceiveBufferCnf;


/** Initiate transfer from low-level Rx buffers.
 * This signal is used in two circumstances.
 * <ul>
 * <li> A hardware receive event has occurred and this is called by the ISR
 * to cause the stack to read the data from the hardware.
 * <li> The receive buffer chain has previously filled, but the application
 * has now processed some data and freed some of the receive buffer.
 * The application then uses this to re-activate the receive data flow.
 * </ul>
 */
typedef struct UsbReadDataReqTag
{
  /** Endpoint that this applies to. */
  Int8              logicalEndpoint;
} UsbReadDataReq;

/** Indication that the receive buffer chain has filled.
 * This signal is raised when the buffer chain cannot accept
 * all of the received data in the USBD hardware FIFO.  Nothing can usefully
 * happen until the Application processes some data in the chain and frees
 * buffer space.  UsbReadDataReq is used to re-actvate system (otherwise
 * the application would be swamped with these signals).
 */
typedef struct UsbRxDataPendingIndTag
{
  /** Endpoint that this applies to. */
  Int8              logicalEndpoint;
  /** Amount of pending data left. */
  Int16             length;
} UsbRxDataPendingInd;


/** Request to return the amount of free space in a receive buffer. */
typedef struct UsbReceiveBufferFreeSpaceReqTag
{
  /** Endpoint that this applies to. */
  Int8              logicalEndpoint;
} UsbReceiveBufferFreeSpaceReq;

/** Return of the amount of free space in a receive buffer. */
typedef struct UsbReceiveBufferFreeSpaceCnfTag
{
  /** Endpoint that this applies to. */
  Int8              logicalEndpoint;
  /** Number of bytes of free space. */
  Int16             freeSpace;
} UsbReceiveBufferFreeSpaceCnf;

/*
 * USB protocol stack control/status signals.
 */

/** Query the USB device controller presence. */
typedef struct UsbGetDeviceControllerIsPresentReqTag
{
  /** ID of task making the request (where to respond to). */
  TaskId       taskID;
} UsbGetDeviceControllerIsPresentReq;

/** Report the USB device controller presence. */
typedef struct UsbGetDeviceControllerIsPresentIndTag
{
  /** TRUE if USBD is present. */
  Boolean      deviceControllerIsPresent;
} UsbGetDeviceControllerIsPresentInd;


/** Request the USB device status.
 * Note - this signal is implemented but not currently used, it may not be
 * supported in future releases should it continue to be unused.*/
typedef struct UsbGetStatusDeviceReqTag
{
  /** ID of task making the request (where to respond to). */
  TaskId       taskID;
} UsbGetStatusDeviceReq;

/** Return the USB device status.
 * Note - this signal is implemented but not currently used, it may not be
 * supported in future releases should it continue to be unused.*/
typedef struct UsbGetStatusDeviceIndTag
{
  /** Status of device */
  Int16        getStatusDevice;
} UsbGetStatusDeviceInd;


/** Set the USB 'Device Application' framework [Target] Task ID. */
typedef struct UsbSetDevAppTaskIDReqTag
{
  /** Identity of task to direct USB DevApp signals to. */
  TaskId       taskID;
} UsbSetDevAppTaskIDReq;


/** Indicates that the device configuration has been updated.
 * This indicates that the host PC has successfully issued a SET_CONFIGURATION
 * command and it has been accepted and implemented by the Protocol Stack. */
typedef struct UsbSetConfigurationIndTag
{
  Int8 configurationValue;
} UsbSetConfigurationInd;

/** Indicates that a device interface has been updated.
 * This indicates that the host PC has successfully issued a SET_INTERFACE
 * command and it has been accepted and implemented by the Protocol Stack. */
typedef struct UsbSetInterfaceIndTag
{
  Int8 interfaceNumber;
  Int8 alternateNumber;
} UsbSetInterfaceInd;


/** A request from the stack to reset all USB device applications.
 * Strictly, this should be a IND signal as it comes unsolicited from the
 * stack up to a higher layer. It is the result of a USB [bus] reset, and
 * the stack now requires the device applications to reset, and inform
 * the stack that it has done so, before the stack can
 * proceed with enumeration with the host PC.
 */
typedef struct UsbResetApplicationReqTag
{
  /** ID of task making the request (where to respond to). */
  TaskId taskId;
} UsbResetApplicationReq;

/** Confirms that a device application has re-initialised.
 * See UsbResetApplicationReqTag.
 * This signal, strictly, should be a RSP signal, in response to the request
 * from the stack for the higher-layer device applications to reset themselves.
 * When all the devapps have reported that they have reset, the stack
 * may proceed with enumeration. */
typedef struct UsbResetApplicationCnfTag
{
  /** ID of the application task that has reset.  */
  TaskId taskId;
} UsbResetApplicationCnf;

/** Request to initiate a reset (by disconnecting).
 * This signal results (if hardware allows it) in the electrical
 * disconnection and reconnection of the device. */
typedef struct UsbTriggerResetReqTag
{
  /** TRUE to request to enter Reset, FALSE to request exit from reset state. */
  Boolean resetting;
}UsbTriggerResetReq;

/** Response to a Reset request */
typedef struct UsbTriggerResetCnfTag
{
  /** USB_HW_CANNOT_RESET or USB_RESET_OK accordingly. */
  UsbResetStatus resetStatus;
}UsbTriggerResetCnf;

/*
 * Signalling betwen the stack and itself.
 */

/** [Internal] Notification that an error condition has occurred. */
typedef struct UsbNotifyErrorIndTag
{
  /** Which error? */
  UsbErrorCode errorCode;
} UsbNotifyErrorInd;


/** [Internal] Indication from the DCL that a Device Request has been received. */
typedef struct UsbDeviceRequestIndTag
{
  /** Which endpoint it has been received on, typically this is EP0. */
  Int8 physicalEndpoint;
  /** the SETUP packet payload */
  Int8 setupPacket[USB_SPEC_LENGTH_OF_SETUP_PACKET];
} UsbDeviceRequestInd;


/** [Internal] Indication, from the driver, that power-saving may or may not be allowed. */
typedef struct UsbAllowPowerSavingInd
{
  /** TRUE if power-saving is allowable. */
  Boolean allowPowerSaving;
}
UsbAllowPowerSavingInd;


/** [Internal] USB device connection request. */
typedef struct UsbConnectReqTag
{
  /** ID of task making the request (where to respond to). */
  TaskId       taskID;
} UsbConnectReq;

/** [Internal] Return the result of a USB device connection request. */
typedef struct UsbConnectCnfTag
{
  /** Error code in the event of failure - USB_ERROR_NONE indicates success. */
  UsbErrorCode errorCode;
} UsbConnectCnf;


/** [Internal] Cable detection - change of state. */
typedef struct UsbDclUsbCableStateChangeIndTag
{
  /** TRUE if cable is now inserted. */
  Boolean usbCableInserted;
}UsbDclUsbCableStateChangeInd;


/** [Internal] General carrier of debug information - all fields are optional. */
typedef struct UsbDevAndDebugTag
{
  /** Identifying code */
  Int8  code;
  /** Endpoint number */
  Int8  endpoint;
  /** Arbitrary data pointer #1 */
  Int8* pointer1;
  /** Arbitrary data pointer #2 */
  Int8* pointer2;
  /** Arbitrary data value #1 */
  Int16 data1;
  /** Arbitrary data value #2 */
  Int16 data2;
  /** Arbitrary packet of data */
  Int8  packet[64];
  /** Length of packet data */
  Int8  packetLength;
} UsbDevAndDebug;

/** Used to test the Genie USB transport DLL. This signal is used by the
 * Genie USB transport DLL test Pass-Thru Task to instruct the target to
 * commence a test. */
typedef struct UsbGenieDllTestStartReqTag
{
  /** (Encoded form of) the number of bytes to be sent to the host per
   * test signal. */
  Int8 testBytesPerSignal;
} UsbGenieDllTestStartReq;

/** Used to test the Genie USB transport DLL. This signal is used by the
 * target to acknowledge a \e usbGenieDllTestStartReq signal from the host. */
typedef struct UsbGenieDllTestStartCnfTag
{
  /** Result of the request (zero indicates success). */
  Int8 result;
} UsbGenieDllTestStartCnf;

/** Used to test the Genie USB transport DLL. This signal is used by the
 * target to acknowledge a \e usbGenieDllTestStopReq signal from the host. */
typedef struct UsbGenieDllTestStopCnfTag
{
  /** Result of the request (zero indicates success). */
  Int8 result;
} UsbGenieDllTestStopCnf;

/** Used to test the Genie USB transport DLL. This signal is used to transport
 * a data payload of 32-bytes to the host or target. */
typedef struct UsbGenieDllTestData32IndTag
{
  /** A tag which is returned in the corresponding \e usbGenieDllTestDataRsp
   * from the host/target. */
  Int16 tag;
  /** Test data payload. */
  Int8 data[30];
} UsbGenieDllTestData32Ind;

/** Used to test the Genie USB transport DLL. This signal is used to transport
 * a data payload of 64-bytes to the host or target. */
typedef struct UsbGenieDllTestData64IndTag
{
  /** A tag which is returned in the corresponding \e usbGenieDllTestDataRsp
   * from the host/target. */
  Int16 tag;
  /** Test data payload. */
  Int8 data[62];
} UsbGenieDllTestData64Ind;

/** Used to test the Genie USB transport DLL. This signal is used to transport
 * a data payload of 128-bytes to the host or target. */
typedef struct UsbGenieDllTestData128IndTag
{
  /** A tag which is returned in the corresponding \e usbGenieDllTestDataRsp
   * from the host/target. */
  Int16 tag;
  /** Test data payload. */
  Int8 data[126];
} UsbGenieDllTestData128Ind;

/** Used to test the Genie USB transport DLL. This signal is used to transport
 * a data payload of 256-bytes to the host or target. */
typedef struct UsbGenieDllTestData256IndTag
{
  /** A tag which is returned in the corresponding \e usbGenieDllTestDataRsp
   * from the host/target. */
  Int16 tag;
  /** Test data payload. */
  Int8 data[254];
} UsbGenieDllTestData256Ind;

/** Used to test the Genie USB transport DLL. This signal is used to transport
 * a data payload of 512-bytes to the host or target. */
typedef struct UsbGenieDllTestData512IndTag
{
  /** A tag which is returned in the corresponding \e usbGenieDllTestDataRsp
   * from the host/target. */
  Int16 tag;
  /** Test data payload. */
  Int8 data[510];
} UsbGenieDllTestData512Ind;

/** Used to test the Genie USB transport DLL. This signal is used to transport
 * a data payload of 1024-bytes to the host or target. */
typedef struct UsbGenieDllTestData1024IndTag
{
  /** A tag which is returned in the corresponding \e usbGenieDllTestDataRsp
   * from the host/target. */
  Int16 tag;
  /** Test data payload. */
  Int8 data[1022];
} UsbGenieDllTestData1024Ind;

/** Used to test the Genie USB transport DLL. This signal is used to transport
 * a data payload of 2048-bytes to the host or target. */
typedef struct UsbGenieDllTestData2048IndTag
{
  /** A tag which is returned in the corresponding \e usbGenieDllTestDataRsp
   * from the host/target. */
  Int16 tag;
  /** Test data payload. */
  Int8 data[2046];
} UsbGenieDllTestData2048Ind;

/** Used to test the Genie USB transport DLL. This signal is used by the
 * target or host to acknowledge a \e usbGenieDllTestDataXXXInd signal received
 * from the host or target. */
typedef struct UsbGenieDllTestDataRspTag
{
  /** A tag which is contained in the corresponding \e usbGenieDllTestDataXXXInd
   * from the target or host. */
  Int8 tag;
  /** (Encoded form of) the number of bytes to be sent in the
   * corresponding \e usbGenieDllTestDataXXXInd from the target or host. */
  Int8 testBytesPerSignal;
} UsbGenieDllTestDataRsp;

# if defined (DEVELOPMENT_VERSION)
/** [Internal] Carrier of debug information. */
typedef struct UsbRegStatusTag
{
  Int8 code;
  Int8 ADDR_CCONF;
  Int8 ADDR_FAR;
  Int8 ADDR_EPC0;
  Int8 ADDR_NAKMSK;
  Int8 ADDR_TXMSK;
  Int8 ADDR_RXMSK;
  Int8 ADDR_ALTMSK;
  Int8 ADDR_MAMSK;
  Int8 ADDR_MCNTRL;
} UsbRegStatus;
# endif

/** @} */ /* End of group (devapp + internal) */

#endif /* !defined (USBSIGTYP_H) */

/* END OF FILE */


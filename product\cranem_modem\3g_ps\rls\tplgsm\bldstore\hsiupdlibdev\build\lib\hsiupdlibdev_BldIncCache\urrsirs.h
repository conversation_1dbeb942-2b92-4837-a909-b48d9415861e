/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urrsirs.h#4 $
 *   $Revision: #4 $
 *   $DateTime: 2007/03/19 13:59:30 $
 **************************************************************************
 * File Description:
 *
 *    Header file for URRSIRS.C.
 **************************************************************************/

#if !defined (URRSIRS_H)
#define       URRSIRS_H

/***************************************************************************
* Nested Include Files
***************************************************************************/
#include <urrintsig.h>

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/

/***************************************************************************
*   Macro Functions
***************************************************************************/

/***************************************************************************
 * Types
 ***************************************************************************/

/***************************************************************************
 * Typed Constants
 ***************************************************************************/

/***************************************************************************
 * Function Prototypes
 ***************************************************************************/

Boolean UrrSirsStopTimerIfRunning (UrrSirTimerIdentity timerId);

void UrrSirsStopAllSirTimers (void);

Boolean UrrSirsProcessMasterInformationBlock (
                            UrrSirCellType cellType,
                            UMasterInformationBlock *uMasterInformationBlock_p);

void UrrSirsProcessSchedulingBlock1     (UrrSirCellType  cellType,
                                         USysInfoTypeSB1 *uSysInfoTypeSB1_p);

void UrrSirsProcessSchedulingBlock2     (UrrSirCellType  cellType,
                                         USysInfoTypeSB2 *uSysInfoTypeSB2_p);

Boolean UrrSirsSystemInformationDueNow (UrrSirCellType    cellType,
                                                     Int16             sfn,
                                                     UrrSirSibTypeExtExt2    *sib_TypeExt_p);

Boolean UrrSirsNextSystemInformationDue (UrrSirCellType  cellType,
                                         Int16           sfn,
                                         Int16           *nextSfnDue_p,
                                         Boolean         *nonMibDue_p);

void UrrSirsResetSysInfoDatabase        (UrrSirCellType cellType);

UrrSirDatabaseEntry *UrrSirsFindSibStoreInDatabase (
                                                  UrrSirDatabase *databasePtr_p,
                                                  USIB_Type     sib_Type,
                                                  UrrSirSibTypeExtExt2 sib_TypeExt,
                                                  Boolean       checkInstance,
                                                  Int8          instanceId,
                                                  Boolean       *found_p);

void UrrSirsUpdateRxSegmentInfoInDatabase (
                                    UrrSirCellType              cellType,
                                    USIB_Type                   sib_Type,
                                    UrrSirSibTypeExtExt2              sib_TypeExt,
                                    Int8                        instanceId,
                                    Int8                        segmentNumber,
                                    /*Boolean                     lastSegment,*/
                                    UrrSirBlockSegmentStoreInfo *segStorePtr_p);

void UrrSirsResetAllRxSegmentInfoInDatabase (UrrSirCellType     cellType);    /* CQ00112411 added */
void UrrSirsResetRxSegmentInfoInDatabase (UrrSirCellType     cellType,
                                          USIB_Type         sib_Type,
                                          UrrSirSibTypeExtExt2    sib_TypeExt,
                                          Int8              instanceId,
                                          Boolean           allInstances);

Int8 UrrSirsGetInstanceIdFromDatabase     (UrrSirCellType cellType,
                                           Int16          sfn,
                                           USIB_Type      sib_Type,
                                           Int8           segmentNumber);

UrrSirDatabase *UrrSirsGetPointerToDatabase (UrrSirCellType cellType);

Boolean UrrSirsGetSibReadDueState (UrrSirCellType  cellType,
                                   USIB_Type       sib_Type,
                                   Int8            instanceId);

Boolean UrrSirsGetExtSibReadDueState (UrrSirCellType   cellType,
                                   UrrSirSibTypeExtExt2 sib_TypeExt);

void UrrSirsSetSibReadDueFlag     (UrrSirCellType  cellType,
                                   USIB_Type       sib_Type,
                                   Int8            instanceId,
                                   Boolean         state);

void UrrSirsSetExtSibReadDueFlag (UrrSirCellType   cellType,
                               UrrSirSibTypeExtExt2  sib_TypeExt,
                               Boolean         state);


void UrrSirsCheckForBchSkip       (UrrSirCellType cellType,
                                   Int16          sfn,
                                   Int16          nextSfnDue);

void UrrSirsHandleTimers (RadioResourceControlEntity *urr_p);

void UrrSirsStartSysInfoTimers (UrrSirCellType  cellType,
                                USIB_Type       sib_Type,
                                Boolean         mibTimeout,
                                Boolean         sib7Timeout,
                                Boolean         bcchModTimeout);

Int32 GetSibBit (USIB_Type sib_Type);

Int32 GetExtSibBit (UrrSirSibTypeExtExt2 sib_TypeExt);

void UrrSirsStartSysInfoTimersOnCell (UrrSirCellType cellType);

void UrrSirsStartSysInfoTimerSib7 (void);

void UrrSirsSetExpirationTime     (UrrSirCellType cellType,
                                   USIB_Type      sib_Type,
                                   Int8           expirationTimeFactor);

void UrrSirsIndicateSibToAcquireForSubProcess (UrrSubProcessId subProcessId,
                                               UrrSirCellType  cellType,
                                               Int8            numberOfSibs,
                                               USIB_Type       *sib_Type_p,
                                               Int8            numberOfExtSibs,
                                               UrrSirSibTypeExtExt2  *sib_TypeExt_p);

void UrrSirsActionSibToAcquireForSubProcess (
                                        UrrSirCellType  cellType,
                        UrrInternalSignalIndicateScellSibsToAcquire *sig_p);

void UrrSirsActionExtSibToAcquireForSubProcess (UrrSirCellType cellType,
                           UrrInternalSignalIndicateScellSibsToAcquire *sig_p);

void UrrSirsIndicateSibRequiredBySubProcess (UrrSubProcessId subProcessId,
                                             UrrSirCellType  cellType,
                                             Int8            numberOfSibs,
                                             USIB_Type       *sib_Type_p);

void UrrSirsIndicateExtSibRequiredBySubProcess (UrrSubProcessId subProcessId,
                                             UrrSirCellType  cellType,
                                             Int8            numberOfExtSibs,
                                             UrrSirSibTypeExtExt2  *sib_TypeExt_p);

void UrrSirsIndicateSibNotRequiredBySubProcess (UrrSubProcessId subProcessId,
                                                UrrSirCellType  cellType,
                                                USIB_Type       sib_Type);

Boolean UrrSirsSibToBeDistributedToSubProcess (UrrSubProcessId subProcessId,
                                               UrrSirCellType  cellType,
                                               USIB_Type       sib_Type);

Boolean UrrSirsExtSibToBeDistributedToSubProcess (UrrSubProcessId subProcessId,
                                               UrrSirCellType  cellType,
                                               UrrSirSibTypeExtExt2  sib_TypeExt);

void UrrSirsTurnServingCellBchOnOff (Boolean on, Boolean stopSkip);
/* **********, BEGIN */
void DestroySirBlockSegmentStore (UrrSirCellType cellType); 
/* **********, END */

void UrrSirsTurnNeighbourCellBchOff (void);

void UrrSirsSendInternalSignalTurnOffScellBchIfOn (void);

void UrrSirsSendCphyBchReq (Boolean bchOn);

void UrrSirsHandleCphyBchReq (Boolean bchOn);


void UrrSirsSendCphyNcellBchReq (CphyNcellBchReq *cphyNcellBchReq_p);

void UrrSirsNotifyAllSibsReceived (UrrSirCellType cellType);

#if !defined (UPGRADE_EXCLUDE_2G)
void UrrSirsClearUmtsSchedulingInfoForGsmStack (UMTS_SysInfo *sysInfo_p);
void UrrSirsSetUmtsSchedulingInfoForGsmStack (UMTS_SysInfo  *sysInfo_p,
                                              USIB_Type     sib_Type,
                                              Int8          instanceId);
void UrrSirsSetUmtsSchedulingInfoForPredefinedConfigs (UMTS_SysInfo *sysInfo_p);
#endif /* UPGRADE_EXCLUDE_2G */

void UrrSirsInitialiseReadBitmap (Boolean onlyNcellToBeReset);
#endif
Boolean UrrSirsSib7TimerExpired (void);
void UrrSirsNotifySubProcessesThatScellSibsUpToDate (void);
void UrrSirsNotifySubProcessThatBcchModReadingSibsStarted (
                                         UrrSubProcessId destinationSubProcess);
void UrrSirsNotifySubProcessThatBcchModReadingSibsFinished (
                                         UrrSubProcessId destinationSubProcess);
/* CQ00016400, begin */
Boolean IsSingleSibValid (USIB_Type sibType);
UrrSirBlockSegmentStoreInfo *CreateSiBlockSegmentStore (
                                                    UrrSirCellType    cellType,
                                                    USIB_Type        sib_Type,
                                                    UrrSirSibTypeExtExt2   sib_TypeExt,
                                                    Int8             instanceId,
                                                    USegCount        segCount);
void FreeSirBlockSegmentStore (UrrSirCellType    cellType,
                                      USIB_Type         sib_Type,
                                      UrrSirSibTypeExtExt2    sib_TypeExt,
                                      Int8              instanceId);
UrrSirBlockSegmentStoreInfo *GetPtrToSiBlockSegmentStore (
                                                    UrrSirCellType  cellType,
                                                    USIB_Type      sib_Type,
                                                    UrrSirSibTypeExtExt2 sib_TypeExt,
                                                    Int8           instanceId);
void AddSegmentToSiBlockSegmentStore (
                                       /*UrrSirBlockSegmentStoreInfo *storePtr_p,
                                       USegmentIndex              segmentIndex,*/
                                       UrrSirSegmentInfo         *storePtr_p,
                                       Int16                      segLength,
                                       Int8                       *segData_p);
void FreeSirBlockSegmentInfo (UrrSirBlockSegmentStoreInfo *storePtr_p);
void CreateSiBlockSegmentInfo (UrrSirBlockSegmentStoreInfo *sibPtr_p); 
void ProcessSegmentedSib (UrrSirCellType cellType,
                                   UrrSirSibTypeExtExt2 sib_TypeExt);
/* CQ00016400, end */

void UrrSirsCopySystemInformationDatabase (UrrSirDatabase *src_p,
                                           UrrSirDatabase **dest_p);
void UrrSirsDestroySystemInformationDatabaseIfPresent (
                                                   UrrSirDatabase **database_p);

void UrrSirsNotifyRbcOfSib7TimeoutTimerExpiry (void);

Boolean UrrSirsCheckIfSibScheduledForScell(USIB_Type sibType);

Boolean UrrSirsChangeSib5bisToSib5IfNecessary (USIB_Type *sib_Type_p);

Boolean UrrSirsCheckIfOnlySib7Required(void);

/* CQ00063419 begin */
Int16 NextSegmentDueForSib (Boolean   mib,
                            Int16     sibPos,
                            Int16     sibRep,
                            Int8      segNumber,
                            Int8      *sibOffs,
                            Int16     sfn,
                            Boolean   *dueAtThisSfn_p);
/* CQ00063419 end */                             


 /* END OF FILE */


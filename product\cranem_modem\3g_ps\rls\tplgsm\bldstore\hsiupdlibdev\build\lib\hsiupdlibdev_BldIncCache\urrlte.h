/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/
#if !defined (URRLTE_H)
#define       URRLTE_H

/***************************************************************************
* Nested Include Files
***************************************************************************/
#include "kernel.h"
/* CQ00025639 : Upgrade to LTE+WCDMA feature : begin */
//#if !defined (EXCLUDE_GSM_PS_AS) || !defined (EXCLUDE_GSM_L1)
#include "lteUtraGsmIratItf.h"
//#endif
/* CQ00025639 : Upgrade to LTE+WCDMA feature : begin */
#include "urrtypes.h"

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/

/***************************************************************************
 *  Macro Functions
 ***************************************************************************/

/***************************************************************************
 * Types
 ***************************************************************************/


/***************************************************************************
 * Typed Constants
 ***************************************************************************/
typedef struct IratInfoTag
{
    UInterRATHandoverInfo_ue_CapContainer_present_str irrUeCapabilityContainer;
    Boolean                                                                         irrUeV390infoPresent;
    UInterRATHandoverInfo_present                                                   irrUeV390info;
    UInterRATHandoverInfo_v3a0NonCriticalExt                                        irrV3a0NonCriticalExt;
    UInterRATHandoverInfo_laterNCritExt                                             irrLaterNCritExt;
    UInterRATHandoverInfo_r3_add_ext_IEs                                            irrR3_add_ext;
    UInterRATHandoverInfo_v3g0NonCriticalExt                                        irrV3g0NonCriticalExt;
    UInterRATHandoverInfo_v4b0NonCriticalExt                                        irrV4b0NonCriticalExt;
    UInterRATHandoverInfo_v4d0NonCriticalExt                                        irrV4d0NonCriticalExt;
    UInterRATHandoverInfo_v590NonCriticalExt                                        irrV590NonCriticalExt;
    UInterRATHandoverInfo_v690NcExt                                                 irrV690NcExt;
    UInterRATHandoverInfo_NonCriticalExtension_v6b0_IEs								irrV6b0NonCriticalExt;
    UInterRATHandoverInfo_NonCriticalExtension_v6b0_IEs_v6e0NonCriticalExtensions	irrV6e0NonCriticalExt;
    UInterRATHandoverInfo_NonCriticalExtension_v6b0_IEs_v770NonCriticalExtensions	irrV770NonCriticalExt;
    UUE_RadioAccessCapabilityComp_v770ext											irrUeRadioAccessCapabilityComp;
    UUE_RadioAccessCapabilityComp2_fddPhChCapab_hspdsch_edch						irrHspdschEdch;
    //UInterRATHandoverInfo_NonCriticalExtension_v6b0_IEs_v770NonCriticalExtensions   irrV770NonCriticalExtensions; //CQ00111602 removed
    UInterRATHandoverInfo_NonCriticalExtension_v6b0_IEs_v790nonCriticalExtensions   irrV790nonCriticalExtensions;
    UInterRATHandoverInfo_NonCriticalExtension_v6b0_IEs_v860NonCriticalExtensions   irrV860NonCriticalExtensions;
    UUE_RadioAccessCapability_v860ext                                               irrUe_RadioAccessCapabilityInfo;
    /*CQ00053787 add begin*/
    UUE_RadioAccessCapabilityInfo_v770ext                                           ue_RadioAccessCapabilityInfo;
    UUE_RadioAccessCapabilityComp2_v770ext                                          ue_RadioAccessCapabilityComp2;
    /*CQ00053787 add begin*/
	/* CQ00065791 Begin */
    UInterRATHandoverInfo_r3_add_ext_IEs_v7e0NonCriticalExtensions                  irrR3_add_ext_IEs_v7e0NonCriticalExt;
    UUE_RadioAccessCapabilityComp2_v860ext                                          ue_RadioAccessCapabilityComp2_v860ext;
	/* CQ00065791 End */
}
IratInfo;

/***************************************************************************
 * Global Data declarations
 ***************************************************************************/

/***************************************************************************
 * Global Function Prototypes
 ***************************************************************************/
Boolean UrrLteCheckIsSupportLteModeSingleLink(void);

void UrrLteIRatReselectRequest(RadioResourceControlEntity *urr_p);
void UrrLteIRatReselectAck(RadioResourceControlEntity *urr_p);
void UrrLteSendIRatReselectCnf (IratReselectionCnf * const iratReselectionCnf_p);
void UrrLteSendIratUrrErrcUmtsUeCapInfoInd (void);
void UrrLteFillcompressedModeMeasCapabEUTRAList (UCompressedModeMeasCapabEUTRAList
*compressedModeMeasCapabEUTRAList);
void UrrLteFillRadioAccessCapabBandFDDList3 (UUE_RadioAccessCapabBandFDDList3  *ue_RadioAccessCapabBandFDDList3);
void UrrLteEncodeUECapabilities(IratInfo   *iratInfo);   /* ********** added */

#endif /* !defined (URRLTE_H) */

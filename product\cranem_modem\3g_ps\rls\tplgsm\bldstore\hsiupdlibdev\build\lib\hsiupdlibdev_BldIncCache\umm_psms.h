/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/3g.mod/lib/src/umm_psms.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/26 20:17:00 $
 **************************************************************************
 * File Description :
 *
 * Contains declarations for functions in umm_psms.c
 **************************************************************************/
#if defined (SUPPORT_SMS)

#if !defined (UMM_PSMS_H)
#define       UMM_PSMS_H

/***************************************************************************
 * Nested Include Files
 **************************************************************************/


#include <system.h>

#if defined (UPGRADE_3G)
extern void UmmPsmsEstReq (MobilityManagerEntity *mm);
extern void UmmPsmsUnitDataReq (MobilityManagerEntity *mm);
extern void UmmHandlePsmsUnitDataInd (MobilityManagerEntity *mm);
extern void UmmPsmsRelReq (MobilityManagerEntity *mm);
extern void UmmSendPsmsPrimitive (MobilityManagerEntity *mm,
                                  const MmSignalDescr   *sigDescr,
                                  Int8                  connIndex);
extern void UmmReleasePsmsConnections (MobilityManagerEntity *mm);
extern void UmmSendPsmsErrorInd (MobilityManagerEntity *mm);

/* **********, 16/11/14, lkrasnop, propagated from CL1, for USAT/OTA REFRESH, begin */
extern Boolean MmSearchUmmConnections (MobilityManagerEntity *mm,
                                      MmConnectionState state,
                                      Combination combination);
/* **********, 16/11/14, lkrasnop, propagated from CL1, for USAT/OTA REFRESH, end */
extern Int8 MmSearchMtSmsConnections (MobilityManagerEntity *mm);/*Add for ********** by chenbo 2014-12-31*/ /*UNIFICATION for DS_DEV and OT_DEV*/
extern void EmmSendPsmsCsServiceNotificationInd(MobilityManagerEntity *mm); /*Add for ********** by chenbo 2014-12-31*/ /*UNIFICATION for DS_DEV and OT_DEV*/
/*Modify by qinglanwang, CQ00118173, 20200116, begin*/
extern void UmmSendPsmsErrorIndWithValues (MobilityManagerEntity *mm,
                                               Int8                   tv,
                                               Boolean                myTv);
/*Modify by qinglanwang, CQ00118173, 20200116, end*/
#endif

#endif
#endif
/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

#if defined(UPGRADE_USB)
#if defined(USB_TTPCOM_TEST_INTERFACE)

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/devices/transports/usb.mod/pub/src/usbtestdesc.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/24 10:16:13 $
 **************************************************************************
 *  File Description :
 *    Device descriptors for the TTPCom Ltd USB test interface.
 **************************************************************************/

#ifndef USBTESTDESC_H
#define USBTESTDESC_H

/* This TTPCom USB Test Interface is only ever built on its own. */

/*******************************************************************************
 * TTPCom test interface descriptors.
 ******************************************************************************/

/* A set of Test Interface descriptors exist for each supported USBD controller.
   Declaration of multiple USBD controllers is checked in usbendpoints.h, and
   hence such a check is not required in this file. */

#if defined(USB_NETCHIP_NET2890)
  /*
   * Test 1, control / control loopback.
   */
  USB_INTERFACE_DESCRIPTOR(USB_TEST_1_INTERFACE_NUMBER,         /* bInterfaceNumber */
                           USB_TEST_1_ALTERNATE_NUMBER,         /* bAlternateSetting */
                           0,                                   /* bNumEndpoints */
                           USB_INTERFACE_CLASS_VENDOR,          /* bInterfaceClass */
                           USB_TTPCOM_INTERFACE_SUBCLASS_TEST,  /* bInterfaceSubclass */
                           USB_TTPCOM_INTERFACE_PROTOCOL_CTRL,  /* bInterfaceProtocol */
                           USB_STRING_NOT_DEFINED_INDEX),       /* iInterface */

  /*
   * Test 2, interrupt / interrupt loopback.
   */
  USB_INTERFACE_DESCRIPTOR(USB_TEST_2_INTERFACE_NUMBER,         /* bInterfaceNumber */
                           USB_TEST_2_ALTERNATE_NUMBER,         /* bAlternateSetting */
                           2,                                   /* bNumEndpoints */
                           USB_INTERFACE_CLASS_VENDOR,          /* bInterfaceClass */
                           USB_TTPCOM_INTERFACE_SUBCLASS_TEST,  /* bInterfaceSubclass */
                           USB_TTPCOM_INTERFACE_PROTOCOL_INT,   /* bInterfaceProtocol */
                           USB_STRING_NOT_DEFINED_INDEX),       /* iInterface */

  USB_ENDPOINT_DESCRIPTOR((USB_TEST_2_IN_ENDPOINT
                           |USB_ENDPOINT_ATTR_DIRECTION_IN),    /* bEndpointAddress */
                          USB_ENDPOINT_TYPE_INTERRUPT,          /* bmAttributes */
                          USB_TEST_2_IN_ENDPOINT_MAX_LENGTH,    /* wMaxPacketSize */
                          1),                                   /* bInterval (ms) */

  USB_ENDPOINT_DESCRIPTOR((USB_TEST_2_OUT_ENDPOINT
                           |USB_ENDPOINT_ATTR_DIRECTION_OUT),   /* bEndpointAddress */
                          USB_ENDPOINT_TYPE_INTERRUPT,          /* bmAttributes */
                          USB_TEST_2_OUT_ENDPOINT_MAX_LENGTH,   /* wMaxPacketSize */
                          1),                                   /* bInterval (ms) */

  /*
   * Test 3, bulk / bulk loopback.
   */
  USB_INTERFACE_DESCRIPTOR(USB_TEST_3_INTERFACE_NUMBER,         /* bInterfaceNumber */
                           USB_TEST_3_ALTERNATE_NUMBER,         /* bAlternateSetting */
                           2,                                   /* bNumEndpoints */
                           USB_INTERFACE_CLASS_VENDOR,          /* bInterfaceClass */
                           USB_TTPCOM_INTERFACE_SUBCLASS_TEST,  /* bInterfaceSubclass */
                           USB_TTPCOM_INTERFACE_PROTOCOL_BULK,  /* bInterfaceProtocol */
                           USB_STRING_NOT_DEFINED_INDEX),       /* iInterface */

  USB_ENDPOINT_DESCRIPTOR((USB_TEST_3_IN_ENDPOINT
                           |USB_ENDPOINT_ATTR_DIRECTION_IN),    /* bEndpointAddress */
                          USB_ENDPOINT_TYPE_BULK,               /* bmAttributes */
                          USB_TEST_3_IN_ENDPOINT_MAX_LENGTH,    /* wMaxPacketSize */
                          1),                                   /* bInterval (ms) */

  USB_ENDPOINT_DESCRIPTOR((USB_TEST_3_OUT_ENDPOINT
                           |USB_ENDPOINT_ATTR_DIRECTION_OUT),   /* bEndpointAddress */
                          USB_ENDPOINT_TYPE_BULK,               /* bmAttributes */
                          USB_TEST_3_OUT_ENDPOINT_MAX_LENGTH,   /* wMaxPacketSize */
                          1)                                    /* bInterval (ms) */

  /*
   * Test 4, isochronous / isochronous loopback.
   */

  /* This test not yet supported. */

  /*
   * Test 5, interrupt streaming throughput.
   */

  /* This test not yet supported. */

  /*
   * Test 6, bulk streaming throughput.
   */

  /* This test not yet supported. */

#endif /* defined(USB_NETCHIP_NET2890) */














































































































































                                                               

#endif /* USBTESTDESC_H */

#endif /* defined(USB_TTPCOM_TEST_INTERFACE) */
#endif /* defined(UPGRADE_USB) */
/* END OF FILE */

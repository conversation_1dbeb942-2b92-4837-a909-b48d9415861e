/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/tplgsm/utinc/utebmm.h#6 $
 *   $Revision: #6 $
 *   $DateTime: 2006/02/09 12:25:17 $
 **************************************************************************
 * File Description
 * ----------------
 *
 * Header file for Extended Block Memory Manager utility.
 * This is used by data services (Voyager) and GPRS stack, and can
 * be used for other client specific requirements.
 **************************************************************************/
#ifndef UTEBMM_H
#define UTEBMM_H

/***************************************************************************
 * Nested Include Files
 **************************************************************************/

/***************************************************************************
 * Manifest Constants
 **************************************************************************/

/***************************************************************************
   The EBMM_MEMORY_POOL_SIZE and the EBMM_MEMORY_BLOCK_SIZE are user
   configurable.  However, the suggested minumums are listed below.

   The EBMM_MEMORY_POOL_SIZE must be a multiple of the EBMM_MEMORY_BLOCK_SIZE
 **************************************************************************/
#if defined (ON_PC)
# if defined (UPGRADE_GPRS)
#define EBMM_MEMORY_POOL_SIZE_BASE      56320
# else
#define EBMM_MEMORY_POOL_SIZE_BASE      128
# endif
#else
#  if defined (UPGRADE_GPRS)
#   if ( (!defined(VG_DISABLE_14400_DATA)) || (!defined(VG_DISABLE_V42_BIS)) )
#define EBMM_MEMORY_POOL_SIZE_BASE      56320  /* 110 blocks */
#   else
#    if ( (!defined(DISABLE_SNDCP_HEADER_COMP)) || (!defined(DISABLE_SNDCP_DATA_COMP)) )
#define EBMM_MEMORY_POOL_SIZE_BASE      15360  /* 30 blocks */
#    else
#define EBMM_MEMORY_POOL_SIZE_BASE      12288  /* 24 blocks */
#    endif
#   endif
#  else
#   if defined (UPGRADE_DATA_ON_BOARD)
#    if ( (!defined(VG_DISABLE_14400_DATA)) || (!defined(VG_DISABLE_V42_BIS)) )
#define EBMM_MEMORY_POOL_SIZE_BASE      56320  /* 110 blocks */
#    else
#define EBMM_MEMORY_POOL_SIZE_BASE      12288  /* 24 blocks */
#    endif
#   else
#define EBMM_MEMORY_POOL_SIZE_BASE      1
#   endif
#  endif
#endif

/* Header compression on WAP CSD */


     
#define EBMM_MEMORY_SIZE_WAP_PPP        0
      

/* Modified by Daniel for NOKIA IOT pre-testing(TVRTD CQ00004849) begin */
/* Header compression on GPRS PPP */
#if (1) //defined (ENABLE_PPP_NW_HEADER_COMP)
/* Modified by Daniel for NOKIA IOT pre-testing(TVRTD CQ00004849) end */
#define EBMM_MEMORY_SIZE_GP_PPP         4608  /* 9 blocks */
#else
#define EBMM_MEMORY_SIZE_GP_PPP         0
#endif

/* Header compression test on GPRS PPP */
#if defined (GPRS_PPP_TEST_VJ) || defined (GPRS_PPP_TEST_VJ_RT)
#define EBMM_MEMORY_SIZE_TEST_VJ        9216  /* 18 blocks */
#else
#define EBMM_MEMORY_SIZE_TEST_VJ        0
#endif

#define EBMM_MEMORY_POOL_SIZE   (EBMM_MEMORY_POOL_SIZE_BASE + \
                                 EBMM_MEMORY_SIZE_WAP_PPP + \
                                 EBMM_MEMORY_SIZE_GP_PPP + \
                                 EBMM_MEMORY_SIZE_TEST_VJ)

#if defined (ON_PC)
# if defined (UPGRADE_GPRS)
#define EBMM_MEMORY_BLOCK_SIZE       512
# else
#define EBMM_MEMORY_BLOCK_SIZE       16
# endif
#else
/* -----------------4/19/00 10:26AM------------------
The EBMM_MEMORY_BLOCK_SIZE MUST BE A MULTIPLE of 4 to
ensure structure alignment.
 --------------------------------------------------*/
#  if defined (UPGRADE_DATA_ON_BOARD)
#define EBMM_MEMORY_BLOCK_SIZE       512    /* 512 bytes */
#  else
#define EBMM_MEMORY_BLOCK_SIZE       1
#  endif
#endif

#define EBMM_NUMBER_OF_BLOCKS       (EBMM_MEMORY_POOL_SIZE/EBMM_MEMORY_BLOCK_SIZE)

 /***************************************************************************
  The EBMM_LOGGING_TASK_ID is user configurable.  The default task is
  the application (background) layer.
 **************************************************************************/
#define EBMM_LOGGING_TASK_ID        AL_TASK_ID

/***************************************************************************
 * Type Definitions
 **************************************************************************/
typedef Int16  EbmmBlockIndex;

typedef struct EbmmFreeBlockGroupHeaderTag
{
  EbmmBlockIndex numberOfFreeBlocks;
  EbmmBlockIndex nextFreeBlockGroupIndex;
} EbmmFreeBlockGroupHeader;

typedef struct EbmmAllocBlockGroupHeaderTag
{
  EbmmBlockIndex numberOfAllocBlocks;
  TaskId         ownerTask;
} EbmmAllocBlockGroupHeader;

typedef struct EbmmMemoryPoolTag
{
  EbmmFreeBlockGroupHeader startFreeBlockHeader;
  Int8                     *poolAlignmentDummy;  /* Forces memory pool alignment
                                                    to 4 byte boundary */
  Int8                     pool [EBMM_MEMORY_POOL_SIZE];
} EbmmMemoryPool;

typedef enum EbmmResultTag
{
  EBMM_OK,
  EBMM_ERROR,
  EBMM_OUT_OF_MEMORY,
  EBMM_COLLISION
} EbmmResult;

/***************************************************************************
 *  Macros
 **************************************************************************/

/***************************************************************************
 *  Exported Variables
 **************************************************************************/

/***************************************************************************
 *  Function Prototypes
 **************************************************************************/

EbmmResult   utEbmmAllocMemory       (TaskId     task,
                                      void     **pool,
                                      Int32      size);

EbmmResult   utEbmmFreeMemory        (TaskId     task,
                                      void     **pool);

EbmmResult   utEbmmInitialise        (TaskId                  task);

EbmmResult   utEbmmFreeAllTaskMemory (TaskId                  task);

#endif

/* END OF FILE */

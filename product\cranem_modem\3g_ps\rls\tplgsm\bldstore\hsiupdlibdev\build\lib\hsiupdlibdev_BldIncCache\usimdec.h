/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/3g.mod/lib/src/usimdec.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2007/01/12 13:10:41 $
 **************************************************************************
 *  File Description :
 *
 *  Header file for encode and decode procedures for uicc.
 **************************************************************************/

#if !defined (USIMDEC_H)
#define USIMDEC_H
#if defined (UPGRADE_3G)
/***************************************************************************
 * Nested Include Files
 **************************************************************************/

#if !defined (SIMDATA_H)
#include "simdata.h"
#endif

#if !defined (SIMDEC_H)
#include "simdec.h"
#endif

#if !defined (ALSU_SIG_H)
#include "alsu_sig.h"
#endif

/***************************************************************************
 * Macro
 **************************************************************************/

#define GET_HIGH_BYTE(word)             ((Int8)((word) >> 8))
#define GET_LOW_BYTE(word)              ((Int8)((word) & 0x00ff))
#define INVERT_NIBBLE(bYTE)             ((Int8) (((bYTE >> 4) & 0x0f) | ((bYTE << 4) & 0xf0)));

#define GET_EFDF_ID(pBuf,offset)		( (((Int16)pBuf[offset]) << 8) | pBuf[offset + 1] )
#define TLV_OBJECT_OFFSET_ADVANCE( oFFSETpARAM, oBJdATAlENGTH ) \
                        oFFSETpARAM = oFFSETpARAM + 2 + oBJdATAlENGTH;
/***************************************************************************
 * Manifest Constants
 **************************************************************************/

/* Used as an offset into the LAI starting with octet 1 */
#define USIM_MNC_OFFSET  1

#define LEVEL_2_KEY_REF   0x80
#define LEVEL_1_KEY_REF   0x00
/* Data to encode/decode Cipher and Integrity Keys*/
#define CK_OFFSET                       1
#define IK_OFFSET                       17
#define EF_KEYS_FILE_SIZE               33
#define EF_KC_FILE_SIZE                 9
#define EF_KC_GPRS_FILE_SIZE            EF_KC_FILE_SIZE
#define EF_START_HFN_FILE_SIZE          6
#define EF_THRESHOLD_FILE_SIZE          3

#define OCI_MIN_REC_LENGTH        27
#define ICI_MIN_REC_LENGTH        28
#define ICI_CALL_INFO_OFFSET      14
#define OCI_CALL_INFO_OFFSET      13

#define ADN_MIN_REC_LENGTH        14
#define BDN_MIN_REC_LENGTH        15

#define NUM_QUARTERS_IN_AN_HOUR     4
#define NUM_MINUTES_IN_A_QUARTER   15
#define CALL_INFO_TIMESTAMP_SIZE    7
#define CALL_DURATION_SIZE          3

/*size of the extension records */
#define SIM_SIZE_EXT_RECS        13
/*Modification for CQ00013991 PB optimization by zhjzhao  07-22-11, Begin*/
#define UICC_ANR_EXT_OFFSET 14
/*Modification for CQ00013991 PB optimization by zhjzhao  07-22-11, End*/

/*Tags used forthe FCP template*/
#define UICC_FCP_TAG                    0x62
#define UICC_FILE_DESCRIPTOR_TAG        0x82
#define UICC_FILE_ID_TAG                0x83
#define UICC_DF_NAME_TAG                0x84
#define UICC_PROPRIETARY_INFO_TAG       0xa5
#define UICC_CHARACTERISTICS_TAG        0x80
#define UICC_APP_POWER_CONSUMPTION_TAG  0x81
#define UICC_MIN_APP_CLOCK_FREQ_TAG     0x82
#define UICC_MEM_AVAIL_TAG              0x83
#define UICC_LIFE_CYCLE_STATUS_TAG      0x8a

#define UICC_COMPACT_TAG                0x8c
#define UICC_EXPANDED_TAG               0xab
#define UICC_REF_TO_EXPANDED_TAG        0x8b
#define UICC_PIN_STATUS_TEMPLATE_DO     0xc6

#define UICC_AM_DO_TAG                  0x80
#define UICC_OR_TAG                     0xa0
#define UICC_AND_TAG                    0xaf
#define UICC_SC_DO_TAG                  0xa4
#define UICC_SC_DO_ALWAYS_TAG           0x90
/*CQ00016528 464593 for deocde ARR reocord, zhijie, 20120228, Begin*/
#define UICC_AM_DO_TAG_INS_START        0x81
#define UICC_AM_DO_TAG_INS_END          0x8F
#define UICC_AM_DO_TAG_PROPRITARY       0x9C
/*CQ00016528 464593 for deocde ARR reocord, zhijie, 20120228, End*/



#define UICC_FILE_SIZE_TAG              0x80
#define UICC_TOTAL_FILE_SIZE_TAG        0x81
#define UICC_SFI_TAG                    0x88

#define UICC_PS_DO_TAG                  0x90
#define UICC_USAGE_QUAL_TAG             0x95
#define UICC_KEY_REFERENCE_TAG          0x83

#define UICC_APPLICATION_TEMPLATE_TAG   0x61
#define UICC_APPLICATION_IDENTIFIER_TAG 0x4f
#define UICC_APPLICATION_LABEL_TAG      0x50

/*Tag of TLV objects in EF NETPAR (network parameters) */
#define UICC_GSM_CELL_INFO_TAG          0xa0
/*Zhiwei add TDD  for IT test start */
#define UICC_FDD_CELL_INFO_TAG          0xa1
#define UICC_TDD_CELL_INFO_TAG          0xa2
/*Zhiwei add TDD  for IT test end */
#define UICC_CAMPING_FREQ_TAG           0x80
#define UICC_NEIGHBOUR_FREQ_TAG         0x81
#define UICC_INTRA_FREQ_INFO_TAG        0x80
#define UICC_INTER_FREQ_INFO_TAG        0x81


#define UICC_TYPE_1_TAG                 0xa8
#define UICC_TYPE_2_TAG                 0xa9
#define UICC_TYPE_3_TAG                 0xaa
#define UICC_ADN_TAG                    0xc0
#define UICC_IAP_TAG                    0xc1
#define UICC_EXT1_TAG                   0xc2
#define UICC_SNE_TAG                    0xc3
#define UICC_ANR_TAG                    0xc4
#define UICC_PBC_TAG                    0xc5
#define UICC_GRP_TAG                    0xc6
#define UICC_AAS_TAG                    0xc7
#define UICC_GAS_TAG                    0xc8
#define UICC_UID_TAG                    0xc9
#define UICC_EMAIL_TAG                  0xca
#define UICC_CCP1_TAG                   0xcb
#define UICC_APN_TAG                    0xdd


/*length of the TLV objects */
#define UICC_APP_POWER_CONSUMPTION_TLV_LENGTH   3
#define UICC_TOTAL_FILE_SIZE_TLV_LENGTH         2
#define UICC_SFI_TLV_LENGTH                     1
#define UICC_FILE_SIZE_TLV_LENGTH               2
#define UICC_FILE_ID_TLV_LENGTH                 2
#define UICC_MIN_APP_CLOCK_FREQ_TLV_LENGTH      1
#define UICC_CHARACTERISTICS_TLV_LENGTH         1
#define UICC_LIFE_CYCLE_STATUS_TLV_LENGTH       1
#define UICC_KEY_REFERENCE_TLV_LENGTH           1
#define UICC_USAGE_QUAL_TLV_LENGTH              1
#define UICC_REF_TO_EXPANDED_TLV_LENGTH         3
#define UICC_AM_DO_TLV_LENGTH                   1
#define UICC_CAMPING_FREQ_TLV_LENGTH            2
#define UICC_NEIGHBOUR_FREQ_TLV_LENGTH          2

/*          file structures                */
#define EF_STRUCT_T              1
#define EF_STRUCT_LF             2
#define EF_STRUCT_C              6


#define DELETE_FILE_MASK        0x40
#define TERMINATE_MASK          0x20
#define ACTIVATE_MASK           0x10
#define DEACTIVATE_MASK         0x08
#define WRITE_MASK              0x04
#define UPDATE_MASK             0x02
#define READ_AND_SEARCH_MASK    0x01
#define DELETE_FILE             0x40
#define TERMINATE               0x20
#define ACTIVATE                0x10
#define DEACTIVATE              0x08
#define WRITE                   0x04
#define UPDATE                  0x02
#define READ_AND_SEARCH         0x01

#define APN_LABEL_SEPARATOR     0x2e

/*CQ00014528 Modfication by zhjzhao for CQ00013425 Merge from LTE_PS_DEV to NeZha_DEV_Daily 10-09-11, Begin*/
/*Modification for CQ00004507, cgLiu,Jun.02 2010, begin*/
#if defined (UPGRADE_LTE)
#define NCPIP_APN_TAG           0x50 /*80, the value maybe is 0x80*/
#define NCPIP_LOGIN_TAG         0x51
#define NCPIP_PASSWORD_TAG      0x52
#define NCPIP_DATADESTADDR_TAG  0x53
#define NCPIP_BEARDESC_TAG      0x54

/*define the EPS NSC TAGS*/
#define EPS_NSC_TAG             0xA0
#define EPS_NSC_ASME_KSI_TAG    0x80
#define EPS_NSC_ASME_KEY_TAG    0x81
#define EPS_NSC_UPLINK_NAS_COUNT_TAG    0x82
#define EPS_NSC_DOWNLINK_NAS_COUNT_TAG  0x83
#define EPS_NSC_NAS_ALGORITHMS_TAG      0x84

#define  SIM_TAC_DELETED_PHASE1  0x0000       
#define  SIM_TAC_DELETED_PHASE2  0xfffe       

#define  MOBILE_TAC_DELETED      0xfffe       

#define USIM_EPSLOCI_LENGTH             0x12
#define USIM_EPSLOCI_GUTI_LENGTH_ANITE 0x0C //added by zhjzhao 20111219 for temp use, should be deleted later
#define USIM_EPSLOCI_GUTI_LENGTH        0x0B
#define USIM_EPSLOCI_LAST_TAI_LENGTH    0x05
#define USIM_EPSLOCI_STATUS_LENGTH      0x01
#define USIM_EPSLOCI_GUTI_FLAG          0xF6

#endif /*end UPGRADE_LTE*/
/*Modification for CQ00004507, cgLiu,Jun.02 2010, end  */
/*CQ00014528 Modfication by zhjzhao for CQ00013425 Merge from LTE_PS_DEV to NeZha_DEV_Daily 10-09-11, End*/
/*----------CSG----------------------------------------*/

#define USIM_CSGL_TAG          0xA0
#define USIM_CSGL_PLMN_TAG     0x80
#define USIM_CSGL_CSGID_TAG    0x81
#define USIM_CSGL_DISP_TAG     0x82

#define USIM_CSGT_TAG               0x89
#define USIM_CSGT_ICON_URL          0x80
#define USIM_CSGT_ICON_REC_NU       0x81

#define USIM_CSG_HNBN_TAG           0x80
#define USIM_CSG_INVLID_TAG	0xFF
/*--------------------------------------------------*/

typedef enum SimUiccDecodeStatusTag
{
    UICC_DEC_COMM_OK,                  /* command data OK */
    UICC_DEC_COM_DATA_NOT_UNDERSTOOD,  /* unknown tag, minimum data OK, no CR */
    UICC_DEC_COM_TYPE_NOT_UNDERSTOOD,  /* unknown Command Type in Comm Details data */
    UICC_DEC_REQ_VALUES_MISSING,       /* minimum set of data elements not in command */
    UICC_DEC_UNEXPECTED_TAG,           /* Unexpected/Unknown tag */
    UICC_DEC_BAD_LENGTH,               /* wrong length */
    UICC_DEC_SEC_ATTRIBUTES_MISSING,
    UICC_DEC_PHONEBOOK_ERROR,           /*phone book error*/
    UICC_DEC_PIN_TEMPLATE_ERROR         /*error when decoding the PIN template*/
}
SimUiccDecodeStatus;

typedef enum SimUiccEncodeStatusTag
{
    UICC_ENC_COMM_OK,                  /* command data OK */
    UICC_ENC_MEMORY_PROBLEM,           /* memory problem on the USIM, e.g. data we want to encode is too big*/
    UICC_ENC_INVALID_PARAMS,           /*invalid params*/
    UICC_ENC_BAD_USIM_DATA             /*USIM data is corrupted*/

}
SimUiccEncodeStatus;


void SimUiccDecodeEst ( SimDataBlock   *dataBlock,
                        SimManagerData *simData);

void SimUiccDecodeUst ( SimDataBlock     *dataBlock,
                        UsimServiceTable *serviceTable);

SimUiccDecodeStatus SimUiccDecodeEfFcp ( SimDataBlock      *respData,
                                         SimUiccAppData    *uiccData,
                                         SimManagerData    *simData);

SimUiccDecodeStatus SimUiccDecodeDirFcp ( SimDataBlock      *respData,
                                          SimUiccAppData    *uiccData,
                                          SimManagerData    *simData);

SimUiccDecodeStatus SimUiccDecodeAidFcp ( SimDataBlock      *respData,
                                          SimUiccAppData    *uiccData,
                                          SimManagerData    *simData);

SimUiccDecodeStatus SimUiccDecodeEfDirRecord ( SimDataBlock    *dataBlock,
                                               SimUiccAid      *aid,
                                               SimUiccAidLabel *label);

void SimUiccDecodeEccRecord (SimDataBlock *dataBlock,
                             Int8 recordNumber,
                             SimEccList *eccList);

void SimUiccDecodeLi (SimDataBlock  *dataBlock,
                      Int16          startField,
                      Int16          numEntriesDesired,
                      Int16         *totalNumEntries,
                      LanguagePrefList *liList);


#if defined(SUPPORT_CB)
void  SimUiccDecodeCbmid ( SimDataBlock            *dataBlock,
                           Int16                   startField,
                           SimCbmidList            *cbmidList);
#endif

void  SimUiccDecodePlmn ( SimDataBlock            *dataBlock,
                          Int16                   startField,
                          SimPlmnList             *plmnList);

 void SimUiccEncodeLi ( LanguagePrefList *lpList,
                        SimDataBlock *dataBlock );
 /***********, Cgliu, 2022-03-04,Begin*/
#if defined (SUPPORT_PB)

Boolean SimUiccDecodePbrRecord   ( SimDataBlock *dataBlock,
                                   SimUiccPbrRecord *pbrRecord );

SimEfInfo *SimUiccGetFileInfo ( SimUiccPhoneBook *phonebook_p,
                                Int16             fileId );
void SimUiccDecodeGrpRecord   ( SimDataBlock *dataBlock,
                                SimUiccGrpData *grpData,
                                Int8            maxNumGasRecords );

void SimUiccDecodeGas         ( SimDataBlock *dataBlock,
                                SimAlphaIdentifier *gas);

void SimUiccEncodeGas         ( SimDataBlock *dataBlock,
                                Int8         recordLength,
                                SimAlphaIdentifier *gas);

void SimUiccDecodeAas         ( SimDataBlock *dataBlock,
                                SimAlphaIdentifier *aas);

Boolean SimUiccDecodeAasRecordNumber
                              ( SimDataBlock *dataBlock,
                                Int8          *aasRecNum );

void SimUiccEncodeAas         ( SimDataBlock *dataBlock,
                                Int8         recordLength,
                                SimAlphaIdentifier *aas);
#endif
/***********, Cgliu, 2022-03-04,End  */


#if 0
void SimUiccDecodeKeys ( SimDataBlock *dataBlock,
                         CipherKeyData *keyData );



void SimUiccDecodeStartHfn (SimDataBlock *dataBlock, Int32 *startHfnCs, Int32 *startHfnPs);
void SimUiccDecodeThreshold (SimDataBlock *dataBlock, Int32 *threshold);
void SimUiccEncodeStartHfn ( Int32 startHfnCs, Int32 startHfnPs, SimDataBlock *dataBlock);
#endif
void SimUiccEncodeKeys ( CipherKeyData *keyData,
                         SimDataBlock *dataBlock );

SimUiccDecodeStatus SimUiccDecodeArrRecord (Int8                      recordLength,
                                            Int8                     *ptr,
                                            SimUiccEfExpandedFormat  *expandedFormat);
Boolean SimUiccDecodeDialNumberRecord ( SimDataBlock     *dataBlock,
                                        SimDialNumber     *dialNum,
                                        Int8              *ccpRecord,
                                        Int8              *extRecord,
                                        SimDialNumberFile  dnFile);
 /***********,**********, Cgliu, 2022-03-04,Begin*/
#if defined (SUPPORT_PB)
 



 Int8 SimUiccGetAlphaIdSize (SimDialNumberFile  file,
                             Int16 recordLength,
                             Boolean returnMaxAllowedBySim);


Int8 SimUiccGetExtOffset(Int16     efId,
                         Int16     recordLength);
#endif
  /***********, Cgliu, 2022-03-04,End  */

#if 0
SimUiccDecodeStatus SimUiccDecodeNetPar (Int8               *readData_p,
                                         Int16                      dataLength,
                                         SimUiccGsmCellInformation *gsmCellInfo,
                                         SimUicc3GCellInformation  *tddCellInfo,
                                         SimUicc3GCellInformation  *fddCellInfo,
                                         Int16                     *gsmCurrentCampedCellFreq );
/* Mod by jungle for ********** on 2014-03-13 Begin */
void SimUiccEncodeNetPar (  SimUicc3GCellInformation         *tddCellInfo,
                                   SimUicc3GCellInformation         *fddCellInfo,
                                   SimUiccGsmCellInformation        *gsmCellInfo,
                                   Int16                            gsmCurrentCampedCellFreq,
                                   SimManagerData                   *simData,
                                   Int8                             *rawData_p,
                                   Int16                            *dataLength);
#endif
/* Mod by jungle for ********** on 2014-03-13 End */
/***********, Cgliu, 2022-03-04,Begin*/
#if defined (SUPPORT_PB)
void SimUiccDecodePbcRecord   (SimDataBlock *dataBlock, SimUiccPbcRecord *pbcRecord);
/* mod for CQ00085507 by yunhail  2015 02 12 begin */
void SimUiccDecodeEmailRecord (SimDataBlock *dataBlock, SimUiccEmailData *emailAddress,SimUiccFileLinking fileLinkingType);
/* mod for CQ00085507 by yunhail  2015 02 12 end */

Boolean SimUiccDecodeAnrRecord ( SimDataBlock  *dataBlock,
                                 SimUiccAnr    *anr,
                                 Int8          *ccpRecord,
                                 Int8          *extRecord,
                                 Int8          *additionalNumberId);
void SimUiccDecodeSneRecord (SimDataBlock *dataBlock, SimAlphaIdentifier *sne);
void SimUiccReadSneReq ( SimManagerData *simData );

void SimUiccEncodeAlphaId ( SimAlphaIdentifier *alphaId,
                            SimDataBlock       *dataBlock,
                            Int8                recordLength,
                            SimUiccFileLinking  fileLinking,
                            Int8                adnRecordNum,
                            Int8                adnSfi);
void SimUiccEncodeEmail   ( SimUiccEmailData   *email,
                            SimDataBlock       *dataBlock,
                            Int8                recordLength,
                            SimUiccFileLinking  fileLinking,
                            Int8                adnRecordNum,
                            Int8                adnSfi);

Boolean SimUiccEncodeGrp ( SimUiccGrpData  *grp,
                           SimDataBlock    *dataBlock,
                           Int8             recordLength,
                           Int8             maxGasRecords);

void SimUiccEncodeAnr (SimUiccAnr    *anr,
                       Int8           ccp1Record,
                       Int8           ext1Record ,
                       SimDataBlock  *dataBlock ,
                       Int8           aasRecordNumber,
                       Int8           adnSfi,
                       Int8           adnRecordNumber,
                       SimUiccFileLinking fileLinking);

void SimUiccEncodeCps ( CalledPartySubaddress *cps,
                        SimDataBlock          *dataBlock );

void SimUiccEncodeDialnum ( SimDialNumber          *dialnum_p,
                            Int8                    ccpRecord,
                            Int8                    extRecord ,
                            SimDataBlock           *dataBlock ,
                            Int8                    recordLength,
                            SimDialNumberFile       dnFile);  /*type of dialling number file*/
#endif
/***********, Cgliu, 2022-03-04,End  */

#if 0

void SimUiccEncodeCallInfo ( SimDataBlock      *dataBlock,
                             SimDialNumberFile  file,
                             SimUiccTimingInfo *timingInfo,
                             SimUiccPbLink     *pbLink,
                             Boolean            callAnswered,
                             Int8               recordLength,
                             SimManagerData    *simData );
#endif

void SimUiccEncodeAct      ( Int32              act,
                             SimDataBlock      *dataBlock );
/***********, Cgliu, 2022-03-04,Begin*/
#if defined (SUPPORT_PB)
Boolean SimUiccUpdateExt1InAdn (SimDialNumber      *oldAdn_p,
                                SimDialNumber      *newAdn_p,
                                Int8                oldExt1RecNum,
                                Int16               ext1FileId,
                                Int8               *newExt1RecNum_p,
                                SimManagerData     *simData,
                                SimUiccPhoneBook   *phonebook_p);
#endif
/***********, Cgliu, 2022-03-04,End  */


void SimUiccEncodeEst ( SimDataBlock      *dataBlock,
                        SimManagerData    *simData );
#if 0
void    SimUiccDecodeCallInfo ( SimDataBlock      *dataBlock,
                                SimDialNumberFile  file,
                                SimUiccTimingInfo *timingInfo,
                                SimUiccPbLink     *pbLink,
                                Boolean           *callAnswered,
                                SimManagerData    *simData);
#endif

void  SimUiccDecodeCallDuration ( SimDataBlock      *dataBlock,
                                  Int32             *callDuration);
    /*CQ00139815, Cgliu, 2022-11-09,Begin*/ 
#if 0

#if defined (UPGRADE_GPRS)
void  SimUiccDecodeApnList ( Int8                   *rawData_p,
                             Int16                   totalDataLength,
                             SimApnList             *apnList_p,
                             Int8                    startField);

SimUiccEncodeStatus SimUiccEncodeApnList ( Int8 position,
                                           TextualAccessPointName *apn_p,
                                           Int8 *rawData_p,
                                           Int16 totalDataLength,
                                           Boolean insert );
SimUiccEncodeStatus SimUiccDeleteApnFromAcl ( Int8  position,
                                              Int8 *rawData_p,
                                              Int16 totalDataLength );
#endif 	
	/*CQ00139815, Cgliu, 2022-11-09,End  */	

#endif  /*UPGRADE_GPRS*/

/*CQ00014528 Modfication by zhjzhao for CQ00013425 Merge from LTE_PS_DEV to NeZha_DEV_Daily 10-09-11, Begin*/
/*Modification for CQ00004507, cgLiu,Jun.02 2010, begin*/
#if defined (UPGRADE_LTE)
SimUiccDecodeStatus SimUiccDecodeNcpipRecord (SimDataBlock *dataBlock,
                             Int8 recordNumber,
                             UsimNcpipList *ncpipList);
SimUiccDecodeStatus SimUiccDecodeEpsloci ( SimDataBlock *dataBlock,
                            TrackingInfo *epsLoci);

SimUiccDecodeStatus  SimUiccDecodeEpsnsc (SimDataBlock *dataBlock,
                                Int8        *asmeKsi, 
                                AsmeKey     *asmeKey,
                                NasCount    *uplinkNasCount,
                                NasCount    *downlinkNasCount,
                                Int8        *algorithmIdentifier);

void SimUiccEncodeEpsloci (TrackingInfo *epsLociInfo, 
                                SimManagerData    *simData, 
                                SimDataBlock      *dataBlock);
                           
void SimUiccEncodeEpsnsc ( Int8         *asmeKsi, 
                                AsmeKey     *asmeKey,
                                NasCount    *uplinkNasCount,
                                NasCount    *downlinkNasCount,
                                Int8        *algorithmIdentifier, 
                                SimDataBlock *dataBlock);                          
                           


#endif /*end UPGRADE_LTE*/
/*Modification for CQ00004507, cgLiu,Jun.02 2010, end  */
/*CQ00014528 Modfication by zhjzhao for CQ00013425 Merge from LTE_PS_DEV to NeZha_DEV_Daily 10-09-11, End*/
#endif
#if defined (UPGRADE_CSG1)						
/* --------------------------- CSG ----------------------------- */
Boolean SimUiccEncodeAcsglRecord (SimManagerData         *simData, SimPlmn *plmn_p, SimUiccCsgInfo *csgInfo_p,Int8 * csgLength,  SimDataBlock  *dataBlock);

Boolean SimUiccEncodeCsgtRecord (SimManagerData         *simData,
                                SimDataBlock  *dataBlock,
                                Int8           iconUrlLength,             /**<  only for Graphic type */
                                Int8           *iconUrl_p,
                                Int8           iconRecNo,                  /**<  Record number in EF_IMG file.  */
                                Int8           iconQualifier,               /**< Icon qualifier */
                                Int8           textLength,
                                Int8           *text_p);

void SimUiccEncodeHnbnRecord(SimDataBlock  *dataBlock, Int8 dataLength, Int8* data_p);


Boolean SimDecodeCsglRecord ( SimDataBlock *data_p, SimPlmn *plmn_p, SimUiccCsgInfo *csgInfo_p,Int8 * csgLength, Int8 *displayInd_p, Boolean *displayIndPresent_p);

Boolean SimDecodeCsgtRecord ( SimDataBlock *data_p,
                                  Int8      *iconUrlLength,             /**<  only for Graphic type */
                                  Int8      *iconUrl,
                                  Int8      *iconRecNo,                  /**<  Record number in EF_IMG file.  */
                                  Int8      *iconQualifier,               /**< Icon qualifier */
                                  Int8      *textLength,
                                  Int8      *text);
Boolean SimDecodeHomeNodeBName (SimDataBlock *cmdBlock_p,Int8 *dataLength_p, Int8* data_p);
#endif // (UPGRADE_CSG1)						
#endif


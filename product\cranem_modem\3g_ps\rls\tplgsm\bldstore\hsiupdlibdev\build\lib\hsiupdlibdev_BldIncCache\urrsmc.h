/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/
 
/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urrsmc.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/27 15:10:43 $
 **************************************************************************
 * File Description:
 *
 *    Header file for URRSMC.C.
 **************************************************************************/

#if !defined (URRSMC_H)
#define       URRSMC_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <urr_sig.h>

#if defined (RRC_UPGRADE_ENG_MODE_REDESIGN) //********** begin
#include <urrmtcem.h>
#endif //RRC_UPGRADE_ENG_MODE_REDESIGN :********** end

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/

/***************************************************************************
*   Macro Functions
***************************************************************************/

/***************************************************************************
 * Types
 ***************************************************************************/

/***************************************************************************
 * Typed Constants
 ***************************************************************************/

/***************************************************************************
 * Function Prototypes
 ***************************************************************************/

void UrrSmcInit (void);

Boolean UrrSmcCheckInConnectedState(UrrSmcConnectedState connectedState);

Boolean UrrSmcCheckInIdleState(void);
Boolean UrrSmcCheckInConnectedMode(void);/* **********  added */
void UrrSmcProcessInternalSignal (SignalBuffer *signal_p);

void UrrSmcGetModeAndState (UrrSmcModeAndState *modeAndState_p);
void UrrSmcGetLastModeAndState (UrrSmcModeAndState *lastModeAndState_p);  /* for ********** */

UrrSmcMode UrrSmcGetMode (void);
UrrSmcMode UrrSmcGetLastMode (void);                                           

void UrrSmcSetModeAndState (UrrSmcModeAndState *modeAndState_p, Boolean isReconfig, Boolean reconfigIncludedPsc); /*********** - added reconfigIncludedPsc*/

void UrrSmcSetCurrentModeAndState (UrrSmcModeAndState modeAndState);/************/

void UrrSmcGetPhyState (UrrSmcPhyState *state_p);

void UrrSmcKiStartTimer (KiTimer *timerToStart);
void UrrSmcKiStopTimer (KiTimer *timerToStop);

void UrrSmcKiSendSignal (TaskId destTask, SignalBuffer *signal);

void UrrSmcKiEnqueue (KiUnitQueue *queue_p, SignalBuffer *signal_p);

void UrrSmcKiDequeue (KiUnitQueue *queue_p, SignalBuffer *signal_p);

void UrrSmcSetDebugStateChange (Boolean enabled);

Boolean UrrSmcUeIsInConnectedState (UrrSmcConnectedState connectedState);
Boolean UrrSmcUeIdleOrConnectedNonDch (void);//**********


Boolean UrrSmcGetReselectToLteAviable(void);
#if defined(UPGRADE_DSDSWB)
/* ********** added begin */
Boolean UrrSmcCheckInPchStateOfOtherTask(void);
/* ********** added end */
void UrrSmcKiSendSignalToAnotherCard (TaskId destTask, SignalBuffer *signal);
#endif
/* **********  added begin */
#if defined(UPGRADE_DSDS)
Boolean UrrSmcCheckInConnectedModeWithSimId(Int8 simId);
#endif/*UPGRADE_DSDS*/
/* **********  added end */

#endif
/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 2006 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/releases/development/branch_<PERSON><PERSON>_<PERSON><PERSON>_WW44_2007/tplgsm/modem/psas/3g.mod/lib/src/urlamwin.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2007/10/23 18:24:35 $
 **************************************************************************
 *  File Description :
 **************************************************************************/

#if !defined (URLAMWIN_H)
#define       URLAMWIN_H

#if defined (ENABLE_NEW_URLC_OPTIMISATIONS)

/***************************************************************************
 * Nested Include Files
 **************************************************************************/
#include <urlam.h>


/***************************************************************************
 * Manifest Constants
 **************************************************************************/


/***************************************************************************
 * Type Definitions
 **************************************************************************/


/***************************************************************************
 *  Macros
 **************************************************************************/
/***************************************************************************
 * Macro       : URL_AM_WIN_DEBUG_PARAMS
 * Group       : URLC AM
 * Parameter   : None
 * Description : Adds debug information to the function parameters when
 *               DEVELOPMENT_VERSION is enabled.
 ***************************************************************************/
#if defined (DEVELOPMENT_VERSION) || defined (KI_CHECK_ASSERTIONS)
#define URL_AM_WIN_DEBUG_PARAMS         ,char * file, Int32 line

#define M_UrlAmWinGetPduEntry( eNTITY_P, sN) UrlAmWinGetPduEntry (eNTITY_P, sN, MODULE_NAME, __LINE__)

#else
#define URL_AM_WIN_DEBUG_PARAMS

#define M_UrlAmWinGetPduEntry( eNTITY_P, sN) UrlAmWinGetPduEntry (eNTITY_P, sN)
#endif

/***************************************************************************
 * Macro       : M_UrlAmWinFreeSpaceAvailable
 * Group       : URLC AM
 * Parameter   : eNTITY_P: Pointer to the entity data
 * Description : Checks if there is space in the transmit window to store
 *               a new block. Resolves to TRUE if there is space in the
 *               window. Resolves to FALSE if the window is full.
 ***************************************************************************/
#define M_UrlAmWinFreeSpaceAvailable( eNTITY_P) \
    (UrlUtilAmDiffSn (eNTITY_P->vtNew, \
                    ((eNTITY_P->vtA + eNTITY_P->txPduTableSize) % URL_AM_SN_MODULUS), \
                    URL_AM_PREV_SN (eNTITY_P->vtNew) \
                    ) < 0)

/***************************************************************************
 * Function    : UrlAmWinIsPduPresentInBitmap
 * Group       : URLC AM
 * Parameter   : bITmAP: Pointer to the start of a bitmap
 *               sEQUENCEnUMBER: SN of PDU to check
 * Description : Checks if a PDU with a specific SN is present in a bitmap
 ***************************************************************************/
#define UrlAmWinIsPduPresentInBitmap( bITmAP, sEQUENCEnUMBER) \
    ((bITmAP [sEQUENCEnUMBER / BITS_PER_INT32] & (0x80000000 >> (sEQUENCEnUMBER % BITS_PER_INT32))) != 0)


/***************************************************************************
 *  Function Prototypes
 **************************************************************************/
Boolean UrlAmWinRemovePduFromBitmap (Int32 * const bitmap_p, const UrlSequenceNumber sequenceNumber);
Boolean UrlAmWinAddPduToBitmap (Int32 * const bitmap_p, const UrlSequenceNumber sequenceNumber);

void UrlAmWinIntitialiseTxActiveBitmap (UrlAmTxRxEntity * const entityData_p);

void UrlAmWinAddPduToTxActiveBitmap (UrlAmTxRxEntity * const entityData_p,
                                     const UrlSequenceNumber sequenceNumber);

Boolean UrlAmWinRemovePduFromActiveTxBitmap (UrlAmTxRxEntity * const entityData_p,
                                             const UrlSequenceNumber sequenceNumber);

Boolean UrlAmWinGetFirstTxActivePduUsingSn (UrlAmTxRxEntity * const entityData_p,
                                            const UrlSequenceNumber startSequenceNumber,
                                            UrlSequenceNumber * const sequenceNumber_p);

Boolean UrlAmWinGetFirstTxActivePdu (UrlAmTxRxEntity * const entityData_p,
                                     UrlSequenceNumber * const sequenceNumber_p);

Boolean UrlAmWinGetLastTxActivePduUsingSn (UrlAmTxRxEntity * const entityData_p,
                                           const UrlSequenceNumber lastSequenceNumber,
                                           UrlSequenceNumber * const sequenceNumber_p);

void UrlAmWinInitialiseReTxBitmap (UrlAmTxRxEntity * const entityData_p);

void UrlAmWinAddPduToReTxBitmap (UrlAmTxRxEntity * const entityData_p,
                                 const UrlSequenceNumber sequenceNumber);

void UrlAmWinInitialiseTxDataPduTable (UrlAmTxRxEntity * const entityData_p);

void UrlAmWinCreateNewTxDataPduTable (UrlAmTxRxEntity * const entityData_p,
                                      const Int16 tableSize);

void UrlAmWinResizeTxDataPduTable (UrlAmTxRxEntity * const entityData_p,
                                   const Int16 newTableSize);

UrlAmTxDataPdu * UrlAmWinGetPduEntry (UrlAmTxRxEntity * const entityData_p,
                                      const UrlSequenceNumber sequenceNumber
                                      URL_AM_WIN_DEBUG_PARAMS);

Boolean UrlAmWinGetNextPduToReTxFromBitmap (UrlAmTxRxEntity * const entityData_p,
                                            UrlSequenceNumber * const sequenceNumber_p);

Boolean UrlAmWinRemovePduFromReTxBitmap (UrlAmTxRxEntity * const entityData_p,
                                         const UrlSequenceNumber sequenceNumber);

Boolean UrlAmWinGetNextPduToReTxUsingSn (UrlAmTxRxEntity * const entityData_p,
                                         const UrlSequenceNumber startSequenceNumber,
                                         UrlSequenceNumber * const foundSequenceNumber_p);

Boolean UrlAmWinGetNextPduToReTx (UrlAmTxRxEntity * const entityData_p,
                                  UrlSequenceNumber * const sequenceNumber_p);


Boolean UrlAmWinGetLastPduToReTx (UrlAmTxRxEntity * const entityData_p,
                                  UrlSequenceNumber * const sequenceNumber_p);

Boolean UrlAmWinGetLastPduToReTxUsingSn (UrlAmTxRxEntity * const entityData_p,
                                         const UrlSequenceNumber lastSequenceNumber,
                                         UrlSequenceNumber * const sequenceNumber_p);

void UrlAmWinDestroyTxDataPduTable (UrlAmTxRxEntity * const entityData_p);
void UrlAmWinIntialiseTxAckNackBitmap (UrlAmTxRxEntity * const entityData_p);
#endif /* ENABLE_NEW_URLC_OPTIMISATIONS */
#endif
/* END OF FILE */

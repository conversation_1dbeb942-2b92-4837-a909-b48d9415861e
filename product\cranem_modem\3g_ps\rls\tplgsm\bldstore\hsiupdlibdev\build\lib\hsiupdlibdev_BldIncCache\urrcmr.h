/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/
  
/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urrcmr.h#5 $
 *   $Revision: #5 $
 *   $DateTime: 2007/03/30 13:53:25 $
 **************************************************************************
 * File Description:
 *
 *    Header file for URRCMR.C.
 **************************************************************************/

#if !defined (URRCMR_H)
#define       URRCMR_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <urr_sig.h>

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/

/***************************************************************************
*   Macro Functions
***************************************************************************/

/***************************************************************************
 * Types
 ***************************************************************************/

typedef enum securityConfigTypeTag
{
    CIPHERING,
    INTEGRITY,
    BOTH
}
securityConfigType;

/***************************************************************************
 * Typed Constants
 ***************************************************************************/

/***************************************************************************
 * Function Prototypes
 ***************************************************************************/

void UrrCmrInit (void);

void UrrCmrProcessInternalSignal (SignalBuffer *signal_p);

void UrrCmrCrlcDataPendingInd (RadioResourceControlEntity *urr_p);

void UrrCmrHandleTimers (RadioResourceControlEntity *urr_p);

void UrrCmrSib2Ind (UURA_IdentityList *uUraIdentityList_p);

void UrrCmrSetCellUpdateStarted (Boolean state);

Boolean UrrCmrGetCellUpdateStarted (void);

UCellUpdateCause UrrCmrGetCellUpdateCause (void);

void UrrCmrSetFailureIndicator (Boolean state);

Boolean UrrCmrGetCellHasChanged(void);
UrlTrafficVolumeBits UrrMcrGetTrafficVolumeBits(void);
void UrrCmrSetUraIdentity (URRC_StateIndicator newUeState,
                           Boolean identityPresent,
                           UURA_Identity identity);

void UrrCmrTest(void);

Boolean UrrCmrGetUraIdentity (UURA_Identity *uraId_p);

void UrrCmrTriggerRntiUpdateIfNoStateChange (UrrSmcMode newUeMode,
                                             URRC_StateIndicator newUeState,
                                             Boolean forceImmediateUpdate);

void UrrCmrClearCrntiInRrcAndMac (void);

void UrrCmrClearUrntiAndCrntiInRrcAndMac (void);

Boolean UrrCmrUtranCanInferUeCellLocation (UrrInternalSignalCampedOnCell *sig_p);

void UrrCmrSetCellHasChanged (Boolean cellHasChanged);

Boolean UrrCmrT314IsRunning (void);

#if defined (UPGRADE_3G_RELEASE_6)
void UrrCmrSetReconfigStatusIndicator (Boolean orderReconfig);
#endif /* UPGRADE_3G_RELEASE_6 */

void UrrCmrGetState (UrrCmrState *cmrState_p);

#if defined(UPGRADE_UL_ECF) // CQ00065300 begin 	
void ReStartT305UnlessInfinity (void);		
#endif /* UPGRADE_UL_ECF  */ // CQ00065300 end	

Boolean UrrCmrCheckIfCmrIsWaitingForSib7BeforeUpdate(void);

Boolean UrrCmrUDTConditionsFulfilled(void);

#endif
 /* END OF FILE */

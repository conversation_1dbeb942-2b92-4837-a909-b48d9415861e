/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/******************************************************************************
 * File Name:   udtcif.h
 *
 *
 * File Description: DTC interface header file.
 *
 * Data Transfer Cipher (DTC) - is hardware accelerator for processing load for
 * data transfer and ciphering operations.
 *
 * DTC has the followings attributes:
 * ==================================
 *  1. F8 and F9 for UMTS R99 and R5, R6 features including HSDPA and HSUPA (E-DCH).
 *  2. Support of HSDPA category10 (13.92Mbps) and HSUPA category6(5.72Mbps).
 *  3. The DTC is based on usage of a DMA for Data transfers (to save core cycles
 *     used on data copying).
 *  4. DTC Minimize number of Cipher HW Interrupts in Ciphering driver/SW and
 *     reduce context switches and latency in ARM.  This is be done by using chained
 *     configuration with HW self reloading via DMA. (for HSDPA and HSUPA).
 *  5. DTC supports chained descriptors for concatenating data transactions.
 *  6. Data and configuration descriptors can be located in different locations in memory.
 *  7. DTC supports configuration of DTC for single block ciphering mode (for R99
 *     legacy support) -  direct addressing via SW.
 *  8. DTC allows different bit offset for input and output stream.
 *     a. INPUT_BIT_OFFSET (IBO) - determines the number of bits that are not taken into
 *        the ciphering or transfer operation.
 *     b. OUTPUT_BIT_OFFSET(OBO) - Determines the number of bits offset in the output
 *        stream. Data copy with output bit offset defined writes the output stream with
 *        this offset.
 *  9. Enable write & read operations from byte aligned addresses.
 *  10.Enable cipher bypassed mode - when ciphering is not used, the DTC can be used to
 *     copy PDUs from one memory location to another location including bit shifting.
 *  11.Continuous output mode for building PDUs.
 *     In this mode the SW provides the source PDU and the destination address for the
 *     MAC-E PDU head.  The DTC handles and tracks the destination address per MAC-es PDU
 *     and bit offset within the MAC-e PDU. (i.e. SW does not need to track the destination
 *     address and bit offset per MAC-es PDU within the MAC-e PDU).
 *     CONT_OUT_MODE is mandatory for HSUPA PDU building.
 *  12.Source or Target can be from DDR, DTCM, GB L2SRAM.
 *  13.DTC supports ciphering context switch to handle processing of higher priority
 *     chained ciphering. This is required due to HSUPA latency requirements.
 *  14.DTC supports Little/Big Endean output and input modes - mode configured by SW.
 *  15.DTC will start processing the chain once a valid configuration is available.
 *     (The SW can build the chain in parallel to the HW processing)
 *  16.DTC supports retaining the cipher state between two configurations.
 *  17.DTC supports ciphering to same address (Destination Dddress = Source Address)
 *  18.DTC operation can be done in blocking/non-blocking mode.
 *
 * DTC operation:
 * ==============
 *  1. Application should build linked descriptors chain containing the below
 *     information. the chain must be NULL terminated.
 *  2. Application can provide call-back function that will be called from HISR when
 *     DTC will finish the request processing.
 *  3. The DTC request contains the followings:
 *      - DTC operation (F8/F9).
 *      - Chain end event action (call-back function/interupt/none).
 *      - Priority (high/normal).
 *      - Blocking mode (blocking/non-blocking).
 *      - Direction (uplink/downlink).
 *      - Request ID - application request ID. Will moved to call-back function.
 *      - First descriptor pointer.
 *      - Callback function address (valid address/NULL for no call-back).
 *
 * Descriptor content:
 * ===================
 *  struct UdtcF8DescriptorTag *next_p; - Next descriptor pointer
 *  Int8                  *srcData_p;   - Source data address pointer
 *  Int8                  *dstData_p;   - Destination address pointer
 *  Int16                 bitLength;    - Total bit length
 *  Int8                  bearer;       - Radio bearer ID for F8
 *  Int8                  dtcModeReg;   -
 *  ----------------------------------------------------
 *  |  RLC  |   r   |   c   |   c   |   r   |    F8    |
 *  |  ign  |   c   |   b   |   o   |   s   |  cipher  |
 *  |  ore  |   s   |       |   n   |   v   |  offset  |
 *  |       |       |       |   t   |       |          |
 *  |  (1)  |  (1)  |  (1)  |  (1)  |  (1)  |   (3)    |
 *  ----------------------------------------------------
 *  F8 Cipher Offset (3 bits), Reserved (1 bit), F8 Continuous Output (1 bit),
 *  F8 Cipher Bypass (1 bit), F8 Retain Cipher State (1 bit), Reserved (1 bit)
 *
 *  Int32                 headerReg     -
 *  ---------------------------------------------
 *  |    HDR    | RLC   |   r   |  Header Data  |
 *  |    size   | Debug |   s   |               |
 *  |    ID     | Mark  |   v   |               |
 *  |    (4)    |  (1)  |  (5)  |     (22)      |
 *  ---------------------------------------------
 *  F8 Header Data (22 bits),  Reserved (5 bits),
 *  RLC Unfiltered Debug Mark (1Bit), F8 Header Size ID (4 bits)
 *
 *  Int16                 dstBitOffset; - F8 Output bit Offset
 *  Int16                 srcBitOffset; - F8 Input bit Offset
 *  Int32                 countC;       - Parameter for cipher
 *  Int8                  *key_p;       - Ciphering key address
 *
 *****************************************************************************/

#if !defined (UDTCIF_H)
#define       UDTCIF_H

#define DTC_HW_VERSION_5
//#define DTC_SUPPORT_RETAIN_CIPHER_STATE

/*  If UDTC_MACI_SS_SW_WORKAROUND_SUPPORT is set, MAC-i will use an additional dtc descriptor
  * to embbed the SS field in the mac-is header. 
  * Else the SS will be embedded in the headerReg field along with the TSN and RLC header.
  * the current dtc in FB version does not support 24 bits in the header reg so need to use the SW workaround
*/
#if defined (UPGRADE_3G_UL_EL2) 
#define UDTC_MACI_SS_SW_WORKAROUND_SUPPORT
#endif
/******************************************************************************
 * Include Files
 *****************************************************************************/
#include <dtc.h>
#include <ups_typ.h>
#include <u1_cfg.h>
#include <umacebase.h>


/******************************************************************************
 * External prototypes
 *****************************************************************************/

/* Temp, until included in dtc.h*/
extern void dtcDebugPrintHwRegisters(void);

/******************************************************************************
 * Constants
 *****************************************************************************/
#define UDTC_IF_CONTINUOUS_OUTPUT_MASK      (0x10)    /* 00010000 */
#define UDTC_IF_CIPHER_BYPASS_MASK          (0x20)    /* 00100000 */
#define UDTC_IF_RETAIN_CIPHER_STATE_MASK    (0x40)    /* 01000000 */
#define UDTC_IF_CIPHER_OFFSET_BITS_ON       (0x7)     /* 00000111 */
#define UDTC_IF_CIPHER_OFFSET_CLEAR_MASK    (0xF8)    /* 11111000 */
#define UDTC_IF_MAX_F8_CIPHER_OFFSET        (20)

/* Default mode: don't retain cipher state, no cipher, */
/* not continous output, no cipher offset  =  00100000 */
#define UDTC_IF_DEFAULT_MODE_REG            (0x20)

/* Default header: no f8 header size ID, no f8 header data */
#define UDTC_IF_DEFAULT_HEADER_REG          (0)

/* Clear header size ID mask:    00001111111111111111111111111111 */
#define UDTC_IF_HEADER_SIZE_ID_CLEAR_MASK   (0x0FFFFFFF)

/* Header size ID mask:          11110000000000000000000000000000 */
#define UDTC_IF_HEADER_SIZE_ID_MASK         (0xF0000000)

/* TSN header size ID mask:      00100000000000000000000000000000 */
#define UDTC_IF_TSN_HEADER_SIZE_ID_MASK     (0x20000000)

#if !defined (UDTC_MACI_SS_SW_WORKAROUND_SUPPORT)
/* TSN+SS header size ID mask:   00110000000000000000000000000000 */
#define UDTC_IF_TSN_SS_HEADER_SIZE_ID_MASK          (0x30000000)
#endif

#define UDTC_IF_IGNORE_F8_DESCRIPTOR_RLC_ONLY_MASK  (0x80)
#define UDTC_IF_IS_IGNORED_BY_RLC(modReg)           (modReg & UDTC_IF_IGNORE_F8_DESCRIPTOR_RLC_ONLY_MASK)

/* RLC Debug mark mask:          000010000000000000000000000000000*/
#define UDTC_IF_HEADER_RLC_DEBUG_MARK_MASK  (0x08000000)

/* Clear RLC Debug mark mask:    111101111111111111111111111111111*/
#define UDTC_IF_HEADER_RLC_DEBUG_MARK_CLEAR_MASK    (0xF7FFFFFF)

/* Left header data bit on mask: 10000000000000000000000000000000 */
#if !defined (UDTC_MACI_SS_SW_WORKAROUND_SUPPORT)
#define UDTC_IF_MAX_HEADER_SIZE_BITLEN      (24)
#define UDTC_IF_MAX_HEADER_SIZE_ID          (9)           /* 1001 */
#else
#define UDTC_IF_MAX_HEADER_SIZE_BITLEN      (22)
#define UDTC_IF_MAX_HEADER_SIZE_ID          (8)           /* 1000 */
#endif

#define UDTC_IF_HEADER_SIZE_ID_OFFSET       (28)    /* 1(RLC Debug Mark) + 5(rsv) + 22(header size) */
#define UDTC_IF_UNDIFINED_HEADER_SIZE_ID    (0)
#define UDTC_IF_RLC_DEBUG_MARK_OFFSET       (27)    /* 5(rsv) + 22(header size) */
#define UDTC_IF_RLC_DEBUG_MARK_BITLEN       (1)

/* Alignment limitations */
#define UDTC_IF_DESCRIPTOR_BYTES_ALIGNMENT  (32)    /* DTC: 256 bits, Cache line: 256 bits */
#define UDTC_IF_KEY_BYTES_ALIGNMENT         (32)    /* DTC: 128 bits, Cache line: 256 bits */

/* R99/R5/R6 descriptors constants */
#define UDTC_IF_F9_DESCRIPTORS_NUM          (1)
#define UDTC_IF_BLOCKING_F8_DESCRIPTORS_NUM (1)
#define UDTC_IF_R99_DL_DESCRIPTORS_NUM      (UPS_MAX_DL_NO_OF_TB_PER_TTI*UPS_MAX_OVERLAPPING_UMAC_PDU_LIST_IND)
#define UDTC_IF_R99_UL_DESCRIPTORS_NUM      (UPS_MAX_UL_NO_OF_TB_PER_TTI)
#if defined (UPGRADE_3G_HSDPA)
#if defined (DTC_SUPPORT_RETAIN_CIPHER_STATE)
#define UDTC_IF_HSDPA_DESCRIPTORS_NUM       (MAX(UMAHS_MAX_SDUS_PER_DATA_IND,UMAEHS_MAX_SDUS_PER_DATA_IND))
#else
/* if using the RCS software bypass, need to add 1 dtc descriptor for each possible segmentain of an RLC pdu */
#define UDTC_IF_HSDPA_DESCRIPTORS_NUM       (MAX(UMAHS_MAX_SDUS_PER_DATA_IND,UMAEHS_MAX_SDUS_PER_DATA_IND) + (UMAEHS_MAX_REORDERNG_PDU_PER_DATA_IND-1))
#endif
#if defined (URL_DEBUG_HSDPA_OVER_DTC)
#define UDTC_IF_HSDPA_DEBUG_DESCRIPTORS_NUM (3)     /* Metadata, Alldata, Ciphering Info */
#else
#define UDTC_IF_HSDPA_DEBUG_DESCRIPTORS_NUM (0)
#endif /* URL_DEBUG_HSDPA_OVER_DTC */
#else
#define UDTC_IF_HSDPA_DESCRIPTORS_NUM       (0)
#define UDTC_IF_HSDPA_DEBUG_DESCRIPTORS_NUM (0)
#endif /* UPGRADE_3G_HSDPA */
#if defined (UPGRADE_3G_EDCH)
#define UDTC_IF_EDCH_DESCRIPTORS_NUM        (256)
#if defined (URL_DEBUG_EDCH_OVER_DTC)
#define UTDC_IF_DEBUG_DATA_DESCRIPTORS_NUM  ((UPS_EDCH_MAX_TB_BITS/336)/2 + UMAC_EDCH_MAX_LOGICAL_CHANNELS) /* Rest of debug data */
#define UDTC_IF_EDCH_DEBUG_DESCRIPTORS_NUM  (2 + UTDC_IF_DEBUG_DATA_DESCRIPTORS_NUM) /* Metadata, Ciphering Info and rest of data */
#else
#define UDTC_IF_EDCH_DEBUG_DESCRIPTORS_NUM  (0)
#endif /* URL_DEBUG_HSDPA_OVER_DTC */
#else
#define UDTC_IF_EDCH_DESCRIPTORS_NUM        (0)
#define UDTC_IF_EDCH_DEBUG_DESCRIPTORS_NUM  (0)
#endif /* UPGRADE_3G_EDCH */
#ifdef UMTS7_PRE_R8
#define UDTC_IF_DLBGDL_DESCRIPTORS_NUM    (480*3)//(200*3) Fixed ********** 20131114
#endif
#ifdef UMTS7_PRE_R8 
#define UDTC_IF_TOTAL_DESRIPTORS_NUMBER     (UDTC_IF_F9_DESCRIPTORS_NUM          +\
                                             UDTC_IF_BLOCKING_F8_DESCRIPTORS_NUM +\
                                             UDTC_IF_R99_DL_DESCRIPTORS_NUM      +\
                                             UDTC_IF_R99_UL_DESCRIPTORS_NUM      +\
                                             UDTC_IF_HSDPA_DESCRIPTORS_NUM       +\
                                             UDTC_IF_HSDPA_DEBUG_DESCRIPTORS_NUM +\
                                             UDTC_IF_EDCH_DESCRIPTORS_NUM        +\
                                             UDTC_IF_EDCH_DEBUG_DESCRIPTORS_NUM+\
                                             UDTC_IF_DLBGDL_DESCRIPTORS_NUM)

#else
#define UDTC_IF_TOTAL_DESRIPTORS_NUMBER     (UDTC_IF_F9_DESCRIPTORS_NUM          +\
                                             UDTC_IF_BLOCKING_F8_DESCRIPTORS_NUM +\
                                             UDTC_IF_R99_DL_DESCRIPTORS_NUM      +\
                                             UDTC_IF_R99_UL_DESCRIPTORS_NUM      +\
                                             UDTC_IF_HSDPA_DESCRIPTORS_NUM       +\
                                             UDTC_IF_HSDPA_DEBUG_DESCRIPTORS_NUM +\
                                             UDTC_IF_EDCH_DESCRIPTORS_NUM        +\
                                             UDTC_IF_EDCH_DEBUG_DESCRIPTORS_NUM)
  #endif                                          


/* Descriptors table is hard coded allocated for different requests/channels as following:
 *  --------------------------------------------------------------------------------
 *  | F9 | Blocking F8 | R99 DL | R99 UL | HSDPA | HSDPA DEBUG | EDCH | EDCH DEBUG |
 *  --------------------------------------------------------------------------------*/
#define UDTC_IF_F9_TABLE_ENTRY                  (0)
#define UDTC_IF_BLOCKING_F8_TABLE_ENTRY         (UDTC_IF_F9_DESCRIPTORS_NUM)
#define UDTC_IF_FIRST_R99_DL_TABLE_ENTRY        (UDTC_IF_BLOCKING_F8_TABLE_ENTRY  + UDTC_IF_BLOCKING_F8_DESCRIPTORS_NUM)
#define UDTC_IF_FIRST_R99_UL_TABLE_ENTRY        (UDTC_IF_FIRST_R99_DL_TABLE_ENTRY + UDTC_IF_R99_DL_DESCRIPTORS_NUM)
#if defined (UPGRADE_3G_HSDPA)
#define UDTC_IF_FIRST_HSDPA_TABLE_ENTRY         (UDTC_IF_FIRST_R99_UL_TABLE_ENTRY + UDTC_IF_R99_UL_DESCRIPTORS_NUM)
#define UDTC_IF_FIRST_HSDPA_DEBUG_TABLE_ENTRY   (UDTC_IF_FIRST_HSDPA_TABLE_ENTRY  + UDTC_IF_HSDPA_DESCRIPTORS_NUM)
#endif /* UPGRADE_3G_HSDPA */
#if defined (UPGRADE_3G_EDCH)
#define UDTC_IF_FIRST_EDCH_TABLE_ENTRY          (UDTC_IF_FIRST_HSDPA_DEBUG_TABLE_ENTRY  + UDTC_IF_HSDPA_DEBUG_DESCRIPTORS_NUM)
#define UDTC_IF_FIRST_EDCH_DEBUG_TABLE_ENTRY    (UDTC_IF_FIRST_EDCH_TABLE_ENTRY  + UDTC_IF_EDCH_DESCRIPTORS_NUM)
#endif /* UPGRADE_3G_EDCH */
#ifdef UMTS7_PRE_R8 
#ifdef UPGRADE_3G_HSDPA
#define UDTC_IF_FIRST_DLBGDL_TABLE_ENTRY      (UDTC_IF_FIRST_EDCH_DEBUG_TABLE_ENTRY+UDTC_IF_EDCH_DEBUG_DESCRIPTORS_NUM)
#else
#define UDTC_IF_FIRST_DLBGDL_TABLE_ENTRY      (UDTC_IF_FIRST_R99_UL_TABLE_ENTRY+UDTC_IF_R99_UL_DESCRIPTORS_NUM)
#endif
#endif


/******************************************************************************
 * Macros
 *****************************************************************************/
#define UDTC_IF_ENABLE_CIPHERING(modReg)            (modReg &= ~UDTC_IF_CIPHER_BYPASS_MASK)
#define UDTC_IF_DISABLE_CIPHERING(modReg)           (modReg |= UDTC_IF_CIPHER_BYPASS_MASK)
#define UDTC_IF_IS_CIPHERING_DISABLED(modReg)       (modReg &  UDTC_IF_CIPHER_BYPASS_MASK)
#define UDTC_IF_IS_CIPHERING_ENABLED(modReg)        (!(modReg & UDTC_IF_CIPHER_BYPASS_MASK))
#if defined (DTC_SUPPORT_RETAIN_CIPHER_STATE)
#define UDTC_IF_ENABLE_RETAIN_CIPHER_STATE(modReg)  (modReg |= UDTC_IF_RETAIN_CIPHER_STATE_MASK)
#endif /* DTC_SUPPORT_RETAIN_CIPHER_STATE */
#define UDTC_IF_DISABLE_CONT_MODE(modReg)           (modReg &= ~UDTC_IF_CONTINUOUS_OUTPUT_MASK)
#define UDTC_IF_ENABLE_CONT_MODE(modReg)            (modReg |= UDTC_IF_CONTINUOUS_OUTPUT_MASK)

#define UDTC_IF_GET_REQUEST_FIRST_DESCRIPTOR_P(udtcRequestInfo_p) \
    (dtcF8DescriptorInfo*) (udtcRequestInfo_p->dtcRequest.firstDescriptor_p)

/* Currently when we want to force DTC to ignore specific descriptor we will set:
 * Source address(+offset) = Destination address(+offset), no ciphering, no CK.
 * We do it when we don't want to remove the descriptor from the chain.
 * Future version of DTC will allow us to use specific descriptor field to do it. */

#if defined (DATA_IN_SIGNAL) && defined(ON_PC)
#define BITLENGTH_ZERO 0
#else
#define BITLENGTH_ZERO 8
#endif


#define UDTC_IF_IGNORE_F8_DESCRIPTOR(F8Desc_p)                 \
   {if (F8Desc_p->srcData_p == PNULL)                          \
    {                                                          \
        if (F8Desc_p->dstData_p == PNULL)                      \
        {                                                      \
            F8Desc_p->srcData_p = (Int8 *)&dtcDummyCipherBuff; \
            F8Desc_p->dstData_p = (Int8 *)&dtcDummyCipherBuff; \
            F8Desc_p->srcBitOffset = 0;                        \
            F8Desc_p->dstBitOffset = 0;                        \
            F8Desc_p->bitLength    = 8;                        \
        }                                                      \
        else                                                   \
        {                                                      \
            F8Desc_p->srcData_p    = F8Desc_p->dstData_p;      \
            F8Desc_p->srcBitOffset = F8Desc_p->dstBitOffset;   \
        }                                                      \
    }                                                          \
    else                                                       \
    {                                                          \
        F8Desc_p->dstData_p    = F8Desc_p->srcData_p;          \
        F8Desc_p->dstBitOffset = F8Desc_p->srcBitOffset;       \
    }                                                          \
    F8Desc_p->key_p = PNULL;                                   \
    UDTC_IF_DISABLE_CIPHERING(F8Desc_p->dtcModeReg);           \
    if (F8Desc_p->bitLength == 0)                              \
        F8Desc_p->bitLength = BITLENGTH_ZERO;                               \
   }

#define UDTC_IF_IS_F8_DESCRIPTOR_IGNORED(F8Desc_p)        \
    ((F8Desc_p->dstData_p    == F8Desc_p->srcData_p)    &&\
     (F8Desc_p->dstBitOffset == F8Desc_p->srcBitOffset) &&\
     (F8Desc_p->key_p        == PNULL))                  /*&&\
     (UDTC_IF_IS_CIPHERING_DISABLED(F8Desc_p->dtcModeReg)))*/

/* F8 Cipher Offset - indicates the number of bits that should be passed from
 * source to dest but do not pass ciphering. The following values are allowed:
 * Ciphering offset(bits)      Offset ID
 *  0                           000
 *  4                           001
 *  8                           010
 *  12                          011
 *  16                          100
 *  20                          101
 *  Reserved                    110&111 */
#define UDTC_IF_SET_CIPHER_OFFSET_ID(modReg, len)\
    {modReg &= UDTC_IF_CIPHER_OFFSET_CLEAR_MASK;\
     modReg |= (((len>0)&&(len<=UDTC_IF_MAX_F8_CIPHER_OFFSET))?(Int8)(len>>2):0);}

#define UDTC_IF_GET_CIPHER_OFFSET_BITS(modReg)\
    (Int8)((modReg & UDTC_IF_CIPHER_OFFSET_BITS_ON)<<2)

/* F8 Header Size - indicates the size in bits of the header data
 * The following values are allowed:
 * Header size(bits)          Header size ID
 *  0                           0000
 *  4                           0001
 *  6                           0010
 *  8                           0011
 *  12                          0100
 *  14                          0101
 *  16                          0110
 *  20                          0111
 *  22                          1000
 *  Reserved                    1001-1111 */
#define UDTC_IF_GET_HEADER_SIZE_BITLEN(hdrReg)\
dtcHdrSizeByIdTable_g[(hdrReg&UDTC_IF_HEADER_SIZE_ID_MASK)>>UDTC_IF_HEADER_SIZE_ID_OFFSET]

#define UDTC_IF_SET_HEADER_SIZE_ID(hdrReg, len)\
    {(hdrReg) &= UDTC_IF_HEADER_SIZE_ID_CLEAR_MASK;\
     if((len)<=UDTC_IF_MAX_HEADER_SIZE_BITLEN) \
        (hdrReg) |=dtcHdrIdBySizeTable_g[(len)];}

#define UDTC_IF_SET_HEADER(hdrReg, len, header)\
{\
    Int32 hdrId = (hdrReg)&UDTC_IF_HEADER_SIZE_ID_MASK;\
    Int32 length=(len); \
    if (hdrId)\
    {   /* Header already exist */\
        Int32 hdrMask, newHdrReg = 0;\
        hdrId = hdrId>>UDTC_IF_HEADER_SIZE_ID_OFFSET;\
        hdrMask = dtcHdrMaskByIdTable_g[hdrId];\
        newHdrReg = (hdrReg) & hdrMask; /* Pull out current header          */\
        newHdrReg = newHdrReg << len; /* Move current header to the left    */\
        newHdrReg |= (Int32)header;   /* Add new header at the right side   */\
        (hdrReg) &= UDTC_IF_HEADER_SIZE_ID_MASK;    /* Clear current header */\
        (hdrReg) |= newHdrReg;                      /* Save new headers     */\
        length += dtcHdrSizeByIdTable_g[hdrId];\
    }\
    else\
    {   /* New header */\
        (hdrReg) |= (Int32)header;\
    }\
    UDTC_IF_SET_HEADER_SIZE_ID((hdrReg), length);  /* Set header size       */\
}


/* Macro to convert quickly from ASN.1 cipher algorithm to dtc driver cipher algorithm */
#define UDTC_IF_CIPHER_ALG_CONVERT_ASN1_TO_DTC_DRIVER(asn1Alg)\
	dtcCipherAlgorithmTable_g[asn1Alg-1]


#define UDTC_IF_EXTRACT_HEADER_DATA(hdrReg, len)      (hdrReg & (~(0xFFFFFFFF<<len)))
#define UDTC_IF_TURN_ON_LAST_HEADER_DATA_BIT(hdrReg)  (hdrReg |= 0x1)
#define UDTC_IF_TURN_OFF_LAST_HEADER_DATA_BIT(hdrReg) (hdrReg &= 0xFFFFFFFE)

#define UDTC_IF_SET_RLC_DEBUG_MARK_F8_DESC(F8Desc_p)    \
    ((F8Desc_p)->headerReg |= UDTC_IF_HEADER_RLC_DEBUG_MARK_MASK)
#define UDTC_IF_CLEAR_RLC_DEBUG_MARK_F8_DESC(F8Desc_p)  \
    ((F8Desc_p)->headerReg &= UDTC_IF_HEADER_RLC_DEBUG_MARK_CLEAR_MASK)
#define UDTC_IF_GET_RLC_DEBUG_MARK_F8_DESC(F8Desc_p)    \
    (((F8Desc_p)->headerReg >> UDTC_IF_RLC_DEBUG_MARK_OFFSET) & UDTC_IF_RLC_DEBUG_MARK_BITLEN)

/* Alignment macros: 256 bits descriptor alignment, 256 bits key alignment  */
#if defined (ON_PC)
#define UDTC_IF_DESCRIPTOR_ALIGN
#define UDTC_IF_KEY_ALIGN
#else
#define UDTC_IF_DESCRIPTOR_ALIGN    __align(UDTC_IF_DESCRIPTOR_BYTES_ALIGNMENT)
#define UDTC_IF_KEY_ALIGN           __align(UDTC_IF_KEY_BYTES_ALIGNMENT)
#endif /* ON_PC */

/* Request id is 32 bits which set as follows:
 * --------------------------------------------------------------------------
 * |     Descriptor type     |  Debug   | RLC Debug  |   User   | sequence  |
 * | See UdtcDescriptorsType | Reserved | Indication |   data   |  number   |
 * |        (4 bits)         | (3 bits) |  (1 bit)   | (8 bits) | (16 bits) |
 * --------------------------------------------------------------------------*/
#define UDTC_IF_GET_FIRST_REQ_ID(descType)      ((Int32)(descType) << 28)
/* Use 15 bits for sequence number. the 16th bit from the right is used
 * as separator between the sequence number and other request id info */
#define UDTC_IF_REQ_ID_INCREMENT(reqId)         (reqId = (((reqId)+1) & 0xFFFF7FFF))
/* Set user data up to user data length after clearing previous value */
#define UDTC_IF_SET_REQ_USER_DATA(reqId, userData)\
    {(reqId &= 0xFF00FFFF); (reqId |= (((userData) << 16) & 0xFF0000));}
#define UDTC_IF_GET_REQ_USER_DATA(reqId)        (((reqId) >> 16) & 0xFF)
#define UDTC_IF_GET_REQ_DESC_TYPE(reqId)        (((reqId) >> 28) & 0xF)
#define UDTC_IF_RET_REQ_RLC_DEBUG_IND(reqId)        (reqId | 0x01000000)

/******************************************************************************
 * Type Definitions
 *****************************************************************************/
typedef enum UdtcDescriptorsTypeTag
{
    UDTC_IF_F9_DESCRIPTOR,
    UDTC_IF_BLOCKING_F8_DESCRIPTOR,
    UDTC_IF_R99_DL_DESCRIPTORS,
    UDTC_IF_R99_UL_DESCRIPTORS,
    UDTC_IF_HSDPA_DESCRIPTORS,
    UDTC_IF_HSDPA_DEBUG_DESCRIPTORS,
    UDTC_IF_EDCH_DESCRIPTORS,
    UDTC_IF_EDCH_DEBUG_DESCRIPTORS,
    UDTC_IF_EHSDPA_DESCRIPTORS
#ifdef UMTS7_PRE_R8
    ,
    UDTC_IF_DLBGDL_DESCRIPTORS
#endif    
}
UdtcDescriptorsType;

typedef struct UdtcRequestInfoTag
{
    dtcF8DescriptorInfo     *chainLastDesc_p; /* Last chain descriptor */
    dtcF8DescriptorInfo     *chainNextDesc_p; /* Descriptor to point after DTC ending */
    dtcTransferReq_ts       dtcRequest;
}
UdtcRequestInfo;


/******************************************************************************
 * Global Variables
 *****************************************************************************/
extern Int32                dtcHdrIdBySizeTable_g[];
extern Int32                dtcHdrSizeByIdTable_g[];
extern Int32                dtcHdrMaskByIdTable_g[];
extern dtcF8DescriptorInfo  dtcDescriptorsTable_g[];
extern Int32                dtcDummyCipherBuff;
extern cipherType_te 		dtcCipherAlgorithmTable_g[];


/******************************************************************************
 * Function Prototypes
 *****************************************************************************/

#if !defined (DATA_IN_SIGNAL) || defined (UPGRADE_3G_EDCH)
/******************************************************************************
 *
 * Function : UdtcF8ChainAlloc
 *
 * Scope        : Global
 *
 * Parameters   :
 *  in:     descriptorsType - Request/Channel type the descriptors are used for.
 *                            Used to determine the needed descriptors number and
 *                            where to allocate the descriptors from.
 *
 * Returns      : pointer to the chain head.
 *
 * Description  : Build dynamic descriptors linked chain.
 *
 *****************************************************************************/
extern dtcF8DescriptorInfo  *UdtcF8ChainAlloc(UdtcDescriptorsType descriptorsType);

#endif /* !DATA_IN_SIGNAL || UPGRADE_3G_EDCH */


/******************************************************************************
 *
 * Function     : UdtcBlockingF8
 *
 * Scope        : Global
 *
 * Parameters   :
 *  in: key_p       - Pointer to the cipher key
 *  i/o:srcData_p   - Pointer to data to be ciphered/deciphered
 *  in: bearer      - Bearer identity
 *  in: cipherOffset- The number of bits that should be passed from source to
 *                      destination but do not pass ciphering.
 *  in: bitLength   - Number of bits to be ciphered/deciphered
 *  in: srcBitOffset- Bit offset from start of data to first bit of data to be
 *                    ciphered/deciphered
 *  in: countC      - COUNT-C
 *  in: direction   - Direction (uplink or downlink)
 *  in:  cipherAlgorithm  - dtc ciphering algorithm (Kasumi/Snow 3G)
 *
 * Returns      : None
 *
 * Description  :
 *  Handles f8 confidentiality algorithm requests for single descriptor.
 *  Using in-situ mode where the source address and destination address are the same.
 *  The DTC reads the data, ciphers it and writes the ciphered data back to the
 *  same location where it came from.
 *  In this mode, the following restrictions apply:
 *      - Header insertion feature is not allowed.
 *      - IBO = OBO (cannot be different).
 *      - CONT_MODE is not allowed.
 *      - Note that the DTC will overwrite up to the last byte at the destination
 *        (like in all other cases).
 *  Done in blocking mode.
 *
 *****************************************************************************/
extern void UdtcBlockingF8(
        Int8                *key_p,
        Int8                *srcData_p,
        Int8                *dstData_p, /********** - add*/
        Int8                bearer,
        Int8                cipherOffset,
        Int16               bitLength,
        Int16               srcBitOffset,
        Int16               dstBitOffset, /********** - add*/
        Int32               countC,
        DtcDirection        direction,
        cipherType_te		cipherAlgorithm);


/******************************************************************************
 *
 * Function : UdtcF8Chain
 *
 * Scope        : Global
 *
 * Parameters   :
 *   in: dtcRequest - pointer to the DTC request structure
 *
 * Returns      : None
 *
 * Description  : A chained version of f8, which copies and performs f8
 *                on a set of blocks passed to UdtcF8Chain as a list.
 *
 *****************************************************************************/
extern void UdtcF8Chain(dtcTransferReq_ts *dtcRequest);


/******************************************************************************
 *
 * Function : UdtcBlockingF9
 *
 * Scope        : Global
 *
 * Parameters   :
 *  in: key_p       - Pointer to the cipher key
 *  in: countI      - COUNT-I
 *  in: fresh       - A 32-bit random number input
 *  in: direction   - Direction (uplink or downlink)
 *  in: data_p      - Pointer to data to be ciphered/deciphered
 *  in: bitLength   - Number of bits to be ciphered/deciphered
 *
 * Returns      : Message authentication code.
 *
 * Description  : Handles f9 integrity algorithm requests.
 *
 *****************************************************************************/
extern Int32 UdtcBlockingF9(
        Int8            *key_p,
        Int32           countI,
        Int32           fresh,
        DtcDirection    direction,
        Int8            *data_p,
        Int32           bitLength,
        cipherType_te	cipherAlgorithm);

#endif /* UDTCIF_H */


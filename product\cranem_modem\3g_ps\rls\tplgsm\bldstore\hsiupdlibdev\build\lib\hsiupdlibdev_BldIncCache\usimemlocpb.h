/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/3g.mod/lib/src/usimemlocpb.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/26 20:17:00 $
 **************************************************************************
 *  File Description :
 *
 *  File definition for the UICC emulator (local phonebook)
 **************************************************************************/

#if !defined (USIMEMLOCPB_H)
#define USIMEMLOCPB_H

/***************************************************************************
 * Nested Include Files
 **************************************************************************/

#if !defined (ALSI_SIG_H)
#include "alsi_sig.h"
#endif

#if !defined (SIMDATA_H)
#include "simdata.h"
#endif

#if !defined (USIMEMU_H)
#include "usimemu.h"
#endif

/***************************************************************************
 * Manifest Constants
 **************************************************************************/
/*Note: The local phonebook includes type 2 file linking */

/*-----------------22/07/2003 10:56-----------------
 * Number of records for each file defined in
 * the local phonebook
 * --------------------------------------------------*/
#define SIM_EMU_NUM_USIM_PBR_RECS      2
#define SIM_EMU_NUM_USIM_ADN_RECS      10
#define SIM_EMU_NUM_USIM_ANRA_RECS     5
#define SIM_EMU_NUM_USIM_ANRB_RECS     5
#define SIM_EMU_NUM_USIM_ANRC_RECS     5
#define SIM_EMU_NUM_USIM_SNE_RECS      5
#define SIM_EMU_NUM_USIM_EMAIL_RECS    5
#define SIM_EMU_NUM_USIM_UID_RECS      SIM_EMU_NUM_USIM_ADN_RECS
#define SIM_EMU_NUM_USIM_GRP_RECS      SIM_EMU_NUM_USIM_ADN_RECS
#define SIM_EMU_NUM_USIM_PBC_RECS      SIM_EMU_NUM_USIM_ADN_RECS
#define SIM_EMU_NUM_USIM_AAS_RECS       5
#define SIM_EMU_NUM_USIM_IAP_RECS      SIM_EMU_NUM_USIM_ADN_RECS
#define SIM_EMU_NUM_USIM_ADN1_RECS     11
#define SIM_EMU_NUM_USIM_SNE1_RECS     5
#define SIM_EMU_NUM_USIM_ANRA1_RECS    5
#define SIM_EMU_NUM_USIM_ANRB1_RECS    5
#define SIM_EMU_NUM_USIM_ANRC1_RECS    5
#define SIM_EMU_NUM_USIM_EMAIL1_RECS   5
#define SIM_EMU_NUM_USIM_UID1_RECS     SIM_EMU_NUM_USIM_ADN1_RECS
#define SIM_EMU_NUM_USIM_GRP1_RECS     SIM_EMU_NUM_USIM_ADN1_RECS
#define SIM_EMU_NUM_USIM_PBC1_RECS     SIM_EMU_NUM_USIM_ADN1_RECS
#define SIM_EMU_NUM_USIM_IAP1_RECS     SIM_EMU_NUM_USIM_ADN1_RECS
#define SIM_EMU_NUM_USIM_CCP1_RECS     4
#define SIM_EMU_NUM_USIM_GAS_RECS      7
#define SIM_EMU_NUM_USIM_EXT1_RECS     12

/*-----------------22/07/2003 10:57-----------------
 * Length of the records/files defined in the local
 * phonebook
 * --------------------------------------------------*/
#define SIM_EMU_SIZE_USIM_EXT1_FILE        13
#define SIM_EMU_SIZE_USIM_PBR_FILE         64
#define SIM_EMU_SIZE_USIM_ANRA_FILE        17
#define SIM_EMU_SIZE_USIM_ANRB_FILE        17
#define SIM_EMU_SIZE_USIM_ANRC_FILE        17
#define SIM_EMU_SIZE_USIM_ANRA1_FILE       17
#define SIM_EMU_SIZE_USIM_ANRB1_FILE       17
#define SIM_EMU_SIZE_USIM_ANRC1_FILE       17
#define SIM_EMU_SIZE_USIM_EMAIL_FILE       17
#define SIM_EMU_SIZE_USIM_EMAIL1_FILE      17
#define SIM_EMU_SIZE_USIM_SNE_FILE         12
#define SIM_EMU_SIZE_USIM_SNE1_FILE        12
#define SIM_EMU_SIZE_USIM_ADN_FILE         46
#define SIM_EMU_SIZE_USIM_IAP_FILE         5
#define SIM_EMU_SIZE_USIM_IAP1_FILE         5
#define SIM_EMU_SIZE_USIM_ADN1_FILE        46
#define SIM_EMU_SIZE_USIM_PBC_FILE         2
#define SIM_EMU_SIZE_USIM_PBC1_FILE        2

#define SIM_EMU_SIZE_USIM_CCP1_FILE        14
#define SIM_EMU_SIZE_USIM_GRP_FILE         3
#define SIM_EMU_SIZE_USIM_GRP1_FILE        3

#define SIM_EMU_SIZE_USIM_PSC_FILE         4
#define SIM_EMU_SIZE_USIM_CC_FILE          2
#define SIM_EMU_SIZE_USIM_PUID_FILE        2
#define SIM_EMU_SIZE_USIM_UID_FILE         2
#define SIM_EMU_SIZE_USIM_UID1_FILE         2
#define SIM_EMU_SIZE_USIM_GAS_FILE         15
#define SIM_EMU_SIZE_USIM_AAS_FILE         12


/***************************************************************************
 * Typed Constants
 **************************************************************************/



/*CQ00135656, Cgliu, 2022-02-25,Begin*/
#if defined (ON_PC)

/*-----------------27/03/02 09:28-------------------------------------------
 * Phone book reference: This phonebook has got type 2 files:
 * ANRA, ANRB, ANRC , SNE and EMAIL are type 2
 * See 131 102 section 4.4.2.1
 * -------------------------------------------------------------------------*/
Int8    defPbrUsimData[] = {
                             /*record number 1*/

                             0xa8, 0x16,                    /*type 1 files*/
                             0xc0, 0x03, 0x4f, 0x3a, 0x01,  /*ADN*/
                             0xc5, 0x03, 0x4f, 0x09, 0x02,  /*PBC*/
                             0xc6, 0x02, 0x4f, 0x83,        /*GRP*/
                             0xc9, 0x02, 0x4f, 0x81,        /*UID*/
                             0xc1, 0x02, 0x4f, 0x97,        /*IAP*/

                             0xa9, 0x14,                    /*type 2 files */
                             0xc4, 0x02, 0x4f, 0x11,        /*ANR*/
                             0xc4, 0x02, 0x4f, 0x13,        /*ANR*/
                             0xc4, 0x02, 0x4f, 0x15,        /*ANR*/
                             0xc3, 0x02, 0x4f, 0x19,        /*SNE*/
                             0xca, 0x02, 0x4f, 0x50,        /*EMAIL*/

                             0xaa, 0x0c,                    /*type 3 files */
                             0xc2, 0x02, 0x4f, 0x4a,        /*EXT1*/
                             0xc7, 0x02, 0x4f, 0x4b,        /*AAS*/
                             0xc8, 0x02, 0x4f, 0x4c,
                             0xcb, 0x02, 0x4f, 0x99,        /*CCP1*/

                             /*record number 2*/
                             0xa8, 0x10,                    /*type 1 files*/
                             0xc0, 0x02, 0x4f, 0x3b,        /*ADN*/
                             0xc5, 0x02, 0x4f, 0x0a,        /*PBC*/
                             0xc6, 0x02, 0x4f, 0x84,        /*GRP*/
                             0xc9, 0x02, 0x4f, 0x82,        /*UID*/
                             0xc1, 0x02, 0x4f, 0x98,        /*IAP1*/

                             0xa9, 0x14,                    /*type 2 files*/
                             0xc4, 0x02, 0x4f, 0x12,        /*ANR*/
                             0xc4, 0x02, 0x4f, 0x14,        /*ANR*/
                             0xc4, 0x02, 0x4f, 0x16,        /*ANR*/
                             0xc3, 0x02, 0x4f, 0x1a,        /*SNE*/
                             0xca, 0x02, 0x4f, 0x51,        /*EMAIL*/

                             0xaa, 0x0c,                     /*type 3 files*/
                             0xc2, 0x02, 0x4f, 0x4a,
                             0xc7, 0x02, 0x4f, 0x4b,
                             0xc8, 0x02, 0x4f, 0x4c,
                             0xcb, 0x02, 0x4f, 0x99,         /*CCP1*/
                             0xff, 0xff

                             };



SimEfData    defPbrUsimEfData =
{
    SIM_EMU_EF_PBR,                 /* Id           */
    SIM_EFSTRUCT_LF,              /* Type         */
    SIM_DIR_DF_PHONEBOOK,        /* Dir          */
    SIM_EMU_NUM_USIM_PBR_RECS,   /* Num Recs     */
    SIM_EMU_SIZE_USIM_PBR_FILE,  /* Rec Len     */
    (0),                         /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_ADM,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};




/*-----------------05/04/02 16:13-------------------
 * EF PBC: phonebook control   (file listed in PBR record 1)
 * see 131 102 section 4.4.2.5 for coding
 * first byte: entry control info,
 * second byte: flag to indicate whether entry is hidden
 * --------------------------------------------------*/
Int8    defPbcUsimData[] = { 0x00, 0x01,
                             0x00, 0x00,
                             0x00, 0x00,
                             0x00, 0x00,
                             0x00, 0x01,
                             0x00, 0x00,
                             0x00, 0x00,
                             0x00, 0x00,
                             0x00, 0x00,
                             0x00, 0x00   };

SimEfData    defPbcUsimEfData =
{

    SIM_EMU_EF_PBC,            /* Id           */
    SIM_EFSTRUCT_LF,        /* Type         */
    SIM_DIR_DF_PHONEBOOK,     /* Dir          */
    SIM_EMU_NUM_USIM_PBC_RECS, /* Num Rec      */
    SIM_EMU_SIZE_USIM_PBC_FILE, /*  Rec  len   */
  (0),                    /* curr Rec     */
  (0),
  {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
   },
   SIM_UICC_ACTIVATED_STATE, /*file State */
   FALSE,                    /*sfi supported  */
   0,                        /*sfi */
   TRUE                      /*Under USIM */
};

/*-----------------05/04/02 16:13-----------------------------------
 * EF PBC 1: phonebook control   (file listed in PBR record 2)
 * -----------------------------------------------------------------*/
Int8    defPbc1UsimData[] = { 0x00, 0x03,
                             0x00, 0x00,
                             0x00, 0x03,
                             0x00, 0x02,
                             0x00, 0x03,
                             0x00, 0x00,
                             0x00, 0x00,
                             0x00, 0x00,
                             0x00, 0x00,
                             0x00, 0x00,
                             0x00, 0x00   };

SimEfData    defPbc1UsimEfData =
{

    SIM_EMU_EF_PBC1,            /* Id           */
    SIM_EFSTRUCT_LF,        /* Type         */
    SIM_DIR_DF_PHONEBOOK,     /* Dir          */
    SIM_EMU_NUM_USIM_PBC1_RECS, /* Num Rec      */
    SIM_EMU_SIZE_USIM_PBC1_FILE, /*  Rec  len   */
  (0),                    /* curr Rec     */
  (0),
  {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
   },
   SIM_UICC_ACTIVATED_STATE, /*file State */
   FALSE,                    /*sfi supported  */
   0,                        /*sfi */
   TRUE                      /*Under USIM */
};


/*-----------------05/04/02 16:13-------------------
 * EF EXT1: used for ADN and ANR (additional numbers)
 * --------------------------------------------------*/
Int8    defExt1UsimData[] = {
/*1*/   0x01,
        0x00, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*2*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*3*/   0x02,
        0x0a, 0x77, 0x11, 0x77, 0x11, 0x77, 0x11, 0x77, 0x11, 0x77, 0x11,
        0x04,

/*4*/   0x02,
        0x04, 0x11, 0x22, 0x33, 0x44, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0x06,

/*5*/   0x02,
        0x04, 0x22, 0x22, 0x22, 0x22, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*6*/   0x01,
        0x0a, 0x81, 0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99,
        0x01,

/*7*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*8*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*9*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*a*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*b*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*c*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff
};

SimEfData    defExt1UsimEfData =
{

    SIM_EMU_EF_EXT1,            /* Id           */
    SIM_EFSTRUCT_LF,        /* Type         */
    SIM_DIR_DF_PHONEBOOK,     /* Dir          */
    SIM_EMU_NUM_USIM_EXT1_RECS, /* Num Rec      */
    SIM_EMU_SIZE_USIM_EXT1_FILE, /*  Rec  len   */
  (0),                    /* curr Rec     */
  (0),
  {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
   },
   SIM_UICC_ACTIVATED_STATE, /*file State */
   TRUE,                     /*sfi supported  */
   0x4a,                     /*sfi */
   TRUE                      /*Under USIM */
};


/*-----------------05/04/02 16:13-------------------
 * EF CCP1 (capability configuration parameters)
 * 131 102 section 4.4.2.11
 * --------------------------------------------------*/
Int8    defCcp1UsimData[] = {
   /* 0x07, 0xc2, 0xc8, 0x81, 0x21, 0x13, 0x63, 0xa3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,   */
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
  /*  0x01, 0xa0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,*/
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x07, 0xc2, 0xc8, 0x81, 0x21, 0x13, 0x63, 0xa3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x07, 0xc2, 0xc8, 0x81, 0x21, 0x13, 0x63, 0xa3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff };

SimEfData    defCcp1UsimEfData =
{

    SIM_EMU_EF_CCP1,            /* Id           */
    SIM_EFSTRUCT_LF,        /* Type         */
    SIM_DIR_DF_PHONEBOOK,     /* Dir          */
    SIM_EMU_NUM_USIM_CCP1_RECS, /* Num Rec      */
    SIM_EMU_SIZE_USIM_CCP1_FILE, /*  Rec  len   */
  (0),                    /* curr Rec     */
  (0),
  {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
   },
   SIM_UICC_ACTIVATED_STATE, /*file State */
   TRUE,                     /*sfi supported  */
   0x4a,                     /*sfi */
   TRUE                      /*Under USIM */
};


 /*-----------------27/03/02 09:25-------------------
 * EF IAP only used in phonebook example 2
 *
 * --------------------------------------------------*/

Int8    defIapUsimData[] =
{
   0x04,   /*ANRA*/
   0x05,   /*ANRB*/
   0x05,   /*ANRC*/
   0x01,  /*SNE*/
   0x01,  /*EMAIL*/

   0x05,  /*ANRA*/
   0x03,  /*ANRB*/
   0x03,  /*ANRC*/
   0x02,  /*SNE*/
   0x02,  /*EMAIL*/


   0x03,  /*ANRA*/
   0x04,  /*ANRB*/
   0x04,  /*ANRC*/
   0x03,  /*SNE*/
   0xff,  /*EMAIL*/


   0x02,  /*ANRA*/
   0x02,  /*ANRB*/
   0x02,  /*ANRC*/
   0x04,  /*SNE*/
   0xff,  /*EMAIL*/


   0x01,   /*ANRA*/
   0x01,   /*ANRB*/
   0x01,   /*ANRC*/
   0x05,  /*SNE*/
   0xff,  /*EMAIL*/

   0xff,   /*ANRA*/
   0xff,   /*ANRB*/
   0xff,   /*ANRC*/
   0xff,  /*SNE*/
   0xff,  /*EMAIL*/


   0xff,   /*ANRA*/
   0xff,  /*ANRB*/
   0xff,   /*ANRC*/
   0xff,  /*SNE*/
   0xff,  /*EMAIL*/

   0xff,   /*ANRA*/
   0xff,   /*ANRB*/
   0xff,   /*ANRC*/
   0xff,  /*SNE*/
   0x03,  /*EMAIL*/

   0xff,   /*ANRA*/
   0xff,   /*ANRB*/
   0xff,   /*ANRC*/
   0xff,  /*SNE*/
   0xff,  /*EMAIL*/

   0xff,   /*ANRA*/
   0xff,   /*ANRB*/
   0xff,   /*ANRC*/
   0xff,  /*SNE*/
   0xff   /*EMAIL*/
};

 SimEfData    defIapUsimEfData =
{
    SIM_EMU_EF_IAP,             /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_USIM_IAP_RECS,  /* num Rec      */
    SIM_EMU_SIZE_USIM_IAP_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                     /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};


 /*------------------------------------------------------------------------------------
 * IAP   (this file is listed in EF PBR record 2)
 * -----------------------------------------------------------------------------------*/

Int8    defIap1UsimData[] =
{
   0x04,   /*ANRA*/
   0x05,   /*ANRB*/
   0x05,   /*ANRC*/
   0x01,  /*SNE*/
   0xff,  /*EMAIL*/

   0x05,  /*ANRA*/
   0x03,  /*ANRB*/
   0x02,  /*ANRC*/
   0x02,  /*SNE*/
   0xff,  /*EMAIL*/


   0x03,  /*ANRA*/
   0x04,  /*ANRB*/
   0x04,  /*ANRC*/
   0x03,  /*SNE*/
   0xff,  /*EMAIL*/


   0x02,  /*ANRA*/
   0x02,  /*ANRB*/
   0x02,  /*ANRC*/
   0x04,  /*SNE*/
   0xff,  /*EMAIL*/


   0x01,   /*ANRA*/
   0x01,   /*ANRB*/
   0x01,   /*ANRC*/
   0x05,  /*SNE*/
   0xff,  /*EMAIL*/

   0xff,   /*ANRA*/
   0xff,   /*ANRB*/
   0xff,   /*ANRC*/
   0xff,  /*SNE*/
   0xff,  /*EMAIL*/


   0xff,   /*ANRA*/
   0xff,  /*ANRB*/
   0xff,   /*ANRC*/
   0xff,  /*SNE*/
   0xff,  /*EMAIL*/

   0xff,   /*ANRA*/
   0xff,   /*ANRB*/
   0xff,   /*ANRC*/
   0xff,  /*SNE*/
   0xff,  /*EMAIL*/

   0xff,   /*ANRA*/
   0xff,   /*ANRB*/
   0xff,   /*ANRC*/
   0xff,  /*SNE*/
   0xff,  /*EMAIL*/

   0xff,   /*ANRA*/
   0xff,   /*ANRB*/
   0xff,   /*ANRC*/
   0xff,  /*SNE*/
   0xff,   /*EMAIL*/

   0xff,   /*ANRA*/
   0xff,   /*ANRB*/
   0xff,   /*ANRC*/
   0xff,   /*SNE*/
   0xff   /*EMAIL*/

};

 SimEfData    defIap1UsimEfData =
{
    SIM_EMU_EF_IAP1,             /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_USIM_IAP1_RECS,  /* num Rec      */
    SIM_EMU_SIZE_USIM_IAP1_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                     /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};




/*-----------------27/03/02 09:25-------------------
 * EF ADN Abreviated dial numbers
 *
 * --------------------------------------------------*/

Int8    defAdnUsimData[] =
{
        'E' , 'L', 'A', 'I', 'N', 'E', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0x03,0x81,0x21,0xf3,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,

        'C' , 'A', 'R', 'O', 'L', 'I','N','E',0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x06,0xa8,0x07,0x63,0x26,
        0x26,0x26,0xff,0xff,0xff,0xff,0xff,0xff,0xff,

        'M' , 'A', 'C', 'B', 'E', 'A', 'T', 'H', 0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x08,0x98,0x01,0x01,0x20,
        0x44,0x88,0x83,0x36,0xff,0xff,0xff,0xff,0xff,

        'J' , 'A', 'M', 'E', 'S', ' ', 'B', 'O', 'N', 'D',0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x08,0x98,0x01,0x01,0x20,
        0x44,0x88,0x83,0x36,0xff,0xff,0xff,0xff,0xff,

        'A' , 'L', 'E', 'K', 'S', 'A', 'N', 'D', 'R', 'A',0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x08,0x98,0x01,0x01,0x20,
        0x44,0x88,0x83,0x36,0xff,0xff,0xff,0xff,0xff,

        'S' , 'U', 'E', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x08,0x98,0x01,0x01,0x20,
        0x44,0x88,0x83,0x36,0xff,0xff,0xff,0xff,0xff,

        'S' , 'T', 'E', 'V', 'E', 0xff, 0xff, 0xff, 0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x04,0x81,0x11,0x11,0x11,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,

        'R' , 'I', 'T', 'E', 'S', 'H', 0xff, 0xff, 0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x08,0x98,0x01,0x01,0x20,
        0x44,0x88,0x83,0x36,0xff,0xff,0xff,0xff,0xff,

       'C' , 'A', 'L', 'L', 'U', 'M', 0xff, 0xff, 0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x08,0x98,0x01,0x01,0x20,
        0x44,0x88,0x83,0x36,0xff,0xff,0xff,0xff,0xff,

        'P' , 'A', 'U', 'L', 0xff, 0xff, 0xff, 0xff, 0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0x08,0x98,0x01,0x01,0x20,
        0x44,0x88,0x83,0x36,0xff,0xff,0xff,0xff,0xff,
};

SimEfData    defAdnUsimEfData =
{
    SIM_EMU_EF_ADN,             /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_USIM_ADN_RECS,  /* num Rec      */
    SIM_EMU_SIZE_USIM_ADN_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    TRUE,                     /*sfi supported  */
    0x01,                     /*sfi */
    TRUE                      /*Under USIM */
};

/*------------------------------------------------------
 *
 * ADN1 Abreviated dial numbers   (11 records)
 *
 * --------------------------------------------------*/

Int8    defAdn1UsimData[] =
{
        'A' , 'D', 'N', '1', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0x0b,0x81,0x21,0x43,0x65,
        0x87,0x09,0x21,0x43,0x65,0x87,0x90,0xff,0x03,

         'A' , 'D', 'N', '2', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0x05,0x81,0x21,0x43,0x65,
        0x87,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,

         'A' , 'D', 'N', '3', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
         0x04,0x81,0x33,0x33,0x65,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,

         'A' , 'D', 'N', '4', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0x03,0x81,0x21,0xf3,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,

         'A' , 'D', 'N', '5', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0x03,0x81,0x54,0x54,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,

       'A' , 'D', 'N', '6', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0x03,0x81,0x22,0x22,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,

        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,

        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,

        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,

        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,

        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,
        0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff
};

SimEfData    defAdn1UsimEfData =
{
    SIM_EMU_EF_ADN1,            /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_USIM_ADN1_RECS,  /* num Rec      */
    SIM_EMU_SIZE_USIM_ADN1_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};



/*-----------------27/03/02 09:25-------------------
 * EF SNE second name entry
 * --------------------------------------------------*/

Int8    defSneUsimData[] =
{
        'S' , 'N', 'E', '1', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
         0x01, 0x01,

        'S' , 'N', 'E', '2', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
         0x01, 0x02,

        'S' , 'N', 'E', '3', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
         0x01, 0x03,

        'S' , 'N', 'E', '4', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
         0x01, 0x04,

        'S' , 'N', 'E', '5', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
         0x01, 0x05
};

SimEfData    defSneUsimEfData =
{
    SIM_EMU_EF_SNE,             /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_USIM_SNE_RECS,  /* num Rec      */
    SIM_EMU_SIZE_USIM_SNE_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                     /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};


/*-----------------27/03/02 09:25---------------------------
 * SNE1 second name entry  (listed in PBR record number 2)
 * ---------------------------------------------------------*/
Int8    defSne1UsimData[] =
{
        'S' , 'N', 'E', 'a', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
         0xff, 0x01,

        'S' , 'N', 'E', 'b', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
         0xff, 0x02,

        'S' , 'N', 'E', 'c', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
         0xff, 0x03,

        'S' , 'N', 'E', 'd', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
         0xff, 0x04,

        'S' , 'N', 'E', 'e', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
         0xff, 0x05

};

SimEfData    defSne1UsimEfData =
{
    SIM_EMU_EF_SNE1,             /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_USIM_SNE1_RECS,  /* num Rec      */
    SIM_EMU_SIZE_USIM_SNE1_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};

/*-----------------18/07/2003 09:37-----------------
 *
 * EF AAS: Additional Alpha String
 *
 * --------------------------------------------------*/

Int8    defAasUsimData[] =
{
        'W' , 'O', 'R', 'K', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
         0xff, 0xff,

        'H' , 'O', 'M', 'E', 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
         0xff, 0xff,

        'M' , 'O', 'B', 'I', 'L', 'E', 0xff, 0xff, 0xff, 0xff,
         0xff, 0xff,

         0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
         0xff, 0xff,

         0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
         0xff, 0xff
};

SimEfData    defAasUsimEfData =
{
    SIM_EMU_EF_AAS,             /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_USIM_AAS_RECS,  /* num Rec      */
    SIM_EMU_SIZE_USIM_AAS_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                     /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};


/*-----------------27/03/02 09:25-------------------
 * EF GRP (grouping file)
 *  section 4.4.2.6
 * --------------------------------------------------*/

Int8    defGrpUsimData[] =
{
        0x01, 0x03, 0x00,   /* belongs to group 1 and 3*/
        0x02, 0x01, 0x03,   /* belongs to group 2, 1 and 3*/
        0x03, 0x00, 0x00,
        0x04, 0x01, 0x03,
        0x05, 0x02, 0x03,
        0x06, 0x00, 0x00,
        0x00, 0x00, 0x00,
        0x00, 0x00, 0x00,
        0x00, 0x00, 0x00,
        0x01, 0x02, 0x00
};

SimEfData    defGrpUsimEfData =
{
    SIM_EMU_EF_GRP,             /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_USIM_GRP_RECS,  /* num Rec      */
    SIM_EMU_SIZE_USIM_GRP_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                     /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};

/*-----------------27/03/02 09:25-------------------
 * EF GRP1 Grouping info (for PBR record number 2)
 * --------------------------------------------------*/

Int8    defGrp1UsimData[] =
{
        0x01, 0x03, 0x00,
        0x02, 0x01, 0x03,
        0x03, 0x00, 0x00,
        0x04, 0x01, 0x03,
        0x05, 0x02, 0x03,
        0x06, 0x00, 0x00,
        0x00, 0x00, 0x00,
        0x00, 0x00, 0x00,
        0x00, 0x00, 0x00,
        0x00, 0x00, 0x00,
        0x00, 0x00, 0x00,
};

SimEfData    defGrp1UsimEfData =
{
    SIM_EMU_EF_GRP1,             /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_USIM_GRP1_RECS, /* num Rec      */
    SIM_EMU_SIZE_USIM_GRP1_FILE,/* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                     /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};



/*-----------------17/06/2003 10:54-----------------
 * EMAIL (type 1)
 * --------------------------------------------------*/


Int8  defEmailUsimData[] =
{
        'E' , 'H', '1', '@', 'T', 'T', 'P', 'C', 'O', 'M', '.', 'C', 'O', 'M', 0xff,
        0x01, 0x01,
        'C' , 'M', 'B', '@', 'T', 'T', 'P', 'C', 'O', 'M', '.', 'C', 'O', 'M', 0xff,
        0x01, 0x02,
        'R' , 'H', 'S', '@', 'T', 'T', 'P', 'C', 'O', 'M', '.', 'C', 'O', 'M', 0xff,
        0x01, 0x08,
        0xff , 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff,
        0xff , 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff
};

SimEfData    defEmailUsimEfData =
{
    SIM_EMU_EF_EMAIL,             /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_USIM_EMAIL_RECS,  /* num Rec      */
    SIM_EMU_SIZE_USIM_EMAIL_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                     /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};

Int8    defEmail1UsimData[] =
{
        0xff , 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff,
        0xff , 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff,
        0xff , 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff,
        0xff , 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff,
        0xff , 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff,

};

SimEfData    defEmail1UsimEfData =
{
    SIM_EMU_EF_EMAIL1,             /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_USIM_EMAIL1_RECS,  /* num Rec      */
    SIM_EMU_SIZE_USIM_EMAIL1_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                     /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};


/*-----------------17/06/2003 10:54-------------------------------
 * GAS (type 3 file) Lists the different existing group names
 * --------------------------------------------------------------*/
Int8    defGasUsimData[] =
{
        'G' , 'R', 'O', 'U', 'P', ' ', 'N', 'U', 'M', 'B', 'E', 'R', '1', 0xff, 0xff,
        'G' , 'R', 'O', 'U', 'P', ' ', 'N', 'U', 'M', 'B', 'E', 'R', '2', 0xff, 0xff,
        'G' , 'R', 'O', 'U', 'P', ' ', 'N', 'U', 'M', 'B', 'E', 'R', '3', 0xff, 0xff,
        'G' , 'R', 'O', 'U', 'P', ' ', 'N', 'U', 'M', 'B', 'E', 'R', '4', 0xff, 0xff,
        'G' , 'R', 'O', 'U', 'P', ' ', 'N', 'U', 'M', 'B', 'E', 'R', '5', 0xff, 0xff,
        'G' , 'R', 'O', 'U', 'P', ' ', 'N', 'U', 'M', 'B', 'E', 'R', '6', 0xff, 0xff,
         0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,  0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff

};

SimEfData    defGasUsimEfData =
{
    SIM_EMU_EF_GAS,             /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_USIM_GAS_RECS,  /* num Rec      */
    SIM_EMU_SIZE_USIM_GAS_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                     /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};

/*-----------------17/06/2003 10:54-----------------
 * EF UID used to uniquely identify each ADN record
 * --------------------------------------------------*/
Int8    defUidUsimData[] =
{
      0x00, 0x01,
      0x00, 0x03,
      0x00, 0x04,
      0x00, 0x07,
      0x00, 0x08,
      0x00, 0x09,
      0x00, 0x0a,
      0x00, 0x0b,
      0x00, 0x0d,
      0x00, 0x0e,
      0x00, 0x0f

};

SimEfData    defUidUsimEfData =
{
    SIM_EMU_EF_UID,             /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_USIM_UID_RECS,  /* num Rec      */
    SIM_EMU_SIZE_USIM_UID_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                     /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};

/*-----------------17/06/2003 10:54-----------------
 * UID1  (this belongs to PBR record number 2)
 * --------------------------------------------------*/
Int8    defUid1UsimData[] =
{
      0x00, 0x10,
      0x00, 0x11,
      0x00, 0x12,
      0x00, 0x13,
      0x00, 0x15,
      0x00, 0x17,
      0x00, 0x00,
      0x00, 0x00,
      0x00, 0x00,
      0x00, 0x00,
      0x00, 0x00
};

SimEfData    defUid1UsimEfData =
{
    SIM_EMU_EF_UID1,             /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_USIM_UID1_RECS,  /* num Rec      */
    SIM_EMU_SIZE_USIM_UID1_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                     /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};



/*-----------------17/06/2003 10:54-----------------
 * EF PSC: phonebook synchronization counter
 * see section 4.4.2.12.2 in TS 131 102
 * --------------------------------------------------*/
Int8    defPscUsimData[] =
{
      0x00, 0x12, 0x34, 0x56

};

SimEfData    defPscUsimEfData =
{
    SIM_EMU_EF_PSC,             /* Id           */
    SIM_EFSTRUCT_T,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    1 ,                         /* num Rec      */
    SIM_EMU_SIZE_USIM_PSC_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                     /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};

/*-----------------17/06/2003 10:54-----------------
 * EF CC (Change Counter)
 * see TS 131 102 section 4.4.2.12.3
 *--------------------------------------------------*/
Int8    defCcUsimData[] =
{
      0x00, 0x12

};

SimEfData    defCcUsimEfData =
{
    SIM_EMU_EF_CC,             /* Id           */
    SIM_EFSTRUCT_T,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    1 ,                         /* num Rec      */
    SIM_EMU_SIZE_USIM_CC_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                     /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};

/*-----------------17/06/2003 10:54-----------------
 * EF PUID   (previous UID)
 * section 4.4.2.12.4
 *--------------------------------------------------*/
Int8    defPuidUsimData[] =
{
    0x00, 0x17
};

SimEfData    defPuidUsimEfData =
{
    SIM_EMU_EF_PUID,           /* Id           */
    SIM_EFSTRUCT_T,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    1 ,                         /* num Rec      */
    SIM_EMU_SIZE_USIM_PUID_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                     /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};

/*-----------------27/03/02 09:25-------------------
 * ANR A   (first additional number)
 *--------------------------------------------------*/
Int8    defAnrAUsimData[] =
{
        0x01,   /*Additional number identifier*/
        0x03,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x33, 0xf3,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */
        0x01,   /*ADN SFI*/
        0x05,   /*ADN record number*/


        0x01,   /*Additional number identifier*/
        0x03,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x44, 0xf4,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */
        0x01,  /*ADN SFI*/
        0x04,   /*ADN record number*/


        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */
        0x01,  /*ADN SFI*/
        0x03,   /*ADN record number*/


        0x01,   /*Additional number identifier*/
        0x05,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x11, 0x11,0x11, 0xf1,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */
        0x01,   /*ADN SFI*/
        0x01,   /*ADN record number*/


        0x03,   /*Additional number identifier*/
        0x0b,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x44, 0x44, 0x44, 0x44,0x44,0x44,0x44,0x44,0x44,0x44,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */
        0x01,  /*ADN SFI*/
        0x02   /*ADN record number*/

};



SimEfData    defAnrAUsimEfData =
{
    SIM_EMU_EF_ANRA,             /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_USIM_ANRA_RECS,  /* num Rec      */
    SIM_EMU_SIZE_USIM_ANRA_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};

/*-----------------27/03/02 09:25-------------------
 * ANR B   (second additional number)
 *--------------------------------------------------*/

Int8    defAnrBUsimData[] =
{
        0x01,   /*Additional number identifier*/
        0x03,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x33, 0xf3,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */
        0x01,  /*ADN SFI*/
        0x05,   /*ADN record number*/


        0x01,   /*Additional number identifier*/
        0x03,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x44, 0xf4,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */
        0x01,  /*ADN SFI*/
        0x04,   /*ADN record number*/


        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */
        0x01,  /*ADN SFI*/
        0x02,   /*ADN record number*/


        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */
        0x01,  /*ADN SFI*/
        0x03,   /*ADN record number*/


        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */
        0x01,  /*ADN SFI*/
        0x01   /*ADN record number*/

};


SimEfData    defAnrBUsimEfData =
{
    SIM_EMU_EF_ANRB,             /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_USIM_ANRB_RECS,  /* num Rec      */
    SIM_EMU_SIZE_USIM_ANRB_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};

/*-----------------27/03/02 09:25-------------------
 * ANR C  (type 1 or 2) third additional number
 *--------------------------------------------------*/

Int8    defAnrCUsimData[] =
{
        0x01,   /*Additional number identifier*/
        0x03,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x33, 0xf3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */
        0x01,  /*ADN SFI*/
        0x05,   /*ADN record number*/


        0x01,   /*Additional number identifier*/
        0x03,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x44, 0xf4,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */
        0x01,  /*ADN SFI*/
        0x04,   /*ADN record number*/


        0x03,   /*Additional number identifier*/
        0x0b,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x21, 0x43, 0x65, 0x87, 0x09, 0x21, 0x43, 0x65, 0x87, 0x09,    /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */
        0x01,   /*ADN SFI*/
        0x02,   /*ADN record number*/


        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */
        0x01,  /*ADN SFI*/
        0x03,   /*ADN record number*/


        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */
        0x01,  /*ADN SFI*/
        0x01   /*ADN record number*/

};


 SimEfData    defAnrCUsimEfData =
{
    SIM_EMU_EF_ANRC,             /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_USIM_ANRC_RECS,  /* num Rec      */
    SIM_EMU_SIZE_USIM_ANRC_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};



/*------------------------------------------------------
 * ANR A1   (type 1 / 2)  first additional number
 *--------------------------------------------------*/

Int8    defAnrA1UsimData[] =
{
        0x01,   /*Additional number identifier*/
        0x03,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x33, 0xf3,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0x02,   /*CCP1*/
        0xff,   /*ext 1 */
        0xff,  /*ADN SFI*/
        0x05,   /*ADN record number*/


        0x01,   /*Additional number identifier*/
        0x03,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x44, 0xf4,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */
        0xff,  /*ADN SFI*/
        0x04,   /*ADN record number*/


        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */
        0xff,  /*ADN SFI*/
        0x02,   /*ADN record number*/


        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */
        0xff,  /*ADN SFI*/
        0x03,   /*ADN record number*/


        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */
        0xff,  /*ADN SFI*/
        0x01   /*ADN record number*/

};


SimEfData    defAnrA1UsimEfData =
{
    SIM_EMU_EF_ANRA1,             /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_USIM_ANRA1_RECS,  /* num Rec      */
    SIM_EMU_SIZE_USIM_ANRA1_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};

/*-----------------27/03/02 09:25-------------------
 * ANR B1    (type 1 /2)  second additional number
 *--------------------------------------------------*/

Int8    defAnrB1UsimData[] =
{
        0x01,   /*Additional number identifier*/
        0x03,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x33, 0xf3, 0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */
        0xff,  /*ADN SFI*/
        0x05,   /*ADN record number*/


        0x01,   /*Additional number identifier*/
        0x03,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x44, 0xf4,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */
        0xff,   /*ADN SFI*/
        0x04,   /*ADN record number*/


        0x01,   /*Additional number identifier*/
        0x0b,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x21, 0x43, 0x65, 0x87,0x90,0x21,0x43,0x65,0x87,0x90,   /*additional number */
        0xff,   /*CCP1*/
        0x05,   /*ext 1 */
        0xff,   /*ADN SFI*/
        0x02,   /*ADN record number*/

        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */
        0xff,  /*ADN SFI*/
        0xff,   /*ADN record number*/


        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */
        0xff,  /*ADN SFI*/
        0xff   /*ADN record number*/

};


SimEfData    defAnrB1UsimEfData =
{
    SIM_EMU_EF_ANRB1,             /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_USIM_ANRB1_RECS,  /* num Rec      */
    SIM_EMU_SIZE_USIM_ANRB1_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};

/*-----------------27/03/02 09:25-------------------
 * ANR C1  (type 1 / 2)  third additional number
 *--------------------------------------------------*/

Int8    defAnrC1UsimData[] =
{
        0x01,   /*Additional number identifier*/
        0x03,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x33, 0xf3,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */
        0xff,  /*ADN SFI*/
        0x05,   /*ADN record number*/


        0x01,   /*Additional number identifier*/
        0x03,   /*length of BCD/SSC content*/
        0x81,   /*TON and NPI*/
        0x44, 0xf4,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */
        0xff,  /*ADN SFI*/
        0x04,   /*ADN record number*/


        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */
        0xff,  /*ADN SFI*/
        0x02,   /*ADN record number*/


        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */
        0xff,  /*ADN SFI*/
        0x03,   /*ADN record number*/


        0xff,   /*Additional number identifier*/
        0xff,   /*length of BCD/SSC content*/
        0xff,   /*TON and NPI*/
        0xff, 0xff,0xff, 0xff,0xff,0xff,0xff,0xff,0xff,0xff,   /*additional number */
        0xff,   /*CCP1*/
        0xff,   /*ext 1 */
        0xff,  /*ADN SFI*/
        0xff   /*ADN record number*/

};

SimEfData    defAnrC1UsimEfData =
{
    SIM_EMU_EF_ANRC1,             /* Id           */
    SIM_EFSTRUCT_LF,            /* Type         */
    SIM_DIR_DF_PHONEBOOK,       /* Dir          */
    SIM_EMU_NUM_USIM_ANRC1_RECS,  /* num Rec      */
    SIM_EMU_SIZE_USIM_ANRC1_FILE, /* Recs  len   */
    (0),                        /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_PIN,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};
#endif 
/*CQ00135656, Cgliu, 2022-02-25,End  */

/*Modification for USIM it test. CQ00001116, Chengguang Liu, June.09 09,Begin*/
#ifdef ON_PC
Int8    defAdnUsimData_ccsa[] =
{
        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,

        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,

        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        
        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        
        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        
        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        
        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        
        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        
        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        
        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
};

Int8    defExt1UsimData_ccsa[] = {
/*1*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*2*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*3*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*4*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*5*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*6*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*7*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*8*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*9*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*a*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*b*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*c*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff
};


Int8    defPbrUsimData_ccsa[] = {
                             /*record number 1*/

                             0xa8, 0x16,                    /*type 1 files*/
                             0xc0, 0x03, 0x4f, 0x3a, 0x01,  /*ADN*/
                             0xc5, 0x03, 0x4f, 0x09, 0x02,  /*PBC*/
                             0xc6, 0x02, 0x4f, 0x83,        /*GRP*/
                             0xc9, 0x02, 0x4f, 0x81,        /*UID*/
                             0xc1, 0x02, 0x4f, 0x97,        /*IAP*/

                             0xa9, 0x14,                    /*type 2 files */
                             0xc4, 0x02, 0x4f, 0x11,        /*ANR*/
                             0xc4, 0x02, 0x4f, 0x13,        /*ANR*/
                             0xc4, 0x02, 0x4f, 0x15,        /*ANR*/
                             0xc3, 0x02, 0x4f, 0x19,        /*SNE*/
                             0xca, 0x02, 0x4f, 0x50,        /*EMAIL*/

                             0xaa, 0x0c,                    /*type 3 files */
                             0xc2, 0x02, 0x4f, 0x4a,        /*EXT1*/
                             0xc7, 0x02, 0x4f, 0x4b,        /*AAS*/
                             0xc8, 0x02, 0x4f, 0x4c,
                             0xcb, 0x02, 0x4f, 0x99,        /*CCP1*/

                         

                             };



SimEfData    defPbrUsimEfData_ccsa =
{
    SIM_EMU_EF_PBR,                 /* Id           */
    SIM_EFSTRUCT_LF,              /* Type         */
    SIM_DIR_DF_PHONEBOOK,        /* Dir          */
    1,   /* Num Recs     */
    SIM_EMU_SIZE_USIM_PBR_FILE,  /* Rec Len     */
    (0),                         /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_ADM,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};

Int8    defAdnUsimData_31121[] =
{
        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,

        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,

        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        
        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        
        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        
        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        
        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        
        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        
        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
        
        'A' , 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X','Y', 'Z', 'A', 'B', 'C', 'D', 'E', 'F',
        0x03,0x81,0x21,0xf3,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
};

Int8    defExt1UsimData_31121[] = {
/*1*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*2*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*3*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*4*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*5*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*6*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*7*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*8*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*9*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*a*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*b*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*c*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff
};


Int8    defPbrUsimData_31121[] = {
                             /*record number 1*/

                             0xa8, 0x16,                    /*type 1 files*/
                             0xc0, 0x03, 0x4f, 0x3a, 0x01,  /*ADN*/
                             0xc5, 0x03, 0x4f, 0x09, 0x02,  /*PBC*/
                             0xc6, 0x02, 0x4f, 0x83,        /*GRP*/
                             0xc9, 0x02, 0x4f, 0x81,        /*UID*/
                             0xc1, 0x02, 0x4f, 0x97,        /*IAP*/

                             0xa9, 0x14,                    /*type 2 files */
                             0xc4, 0x02, 0x4f, 0x11,        /*ANR*/
                             0xc4, 0x02, 0x4f, 0x13,        /*ANR*/
                             0xc4, 0x02, 0x4f, 0x15,        /*ANR*/
                             0xc3, 0x02, 0x4f, 0x19,        /*SNE*/
                             0xca, 0x02, 0x4f, 0x50,        /*EMAIL*/

                             0xaa, 0x0c,                    /*type 3 files */
                             0xc2, 0x02, 0x4f, 0x4a,        /*EXT1*/
                             0xc7, 0x02, 0x4f, 0x4b,        /*AAS*/
                             0xc8, 0x02, 0x4f, 0x4c,
                             0xcb, 0x02, 0x4f, 0x99,        /*CCP1*/

                         

                             };



SimEfData    defPbrUsimEfData_31121 =
{
    SIM_EMU_EF_PBR,                 /* Id           */
    SIM_EFSTRUCT_LF,              /* Type         */
    SIM_DIR_DF_PHONEBOOK,        /* Dir          */
    1,   /* Num Recs     */
    SIM_EMU_SIZE_USIM_PBR_FILE,  /* Rec Len     */
    (0),                         /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_ALWAYS,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};
/*Added by zhjzhao for CQ00013463 Add a set of default USIM files value for LTE simulation on PC 10-10-11, End*/
#if defined(UPGRADE_LTE)
Int8    defExt1UsimData_LTE[] = {
/*1*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*2*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*3*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*4*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*5*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*6*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*7*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*8*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*9*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*a*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*b*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff,

/*c*/   0x00,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff
};


Int8    defPbrUsimData_LTE[] = {
                             /*record number 1*/

                             0xa8, 0x16,                    /*type 1 files*/
                             0xc0, 0x03, 0x4f, 0x3a, 0x01,  /*ADN*/
                             0xc5, 0x03, 0x4f, 0x09, 0x02,  /*PBC*/
                             0xc6, 0x02, 0x4f, 0x83,        /*GRP*/
                             0xc9, 0x02, 0x4f, 0x81,        /*UID*/
                             0xc1, 0x02, 0x4f, 0x97,        /*IAP*/

                             0xa9, 0x14,                    /*type 2 files */
                             0xc4, 0x02, 0x4f, 0x11,        /*ANR*/
                             0xc4, 0x02, 0x4f, 0x13,        /*ANR*/
                             0xc4, 0x02, 0x4f, 0x15,        /*ANR*/
                             0xc3, 0x02, 0x4f, 0x19,        /*SNE*/
                             0xca, 0x02, 0x4f, 0x50,        /*EMAIL*/

                             0xaa, 0x0c,                    /*type 3 files */
                             0xc2, 0x02, 0x4f, 0x4a,        /*EXT1*/
                             0xc7, 0x02, 0x4f, 0x4b,        /*AAS*/
                             0xc8, 0x02, 0x4f, 0x4c,
                             0xcb, 0x02, 0x4f, 0x99        /*CCP1*/
               };



SimEfData    defPbrUsimEfData_LTE =
{
    SIM_EMU_EF_PBR,                 /* Id           */
    SIM_EFSTRUCT_LF,              /* Type         */
    SIM_DIR_DF_PHONEBOOK,        /* Dir          */
    1,   /* Num Recs     */
    SIM_EMU_SIZE_USIM_PBR_FILE,  /* Rec Len     */
    (0),                         /* curr Rec     */
    (0),
    {
     USIM_ACCESS_NEVER,    /* delete file */
     USIM_ACCESS_NEVER,    /* terminate */
     USIM_ACCESS_ADM,      /* activate */
     USIM_ACCESS_ADM,      /* deactivate */
     USIM_ACCESS_NEVER,    /* write */
     USIM_ACCESS_ALWAYS,      /* update */
     USIM_ACCESS_PIN       /* read */
    },
    SIM_UICC_ACTIVATED_STATE, /*file State */
    FALSE,                    /*sfi supported  */
    0x00,                     /*sfi */
    TRUE                      /*Under USIM */
};
#endif
/* Modification for USIM it test. CQ00001334, Zhanghao, 20090703 end */
#endif
#endif

/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/api/inc/ups_typ.h#6 $
 *   $Revision: #6 $
 *   $DateTime: 2007/03/30 13:53:25 $
 *************************************************************************/
/** \file
 * 3G PS Primitive Data Type definitions required by more than one interface
 *************************************************************************/

#if !defined (UPS_TYP_H)
#define       UPS_TYP_H
//This flag open WBAMR codec in PS

/***************************************************************************
*   Manifest Constants
***************************************************************************/

#if !defined (UPS_INTRA_FREQ_FILTER_COUNT)
/** Defines the number of measurements the physical layer will send every
 * 200 ms.
 */
# define UPS_INTRA_FREQ_FILTER_COUNT            4
#endif /* UPS_INTRA_FREQ_FILTER_COUNT */

#if !defined (UPS_MAX_MEASURED_INTER_FREQ_CELLS)
/** Defines the maximum number of inter-frequency cells that can be measured by
 * L1. Note: L1 supports 32 inter-frequency cells!
 */
# define UPS_MAX_MEASURED_INTER_FREQ_CELLS      32 /* CQ00021479 change 15 to 32 */
#endif /* UPS_MAX_MEASURED_INTER_FREQ_CELLS */

#if ! defined (UPGRADE_EXCLUDE_2G)

#if !defined (UPS_MAX_MEASURED_INTER_RAT_CELLS)
/** Defines the maximum number of inter-RAT cells that can be measured by L1. */
# define UPS_MAX_MEASURED_INTER_RAT_CELLS      32
#endif /* UPS_MAX_MEASURED_INTER_RAT_CELLS */

#endif

                             

#if !defined(UPS_MINIMUM_SKIP_FRAMES)
/** Defines the minimum number of frames to skip to next required BCH frame. */
#define UPS_MINIMUM_SKIP_FRAMES                 6
#endif /* UPS_MINIMUM_SKIP_FRAMES */

#if defined(UPGRADE_3G_HSDPA)
/** Defines the number of MAC-hs PDU buffers required to be available to L1. */
  #define UPHY_MAX_POINTER_ASSIGN                   3
#endif /* UPGRADE_3G_HSDPA */

/** Some L1s can take advantage of fewer data copies if L1 is allowed to set
 * this parameter to >1. \note The protocol stack will allocate this number
 * of UL PDU lists in CELL_DCH, so there is a memory overhead.
 */
#define UPS_MAX_UL_PDULIST                      2

/***************************************************************************
* 3G Build Option Checking
***************************************************************************/



       
#    if defined (UPGRADE_3G_RELEASE_5)
#      if !defined(UPGRADE_3G_RELEASE_4)
#        error For a 3GPP Release 5 build, UPGRADE_3G_RELEASE_4 is required
#      endif /* UPGRADE_3G_RELEASE_4 */
#    endif /* UPGRADE_3G_RELEASE_5 */
                                   


#  if defined(UPGRADE_3G_HSDPA)
#    if !defined(UPGRADE_3G_RELEASE_5)
#      error HSDPA must be built with at least 3GPP Release 5
#    endif /* UPGRADE_3G_RELEASE_5 */
#  endif /* UPGRADE_3G_HSDPA */

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <uas_asn.h>
#include <system.h>
#include <ups_cfg.h>
#include <u1_typ.h>
#include <mnl3_typ.h>

/***************************************************************************
*   Manifest Constants
***************************************************************************/

/** Defines the maximum stored cell identities. */
#define MAX_STORED_CELL_IDENTITIES          MAX_STORED_SCRAMBLING_CODES

/** Defines the security key length in octets. */
#define SECURITY_KEY_LENGTH             16
/** Defines the number of bits in a START value. */
#define UPS_START_VALUE_BITS            20 /* START value is 20 bits */
/** Defines the default START value. */
#define UPS_DEFAULT_START_VALUE         ((USTART_Value)0x00000000)
/** Defines the invalid START value. */
#define UPS_INVALID_START_VALUE         ((USTART_Value)0xFFFFFFFF)
/** Defines the maximum START value. */
#define UPS_MAX_START_VALUE             ((USTART_Value)((0x1 << UPS_START_VALUE_BITS) - 1))

/* Cipher KSI's
 *                                     Bit
 *          7       6       5       4       3       2       1       0
 *      --------|-------|-------|-------|-------|-------|-------|--------
 *      |            Unused             |Domain |          KSI           |
 *      --------|-------|-------|-------|-------|-------|-------|--------
 *
 * Where:   Domain: 0 = CS, 1 = PS
 *          KSI   : 0-6 = Keys, 7(UPS_NO_KEY_SET) = No key
 */
/** Defines the invalid Key Set Identifier. */
#define UPS_INVALID_KSI                 0XFF

/** Defines that a key is not present in ciphering or integrity protection.
 * First 3 bits are used for KSI. */
#define UPS_NO_KEY_SET                  0x07

/** Defines the mask for identifying the KSI domain. */
#define UPS_KSI_DOMAIN_MASK             0x08

/** Defines the dimension of an array to hold all cipher keys, which for fast
 * access needs to be large enough to hold keys 0-14, although key 7 will
 * always be empty.
 */
#define UPS_KSI_ARRAY_SIZE              15      /* KSI's 0-14 */

/** To right shift the COUNT-C value of an AM entity by 12 bits to
 * extract the START value, which is the 20 MSBs of COUNT-C.
 */
#define COUNTC_TO_START(countc)         ((countc) >> 12)

/** To left shift the START value by 12 bits to fit in the 20 MSB of the COUNT-C
 * used for AM entity.
 */
#define START_TO_COUNTC(start)          ((start) << 12)

/** To mask the COUNT-C of an UM entity by 25 MSB mask to extract the
 * HFN component with 7 LSBs set to 0.
 */
#define COUNTC_MSB_MASK(x)              ((x) & 0xFFFFFF80)

/** To right shift the COUNT-C value of an AM entity by 12 bits to extract the
 * START value, which is the 20 MSBs of COUNT-C.
 */
#define COUNTC_TO_COUNTC_MSB(countc)    COUNTC_TO_START(countc)

/** To left shift 25 MSB of COUNT-C by 7 bits to fit in UM entity COUNT-C. */
#define COUNTC_MSB_TO_COUNTC(countcmsb) ((countcmsb) << 7)

/** To right shift TM entity HFN by 4 bits to extract the START value. */
#define TM_HFN_TO_START(hfn)            ((hfn) >> 4)

/** To left shift START value by 4 bits in to MSB of the HFN. */
#define START_TO_TM_HFN(start)          ((start) << 4)

/** Defines the maximum COUNT-I sequence number. */
#define UPS_MAX_COUNTI_SEQ_NUM          ((URRC_MessageSequenceNumber)15)

/** Defines the number of bits used for storing KSI. */
#define URRC_KEY_SET_ID_BIT_SIZE        3 /* 33.102 - 6.4.4 */

/** Defines the mask for 3 LSBs of KSI. */
#define URRC_KEY_SET_ID_BIT_MASK        ((RrcKeySequence) 0x07)

/** To unify the 4 LSBs of RRC sequence number with 28 MSBs of the HFN
 * of COUNT-I. */
#define SET_COUNTI(hfn,sn)              ((((Int32) (hfn)) << 4) | \
                                         (((Int32) (sn)) & (Int32) (0xf)))

/** To right shift the HFN by 8 bits to extract the START value. */
#define COUNTI_HFN_TO_START_VALUE(hfn)  ((hfn) >> 8)

/** to left shift the START value by 8 bits to extract HFN. */
#define COUNTI_START_VALUE_TO_HFN(start) ((start) << 8)

/** To return the CN domain as a string "CS"/"PS" */
#define DEBUG_DOMAIN_NAME_STRING(cn) (((cn) == UCN_DomainIdentity_cs_domain) ? \
                                            "CS" : "PS")

/* defines above are based on values specified in 33.102 */

/** Defines the longest TTI in frames, which is 80 ms. */
#define UPS_LONGEST_TTI_IN_FRAMES        8

/** Defines the maximum RLC SDU octets, which is used to define the maximum
 * size of an SDU data.
 * Although [3G TS 24.008] defines this to be 1520, we've witnessed SDUs which
 * exceed that on an HSDPA network.
 * The book "PPP design, implementation and debugging" states that although
 * the Maximum Receive Unit (MRU) is 1500 octets, the author has witnessed
 * implementation errors that allow MRUs of 1524, 1576 and 1600 octets
 */
#define UPS_MAX_RLC_SDU_DATA_OCTETS         1600

/** Defines the maximum number of octets required to allow space for in-sutu
 * PDCP header decompression.
 */
#define UPS_MAX_HEADER_DECOMPRESSION_OCTETS 128

/** Defines the maximum RLC SDU octets, which is used to define the size of the
 * SDU data array when the data is passed within the primitive, instead of by
 * reference. It's size comes from <tt> [3G TS 24.008] </tt> (1520) + an
 * allowance for PDCP in-situ header decompression, which although only needed
 * on the downlink on bearers owned by PDCP, its used to define both uplink and
 * dowwnlink SDU's. However as in-primitive SDU data will only be used during
 * development, its not seen as a problem
 */
#define UPS_MAX_RLC_SDU_OCTETS              (UPS_MAX_RLC_SDU_DATA_OCTETS +     \
                                            UPS_MAX_HEADER_DECOMPRESSION_OCTETS)

/* The following 3 constants are common to CSR in RRC and L1. */



/** Defines the System Information Message Type 3 in GSM. */
#define CPHY_GSM_SYS_INFO_MESSAGE_TYPE_2    0x02
#define CPHY_GSM_SYS_INFO_MESSAGE_TYPE_3    0x03

/** Defines the System Information Message Type 4 in GSM. */
#define CPHY_GSM_SYS_INFO_MESSAGE_TYPE_4    0x04

/** Defines the maximum size of the array that holds the TX RLC PDUs information per EDCH TTI */
/*CQ00067395 merged for FRBD 20140806*/
#define MAX_RLC_PDUS_INFO_PER_EDCH_TTI      32


/***************************************************************************
*   Macro Functions
***************************************************************************/

/***************************************************************************
*   Types
***************************************************************************/

/* The following 4 typedef's are common to CSR in RRC and L1. */


/** Indicates the type of logical channel. */
typedef enum UlogicalChannelTypeTag
{
    /** Broadcast Control Channel */
    UBCCH                       = 0,
    /** Paging Control Channel */
    UPCCH                       = 1,
    /** Common Control Channel */
    UCCCH                       = 2,
    /** Common Traffic Channel */
    UCTCH                       = 3,
    /** Dedicated Control/Traffic Channel */
    UDCCH_DTCH                  = 4
}
UlogicalChannelType;


/** A 16 octet length security key used in Ciphering and Integrity. */
typedef Int8    USecurityKey [SECURITY_KEY_LENGTH];

/** The key set identifier (KSI) is a number which is associated with the cipher
 * and integrity keys derived during authentication. The key set identifier is
 * allocated by the network and sent with the authentication request message to
 * the mobile station where it is stored together with the calculated cipher key
 * CK and integrity key IK.
 */
typedef Int8    KeySeqId;

#if !defined (UPGRADE_EXCLUDE_2G)
/** Defines the GSM cell information. */
typedef struct UGSM_CellInformationTag
{
    /** Indicates the Base Transceiver Identification Code, which consists of
     * NCC and BCC.
     */
    UBSIC                          BSIC;

    /** Indicates the band of the GSM cell. */
    UFrequency_Band                bandIndicator;

    /** Indicates the ARFCN of the GSM cell. */
    Int16                          BCCH_ARFCN;
}
UGSM_CellInformation;
#endif


/** Defines the different codec types. For more information refer to <tt>[3G TS * 26.103]</tt>. */

// make CsCodecType as CodecType from mnl3_type.h
/** \def CsCodecType */
#define CsCodecType CodecType

/** GSM Full Rate Codec Type with CoID = 00000000b 0 */
#define    CS_CODEC_TYPE_FR_COID   GSM_FR 

/** GSM Half Rate Codec Type with CoID = 00000001b 1 */
#define    CS_CODEC_TYPE_HR_COID   GSM_HR

/** GSM Enhanced Full Rate Codec Type with CoID = 00000010b 2 */
#define    CS_CODEC_TYPE_EFR_COID  GSM_EFR

/** Full Rate Adaptive Multi-Rate Codec Type with CoID = 00000011b 3 */
#define    CS_CODEC_TYPE_FR_AMR_COID FR_AMR

/** Half Rate Adaptive Multi-Rate Codec Type with CoID = 00000100b 4 */
#define    CS_CODEC_TYPE_HR_AMR_COID  HR_AMR

/** UMTS Adaptive Multi-Rate Codec Type with CoID = 00000101b 5 */
#define   CS_CODEC_TYPE_UMTS_AMR_COID UMTS_AMR

/** UMTS Adaptive Multi-Rate2 Codec Type with CoID = 00000110b 6 */
#define    CS_CODEC_TYPE_UMTS_AMR_2_COID UMTS_AMR_2

/** UMTS ADAPTIVE MULTI-RATE WIDE BAND Codec Type with CoID = 00001010b 0xA */
#define	CS_CODEC_TYPE_UMTS_AMR_WB_COID UMTS_AMR_WB   

/** Unspecified Codec Type Codec Type with CoID = 00001111b 15 */
#define    CS_CODEC_TYPE_NONE_SPECIFIED  CODEC_TYPE_NONE_SPECIFIED 


/* PduListData is an array of octets, containing a list of contiguous PDUs,
 * where each PDU in the list allows room for the maximum transport block size,
 * and contains a bit offset, length and status.
 * Each PDU block is filled from the top down, and allows the higher layers to
 * strip off their header information.
 * Example list, passed to RLC from MAC, containing two 10 bit PDU's,
 * where 9 bits of MAC header has already been stripped off:
 *      bitOffset[ 0 ]      = 9
 *      bitOffset[ 1 ]      = 9
 *      bitOffset[ 2 ]      = 0
 *      ...
 *      bitLength[ 0 ]      = 10
 *      bitLength[ 1 ]      = 10
 *      bitLength[ 2 ]      = 0
 *      ...
 *      data[ 0 ] PDU 1     = Bit 7 contained MSB of MAC headers of PDU 1
 *      data[ 1 ]           = Bit 7 contained LSB MAC header of PDU 1
 *                            Bit 6 contains MSB of PDU 1 data
 *      data[ 2 ]           = Bit 5 contains LSB of PDU 1 data
 *                PDU 2       Bits 4-0 contained MAC header of PDU 2
 *      data[ 3 ]           = Bits 7-4 contained MAC header of PDU 2
 *                            Bit 3 contains MSB of PDU 2 data
 *      data[ 4 ]           = Bit 2 contains LSB of PDU 2 data
 *                            Bits 1-0 unused
 */

#if defined(UPGRADE_3G_RELEASE_5)
#if defined(UPGRADE_3G_HSDPA)
/** Defines the downlink MAC-hs pdu data as an array of octets. */
typedef Int8 UDlHsPduListData [UDL_HS_MAX_MAC_HS_PDU_DATA_OCTETS];
#endif /* UPGRADE_3G_HSDPA */
#endif /* UPGRADE_3G_RELEASE_5 */


/** Defines the cell for \c CbmcCellChangeInd and the corresponding Indication
 * to the application layer.
 */
typedef struct UCellDescriptionTag
{
    /** Indicates if Cell Broadcast Services are supported in this cell or not.
     */
    Boolean                             cbSupported;

    /** Indicates the downlink frequency of the cell. */
    UUARFCN                             uarfcn_dl;

    /** Indicates the Location Area Identifier of the cell. */
    ULAI                                lai;

    /** Indicates the cell identity of the cell. */
    UCellIdentity                       cellIdentity;
}
UCellDescription;


/** Specifies the COUNT-C to use on both CS and PS domains when
 * \c CmacHfnAbortReq primitive is sent to MAC.
 */
typedef struct UmacTmCountcInfoTag
{
    /** Specifies the COUNT-C for the CS domain. */
    Int32                               csCountC;

    /** Specifies the COUNT-C for the PS domain. */
    Int32                               psCountC;
}
UmacTmCountcInfo;


/** Indicates the RLC entity type for a bearer. */
//ICAT EXPORTED ENUM
typedef enum UrlBearerModeTag
{
    /** Transparent Mode */
    URL_BEARER_MODE_TM      = 0,
    /** Unacknowledged Mode */
    URL_BEARER_MODE_UM      = 1,
    /** Acknowledged Mode */
    URL_BEARER_MODE_AM      = 2,
    /** Number of bearer modes */
    URL_MUM_BEARER_MODES    = 3
} UrlBearerMode;


 /** Defines the information required by MAC for TFC selection for an
  * un-segmented transparent mode radio bearer, where MAC is allowed to
  * transmit multiple SDU's in a TTI (one per PDU), provided each SDU is the
  * same size.
 */
//ICAT EXPORTED STRUCT
typedef struct UrlTmUnsegmentedTfcSelectionInfoTag
{
    /** Indicates how many SDU's of the same size (\c sduBits) are available
     * for transmission.
     */
    Int16                           numSdus;

    /** Indicates the size of each SDU in bits. */
    Int16                           sduBits;
} UrlTmUnsegmentedTfcSelectionInfo;


/** Defines the information required by MAC for TFC selection
 * for a segmented transparent mode radio bearer, where MAC is allowed to
 * transmit one SDU per TTI, segmented into one or more  PDU's of the same size.
 */
//ICAT EXPORTED STRUCT
typedef struct UrlTmSegmentedTfcSelectionInfoTag
{
    /** Indicates the size of the next available SDU in bits. */
    Int32                           sduBits;
} UrlTmSegmentedTfcSelectionInfo;


/** Defines the information required by MAC for TFC selection for a transparent
 * mode radio bearer, and its contents differ according to its configuration.
 */
//ICAT EXPORTED STRUCT
typedef struct UrlTmTfcSelectionInfoTag
{
    /** A boolean flag to indicate if the bearer is configured to transmit
     * un-segmented or segmented SDU's:
     *     - FALSE: Unsegmented (one SDU per PDU, provided all the SDUs are of
     *              the same size)
     *     - TRUE: Segmented (one SDU per TTI, evenly segmented)
     */
    Boolean                         segmented;

    /** A data structure to define either of these:
     *     - #UrlTmUnsegmentedTfcSelectionInfo
     *     - #UrlTmSegmentedTfcSelectionInfo
     */
    union
    {
        /** Refer to #UrlTmUnsegmentedTfcSelectionInfo */
        UrlTmUnsegmentedTfcSelectionInfo    unsegmented;
        /** Refer to #UrlTmSegmentedTfcSelectionInfo */
        UrlTmSegmentedTfcSelectionInfo      segmented;
    }                               data;
}
UrlTmTfcSelectionInfo;


/** Defines the information required by MAC for TFC selection for an
 * unacknowledged mode radio bearer.
 */
//ICAT EXPORTED STRUCT
typedef struct UrlUmTfcSelectionInfoTag
{
    /** Indicates an approximation of the number of bits of data available
     * for transmission, given that RLC doesn't know the PDU size that will be
     * selected by MAC; and it includes:
     *     - the total number of bits of SDU data available for transmission
     *     - the size of one length indicator for each SDU
     *     - the size of one length indicator for each SDU that requires a START
     *       LI
     */
    Int16                           numBitsForTx;
}
UrlUmTfcSelectionInfo;
/*CQ00067395 merged for FRBD 20140806 start*/
//ICAT EXPORTED STRUCT
typedef struct UrlSegmentedTxPdusInfoTag
{
    Int16                           pdusNumber;
    Int16                           pduSize;
}UrlSegmentedTxPdusInfo;
/*CQ00067395 merged for FRBD 20140806 end*/
/** Defines the information required by MAC for TFC selection for an
 * acknowledged mode radio bearer, and its contents differ according to
 * configuration.
 */
//ICAT EXPORTED STRUCT
typedef struct UrlAmTfcSelectionInfoTag
{
    /** Indicates if the bearer is configured for one or two logical channels.
     */
    Boolean                         twoLogicalChannels;

    /** Indicates how many bits of control PDUs are available for transmission.
     */
    Int16                           numControlBitsForTx;

    /** Indicates how many bits of data PDUs are available for transmission
     * (excludes PDUs that can't be transmitted yet).
     */
    Int16                           numDataBitsForTx;

    Int16                           maxNumberOfPdusForTx;  /*CQ00067395 merged for FRBD 20140806*/
    /** When twoLogicalChannels is configured, indicates how many bits of control PDUs are available 
        *for transmission on data logical channel.
        */
    Int16                           numControlOnDataLogChBitsForTx;

}
UrlAmTfcSelectionInfo;


/** Defines the information required by MAC for TFC selection for a radio
 * bearer, and its contents vary according to the bearer mode.
 */
//ICAT EXPORTED STRUCT
typedef struct UrlTfcSelectionInfoTag
{
    /** Indicates the operating mode of a bearer, which can be AM/UM/TM. */
    UrlBearerMode                   mode;

    /** Holds any one of the following based on \c mode:
     *     - #UrlTmTfcSelectionInfo
     *     - #UrlUmTfcSelectionInfo
     *     - #UrlAmTfcSelectionInfo
     */
    union
    {
        UrlTmTfcSelectionInfo tm;
        UrlUmTfcSelectionInfo um;
        UrlAmTfcSelectionInfo am;
    }                               info;
}
UrlTfcSelectionInfo;


/** Indicates the total number of bits of data that are queued for transmission
 * on UM and AM bearers, and includes PDU that are not allowed to be
 * transmitted yet. For TM bearers this value is always set to 0.
 */
typedef Int32                       UrlTrafficVolumeBits;


 /** Defines the information required by MAC for TFC selection and traffic
  * volume measurements for a radio bearer, and its contents vary according to
  * the bearer mode.
 */
//ICAT EXPORTED STRUCT
typedef struct UrlBufferInfoTag
{
    /** Indicates the information required by MAC for TFC selection, where its
     * content differs for each mode.
     */
    UrlTfcSelectionInfo             tfcSelection;

    /** Indicates the total number of bits of data queued for transmission. */
    UrlTrafficVolumeBits            trafficVolumeBits;
/*CQ00067395 merged for FRBD 20140806 start*/
    /** PDUs sizes information */
    Int16                           numOfSegmentedTxPdus;
    UrlSegmentedTxPdusInfo          pdusInfo[MAX_RLC_PDUS_INFO_PER_EDCH_TTI];
/*CQ00067395 merged for FRBD 20140806 end*/
}
UrlBufferInfo;


/*CQ29374 yhang 2013-02-27 BEGIN*/
typedef enum UrrSpeechCodecTypeTag
{
    WB_AMR  = 0,
    NB_AMR  = 1,
    NON_AMR = 2
}
UrrSpeechCodecType;
/*CQ29374 yhang 2013-02-27 END*/


#endif /* UPS_TYP_H */

/* END OF FILE */


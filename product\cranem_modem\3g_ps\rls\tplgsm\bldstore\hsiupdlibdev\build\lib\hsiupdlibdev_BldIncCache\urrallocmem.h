/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urrallocmem.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2006/11/08 10:41:49 $
 **************************************************************************
 *  File Description :
 *
 *  This file defines the memory pools for the URRC local memory allocator
 *
 **************************************************************************/

/***************************************************************************
 * Access Macros
 ***************************************************************************/
#if !defined (DS3_LTE_ONLY) && !defined(ENABLE_CAT1_LG)

#if defined (MEM_CFG_GET_POOL_ID)
#define MEM_DEF(nAME, bLKsIZE, nUMgRPS) nAME,
#endif

#if defined (MEM_CFG_DECLARE_POOLS)
#define MEM_DEF(nAME, bLKsIZE, nUMgRPS) \
    Int32 pool##nAME [ \
                      nUMgRPS * NUM_BLOCKS_PER_GROUP * \
                      ((bLKsIZE + sizeof (UrrcMemBlkHeader) + OVERRUN_END_BLOCK_CHECK_SIZE + sizeof (Int32)) / sizeof (Int32)) \
                      ];
#endif

#if defined (MEM_CFG_GET_POOL_PTRS)
#define MEM_DEF(nAME, bLKsIZE, nUMgRPS) urrcMemPools.pool##nAME,
#endif

#if defined (MEM_CFG_DECLARE_BLK_BITMAPS)
#define MEM_DEF(nAME, bLKsIZE, nUMgRPS) \
    static Int32 blkBitmap##nAME [nUMgRPS];
#endif

#if defined (MEM_CFG_GET_BITMAP_PTRS)
#define MEM_DEF(nAME, bLKsIZE, nUMgRPS) blkBitmap##nAME,
#endif

#if defined (MEM_CFG_GET_GROUP_BITMAPS)
#define MEM_DEF(nAME, bLKsIZE, nUMgRPS) ((POOL_FULL >> nUMgRPS) << nUMgRPS),
#endif

#if defined (MEM_CFG_GET_BLOCK_SIZES)
#define MEM_DEF(nAME, bLKsIZE, nUMgRPS)  (bLKsIZE + sizeof (UrrcMemBlkHeader) + OVERRUN_END_BLOCK_CHECK_SIZE),
#endif

#if defined (MEM_CFG_GET_NUM_BLOCKS)
#define MEM_DEF(nAME, bLKsIZE, nUMgRPS) (nUMgRPS * NUM_BLOCKS_PER_GROUP),
#endif

/* Debug macros */
#if defined (MEM_CFG_DEBUG_PRINT_POOL_ADDR)
#define MEM_DEF(nAME, bLKsIZE, nUMgRPS) printf ("Pool %ld addr = 0x%lx\n", nAME, (Int32) urrcMemPools.pool##nAME);
#endif

#if defined (MEM_CFG_DEBUG_GET_TOTAL_POOL_SIZES)
#define MEM_DEF(nAME, bLKsIZE, nUMgRPS) \
    printf ("Pool %ld, size = %ld bytes\n", nAME, nUMgRPS * NUM_BLOCKS_PER_GROUP * (bLKsIZE + sizeof (UrrcMemBlkHeader) + OVERRUN_END_BLOCK_CHECK_SIZE)); \
    totalPoolSizes += (nUMgRPS * NUM_BLOCKS_PER_GROUP * (bLKsIZE + sizeof (UrrcMemBlkHeader) + OVERRUN_END_BLOCK_CHECK_SIZE));
#endif



/***************************************************************************
 * Pool definitions
 ***************************************************************************/
/*       Pool name      Block Size  Num Groups
 *                      (bytes)     (1 group = 32 blocks)
 *                                  Max = 32
 *      ================================================== */
MEM_DEF (POOL_0,          8,          12)
MEM_DEF (POOL_1,          16,         3)
MEM_DEF (POOL_2,          28,         3)
MEM_DEF (POOL_3,          44,         1)
MEM_DEF (POOL_4,          220,        3)





/***************************************************************************
 * Undefine macros and defines
 ***************************************************************************/
#undef MEM_CFG_GET_POOL_ID
#undef MEM_CFG_DECLARE_POOLS
#undef MEM_CFG_GET_POOL_PTRS
#undef MEM_CFG_DECLARE_BLK_BITMAPS
#undef MEM_CFG_GET_BITMAP_PTRS
#undef MEM_CFG_GET_GROUP_BITMAPS
#undef MEM_CFG_GET_BLOCK_SIZES
#undef MEM_CFG_GET_NUM_BLOCKS
#undef MEM_CFG_DEBUG_PRINT_POOL_ADDR
#undef MEM_CFG_DEBUG_GET_TOTAL_POOL_SIZES

#undef MEM_DEF

#endif
/* END OF FILE */

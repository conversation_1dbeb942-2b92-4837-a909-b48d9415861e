/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/3g.mod/lib/src/upde_rab.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/26 20:17:00 $
 **************************************************************************
 * File Description:
 *
 *      The UPDCP Entity - RAB.
 **************************************************************************/

#if !defined (UPDE_RAB_H)
#define UPDE_RAB_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

/***************************************************************************
* Manifest Constants
***************************************************************************/

/***************************************************************************
* Macro Functions
***************************************************************************/

/***************************************************************************
* Types
***************************************************************************/

/***************************************************************************
* Typed Constants
***************************************************************************/

/***************************************************************************
* Function Prototypes
***************************************************************************/

Boolean UpdcpSap (SignalBuffer *sigBuf);

Boolean UpdeReceiveRlcSdu (BearerIdentity  bearerIdentity, 
                           UrlBearerMode   bearerMode, 
                           UrlcSdu        *rlcSdu);

#endif

/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/3g.mod/lib/src/ulbgrabmdata.h#3 $
 *   $Revision: #3 $
 *   $DateTime: 2011/03/06 16:54:44 $
 **************************************************************************
 * File Description:
 *
 * ULBG RABM only data definition - this contains state information for each NSAPI
 * as well as global RABM variables
 **************************************************************************/

#if !defined (ULBGRABMDATA_H)
#define       ULBGRABMDATA_H

/**** NESTED INCLUDE FILES *************************************************/

#include <gkiqueue.h>
#include <gpsystem.h>
#include <gpnsapi.h>
#include <gpqos.h>
#include <rabstate.h>
#include <gpcntr.h>


/**** CONSTANTS ************************************************************/


/**** TYPEDEFS *************************************************************/

typedef struct UlbgCounterDataTag
{
    Int32   upLinkCompressedOctets;
    Int32   upLinkUncompressedOctets;
    Int32   upLinkPackets;
}
UlbgCounterData;

typedef struct UlbgRabmPdpEntityTag
{
    RabmState                stateNow;
    RabmState                stateNext;

    KiTimer                  deactGuardTimer;	/** Timer activated once the RB is inactive but uplink traffic arrives to ULBG */
    Boolean                  reestablishIndArrived;
    Boolean                  smActRspPending;
}
UlbgRabmPdpEntity;

#if defined (ENABLE_UPLANE_STATISTICS)
/* RABM statistics */
typedef struct UlbgRabmStatisticsTag
{
    Int16                       numUlPdusDiscarded; /** Number of UL PDUs discarded */
}
UlbgRabmStatistics;

/**** GLOBALS *************************************************************/

extern UlbgRabmStatistics ulbgRabmStatistics;
#endif /* ENABLE_UPLANE_STATISTICS */

#endif

/* END OF FILE */

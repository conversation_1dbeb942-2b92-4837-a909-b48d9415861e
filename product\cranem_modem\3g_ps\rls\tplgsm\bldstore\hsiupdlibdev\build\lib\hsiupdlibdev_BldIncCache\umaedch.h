/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/******************************************************************************
 * File Name:	umaedch.h
 *
 *
 * File Description: UMAC E-DCH header file
 *
 *****************************************************************************/

#if !defined (UMAEDCH_H)
#define       UMAEDCH_H

#if defined (UPGRADE_3G_EDCH)
/******************************************************************************
 * Include Files
 *****************************************************************************/

#define   UMACE_PLP_SUPPORTS_REF_ETPR_REPORTING //Enable reference eptr

//belong to shmem to be deleted
#define ETFCI_RESTRICTION_INVALID           0xFF
#define ETFCI_RESTRICTION_READ_BY_MACE      0xFE
#define MAX_ETCFCI_RESTRICTIONS             (7)

#include <stdio.h>
#include <string.h>
#include "gbl_types.h"
#include "pl_w_globs.h"
#include "pl_w_shared_memory.h"
#include "umacebase.h"
#include "umc_sig.h"
#include "umac_sig.h"
#include "umacerlcif.h"
#include "umacmaceif.h"
#include "umautl.h"
#include "diag.h"

/******************************************************************************
 * Constants
 *****************************************************************************/

#define UMACE_MAX_NUM_OF_HARQ_PROCESSES (8)
#define UMACE_MAX_NUM_OF_POWER_OFFSETS  MAX_NUM_OF_POWER_OFFSETS

//Serving Grant
#define UMACE_RELATIVE_GRANT_TABLE_SIZE (38) // Table 9.2.5.2.1.1 in 25.321
#define UMACE_GRANT_TABLE_SIZE (UMACE_RELATIVE_GRANT_TABLE_SIZE+2) //38 + INACTIVE + ZERO_GRANT which are defined in Table 16B in 25.212
#define UMACE_GRANT_MAX_GRANT           (37)
#define UMACE_ZERO_GRANT                (38)
#define UMACE_GRANT_INACTIVE            (39)

#define UMACE_SQR_A_TABLE_SIZE          (29)

#define UMACE_SCHED_INFO_BITLEN             (18)
#define UMACE_MACE_HDR_DDI_BITLEN           (6)
#define UMACE_MACE_HDR_PDUS_NUM_BITLEN      (6)
#define UMACE_MACE_HDR_TSN_BITLEN           (6)
#define UMACE_MACE_HDR_ELEMENT_BITLEN       (UMACE_MACE_HDR_DDI_BITLEN+UMACE_MACE_HDR_PDUS_NUM_BITLEN)
#define UMACE_MACES_HDR_BITLEN              (UMACE_MACE_HDR_DDI_BITLEN+UMACE_MACE_HDR_PDUS_NUM_BITLEN+UMACE_MACE_HDR_TSN_BITLEN)
#define UMACE_MAX_RLC_PDU_BITLEN            (5000)
#define UMACE_MAX_MACE_PDU_BITLEN           UPS_EDCH_MAX_TB_BITS// total size of mace pdu (20000 or 22996 in case if UL16QAM) 

#define UMACE_SCHEDULING_INFO_ONLY_ETFCI        (0)
#define UMACE_SCHEDULING_INFO_ONLY_MAX_RETRANS  (7) //25.321 11.8.1.4: a maximum number of HARQ transmissions of 8 will be used to define a HARQ profile for "Control-only" transmissions

#define UMACE_TIMER_DEACTIVATED                 (-1)
#if defined (UPGRADE_3G_UL_EL2) 
#define UMACI_MIN_RLC_PDU_SEGMENT_BITLEN            (32)
#define UMACI_MACIS_HDR_SS_BITLEN                   (2)
#define UMACI_MACIS_HDR_BITLEN                      (UMACI_MACIS_HDR_SS_BITLEN+UMACE_MACE_HDR_TSN_BITLEN)
#define UMACI_MACI_HDR_LCH_BITLEN                   (4)
#define UMACI_MACI_HDR_L_BITLEN                     (11)
#define UMACI_MACI_HDR_F_BITLEN                     (1)
#define UMACI_MACI_HDR_PER_MAC_IS_SDU_BITLEN        (UMACI_MACI_HDR_LCH_BITLEN+UMACI_MACI_HDR_L_BITLEN+UMACI_MACI_HDR_F_BITLEN)
#define UMACI_MAX_SEGMENTED_PDU_SIZE_BYTES          (1000) /* Should match the maximum RLC pdu size */
#define UMACI_CRC_LENGTH_BYTES                      (1)
#define UMACI_MAX_MACIS_SDUS_PER_TTI                (36)

#if defined (UPGRADE_UL_ECF)
#define UMACI_PERSISTENCE_SCALING_FACTOR_FACTOR     ((Int16)(10))
#define UMACI_DYNAMIC_PERSISTENCE_LEVEL_FACTOR      ((Int16)(128))
#define UMACI_DEFAULT_PERSISTENCE_SCALING_FACTOR    UMACI_PERSISTENCE_SCALING_FACTOR_FACTOR
#define UMACI_ERNTI_LCH_ID_BITLENGTH                (4)
#define UMACI_ERNTI_BITLENGHT                       (16)
#define UMACI_ERNTI_HEADER_IN_MACI_PDU              (UMACI_ERNTI_LCH_ID_BITLENGTH+UMACI_ERNTI_BITLENGHT+4/*Reserved*/)
#define UMACI_ECF_CCCH_RADIO_BEARER                 (RB_0)
#define UMACI_ECF_INFINITY_BACKOFF_TIMER            (0xFF)
#endif
#endif
/******************************************************************************
 * Macros
 *****************************************************************************/
#define UMACE_MAX(a,b)      ((a)>(b)?(a):(b))
#define UMACE_MIN(a,b)      ((a)<(b)?(a):(b))
#define UMACE_MAX_PRIO(a,b) ((a)<(b)?(a):(b))

#define UMACE_GET_TX_REQ_ID(confid,procid)      (UmacEdchTxReqId_t)(confid*8+procid)
#define UMACE_GET_CONFIG_FROM_TX_REQ_ID(reqId)  &(umaEdchDb_g.config[reqId>>3])
#define UMACE_NO_RLC_TX_REQ_ID  (0x1F) // 5 bits 0-15 for req id 1f no req == SI only data

//#define UMACE_HARQ_GET_MAX_NUM_OF_PROCESSES(edchTti ) (edchTti == UE_DCH_TTI_tti2 ? 8:4)
#define UMACE_SG_GET_TTI_COUNT_FOR_TIMEOUT UMACE_HARQ_GET_MAX_NUM_OF_PROCESSES
#define UMACE_GET_LOG_CH_FROM_RB(c,rbId) (UMAC_READ_RB_LOG_CH(rbId) == UMAC_RLC_LOG_CH_DATA)? \
    c->controlConfig.umaETfcRbMappings[rbId] :\
    c->controlConfig.umaETfcRbCtrlMappings[UMAC_READ_RB_IDENTITY_ENUM(rbId)]
#define UMACE_GET_MACE_PDU_BUFF_ADDR(c) (UINT8*)(c->shareMemoryPduArray_pp[umaEdchDb_g.harq.UmacEHarqCurrentProcess])

//standard bitmask are left to right ( mask is byte)
#define UMACE_BITMSK_IS_BIT_SET(msk,bitno) (msk &((0x80L)>>bitno ))

#define UMACE_SET_TIMER(timerCode, ticks) \
    umaEdchDb_g.timerCnt[timerCode] =  (SignedInt32)(ticks)

#define UMACE_DEACTIVATE_TIMER(timerCode) \
    umaEdchDb_g.timerCnt[timerCode] =  UMACE_TIMER_DEACTIVATED

#define UMACE_IS_TIMER_ACTIVE(timerCode) \
    (umaEdchDb_g.timerCnt[timerCode] !=  UMACE_TIMER_DEACTIVATED)

#define UMACE_IS_TIMER_EXPIRED(timerCode) \
    (umaEdchDb_g.timerCnt[timerCode] ==  0)

#define UMACE_IS_TIMER_NOT_EXPIRED(timerCode) \
    (umaEdchDb_g.timerCnt[timerCode] >  0)

#define UMACE_GET_TIMER_VALUE(timerCode) \
    (umaEdchDb_g.timerCnt[timerCode] )

#if defined (UPGRADE_3G_UL16QAM)
#define UMACE_MAX_MACE_PDU_BILTEN_GIVEN_TTI(edchTfcsConfigReq_p) \
	((edchTfcsConfigReq_p->edchTti == UE_DCH_TTI_tti10)? (20000u) : ((edchTfcsConfigReq_p->use16QAM == TRUE)? (22996u) : (11484u)))
#else
#define UMACE_MAX_MACE_PDU_BILTEN_GIVEN_TTI(edchTfcsConfigReq_p) \
	((edchTfcsConfigReq_p->edchTti == UE_DCH_TTI_tti10)? (20000u) : (11484u))
#endif


#define UMACE_DEBUG_ACCUMULATE_TRACE_INFO(traceIndx, debugData) \
    umaEdchDb_g.debugEdch.umacEdchDebugRecord[umaEdchDb_g.debugEdch.processIndex].flowTrace[traceIndx] = debugData

 /******************************************************************************
 * Type Definitions
 *****************************************************************************/

/* L1 SHARE MEMORY IF IMAGE   DEFINITIONS AND DATA TYPES */

//ICAT EXPORTED ENUM
typedef enum HichStatusTag
{
    HS_NACK  = HICH_NACK_ALL,           // = 1,
    HS_NSACK = HICH_ACK_NACK_SERV_RLS,  // = 2,
    HS_SACK  = HICH_ACK_ACK_SERV_RLS    // = 3
} HichStatus;
//ICAT EXPORTED ENUM
typedef enum GrantStatusTag
{
    GS_HOLD = TX_RGCH_HOLD,
    GS_DOWN = TX_RGCH_DOWN,
    GS_UP   = TX_RGCH_UP
} GrantStatus;

//ICAT EXPORTED ENUM
typedef enum ErntiCrcTag
{
    EC_NONE      = 0,
    EC_PRIMARY   = 1,
    EC_SECONDARY = 2
} ErntiCrc;

//ICAT EXPORTED ENUM
typedef enum AbsGrantScopeTag
{
    AG_SCOPE_ALL_PROCESSES   = AGS_ALL_PROCESSES,   //according to 212 4.10.1a.2
    AG_SCOPE_CURRENT_PROCESS = AGS_CURRENT_PROCESS
} AbsGrantScope;

//ICAT EXPORTED STRUCT
typedef struct UmacEL1RxTag
{
    HichStatus hichStatus;
    UmacCfnSubframe cfnSubframe;
    ErntiCrc        erntiCrc;  // 0 -none 1= primary 2= secondary  is 3 legal
    AbsGrantScope   absoluteGrantScope;  //valid if erntiCrc != 0
    Int32           absoluteGrantValue;  //valid if erntiCrc != 0  share mem value  is 0-31 to be enlarge to 0-37.
    GrantStatus     servingRlsGrantStatus;
    GrantStatus     nonServingRlsGrantStatus;
    Int32           reference_ETPR; //?It is assumed DSP set the value.
    etfcRestrictionDspResults   etfcRestrictionResults;
    etfcRestrictionControl_ts   etfcRestrictionControl;
    Int8 leRefIndex [MAX_REFERENCE_ETFCI_LENGTH]; //0,1,2,4  indexes to   1,2,4,6, F  the reference indexes are as in configuration
}UmacEL1Rx;

/******************************/
//Harq

/* Sceduled Information Data types */

typedef enum UmacEHarqProcessSiPresenceTag
{
    UMACE_HARQ_DATA_WITH_TRIG_SI,       /* Data with triggered SI */
    UMACE_HARQ_DATA_WITHOUT_TRIG_SI,    /* Data without triggered SI */
    UMACE_HARQ_SI_ONLY                  /* SI only */
} UmacEHarqProcessSiPresence;


/* HARQ DEFINITIONS AND DATA TYPES */
typedef enum UmacEHarqTxStatusTag
{
    UMACE_HARQ_TX_SUCCESSFULL,
    UMACE_HARQ_TX_FAIL,
    UMACE_HARQ_TX_RETRANSMITTING,
    UMACE_HARQ_TX_DTX /* purpusly No TX */
} UmacEHarqTxStatus;

typedef struct UmacEHarqProcessTag
{
    Boolean                     active;             /* TRUE, FALSE - set by SGU */
    UmacEHarqProcessSiPresence  siTriggeredPresence;/* noSI, onlySI, dataSI */
    Int32                       maxRetrans;         /* 0 - 15 */
    Int32                       currentRsn;         /* 0 - 3 */
    Int32                       cntRetrans;         /* counter for retransmission */
    Int32                       processIndex;       /* The process index set in initialization */
    Int32                       scheduledDataBits;  /* number of scheduled data bits sent on this process, used by SGU */
    /* data that should be saved for retransmittion */
    Int16                       trBkSize;           /* 0 means that there are no bits to be transmitted */
    Int8                        etfci;              /* 0..127 */
    Int8                        deltaHarq;          /* 0 - 6 */
    /* other data to be used in next tx on this process */
    processState_te             processStateInPreviousCycle;  /* to be used for reference etpr */

    /* The E-DPDCH to DPCCH power ratio used as reference for non serving relative
     * grant commands. This variable is set to the previously stored reference_ETPR
     * on this HARQ process when the reference_ETPR is updated with a new value */
    Int32                       referenceEtpr2;

#ifndef UMACE_UNIT_TEST
#ifndef  UMACE_PLP_SUPPORTS_REF_ETPR_REPORTING
    Int8                        lstSg;
#endif
#endif
} UmacEHarqProcess;

typedef struct UmacEHarqTag
{
    UmacEHarqProcess    harqProcesses [UMACE_MAX_NUM_OF_HARQ_PROCESSES];
    Int32               UmacEHarqCurrentProcess;
    Int32               UmacEHarqNumOfActiveProcesses;
    UmacEHarqProcess    *UmacEHarqCurrentProcess_p;
} UmacEHarq;

/******************************/
//Serving Grant

//ICAT EXPORTED STRUCT
typedef struct UmacEServingGrantTag
{
    Int32   storedSecondaryServingGrant;
    Int32   currentServingGrant;
    /* The value in the currentServingGrant index of the SG table */
    Int32   currentServingGrantValue;
    Int32   maxServingGrant;
    Boolean primaryGrantAvailable;
} UmacEServingGrant;

//ICAT EXPORTED ENUM
typedef enum CmacEdchMacDFlowTxGrantTypeTag
{
    CMAC_EDCH_NON_SCHEDULED = 0,
    CMAC_EDCH_SCHEDULED
}CmacEdchMacDFlowTxGrantType;

 //ICAT EXPORTED STRUCT
typedef struct UmacEdchMacDFlowInfoTag
{
   Int8                         flowId;                         /* 0 - 7 */
   Int8                         powerOffset;                    /* 0 - 6 [dB] */
   Int8                         maxRetrans;                     /* 0 - 15 */
   Int8                         muxList;
   CmacEdchMacDFlowTxGrantType  txGrantType;                    /* scheduled / non scheduled */
   Int16                        nonSchedMaxMacEPduContentsSize; /* 1 - 19982 */
   Int8                         harqProcessAllocation2ms;
   Int8                         logicalChannelsNumber;          /* 1 - 15 */
   struct UmacEdchLogicalChInfoTag  *logicalChannels [UMAC_EDCH_MAX_LOGICAL_CHANNELS];
   /* Run time parameters used for E-TFC Selection */
   Int32                        remainingNonScheduledPayload;
}UmacEdchMacDFlowInfo;

 //ICAT EXPORTED STRUCT
typedef struct UmacEdchLogicalChInfoTag
{
   ULogicalChannelIdentity          logicalChannelIdentity; /* 1 - 15 */
   BearerIdentityWithCtrl           rbIdentityWithCtrl;
   UMAC_LogicalChannelPriority      logicalChPriority;      /* 1 - 8 */
   Int8                             ddi;                    /* 0 - 62 */
   Int32                            rlcPduSizeListLength;   /* 1 - 32 */
   Int16                            rlcPduSizeList [32];
   Boolean                          includeInSchedulingInfo;
   UmacEdchMacDFlowInfo             *edchMacDFlow_p;
#if defined (UPGRADE_3G_UL_EL2) 
    Boolean                         flexibleRlcPduSize;
    Int16                           minimumRlcPduBits;
    Int16                           maximumRlcPduBits;
#endif
} UmacEdchLogicalChInfo;

typedef struct UmacEdchControlConfigTag
{

    CmacEdchTfcsConfigReq   edchTfcsConfigReq;
    Int32                   numberOfLogicalChannels;
    UmacEdchLogicalChInfo   *logicalChanInfo[UMAC_EDCH_MAX_LOGICAL_CHANNELS];
    Int32                   numberOfMacDFlows;
    UmacEdchMacDFlowInfo    *macDFlowInfo[maxE_DCHMACdFlow];
    UmacEdchLogicalChInfo   *umaETfcRbMappings[maxRB];
    UmacEdchLogicalChInfo   *umaETfcRbCtrlMappings[maxRB];
    Boolean                 isUsedDeltaHarq[UMACE_MAX_NUM_OF_POWER_OFFSETS];
} UmacEdchControlConfig;

// Configuration states
//ICAT EXPORTED ENUM
typedef enum  configurationStateEnum
{
    UMACE_UNDEFINED=0 ,
    UMACE_CONFIGURED ,
    UMACE_ACTIVE_SGU_ONLY ,
    UMACE_ACTIVE_NO_HARQ,
    UMACE_ACTIVE_NORMAL,
} UmacEConfigurationState;

//Configuration
#define WAIT_COUNT_IDX_ACTIVE_SGU_ONLY  0
#define WAIT_COUNT_IDX_ACTIVE_NO_HARQ   1
#define MAX_WAIT_COUNT                  2

typedef struct UmacEConfigurationTag
{
    ActivationTime                          activationTime;
    UmacEConfigurationState                 state;
    UmacEdchControlConfig                   controlConfig;
    Boolean TxNotAckedNacked [UMACE_MAX_NUM_OF_HARQ_PROCESSES];  /* indication for TX not Acked or Nacked  pdu's */
    UmacEdchBearerSortedByLogChanPriority   bearerSortedByLogChanPriority;                      /* Input for RLC */
    Int8                                    timerId;                    /* May be 0 or 1 build at initialization */
    Int8                                    waitCount[MAX_WAIT_COUNT];   /* Depend on TTI built in configuration */
    Int8                                    maxNumberOfProcesses;
    Int32                                   delayUserDataInTtis;
    UINT8                                   **shareMemoryPduArray_pp;
    dlDataReportFormat_ts                   **shareMemoryRxArray_pp;

} UmacEConfiguration;

typedef struct UmacEtfcSelectResultPerLogChTag
{
    UmacEdchLogicalChInfo     * logicalChInfo_p;
    Int32                       numberOfRlcPdus;
    Int32                       rlcPduSizeInBits;
}UmacEtfcSelectResultPerLogCh;

typedef struct UmaceEtfcSelectResultTag
{
    UmacEtfcSelectResultPerLogCh    logChResults[UMAC_EDCH_MAX_LOGICAL_CHANNELS];
    Boolean                         addDdi0;
} UmaceEtfcSelectResult;

#if defined (UPGRADE_3G_UL_EL2) 
//ICAT EXPORTED ENUM
typedef enum
{
    SEG_STATUS_1ST_COMPLETE_LAST_COMPLETE_00  = 0,
    SEG_STATUS_1ST_LAST_SEG_LAST_COMPLETE_01  = 1,
    SEG_STATUS_1ST_COMPLETE_LAST_FIRST_SEG_10 = 2,
    SEG_STATUS_1ST_LAST_SEG_LAST_FIRST_SEG_11 = 3
} UmacEdchSegmentationStatus;

 //ICAT EXPORTED STRUCT
typedef struct UmaciEtfcSelectResultPerLogChTag
{
    UmacEdchLogicalChInfo         * logicalChInfo_p;
    UmacEdchSegmentationStatus      segmentationStatus;
    Int16                           totalSegmentedPduSizeBits;
    Int32                           numberOfRlcPdus;
    Int32                           rlcPdusSizeInBits[UMACI_MAX_MACIS_SDUS_PER_TTI];
}UmaciEtfcSelectResultPerLogCh;

 //ICAT EXPORTED STRUCT
typedef struct UmaciEtfcSelectResultTag
{
    Int32                           totalNumberOfSegmentedNewRlcPdus;
    Int32                           numLogCh;
    UmaciEtfcSelectResultPerLogCh   logChResults[UMAC_EDCH_MAX_LOGICAL_CHANNELS];
} UmaciEtfcSelectResult;

typedef struct UmaciSegmentationBufferInfoTag
{
    Int16               totalPduBits;
    Int16               remainPduBits;
    Int16               nextPduBitOffset;
    Int8              * pduBuffer_p;
} UmaciSegmentationBufferInfo;

typedef struct UmacISegmentationDbTag
{
    /* For each configured logical channel, hold a segmentation entity for MACI only */
    UmaciSegmentationBufferInfo     *bufferInfo_p[UMAC_EDCH_MAX_LOGICAL_CHANNELS];
} UmacISegmentationDb;
#endif

#if defined (UPGRADE_UL_ECF)

typedef struct UmacIEcfDbTag
{
    Boolean                         isCcchTransmission;
    Boolean                         addErnti;
    UE_RNTI                         eRnti;
    Int8                            transContBackOffTimer;
} UmacIEcfDb;

#endif

typedef struct UmacEtfcSelectResultTag
{
    Int32                           totalNumberOfRlcPdus;
    Boolean                         changeInternalPrioRequired[0xFF];
    UmaceEtfcSelectResult           maceEtfcSelectResult;
#if defined (UPGRADE_3G_UL_EL2) 
    UmaciEtfcSelectResult           maciEtfcSelectResult;
#endif
} UmacEtfcSelectResult;


typedef struct UmacEtfcsDbTag
{
    UmacEtfcSelectResult        etfcSelectResult;
    UMAC_LogicalChannelPriority rbPriority[0xFF];
}UmacEtfcsDb;


typedef enum  UmacEDtxReasonEnum
{
    UMACE_DTX_REASON_UNDEFINED              = 0,
    UMACE_DTX_REASON_COMPRESSED_GAP,
    UMACE_DTX_REASON_ACTIVE_SGU_ONLY,
    UMACE_DTX_REASON_NO_MAC_DATA_P,
    UMACE_DTX_REASON_DCH_NOT_READY_TO_SEND,
    UMACE_DTX_REASON_DELAYED_EDCH_SIG,
    UMACE_DTX_REASON_ETFC_SELECTION_FAILED,
    UMACE_DTX_REASON_NO_RLC_TRAFFIC_REQ,
    UMACE_DTX_REASON_CPC_COMPRESSED_GAP,
    UMACE_DTX_REASON_CPC_PREAMBLE_DELAY,
    UMACE_DTX_REASON_CPC_PREAMBLE_TRANSMISSION,
    UMACE_DTX_REASON_CPC_DTX_CYCLE_DELAY,
    UMACE_DTX_REASON_CPC_DTX_DISABLE_RETRANSMISSION
#if !defined (PS_L2_R8_API)
    ,UMACE_DTX_REASON_CPC_LATE
#endif
#if defined (UPGRADE_UL_ECF)
    ,UMACE_DTX_REASON_ECF_WAIT_BACKOFF_TIMER,
    UMACE_DTX_REASON_ECF_COMMON_EDCH_RES_RELEASE,
    UMACE_DTX_REASON_ECF_NOT_VALID_STATE,
    UMACE_DTX_REASON_ECF_INACTIVE_EAGCH_AGVAL_RX,
    UMACE_DTX_REASON_MAX_REASON
#endif
} UmacEDtxReason;
/* CPC database */

typedef enum  UmacECpcCycleNumTag
{
    UMACE_CPC_CYCLE_1 = 0,
    UMACE_CPC_CYCLE_2 = 1,
    UMACE_CPC_NUM_CYCLES
} UmacECpcCycleNum;


typedef enum  UmacECpcPreambleLengthTag
{
    UMACE_CPC_PREAMBLE_LENGTH_0_TTI = 0,
    UMACE_CPC_PREAMBLE_LENGTH_1_TTI = 1,
    UMACE_CPC_PREAMBLE_LENGTH_5_TTIS = 5
} UmacECpcPreambleLength;


typedef struct UmacECpcDbTag
{
    /** Current CPC cycle, as read from shmem */
    UmacECpcCycleNum        cycle;
    /** DTX Indication   */
    Boolean                 inDtx;
    /** Long preamble In Ttis, constant per configuration. */
    UmacECpcPreambleLength  cycle2PreambleLengthInTtis;
    /** Track the mac inactivity threshold in ttis   */
    Int16                   macInactivityTtiCnt;
    /** Track the grant monitor inactivity threshold time and instruct
    * DSP to receive Grants by setting the GRANT_MONITORING
    * flag in the shared memory.         */
    Int16                   grantMonitorInactivityTtiCnt;
    /** For 2msTTI only. Indicates that the data is ready for
    * transmission but is delayed due to CM gap.  */
    Bool                    compressedModeDelay;
    /** For debug */
    UmacEDtxReason          cpcDtxReason;
}UmacECpcDb;


/* Happy Bit Setting database */
typedef struct UmaEHappyBitDbTag
{
    Boolean isServingGrantFullyUsed;
    Int32   servingGrantPayload;
    Int32   minReqRlcPduSize;
    Int32   delayConditionTti;
    Int32   maxReqRlcPduSizeWithData;
}UmaEHappyBitDb;

/* Scheduling Information database */
typedef struct UmacESiHandlerDbTag
{
    Int32       uph;            /* UE Power Headroom, the ratio of the maximum UE
                                 * transmission power and the corresponding DPCCH
                                 * code power */
    Int32       tebsBits;       /* Amount of data available for transmission and
                                 * retransmission in RLC and higher layers (bits) */
    Int32       rlcTebsBits;    /* Amount of data available for transmission and
                                 * retransmission in RLC layer (bits) */
    Int32       higherLayerTebsBits; /* Amount of data available for transmission in the
                                      * higher layers */
    Int32       highestPrio;    /* Highest priority value */
    Int32       si;             /* Scheduling Information */
    Boolean     isSiTxTriggered;
    Boolean     isSiTxPossible;
    Boolean     servingRlsSiNack;
    Boolean     servCellChanged;/* TRUE If an E-DCH serving cell change occurs and
                                 * the new E-DCH serving cell was not part of the
                                 * previous Serving E-DCH RLS */
    /** true if SG is too small to allow transmition of a single PDU on any scheduled MAC-d flow */
    Boolean     isSgTooSmallToAllowPduTransmit;
    Boolean     isPrevTtiSiTxPending;
}UmacESiHandlerDb;

typedef struct UmaEReleaseTag
{
    Boolean                 releaseFlag;
    UmacCfnSubframe         releaseActivationTime;
}UmaERelease;

typedef struct UmaECfnManagmentDataTag
{

    UmacCfnSubframe         curCfnSubframe;
    Int8                    startFlag;
    Boolean                 cfnGapDetected;
    Boolean                 initialCfnSet;
}UmaECfnManagmentData;

#define UMACE_MAX_CONFIGURATIONS (2)
#define AG_TIMER                    UMACE_MAX_CONFIGURATIONS    /* Absolut Grant timer */
#define NSRS_TIMER                  UMACE_MAX_CONFIGURATIONS+1  /* None serving Relative grant timer */
#define SI_NO_GRANT_TIMER           UMACE_MAX_CONFIGURATIONS+2  /* SI No Grant timer */
#define SI_GRANT_TIMER              UMACE_MAX_CONFIGURATIONS+3  /* SI Grant timer */
#define USER_DATA_INTERRUPT         UMACE_MAX_CONFIGURATIONS+4  /* User data interruption */
#define CPC_PREAMBLE_END_TIMER      UMACE_MAX_CONFIGURATIONS+5  /* CPC preamble end timer */
#if !defined (UPGRADE_UL_ECF)
#define MAX_EDCH_TIMERS     		UMACE_MAX_CONFIGURATIONS+6
#else
#define ECF_BACKOFF_TIMER           UMACE_MAX_CONFIGURATIONS+6  /* ECF Backoff timer */
#define ECF_TB_TIMER                UMACE_MAX_CONFIGURATIONS+7
#define MAX_EDCH_TIMERS     		UMACE_MAX_CONFIGURATIONS+8
#endif

#define EDCH_ERROR_NOERROR   (0)
//errors are implemented as bitmask.

#define	EDCH_ERROR_MSK_RX_AGCH              (0x1)
#define EDCH_ERROR_MSK_RX_RGCHS             (0x2)
#define EDCH_ERROR_MSK_RX_RGCHNS            (0x4)
#define EDCH_ERROR_MSK_RX_HICH              (0x8)
#define EDCH_ERROR_MSK_CFN_NOT_FIT          (0x10)
#define EDCH_ERROR_MSK_DELAYED_SIGNAL       (0x20)
#define EDCH_ERROR_MSK_NO_MAC_DATA          (0x40)
#define EDCH_ERROR_MSK_NO_WORK_CONFIG       (0x80)
#define EDCH_ERROR_MSK_NO_RESTRICTIONS      (0x100)
#ifndef UMACE_UNIT_TEST 
  #ifndef  UMACE_PLP_SUPPORTS_REF_ETPR_REPORTING
	#define EDCH_ERROR_MSK_REF_ETPR_PATCH (0x200)
	#define EDCH_ERROR_MSK_REF_ETPR_PATCH_INIT (0x400)
  #endif
#endif

#define UMACE_DEBUG_BIT_0       0x00000001
#define UMACE_DEBUG_BIT_1       0x00000002
#define UMACE_DEBUG_BIT_2       0x00000004
#define UMACE_DEBUG_BIT_3       0x00000008
#define UMACE_DEBUG_BIT_4       0x00000010
#define UMACE_DEBUG_BIT_5       0x00000020
#define UMACE_DEBUG_BIT_6       0x00000040
#define UMACE_DEBUG_BIT_7       0x00000080
#define UMACE_DEBUG_BIT_8       0x00000100
#define UMACE_DEBUG_BIT_9       0x00000200
#define UMACE_DEBUG_BIT_10      0x00000400
#define UMACE_DEBUG_BIT_11      0x00000800
#define UMACE_DEBUG_BIT_12      0x00001000
#define UMACE_DEBUG_BIT_13      0x00002000
#define UMACE_DEBUG_BIT_14      0x00004000
#define UMACE_DEBUG_BIT_15      0x00008000
#define UMACE_DEBUG_BIT_16      0x00010000
#define UMACE_DEBUG_BIT_17      0x00020000
#define UMACE_DEBUG_BIT_18      0x00040000
#define UMACE_DEBUG_BIT_19      0x00080000
#define UMACE_DEBUG_BIT_20      0x00100000
#define UMACE_DEBUG_BIT_21      0x00200000
#define UMACE_DEBUG_BIT_22      0x00400000
#define UMACE_DEBUG_BIT_23      0x00800000
#define UMACE_DEBUG_BIT_24      0x01000000
#define UMACE_DEBUG_BIT_25      0x02000000
#define UMACE_DEBUG_BIT_26      0x04000000
#define UMACE_DEBUG_BIT_27      0x08000000
#define UMACE_DEBUG_BIT_28      0x10000000
#define UMACE_DEBUG_BIT_29      0x20000000
#define UMACE_DEBUG_BIT_30      0x40000000
#define UMACE_DEBUG_BIT_31      0x80000000

#define SETMSK(v,a) (v = v|a)
//This should be include here becase it get some definitions from previously included files.
#include "umaedebuginfo.h"

typedef struct UmacEdchDebugInternalTag
{
    Int16 etfcRestrictionBlockSize;

} UmacEdchDebugInternal;



/* Global DB abstract data type */
typedef struct UmacEdchDbTag
{
    Int32                   error; /* 0 -no error otherwise according bitmask above */
    SignedInt32             timerCnt [MAX_EDCH_TIMERS];
    UmacEL1Rx               l1Rx;
    UmacEConfiguration*     currentConfig_p;
    UmacEConfiguration*     nextConfig_p;
    UmacEConfiguration*     workingConfig_p;
    UmacEConfiguration      config[UMACE_MAX_CONFIGURATIONS];
    UmaERelease             release;
    plwPhyEdchTtiInfo_ts    phyEdchTtiInfo;
    UmaECfnManagmentData    cfnManagmentData;
    UmacUlInfoForMacE*      macData_p;
    UmacEHarq               harq;
    UmacEtfcsDb             etfcsDb;
    UmacEServingGrant       servingGrant;
    UmaEHappyBitDb          happyBitDb;
    UmacESiHandlerDb        siDb;
#if defined (UPGRADE_3G_UL_EL2) 
    UmacISegmentationDb     segmentationDb; /* Mac-I only */
#if defined (UPGRADE_UL_ECF)
    UmacIEcfDb              ecfDb;          /* Mac-I only */
#endif
#endif
	edchSharedMemAddrInd_ts sharedMemAddresses;
	Boolean					doNotInitTsn;//if TRUE do not init the TSN counters 25331_CR2773R1_(Rel-6)_R2-061772.doc
    Int32                   dspRestrictionInt32[2];
    UmacEDtxReason          dtxReason;
    UmacECpcDb              cpcDb;
    UmacEdchDebug           debugEdch;
    UmacEdchDebugInternal   debugEdchInternal;
}UmacEdchDb;



/******************************************************************************
 * Function Prototypes
 *****************************************************************************/
/* EDCH Control */
void ProcessEdchSignal (SignalBuffer * rxSignal_p);
/* END DECH Control */

/* SI */
void UmaESiInit(void);
void UmaESiConfigChanged(UmacEConfiguration* config_p);
void UmaESiAnalyzeRlcRequestParams(UmacEConfiguration* config_p,UmacEdchTrafficReq  *edchTrafficReq_p);
void UmaESiCalculate(UmacEConfiguration* config_p,UmacEdchTrafficReq  *edchTrafficReq_p);
Boolean UmaESiIsTxTriggered(UmacEConfiguration* config_p);
/* End SI */

/* MUX */
void UmaEMuxInitSession(UmacEConfiguration *config_p);
void UmaEMuxReleaseSession(void);
void UmaEMuxResetSession(void);
void UmaEMuxResetUnconfiguredTsn(UmacEdchControlConfig * ctrlConfig_p);
dtcTransferReq_ts *UmaEMultiplexing(UmacEConfiguration *config_p, UmacEtfcSelectResult *etfcSelectResult_p, UmacEdchTxReqId_t rlcTxReqId);
#if defined (UPGRADE_3G_UL_EL2) 
dtcTransferReq_ts *UmaIMultiplexing(UmacEConfiguration *config_p, UmacEtfcSelectResult *etfcSelectResult_p, UmacEdchTxReqId_t rlcTxReqId);
#endif
/* End MUX */

/* HARQ */
void UmaEHarqInitialization (void );
void UmaEHarqReset (void);
UmacEHarqTxStatus UmaEHarq (UmacEConfiguration* config_p);
Int32 UmaEHarqGetNumberOfActiveProcesses (void);
void UmaEHarqActivateAllProcesses(UmacEConfiguration* config_p,Boolean bActive);
void UmaEHarqActivateProcess(UmacEConfiguration* config_p, Boolean bActive, Int32 processNumber);
Boolean  UmaEHarqIsCurrentHarqProcessActive(void);
void UmaEHarqInitializeAllProcessesRefEtpr2(Int32 referenceEtpr2);
/* END HARQ */
//

/* SERVING GRANT UPDATE */
void UmaESguReset(void );
void UmaESguUpdate(UmacEConfiguration* config_p);
void UmaESguTTIChange(UmacEConfiguration* config_p);
Boolean UmaESguConfigure(UmacEConfiguration* config_p);
void UmaESguStartOfEdchTx(UmacEConfiguration* config_p);
void UmaESguServingCellChange(UmacEConfiguration* config_p);
/* END SERVING GRANT */

#if defined (UPGRADE_UL_ECF)
/* ECF */
void UmaciEdchEcfInit(void);
void UmaciEdchHandleRachConfigReq(SignalBuffer *sigbuf_p);
void UmaciEdchHandleEdchTrafficReq(void);
void UmaciEdchHandlePhyAccessCnf(SignalBuffer *sigbuf_p);
void UmaciEdchHandlePhyEdchCommonResourceRelInd(void);

void UmaciEdchEagchWithUeRntiReceived(void);
void UmaciEdchEagchInactiveReceived(void);
void UmaciEdchHandleCommonEdchResReleaseReq(void);
void UmaciEdchEcfTimerExpired(Int32 userValue);
void UmaciEdchEcfConfigChanged(UmacEConfiguration* config_p);
Boolean UmaceEdchEcfNewTtiIsTxAvailable(UmacEDtxReason *dtxReason_p);
void UmaceEdchEcfTtiTransmissionEnded(void);
Boolean UmaceEdchEcfIsSiTriggered(Int32 totalNumberOfRlcPdus,Int32 paddingBits);
void UmaciEdchHandleCmacAccessCnf(SignalBuffer *sigbuf_p);
#endif

/*EDCH */
// Timers
void UmaEDchTickTimer(Int32 timerCode);// Should be called each TTI
void UmaEDchTimersTick(void);
void UmaEDchResetTimers(void);
void UmacEDchInitialization(void);

void UmaEtfcConfigure(UmacEConfiguration* config_p);
void UmaETfcSelection(UmacEConfiguration *config_p,UmacEdchTrafficReq  *edchTrafficReq_p, Boolean isRetransmission);
void UmaEHappyBitSetting(UmacEConfiguration *config_p);

// read share memory
void UmaEdchWriteRx(dlDataReportFormat_ts *df);



/******************************************************************************
*
* Function:     UmaEdchReadEtfcRestrictionDspResults
*
* Scope:        Global
*
* Parameters:  etfcRestrictionDspResults_p pointer to input buffer contain etfcRestrictionDspResults
*              l1Rx_p pointer to UmacEL1Rx structure
*
* Returns:      TRUE if restriction changed
*
* Description:  Copy the input to etfcRestrictionResults field of the output.
*
*****************************************************************************/
Boolean  UmaEdchReadEtfcRestrictionDspResults(etfcRestrictionDspResults*  etfcRestrictionDspResults_p,UmacEL1Rx* l1Rx_p);


/******************************************************************************
*
* Function:     UmaEdchReadEtfcRestrictionControl
*
* Scope:        Global
*
* Parameters:  etfcRestrictionControl_p pointer to input buffer contain etfcRestrictionControl data
*              l1Rx_p pointer to UmacEL1Rx structure
*
* Returns:      None
*
* Description:  Copy the input to etfcRestrictionControl field of the output.
*
*****************************************************************************/
Boolean UmaEdchReadEtfcRestrictionControl(etfcRestrictionControl_ts*  etfcRestrictionControl_p,UmacEL1Rx* l1Rx_p);


/******************************************************************************
 *
 * Function:        UmaEdchTransmitInitialize
 *
 * Scope:       Local
 *
 * Parameters:
 *  in:             None
 *
 * Returns:         None.
 *
 * Description:     initialization procedure for each transmission
 * assume by default that we're happy with the current grant:

 *****************************************************************************/
#define  UmaEdchTransmitInitialize() \
    umaEdchDb_g.happyBitDb.isServingGrantFullyUsed = FALSE;\
    umaEdchDb_g.phyEdchTtiInfo.happyBit = TRUE;


Int32  UmaEdchReadRx(dlDataReportFormat_ts*  dlDRF_p, UmacEL1Rx* l1Rx_p);
void UmaEdchClearSharedMemValidityBits(dlDataReportFormat_ts *dlDRF_p);

//umedchTx
void UmaEPhyEdchSetDataReadyInd (void);
void UmaEdchSetDtx (UmacEConfiguration* config_p, UmacEDtxReason dtxReason);
Boolean UmaEdchIs2msCompressedGap (UmacEConfiguration* config_p);
Boolean UmaEdchTransmit(UmacEConfiguration* config_p);
void UmaEdchRetransmission(UmacEConfiguration* config_p);
void UmaEdchMarkProcessAsTx(UmacEConfiguration* config_p,Boolean isTx);
void UmaECallRlcEdchUpdateTrafficInd (UmacEdchBearerSortedByLogChanPriority *umacEdchBearerSortedByLogChanPriority_p);
void UmacEdchTxStatusIndToRlc(UmacEdchTxReqId_t edchTxReqId);
void UmaEdchSetRetransmission (UmacEConfiguration* config_p);
void ProcessEdchSignal (SignalBuffer * rxSignal_p);
void UmaEtfcSetSgTable(Boolean is16QAM);

#if defined(ON_PC)
void SetDataReadyEvent(void);
void UmaEdchDebugUpdateDdiInfo(UmacEdchControlConfig *controlConfig_p);
void UmaEdchDebugCheckOutputBuffer();
#endif

#if defined (UMACE_UNIT_TEST)
void StubUpdateDdiInfo(UmacEdchControlConfig *controlConfig_p);
void StubHandleUnitestUmacDataInd(UnitestUmaceDataInd *unitestUmaceDataInd);
void StubSendSessionInitSignal(void);
void StubSendSessionReleasedSignal(void);
void StubSendCheckUnitestOutputSignal(void);
#endif /* UMACE_UNIT_TEST */

void UmaEtfcPrepareEtfcLookupTables(UmacEConfiguration* config_p);
Int32 UmaEtfcGetSchedulingGrantValueByIndex(Int32 idx);
Int32 UmaEtfcGetSchedulingGrantIndexByValue(Int32 SearchValue);
Int8 GetEtfcTableSize(void);
/* Debug info accumulation functions */
void UmaEdchDebugAccumulateCfnInfo(Int32 processIndex, Int32 tickCounter);
void UmaEdchDebugAccumulateShmemInfo(
        Int32 rxDataFormat[],
        Int32 servingRlsGrantStatus,//0..3 : NO_RECEPTION(0) DOWN(1) HOLD(2) UP (3)
        Int32 nonServingRlsGrantStatus,//0..3: NO_RECEPTION(0) DOWN(1) HOLD(2) UP (3)
        Int32 absoluteGrantValue, //0..37
        Int32 absGrantScope,
        Int32 erntiCrc,
        Int32 referenceEtpr);
void UmaEdchDebugAccumulateInfo(void);
void UmaEdchDebugAccumulateInit(void);
void UmaEdchDebugAccumulateTFCSelectInfo(Int16 etfcRestrictionBlockSize, Int16 servingGrantLimitBlockSize,UmacEdchLogicalChInfo * logCh_p,UmacTrafficReqInfo* trafficReqInfo_p);
Int32 UmaEtfcCalcScheduledGrantPayload (CmacEdchTfcsConfigReq* edchTfcsConfigReq_p, Int8 deltaHarqIndex);
#if !defined (ON_PC)
Boolean UmaEdchIsMaceActive(void);
#endif /* !ON_PC */


/******************************************************************************
 * Global Variables
 *****************************************************************************/
//Return value to MAC-e:
extern UmacEdchDb           umaEdchDb_g;            //Global DB
extern UmacEdchTrafficReq   umacEdchTrafficReq_g;   //decleared in umaedchtx.c
extern UmacEdchTrafficInd   umacEdchTrafficInd_g;

#endif /* UPGRADE_3G_EDCH */
#endif /* UMAEDCH_H */

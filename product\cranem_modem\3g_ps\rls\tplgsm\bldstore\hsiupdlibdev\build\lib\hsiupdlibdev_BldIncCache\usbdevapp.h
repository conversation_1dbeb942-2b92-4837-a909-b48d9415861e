/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/devices/transports/usb.mod/pub/src/usbdevapp.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/24 10:16:13 $
 ***************************************************************************
 * File Description:
 * Function prototypes etc for functions used by the Stack in the client-
 * -configurable Device Application Layer.
 **************************************************************************/

#if defined (UPGRADE_USB)

#ifndef USBDEVAPP_H
#define USBDEVAPP_H

/*******************************************************************************
 * Essential Include Files
 ******************************************************************************/

#include <usbappif.h>     /* for public API types (dataQuantum) */
#include <usbdescs.h>     /* for UsbDescriptorRequest type */
#include <usberr.h>       /* for UsbErrorCode type */

/*******************************************************************************
 * Types
 ******************************************************************************/

typedef enum UsbApplicationTypeTag
{
  USB_TTPCOM_TEST_APPLICATION_TYPE,
  USB_TTPCOM_EMMI_APPLICATION_TYPE,
  USB_MASS_STORAGE_BULK_ONLY_APPLICATION_TYPE,
  USB_COMMS_DEVICE_APPLICATION_TYPE,
  USB_INVALID_APPLICATION_TYPE
} UsbApplicationType;

/*******************************************************************************
 * Call-out functions used by stack
 ******************************************************************************/

extern void usbDevAppInitialise( void );

extern const Int8* usbDevAppGetDescriptor( UsbDescriptorRequest* request,
                                           Int16* length );

extern void usbDevAppInterfaceInd( Int8 interfaceNumber,
                                   Int8 alternateNumber );

extern void usbDevAppConfigurationInd( Int8 configurationValue );

extern void usbDevAppVendorDeviceRequest( Int8 epNumber, Int8* setupPacket );

extern void usbDevAppClassDeviceRequest( Int8 epNumber, Int8* setupPacket );

extern void usbDevAppControlWriteDataphasePayload( UsbDataQuantum dataQuantum );

extern void usbDevAppInitialiseTransmitRequestQueueCnf( Int8 logicalEndpoint,
                                                        TaskId taskID );

extern void usbDevAppFlushTransmitRequestQueueCnf( Int8 logicalEndpoint,
                                                   TaskId taskID );

extern void usbDevAppTransmitDataCnf( UsbTransmitDataRequest* request,
                                      UsbErrorCode errorCode );

extern void usbDevAppTransmitDataInd( UsbTransmitDataRequest* request );

extern void usbDevAppReceiveDataInd( UsbDataQuantum dataQuantum );

extern void usbDevAppReceiveDataFlushedCnf( Int8 logicalEndpoint,
                                            UsbErrorCode errorCode );

extern void usbDevAppNoRoomForData(Int8 endpointNumber, Int16 length);

extern Int16 usbDevAppMaxOutTransfer(Int8 physicalEndpoint);

extern UsbApplicationType usbDevAppGetApplicationFromInterface( Int8 interfaceNumber,
                                                                Int8 alternateNumber );

extern UsbApplicationType usbDevAppGetApplicationFromEndpoint( Int8 endpointAddress );

extern Boolean usbDevAppGetEndpointInterfaceAlternate(Int8 endpointAddress,
                                                      Int8 *interfaceNumber,
                                                      Int8 *alternateNumber);

/* Conversion between endpoint-identifier models */
extern Int8 usbDevAppGetPhysicalEndpoint( Int8 logicalEndpoint );
extern Int8 usbDevAppGetLogicalEndpoint( Int8 physicalEndpoint );
extern Int8 usbDevAppGetInterfaceNumber( Int8 interfaceId );

#endif /* USBDEVAPP_H */
#endif /* UPGRADE_USB */
/* END OF FILE */

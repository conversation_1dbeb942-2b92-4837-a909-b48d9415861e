/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2006 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 * $Id: //central/main/wsd/sys/gki.typ/api/cfg/testsiginc.h#1 $
 * $Revision: #1 $
 * $DateTime: 2007/01/12 13:10:41 $
 **************************************************************************
 * File Description : This file defines the signal definition include
 *     files for the Test signal set.
 *     This file is only included into KISIGINC.H. There is no multiple
 *     inclusion protection as this file can be included more than once
 *     in the same file. If you add a new include into this file you may
 *     also need to add an include into KISIGUNI.H that defines the signal
 *     structures. Building a signal database (that includes the
 *     new signals) in a PC test tool (e.g. Genie, use Signal Maker to
 *     check signals appear with the correct structures) will check that
 *     signal definitions have been correctly added to the system.
 **************************************************************************/

/***************************************************************************
 * IMPORTANT IMPORTANT IMPORTANT IMPORTANT IMPORTANT IMPORTANT IMPORTANT
 *
 * Please read the file description above as it contains information on
 * other changes that might be needed if you change this file.
 *
 * IMPORTANT IMPORTANT IMPORTANT IMPORTANT IMPORTANT IMPORTANT IMPORTANT
 ***************************************************************************/





                            

/* END OF FILE */


/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/ucsmain.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2007/03/19 13:59:30 $
 **************************************************************************
 * File Description:
 *
 *    Header file for CSDI. 
 **************************************************************************/

#if !defined (UCSMAIN_H)
#define       UCSMAIN_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <system.h>
#include <kernel.h>

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/

/***************************************************************************
*   Macro Functions
***************************************************************************/

/***************************************************************************
 * Types
 ***************************************************************************/

/***************************************************************************
 * Typed Constants
 ***************************************************************************/

/***************************************************************************
 * Function Prototypes
 ***************************************************************************/

#if defined (ON_PC)
extern  void            UpCsdiTaskExitRoutine
    (void);
#endif

#if !defined(ENABLE_UP_MIPS_TEST)
KI_ENTRY_POINT UpCsdiTask (void);
#else



      
#endif

#endif

/* end of file */

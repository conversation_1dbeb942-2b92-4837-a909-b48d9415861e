/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urrftf.h#3 $
 *   $Revision: #3 $
 *   $DateTime: 2006/12/15 14:47:38 $
 ***************************************************************************
 * File Description:
 **************************************************************************/

#if !defined (URR_FTF_H)
#define       URR_FTF_H

/* Define URRC sub-process types for the FTF. These needs to be defined before
 * including the ftf header files */
#define UT_FTF_URRC_MTC_SUB_PROCESS  1
#define UT_FTF_URRC_SMC_SUB_PROCESS  2
#define UT_FTF_URRC_CSR_SUB_PROCESS  3
#define UT_FTF_URRC_SIR_SUB_PROCESS  4
#define UT_FTF_URRC_CER_SUB_PROCESS  5
#define UT_FTF_URRC_RBC_SUB_PROCESS  6
#define UT_FTF_URRC_CMR_SUB_PROCESS  7
#define UT_FTF_URRC_AIS_SUB_PROCESS  8
#define UT_FTF_URRC_MCR_SUB_PROCESS  9

#define UT_FTF_URRC_LTE_SUB_PROCESS  10
/**************************************************************************
 * Nested Include Files
 **************************************************************************/

#include <utftf.h>

/**************************************************************************
                     URRC USER TRACE LEVEL MAPPING
 **************************************************************************

Legend:
VHB = Very High-bandwidth logging activity
HB  = High-bandwidth logging activity
MBW = Medium-bandwidth logging activity
LB  = Low-bandwidth logging activity
VLB = Very Low-bandwidth logging activity

*/

/**************************************************************************
                     MTC USER TRACE LEVEL MAPPING
 **************************************************************************

*/

/**************************************************************************
                     SMC USER TRACE LEVEL MAPPING
 **************************************************************************

*/

/**************************************************************************
                     CSR USER TRACE LEVEL MAPPING
 **************************************************************************


DBG_CSR_SYS_INFO_ARRIVAL (LB)
Displays sys info arrival flags upon reception of SI and enables
tracking of System Information Reception errors detected in SIR.

DBG_CSR_CELL_RANKING (VHB)
Displays ranking information.

DBG_CSR_CELL_EVALUATION (VLB)
Displays why a cell has been rejected/accepted.

DBG_CSR_NAS_INFO_HANDLING (VLB)
Displays NAS sys-info handling related information.

DBG_CSR_CELL_UPDATE_INFO_HANDLING (VLB)
Displays info relating to the triggering of RRC_CELL_UPDATE.

DBG_CSR_RESELECTION_CONTROL (MBW)
Displays info relating to the neighbour cells and serving
cell in terms of
a) suitability,
b) acceptability
c) impossibility to have service,
upon reselection.

DBG_CSR_DATA_BASE_HANDLING (VLB)
Displays info relating to CIL and serving cell in
both databases upon database swapping upon reselection.

DBG_CSR_RANKING_CALCULATIONS (VHB)
Displays info relating to the cell-ranking calculations.

DBG_CSR_LOSS_OF_SERVICE (VLB)
Displays info related to entering and leaving loss of
service conditions. Loss of service is defined whenever the
serving cell should be considered as unusable (due to S-criteria,
barred status) or whenever a re-selection is in progress.

DBG_CSR_PENDING_REQUESTS_HANDLING (LB)
Displays info relating to the "Pending request queueing
mechanism" in CSR.

DBG_CSR_HCS (MB)
Displays info relating to the HCS processing.

DBG_CSR_CSRS (?)
Displays all CSRS (Initial Cell Selection) information.

DBG_CSR_SUB_PROCESS (Deprecated)
Any other debug printfs than the ones described above.
These will be grouped and enclosed within appropriate
compilation switches in due course.
*/

#define UT_FTF_TRACE_DBG_CSR_SYS_INFO_ARRIVAL           UT_FTF_TRACE_USER1
#define UT_FTF_TRACE_DBG_CSR_CELL_RANKING               UT_FTF_TRACE_USER2
#define UT_FTF_TRACE_DBG_CSR_CELL_EVALUATION            UT_FTF_TRACE_USER3
#define UT_FTF_TRACE_DBG_CSR_NAS_INFO_HANDLING          UT_FTF_TRACE_USER4
#define UT_FTF_TRACE_DBG_CSR_CELL_UPDATE_INFO_HANDLING  UT_FTF_TRACE_USER5
#define UT_FTF_TRACE_DBG_CSR_RESELECTION_CONTROL        UT_FTF_TRACE_USER6
#define UT_FTF_TRACE_DBG_CSR_DATA_BASE_HANDLING         UT_FTF_TRACE_USER7
#define UT_FTF_TRACE_DBG_CSR_HCS                        UT_FTF_TRACE_USER8
#define UT_FTF_TRACE_DBG_CSR_RANKING_CALCULATIONS       UT_FTF_TRACE_USER9
#define UT_FTF_TRACE_DBG_CSR_LOSS_OF_SERVICE            UT_FTF_TRACE_USER10
#define UT_FTF_TRACE_DBG_CSR_CSRS                       UT_FTF_TRACE_USER11
#define UT_FTF_TRACE_DBG_CSR_SUB_PROCESS                UT_FTF_TRACE_USER12
#define UT_FTF_TRACE_DBG_CSR_MEASUREMENTS               UT_FTF_TRACE_USER13
#define UT_FTF_TRACE_DBG_CSR_DETERMINE_MON_SET          UT_FTF_TRACE_USER14
#define UT_FTF_TRACE_DBG_CSR_PENDING_REQUESTS_HANDLING  UT_FTF_TRACE_USER15
#define UT_FTF_TRACE_DBG_CSR_SNIFFING                   UT_FTF_TRACE_USER16
#define UT_FTF_TRACE_DBG_CSR_CELL_INFO_LIST_HANDLING    UT_FTF_TRACE_USER17
#define UT_FTF_TRACE_DBG_CSR_BCH_ON_OFF                 UT_FTF_TRACE_USER18

/**************************************************************************
                     SIR USER TRACE LEVEL MAPPING
 **************************************************************************

SIR_SYS_INFO
General SIR debugging statement for system information

SIR_CRC
General SIR debugging statement for CRC error information

 */
#define UT_FTF_TRACE_SIR_SYS_INFO                       UT_FTF_TRACE_USER1
#define UT_FTF_TRACE_SIR_CRC                            UT_FTF_TRACE_USER2
#define UT_FTF_TRACE_SIR_SCELL_BCH_ON_OFF               UT_FTF_TRACE_USER3
#define UT_FTF_TRACE_SIR_NCELL_BCH_ON_OFF               UT_FTF_TRACE_USER4

/**************************************************************************
                     CER USER TRACE LEVEL MAPPING
 **************************************************************************

*/

#define UT_FTF_TRACE_DEBUG_CER_SUB_PROCESS              UT_FTF_TRACE_USER1


/**************************************************************************
                     RBC USER TRACE LEVEL MAPPING
 **************************************************************************

*/

#define UT_FTF_TRACE_DBG_RBC_GENERIC_OPERATIONS          UT_FTF_TRACE_USER1
#define UT_FTF_TRACE_DBG_RBC_HSDPA                       UT_FTF_TRACE_USER2
#define UT_FTF_TRACE_DBG_RBC_HSUPA                       UT_FTF_TRACE_USER3
#define UT_FTF_TRACE_DBG_RBC_PHY_CONFIG                  UT_FTF_TRACE_USER4
#define UT_FTF_TRACE_DBG_RBC_CIPHER_OPERATIONS           UT_FTF_TRACE_USER5
#define UT_FTF_TRACE_DBG_RBC_INTEGRITY_OPERATIONS        UT_FTF_TRACE_USER6

/**************************************************************************
                     CMR USER TRACE LEVEL MAPPING
 **************************************************************************
CMR_CELL
General CMR debugging statement for CELL UPDATE

CMR_URA
General CMR debugging statement for URA UPDATE

CMR_QUEUE
General CMR debugging statement for QUEUED events

*/
#define UT_FTF_TRACE_CMR_CELL                           UT_FTF_TRACE_USER1
#define UT_FTF_TRACE_CMR_URA                            UT_FTF_TRACE_USER2
#define UT_FTF_TRACE_CMR_QUEUE                          UT_FTF_TRACE_USER3

/**************************************************************************
                     AIS USER TRACE LEVEL MAPPING
 **************************************************************************

*/

/**************************************************************************
                     MCR USER TRACE LEVEL MAPPING
 **************************************************************************

DEBUG_MCR_SUB_PROCESS
General MCR debugging statements applicable to all modules.

DEBUG_MCRMG_SUB_PROCESS
Debugging statements specific to MCRMG module.

DEBUG_MCR_MEAS_INDS (High Bandwidth)
Measurement indication debugging statements. The volume of statements can
be quite high.

DEBUG_MCRIA_EVENTS
Logs intra-frequency event handling data.

DEBUG_MCRIR_EVENTS
Logs inter-frequency event handling data.

DEBUG_MCRIR_EVENTS
Logs inter-RAT event handling data.

DEBUG_MCRMG_STRONGER_CELL (High Bandwidth)
Details the calculations of the GSM Stronger Cell List.

DEBUG_MCR_ALL_QTY_PRES (High Bandwidth)
Indicates the measurement quantities present for a cell.

DEBUG_MCR_BUILD_RESULTS (Medium Bandwidth)
Logs data when building the measurements results list.

DEBUG_MCR_SEND_REPORT
Logs where the sending of a Measurement Report is initiated.

*/

#define UT_FTF_TRACE_DEBUG_MCR_SUB_PROCESS              UT_FTF_TRACE_USER1
#define UT_FTF_TRACE_DEBUG_MCR_MEAS_INDS                UT_FTF_TRACE_USER2
#define UT_FTF_TRACE_DEBUG_MCRMG_SUB_PROCESS            UT_FTF_TRACE_USER3
#define UT_FTF_TRACE_DEBUG_MCRIA_EVENTS                 UT_FTF_TRACE_USER4
#define UT_FTF_TRACE_DEBUG_MCRIE_EVENTS                 UT_FTF_TRACE_USER5
#define UT_FTF_TRACE_DEBUG_MCRIR_EVENTS                 UT_FTF_TRACE_USER6
#define UT_FTF_TRACE_DEBUG_MCREU_EVENTS                 UT_FTF_TRACE_USER22
#define UT_FTF_TRACE_DEBUG_MCRMG_STRONGER_CELL          UT_FTF_TRACE_USER7
#define UT_FTF_TRACE_DEBUG_MCR_ALL_QTY_PRES             UT_FTF_TRACE_USER8
#define UT_FTF_TRACE_DEBUG_MCR_BUILD_RESULTS            UT_FTF_TRACE_USER9
#define UT_FTF_TRACE_DEBUG_MCR_SEND_REPORT              UT_FTF_TRACE_USER10

#define UT_FTF_TRACE_DEBUG_MCRIA_EVENT_1A               UT_FTF_TRACE_USER11
#define UT_FTF_TRACE_DEBUG_MCRIA_EVENT_1B               UT_FTF_TRACE_USER12
#define UT_FTF_TRACE_DEBUG_MCRIA_EVENT_1C               UT_FTF_TRACE_USER13
#define UT_FTF_TRACE_DEBUG_MCRIA_EVENT_1D               UT_FTF_TRACE_USER14
#define UT_FTF_TRACE_DEBUG_MCRIA_EVENT_1E               UT_FTF_TRACE_USER15
#define UT_FTF_TRACE_DEBUG_MCRIA_EVENT_1F               UT_FTF_TRACE_USER16

#define UT_FTF_TRACE_DEBUG_MCRIE_VAS                    UT_FTF_TRACE_USER17
#define UT_FTF_TRACE_DEBUG_MCR_MODIFY                   UT_FTF_TRACE_USER18
#define UT_FTF_TRACE_DEBUG_UPDATE_ACTIVE_SET            UT_FTF_TRACE_USER19

/**************************************************************************
 * Manifest Constants
 **************************************************************************/

/**************************************************************************
 * Type Definitions
 **************************************************************************/
/* Define the FTF data structure for URRC */
UT_FTF_DEFINE_TRACE_DATA (URRC)
#if defined(UPGRADE_DSDS) && defined(UPGRADE_DSDSWB)
UT_FTF_DEFINE_TRACE_DATA (URRC2)
#endif/*DSDS && UPGRADE_DSDSWB*/
/**************************************************************************
 * Macros
 **************************************************************************/

/**************************************************************************
 * Function Prototypes
 **************************************************************************/


#endif
/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/l1/3g.mod/api/inc/u1sig.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2007/03/30 13:53:25 $
 **************************************************************************
 * File Description: Signal Id to structure mapping for U1 signals
 **************************************************************************/

#if !defined (EXCLUDE_U1CD)
    /* U1CD Signals */
    SIG_DEF( SIG_U1CD_DUMMY = U1CD_SIGNAL_BASE,     EmptySignal                     u1cdDummy)
    SIG_DEF( SIG_U1CD_ENTER_REQ,                    U1cdEnterReq                    u1cdEnterReq)
    SIG_DEF( SIG_U1CD_ENTER_CNF,                    U1cdEnterCnf                    u1cdEnterCnf)
    SIG_DEF( SIG_U1CD_EXIT_REQ,                     U1cdExitReq                     u1cdExitReq)
    SIG_DEF( SIG_U1CD_EXIT_CNF,                     U1cdExitCnf                     u1cdExitCnf)
    SIG_DEF( SIG_U1CD_PHY_TEST_REQ,                 U1cdPhyTestReq                  u1cdPhyTestReq)
    SIG_DEF( SIG_U1CD_PHY_TEST_CNF,                 U1cdPhyTestCnf                  u1cdPhyTestCnf)
    SIG_DEF( SIG_U1CD_PHY_TEST_IND,                 U1cdPhyTestInd                  u1cdPhyTestInd)
    SIG_DEF( SIG_U1CD_HARDWARE_VERSION_REQ,         EmptySignal                     u1cdHardwareVersionReq)
    SIG_DEF( SIG_U1CD_HARDWARE_VERSION_CNF,         U1cdHardwareVersionCnf          u1cdHardwareVersionCnf)
    SIG_DEF( SIG_U1CD_TX_PWR_RMP_CAL_REQ,           U1cdTxPwrRmpCalReq              u1cdTxPwrRmpCalReq)
    SIG_DEF( SIG_U1CD_TX_PWR_RMP_CAL_CNF,           EmptySignal                     u1cdTxPwrRmpCalCnf)
    SIG_DEF( SIG_U1CD_AGC_CAL_REQ,                  U1cdAgcCalReq                   u1cdAgcCalReq)
    SIG_DEF( SIG_U1CD_AGC_CAL_CNF,                  U1cdAgcCalCnf                   u1cdAgcCalCnf)
#endif

#if !defined (EXCLUDE_U1IT)
    /* U1IT Signals */
    SIG_DEF( SIG_U1IT_DUMMY = U1IT_SIGNAL_BASE,     EmptySignal                     u1itDummy)
    SIG_DEF( SIG_U1IT_READ_SMQU_IND,                EmptySignal                     u1itReadSmquInd)
    SIG_DEF( SIG_U1IT_READ_SMTD_IND,                EmptySignal                     u1itReadSmtdInd)

    /* Development Signals. */
    SIG_DEF( SIG_U1IT_PSCH_AFC_IND,                 U1itPschAfcInd                  u1itPschAfcInd)
    SIG_DEF( SIG_U1IT_PSCH_DETECT_IND,              U1itPschDetectInd               u1itPschDetectInd)
    SIG_DEF( SIG_U1IT_CODE_GROUP_DETECT_IND,        U1itCodeGroupDetectInd          u1itCodeGroupDetectInd)
    SIG_DEF( SIG_U1IT_SCRAMBLING_CODE_IDENTIFY_IND, U1itScramblingCodeIdentifyInd   u1itScramblingCodeIdentifyInd)
    SIG_DEF( SIG_U1IT_SCRAMBLING_CODE_VERIFY_IND,   U1itScramblingCodeVerifyInd     u1itScramblingCodeVerifyInd)
    SIG_DEF( SIG_U1IT_SCRAMBLING_CODE_VERIFY_FAIL_IND,U1itScramblingCodeVerifyFailInd u1itScramblingCodeVerifyFailInd)
    SIG_DEF( SIG_U1IT_RAKE_PATH_INFO_IND,           U1itRakePathInfoInd             u1itRakePathInfoInd)
    SIG_DEF( SIG_U1IT_TIMEBASE_ALIGN_IND,           U1itTimebaseAlignInd            u1itTimebaseAlignInd)
    SIG_DEF( SIG_U1IT_TIMEBASE_PATH_TRACK_IND,      U1itTimebasePathTrackInd        u1itTimebasePathTrackInd)
    SIG_DEF( SIG_U1IT_TIMEBASE_DL_TRACK_IND,        U1itTimebaseDlTrackInd          u1itTimebaseDlTrackInd)
    SIG_DEF( SIG_U1IT_TIMEBASE_UL_TRACK_IND,        U1itTimebaseUlTrackInd          u1itTimebaseUlTrackInd)
    SIG_DEF( SIG_U1IT_DL_TFCS_IND,                  U1itDlTfcsInd                   u1itDlTfcsInd)
    SIG_DEF( SIG_U1IT_UL_TRCH_IND,                  U1itUlTrchInd                   u1itUlTrchInd)
    SIG_DEF( SIG_U1IT_DL_TRCH_IND,                  U1itDlTrchInd                   u1itDlTrchInd)
    SIG_DEF( SIG_U1IT_INTRA_FREQ_CELL_INFO_IND,     U1itIntraFreqCellInfoInd        u1itIntraFreqCellInfoInd)
    SIG_DEF( SIG_U1IT_INTER_FREQ_CELL_INFO_IND,     U1itInterFreqCellInfoInd        u1itInterFreqCellInfoInd)
#if !defined (UPGRADE_EXCLUDE_2G)
    SIG_DEF( SIG_U1IT_GSM_CELL_INFO_IND,            U1itGsmCellInfoInd              u1itGsmCellInfoInd)
    SIG_DEF( SIG_U1IT_GSMIN3G_SQ_RSSI_STT_IND,      EmptySignal                     u1itGsmRssiSeqStartInd)
    SIG_DEF( SIG_U1IT_GSMIN3G_SQ_RSSI_END_IND,      EmptySignal                     u1itGsmRssiSeqEndInd)
    SIG_DEF( SIG_U1IT_GSMIN3G_L1_INIT_IND,          U1itL1DlmGsmL1InitInd           u1itL1DlmGsmL1InitInd)
    SIG_DEF( SIG_U1IT_GSMIN3G_BA_LIST_IND,          U1itL1DlmGsmBaListDataInd       u1itL1DlmGsmBaListDataInd)
    SIG_DEF( SIG_U1IT_GSMIN3G_BSIC_DECODE_LIST_IND, U1itL1DlmGsmBsicDecodeListInd   u1itL1DlmGsmBsicDecodeListInd)
    SIG_DEF( SIG_U1IT_GSMIN3G_DCH_MEAS_START_IND,   U1itL1DlmDchGsmMeasStartInd     u1itL1DlmDchGsmMeasStartInd)
    SIG_DEF( SIG_U1IT_GSMIN3G_DCH_MEAS_STOP_IND,    U1itL1DlmDchGsmMeasStopInd      u1itL1DlmDchGsmMeasStopInd)
    SIG_DEF( SIG_U1IT_GSMIN3G_DCH_MEAS_PERIOD_IND,  U1itL1DlmDchGsmMeasPeriodSttInd u1itL1DlmDchGsmMeasPeriodSttInd)
    SIG_DEF( SIG_U1IT_GSMIN3G_DCH_CM_GAP_INFO_IND,  U1itL1DlmCmGapInfoInd           u1itL1DlmCmGapInfoInd)
    SIG_DEF( SIG_U1IT_GSMIN3G_L1DLM_RSSI_RES_IND,   U1itL1DlmGsmRssiDataInd         u1itL1DlmGsmRssiDataInd)
    SIG_DEF( SIG_U1IT_GSMIN3G_L1DLM_BSIC_RES_IND,   U1itL1DlmGsmBsicDataInd         u1itL1DlmGsmBsicDataInd)
    SIG_DEF( SIG_U1IT_GSMIN3G_SQ_FBSB_STT_IND,      U1itGsmFbSbSeqSttInd            u1itGsmFbSbSeqSttInd)
    SIG_DEF( SIG_U1IT_GSMIN3G_SQ_FBSB_END_IND,      U1itGsmFbSbSeqEndInd            u1itGsmFbSbSeqEndInd)
    SIG_DEF( SIG_U1IT_GSMIN3G_FACH_MO_GAP_INFO_IND, U1itU1DlmMoGapInfoInd           u1itU1DlmMoGapInfoInd)
    SIG_DEF( SIG_U1IT_GSMIN3G_FACH_MEAS_START_IND,  U1itU1DlmFachGsmMeasSttInd      u1itU1DlmFachGsmMeasSttInd)
    SIG_DEF( SIG_U1IT_GSMIN3G_FACH_MEAS_STOP_IND,   EmptySignal                     u1itU1DlmFachGsmMeasStopInd)
    SIG_DEF( SIG_U1IT_GSMIN3G_FACH_MEAS_PERIOD_IND, U1itU1DlmFachGsmMeasPeriodInd   u1itU1DlmFachGsmMeasPeriodInd)
    SIG_DEF( SIG_U1IT_GSMIN3G_PAG_MEAS_START_IND,   U1itU1DlmPagingGsmMeasStartInd  u1itU1DlmPagingGsmMeasStartInd)
    SIG_DEF( SIG_U1IT_GSMIN3G_PAG_MEAS_STOP_IND,    EmptySignal                     u1itU1DlmPagingGsmMeasStopInd)
    SIG_DEF( SIG_U1IT_GSMIN3G_OPT_GAP_IND,          U1itU1DlmGsmOptionalGapInd      u1itU1DlmGsmOptionalGapInd)
    SIG_DEF( SIG_U1IT_GSMIN3G_OPT_GAP_START_IND,    U1itU1DlmGsmOptionalGapStartInd u1itU1DlmGsmOptionalGapStartInd)
    SIG_DEF( SIG_U1IT_GSMIN3G_STOP_ACTION_IND,      EmptySignal                     u1itU1DlmStopActionInd)
    SIG_DEF( SIG_U1IT_GSMIN3G_OPT_GAP_COMP_IND,     U1itL1DlmGsmOptGapCompInd       u1itL1DlmGsmOptGapCompInd)
    SIG_DEF( SIG_U1IT_GSMIN3G_TST_NCELL_DEBUG_IND,  U1itL1TstGsmNcellDebugInd       u1itL1TstGsmNcellDebugInd)
    SIG_DEF( SIG_U1IT_MODE_CHANGE_IND,              U1itModeChangeInd               u1itModeChangeInd)

#endif
    SIG_DEF( SIG_U1IT_CELL_PTR_LIST_IND,            U1itCellPtrListInd              u1itCellPtrListInd)
    SIG_DEF( SIG_U1IT_PHCH_RECONFIG_IND,            U1itPhChReconfigInd             u1itPhChReconfigInd)
    SIG_DEF( SIG_U1IT_TRCH_RECONFIG_IND,            U1itTrChReconfigInd             u1itTrChReconfigInd)
    SIG_DEF( SIG_U1IT_MEAS_OCCASION_IND,            U1itMeasOccasionInd             u1itMeasOccasionInd)
    SIG_DEF( SIG_U1IT_CBS_OCCASION_IND,             U1itCbsOccasionInd              u1itCbsOccasionInd)
    SIG_DEF( SIG_U1IT_TGPS_IND,                     U1itTgpsInd                     u1itTgpsInd)
    SIG_DEF( SIG_U1IT_TG_SCHED_IND,                 U1itTgSchedInd                  u1itTgSchedInd)
    SIG_DEF( SIG_U1IT_PCH_SCHED_IND,                U1itPchSchedInd                 u1itPchSchedInd)
    SIG_DEF( SIG_U1IT_RACH_SCHED_IND,               U1itRachSchedInd                u1itRachSchedInd)
    SIG_DEF( SIG_U1IT_DEBUG1_IND,                   U1itDebug1Ind                   u1itDebug1Ind)
    SIG_DEF( SIG_U1IT_DEBUG2_IND,                   U1itDebug2Ind                   u1itDebug2Ind)
    SIG_DEF( SIG_U1IT_DEBUG3_IND,                   U1itDebug3Ind                   u1itDebug3Ind)
    SIG_DEF( SIG_U1IT_DEBUG4_IND,                   U1itDebug4Ind                   u1itDebug4Ind)
    SIG_DEF( SIG_U1IT_DEBUG_GEN_STRUCT_IND,         U1itDebugGenStructInd           u1itDebugGenStructInd)
    SIG_DEF( SIG_U1IT_PICH_IND,                     U1itPichInd                     u1itPichInd)
    SIG_DEF( SIG_U1IT_AICH_IND,                     U1itAichInd                     u1itAichInd)
    SIG_DEF( SIG_U1IT_PDU_LIST_IND,                 U1itPduListInd                  u1itPduListInd)
    SIG_DEF( SIG_U1IT_INT_TRACE_IND,                U1itIntTraceInd                 u1itIntTraceInd)
    SIG_DEF( SIG_U1IT_DEBUG_3_PARAM_IND,            U1itDebug3ParamInd              u1itDebug3ParamInd)
    SIG_DEF( SIG_U1IT_DEBUG_6_PARAM_IND,            U1itDebug6ParamInd              u1itDebug6ParamInd)
    SIG_DEF( SIG_U1IT_MPS_PAM_IND,                  U1itMpsPamInd                   u1itMpsPamInd)
    SIG_DEF( SIG_U1IT_TRCHOUT_IND,                  U1itTrChOutInd                  u1itTrChOutInd)
    SIG_DEF( SIG_U1IT_TPC_IND,                      U1itTpcInd                      u1itTpcInd)
    SIG_DEF( SIG_U1IT_AFC_IND,                      U1itAfcInd                      u1itAfcInd)
    SIG_DEF( SIG_U1IT_MPS_SUCCESS_IND,              U1itMpsSuccessInd               u1itMpsSuccessInd)
    SIG_DEF( SIG_U1IT_MPS_FAIL_IND,                 U1itMpsFailInd                  u1itMpsFailInd)
    SIG_DEF( SIG_U1IT_SFN_DETECT_SUCCESS_IND,       U1itSfnDetectSuccessInd         u1itSfnDetectSuccessInd)
    SIG_DEF( SIG_U1IT_SFN_DETECT_FAIL_IND,          U1itSfnDetectFailInd            u1itSfnDetectFailInd)
    SIG_DEF( SIG_U1IT_BCH_SCHED_IND,                U1itBchSchedInd                 u1itBchSchedInd)
    SIG_DEF( SIG_U1IT_BAD_CRC_IND,                  U1itBadCrcInd                   u1itBadCrcInd)
    SIG_DEF( SIG_U1IT_SIM_SLOW_CLOCK_WAKEUP,        U1itSimSlowClockWakeupInd       u1itSimSlowClockWakeupInd)
    SIG_DEF( SIG_U1IT_TFCI_FAIL_IND,                U1itTfciFailInd                 u1itTfciFailInd)
    SIG_DEF( SIG_U1IT_TXON_IND,                     U1itTxOnInd                     u1itTxOnInd)
    SIG_DEF( SIG_U1IT_TXOFF_IND,                    U1itTxOffInd                    u1itTxOffInd)
    SIG_DEF( SIG_U1IT_PHY_DL_DATA_STATUS_IND,       U1itPhyDlDataStatusInd          u1itPhyDlDataStatusInd)
    SIG_DEF( SIG_U1IT_PHY_UL_DATA_STATUS_IND,       U1itPhyUlDataStatusInd          u1itPhyUlDataStatusInd)
    SIG_DEF( SIG_U1IT_ALARM_IND,                    U1itAlarmInd                    u1itAlarmInd)
#endif

/* END OF FILE */

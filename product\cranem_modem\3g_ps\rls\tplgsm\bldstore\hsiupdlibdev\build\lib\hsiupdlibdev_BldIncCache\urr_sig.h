/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urr_sig.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2007/03/30 13:53:25 $
 **************************************************************************
 * File Description:
 *
 *    File containing the 'signals' union for URRC.
 **************************************************************************/
#if !defined (URR_SIG_H)
#define       URR_SIG_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <cbmc_sig.h>
#include <ccsd_sig.h>
#include <cmac_sig.h>
#include <cpdc_sig.h>
#include <cphy_sig.h>
#include <crlc_sig.h>
#include <mmr_sig.h>
#include <rrc_sig.h>
#include <umac_sig.h>
#include <uphy_sig.h>
#include <urlc_sig.h>
#include <rabmrrc_sig.h>
#include <tirrc_sig.h>
#if !defined (UPGRADE_EXCLUDE_2G)
#include <grrrcsig.h>
#endif
#include <urrtypes.h>
#include <urrintsig.h>
#include <kernel.h>
/* CQ00025639 : Upgrade to LTE+WCDMA feature : begin */
//#if !defined (EXCLUDE_GSM_PS_AS) || !defined (EXCLUDE_GSM_L1)
#include "lteUtraGsmIratItf.h"
//#endif
/* CQ00025639 : Upgrade to LTE+WCDMA feature : end */
#include <utftf_sig.h>

/*CQ00070208 add begin*/
#if defined UPGRADE_PLMS
#include <plms_sig.h>
#include <urrl1asig.h>
#endif/*UPGRADE_PLMS*/

/*CQ00070208 add end*/

//#if defined(UPGRADE_COMMON_STORE)
#include <rrcdstypes.h>
//#endif/*UPGRADE_COMMON_STORE*/

union Signal
{
    CbmcRxInd                        cbmcRxInd;
    CbmcConfigInd                    cbmcConfigInd;
    CbmcDrxLevel1Ind                 cbmcDrxLevel1Ind;
    CbmcCellChangeInd                cbmcCellChangeInd;
    CbmcLevel2InfoInd                cbmcLevel2InfoInd;
    CmacRachConfigReq                cmacRachConfigReq;
    CmacUlDedicatedTfcsConfigReq     cmacUlDedicatedTfcsConfigReq;
    CmacUlTrChConfigReq              cmacUlTrChConfigReq;
    CmacUlMinTfcSetConfigReq         cmacUlMinTfcSetConfigReq;
    CmacUlTfcSubsetConfigReq         cmacUlTfcSubsetConfigReq;
    CmacTrafficMeasurementInd        cmacTrafficMeasurementInd;
    CmacTrafficMeasurementReq        cmacTrafficMeasurementReq;
    CmacMeasurementStopReq           cmacMeasurementStopReq;
    CmacQualityMeasurementInd        cmacQualityMeasurementInd;
    CmacQualityMeasurementReq        cmacQualityMeasurementReq;
    CmacGetMeasurementCnf            cmacGetMeasurementCnf;
    CmacGetMeasurementReq            cmacGetMeasurementReq;
    CmacRachTxStatusInd              cmacRachTxStatusInd;
    CmacRNTIConfigReq                cmacRntiConfigReq;
    CmacUlRbMappingConfigReq         cmacUlRbMappingConfigReq;
    CmacDlRbMappingConfigReq         cmacDlRbMappingConfigReq;
    CmacCsRabConfigReq               cmacCsRabConfigReq;
    CmacActivationTimeReq            cmacActivationTimeReq;
    CmacActivationTimeCnf            cmacActivationTimeCnf;
    CmacHandoverReq                  cmacHandoverReq;
    CmacCipheringConfigReq           cmacCipheringConfigReq;
    CmacHfnConfigReq                 cmacHfnConfigReq;
    CmacHfnConfigCnf                 cmacHfnConfigCnf;
    CmacRachFailuresAlertInd         cmacRachFailuresAlertInd;  /* ********** added */
    CmacDeactivateReq                cmacDeactivateReq;
    CmacHfnAbortReq                  cmacHfnAbortReq;
    CmacHfnMeasurementInd            cmacHfnMeasurementInd;
    CmacUlTrChCombinationConfigReq   cmacUlTrChCombinationConfigReq;
    CmacDlTrChCombinationConfigReq   cmacDlTrChCombinationConfigReq;
#if defined(UPGRADE_3G_HSDPA)
    CmacHsQueueConfigReq             cmacHsQueueConfigReq;
    CmacHsQueueReleaseReq            cmacHsQueueReleaseReq;
    CmacHRNTIConfigReq               cmacHRntiConfigReq;//added for **********
	CmacEhsQueueConfigReq            cmacEhsQueueConfigReq;
    CmacEhsQueueReleaseReq           cmacEhsQueueReleaseReq;
#endif
#if defined (UPGRADE_3G_EDCH)
    CmacEdchTfcsConfigReq            cmacEdchTfcsConfigReq;
    CmacEdchMacdFlowConfigReq        cmacEdchMacdFlowConfigReq;
    CmacEdchReleaseReq               cmacEdchReleaseReq;
#endif
#if defined(UPGRADE_UL_ECF) // ********** begin  
    CmacEdchRachTxStatusInd         cmacEdchRachTxStatusInd;          
    CmacEdchRachConfigReq            cmacEdchRachConfigReq;    
#endif /* UPGRADE_UL_ECF  */ // ********** end
    CpdcpConfigReq                   cpdcpConfigReq;
    CpdcpReleaseReq                  cpdcpReleaseReq;
    CpdcpReleaseReqCont              cpdcpReleaseReqCont; /*CQ0094287 - add*/
    CpdcpReleaseReqContInd           cpdcpReleaseReqContInd; /*CQ0094287 - add*/
    CpdcpRelocCnf                    cpdcpRelocCnf;
    CpdcpRelocReq                    cpdcpRelocReq;
    CpdcpStatusInd                   cpdcpStatusInd;
    CpdcpCloseTestLoopReq            cpdcpCloseTestLoopReq;
    EmptySignal                      cpdcpOpenTestLoopReq;
    CphyBchReq                       cphyBchReq;
    CphyBchSkipReq                   cphyBchSkipReq;
    CphyCbsConfigReq                 cphyCbsConfigReq;
    CphyCellSelectCnf                cphyCellSelectCnf;
    CphyCellSelectReq                cphyCellSelectReq;
#if !defined (UPGRADE_EXCLUDE_2G)
    CphyCellSelectInd                cphyCellSelectInd;
#endif
    CphyCellSearchReq                cphyCellSearchReq;
    EmptySignal                      cbmcOutOfSyncInd;
    CphyCcTrChReleaseReq             cphyCcTrChReleaseReq;
    EmptySignal                      cphyDeactivateCnf;
    CphyDeactivateReq                cphyDeactivateReq;
    CphySwitchRatReq                 cphySwitchRatReq;
    CphyDetectedCellMeasurementInd   cphyDetectedCellMeasurementInd;
    CphyDetectedCellMeasurementReq   cphyDetectedCellMeasurementReq;
    CphyDlTfcConfigReq               cphyDlTfcConfigReq;
    CphyDlTrchConfigReq              cphyDlTrchConfigReq;
    CphyFachConfigReq                cphyFachConfigReq;
    CphyFindCellCnf                  cphyFindCellCnf;
    CphyFindCellReq                  cphyFindCellReq;
    CphyDetectedCellMeasInd          cphyDetectedCellMeasInd;   /* ********** added */
    CphyInitialFachConfigReq         cphyInitialFachConfigReq;
    CphyIntraFreqCellInfoReq         cphyIntraFreqCellInfoReq;
    CphyInterFreqCellInfoReq         cphyInterFreqCellInfoReq;
    CphyGsmCellInfoReq               cphyGsmCellInfoReq;
    CphyMeasureInterFreqCellsInd     cphyMeasureInterFreqCellsInd;
    CphyMeasureIntraFreqCellsInd     cphyMeasureIntraFreqCellsInd;
    CphyMonitorIntraFreqCellReq      cphyMonitorIntraFreqCellReq;
    CphyMonitorInterFreqCellReq      cphyMonitorInterFreqCellReq;
    CphyMonitorGsmCellReq            cphyMonitorGsmCellReq;
    CphyBsicDecodeReq                cphyBsicDecodeReq;
    CphyMonitorIntraFreqCellInd      cphyMonitorIntraFreqCellInd;
    CphyMonitorInterFreqCellInd      cphyMonitorInterFreqCellInd;
    CphyMonitorGsmCellInd            cphyMonitorGsmCellInd;
    CphyBsicDecodeInd                cphyBsicDecodeInd;
    CphyLteCellInfoReq               cphyLteCellInfoReq;
    CphyMonitorLteCellReq            cphyMonitorLteCellReq;
    CphyIdleIntervalInfoReq          cphyIdleIntervalInfoReq;
    CphyMonitorLteCellInd            cphyMonitorLteCellInd;
    CphyStopLteCellMeasReq           cphyStopLteCellMeasReq;
    CphyHandoverToLteCnf             cphyHandoverToLteCnf;
    CphyHandoverToLteReq             cphyHandoverToLteReq;
    CphyDrxFindLteCellReq            cphyDrxFindLteCellReq;
    CphyDrxFindLteCellCnf            cphyDrxFindLteCellCnf;
    CphyLteNcellBchReq               cphyLteNcellBchReq;
    CphyLteNcellBchInd               cphyLteNcellBchInd;
    CphyLteNcellStopBchCnf           cphyLteNcellStopBchCnf;
    CphyHandoverToUmtsFailCnf        cphyHandoverToUmtsFailCnf;
    CphyStopIntraFreqCellMeasReq     cphyStopIntraFreqCellMeasReq;
    CphyStopInterFreqCellMeasReq     cphyStopInterFreqCellMeasReq;
    CphyStopGsmCellMeasReq           cphyStopGsmCellMeasReq;
    CphyDrxFindCellCnf               cphyDrxFindCellCnf;
    CphyDrxFindCellReq               cphyDrxFindCellReq;
    EmptySignal                      cphyDrxNextCellReq;
    CphyDrxNextCellCnf               cphyDrxNextCellCnf;
    CphyDrxRssiScanReq               cphyDrxRssiScanReq;
    CphyDrxRssiScanCnf               cphyDrxRssiScanCnf;
    /* Start of signals are used for GSM PLMN search when the 3G stack is active. */
#if !defined (UPGRADE_PLMS) /* ********** added */
    CphyGsmListBcchReq               cphyGsmListBcchReq;
    CphyGsmListMeasInd               cphyGsmListMeasInd;
    CphyGsmListBsicInd               cphyGsmListBsicInd;
#endif
    CphyGsmBcchDecodeInd             cphyGsmBcchDecodeInd;
    CphyGsmMultiBcchDecodeReq        cphyGsmMultiBcchDecodeReq;
    CphyGsmMultiBcchDecodeInd        cphyGsmMultiBcchDecodeInd;
    /*End of signals are used for GSM PLMN search when the 3G stack is active.*/
    CphyStopFreqMeasOnRachReq        cphyStopFreqMeasOnRachReq;
    CphyStopDetectedCellMeasReq      cphyStopDetectedCellMeasReq;
    CphyStopUeInternalMeasReq        cphyStopUeInternalMeasReq;
    CphyFreqMeasOnRachReq            cphyFreqMeasOnRachReq;
    CphyFreqMeasOnRachCnf            cphyFreqMeasOnRachCnf;
    CphyNcellBchReq                  cphyNcellBchReq;
    CphyNcellBchCnf                  cphyNcellBchCnf;
#if defined(UPGRADE_PLMS)
/* CQ00079376 added begin */    
    CphyFgBchReq                     cphyFgBchReq;
/* CQ00079376 added end */    
    CphyFgBchCnf                     cphyFgBchCnf;
#endif
    CphyNcellBchSkipReq              cphyNcellBchSkipReq;
    CphyNextCellCnf                  cphyNextCellCnf;
    EmptySignal                      cphyNextCellReq;
    CphyOutOfSyncInd                 cphyOutOfSyncInd;
    CphyPchConfigReq                 cphyPchConfigReq;
    CphyUeCapabilityConfigReq        cphyUeCapabilityConfigReq;
    CphyRachConfigReq                cphyRachConfigReq;
    CphyRlCommonSetupReq             cphyRlCommonSetupReq;
    CphyRlReleaseReq                 cphyRlReleaseReq;
    CphyRlSetupReq                   cphyRlSetupReq;
    CphyRssiScanReq                  cphyRssiScanReq;
    CphyRssiScanCnf                  cphyRssiScanCnf;
    CphySccpchConfigReq              cphySccpchConfigReq;
    CphyServingCellMeasurementInd    cphyServingCellMeasurementInd;
    EmptySignal                      cphyServingCellMeasurementReq;
    EmptySignal                      cphyStopServingCellMeasReq;
    EmptySignal                      cphySuspendInd;
    EmptySignal                      cphySyncInd;
    CphyUeInternalMeasurementInd     cphyUeInternalMeasurementInd;
    CphyUeInternalMeasurementReq     cphyUeInternalMeasurementReq;
    CphyUlTfcConfigReq               cphyUlTfcConfigReq;
    CphyUlTrchConfigReq              cphyUlTrchConfigReq;
    CphyCcTrChConfigCnf              cphyCcTrChConfigCnf;
    CphyDlTrchCrcReq                 cphyDlTrchCrcReq;
    EmptySignal                      cphyDlTrchCrcCnf;
    CphyCompressedModeConfigReq      cphyCompressedModeConfigReq;
    CphyCompressedModeErrorInd       cphyCompressedModeErrorInd;
    CphyDlHsDschTrChConfigReq        cphyDlHsDschTrChConfigReq;
    CphyHsScchConfigReq              cphyHsScchConfigReq;
    CphyCpcConfigReq                 cphyCpcConfigReq;
    CphyHsDschReleaseReq             cphyHsDschReleaseReq;
    CphyCellLockReq                  cphyCellLockReq;                          //-+ ********** 29-Mar-2012 +-
// ********** begin  	
#if defined(ENABLE_END_OF_DRX_MEAS_IND)
    CphyEndOfDrxMeasInd              cphyEndOfDrxMeasInd;
#endif /* ENABLE_END_OF_DRX_MEAS_IND  */    
// ********** end
#if defined (UPGRADE_DSDS)
    CphySuspendReq                   cphySuspendReq;
    CphyResumeReq                    cphyResumeReq;
    CphyResumeCnf                    cphyResumeCnf;
#if !defined(UPGRADE_COMMON_STORE)
    GrrRcSuspendReq                  grrRcSuspendReq;
    GrrRcResumeReq                   grrRcResumeReq;
#endif/*UPGRADE_COMMON_STORE*/
    CphyDsControlPchReq              cphyDsControlPchReq;
#if !defined(UPGRADE_COMMON_STORE)
    GrrRcResumeCnf                   grrRcResumeCnf;
    GrrRcAbortPsReq                  grrRcAbortPsReq;       /*CQ00018244 add*/
#endif/*UPGRADE_COMMON_STORE*/
    CphyDspagingFailureInd           cphyDspagingFailureInd;/*CQ00017792 add*/
    CphySuspendByPchReq              cphySuspendByPchReq; /*CQ00038099, added */
    CphyResumeByPchReq               cphyResumeByPchReq; /*CQ00038099, added */
#endif
/* CQ00072273 begin */
    CphyUeRxTxTimeDiffType2MeasurementInd  cphyUeRxTxTimeDiffType2MeasurementInd;
    CphyUeRxTxTimeDiffType2MeasurementReq  cphyUeRxTxTimeDiffType2MeasurementReq;
/* CQ00072273 end */
#if defined(UPGRADE_UL_ECF) // ********** begin
    CphyEdchCommonResourceRelInd 	 cphyEdchCommonResourceRelInd;
#endif /* UPGRADE_UL_ECF  */ // ********** end
    CrlcConfigReq                    crlcConfigReq;
    CrlcContinueReq                  crlcContinueReq;
    CrlcDataPendingInd               crlcDataPendingInd;
    CrlcHaltReq                      crlcHaltReq;
    CrlcReleaseReq                   crlcReleaseReq;
    CrlcResumeReq                    crlcResumeReq;
    CrlcStatusInd                    crlcStatusInd;
    CrlcCountcReq                    crlcCountcReq;
    CrlcCountcCnf                    crlcCountcCnf;
    CrlcCountcInd                    crlcCountcInd;
    CrlcPrepareCipherCfgChangeReq    crlcPrepareCipherCfgChangeReq;
    CrlcPrepareCipherCfgChangeCnf    crlcPrepareCipherCfgChangeCnf;
    CrlcAbortCipherConfigReq         crlcAbortCipherConfigReq;
    CrlcStopReq                      crlcStopReq;
    CrlcSuspendCnf                   crlcSuspendCnf;
    CrlcSuspendReq                   crlcSuspendReq;
    CrlcCloseTestLoopReq             crlcCloseTestLoopReq;
    CrlcDlAmPduSizeInd               crlcDlAmPduSizeInd;
    EmptySignal                      crlcOpenTestLoopReq;
    CrlcEngInfoReq                   crlcEngInfoReq;
	EmptySignal					     crlcNotifyPsDataReq;
	EmptySignal					     crlcNotifyPsDataInd;
#if defined (RRC_UPGRADE_ENG_MODE_REDESIGN) //********** begin
    RrcEngModeInfoReq                rrcEngModeInfoReq;
    RrcEngModeInfoInd                rrcEngModeInfoInd;
	CrlcEngModeInfoReq	             crlcEngModeInfoReq;
	CmacEngEdchModeInfoReq			 cmacEngEdchModeInfoReq;
	CphyEngModeInfoReq				 cphyEngModeInfoReq;
#endif //RRC_UPGRADE_ENG_MODE_REDESIGN :********** end
//********** start
#if defined (UPGRADE_ESCC)
	CphyTcHscchReleaseReq			cphyTcHscchReleaseReq;
	CphyTcHscchReleaseCnf			cphyTcHscchReleaseCnf;
	CphyTcHscchSetupReq				chpyTcHscchSetupReq;
	CphyTcHscchSetupCnf				cphyTcHscchSetupCnf;
	CmacActivationTimeOffsetReq     cmacActivationTimeOffsetReq;
    CmacActivationTimeOffsetCnf     cmacActivationTimeOffsetCnf;
    EmptySignal                     cmacActivationTimeOffsetExpiry;
#endif//UPGRADE_ESCC
//********** end
#if defined (DEVELOPMENT_VERSION)
    CrlcTestModeReq                  crlcTestModeReq;
#endif
    KiTimerExpiry                    kiTimerExpiry;
    MmrRssiInd                       mmrRssiInd;
    RrcActCnf                        rrcActCnf;
    RrcActInd                        rrcActInd;
    RrcActReq                        rrcActReq;
#if !defined (NON_NEZHA)
    RrcCellUpdateInd                 rrcCellUpdateInd;
#endif
    RrcPowerClassReq                 rrcPowerClassReq;
    RrcConnRelCnf                    rrcConnRelCnf;
    RrcConnRelInd                    rrcConnRelInd;
    RrcConnRelReq                    rrcConnRelReq;
	RrcFdEnabledInd					 rrcFdEnabledInd;
	RrcFdConfigReq					 rrcFdConfigReq;
    RrcDataInd                       rrcDataInd;
    RrcDataReq                       rrcDataReq;
    EmptySignal                      rrcDeactCnf;
    RrcDeactReq                      rrcDeactReq;
    RrcEstablishCnf                  rrcEstablishCnf;
    RrcEstablishReq                  rrcEstablishReq;
    RrcMeDataReq                     rrcMeDataReq;
    RrcMsDataReq                     rrcMsDataReq;
    RrcPageInd                       rrcPageInd;
    RrcPlmnListCnf                   rrcPlmnListCnf;
    RrcPlmnListInd                   rrcPlmnListInd;
    RrcPlmnListReq                   rrcPlmnListReq;
    RrcStatusInd                     rrcStatusInd;
    RrcHsStatusInd                   rrcHsStatusInd;
	RrcCellHsCapInd                  rrcCellHsCapInd;
	RrcSmcAbortInd                   rrcSmcAbortInd;/*CQ00048124 add*/
#if !defined (NON_NEZHA)
    RrcSysInfoInd                    rrcSysInfoInd;
#endif
    RrcTestModeReq                   rrcTestModeReq;
    RrcSecurityKeySetConfigReq       rrcSecurityKeySetConfigReq;
    RrcUpdateReq                     rrcUpdateReq;
    RrcSyncInd                       rrcSyncInd;
    RrcEngInfoReq                    rrcEngInfoReq;
    RrcEngInfoInd                    rrcEngInfoInd;
#if defined (UPGRADE_ECID)
    RrcEcidMeasCnf                   rrcEcidMeasCnf; //CQ00067244 add	
#endif/*UPGRADE_ECID*/
    RrcDebugSystemInformationInd     rrcDebugSystemInformationInd;
    RrcDebugSysInfoWithErrorInd      rrcDebugSysInfoWithErrorInd;
    RrcDebugStateChangeInd           rrcDebugStateChangeInd;
    UtFlexibleTraceControlReq        utFlexibleTraceControlReq;
#if defined (DEVELOPMENT_VERSION)
    RrcDebugInformationInd           rrcDebugInformationInd;
    RrcDebugMscInd                   rrcDebugMscInd;
#endif
    RrcDebugAirInterfaceRxInd        rrcDebugAirInterfaceRxInd;
    RrcDebugAirInterfaceTxInd        rrcDebugAirInterfaceTxInd;
    RrcDebugGsmAirInterfaceRxInd     rrcDebugGsmAirInterfaceRxInd;
    RrcDebugIntegrityProtectFailInd  rrcDebugIntegrityProtectFailInd;
    /* added for ********** start */
    RrcDebugIntegrityProtectSuccInd  rrcDebugIntegrityProtectSuccInd;
    /* added for ********** end */
    RrcDebugTransListInd             rrcDebugTransListInd;
    RrcDebugAirInterfaceStatusInd    rrcDebugAirInterfaceStatusInd;
    RrcPsDlDataInd                   rrcPsDlDataInd;
    RrcPsDlDataRsp                   rrcPsDlDataRsp;
    RrcPsUlDataReq                   rrcPsUlDataReq;
    RrcPsUlDataCnf                   rrcPsUlDataCnf;
	RrcDataStatusReq				 rrcDataStatusReq;
	RrcDataStatusCnf				 rrcDataStatusCnf;
	RrcDataStatusInd				 rrcDataStatusInd;
    EmptySignal                      rrcPlmnListAbortReq;
    RrcCellLockReq                   rrcCellLockReq;
    RrcCellLockCnf                   rrcCellLockCnf;
    RrcSetCellSelectConfigReq        rrcSetCellSelectConfigReq;//********** added
    RrcMonitorUeInd                  rrcMonitorUeInd; /*********** add */
    RrcBarredCellUpdateReq           rrcBarredCellUpdateReq;//********** added

    /* **********,added begin */
    RrcDipDataInd                    rrcDipDataInd;
    /* **********,added end */
#if 0/*********** remove*/
    RrcConnectionStateInd            rrcConnectionStateInd;    /* ********** addeded */
#endif
    /* **********,added begin */
    RrcCsTestLoopModeInd             rrcCsTestLoopModeInd;
    /* **********,added end */
/* CQ00053559, added begin */
#if defined (UPGRADE_SRVCC)
    RrcSrvccInd                      rrcSrvccInd;
#endif
/* CQ00053559, added end */

    /* CQ00054105, added begin *//*CQ00084302 change begin*/
    RrcSacCommonInfoInd              rrcSacCommonInfoInd;
    /* CQ00054105, added end *//*CQ00084302 change end*/
#if !defined UPGRADE_PLMS
    RrcScanAndDetectResultReportInd rrcScanAndDetectResultReportInd;//CQ00118692 added
#endif

    // Internal signals group
    RrcIntBcchSysInfo                   rrcIntBcchSysInfo;
/* CQ00065510 begin */    
#if defined (RRC_SIR_OPTIMIZATION)
    EmptyIntSig                         rrcIntCellMatchDb;
#endif //RRC_SIR_OPTIMIZATION
/* CQ00065510 end */
    RrcIntChangeState                   rrcIntChangeState;
    RrcIntNewUeState                    rrcIntNewUeState;
    RrcIntChangePhySm                   rrcIntChangePhySm;
    RrcIntRrcMsDataReq                  rrcIntRrcMsDataReq;
    RrcIntRrcMeDataReq                  rrcIntRrcMeDataReq;
    RrcIntRrcUpdateReq                  rrcIntRrcUpdateReq;
    RrcIntCellSelected                  rrcIntCellSelected;
    RrcIntSelectCell                    rrcIntSelectCell;
    RrcIntCampedOnCell                  rrcIntCampedOnCell;
    RrcIntBcchModInfo                   rrcIntBcchModificationInfo;
    RrcIntCellUpdate                    rrcIntCellUpdate;
    EmptyIntSig                         rrcIntCellUpdateOccured;
    RrcIntCellUpdateConfirm             rrcIntCellUpdateConfirm;
    RrcIntUraUpdateConfirm              rrcIntUraUpdateConfirm;
    RrcIntUpdateConfirmProcessed        rrcIntUpdateConfirmProcessed;
    RrcIntIndicateSibScheduling         rrcIntIndicateSibScheduling;
    RrcIntAirSignalSendStatus           rrcIntAirSignalSendStatus;
    RrcIntProcessRxAirSignal            rrcIntProcessRxAirSignal;
    EmptyIntSig                         rrcIntReleaseRxAirSignal;
    EmptyIntSig                         rrcIntStartPeriodicUpdate;
    RrcIntDrxCycle                      rrcIntDrxCycle;
    EmptyIntSig                         rrcIntOutOfServiceArea;
    RrcIntRrcConnectionSetup            rrcIntRrcConnectionSetup;
    RrcIntRrcConnSetupProcessed         rrcIntRrcConnSetupProcessed;
    EmptyIntSig                         rrcIntRrcConnectionRelease;
    EmptyIntSig                         rrcIntRrcConnRelProcessed;
    RrcIntActiveSet                     rrcIntActiveSet;
    RrcIntRachMeasCnf                   rrcIntRachMeasCnf;
    RrcIntSibReceived                   rrcIntSibReceived;
    EmptyIntSig                         rrcIntLeavingCellDch;
    EmptyIntSig                         rrcIntPhySyncSuccess;
    EmptyIntSig                         rrcIntPhySyncFail;
    EmptyIntSig                         rrcIntRadioLinkFailure;
    RrcIntPhyToIdleState                rrcIntPhyToIdleState;
    RrcIntResetUeToIdle                 rrcIntResetUeToIdle;
    RrcIntSelectInitFachRach            rrcIntSelectInitFachRach;
    RrcIntInitFachRachSelected          rrcIntInitFachRachSelected;
    RrcIntStartValueTransmitted         rrcIntStartValueTransmitted;
    EmptyIntSig                         rrcIntCfnInvalidInd;
    RrcIntTmHfnCfgInd                   rrcIntTmHfnCfgInd;
    RrcIntTmHfnInd                      rrcIntTmHfnInd;
    EmptyIntSig                         rrcIntDistribNextStoredSib;
    EmptyIntSig                         rrcIntAllScellSibsReceived;
    EmptyIntSig                         rrcIntAllNcellSibsReceived;
    EmptyIntSig                         rrcIntAllScellSibsDistributed;
    EmptyIntSig                         rrcIntNcellBchReq;
    RrcIntNcellBchCnf                   rrcIntNcellBchCnf;
    EmptyIntSig                         rrcIntResyncCellReq;
    RrcIntResyncCellCnf                 rrcIntResyncCellCnf;
    EmptyIntSig                         rrcIntReleaseUpdateConfirm;
    RrcIntRlcUnrecoverableError         rrcIntRlcUnrecoverableError;
    RrcIntDisableCellSelectionOnFreq    rrcIntDisableCellSelectionOnFreq;
    EmptyIntSig                         rrcIntEnableCellSelectionOnFreq;
    EmptyIntSig                         rrcIntBcchModReadSibsStart;
    EmptyIntSig                         rrcIntBcchModReadSibsFinish;
    EmptyIntSig                         rrcIntScellMibTimeout;
    EmptyIntSig                         rrcIntNcellMibTimeout;
    RrcIntScellBcchError                rrcIntScellBcchError;                  //-+ ********** 29-Mar-2012 +-
    RrcIntNcellBcchError                rrcIntNcellBcchError;                  //-+ ********** 29-Mar-2012 +-
    EmptyIntSig                         rrcIntScellBadCrc;
    EmptyIntSig                         rrcIntNcellBadCrc;
    RrcIntAbortPlmnSearchReq            rrcIntAbortPlmnSearchReq;              /*CQ00098682 change to RrcIntAbortPlmnSearchReq*/
    EmptyIntSig                         rrcIntAbortPlmnSearchCnf;
    EmptyIntSig                         rrcIntNetworkAuthFail;

#if ! defined (UPGRADE_EXCLUDE_2G)
    EmptyIntSig                         rrcIntRrcConnEstablished;
    EmptyIntSig                         rrcIntReselectToGsm;
#if !defined (L1_SUPPORT_GSM_FG_PLMN_SEARCH) && !defined UPGRADE_PLMS /*CQ00104943 add UPGRADE_PLMS*/
        RrcIntSwitchFromGsmPlmnSearch    rrcIntSwitchFromGsmPlmnSearch;
#endif
    RrcIntReselectToGsmReq              rrcIntReselectToGsmReq;
    RrcIntReselectToGsmCnf              rrcIntReselectToGsmCnf;
    EmptyIntSig                         rrcIntReselectToGsmSuccess;
    RrcIntReselectToGsmFailure          rrcIntReselectToGsmFailure;
    EmptyIntSig                         rrcIntReselectToUmts;
    EmptyIntSig                         rrcIntReselectToUmtsFailure;
    EmptyIntSig                         rrcIntCphySuspedInd;
    RrcIntCellSelectInd                 rrcIntCellSelectInd;
    EmptyIntSig                         rrcIntOutOfSync;
#endif      /* UPGRADE_EXCLUDE_2G */

    EmptyIntSig                         rrcIntNoCell;
    EmptyIntSig                         rrcIntAllScellSibsUpToDate;
    EmptyIntSig                         rrcIntPhyDeactivationPending;
    EmptyIntSig                         rrcIntPhyConfigFinish;
    RrcIntIndicateScellSibsToAcquire    rrcIntIndicateScellSibsToAcquire;
    RrcIntIndicateNcellSibsToAcquire    rrcIntIndicateNcellSibsToAcquire;
    EmptyIntSig                         rrcIntTurnOffScellBchIfOn;
    EmptyIntSig                         rrcIntSib7Timout;
    EmptyIntSig                         rrcIntInterFrequencyHardHandover;

#if ! defined (UPGRADE_EXCLUDE_2G)
    EmptyIntSig                         rrcIntReselectToGsmOos;
#endif      /* UPGRADE_EXCLUDE_2G */

    RrcIntCphyDeactivateReq             rrcIntCphyDeactivateReq;/*CQ00084121 add*/
    EmptyIntSig                         rrcIntScellSibPerError;
    EmptyIntSig                         rrcIntNcellSibPerError;
    EmptyIntSig                         rrcIntBackOnCell;
    EmptyIntSig                         rrcIntPowerOffFromOtherRat;
    EmptyIntSig                         rrcInternalEmptySignal;
    EmptyIntSig                         rrcIntInitiateEcfMeasReport;
    EmptyIntSig                         rrcIntAbortFgGsmSearch;
    EmptyIntSig                         rrcIntFgGsmOosConnNonDchComplete;
    RrcIntReselectToLteFailure          rrcIntReselectToLteFailure;
	RrcIntIcsEvalFinished            	rrcIntIcsEvalFinished;
    // Internal signals group ends here


#if !defined (UPGRADE_EXCLUDE_2G)
    RrcNasTxQueueInfoInd             rrcNasTxQueueInfoInd;
#endif
#if defined (ENABLE_URRC_UNIT_TESTING)
	RrcUtmTmDataInd 				 rrcUtmTmDataInd;
	RrcUtmUmDataInd 				 rrcUtmUmDataInd;
	RrcUtmAmDataInd 				 rrcUtmAmDataInd;
#endif
    UrlcAmDataCnf                    urlcAmDataCnf;
    UrlcAmDataInd                    urlcAmDataInd;
    UrlcAmDataReq                    urlcAmDataReq;
    UrlcAmDlSduDiscardInd            urlcAmDlSduDiscardInd;
    UrlcUlSduDiscardInd              urlcUlSduDiscardInd;
    UrlcTmDataInd                    urlcTmDataInd;
    UrlcTmDataReq                    urlcTmDataReq;
    UrlcUmDataInd                    urlcUmDataInd;
    UrlcUmDataReq                    urlcUmDataReq;
    UrlcUmDataTransmittedInd         urlcUmDataTransmittedInd;
    UrlcXoffInd                      urlcXoffInd;
    UrlcXonInd                       urlcXonInd;
    UrlcUlConfigChangeInd            urlcUlConfigChangeInd;
    UrlcDlConfigChangeInd            urlcDlConfigChangeInd;
    RabmRrcEstablishInd              rabmRrcEstablishInd;
    RabmRrcEstablishRes              rabmRrcEstablishRes;
    /* Added by Daniel for HSD data throughput low issue, begin */
    /*EmptySignal             rabmRrcEstablishRej;*/
    RabmRrcEstablishRej      rabmRrcEstablishRej;
    /* Added by Daniel for HSD data throughput low issue, end */
    RabmRrcReleaseInd                rabmRrcReleaseInd;
    RabmRrcReleaseRes                rabmRrcReleaseRes;
    RabmRrcStatusInd                 rabmRrcStatusInd;
    RabmRrcPdpCtxStatusReq           rabmRrcPdpCtxStatusReq;
    EmptySignal                      tiRrcActivateRbTestLoopModeReq;
    TiRrcCloseTestLoopModeReq        tiRrcCloseTestLoopModeReq;
    TiRrcCloseTestLoopModeCnf        tiRrcCloseTestLoopModeCnf;
    EmptySignal                      tiRrcOpenTestLoopModeReq;
    TiRrcOpenTestLoopModeCnf         tiRrcOpenTestLoopModeCnf;
    EmptySignal                      tiRrcDeactivateRbTestLoopModeReq;
#if !defined (UPGRADE_EXCLUDE_2G)
    GrrRcReselectToGsmReq            grrRcReselectToGsmReq;
    GrrRcReselectToGsmCnf            grrRcReselectToGsmCnf;
    GrrRcReselectToUmtsReq           grrRcReselectToUmtsReq;
    GrrRcReselectToUmtsCnf           grrRcReselectToUmtsCnf;
    GrrRcHandoverToGsmReq            grrRcHandoverToGsmReq;
    GrrRcHandoverToGsmCnf            grrRcHandoverToGsmCnf;
    GrrRcHandoverToUmtsReq           grrRcHandoverToUmtsReq;
    GrrRcHandoverToUmtsCnf           grrRcHandoverToUmtsCnf;
    GrrRcGsmInfoInd                  grrRcGsmInfoInd;
    EmptySignal                      umphHandoverToUmtsFailCnf;    
	GrrRcUmtsUeCapInfoInd            grrRcUmtsUeCapInfoInd; /*CQ00031365*/
#if !defined (L1_SUPPORT_GSM_FG_PLMN_SEARCH) && !defined UPGRADE_PLMS /*CQ00104943 add UPGRADE_PLMS*/                                    //-+ ********** 29-Mar-2012 +-
    GrrRcGsmPlmnListReq grrRcGsmPlmnListReq;
#endif
    GrrRcGsmPlmnListInd          grrRcGsmPlmnListInd;
#if !defined (L1_SUPPORT_GSM_FG_PLMN_SEARCH) && !defined UPGRADE_PLMS /*CQ00104943 add UPGRADE_PLMS*/                                    //-+ ********** 29-Mar-2012 +-
    GrrRcGsmPlmnListCnf          grrRcGsmPlmnListCnf;
    GrrRcGsmPlmnListAbortReq          grrRcGsmPlmnListAbortReq;
#endif



#if !defined (L1_SUPPORT_GSM_FG_PLMN_SEARCH) && !defined UPGRADE_PLMS /*CQ00104943 add*/
    /* CQ00054100 begin */    
    ErrRcLtePlmnListReq              errRcLtePlmnListReq;
#endif/*CQ00104943 add*/
    ErrRcLtePlmnListInd              errRcLtePlmnListInd;
#if !defined (L1_SUPPORT_GSM_FG_PLMN_SEARCH) && !defined UPGRADE_PLMS /*CQ00104943 add*/
    ErrRcLtePlmnListCnf              errRcLtePlmnListCnf;
    ErrRcLtePlmnListAbortReq         errRcLtePlmnListAbortReq;
    /* CQ00054100 end */
#endif/*CQ00104943 add*/

    IratErrcUrrEutraScellInfo        iratErrcUrrEutraScellInfo;
	IratUrrErrcUmtsTddScellInfoInd   iratUrrErrcUmtsTddScellInfoInd;
	IratReselectionRequire           iratReselectionRequire;
	IratReselectionRequest           iratReselectionRequest;
	IratReselectionCnf               iratReselectionCnf;
	IratReselectionAck               iratReselectionAck;
	IratPsActResult                  iratPsActResult;
	IratHandoverRequire              iratHandoverRequire;
	IratHandoverRequest              iratHandoverRequest;
	IratHandoverCnf                  iratHandoverCnf;
	IratHandoverAck                  iratHandoverAck;
	IratHandoverSecurityParaInd      iratHandoverSecurityParaInd;
    IratErrcUrrEutraInfoInd          iratErrcUrrEutraInfoInd;
    IratErrcUrrLteUeCapInfoInd       iratErrcUrrLteUeCapInfoInd;
    IratUrrErrcUmtsUeCapInfoInd      iratUrrErrcUmtsUeCapInfoInd;
#endif

#if defined (UPGRADE_DSDS)
#if defined(UPGRADE_COMMON_STORE)
    IratDsSuspendReq            iratDsSuspendReq;
    IratDsAbortGsmPsReq         iratDsAbortGsmPsReq;
    IratDsCampOnReq             iratDsCampOnReq;
    IratDsSuspendCnf            iratDsSuspendCnf;/* CQ00111976 change from EmptySignal */
    EmptySignal                 iratDsCancelGsmSuspendReq;
    EmptySignal                 iratDsCellSearchFinishInd;
    EmptySignal                 iratDsResumeFinishInd;
    EmptySignal                 iratDsPowerOffCompleteInd;
    EmptySignal                 iratDsCancelAbortGsmPsReq;
    EmptySignal                 iratDsStopIratReq;
#if defined(LTE_DS_SUSPEND_BY_SIM2)
    EmptySignal                 iratDsResumeLtePsInd;
#endif /* LTE_DS_SUSPEND_BY_SIM2 */
#endif/*UPGRADE_COMMON_STORE*/
    IratDsAbortSearchReq        iratDsAbortSearchReq;
    EmptySignal                 iratDsAbortSearchCnf;
/* CQ00109244 added begin */	
#if defined(ENABLE_WAIT_ABORT_SEARCH_CNF)
    EmptySignal                 iratDsPhyConfigFinishInd;
#endif /* ENABLE_WAIT_ABORT_SEARCH_CNF */
/* CQ00109244 added end */	
#endif
#if defined(UPGRADE_LTE_SIM1_PS_SIM2_RESELECTION)
    IratDsControlPchReq         iratDsControlPchReq;
#endif

/*CQ00113072,20181205+*/
#if defined(ENABLE_WB_PCH_LTE_SIM2_MEASUREMENT)
    IratDsGwControlPchReq       iratDsGwControlPchReq;
#endif/*ENABLE_WB_PCH_LTE_SIM2_MEASUREMENT*/
/*CQ00113072,20181205-*/

/* CQ00120196 added begin */
#if defined(UPGRADE_WIFI)
    EmptySignal                 iratDsWifiStartReq;
    IratDsWifiStartCnf          iratDsWifiStartCnf;
    EmptySignal                 iratDsWifiFinishInd;
    IratDsWifiAbortReq          iratDsWifiAbortReq;//CQ00126756 modified
    EmptySignal                 iratDsWifiAbortCnf;
#endif/*UPGRADE_WIFI*/
/* CQ00120196 added end */

    IratDsVoiceBadInd           iratDsVoiceBadInd;/*CQ00116080 add*/

    EmptySignal                 iratDsPagingDrxLengthChangeInd; //CQ00117031 added

/*CQ00065587 START */
#if defined UPGRADE_CSG1
    RrcCsgListReq                    rrcCsgListReq;
    RrcCsgListCnf                    rrcCsgListCnf;
    RrcAutoCsgSelectReq              rrcAutoCsgSelectReq;
    RrcAutoCsgSelectCnf              rrcAutoCsgSelectCnf;
    RrcCsgSelectReq                  rrcCsgSelectReq;
    RrcCsgSelectCnf                  rrcCsgSelectCnf;
#endif //UPGRADE_CSG1
/*CQ00065587 END */

/*CQ00070208 add begin*/
#if defined UPGRADE_PLMS
    IrrPlmnListReq                   irrPlmnListReq;
/*CQ00078036 - Add BEGIN */	
    IrrGsmSisAbortCnf                irrGsmSisAbortCnf;
    IrrPlmnListSuspendCnf            irrPlmnListSuspendCnf;
/*CQ00078036 - Add END */	
    IrrPlmnListResumeReq             irrPlmnListResumeReq; /*CQ00078168 - Add  */
    IrrPlmnListCnf                   irrPlmnListCnf;
    IrrGetLteSibsReq                 irrGetLteSibsReq;
    IrrGetLteSibsCnf				 irrGetLteSibsCnf;
    IrrGetGsmSisReq                  irrGetGsmSisReq;
    IrrGetGsmSisCnf                  irrGetGsmSisCnf;
/*CQ00070305 add begin*/
    IrrGetUmtsSibsReq                irrGetUmtsSibsReq;
    IrrGetUmtsSibsCnf				 irrGetUmtsSibsCnf;
/*CQ00070305 add end*/
/****ICS****/
	IrrIcsSearchReq		    	irrIcsSearchReq	;
	IrrIcsSearchCnf				irrIcsSearchCnf;
	IrrIcsEvalReq				irrIcsEvalReq;
	IrrIcsEvalCnf				irrIcsEvalCnf;
	IrrIcsCampingReq			irrIcsCampingReq;
	IrrIcsCampingCnf			irrIcsCampingCnf;
	IrrIcsAbortReq				irrIcsAbortReq;
	CphyIcsInitReq 				cphyIcsInitReq;
	/****ICS****/

    EcphyGsmMultiBcchDecodeReq       ecphyGsmMultiBcchDecodeReq;
    EcphyGsmBcchDecodeInd            ecphyGsmBcchDecodeInd;
	IrrGsmPlmnInd                    irrGsmPlmnInd;
	EcphyGsmMultiBcchDecodeInd       ecphyGsmMultiBcchDecodeInd;
#endif //UPGRADE_PLMS

/*CQ00070208 add end*/


};
#endif
/* END OF FILE */

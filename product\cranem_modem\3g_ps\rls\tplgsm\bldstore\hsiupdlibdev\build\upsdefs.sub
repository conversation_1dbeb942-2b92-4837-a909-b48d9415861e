#-Iw:\3g_ps\rls\tplgsm\bldstore\hsiupdlibdev\build\lib\hsiupdlibdev_bldinccache
#-Iw:\3g_ps\rls\tplgsm\bldstore\hsiupdlibdev\build
#-Iw:\3g_ps\rls\tplgsm\platforms\hermon\sys\gki.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\platforms\hermon\sys\gki.typ\api\cfg
#-Iw:\3g_ps\rls\tplgsm\platforms\hermon\devices\other\accessory.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\platforms\hermon\devices\other\adc.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\platforms\hermon\devices\other\backlight.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\platforms\hermon\devices\other\battery.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\platforms\hermon\devices\other\camera.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\platforms\hermon\devices\other\keypad.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\platforms\hermon\devices\other\power.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\platforms\hermon\devices\other\rtc.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\platforms\hermon\devices\storage\mmcsd.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\platforms\hermon\devices\storage\nvm.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\platforms\hermon\devices\storage\nvm_anrm2.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\platforms\hermon\devices\transports\spal.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\platforms\hermon\devices\transports\usb.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\platforms\hermon\media\control\mmcc.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\platforms\hermon\media\control\mmcc.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\platforms\hermon\media\framework\common.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\platforms\hermon\media\framework\common.mod\lib\src
#-Iw:\3g_ps\rls\tplgsm\platforms\hermon\media\framework\common.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\platforms\hermon\media\framework\mds.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\platforms\hermon\media\framework\mes.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\platforms\hermon\media\l1am\l1aml1al.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\platforms\hermon\media\lowlevel\mmshm.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\platforms\hermon\media\lowlevel\mmshm.typ\api\inc
#-Iw:\3g_ps\rls\tplgsm\platforms\hermon\modem\l1\2g.mod.w\api\inc
#-Iw:\3g_ps\rls\tplgsm\platforms\hermon\modem\l1\2g.mod.w\pub\src
#-Iw:\3g_ps\rls\tplgsm\platforms\hermon\modem\l1\3g.mod.w\api\inc
#-Iw:\3g_ps\rls\tplgsm\platforms\hermon\modem\l1\3g.mod.w\pub\src
#-Iw:\3g_ps\rls\tplgsm\platforms\hermon\modem\l1\simdrv.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\modem\pscommon\psm.mod\inc
#-Iw:\3g_ps\rls\tplgsm\modem\pscommon\psm.mod\src
#-Iw:\3g_ps\rls\tplgsm\sys\gki.typ\api\cfg
#-Iw:\3g_ps\rls\tplgsm\sys\gki.typ\api\inc
#-Iw:\3g_ps\rls\tplgsm\sys\gki.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\sys\gki.mod\api\shinc
#-Iw:\3g_ps\rls\tplgsm\modem\psnas\ab.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\devices\transports\bluetooth.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\psnas\2g.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\sys\plf.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\pscommon\2g.typ\api\inc
#-Iw:\3g_ps\rls\tplgsm\sys\sys.typ\api\inc
#-Iw:\3g_ps\rls\tplgsm\sys\sys.typ\api\shinc
#-Iw:\3g_ps\rls\tplgsm\modem\psnas\gp.mod\api\shinc
#-Iw:\3g_ps\rls\tplgsm\modem\psas_w\2g.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\psas_w\3g.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\l1_w\2g.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\psnas\csd.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\sys\plf.mod\api\cfg
#-Iw:\3g_ps\rls\tplgsm\sys\gki.typ\api\shinc
#-Iw:\3g_ps\rls\tplgsm\sys\plf.mod\api\shinc
#-Iw:\3g_ps\rls\tplgsm\modem\psnas\ab.mod\api\shinc
#-Iw:\3g_ps\rls\tplgsm\devices\manager.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\pscommon\gp.typ\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\l1_w\2g.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\devices\storage\nvm.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\sys\mapal.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\l1_w\3g.typ\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\l1_w\3g.typ\api\cfg
#-Iw:\3g_ps\rls\tplgsm\modem\pscommon\3g_asn1.typ.w\api\inc
#-Iw:\3g_ps\rls\tplgsm\media\framework\audio\mmac.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\l1_w\2g.mod\lib\src
#-Iw:\3g_ps\rls\tplgsm\modem\l1_w\2g.typ\api\inc
#-Iw:\3g_ps\rls\tplgsm\media\l1am\interface.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\media\l1am\interface.mod\api\cfg
#-Iw:\3g_ps\rls\tplgsm\modem\psnas\gp.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\devices\transports\usb.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\media\framework\audio\auddr.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\media\framework\audio\manager.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\media\control\meds.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\media\framework\mes.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\devices\transports\irda.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\devices\transports\obex.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\media\control\mmsc.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\media\framework\mds.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\media\control\lard.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\devices\other\camera.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\modem\psnas\atci.mod\api\shinc
#-Iw:\3g_ps\rls\tplgsm\media\lowlevel\mmshm.typ\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\psnas\csd.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\modem\psnas\csd.mod\api\shinc
#-Iw:\3g_ps\rls\tplgsm\modem\psnas\atci.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\pscommon\gp.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\sys\plk\plk.typ\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\psnas\2g.mod\api\shinc
#-Iw:\3g_ps\rls\tplgsm\sys\plk\plkm.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\sys\plk\plkm.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\modem\psnas\ab.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\sys\gki.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\devices\storage\filesystem.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\l1_w\gp.mod\lib\src
#-Iw:\3g_ps\rls\tplgsm\modem\l1_w\2g.mod\pub\cfg
#-Iw:\3g_ps\rls\tplgsm\devices\other\gpio.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\psnas\gp.mod\lib\src
#-Iw:\3g_ps\rls\tplgsm\devices\storage\nvm.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\modem\l1_w\simdrv.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\l1_w\simdrv.typ\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\psnas\atci.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\devices\other\adc.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\devices\other\battery.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\sys\tmm.mod\api\cfg
#-Iw:\3g_ps\rls\tplgsm\sys\tmm.mod\api\shinc
#-Iw:\3g_ps\rls\tplgsm\sys\tmm.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\devices\other\rtc.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\devices\other\power.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\devices\tools\dtf\dtf.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\devices\tools\dtf\dtf.mod\api\shinc
#-Iw:\3g_ps\rls\tplgsm\modem\psnas\2g.mod\lib\src
#-Iw:\3g_ps\rls\tplgsm\devices\other\adc.mod\api\cfg
#-Iw:\3g_ps\rls\tplgsm\modem\psas_w\2g.mod\lib\src
#-Iw:\3g_ps\rls\tplgsm\modem\l1_w\gp.typ\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\psnas\3g.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\devices\storage\nvm.mod\api\cfg
#-Iw:\3g_ps\rls\tplgsm\media\framework\common.typ\api\inc
#-Iw:\3g_ps\rls\tplgsm\devices\other\accessory.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\psnas\ps_mpdrivers.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\media\framework\audio\mmac.mod\pub\cfg
#-Iw:\3g_ps\rls\tplgsm\media\codecs\amr_common_soft.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\media\framework\audio\mmac.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\media\codecs\midi_common.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\media\codecs\telephony_common_soft.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\media\codecs\wav_common_soft.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\devices\transports\spal.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\devices\other\battery.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\l1_w\gp.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\devices\other\rtc.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\devices\tools\dtf\dtf.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\modem\psnas\ab.mod\lib\src
#-Iw:\3g_ps\rls\tplgsm\modem\psnas\mpdrivercontroller.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\devices\other\accessory.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\devices\other\adc.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\media\framework\audio\auddr.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\devices\other\backlight.mod\api\cfg
#-Iw:\3g_ps\rls\tplgsm\devices\other\gpio.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\devices\other\keypad.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\devices\other\keypad.mod\pub\cfg
#-Iw:\3g_ps\rls\tplgsm\sys\gki.mod\pub\cfg
#-Iw:\3g_ps\rls\tplgsm\modem\psnas\mpdrivercontroller.mod\api\shinc
#-Iw:\3g_ps\rls\tplgsm\modem\psas_w\gp.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\psnas\3g.mod\lib\src
#-Iw:\3g_ps\rls\tplgsm\modem\psas_w\gp.mod\lib\src
#-Iw:\3g_ps\rls\tplgsm\devices\transports\usb.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\media\framework\audio\auddr.mod\pub\cfg
#-Iw:\3g_ps\rls\tplgsm\sys\plk\plkd.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\dzign\include\services\ss\adapter\mapal
#-Iw:\3g_ps\rls\tplgsm\media\control\meds.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\media\framework\audio\mmac.mod\lib\src
#-Iw:\3g_ps\rls\tplgsm\modem\psas_w\2g.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\modem\pscommon\2g.mod\lib\src
#-Iw:\3g_ps\rls\tplgsm\modem\pscommon\2g.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\sys\plk\plkd.mod\api\shinc
#-Iw:\3g_ps\rls\tplgsm\media\framework\util.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\media\codecs\midi_fm.mod\lib\src
#-Iw:\3g_ps\rls\tplgsm\modem\l1_w\gp.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\media\framework\audio\manager.mod\lib\src
#-Iw:\3g_ps\rls\tplgsm\media\codecs\encodersoftwarempeg.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\psnas\3g.mod\api\shinc
#-Iw:\3g_ps\rls\tplgsm\modem\l1_w\simdrv.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\devices\other\keypad.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\devices\storage\filesystem.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\media\codecs\midi_fm.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\psas_w\3g.mod\lib\src
#-Iw:\3g_ps\rls\tplgsm\modem\psnas\2g.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\media\lowlevel\mmshm.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\phy\gp.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\media\control\meds.mod\api\shinc
#-Iw:\3g_ps\rls\tplgsm\devices\other\backlight.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\media\control\meds.mod\pub\cfg
#-Iw:\3g_ps\rls\tplgsm\modem\psas_w\gp.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\modem\psnas\csd.mod\lib\src
#-Iw:\3g_ps\rls\tplgsm\media\codecs\commonjpegsoftware.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\media\framework\mes.mod\api\cfg
#-Iw:\3g_ps\rls\tplgsm\modem\pscommon\gp.mod\lib\src
#-Iw:\3g_ps\rls\tplgsm\devices\other\usc.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\devices\storage\mmcsd.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\media\codecs\amr_decoder_soft.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\devices\transports\spal.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\media\codecs\amr_encoder_soft.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\media\codecs\mp3_decoder_soft.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\media\codecs\wav_decoder_soft.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\media\codecs\aac_decoder_soft.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\media\codecs\aac_decoder_soft_alt1.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\media\codecs\midi_wt.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\media\codecs\telephony_common_decoder.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\media\codecs\telephony_common_encoder.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\media\codecs\wav_encoder_soft.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\psnas\gp.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\devices\other\battery.mod\pub\cfg
#-Iw:\3g_ps\rls\tplgsm\media\control\cdc.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\media\framework\mes_sh.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\media\framework\mes_sh.mod\api\cfg
#-Iw:\3g_ps\rls\tplgsm\devices\storage\mmcsd.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\devices\transports\usb.mod\lib\src
#-Iw:\3g_ps\rls\tplgsm\media\control\mmsc.mod\api\shinc
#-Iw:\3g_ps\rls\tplgsm\media\control\mmsc.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\devices\other\rtc.mod\pub\cfg
#-Iw:\3g_ps\rls\tplgsm\devices\tools\dtf\dtf.mod\pub\cfg
#-Iw:\3g_ps\rls\tplgsm\devices\transports\bluetooth.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\devices\transports\irda.mod\api\shinc
#-Iw:\3g_ps\rls\tplgsm\devices\manager.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\devices\storage\mmcsd.mod\pub\cfg
#-Iw:\3g_ps\rls\tplgsm\devices\transports\irda.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\devices\transports\obex.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\devices\transports\usb.mod\pub\cfg
#-Iw:\3g_ps\rls\tplgsm\media\framework\perf.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\sys\mapal.mod\api\cfg
#-Iw:\3g_ps\rls\tplgsm\sys\tmm.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\devices\other\fmradio.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\devices\storage\nvm.mod\pub\cfg
#-Iw:\3g_ps\rls\tplgsm\media\control\lard.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\modem\psnas\4g.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\psnas\4g.mod\lib\src
#-Iw:\3g_ps\rls\tplgsm\security\inc
#-Iw:\3g_ps\rls\tplgsm\modem\pscommon\lte.typ\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\pscommon\4g_asn1.typ\api\shinc
#-Iw:\3g_ps\rls\tplgsm\modem\pscommon\lte.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\zlib\contrib\minizip
#-Iw:\3g_ps\rls\tplgsm\modem\pscommon\rrutility\inc
#-Iw:\3g_ps\rls\tplgsm\modem\phy\3g.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\phy\3g.mod\api\shinc
#-Iw:\3g_ps\rls\tplgsm\modem\l1_w\2gmmx.typ\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\l1_w\2gmmx.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\modem\l1_w\3g.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\modem\l1_w\3g.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\l1_w\ctm.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\l1_w\ctm.mod\lib\src
#-Iw:\3g_ps\rls\tplgsm\platforms\null\devices\other\camera.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\devices\l1al.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\devices\transports\mapal_comport.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\devices\other\power.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\media\codecs\tone.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\media\codecs\tone.mod\api\cfg
#-Iw:\3g_ps\rls\tplgsm\media\codecs\tone.typ\api\inc
#-Iw:\3g_ps\rls\tplgsm\media\codecs\tone.typ\api\cfg
#-Iw:\3g_ps\rls\tplgsm\media\l1am\l1aml1al.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\media\l1am\l1aml1al.mod\api\cfg
#-Iw:\3g_ps\rls\tplgsm\media\l1am\l1aml1al.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\media\l1am\l1aml1al.mod\pub\cfg
#-Iw:\3g_ps\rls\tplgsm\devices\storage\nvm_anrm2.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\devices\storage\nvm_anrm2.mod\api\shinc
#-Iw:\3g_ps\rls\tplgsm\devices\storage\nvm_anrm2.mod\api\cfg
#-Iw:\3g_ps\rls\tplgsm\devices\storage\nvm_anrm2.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\devices\storage\nvm_anrm2.mod\pub\cfg
#-Iw:\3g_ps\rls\tplgsm\devices\storage\storage.typ\api\inc
#-Iw:\3g_ps\rls\tplgsm\sys\plf.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\devices\other\camera.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\devices\other\camera.mod\api\shinc
#-Iw:\3g_ps\rls\tplgsm\devices\other\trembler.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\media\control\telephony\tac.mod\lib\src
#-Iw:\3g_ps\rls\tplgsm\media\control\telephony\tac.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\media\control\telephony\css.typ\api\inc
#-Iw:\3g_ps\rls\tplgsm\media\framework\mds.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\media\framework\mds.mod\pub\cfg
#-Iw:\3g_ps\rls\tplgsm\media\framework\mes.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\media\codecs\mp4decsw1.mod\prv\src
#-Iw:\3g_ps\rls\tplgsm\media\codecs\mp4decsw1.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\media\codecs\mp4decsw1.mod\api\shinc
#-Iw:\3g_ps\rls\tplgsm\media\codecs\encodersoftwarempeg.mod\prv\src
#-Iw:\3g_ps\rls\tplgsm\media\codecs\encodersoftwarejpeg.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\media\io\sinkmvt.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\media\io\sink3gp.mod\lib\src
#-Iw:\3g_ps\rls\tplgsm\media\io\sink3gp.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\media\io\sinkjfif.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\psnas\3g.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\modem\psnas\ab.mod\pub\cfg
#-Iw:\3g_ps\rls\tplgsm\modem\psnas\mpapp.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\modem\psas_w\3g.mod\pub\src
#-Iw:\3g_ps\rls\tplgsm\modem\psas_w\3g.mod\prv\src
#-Iw:\3g_ps\rls\tplgsm\media\codecs\midi_wt.mod\lib\src
#-Iw:\3g_ps\rls\tplgsm\media\codecs\aac_decoder_soft.mod\lib\src
#-Iw:\3g_ps\rls\tplgsm\modem\pscommon\3g_ut.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\pscommon\3g_asn1.typ.w\api\shinc
#-Iw:\3g_ps\rls\tplgsm\modem\pscommon\3g_asn1.mod.w\api\shinc
#-Iw:\3g_ps\rls\tplgsm\modem\pscommon\3g_asn1.mod.w\api\inc
#-Iw:\tdl1c\tdl1c\api\inc
#-Iw:\tdl1c\tdl1c\pub\src
#-Ix:\crd\ipc\inc
#-Ix:\aud_sw\audio\inc
#-Ix:\aud_sw\audio\inc
#-Ix:\drat\gsm_if\inc
#-Iw:\ltel1a\ltel1a\inc
#-Iw:\ltel1a\ltel1a\test
#-Iw:\ltel1a\ltel1a\src
#-Iw:\3g_ps\rls\tplgsm\modem\psas\lte.mod\rrc\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\psas\lte.mod\rrc\src
#-Iw:\3g_ps\rls\tplgsm\modem\psas\lte.mod\mac\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\psas\lte.mod\mac\lib\src
#-Iw:\3g_ps\rls\tplgsm\modem\psas\lte.mod\mac\test\src
#-Iw:\3g_ps\rls\tplgsm\modem\psas\lte.mod\rlc\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\psas\lte.mod\rlc\lib\src
#-Iw:\3g_ps\rls\tplgsm\modem\psas\lte.mod\rlc\test\src
#-Iw:\3g_ps\rls\tplgsm\modem\psas\lte.mod\pdcp\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\psas\lte.mod\pdcp\lib\src
#-Iw:\3g_ps\rls\tplgsm\modem\psas\lte.mod\rabm\lib\src
#-Iw:\3g_ps\rls\tplgsm\modem\psas\lte.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\pscommon\4g_asn1.typ\api\shinc
#-Iw:\3g_ps\rls\tplgsm\modem\pscommon\4g_asn1.mod\api\shinc
#-Iw:\3g_ps\rls\tplgsm\modem\pscommon\4g_asn1.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\psas\lte.mod\rohc_a\common
#-Iw:\3g_ps\rls\tplgsm\modem\psas\lte.mod\rohc_a\common\protocols
#-Iw:\3g_ps\rls\tplgsm\modem\psas\lte.mod\rohc_a\comp
#-Iw:\3g_ps\rls\tplgsm\modem\psas\lte.mod\rohc_a\comp\schemes
#-Iw:\3g_ps\rls\tplgsm\modem\psas\lte.mod\rohc_a\decomp
#-Iw:\3g_ps\rls\tplgsm\modem\psas\lte.mod\rohc_a\decomp\schemes
#-Iw:\3g_ps\rls\tplgsm\modem\psas\plms.mod\inc
#-Ix:\pcac\ci_stub\src
#-Iw:\3g_ps\rls\tplgsm\ipinc
#-Ix:\softutil\datacollector\inc
#-Iw:\sac\sac\inc
#-Iw:\sac\sac\src
#-Ix:\genlib\fsm\inc
#-Ix:\genlib\qmgr\inc
#-Ix:\pcac\msl_dl\inc
#-Ix:\pcac\atparser\inc
#-Ix:\pcac\msl_utils\inc
#-Ix:\pcac\td_telephony\modem\inc
#-Ix:\diag\diag_comm\inc
#-Ix:\pcac\pca_components\inc
#-Ix:\pcac\ci_stub\inc
#-Ix:\crd\dtc\src\include
#-Ix:\hop\dma\inc
#-Iw:\3g_ps\rls\tplgsm\modem\pscommon\irat.mod\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\pscommon\irat.typ\api\inc
#-Iw:\3g_ps\rls\tplgsm\modem\pscommon\3g.mod\api\inc
#-Ix:\gplc\l1c\inc
#-Ix:\gplc\gplc\inc
#-Iw:\3g_ps\rls\tplgsm\modem\pscommon\irat.typ\api\inc
#-Ix:\drat\drat\inc
#-Ix:\pcac\atcmdsrv\inc
#-Ix:\drat\inc
#-Ix:\drat\wb\inc
#-Ix:\drat\plw\inc
#-Ix:\softutil\softutil\inc
#-Iw:\ci\inc
#-Ix:\csw\platform\inc
#-Ix:\aud_sw\auc\inc
#-Ix:\env\win32\inc
#-Ix:\hal\core\inc
#-Ix:\tavor\arbel\inc\hdr
#-Ix:\hal\acipc\inc
#-Iw:\3g_ps\rls\tplgsm\modem\pscommon\prlgki\inc
#-Ix:\os\alios\kernel\rhino\include
#-Ix:\os\alios\hisr
#-Ix:\os\alios\asr3601\config
#-Ix:\os\alios\kernel\armv7r\include
#-Iw:\3g_ps\rls\tplgsm\platforms\hermon\dminc
#-Iw:\3g_ps\rls\tplgsm
#-Iw:\3g_ps\rls\tplgsm\utinc
#-Iw:\3g_ps\rls\tplgsm\zlib
#-Iw:\3g_ps\rls\tplgsm\ipinc
#-Iw:\3g_ps\rls\tplgsm\altest\nvsim
#-Iw:\3g_ps\rls\tplgsm\l1inc
#-Iw:\3g_ps\rls\tplgsm\dminc
#-Ix:\hop\bsp\inc
#-Ix:\hop\core\inc
#-Ix:\hop\cpmu\inc
#-Ix:\hop\csw_memory\inc
#-Ix:\hop\dma\inc
#-Ix:\hop\eehandler\inc
#-Ix:\hop\fdi\inc
#-Ix:\hop\intc\inc
#-Ix:\hop\keypad\inc
#-Ix:\hop\pm\inc
#-Ix:\hop\pmu\inc
#-Ix:\hop\rtc\inc
#-Ix:\hop\stubs\inc
#-Ix:\hop\tickmanager\inc
#-Ix:\hop\uart\inc
#-Ix:\hop\uart\src
#-Ix:\hop\usim_drv\inc
#-Ix:\hop\usim_drv\src
#-Ix:\hop\wcipher\inc
#-Ix:\hop\timer\inc
#-Ix:\crossplatformsw\nvmclient\inc\include
#-Ix:\crossplatformsw\nvmclient\inc\fdi_add
#-Ix:\crossplatformsw\nvmclient\inc\fm_inc
#-Ix:\tavor\arbel\inc
#-Ix:\tavor\env\inc
#-Ix:\csw\platform\inc_arbel
#-Ix:\csw\platform\inc
#-Ix:\csw\syscfg\inc
#-Ix:\agps\core\inc
#-Ix:\spi\spi_wrapper\inc
#-Ix:\spi\nxp\inc
#-Ix:\spi\spi_wrapper\src\include
#-Ix:\spi\sdv\inc
#-Ix:\aplp\aplp\inc
#-Ix:\drat\plw\inc
#-Ix:\drat\plugin\inc
#-Ix:\aplp\aplp_misc\inc
#-Ix:\aplp\mcl\inc
#-Ix:\csw\bsp\inc
#-Ix:\drat\drat\inc
#-Ix:\drat\inc
#-Ix:\drat\wb\inc
#-Ix:\drat\plw\inc
#-Ix:\drat\cellularpowerapplication\inc
#-Ix:\env\win32\inc
#-Ix:\gplc\gplc\inc
#-Ix:\gplc\l1c\inc
#-Ix:\gplc\l1c\src\include
#-Ix:\gplc\l1gki\inc
#-Ix:\gplc\l1gki\inc\gpinc
#-Ix:\gplc\l1gki\inc\psinc
#-Ix:\gplc\abp\inc
#-Ix:\gplc\plkinc
#-Ix:\gplc\genericrfdriver\inc
#-Ix:\hop\intc\inc
#-Ix:\hop\intc\inc
#-Ix:\csw\pm\inc
#-Ix:\hop\pm\inc
#-Ix:\softutil\tickmanager\inc
#-Ix:\hop\timer\inc
#-Ix:\csw\syscfg\inc
#-Ix:\hal\gpio\inc
#-Ix:\gplc\l1c\src\include
#-Ix:\gplc\l1gki\inc\psinc
#-Ix:\aplp\aplp\inc
#-Ix:\gplc\l1gki\inc
#-Ix:\crd\dtc\inc
#-Ix:\drat\plw\inc
#-Ix:\csw\bsp\inc
#-Ix:\drat\drat\inc
#-Ix:\drat\inc
#-Ix:\drat\wb\inc
#-Ix:\drat\plw\inc
#-Ix:\env\win32\inc
#-Ix:\gplc\gplc\inc
#-Ix:\hermon\hermond\inc
#-Ix:\os\threadx\inc
#-Ix:\os\nu_xscale\inc
#-Ix:\os\osa\inc
#-Ix:\sac\sac\inc
#-Ix:\pcac\ci\inc
#-Ix:\pcac\atcmdsrv\inc
#-Ix:\hal\clcd\inc
#-Ix:\hal\clcd\src
#-Ix:\hal\core\inc
#-Ix:\hal\dma\inc
#-Ix:\hal\gpio\inc
#-Ix:\hal\i2s\inc
#-Ix:\hal\i2s\src
#-Ix:\hal\intc\inc
#-Ix:\hal\keypad\inc
#-Ix:\hal\lcd\inc
#-Ix:\hal\lcd\src
#-Ix:\hal\lcdif\inc
#-Ix:\hal\mmcsd\inc
#-Ix:\hal\mmcsd\src
#-Ix:\hal\mmu\inc
#-Ix:\hal\rtc\inc
#-Ix:\hal\pmu\inc
#-Ix:\hal\uart\inc
#-Ix:\hal\uart\src
#-Ix:\hal\usb\inc
#-Ix:\hal\usb\src
#-Ix:\hal\usbmgr\inc
#-Ix:\hal\usbmgr\src
#-Ix:\hal\usb_cable\inc
#-Ix:\hal\usb_device\inc
#-Ix:\hal\usb_standart\inc
#-Ix:\hal\usim\inc
#-Ix:\hal\usim\src
#-Ix:\crd\wcipher\inc
#-Ix:\crd\dtc\inc
#-Ix:\hal\timer\inc
#-Ix:\aud_sw\acm\inc
#-Ix:\aud_sw\auc\inc
#-Ix:\softutil\csw_memory\inc
#-Ix:\diag\diag_logic\inc
#-Ix:\diag\diag_logic\src
#-Ix:\softutil\diag\inc
#-Ix:\softutil\diag\src
#-Ix:\softutil\eehandler\inc
#-Ix:\softutil\fdi\src\dav_inc
#-Ix:\softutil\fdi\src\etc
#-Ix:\softutil\fdi\src\fdi_add
#-Ix:\softutil\fdi\src\fm_inc
#-Ix:\softutil\fdi\src\include
#-Ix:\softutil\fdi_7_1\src\common\inc
#-Ix:\softutil\fdi_7_1\src\ffscore\mfm\musb\inc
#-Ix:\softutil\fdi_7_1\src\platform\flashapi\inc
#-Ix:\softutil\fdi_7_1\src\platform\mtd\rtos\inc
#-Ix:\softutil\fdi_7_1\src\platform\oslayer\rtos\nucleus\inc
#-Ix:\softutil\fdi_7_1\src\platform\oslayer\rtos\api\inc
#-Ix:\softutil\nvm\inc
#-Ix:\softutil\softutil\inc
#-Ix:\softutil\tickmanager\inc
#-Ix:\csw\pm\inc
#-Iw:\3g_ps\rls\tplgsm\platforms\hermon\hastub
-DDEFAULT_CSR_FTF_LOGGING_CATEGORY=0x00000205
-DDEFAULT_CER_FTF_LOGGING_CATEGORY=0xffffffff
-DENABLE_SIR_STRICT_CHECKING
-DDM_EXCLUDE_ADC_DEVICE_DRIVER
-DDM_EXCLUDE_GPIO_PLATFORM_DRIVER
-DNO_EMMI_INTERFACE
-DKI_COMMS_TASK_ID=HA_COMMS_TASK_ID
-DSHORT_DEV_ASSERTS
-DKI_RLG_MAX_RLOG_GKE_ENTRIES=16384
-DKI_ASSERTFAIL_ENABLE_CONFIG
-DURLC_TASK_FLOW_PROTECT
-DTRANSFER_MODIFICATION
-DDISABLE_L1AL_AUDIO
-DDISABLE_L1AM
-DKI_TTI_ENABLE_STATIC_FILTER
-DUPGRADE_45_08_CR_250_LB_MS
-DUPGRADE_4460_CR_49_TBF_ESTAB
-DUPGRADE_4460_CR_665_EXT_UL_TBF
-DUPGRADE_44_60_CR_599_ENHANCED_PSCD
-DUPGRADE_44_60_CR_622_ENHANCED_PNCD
-DUPGRADE_45_08_CR_245_FDD_BARRED
-DUPGRADE_25_331_CR_2104_CELL_LESS_REDIRECTION
-DUPGRADE_44060_CR_595_FDD_THR_2
-DUPGRADE_DUAL_LINK
-DUPGRADE_44065_CR_15_UNKWN_ALGO_TYPE
-DUPGRADE_44064_CR_8_UI_FRAME_NO_INFO
-DGRR_RESELECTION_MONITOR
-DUPGRADE_FAST_ACQUISITION
-DUPGRADE_MORE_THAN_3_FDD_FREQ_IN_GSM
-DUPGRADE_Q6_GRR
-DLTE_RR_MEAS_Q6
-DLTE_RR_MEAS_Q6_2
-DL1A_MEMORY_OPTIMIZATION
-DENABLE_3G_CCO_GERAN_SI_HANDLING
-DUPGRADE_REL6_SAIC
-DUPGRADE_3G_RELEASE_4
-DUPGRADE_3G_RELEASE_5
-DUPGRADE_REL5
-DUPGRADE_R4_FS1
-DENABLE_3G_CCO_GERAN_SI_HANDLING
-DUPGRADE_3G_RELEASE_6
-DUPGRADE_3G_RELEASE_7
-DUPGRADE_ASN1_R9
-DRLCDEC_R6
-DUPGRADE_REL6_SAIC
-DL1_UPGRADE_R6
-DL1_UPGRADE_R7
-DUM_RX_OPT
-DRLC_LOW_PRIOR_OPT
-DUPS_CFG_HS_DSCH_CATEGORY_10
-DUPS_CFG_HS_DSCH_CATEGORY_EXT_14
-DENABLE_END_OF_DRX_MEAS_IND
-DRRC_SIR_OPTIMIZATION
-DVOID_SPECIAL_VALUE_OF_HE
-DENABLE_READ_SIB19_FOR_23G
-DCR_4979_CORRECTION_OF_SRB1_FOR_FACH
-DENABLE_OPT_DATA_FLOW_OVER_SHMEM
-DUPGRADE_24_08_CR_974_CELL_UPDATE
-DUPGRADE_44_18_CR_287_REDIRECTION
-DINTEL_UPGRADE_AMR_L1_INTEG
-DUPGRADE_AMR_PS
-DPS_L2_R8_API
-DUPGRADE_R8
-DUPGRADE_LTE_MEAS_FACH
-DUPGRADE_SMC_REL10
-DUPGRADE_4G_FROM_3G_MFBI
-DUPGRADE_4G_FROM_2G_MFBI
-DNEZHA3
-DLTE_MEAS_AT_GSM_OPTIMIZATION
-DL1A_PMU_OPTIMIZATION
-DFG_RSSI_USING_APLP_GPLC
-DUPGRADE_COMMON_STORE
-DUPGRADE_SPEED_CAMP
-DKI_DYNMEM_2_OSA
-DDISABLE_MDT
-DDISABLE_RLF
-DUPGRADE_L2_MEM_OPT
-DUPGRADE_RR_MEM_OPT
-DUPGRADE_RR_MEM_OPT_GSM
-DGRR_SPLIT_MALLOC_SYS_DB
-DGRR_SPLIT_MALLOC_DATA
-DDISABLE_LTE_CA
-DSUPPORT_ERRC_FG_PLMN
-DDS3_CAT1
-DDS3_CAT1_CATEGORY
-DDS3_LTE_ONLY
-DCRANE_Z1
-DCRANE_PLATFORM
-DCRANE_L1
-DPS_TCM_CODE
-DROHC_SIMPLE_VOLTE
-DREMOVE_POOL
-DUPGRADE_WIFI
-DOPTIMIZATION_START_FROM_CRANE
-DFOTA_MINI
-DENABLE_SET_CELL_SELECT_CONFIG
-DPLMS_EXCLUDE_UMTS_GSM
-DUPGRADE_4G_BAND_EXT
-DZTE_SAR
-DUPGRADE_GPLC_SUSPEND
-DUPGRADE_Q6_GRR
-DUPGRADE_DSDSL1
-DENABLE_PREFER_BANDS
-DLTE_HIGH_MOBILITY_OPTIMIZATION
-DUPGRADE_LTE_ASN_R15
-DOPTIMIZATION_PINGPONG_HANDOVER
-DUPGRADE_ERRC_R13_AC
-DUPGRADE_R13_EDRX
-DUPGRADE_R13_L2
-DUPGRADE_ATTACH_WO_PDN
-DLTE_MR_TIMER
-DGRR_CHECK_FAKE_BASE_STATION
-DUPGRADE_INCREASED_MEAS_FREQ_NUM
-DSUPPORT_PSM_EDRX
-DSUPPORT_CP_DATA_CONTROL
-DDISABLE_DIAG
-DENABLE_NETWORK_SCAN_REPORT
-DCRANEL_THIN_L2
-DCRANEL_THINNER_L2
-DDISABLE_ECID_OTDOA
-DDISABLE_SOME_R15_IES
-DSUPPORT_SMS
-DDISABLE_THELOG
-DSUPPORT_LTE_EAB
-DOPT_PCH_ERROR_CELL
-DUPGRADE_MCR_STRICT_CHECKING
-DUPGRADE_AGPS
-DENABLE_AGPS_DEBUG
-DUSE_ABAPP
-DUPGRADE_CTM
-DENABLE_CIRM_DATA_IND
-DVG_DEFAULT_IPR=9
-DUSB_COMMS_DEVICE_INTERFACE
-DCONFIG_TTP_MUX_USBNULL

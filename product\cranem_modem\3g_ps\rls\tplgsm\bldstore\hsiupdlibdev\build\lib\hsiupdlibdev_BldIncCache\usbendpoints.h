/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/devices/transports/usb.mod/pub/src/usbendpoints.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/24 10:16:13 $
 ***************************************************************************
 * File Description:
 * Endpoint, Interface and Configuration constants determined by build options.
 **************************************************************************/

#if defined(UPGRADE_USB)

#ifndef USBENDPOINTS_H
#define USBENDPOINTS_H

#if defined (USB_LIBRARY_MODULE)
#error "Include file contains client-configurable build switches"
#endif

#if defined(USB_DYNAMIC_CONFIGURATION)

/* Enumerated type to provide arbitrary ID's for endpoints within the
 * applications.
 * Force the ID's to be illegal as physical endpoints (ie >16) to allow for
 * checking for programming errors.
 * Remember the endpoint is passed around as an Int8.
 * Make sure you keep clear of USB_ENDPOINT_INVALID.
 */

#define USB_MIN_LOGICAL_ENDPOINT  (20)

typedef enum UsbLogicalEndpointTag
{
  USB_MAST_IN_ENDPOINT = USB_MIN_LOGICAL_ENDPOINT,
  USB_MAST_OUT_ENDPOINT,
  USB_EMMI_IN_ENDPOINT,
  USB_EMMI_OUT_ENDPOINT,
  USB_COMM_CTRL_IN_ENDPOINT,
  USB_COMM_DATA_IN_ENDPOINT,
  USB_COMM_DATA_OUT_ENDPOINT,
  USB_DIAG_IN_ENDPOINT,
  USB_DIAG_OUT_ENDPOINT,

  /* End list with: */
  USB_ILLEGAL_LOGICAL_ENDPOINT
} UsbLogicalEndpoint;

#define USB_MAX_LOGICAL_ENDPOINT  (USB_ILLEGAL_LOGICAL_ENDPOINT-1)

#else  /* !(USB_DYNAMIC_CONFIGURATION) */

#define USB_MIN_LOGICAL_ENDPOINT         1
#define USB_MAX_LOGICAL_ENDPOINT         15
#define USB_ILLEGAL_LOGICAL_ENDPOINT     (USB_MAX_LOGICAL_ENDPOINT+1)

#endif /* !(USB_DYNAMIC_CONFIGURATION) */




#if defined(DEVELOPMENT_VERSION)
/* Endpoint range (and thus physical/logical set) checking.
 *
 * DO NOT use USB_LOGICAL_EP_CHECK within library code because the limits
 * may not be defined correctly.
 */

#define USB_LOGICAL_EP_CHECK( eP ) \
  if( (eP != USB_ENDPOINT_ZERO) && \
      ((eP < USB_MIN_LOGICAL_ENDPOINT ) || ( eP > USB_MAX_LOGICAL_ENDPOINT ))) \
  { DevFail("Logical Endpoint out of range"); }

#else
#define USB_LOGICAL_EP_CHECK( eP )
#endif


/* Enumerated type for a set of Interface identifiers.
 *
 * Historically these have been hard coded on build, and could remain so
 * with dynamic configuration until the selection of a composite is
 * required dynamically.
 */
typedef enum UsbLogicalInterfaceTag
{
  USB_MAST_INTERFACE,
  USB_EMMI_INTERFACE,
  USB_COMM_INTERFACE,
  USB_COMM_DATA_INTERFACE,
  USB_DIAG_INTERFACE,

  USB_NUM_LOGICAL_INTERFACES
} UsbLogicalInterface;



/* Normal "physical" INTERFACE_NUMBER's for use in descriptors. Defined here
 * because these constants are used in code and we'd rather not include
 * usbtargetdesc.h in anything other than usbdevapp.c
 *
 * For single-function devices: */
#define USB_MAST_INTERFACE_NUMBER                  (USB_SPEC_MIN_INTERFACE_NUMBER)
#define USB_MAST_INTERFACE_ALTERNATE_NUMBER        (0)

#define USB_EMMI_INTERFACE_NUMBER                  (USB_SPEC_MIN_INTERFACE_NUMBER)
#define USB_EMMI_INTERFACE_ALTERNATE_NUMBER        (0)

#define USB_COMM_INTERFACE_NUMBER                  (USB_SPEC_MIN_INTERFACE_NUMBER)
#define USB_COMM_ALTERNATE_NUMBER                  (0)
#define USB_COMM_DATA_INTERFACE_NUMBER             (USB_SPEC_MIN_INTERFACE_NUMBER +1)
#define USB_COMM_DATA_INTERFACE_ALTERNATE_NUMBER   (0)

#define USB_DIAG_INTERFACE_NUMBER                  (USB_SPEC_MIN_INTERFACE_NUMBER)

#if defined(USB_DYNAMIC_CONFIGURATION)
/* and for dynamic composites: */   /* Example only as yet */
#define USB_MASTEMMI_MAST_INTERFACE_NUMBER  (USB_SPEC_MIN_INTERFACE_NUMBER)
#define USB_MASTEMMI_EMMI_INTERFACE_NUMBER  (USB_SPEC_MIN_INTERFACE_NUMBER +1)
#endif /*  (USB_DYNAMIC_CONFIGURATION) */


/*
 * Include configuration file specific to the USBD controller. Also check that
 * only one USBD controller has been defined.
 */

#undef USBD_CONTROLLER_DEFINED

#if defined(USB_NETCHIP_NET2890)
/* NetChip NET2890. */
# if defined(USBD_CONTROLLER_DEFINED)
#  error More than one USBD controller defined in build.
# else
#  define USBD_CONTROLLER_DEFINED
#  include <usbnet2890ep.h>
# endif
#endif











      











      

#if defined(UPGRADE_HERMON_PLATFORM)
# if defined(USB_DC_HERMON)
/* Intel Hermon embedded controller. */
#  if defined(USBD_CONTROLLER_DEFINED)
#   error More than one USBD controller defined in build.
#  else
#   define USBD_CONTROLLER_DEFINED
#   include <usbhermonep.h>
#  endif
# endif
#endif

/* Confirm that at least one USBD controller has been defined. */
#if !defined(USBD_CONTROLLER_DEFINED)
# error No USBD controller defined in build.
#else
# undef USBD_CONTROLLER_DEFINED
#endif


#if !defined(USB_DYNAMIC_CONFIGURATION)
/* In the Static model we need to equate _PHYSICAL_ENDPOINT with_ENDPOINT */

#define USB_MAST_IN_ENDPOINT          USB_MAST_IN_PHYSICAL_ENDPOINT
#define USB_MAST_OUT_ENDPOINT         USB_MAST_OUT_PHYSICAL_ENDPOINT

#define USB_EMMI_IN_ENDPOINT          USB_EMMI_IN_PHYSICAL_ENDPOINT
#define USB_EMMI_OUT_ENDPOINT         USB_EMMI_OUT_PHYSICAL_ENDPOINT

#define USB_COMM_CTRL_IN_ENDPOINT     USB_COMM_CTRL_IN_PHYSICAL_ENDPOINT
#define USB_COMM_DATA_IN_ENDPOINT     USB_COMM_DATA_IN_PHYSICAL_ENDPOINT
#define USB_COMM_DATA_OUT_ENDPOINT    USB_COMM_DATA_OUT_PHYSICAL_ENDPOINT

#define USB_DIAG_IN_ENDPOINT          USB_DIAG_IN_PHYSICAL_ENDPOINT
#define USB_DIAG_OUT_ENDPOINT         USB_DIAG_OUT_PHYSICAL_ENDPOINT

#endif /*  (USB_DYNAMIC_CONFIGURATION) */

#endif /* USBENDPOINTS_H */
#endif /* UPGRADE_USB */
/* END OF FILE */


/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2006 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/api/inc/ubnd_cfg.h#6 $
 *   $Revision: #6 $
 *   $DateTime: 2007/03/30 13:53:25 $
 **************************************************************************
 * File Description:
 *
 * 3G Band configuration
 **************************************************************************/

#if !defined (UBND_CFG_H)
#define       UBND_CFG_H

#include "uas_asn.h"
#include "ut_mcc_mnc.h"
#include <mbndType.h> //for Frequency/*********** add*/

/***************************************************************************
* Nested Include Files
***************************************************************************/


/***************************************************************************
*   Manifest Constants
***************************************************************************/

/****************************************************************************
 * Tdd Bands Bitmap 
 *****************************************************************************/
#define NO_TDD_BANDS  		   0x0000 	/* Should always be set to 0!*/
#define TDD_BAND_1_BIT         0x0001
#define TDD_BAND_2_BIT         0x0002
#define TDD_BAND_3_BIT         0x0004
#define TDD_BAND_4_BIT         0x0008
#define TDD_BAND_5_BIT         0x0010
/*CQ00019884 add E band add begin*/
#define TDD_BAND_6_BIT         0x0020
#define TDD_BAND_7_BIT         0x0040 /*CQ00026312 fix error*/
#define TDD_BAND_8_BIT         0x0080 /*CQ00026312 fix error*/
#define TDD_BAND_9_BIT         0x0100 /*CQ00026312 add*/
/*CQ00019884 add E band add end*/

#if 0/*CQ00107338 remove*/
#if defined (LTE_W_PS)
#define GSM_BAND_BIT           0x80000   /* used to indicate currently in GSM mode */
/*********** changed from 0x8000*/
#else
#define GSM_BAND_BIT           0x1000    /* used to indicate currently in GSM mode */
#endif
#endif

#define GSM_BAND_BIT           0x1000    /* used to indicate currently in GSM mode */
/*CQ00107338 changed back to old value */

#define ALL_TDD_BANDS  		   0xffff 
#define TDD_BAND_LAST_BIT      TDD_BAND_5_BIT    /* Last TDD band bit */
#define TDD_VALID_BAND_MASK    0x000F

/******************************************************************************
 * Tdd Bands Indices
 *****************************************************************************/
#define TDD_BAND_1_INDEX   			0
#define TDD_BAND_2_INDEX   			1
#define TDD_BAND_3_INDEX   			2
#define TDD_BAND_4_INDEX   			3
/*CQ00019884 add E band add begin*/
#define TDD_BAND_5_INDEX   			4
#define TDD_BAND_6_INDEX   			5
#define TDD_BAND_7_INDEX   			6
#define TDD_BAND_8_INDEX   			7
/*CQ00019884 add E band add end*/
#define NUM_TDD_BANDS      			(TDD_BAND_8_INDEX+1)    /* Last TDD band index *//*CQ00019884 add E modify, (TDD_BAND_4_INDEX+1)*/
#define INVALID_UARFCN         0xffff
/******************************************************************************
 * UMTS TDD bands definitions
 *
 * Min and Max values of UARFCN for UMTS TDD bands, as defined in 25.102 section 5.4.4.
 *
 *****************************************************************************/


#if !defined (LTE_W_PS)
/***************************************************************************
 *                           	  			Band A                   	   *
 ***************************************************************************/
/* NOTE: For LCR TDD, the supported UARFCN is :
 * 2010 - 2025 MHz  (BAND I:   10054 - 10121 )
 * 1880 - 1920 MHz  (BAND II:   9404 -  9468 )
 * 1880 - 1920 MHz  (BAND III:  9469 -  9533 )
 * 1880 - 1920 MHz  (BAND IV:   9534 -  9596 )
 * 2320 - 2330 MHz	(Band V:	11604 - 11650 )
 * 2330 - 2370 MHz	(Band VI:	11651 - 11716 )
 * 2330 - 2370 MHz	(Band VII:	11717 - 11782 )
 * 2330 - 2370 MHz	(Band VIII: 11783 - 11846 )
*//*CQ00019884 add E band*/

#define MIN_UARFCN_IN_BAND_1   10054
#define MAX_UARFCN_IN_BAND_1   10121
/* No additional UARFCNs defined */
#define NUM_ADD_UARFCN_IN_BAND_1   	0
/** Defines the GCF test UARFCN for band I. */
#define GCF_TEST_UARFCN_IN_BAND_1  	(UUARFCN) 10087
#define NUM_UARFCN_IN_BAND_1   		9

#if defined (UPGRADE_TDD_MULTIBAND)
/***************************************************************************
 *                           	  			Band B                  	   *
 ***************************************************************************/
#define MIN_UARFCN_IN_BAND_2   9404
#define MAX_UARFCN_IN_BAND_2   9468

#define NUM_ADD_UARFCN_IN_BAND_2   0
/** Defines the GCF test UARFCN for band II. */
#define GCF_TEST_UARFCN_IN_BAND_2  	(UUARFCN) 9404
#define NUM_UARFCN_IN_BAND_2   		65       /*CQ00019884 add E band, fix bug*/

/***************************************************************************
 *                           	  			Band C                   	   *
 ***************************************************************************/
#define MIN_UARFCN_IN_BAND_3   9469
#define MAX_UARFCN_IN_BAND_3   9533
#define NUM_ADD_UARFCN_IN_BAND_3   	0
#define NUM_UARFCN_IN_BAND_3   		65       /*CQ00019884 add E band, fix bug*/

#define MIN_UARFCN_IN_BAND_4   9534
#define MAX_UARFCN_IN_BAND_4   9596
#define NUM_ADD_UARFCN_IN_BAND_4   	0
#define NUM_UARFCN_IN_BAND_4   		63       /*CQ00019884 add E band, fix bug*/

/*CQ00019884 add E band add begin*/
#define MIN_UARFCN_IN_BAND_5   11604
#define MAX_UARFCN_IN_BAND_5   11650
#define NUM_ADD_UARFCN_IN_BAND_5   	0
#define NUM_UARFCN_IN_BAND_5   		47

#define MIN_UARFCN_IN_BAND_6   11651
#define MAX_UARFCN_IN_BAND_6   11716
#define NUM_ADD_UARFCN_IN_BAND_6   	0
#define NUM_UARFCN_IN_BAND_6   		66

#define MIN_UARFCN_IN_BAND_7   11717
#define MAX_UARFCN_IN_BAND_7   11782
#define NUM_ADD_UARFCN_IN_BAND_7   	0
#define NUM_UARFCN_IN_BAND_7   		66

#define MIN_UARFCN_IN_BAND_8   11783
#define MAX_UARFCN_IN_BAND_8   11846
#define NUM_ADD_UARFCN_IN_BAND_8   	0
#define NUM_UARFCN_IN_BAND_8   		64
/*CQ00019884 add E band add end*/

#endif

/* Defines the maximum number of UARFCNs in an TDD band (regular+additional). */
#if defined(UPGRADE_TDD_MULTIBAND)
  /* Max number of UARFCNs in an TDD Band is in band 3 and equals to 352. */
  #define MAX_TDD_BAND_FREQS NUM_UARFCN_IN_BAND_2  
#else
  /* Max number of UARFCNs in band 1. */
  #define MAX_TDD_BAND_FREQS NUM_UARFCN_IN_BAND_1
#endif

typedef Int32 Earfcn; //**********
#endif
/***************************************************************************
*   Macro Functions
***************************************************************************/

/***************************************************************************
*   Types
***************************************************************************/

typedef enum TddBandTag
{
    TDD_BAND_1             = TDD_BAND_1_INDEX,
#if defined (UPGRADE_TDD_MULTIBAND)
    TDD_BAND_2             = TDD_BAND_2_INDEX,
    TDD_BAND_3             = TDD_BAND_3_INDEX,
    /*CQ00019884 add E band add begin*/
    TDD_BAND_4             = TDD_BAND_4_INDEX,
    TDD_BAND_5             = TDD_BAND_5_INDEX,
    TDD_BAND_6             = TDD_BAND_6_INDEX,
    TDD_BAND_7             = TDD_BAND_7_INDEX,
    TDD_BAND_8             = TDD_BAND_8_INDEX,
    /*CQ00019884 add E band add begin*/
#endif

    INVALID_TDD_BAND       = 0xFF
}
TddBand;

//ICAT EXPORTED ENUM
typedef enum TddBandModeTag
{
/* @ENUM_DESC@ TDD band mode bit */

    TDD_BAND_1_MODE        = TDD_BAND_1_BIT,
    TDD_BAND_2_MODE        = TDD_BAND_2_BIT,
    TDD_BAND_3_MODE        = TDD_BAND_3_BIT,
    TDD_BAND_4_MODE        = TDD_BAND_4_BIT,
    /*CQ00019884 add E band add begin*/
    TDD_BAND_5_MODE        = TDD_BAND_5_BIT,
    TDD_BAND_6_MODE        = TDD_BAND_6_BIT,
    TDD_BAND_7_MODE        = TDD_BAND_7_BIT,
    TDD_BAND_8_MODE        = TDD_BAND_8_BIT,
    /*CQ00019884 add E band add end*/
    GSM_BAND_MODE          = GSM_BAND_BIT,
    INVALID_TDD_BAND_MODE  = 0xffff
}
TddBandMode;


/****************************************************************************
 * Fdd Bands Bitmap
 *****************************************************************************/
#if defined(DISABLE_FDD_BAND_19)/*********** remove for band 19*/
#define NO_FDD_BANDS  		   0x0000 	/* Should always be set to 0!*/
#define FDD_BAND_1_BIT         0x0001
#define FDD_BAND_2_BIT         0x0002
#define FDD_BAND_3_BIT         0x0004
#define FDD_BAND_4_BIT         0x0008
#define FDD_BAND_5_BIT         0x0010
#define FDD_BAND_6_BIT         0x0020
#define FDD_BAND_7_BIT         0x0040
#define FDD_BAND_8_BIT         0x0080
#define FDD_BAND_9_BIT         0x0100
#define FDD_BAND_10_BIT        0x0200
#define FDD_BAND_11_BIT        0x0400
#define FDD_BAND_12_BIT        0x0800
#define FDD_BAND_13_BIT        0x1000
#define FDD_BAND_14_BIT        0x2000
#else
/*********** add band 19 begin*/
#define NO_FDD_BANDS  		   0x00000 	/* Should always be set to 0!*/
#define FDD_BAND_1_BIT         0x00001
#define FDD_BAND_2_BIT         0x00002
#define FDD_BAND_3_BIT         0x00004
#define FDD_BAND_4_BIT         0x00008
#define FDD_BAND_5_BIT         0x00010
#define FDD_BAND_6_BIT         0x00020
#define FDD_BAND_7_BIT         0x00040
#define FDD_BAND_8_BIT         0x00080
#define FDD_BAND_9_BIT         0x00100
#define FDD_BAND_10_BIT        0x00200
#define FDD_BAND_11_BIT        0x00400
#define FDD_BAND_12_BIT        0x00800
#define FDD_BAND_13_BIT        0x01000
#define FDD_BAND_14_BIT        0x02000
#define FDD_BAND_15_BIT        0x04000
#define FDD_BAND_16_BIT        0x08000
#define FDD_BAND_17_BIT        0x10000
#define FDD_BAND_18_BIT        0x20000
#define FDD_BAND_19_BIT        0x40000
#endif
/*********** add band 19 end*/


#if defined(DISABLE_FDD_BAND_19)/*********** remove for band 19*/
#define ALL_FDD_BANDS  		   0xffff
#else
#define ALL_FDD_BANDS  		   0xFFFFFFFF /*********** add for band 19*/
#endif

#if defined(DISABLE_FDD_BAND_19)
#define FDD_BANDS_JAPAN_BIT    (FDD_BAND_6_BIT|FDD_BAND_7_BIT|FDD_BAND_8_BIT|FDD_BAND_9_BIT)
#else
#define FDD_BANDS_JAPAN_BIT    (FDD_BAND_6_BIT|FDD_BAND_7_BIT|FDD_BAND_8_BIT|FDD_BAND_9_BIT|FDD_BAND_19_BIT)/*********** add band 19*/
#endif
#define FDD_BANDS_US_BIT	   (FDD_BAND_2_BIT|FDD_BAND_4_BIT|FDD_BAND_5_BIT)
#define FDD_BANDS_EUROPE_BIT   (FDD_BAND_1_BIT|FDD_BAND_3_BIT)

#define FDD_BANDS_SIB5BIS_SCHEDULED  (FDD_BAND_4_BIT|FDD_BAND_10_BIT);

#if defined(DISABLE_FDD_BAND_19)
#define FDD_BAND_LAST_BIT      FDD_BAND_14_BIT
#else
#define FDD_BAND_LAST_BIT      FDD_BAND_19_BIT    /* Last FDD band bit */ /*********** changed from band 14 to band 19*/
#endif

#define FDD_BANDS_MASK  (FDD_BAND_1_BIT|FDD_BAND_2_BIT|FDD_BAND_3_BIT|FDD_BAND_4_BIT|FDD_BAND_5_BIT|FDD_BAND_6_BIT|FDD_BAND_7_BIT)
#if defined(DISABLE_FDD_BAND_19)
#define FDD_BANDS_MASK2 (FDD_BAND_8_BIT|FDD_BAND_9_BIT|FDD_BAND_10_BIT|FDD_BAND_11_BIT|FDD_BAND_12_BIT|FDD_BAND_13_BIT|FDD_BAND_14_BIT)
#else
#define FDD_BANDS_MASK2 (FDD_BAND_8_BIT|FDD_BAND_9_BIT|FDD_BAND_10_BIT|FDD_BAND_11_BIT|FDD_BAND_12_BIT|FDD_BAND_13_BIT|FDD_BAND_14_BIT|FDD_BAND_19_BIT)/*********** add band 19*/
#endif


/* Band 7 not supported by UE */
#if defined(DISABLE_FDD_BAND_19)/*********** changed from 0x03BF*/
#define FDD_VALID_BAND_MASK    0x03BF
#else
#define FDD_VALID_BAND_MASK    0x803BF /*********** changed from 0x03BF*/
#endif

/******************************************************************************
 * Fdd Bands Indices
 *****************************************************************************/

#define FDD_BAND_1_INDEX            0
#define FDD_BAND_2_INDEX            1
#define FDD_BAND_3_INDEX            2
#define FDD_BAND_4_INDEX            3
#define FDD_BAND_5_INDEX            4
#define FDD_BAND_6_INDEX            5
#define FDD_BAND_7_INDEX            6
#define FDD_BAND_8_INDEX            7
#define FDD_BAND_9_INDEX            8
#define FDD_BAND_10_INDEX           9
#define FDD_BAND_11_INDEX           10
#define FDD_BAND_12_INDEX           11
#define FDD_BAND_13_INDEX           12
#define FDD_BAND_14_INDEX           13
/*********** add band 19 begin*/
#define FDD_BAND_15_INDEX           14
#define FDD_BAND_16_INDEX           15
#define FDD_BAND_17_INDEX           16
#define FDD_BAND_18_INDEX           17
#define FDD_BAND_19_INDEX           18
#define FDD_BAND_20_INDEX           19
#define FDD_BAND_21_INDEX           20
#define FDD_BAND_22_INDEX           21
/*********** add band 19 end*/
#define FDD_BAND_INVALID_INDEX		0xFF

#if defined(DISABLE_FDD_BAND_19)
#define NUM_FDD_BANDS               (FDD_BAND_14_INDEX+1)
#else
#define NUM_FDD_BANDS               (FDD_BAND_19_INDEX+1)    /* Last FDD band index */ /*********** changed from 14 to 19*/
#endif

#define NUM_FDD_BAND_REGIONS         5
#define NUM_MCC_REGION_EXEMPTIONS    2

#define MCC_JAPAN_1         0x440   /* Japan MCCs. Used to distinguish Band VI from Band V. */
#define MCC_JAPAN_2         0x441   /* See 25.101 Section 5.4.4 */

#define INVALID_UARFCN      0xffff


#if defined (LTE_W_PS)
/******************************************************************************
 * UMTS FDD bands definitions
 *
 * Min and Max values of UARFCN, TxRx Separation & Num of Additional UARFCNs
 * for UMTS FDD bands, as defined in 25.101 section 5.4.4.
 *
 *****************************************************************************/

/* Band I */
/***********/
#define MIN_UARFCN_IN_BAND_1        10562
#define MAX_UARFCN_IN_BAND_1        10838
#define TXRX_SEPARATION_BAND_1      950
/* No additional UARFCNs defined */
#define NUM_ADD_UARFCN_IN_BAND_1    0
/** Defines the GCF test UARFCN for band I. */
#define GCF_TEST_UARFCN_IN_BAND_1   (UUARFCN) 10700
#define NUM_UARFCN_IN_BAND_1        (((MAX_UARFCN_IN_BAND_1 + 1) - MIN_UARFCN_IN_BAND_1) + NUM_ADD_UARFCN_IN_BAND_1)
#define DL_UARFCN_FORMULA_OFFSET_BAND_1 0
#define DL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_1 0
#define UL_UARFCN_FORMULA_OFFSET_BAND_1 0/*********** add*/
#define UL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_1 0/*********** add*/

/* Band II */
/***********/
#define MIN_UARFCN_IN_BAND_2        9662
#define MAX_UARFCN_IN_BAND_2        9938
#define TXRX_SEPARATION_BAND_2      400
/* There are also 12 additional UARFCNs within range 412-687 */
#define NUM_ADD_UARFCN_IN_BAND_2    12
/** Defines the GCF test UARFCN for band II. */
#define GCF_TEST_UARFCN_IN_BAND_2   (UUARFCN) 9800
#define NUM_UARFCN_IN_BAND_2        (((MAX_UARFCN_IN_BAND_2 + 1) - MIN_UARFCN_IN_BAND_2) + NUM_ADD_UARFCN_IN_BAND_2)
#define DL_UARFCN_FORMULA_OFFSET_BAND_2 0
#define DL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_2 18501 /*freuqency*10, ********** add*/
#define UL_UARFCN_FORMULA_OFFSET_BAND_2 0
#define UL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_2 18501 /*freuqency*10, ********** add*/

/* Band III */
/***********/
#define MIN_UARFCN_IN_BAND_3        1162
#define MAX_UARFCN_IN_BAND_3        1513
#define TXRX_SEPARATION_BAND_3      225
/* No additional UARFCNs defined */
#define NUM_ADD_UARFCN_IN_BAND_3    0
#define NUM_UARFCN_IN_BAND_3        (((MAX_UARFCN_IN_BAND_3 + 1) - MIN_UARFCN_IN_BAND_3) + NUM_ADD_UARFCN_IN_BAND_3)
#define DL_UARFCN_FORMULA_OFFSET_BAND_3 15750 		/*freuqency*10, ********** add*/
#define DL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_3 0
#define UL_UARFCN_FORMULA_OFFSET_BAND_3 15250 		/*freuqency*10, ********** add*/
#define UL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_3 0/*********** add*/

/* Band IV */
/***********/
#define MIN_UARFCN_IN_BAND_4        1537
#define MAX_UARFCN_IN_BAND_4        1738
#define TXRX_SEPARATION_BAND_4      225
/* There are also 9 additional UARFCNs within range 1887-2087 */
#define NUM_ADD_UARFCN_IN_BAND_4    9
#define NUM_UARFCN_IN_BAND_4        (((MAX_UARFCN_IN_BAND_4 + 1) - MIN_UARFCN_IN_BAND_4) + NUM_ADD_UARFCN_IN_BAND_4)
#define DL_UARFCN_FORMULA_OFFSET_BAND_4 18050			 /*freuqency*10, ********** add*/
#define DL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_4 17351 /*freuqency*10, ********** add*/	
#define UL_UARFCN_FORMULA_OFFSET_BAND_4 14500			 /*freuqency*10, ********** add*/
#define UL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_4 13801 /*freuqency*10, ********** add*/

/* Band V */
/***********/
#define MIN_UARFCN_IN_BAND_5        4357
#define MAX_UARFCN_IN_BAND_5        4458
#define TXRX_SEPARATION_BAND_5      225
/* There are also 6 additional UARFCNs within range 1007-1087 */
#define NUM_ADD_UARFCN_IN_BAND_5    6
/** Defines the GCF test UARFCN for band V. */
#define GCF_TEST_UARFCN_IN_BAND_5   (UUARFCN) 4400
#define NUM_UARFCN_IN_BAND_5        (((MAX_UARFCN_IN_BAND_5 + 1) - MIN_UARFCN_IN_BAND_5) + NUM_ADD_UARFCN_IN_BAND_5)
#define DL_UARFCN_FORMULA_OFFSET_BAND_5 0
#define DL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_5 6701 /*freuqency*10, ********** add*/	
#define UL_UARFCN_FORMULA_OFFSET_BAND_5 0				/*********** add*/
#define UL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_5 6701 /*freuqency*10, ********** add*/	

/* Band VI */
/***********/
#define MIN_UARFCN_IN_BAND_6        4387
#define MAX_UARFCN_IN_BAND_6        4413
#define TXRX_SEPARATION_BAND_6      225
/* There are also 2 additional UARFCNs 1037 & 1062 */
#define NUM_ADD_UARFCN_IN_BAND_6    2
#define NUM_UARFCN_IN_BAND_6        (((MAX_UARFCN_IN_BAND_6 + 1) - MIN_UARFCN_IN_BAND_6) + NUM_ADD_UARFCN_IN_BAND_6)
#define DL_UARFCN_FORMULA_OFFSET_BAND_6 0
#define DL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_6 6701 /*freuqency*10, ********** add*/	
#define UL_UARFCN_FORMULA_OFFSET_BAND_6 0				/*********** add*/
#define UL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_6 6701 /*freuqency*10, ********** add*/

/* Band VII */
/***********/
#define MIN_UARFCN_IN_BAND_7        2237
#define MAX_UARFCN_IN_BAND_7        2563
#define TXRX_SEPARATION_BAND_7      225
/* There are also 14 additional UARFCNs within range 2587-2912 */
#define NUM_ADD_UARFCN_IN_BAND_7    14
#define NUM_UARFCN_IN_BAND_7        (((MAX_UARFCN_IN_BAND_7 + 1) - MIN_UARFCN_IN_BAND_7) + NUM_ADD_UARFCN_IN_BAND_7)
#define DL_UARFCN_FORMULA_OFFSET_BAND_7 21750				/*freuqency*10, ********** add*/			
#define DL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_7 21051	/*********** add*/
#define UL_UARFCN_FORMULA_OFFSET_BAND_7 21000				/*freuqency*10, ********** add*/
#define UL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_7 20301	/*freuqency*10, ********** add*/


/* Band VIII */
/***********/
#define MIN_UARFCN_IN_BAND_8        2937
#define MAX_UARFCN_IN_BAND_8        3088
#define TXRX_SEPARATION_BAND_8      225
/* No additional UARFCNs defined */
#define NUM_ADD_UARFCN_IN_BAND_8    0
#define NUM_UARFCN_IN_BAND_8        (((MAX_UARFCN_IN_BAND_8 + 1) - MIN_UARFCN_IN_BAND_8) + NUM_ADD_UARFCN_IN_BAND_8)
#define DL_UARFCN_FORMULA_OFFSET_BAND_8 3400		 /*freuqency*10, ********** add*/	
#define DL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_8 0
#define UL_UARFCN_FORMULA_OFFSET_BAND_8 3400		 /*freuqency*10, ********** add*/ 
#define UL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_8 0 /*********** add*/

/* Band IX */
/***********/
#define MIN_UARFCN_IN_BAND_9        9237
#define MAX_UARFCN_IN_BAND_9        9387
#define TXRX_SEPARATION_BAND_9      475
/* No additional UARFCNs defined */
#define NUM_ADD_UARFCN_IN_BAND_9    0
#define NUM_UARFCN_IN_BAND_9        (((MAX_UARFCN_IN_BAND_9 + 1) - MIN_UARFCN_IN_BAND_9) + NUM_ADD_UARFCN_IN_BAND_9)
#define DL_UARFCN_FORMULA_OFFSET_BAND_9 0
#define DL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_9 0
#define UL_UARFCN_FORMULA_OFFSET_BAND_9 0				/*********** add*/
#define UL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_9 0	/*********** add*/

/* Band X */
/***********/
#define MIN_UARFCN_IN_BAND_10       3112
#define MAX_UARFCN_IN_BAND_10       3388
#define TXRX_SEPARATION_BAND_10     400
/* There are also 12 additional UARFCNs within UARFCN range 3412-3687 */
#define NUM_ADD_UARFCN_IN_BAND_10   12
#define NUM_UARFCN_IN_BAND_10       (((MAX_UARFCN_IN_BAND_10 + 1) - MIN_UARFCN_IN_BAND_10) + NUM_ADD_UARFCN_IN_BAND_10)
#define DL_UARFCN_FORMULA_OFFSET_BAND_10 14900					/*freuqency*10, ********** add*/ 					
#define DL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_10 14301		/*freuqency*10, ********** add*/ 
#define UL_UARFCN_FORMULA_OFFSET_BAND_10 11350					/*freuqency*10, ********** add*/ 
#define UL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_10 10751		/*freuqency*10, ********** add*/ 

/*Band XI */
/***********/
#define MIN_UARFCN_IN_BAND_11       3712
#define MAX_UARFCN_IN_BAND_11       3812
#define TXRX_SEPARATION_BAND_11     240
#define NUM_ADD_UARFCN_IN_BAND_11   0
#define NUM_UARFCN_IN_BAND_11       (((MAX_UARFCN_IN_BAND_11 + 1) - MIN_UARFCN_IN_BAND_11) + NUM_ADD_UARFCN_IN_BAND_11)
#define DL_UARFCN_FORMULA_OFFSET_BAND_11 7360				/*freuqency*10, ********** add*/ 
#define DL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_11 0
#define UL_UARFCN_FORMULA_OFFSET_BAND_11 7330				/*freuqency*10, ********** add*/ 
#define UL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_11 0		 /*********** add*/

/* Band XII */
/***********/
#define MIN_UARFCN_IN_BAND_12       3837
#define MAX_UARFCN_IN_BAND_12       3903
#define TXRX_SEPARATION_BAND_12     150
#define NUM_ADD_UARFCN_IN_BAND_12   6
#define NUM_UARFCN_IN_BAND_12       (((MAX_UARFCN_IN_BAND_12 + 1) - MIN_UARFCN_IN_BAND_12) + NUM_ADD_UARFCN_IN_BAND_12)
#define DL_UARFCN_FORMULA_OFFSET_BAND_12 (-370)				/*freuqency*10, ********** add*/ 
#define DL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_12 (-549)  /*freuqency*10, ********** add*/ 
#define UL_UARFCN_FORMULA_OFFSET_BAND_12 (-220)				/*freuqency*10, ********** add*/ 
#define UL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_12 (-399)	/*freuqency*10, ********** add*/ 

/* Band XIII */
/***********/
#define MIN_UARFCN_IN_BAND_13       4017
#define MAX_UARFCN_IN_BAND_13       4043
#define TXRX_SEPARATION_BAND_13     155
#define NUM_ADD_UARFCN_IN_BAND_13   2
#define NUM_UARFCN_IN_BAND_13       (((MAX_UARFCN_IN_BAND_13 + 1) - MIN_UARFCN_IN_BAND_13) + NUM_ADD_UARFCN_IN_BAND_13)
#define DL_UARFCN_FORMULA_OFFSET_BAND_13 (-550)				/*freuqency*10, ********** add*/
#define DL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_13 (-649)  /*freuqency*10, ********** add*/
#define UL_UARFCN_FORMULA_OFFSET_BAND_13 210				/*freuqency*10, ********** add*/
#define UL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_13 (-111)  /*freuqency*10, ********** add*/

/* Band XIV */
/***********/
#define MIN_UARFCN_IN_BAND_14       4117
#define MAX_UARFCN_IN_BAND_14       4143
#define TXRX_SEPARATION_BAND_14     150
#define NUM_ADD_UARFCN_IN_BAND_14   2
#define NUM_UARFCN_IN_BAND_14       (((MAX_UARFCN_IN_BAND_14 + 1) - MIN_UARFCN_IN_BAND_14) + NUM_ADD_UARFCN_IN_BAND_14)
#define DL_UARFCN_FORMULA_OFFSET_BAND_14 (-630)				/*freuqency*10, ********** add*/
#define DL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_14 (-729)  /*freuqency*10, ********** add*/
#define UL_UARFCN_FORMULA_OFFSET_BAND_14 120				/*freuqency*10, ********** add*/
#define UL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_14 21		/*freuqency*10, ********** add*/

/*********** add band 19 begin*/
#if !defined(DISABLE_FDD_BAND_19)
/* Band XV - R E S E R V E D */
/***********/
#define MIN_UARFCN_IN_BAND_15       INVALID_UARFCN
#define MAX_UARFCN_IN_BAND_15       INVALID_UARFCN
#define TXRX_SEPARATION_BAND_15     INVALID_UARFCN
#define NUM_ADD_UARFCN_IN_BAND_15   0
#define NUM_UARFCN_IN_BAND_15       (((MAX_UARFCN_IN_BAND_15 + 1) - MIN_UARFCN_IN_BAND_15) + NUM_ADD_UARFCN_IN_BAND_15)
#define DL_UARFCN_FORMULA_OFFSET_BAND_15 (0)
#define DL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_15 (0)
#define UL_UARFCN_FORMULA_OFFSET_BAND_15 0
#define UL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_15 0

/* Band XVI - R E S E R V E D  */
/***********/
#define MIN_UARFCN_IN_BAND_16       INVALID_UARFCN
#define MAX_UARFCN_IN_BAND_16       INVALID_UARFCN
#define TXRX_SEPARATION_BAND_16     INVALID_UARFCN
#define NUM_ADD_UARFCN_IN_BAND_16   0
#define NUM_UARFCN_IN_BAND_16       (((MAX_UARFCN_IN_BAND_16 + 1) - MIN_UARFCN_IN_BAND_16) + NUM_ADD_UARFCN_IN_BAND_16)
#define DL_UARFCN_FORMULA_OFFSET_BAND_16 (0)
#define DL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_16 (0)
#define UL_UARFCN_FORMULA_OFFSET_BAND_16 0
#define UL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_16 0


/* Band XVII - R E S E R V E D  */
/***********/
#define MIN_UARFCN_IN_BAND_17       INVALID_UARFCN
#define MAX_UARFCN_IN_BAND_17       INVALID_UARFCN
#define TXRX_SEPARATION_BAND_17     INVALID_UARFCN
#define NUM_ADD_UARFCN_IN_BAND_17   0
#define NUM_UARFCN_IN_BAND_17       (((MAX_UARFCN_IN_BAND_17 + 1) - MIN_UARFCN_IN_BAND_17) + NUM_ADD_UARFCN_IN_BAND_17)
#define DL_UARFCN_FORMULA_OFFSET_BAND_17 (0)
#define DL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_17 (0)
#define UL_UARFCN_FORMULA_OFFSET_BAND_17 0
#define UL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_17 0


/* Band XVIII - R E S E R V E D  */
/***********/
#define MIN_UARFCN_IN_BAND_18       INVALID_UARFCN
#define MAX_UARFCN_IN_BAND_18       INVALID_UARFCN
#define TXRX_SEPARATION_BAND_18     INVALID_UARFCN
#define NUM_ADD_UARFCN_IN_BAND_18   0
#define NUM_UARFCN_IN_BAND_18       (((MAX_UARFCN_IN_BAND_18 + 1) - MIN_UARFCN_IN_BAND_18) + NUM_ADD_UARFCN_IN_BAND_18)
#define DL_UARFCN_FORMULA_OFFSET_BAND_18 (0)
#define DL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_18 (0)
#define UL_UARFCN_FORMULA_OFFSET_BAND_18 0
#define UL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_18 0



/* Band XIX */
/***********/
#define MIN_UARFCN_IN_BAND_19       712
#define MAX_UARFCN_IN_BAND_19       763
#define TXRX_SEPARATION_BAND_19     400
#define NUM_ADD_UARFCN_IN_BAND_19   3
#define NUM_UARFCN_IN_BAND_19       (((MAX_UARFCN_IN_BAND_19 + 1) - MIN_UARFCN_IN_BAND_19) + NUM_ADD_UARFCN_IN_BAND_19)
#define DL_UARFCN_FORMULA_OFFSET_BAND_19 (7350)
#define DL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_19 (7201)
#define UL_UARFCN_FORMULA_OFFSET_BAND_19 7700
#define UL_ADDITIONAL_UARFCN_FORMULA_OFFSET_BAND_19 7551

#endif
/*********** add band 19 end*/
#endif
/* Defines the maximum number of UARFCNs in an FDD band (regular+additional). */

/* Max number of UARFCNs in an FDD Band is in band 3 and equals to 352. */
#define MAX_FDD_BAND_FREQS NUM_UARFCN_IN_BAND_3

#define MAX_FDD_BAND_PRIME_UARFCNS	 25

#define INVALID_REDIRECTION_INITIATED 0xFFFE

#define FDD_UARFCN_BANDWIDTH	(36)	 /* 3.6Mhz UARFCN carrier bandwith multiple by 10 */ /*add ********** */

//********** add begin
#if defined (UPGRADE_RRC_NO_CM_ON_ADJ_FREQ)
#define MAX_BW_FDD 50 //[5 MHZ]x10
#endif
//********** add end
typedef enum FddBandTag
{
    FDD_BAND_1             = FDD_BAND_1_INDEX,
    FDD_BAND_2             = FDD_BAND_2_INDEX,
    FDD_BAND_3             = FDD_BAND_3_INDEX,
    FDD_BAND_4             = FDD_BAND_4_INDEX,
    FDD_BAND_5             = FDD_BAND_5_INDEX,
    FDD_BAND_6             = FDD_BAND_6_INDEX,
    FDD_BAND_7             = FDD_BAND_7_INDEX,
    FDD_BAND_8             = FDD_BAND_8_INDEX,
    FDD_BAND_9             = FDD_BAND_9_INDEX,
    FDD_BAND_10            = FDD_BAND_10_INDEX,
    FDD_BAND_11            = FDD_BAND_11_INDEX,
    FDD_BAND_12            = FDD_BAND_12_INDEX,
    FDD_BAND_13            = FDD_BAND_13_INDEX,
    FDD_BAND_14            = FDD_BAND_14_INDEX,
/*********** add band 19 begin*/
#if !defined(DISABLE_FDD_BAND_19)
	FDD_BAND_15 		   = FDD_BAND_15_INDEX,
	FDD_BAND_16 		   = FDD_BAND_16_INDEX,
	FDD_BAND_17 		   = FDD_BAND_17_INDEX,
	FDD_BAND_18 		   = FDD_BAND_18_INDEX,
	FDD_BAND_19 		   = FDD_BAND_19_INDEX,
#endif

/*********** add band 19 end*/
    INVALID_FDD_BAND       = 0xFF
}
FddBand;

//ICAT EXPORTED ENUM

typedef enum FddBandModeTag
{
/* @ENUM_DESC@ FDD band mode bit */
    FDD_BAND_1_MODE        = FDD_BAND_1_BIT,
    FDD_BAND_2_MODE        = FDD_BAND_2_BIT,
    FDD_BAND_3_MODE        = FDD_BAND_3_BIT,
    FDD_BAND_4_MODE        = FDD_BAND_4_BIT,
    FDD_BAND_5_MODE        = FDD_BAND_5_BIT,
    FDD_BAND_6_MODE        = FDD_BAND_6_BIT,
    FDD_BAND_7_MODE        = FDD_BAND_7_BIT,
    FDD_BAND_8_MODE        = FDD_BAND_8_BIT,
    FDD_BAND_9_MODE        = FDD_BAND_9_BIT,
    FDD_BAND_10_MODE       = FDD_BAND_10_BIT,
    FDD_BAND_11_MODE       = FDD_BAND_11_BIT,
    FDD_BAND_12_MODE       = FDD_BAND_12_BIT,
    FDD_BAND_13_MODE       = FDD_BAND_13_BIT,
    FDD_BAND_14_MODE       = FDD_BAND_14_BIT,
/*********** add band 19 begin*/
#if !defined(DISABLE_FDD_BAND_19)
    FDD_BAND_15_MODE       = FDD_BAND_15_BIT,
    FDD_BAND_16_MODE       = FDD_BAND_16_BIT,
    FDD_BAND_17_MODE       = FDD_BAND_17_BIT,
    FDD_BAND_18_MODE       = FDD_BAND_18_BIT,
    FDD_BAND_19_MODE       = FDD_BAND_19_BIT,
#endif
/*********** add band 19 end*/
    FDD_GSM_BAND_MODE      = 0x80000, /*CQ00107338 change from GSM_BAND_BIT,*/
    INVALID_FDD_BAND_MODE  = 0xFFFF
}
FddBandMode;


typedef Int8 FddBandIndex;

#if defined(DISABLE_FDD_BAND_19)
typedef Int16 FddBandsMask;
#else
typedef Int32 FddBandsMask;  /*********** changed from Int16 to Int32*/
#endif

typedef enum FddBandRegionTag
{
	FDD_BAND_REGION_UNKNOWN,
	FDD_BAND_REGION_EUROPE,
	FDD_BAND_REGION_JAPAN,
	FDD_BAND_REGION_US,
	FDD_BAND_REGION_ALL
}
FddBandRegion;

typedef struct FddBandRegionTableElement
{
    FddBandRegion   region;
    FddBandsMask	regionMask;
}
FddBandRegionTableElement;

typedef struct FddBandRegionTableTag
{
    FddBandRegionTableElement 	fddRegionElements[NUM_FDD_BAND_REGIONS];
    Int8   						mccExemptionsSize;
    Mcc    						mccExemptions[NUM_MCC_REGION_EXEMPTIONS];
}
FddBandRegionTable;

typedef enum FddBandSib5bisScheduled
{
    FDD_BAND_SIB5BIS_NOT_SCHEDULED,
    FDD_BAND_SIB5BIS_SCHEDULED,
    FDD_BAND_SIB5BIS_SCHEDULED_BAND_IND_REQUIRED
}
FddBandSib5OrSib5bisScheduled;

typedef struct FddBandDataTableElementTag
{
   FddBandsMask                bandBit;				/* FDD band bit */
   UUARFCN                     minUarfcn;				/* FDD band min UARFCN */
   UUARFCN                     maxUarfcn;				/* FDD band max UARFCN */
   UUARFCN                     gcfTestUarfcn;			/* FDD band GCF test UARFCN */
   Int8                        numAdditionalUarfcn;	/* FDD band num of Additional UARFCNs */
   UUARFCN                     (*additional_p)[];		/* FDD band Additional UARFCNs array */
   UUARFCN                     numUarfcns; 			/* FDD band total num of UARFCNs (regular + additional) */
   Int16                       numPrimeUarfcns;		/* FDD band num of Prime UARFCNs */
   UUARFCN                     primeUarfcns[MAX_FDD_BAND_PRIME_UARFCNS];/* FDD band Prime UARFCNs array */
   UUARFCN                     txRxSeperation;			/* FDD band TX-RX separation (In UARFCN units) */
   Boolean                     uarfcnIdentifiesBand;	/* FDD band can be Identified by UARFCN */
   FddBandSib5OrSib5bisScheduled sib5bisIsScheduled;
   SignedInt16                   dlUarfcnFormulaOffset;
   SignedInt16                   dlAdditionalUarfcnFormulaOffset;
   SignedInt16                   ulUarfcnFormulaOffset;/*********** add*/
   SignedInt16                   ulAdditionalUarfcnFormulaOffset;/*********** add*/
}
FddBandDataTableElement;

typedef struct FddBandOverlappingBandsElementTag
{
   FddBandIndex  sib5ScheduledBand;
   FddBandIndex  sib5BisScheduledBand;
   FddBandIndex  bandToScan;
}
FddBandOverlappingBandsElement;

//ICAT EXPORTED STRUCT
typedef struct FddBandPrimeUarfcnListTag
{
   Int8     numPrimeUarfcns;
   UUARFCN  primeUarfcns[MAX_FDD_BAND_PRIME_UARFCNS];
}
FddBandPrimeUarfcnList;

//ICAT EXPORTED STRUCT
typedef struct FddBandPrimeUarfcnListsTag
{
   FddBandPrimeUarfcnList  primeList[NUM_FDD_BANDS];
}
FddBandPrimeUarfcnLists;

#if 0
#if defined (ON_PC)
#if defined (UPGRADE_GENIE_INTERNAL)
#define LTE_MAX_FREQ        8
#define LTE_BAND_NUM        64

#define NO_TDLTE_BANDS           0x0000     /* Should always be set to 0!*/
#define TDLTE_BAND_1_BIT         0x0001 
#define TDLTE_BAND_2_BIT         0x0002
#define TDLTE_BAND_3_BIT         0x0004
#define TDLTE_BAND_4_BIT         0x0008
#define TDLTE_BAND_5_BIT         0x0010
#define TDLTE_BAND_6_BIT         0x0020
#define TDLTE_BAND_7_BIT         0x0040
#define TDLTE_BAND_8_BIT         0x0080
/* ********** begin */
#define TDLTE_BAND_9_BIT         0x0100
#define TDLTE_BAND_10_BIT        0x0200
#define TDLTE_BAND_11_BIT        0x0400
#define TDLTE_BAND_12_BIT        0x0800

#define TDLTE_BAND_LAST_BIT      0x1000   /* Last TDLTE band bit, ********** add */
/* ********** end */

typedef Int32 TddLteBandsMask;

typedef enum TdLteBandModeTag
{
    TDLTE_BAND_1_MODE        = TDLTE_BAND_1_BIT,
    TDLTE_BAND_2_MODE        = TDLTE_BAND_2_BIT,
    TDLTE_BAND_3_MODE        = TDLTE_BAND_3_BIT,
    TDLTE_BAND_4_MODE        = TDLTE_BAND_4_BIT,
    TDLTE_BAND_5_MODE        = TDLTE_BAND_5_BIT,
    TDLTE_BAND_6_MODE        = TDLTE_BAND_6_BIT,
    TDLTE_BAND_7_MODE        = TDLTE_BAND_7_BIT,
    TDLTE_BAND_8_MODE        = TDLTE_BAND_8_BIT,
    /* ********** begin */
    TDLTE_BAND_9_MODE        = TDLTE_BAND_9_BIT,
    TDLTE_BAND_10_MODE       = TDLTE_BAND_10_BIT,
    TDLTE_BAND_11_MODE       = TDLTE_BAND_11_BIT,
    TDLTE_BAND_12_MODE       = TDLTE_BAND_12_BIT,
    /* ********** end */
    TDLTE_GSM_BAND_MODE      = GSM_BAND_BIT,
    INVALID_TDLTE_BAND_MODE  = 0xffff
}
TdLteBandMode;

#define NO_FDDLTE_BANDS           0x00000000     /* Should always be set to 0!*/
#define FDDLTE_BAND_1_BIT         0x00000001 
#define FDDLTE_BAND_2_BIT         0x00000002
#define FDDLTE_BAND_3_BIT         0x00000004
#define FDDLTE_BAND_4_BIT         0x00000008
#define FDDLTE_BAND_5_BIT         0x00000010
#define FDDLTE_BAND_6_BIT         0x00000020
#define FDDLTE_BAND_7_BIT         0x00000040
#define FDDLTE_BAND_8_BIT         0x00000080
#define FDDLTE_BAND_9_BIT         0x00000100 
#define FDDLTE_BAND_10_BIT        0x00000200
#define FDDLTE_BAND_11_BIT        0x00000400
#define FDDLTE_BAND_12_BIT        0x00000800
#define FDDLTE_BAND_13_BIT        0x00001000
#define FDDLTE_BAND_14_BIT        0x00002000
#define FDDLTE_BAND_15_BIT        0x00004000
#define FDDLTE_BAND_16_BIT        0x00008000
#define FDDLTE_BAND_17_BIT        0x00010000
#define FDDLTE_BAND_18_BIT        0x00020000
#define FDDLTE_BAND_19_BIT        0x00040000
#define FDDLTE_BAND_20_BIT        0x00080000
#define FDDLTE_BAND_21_BIT        0x00100000
/* ********** begin */
#define FDDLTE_BAND_22_BIT        0x00200000
#define FDDLTE_BAND_23_BIT        0x00400000
#define FDDLTE_BAND_24_BIT        0x00800000
#define FDDLTE_BAND_25_BIT        0x01000000
#define FDDLTE_BAND_26_BIT        0x02000000
#define FDDLTE_BAND_27_BIT        0x04000000
#define FDDLTE_BAND_28_BIT        0x08000000
#define FDDLTE_BAND_29_BIT        0x10000000
/* ********** end */

typedef Int32 FddLteBandsMask;    
                                  
#define INVALID_FDDLTE_BAND_MODE  0xffffffff
#endif
#endif
#endif

typedef Int8 TddBandIndex;
typedef Int16 TddBandsMask;

#if defined (UPGRADE_TDD_MULTIBAND)

typedef struct TddBandDataTableElementTag
{
	TddBandsMask				bandBit;				/* TDD band bit */
	UUARFCN                     minimum;				/* TDD band min UARFCN */
    UUARFCN                     maximum;				/* TDD band max UARFCN */
    UUARFCN                     gcfTestUarfcn;			/* TDD band GCF test UARFCN */
	Int16						numAdditionalUarfcn;	/* TDD band num of Additional UARFCNs */
    UUARFCN                     (*additional_p)[];		/* TDD band Additional UARFCNs array */
	UUARFCN						numUarfcns; 			/* TDD band total num of UARFCNs (regular + additional) */
}
TddBandDataTableElement;


extern TddBandDataTableElement tddBandDataTable [NUM_TDD_BANDS];
extern TddBandIndex bandIndexGlobal;
#endif

extern const FddBandRegionTable fddRegionTable;
extern FddBandDataTableElement fddBandDataTable [NUM_FDD_BANDS];
extern FddBandIndex bandIndexGlobal;

#if !defined (LTE_W_PS) //********** removed
/* ********** begin */
#define TDLTE_BAND_START          33
#define TDLTE_BAND_END            44

#define FDDLTE_BAND_START         1
#define FDDLTE_BAND_END           31  /*********** change to 31*/
/* ********** end */

typedef Int32 TdLteBandsMaskForLCR;
typedef Int32 FddLteBandsMaskFor23G;
#endif//LTE_W_PS//********** removed

/******************************************************************************
 * Function Prototypes
 ******************************************************************************/


extern Boolean 
UbndIsUarfcnGcfTestUarfcn (const UUARFCN uarfcn
#if defined (UPGRADE_TDD_MULTIBAND)
																	  ,const TddBandIndex bandIndex
#else
   #if defined (LTE_W_PS)
   , const FddBandIndex bandIndex
   #endif
#endif
									   );

#if defined (UPGRADE_TDD_MULTIBAND)
extern TddBandIndex 
	UbndGetTddBandIndexForUarfcn (const UUARFCN uarfcn);

extern Boolean
UbndIsUarfcnValid (const UUARFCN uarfcn);

extern TddBandsMask 
	UbndGetTddBandBitForUarfcn (const UUARFCN uarfcn);
extern Boolean 
	UbndTddBandHasAdditonalUarfcns (const TddBandIndex bandIndex);
#endif

#if defined (LTE_W_PS)
extern Boolean UbndIsUarfcnRegular(const UUARFCN uarfcn_dl, const FddBandIndex bandIndex);

extern Boolean UbndIsUarfcnAdditional (const UUARFCN uarfcn_dl, const FddBandIndex bandIndex);

UUARFCN UbndGetAddUarfcnEquivalent (const UUARFCN addUarfcn_dl, const FddBandIndex bandIndex);
#else
#if defined (UPGRADE_TDD_MULTIBAND)
extern Boolean 
	UbndIsUarfcnRegular(const UUARFCN uarfcn, const TddBandIndex bandIndex);

extern Boolean 
	UbndIsUarfcnAdditional (const UUARFCN uarfcn, const TddBandIndex bandIndex);

UUARFCN 
	UbndGetAddUarfcnEquivalent (const UUARFCN addUarfcn_dl, const TddBandIndex bandIndex);
#endif
#endif


extern Boolean UbndIsUarfcnInStaticPrimeList (const UUARFCN uarfcn_dl, const FddBandIndex bandIndex);

extern void UbndSetStaticPrimeLists (FddBandPrimeUarfcnLists *primeUarfcnLists_p);

extern Boolean UbndGetFddBandIndexForUarfcn (const UUARFCN uarfcn_dl, FddBandIndex *bandIndex);

extern Boolean UbndIsUarfcnValid (const UUARFCN uarfcn_dl);

extern UUARFCN UbndFreqToUarfcn (float freq, FddBandIndex band);

//********** add begin
#if defined (UPGRADE_RRC_NO_CM_ON_ADJ_FREQ)|| defined(UPGRADE_PLMS)
extern Frequency UbndUarfcnToFreq (UUARFCN uarfcn, FddBandIndex band);
#endif //UPGRADE_RRC_NO_CM_ON_ADJ_FREQ
//********** add end

extern FddBandsMask UbndGetFddBandBitForUarfcn (const UUARFCN uarfcn_dl);

extern UUARFCN UbndGetFddBandTxRxSeparation (const FddBandIndex bandIndex);

extern UUARFCN UbndGetFddBandTxRxSeparationForUarfcn (const UUARFCN uarfcn_dl);

extern FddBandRegion UbndGetFddBandRegionForBandIndex (const FddBandIndex bandIndex);

extern FddBandsMask UbndGetRegionMaskForFddBandRegion (const FddBandRegion fddBandRegion);

extern Boolean UbndIsFddBandInRegion (const FddBandIndex bandIndex, const FddBandRegion region);

extern Boolean UbndIsUarfcnInRegion (const UUARFCN uarfcn_dl, const FddBandRegion region);

extern Boolean UbndFddBandCanBeIdentifiedByUarfcn (const FddBandIndex bandIndex);

extern FddBandSib5OrSib5bisScheduled UbndSib5BisIsScheduledInBand (const FddBandIndex bandIndex);

extern Int16 UbndGetDlFormulaOffset (const FddBandIndex bandIndex);

extern Int16 UbndGetAdditionalDlFormulaOffset (const FddBandIndex bandIndex);

extern FddBandIndex UbndGetOverlappingBand (const FddBandIndex bandIndex, Boolean sib5BisScheduled);

Boolean UbndShouldOverlappingBandBeScanned (FddBandsMask bandBitToScan, FddBandsMask supportedBands);

extern Boolean UbndFddBandHasAdditonalUarfcns (const FddBandIndex bandIndex);

void UbndFddBandModeToFddBandNum (
    FddBandMode             bandMode,
    URadioFrequencyBandFDD  *radioFrequencyBandFDD_p,
    URadioFrequencyBandFDD2 *radioFrequencyBandFDD2_p);

Boolean UbndMapBandFDDToBitMask (
    const Boolean bandFddPresent,
    const URadioFrequencyBandFDD bandFdd,
    const Boolean bandFdd2Present,
    const URadioFrequencyBandFDD2 bandFdd2,
    FddBandsMask* fddBandBits_p);

FddBandIndex UbndOneFddBandBitMaskToBandIndex (FddBandsMask bandBitToScan); /*********** - add */

#if !defined (LTE_W_PS)//********** removed
Boolean UbndCheckEarfcnIsSupported (const Earfcn euarfcn, TdLteBandsMaskForLCR bitmask);/* ********** change UEARFCN to Earfcn */
TdLteBandsMaskForLCR UbndConvertEbndMask (Int32 bandMask);
void UbndGetarfcnForEBnd (const Int8 bandIndex, Earfcn *minFreqInBand, Earfcn *maxFreqInBand);/* ********** change UEARFCN to Earfcn */
Int8 UbndGetBandIndexForTddEarfcn (const Earfcn earfcn);   /* ********** add *//* ********** change UEARFCN to Earfcn */
#endif//********** removed
#if 0//********** removed
Boolean UbndCheckFddEarfcnIsSupported (const UEARFCN euarfcn, TdLteBandsMaskForLCR bitmask);
TdLteBandsMaskForLCR UbndConvertFddEbndMask (Int32 bandMask);
void UbndGetarfcnForFddEBnd (const Int8 bandIndex, UEARFCN *minFreqInBand, UEARFCN *maxFreqInBand);
#endif//********** removed
UUARFCN UbndFrequencyToUarfcn (Frequency freq, FddBandIndex band);
UUARFCN UbndGetMinRegularUarfcnsForUtraBand (const FddBandIndex bandIndex);
UUARFCN UbndGetMaxRegularUarfcnsForUtraBand (const FddBandIndex bandIndex);
void UbndGetFreqRangeForUarfcn(FreqRangeInfo* freqRangeInfo_p,  UUARFCN cellUarfcnDownlink);
Int16 UbndGetNumAdditionalUarfcnForUtraBand (const FddBandIndex bandIndex);
UUARFCN UbndFrequencyToRegularUarfcn (Frequency freq, FddBandIndex band);
UUARFCN UbndFrequencyToAdditonalUarfcn (Frequency freq, FddBandIndex band);
FddBandsMask UbndGetFddBandBitForBandIndex (FddBandIndex bandIndex);
Boolean UbndIsFreqInUtraBand(Frequency freq, FddBandIndex fddBand);
UUARFCN UbndChangeUarfcnToOverlappingBand (UUARFCN uarfcn, FddBandIndex newBand);
//********** removed begin
//// ********** begin
//#if defined (UPGRADE_4G_FROM_3G_MFBI)  
//Boolean UbndCheckLteBandIsSupported (const URadioFrequencyBandEUTRA bandInd, FddLteBandsMaskFor23G supportedBitmask); // ********** add	
//#endif
//// ********** end
//********** removed end
Boolean UbndIsUarfcnBelongsToMoreThanOneBand(UUARFCN uarfcn);

#endif /* UBND_CFG_H */

/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/3g.mod/lib/src/ulbgrabmutil.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2011/03/07 16:27:25 $
 **************************************************************************
 * File Description:
 *
 * Function declarations for ulbgrabmutil.c
 **************************************************************************/

#if !defined (ULBGRABMUTIL_H)
#define       ULBGRABMUTIL_H

#include <system.h>
#if defined(UMTS7_PRE_R8)
#include <kernel.h>
#endif
#include <xrabmdata.h>
#if defined (ENABLE_UPLANE_STATISTICS)
#include <uplanestats_sig.h>
#endif /* ENABLE_UPLANE_STATISTICS */

#define MIN_VALID_NSAPI_VALUE GPRS_NSAPI_5

#if defined(UMTS7_PRE_R8)
#define ULBG_WAIT_SDU_LIST_OPT_SEM        KiWaitSemaphore (KI_ULBG_SDU_LIST_OPT)
#define ULBG_RELEASE_SDU_LIST_OPT_SEM     KiIncSemaphore (KI_ULBG_SDU_LIST_OPT)
#endif

//extern UlbgRabmPdpEntity* UlbgRabmCreateNewPdp (XRabmEntity *sn, Nsapi nsapi, Boolean *nsapiAlreadyExists);
//extern UlbgRabmPdpEntity* UlbgRabmFindPdp (XRabmEntity *sn, Nsapi nsapi);
extern void UlbgDeactivateDlbgRabmNsapi(XRabmEntity *sn,XRabmPdpEntity* pdp);
extern void UlbgRabmReleaseNsapi (XRabmEntity *sn, XRabmPdpEntity *pdp);
extern void UlbgRabmSendQueuedUplinkNpdus (XRabmEntity *sn, XRabmPdpEntity *pdp);
extern Boolean UlbgRabmIsRtQos (XRabmPdpEntity *pdp);
extern void UlbgRabmInitPdp (XRabmEntity *sn, Int16 i);
extern Int8 UlbgRabmNumActivePdps (XRabmEntity *sn);
//extern void UlbgReleaseUmtsPdpInfo(Nsapi nsapi);


#if defined (ENABLE_UPLANE_STATISTICS)
extern void RabmFillStatistics(XRabmEntity *sn,UpdcpStatistics *updcpStatistics);
#endif /* ENABLE_UPLANE_STATISTICS */
extern void UlbgRabmUpdatePdpState(XRabmEntity *sn,RabmState* pdpIndex, RabmState nextState);


#endif

/* END OF FILE */

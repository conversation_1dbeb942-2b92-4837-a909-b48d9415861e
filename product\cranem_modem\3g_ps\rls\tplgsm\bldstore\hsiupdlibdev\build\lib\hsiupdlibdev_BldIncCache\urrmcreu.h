/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urrmcreu.h#3 $
 *   $Revision: #3 $
 *   $DateTime: 2012/5/19 14:47:38 $
 **************************************************************************
 * File Description:
 *
 ***************************************************************************
 *
 * Revision Details
 **************************************************************************/

#if !defined (URRMCREU_H)
#define       URRMCREU_H


/***************************************************************************
* Nested Include Files
***************************************************************************/
#include <urrmcrty.h>

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/

/***************************************************************************
*   Macro Functions
***************************************************************************/

/***************************************************************************
 * Types
 ***************************************************************************/
typedef struct EuMeasCellDataTag
{
    Earfcn           earfcn; //CQ00107230 change  UEARFCN to Earfcn
    UEUTRA_PhysicalCellIdentity         physicalCellIdentity;
    SignedInt16            measVal;
    Int8       measIndex;
}
EuMeasCellData;

typedef struct EuMeasValListTag
{
    Int8                            cellNum;
    EuMeasCellData  cellResults[maxCellMeas];       /*modify for CQ00026015, change maxEUTRACellPerFreq to maxCellMeas*/
}
EuMeasValList;

typedef struct EuRawValuesTag
{
    SignedInt16                   eutra_Rsrp;
    SignedInt16                   eutra_Rsrq;
}
EuRawValues;

typedef struct EuFilterValuesTag
{
    SignedInt32                   eutra_Rsrp;
    SignedInt32                   eutra_Rsrq;
}
EuFilterValues;

typedef struct UrrEuMeasResultsTag
{
    EuRawValues                  rawValues;
    EuFilterValues                 filterValues [MAX_FILTERS];
}
UrrEuMeasResults;

typedef struct EuMeasStatusTag
{
    Int8     rsrpStatus;
    Int8     rsrqStatus;
}
EuMeasStatus;

typedef struct EuMeasCountsTag
{
    Int8                           noOfRsrpMeas;
    Int8                           noOfRsrqMeas;
}
EuMeasCounts;

typedef struct EuCellDataTag
{
    UEUTRA_PhysicalCellIdentity         physicalCellIdentity;
    Int8                           measResIndex;
}
EuCellData;

typedef struct EuFreqInfoTag
{
    Earfcn                                            earfcn; //CQ00107230 change  UEARFCN to Earfcn
    Boolean                                                 eutraBandWidthPresent;
    UEUTRA_MeasurementBandwidth   eutraBandWidth;
    Int8                            blackListCellNumber;
    UEUTRA_PhysicalCellIdentity       blackListedCells[maxEUTRACellPerFreq];
    Int8                            cellNumber;
    EuCellData     cellData[maxEUTRACellPerFreq];
}
EuFreqInfo;

typedef struct EuMeasInfoTag
{
    Int32          measResultsMask;  /* Bit mask indicating which locations in
                                        measResults are occupied.     */
    UrrEuMeasResults  measResults [maxCellMeas];   /*modify for CQ00026015, change maxEUTRACellPerFreq to maxCellMeas*/
                                     /* Measurement results.          */
    EuMeasStatus   measStatus;       /* Ongoing measurements status.  */
    EuMeasCounts       measCounts;  /*not for each freq, but for each measId*/
}
EuMeasInfo;


typedef struct EuMeasDataTag
{
    Int8      occupied;       /* Indicates which positions are occupied in EUTRA frequency list. */
    Int8      freqNum;
    EuFreqInfo         freqInfo [maxNumEUTRAFreqs]; /* EUTRA frequency list. */
    EuMeasInfo      *measInfo;      /* Ongoing EUTRA measurements data.   */
    McrFilters        filters;
    McrBestCell3dEventForEutra  currentBestCell3dEvent;
}
EuMeasData;

/***************************************************************************
 * Typed Constants
 ***************************************************************************/

/***************************************************************************
 * Function Prototypes
 ***************************************************************************/
void McreuInitData (Boolean activation);

void McreuStartInterRM (UMeasurementIdentity measId,
                        Boolean              interRATHandoverFail);
                        //Boolean              freqListChanged);

void McreuStoreAndStartMCInterRM (UInterRATMeasurement_r9 *interRM_p,
                                  UMeasurementIdentity measId);

void McreuModifyInterRM (UMeasurementControl_r9_IEs *ie_p);

void McreuSendCphyMonitorLteCellReq (Int8  measId, Int8 rsrp, Int8 rsrq, Int8 highPriority);

void McreuHandleCphyMonitorLteCellInd (CphyMonitorLteCellInd *sig_p);

void McreuSendCurrentFreqListInfoToL1c (void);

void McreuHandleInterFreqHHO (void);

void McreuHandleFailInterFreqHHO (void);

void McreuHandleInterRMTimerExpiry (TimerId              timerId,
                                    UMeasurementIdentity measId);

void McreuRestartPeriodicTimer (TimerId timerId,UMeasurementIdentity measId);

void McreuStopInterRM (UMeasurementIdentity measId,
                       Boolean              leaveDch,
                       Boolean              handover);

void McreuDeleteInterRM (McrEutraMeas *interRM_p);

void McreuUpdateEvent3AStatus (UMeasurementIdentity measId);

Boolean McreuConfigComplete (UInterRATMeasurement_r9 *interRM_p);

void McreuStoreSibEutraFreqs (Int8               noOfEutraFreqs,
                            CsrMcrLteFrequencyInfo *eutraFreqList_p);


void McreuInitFreqInfoList (void); /*added for DMCR DEVELOPMENT*/

EuMeasData * McreuGetEuMeasPtr (void);//********** added
//********** added begin
Int8 McreuMapRsrp (SignedInt16 rsrpVal);
Int8 McreuMapRsrq (SignedInt16 rsrqVal);
//********** added end
  
#endif  /* URRMCRIR_H */



/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/umameas.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/27 15:10:43 $
 **************************************************************************
 * File Description:
 *
 *    Contains the internal declarations for UMAC
 **************************************************************************/

#if !defined (UMAMEAS_H)
#define       UMAMEAS_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <system.h>
#include <kernel.h>

#include <cmac_sig.h>
#include <umac_sig.h>
#include <uphy_sig.h>

#include <umautl.h>

/* Interface Functions */
size_t UmaMeasInit (UmacEntity *umac_p);
void UmaMeasToIdle (void);
/* Internal Signal Handler */
void UmaMeasPeriodicExpiry(SignalBuffer *rxSignal_p);
/* External Signal Handlers */
void UmaMeasHandleCmacStopReq(SignalBuffer *sigbuf_p);
void UmaMeasHandleCmacQualityReq(SignalBuffer *sigbuf_p);
void UmaMeasHandleCmacTrafficReq(SignalBuffer *sigbuf_p);
void UmaMeasHandleCmacGetReq(SignalBuffer *sigbuf_p);
void UmaMeasHandleEvent(SignalBuffer *sigbuf_p);

/* Functional interface to umaul.c / umadl.c */
void UmaMeasHandleReconfiguration(Boolean isUplink);

/*********** add by taoye begin 20150522*/
void ClearPeriodicTrafficValumeMeasurements(void);
/************* add by taoye end 20150522*/


#endif

/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/sys/sys.typ/api/inc/ttconfig.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/27 16:16:26 $
 **************************************************************************
 * File Description : Global configuration parameters
 **************************************************************************/

#ifndef TTCONFIG_H
#define TTCONFIG_H

/****************************************************************************
 *  WARNING WARNING WARNING WARNING WARNING WARNING WARNING WARNING WARNING
 *
 *  TTPCom do not add new items to this file unless they are system wide
 *  and non-Application specific. They should be nothing to do with GSM,
 *  GPRS, 3G, Bluetooth or any other application.
 *
 *  WARNING WARNING WARNING WARNING WARNING WARNING WARNING WARNING WARNING
 ***************************************************************************/
/****************************************************************************
 * Definitions
 ****************************************************************************/

/*
** Mechanism for conditional compiling code dependant on PC/Target
** environment. Default is on target.
*/
/* Detect Borland PC compiler */
#if !defined (ON_PC) && defined (__WIN32__)
#   define ON_PC
#endif
/* Detect Microsoft PC compiler */
#if !defined (ON_PC) && defined (_WIN32)
#   define ON_PC
#endif

#endif
/* END OF FILE */

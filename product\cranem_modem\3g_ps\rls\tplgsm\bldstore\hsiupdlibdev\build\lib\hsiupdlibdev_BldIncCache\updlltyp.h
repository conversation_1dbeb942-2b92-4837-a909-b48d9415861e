/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/3g.mod/lib/src/updlltyp.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/26 20:17:00 $
 **************************************************************************
 * File Description:
 *
 *      UPDCP Linked list type definitions
 **************************************************************************/

#if !defined (UPDLLTYP_H)
#define UPDLLTYP_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <utllist.h>
#include <npdu_typ.h>
#include <urlc_sig.h>
#include <psinterface.h>

/***************************************************************************
* Manifest Constants
***************************************************************************/

/***************************************************************************
* Macro Functions
***************************************************************************/

/***************************************************************************
* Types
***************************************************************************/
/*
This type includes the information stored in the linked list.
It is the minimum information required from the N-PDU signal.
*/
typedef struct UpdcpNpduInfoTag
{
    Npdu                        npdu;
    UrlcMessageUnitIdentifier   mui;
    Int16                       sn;
    Boolean                     sentToRlc;
    Boolean                     ackedByRlc;  /* To process out of sequence cnf's */
    Boolean                     retransmit;  /* For discard with re-establish */
    UrlBearerMode               urlBearerMode; /* Determines if TMM block should
                                                  be freed by PDCP */

    SnUlSduList                 *srcPlatformNode;                                              
}
UpdcpNpduInfo;

/* Type definitions for the linked list are done by this macro */
UT_DOUBLE_LINK_LIST_DECLARE (UpdcpNpduLList, UpdcpNpduInfo)

/* Return value for delete operations */
typedef enum UpdcpLListRetTag
{
    NPDU_DELETED,
    NPDU_MARKED_ACKED,
    NPDU_NOT_FOUND
}
UpdcpLListRet;

/***************************************************************************
* Typed Constants
***************************************************************************/

/***************************************************************************
* Function Prototypes
***************************************************************************/

#endif

/* END OF FILE */

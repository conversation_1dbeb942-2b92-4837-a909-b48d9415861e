/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

                                       /**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/tplgsm/utinc/ut_mcc_mnc.h#4 $
 *   $Revision: #4 $
 *   $DateTime: 2006/08/02 16:16:34 $
 **************************************************************************/
/** \file
 *
 * Utility library for helper functions related to MCCs and MNCs.
 **************************************************************************/

#ifndef UT_MCC_MNC_H
#define UT_MCC_MNC_H

/***************************************************************************
 * Nested Include Files
 ***************************************************************************/

#if !defined (SYSTEM_H)
#include <system.h>
#endif


#if defined (__cplusplus)
extern "C" {
#endif

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/

/** Length of full PLMN name.
 */
#define SIM_PLMN_NAME_FULL_LENGTH   (45)
/** Length of short PLMN name. - 8+1 for trailing 0
 */
#define SIM_PLMN_NAME_ABBR_LENGTH   (11)
/** Length of country initials.
 */
#define COUNTRY_INITIALS_LENGTH     (4)
/** Length of full PLMN name.
 */

#define UT_PLMN_NAME_FULL_LENGTH   (25)

#define PLMN_NAME_FULL_LENGTH       (UT_PLMN_NAME_FULL_LENGTH)
/** Length of short PLMN name.
 */
#define PLMN_NAME_ABBR_LENGTH       (SIM_PLMN_NAME_ABBR_LENGTH)
/** Length of format specifier.
 */
#define FORMAT_SPECIFIER_LENGTH     (14)
/** Number of user defined Plmns in the list stored in Nvram.
 */
#define NVRAM_PLMN_LIST_SIZE        (5)

/***************************************************************************
 * Type Definitions
 ***************************************************************************/

/** \addtogroup MM_SIM
* @{ */


/** Type for Mobile Network Code
 */
typedef Int16 Mnc;

/** Type for Mobile Country Code
 */
typedef Int16 Mcc;


/** Type for AccessTechnologyId.
*For coding of the access technology field, see ETSI TS 131 102.\n
  * A bitmap is used. the meaning of each bit is as follows\n
 * b=1 access technology selected.\n
 * b=0 accesstechnology not selected.\n
 *\n
 * first byte: \n
 * bit 8 specifies whether UTRAN is selected.\n
 * second byte: \n
 * bit 8 specifies whether GSM is selected.\n
 * bit 7 specifies whether GSM COMPACT is selected.\n
 */
typedef Int16 AccessTechnologyId;

/**@}*/

/** Type for Public Land Mobile Network definition.
 */
//ICAT EXPORTED STRUCT
typedef struct PlmnTag
{
  Mcc                mcc;              /**< Mobile Country Code. */
  /* @ITEM_DESC@ Mobile Country Code @ITEM_MODE@ ReadWrite @ITEM_UNIT@  number*/
  Mnc                mnc;              /**< Mobile Network Code. */
  /* @ITEM_DESC@ Mobile Network Code @ITEM_MODE@ ReadWrite @ITEM_UNIT@  number*/
  AccessTechnologyId accessTechnology; /**< Access Technology: GSM_ACCESS_TECHNOLOGY, UTRAN_ACCESS_TECHNOLOGY or GSM_COMPACT_ACCESS_TECHNOLOGY.   */
  /* @ITEM_DESC@ Access Technology ID. For coding see ETSI TS 131 102. @ITEM_MODE@ ReadWrite @ITEM_UNIT@  bitmap
 @ITEM_DEPENDENCY@ ETSI TS 131 102. */
}
Plmn;

/** Type for PLMN with MNC length.
 */
//ICAT EXPORTED STRUCT
typedef struct SimPlmnTag
{
/* @STRUCT_DESC@ PLMN with MNC length */
	Plmn    plmn;                   /**< PLMN.                         */
	/* @ITEM_DESC@ PLMN @ITEM_MODE@ ReadWrite @ITEM_UNIT@ see structure definition */
	Boolean mncThreeDigitsDecoding; /**< Is three digit encoding used. */
	/* @ITEM_DESC@ Is three digit encoding used @ITEM_MODE@ ReadWrite @ITEM_UNIT@ on/off (0-not active, 1-active) */
}
SimPlmn;

/** \addtogroup ABMM_AP
* @{ */


/**\var PlmnNameCodingScheme
 * \brief PLMN name coding schemes.
 */
 //ICAT EXPORTED ENUM
typedef enum PlmnNameCodingSchemeTag
{
  PLMN_CODING_DEFAULT, /**< Default coding scheme. */
  PLMN_CODING_UCS2     /**< UCS2 coding scheme.    */
}
PlmnNameCodingScheme;

/**@}*/

/** Frequency Band.
 */
//ICAT EXPORTED ENUM
typedef enum FreqBandTag
{
    FreqBand_dcs1800Band,
    FreqBand_pcs1900Band
}
FreqBand;

/** \addtogroup ABMM_AP
* @{ */


/** Type for full network name.
 */
// TBD ICAT EXPORTED typedef
typedef Int8  FullName[ PLMN_NAME_FULL_LENGTH ];
/** Type for abbreviated network name.
 */
// TBD ICAT EXPORTED typedef
typedef Int8  AbbrName[ PLMN_NAME_ABBR_LENGTH ];
/** Type for country initials.
 */
// TBD ICAT EXPORTED typedef
typedef Int8  Initials[ COUNTRY_INITIALS_LENGTH ];
/** Type for format specifier.
 */
// TBD ICAT EXPORTED typedef
typedef Int8  FormatSpecifier[ FORMAT_SPECIFIER_LENGTH ];

/**@}*/
/**\struct PlmnName
 * \brief This struct holds PLMN name information
 */
 //ICAT EXPORTED STRUCT
typedef struct PlmnNameTag
{
/* @STRUCT_DESC@ PLMN Name */
  PlmnNameCodingScheme plmnCoding; /**< Coding scheme for 'full', 'abbr' & 'initials'. */
  /* @ITEM_DESC@  @ITEM_MODE@ ReadWrite @ITEM_UNIT@  See enum for values*/
  FullName             full;       /**< Full network name (long).                      */
  AbbrName             abbr;       /**< Abbreviated network name (short).              */
  Initials             initials;   /**< Country initials.                              */
  FormatSpecifier      format;     /**< Format specifier.                              */
}
PlmnName;



/** Type for PLMN Id.
 */
//ICAT EXPORTED STRUCT
typedef struct PlmnIdTag
{
/* @STRUCT_DESC@ PLMN ID */

  Boolean       present;       /**< Is present.              */
/* @ITEM_DESC@ Is present @ITEM_MODE@ ReadWrite @ITEM_UNIT@ on/off (0-not active, 1-active) */
  Plmn          plmn;          /**< PLMN.                    */
/* @ITEM_DESC@ PLMN @ITEM_MODE@ ReadWrite @ITEM_UNIT@ see structure definition */
  Boolean       threeDigitMnc; /**< Is the MNC three digit.  */
/* @ITEM_DESC@ Is present @ITEM_MODE@ ReadWrite @ITEM_UNIT@ on/off (0-not active, 1-active) */
  PlmnName      plmnName;      /**< PLMN Name.               */
/* @ITEM_DESC@ PLMN Name @ITEM_MODE@ ReadWrite @ITEM_UNIT@ see structure definition */
}
PlmnId;

/** Type for country initials.
 */
typedef struct UtmmCountryInitialsTag
{
  Mcc       mcc;        /**< Mobile Country Code.  */
  Initials  initials;   /**< Country initials.     */
}
UtmmCountryInitials;

/** Type for network names.
 */
typedef struct UtmmNetworkNamesTag
{
  Plmn              plmn;           /**< PLMN = MNC + MCC.                 */
  AbbrName          abbr;           /**< Abbreviated network name (short). */
 // FormatSpecifier   format;         /**< Format specifier.                 */  //remove 20190306 remvoe format from UtmmNetworkNames table
  FullName          full;           /**< Full network name (long).         */
  FreqBand          frequencyBand;  /**< DCS / PCS selection.              */
}
UtmmNetworkNames;

/** Type for user defined PLMN data.
 */
 #if 0
typedef struct UserDefPlmnDataTag
{
  PlmnId  userDefPlmnDataList[NVRAM_PLMN_LIST_SIZE]; /**< User defined PLMN values. */
  Boolean userDefPlmnDataIsValid;                    /**< Is this store valid?      */
}
UserDefPlmnData;
#endif
#define NATIONAL_ROAMIN_PLMN_LIST_SIZE   32 /*Zhijie, CQ00059226*/ /*CQ00054040*//*CQ00056581 by zhangxia 20130313 26->29*/

typedef struct AbmmNationalRoamingPlmnListTag
{
    SimPlmn current_plmn[NATIONAL_ROAMIN_PLMN_LIST_SIZE];
    SimPlmn Eplmn[NATIONAL_ROAMIN_PLMN_LIST_SIZE];
}AbmmNationalRoamingPlmnList;


/**************************************************************************
 * Function Prototypes
 **************************************************************************/

/** \defgroup ApiUtMccMnc MCC, MNC Utility Functions
 * \ingroup PrdGSMProtoStack
 * \ingroup Api
 * Common utility functions for MCC and MNC handling/translation.
 * Mobile Country Code (MCC) and Mobile Network Code (MNC) may need to be
 * translated into country initials or network alias. This functionality is
 * required by applications as well as protocol stack. This group of APIs
 * provide related functionality to applications.
 * @{
 */

/** Look up a Network PLMN Name and country initials in the
 * constant look-up table (LUT). This table is doubly sorted:
 * firstly on MCC then on MNC.
 *
 * \param plmn_p [in]Pointer which specifies the MCC and MNC.
 * \param name_p [out]The memory pointed by this param is populated with the
 *                      output strings.
 * \retval None.
 */
void    utmmLookupPlmnName        (SimPlmn *plmn_p, PlmnName *name_p);

/** Look up a countrys' initials in the constant look-up table
 * (LUT). The initials are stored in the passed string. The
 * table is sorted on MCC.
 *
 * \param plmn_p     [in]Pointer which specifies the MCC.
 * \param initials_p [out]The memory pointed by this param is populated with the
 *                   output string.
 * \retval None.
 */
void    utmmLookupCountryInitials (Plmn *plmn_p, Char *initials_p);

 /** Compares PLMN received over air (may be 2/3 digit MNC) with
 * PLMN stored in MS.
 *
 * \param plmn1_p        [in]Pointer which specifies the first PLMN.
 * \param threeDigitMnc1 [in]Is three digit coding used?.
 * \param plmn2_p        [in]Pointer which specifies the second PLMN.
 * \param threeDigitMnc2 [in]Is three digit coding used?.
 * \retval Boolean TRUE if PLMNs match.
 */
Boolean utmmPlPlmnsMatch          (Plmn *plmn1_p, Boolean threeDigitMnc1,
                                   Plmn *plmn2_p, Boolean threeDigitMnc2);


/** Check whether Mcc is North American (USA or Canada)
 *
 * \param mcc        [in]Mobile Country Code.
 * \retval Boolean TRUE if MCC is a North American MCC.
 */
Boolean utmmPlIsNorthAmericanMcc  (Mcc mcc);
/*check same Country with CQ00143854 20230530 by yuling begin*/
Boolean utmmPlIsIndianMcc  (Mcc mcc);
Boolean utmmPlIsJapanMcc  (Mcc mcc);
Boolean utmmPlIsChinaMcc  (Mcc mcc);
Boolean utmmPlIsUKMcc  (Mcc mcc);
Boolean utCheckPlPlmnsInSameCountry (Mcc mmc1, Mcc mmc2);

/*check same Country with CQ00143854 20230530 by yuling end*/

/** @} */ /* End of ApiUtMccMnc */

#if defined (__cplusplus)
}
#endif
#endif /* UT_MCC_MNC_H */

/* END OF FILE */



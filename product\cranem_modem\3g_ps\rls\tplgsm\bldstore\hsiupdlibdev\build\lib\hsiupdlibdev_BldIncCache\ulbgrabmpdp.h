/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/3g.mod/lib/src/rabmpdp.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2006/11/07 15:44:33 $
 **************************************************************************
 * File Description:
 *
 * Declarations for RABM PDP module
 **************************************************************************/

#if !(defined ULBGRABMPDP_H)
#define       ULBGRABMPDP_H

/**** INCLUDE FILES ********************************************************/

#include <system.h>
#include <kernel.h>
#include <xrabmdata.h>


/**** FUNCTION DECLARATIONS ************************************************/

extern void UlbgRabmSnXidReq(XRabmEntity *sn);
extern void UlbgRabmSnXidCnf(XRabmEntity *sn,Nsapi nsapi);
extern void UlbgRabmSnDataUnitDataReq(XRabmEntity *sn,Nsapi nsapi);
extern void UlbgRabmSnDataRsp(XRabmEntity *sn);
#endif

/* END OF FILE */

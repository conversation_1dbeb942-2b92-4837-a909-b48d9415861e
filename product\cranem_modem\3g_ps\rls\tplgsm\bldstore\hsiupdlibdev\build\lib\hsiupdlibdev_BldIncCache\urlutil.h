/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urlutil.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2007/03/19 13:59:30 $
 **************************************************************************
 * File Description:
 *
 *      Header file for UrlUtil.c.
 *      Contains function call declarations, constants and types for use
 *      by other URLC modules.
 **************************************************************************/

#if !defined (URLUTIL_H)
#define       URLUTIL_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <system.h>
#include <ki_typ.h>
#include <ki_sigbuf.h>
#include <kitqid.h>
#include <uas_asn.h>
#include <ucipher.h>
#include <kirlgutl.h>

#if defined (URLC_TASK_FLOW_PROTECT)
#include <urlmain.h> // Get urlGDB_gp
#endif

#if defined (ENABLE_URLC_DEBUG) && defined (ON_PC)
#include <stdio.h>
#endif

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/

/***************************************************************************
*   Macro Functions
***************************************************************************/
#if defined (ENABLE_URLC_DEBUG)
#if defined (ON_PC)
#define M_URLC_PRINT( fMT, aRG1, aRG2) printf (fMT, aRG1, aRG2)
#else
#define M_URLC_PRINT( fMT, aRG1, aRG2) M_KiRlgFastPrintf (fMT, aRG1, aRG2)
#endif

#else
#define M_URLC_PRINT( fMT, aRG1, aRG2)
#endif

#define     URLUTIL_TO_STR_X(x)     #x
#define     URLUTIL_TO_STR(x)       URLUTIL_TO_STR_X(x)

#if defined (KI_ENABLE_RAM_LOGGING)
/***************************************************************************
 *
 * Macro       : DevCheck, DevParam, DevAssert, DevFail
 *
 * Scope       : RLC
 *
 * Parameter   : None
 *
 * Description : This section redefines the DevCheck, DevParam, DevAssert and
 *               DevFail macros. The redefinition allows RLC bearer entity
 *               data to be dumped when an assert occurs in URLC.
 *               The redefinition is only necessary when RAM Logging is
 *               enabled.
 *
 ***************************************************************************/
#if defined DevCheck
#undef DevCheck
#define DevCheck( cOND, vAL1, vAL2, vAL3) \
{ \
    if (!(cOND)) \
    { \
        UrlcFailHandler( TRUE, \
                         #cOND, \
                         (Int32) vAL1, \
                         (Int32) vAL2, \
                         (Int32) vAL3, \
                         MODULE_NAME, \
                         __LINE__); \
    } \
}
#endif /* DevCheck */

#if defined DevParam
#undef DevParam
#define DevParam( vAL1, vAL2, vAL3) \
{ \
    UrlcFailHandler( TRUE, \
                     PNULL, \
                     (Int32) vAL1, \
                     (Int32) vAL2, \
                     (Int32) vAL3, \
                     MODULE_NAME, \
                     __LINE__); \
}
#endif /* DevParam */

#if defined DevAssert
#undef DevAssert
#define DevAssert( cOND) \
{ \
    if (!(cOND)) \
    { \
        UrlcFailHandler( FALSE, \
                         #cOND, \
                         0, 0, 0, \
                         MODULE_NAME, \
                         __LINE__); \
    } \
}
#endif /* DevAssert */

#if defined DevFail
#undef DevFail
#define DevFail( mSG) \
{ \
    UrlcFailHandler( FALSE, \
                     mSG, \
                     0, 0, 0, \
                     MODULE_NAME, \
                     __LINE__); \
}
#endif /* DevFail */
#endif /* KI_ENABLE_RAM_LOGGING */

/* The macro performs (Int32)a * (Int32)b / (Int32)c with the highest possible
 * accuracy.
 */
#define URLC_INT32_UNIT_ADJUST_DIVISION(a,b,c) \
( \
    (((Int32)(a)) < (ULONG_MAX / ((Int32)(b)))) ? \
    ( \
        (((Int32)(a)) * ((Int32)(b))) / ((Int32)(c)) \
    ) : \
    ( \
        (((Int32)(c)) < (ULONG_MAX / ((Int32)(b)))) ? \
        ( \
            ((((Int32)(a)) / ((Int32)(c))) * ((Int32)(b))) + \
                    (((((Int32)(a)) % ((Int32)(c))) * ((Int32)(b))) / \
                        ((Int32)(c))) \
        ) : \
        ( \
            ((Int32)(a)) / (((Int32)(c)) / ((Int32)(b))) \
        ) \
    ) \
)


#if defined (KI_RLG_ENABLE_URLC_TRACE_LOG)
#define URL_LOG_RLG_TRACE KiRlgLogUrlcTrace(MODULE_NAME, __LINE__)
#else
#define URL_LOG_RLG_TRACE 
#endif //KI_RLG_ENABLE_URLC_TRACE_LOG

#if !defined (ON_PC)
#if !defined (URLC_TASK_FLOW_PROTECT)
#define URL_WAIT_SEMAPHORE(semaphoreId)     \
    KiRlgLogSimpleEvent (KI_RLG_GKE_WAIT_SEMAPHORE, semaphoreId);\
    KiWaitSemaphore (semaphoreId);\
    KiRlgLogSimpleEvent (KI_RLG_GKE_TAKE_SEMAPHORE, semaphoreId);

#define URL_RELEASE_SEMAPHORE(semaphoreId)  \
    KiRlgLogSimpleEvent (KI_RLG_GKE_RELEASE_SEMAPHORE, semaphoreId);\
    KiIncSemaphore (semaphoreId);

#define URL_WAIT_TASK_FLOW_SEMAPHORE(callId)
#define URL_RELEASE_TASK_FLOW_SEMAPHORE(callId)

#else

#define URL_WAIT_SEMAPHORE(semaphoreId) 
#define URL_RELEASE_SEMAPHORE(semaphoreId)

#if defined (URLC_TASK_FLOW_EXTENDED_DEBUG) 
#define URL_WAIT_TASK_FLOW_SEMAPHORE(callId)        \
    KiRlgLogSimpleEvent (KI_RLG_GKE_WAIT_SEMAPHORE, callId);\
    KiWaitSemaphore (KI_URLC_TASK_FLOW_SEMAPHORE);\
    KiRlgLogSimpleEvent (KI_RLG_GKE_TAKE_SEMAPHORE, callId);\
    /* Got the semaphore, log the time stamp */     \
    urlGDB_gp->taskFlowSemaDebugArray[callId].lastStartTime = cp14ReadCCNT(); 

#define URL_RELEASE_TASK_FLOW_SEMAPHORE(callId) UrlUtilReleaseTaskFlowSema(callId)

#else
//#define URL_WAIT_TASK_FLOW_SEMAPHORE(callId)        \
//    KiRlgLogSimpleEvent (KI_RLG_GKE_WAIT_SEMAPHORE, callId);\
//    KiWaitSemaphore (KI_URLC_TASK_FLOW_SEMAPHORE);\
//    KiRlgLogSimpleEvent (KI_RLG_GKE_TAKE_SEMAPHORE, callId);
//
//#define URL_RELEASE_TASK_FLOW_SEMAPHORE(callId)    \
//    KiRlgLogSimpleEvent (KI_RLG_GKE_RELEASE_SEMAPHORE, callId);\
//    KiIncSemaphore (KI_URLC_TASK_FLOW_SEMAPHORE);
#if defined(RLC_LOW_PRIOR_OPT)
#define URL_WAIT_TASK_FLOW_SEMAPHORE(callId) \
    semaphoreWaitingFlag = TRUE;             \
    KiWaitSemaphore(KI_URLC_TASK_FLOW_SEMAPHORE)
#define URL_WAIT_TASK_FLOW_SEMAPHORE_RLCBG(callId) \
    semaphoreWaitingFlag = FALSE;                  \
    KiWaitSemaphore(KI_URLC_TASK_FLOW_SEMAPHORE)
#define URL_RELEASE_TASK_FLOW_SEMAPHORE(callId) \
    KiIncSemaphore(KI_URLC_TASK_FLOW_SEMAPHORE)
#define URL_RELEASE_TASK_FLOW_SEMAPHORE_RLCBG(callId) \
    KiIncSemaphore(KI_URLC_TASK_FLOW_SEMAPHORE)
#define URL_VERIFY_SEMAPHORE(callId)                   \
    if (semaphoreWaitingFlag == TRUE)                  \
    {                                                  \
        URL_RELEASE_TASK_FLOW_SEMAPHORE_RLCBG(callId); \
        URL_WAIT_TASK_FLOW_SEMAPHORE_RLCBG(callId);    \
    }
#else

#define URL_WAIT_TASK_FLOW_SEMAPHORE(callId)    KiWaitSemaphore (KI_URLC_TASK_FLOW_SEMAPHORE);
#define URL_RELEASE_TASK_FLOW_SEMAPHORE(callId) KiIncSemaphore (KI_URLC_TASK_FLOW_SEMAPHORE);
#endif

#endif //URLC_TASK_FLOW_EXTENDED_DEBUG

#endif  /* URLC_TASK_FLOW_PROTECT */
#else

#define URL_WAIT_TASK_FLOW_SEMAPHORE(callId)  GetMutexHandle(urlDataBaseMutexHandle)
#define URL_RELEASE_TASK_FLOW_SEMAPHORE(callId) ReleaseMutexHandle(urlDataBaseMutexHandle)
#if defined(RLC_LOW_PRIOR_OPT)
#define URL_WAIT_TASK_FLOW_SEMAPHORE_RLCBG(callId)    URL_WAIT_TASK_FLOW_SEMAPHORE(callId)
#define URL_RELEASE_TASK_FLOW_SEMAPHORE_RLCBG(callId) URL_RELEASE_TASK_FLOW_SEMAPHORE(callId)
#define URL_VERIFY_SEMAPHORE(callId)         \
    URL_RELEASE_TASK_FLOW_SEMAPHORE(callId); \
    URL_WAIT_TASK_FLOW_SEMAPHORE(callId);
#endif
#endif /* ON_PC */
#if defined(RLC_LOW_PRIOR_OPT)
#define URL_IS_SEMAPHORE_WAITING_FLAG_ON (semaphoreWaitingFlag == TRUE)
#endif
#if defined (UPGRADE_3G_HSDPA) && !defined (ON_PC) 
#if defined(RLC_LOW_PRIOR_OPT)
#define URL_WAIT_RLC_AM_RX_SDUS_LIST_SEMAPHORE
#define URL_RELEASE_RLC_AM_RX_SDUS_LIST_SEMAPHORE
#define URL_WAIT_RLC_AM_RX_OOS_PDUS_LIST_SEMAPHORE
#define URL_RELEASE_RLC_AM_RX_OOS_PDUS_LIST_SEMAPHORE
#else  //RLC_LOW_PRIOR_OPT
#define URL_WAIT_RLC_AM_RX_SDUS_LIST_SEMAPHORE          URL_WAIT_SEMAPHORE(KI_URLC_AM_RX_SDUS_LIST_SEMAPHORE)
#define URL_RELEASE_RLC_AM_RX_SDUS_LIST_SEMAPHORE       URL_RELEASE_SEMAPHORE(KI_URLC_AM_RX_SDUS_LIST_SEMAPHORE)
#define URL_WAIT_RLC_AM_RX_OOS_PDUS_LIST_SEMAPHORE      URL_WAIT_SEMAPHORE(KI_URLC_AM_RX_OOS_PDUS_LIST_SEMAPHORE)
#define URL_RELEASE_RLC_AM_RX_OOS_PDUS_LIST_SEMAPHORE   URL_RELEASE_SEMAPHORE(KI_URLC_AM_RX_OOS_PDUS_LIST_SEMAPHORE)
#endif  //RLC_LOW_PRIOR_OPT
#else
#define URL_WAIT_RLC_AM_RX_SDUS_LIST_SEMAPHORE
#define URL_RELEASE_RLC_AM_RX_SDUS_LIST_SEMAPHORE
#define URL_WAIT_RLC_AM_RX_OOS_PDUS_LIST_SEMAPHORE
#define URL_RELEASE_RLC_AM_RX_OOS_PDUS_LIST_SEMAPHORE
#endif /* UPGRADE_3G_HSDPA && !ON_PC && !URLC_TASK_FLOW_PROTECT */

#if defined (UPGRADE_3G_EDCH) && !defined(ARMULATOR_TEST_BUILD) 
#if defined (ON_PC)
#define URL_WAIT_RLC_DATABASE_SEMAPHORE                 
#define URL_RELEASE_RLC_DATABASE_SEMAPHORE              
#define URL_WAIT_RLC_UPDATE_BUFF_INF_SEMAPHORE
#define URL_RELEASE_RLC_UPDATE_BUFF_INF_SEMAPHORE
#else
#if defined(RLC_LOW_PRIOR_OPT)
#define URL_WAIT_RLC_DATABASE_SEMAPHORE
#define URL_RELEASE_RLC_DATABASE_SEMAPHORE
#define URL_WAIT_RLC_UPDATE_BUFF_INF_SEMAPHORE
#define URL_RELEASE_RLC_UPDATE_BUFF_INF_SEMAPHORE
#else  //RLC_LOW_PRIOR_OPT
#define URL_WAIT_RLC_DATABASE_SEMAPHORE                 URL_WAIT_SEMAPHORE(KI_URLC_DATABASE_SEMAPHORE)
#define URL_RELEASE_RLC_DATABASE_SEMAPHORE              URL_RELEASE_SEMAPHORE(KI_URLC_DATABASE_SEMAPHORE)
#define URL_WAIT_RLC_UPDATE_BUFF_INF_SEMAPHORE          URL_WAIT_SEMAPHORE(KI_URLC_UPDATE_BUFF_INF_SEMAPHORE)
#define URL_RELEASE_RLC_UPDATE_BUFF_INF_SEMAPHORE       URL_RELEASE_SEMAPHORE(KI_URLC_UPDATE_BUFF_INF_SEMAPHORE)
#endif //RLC_LOW_PRIOR_OPT
#endif /* ON_PC */
#else
#define URL_WAIT_RLC_DATABASE_SEMAPHORE
#define URL_RELEASE_RLC_DATABASE_SEMAPHORE
#define URL_WAIT_RLC_UPDATE_BUFF_INF_SEMAPHORE
#define URL_RELEASE_RLC_UPDATE_BUFF_INF_SEMAPHORE
#endif /* UPGRADE_3G_EDCH && ARMULATOR_TEST_BUILD */

#define URL_UTIL_DIF_SN(num, ref, maxSn, modulus, diff) \
    DevCheck((modulus == URL_AM_SN_MODULUS) || (modulus == URL_UM_SN_MODULUS), modulus, URL_AM_SN_MODULUS, URL_UM_SN_MODULUS); \
    if (modulus == URL_AM_SN_MODULUS) \
    {\
        diff = UrlUtilAmDiffSn(num,ref,maxSn);\
    }\
    else \
    { /* must be UM */ \
        diff = UrlUtilUmDiffSn(num,ref,maxSn);\
    }


/***************************************************************************
 * Types
 ***************************************************************************/

/***************************************************************************
 * Typed Constants
 ***************************************************************************/
#define URLC_MS_PER_SECOND  1000
#if defined(RLC_LOW_PRIOR_OPT)
extern Boolean semaphoreWaitingFlag;
#endif
/***************************************************************************
 * Function Prototypes
 ***************************************************************************/
#if defined (KI_ENABLE_RAM_LOGGING)
extern void UrlcFailHandler (const Boolean debugParamsPresent,
                             const char * errorString_p,
                             const Int32 val1,
                             const Int32 val2,
                             const Int32 val3,
                             const char * filename_p,
                             const Int16 lineNumber);
#endif /* KI_ENABLE_RAM_LOGGING */

extern  SignedInt32
UrlUtilUmDiffSn
    (Int16                          num,
     Int16                          ref,
     Int16                          maxSn
    );
extern  SignedInt32
UrlUtilAmDiffSn
    (Int16                          num,
     Int16                          ref,
     Int16                          maxSn
    );
extern  Boolean
UrlUtilValidSn
    (UrlSequenceNumber              sn,
     UrlSequenceNumber              winLo,
     UrlSequenceNumber              winHi
    );
extern  UrlDataSegmentType
UrlUtilGetDataSegmentType
    (Boolean                        useStartLi,
     Boolean                        useAlternatEBit,
     Int32                          sduBits,
     Int8                           liBits,
     Int16                          pduBits,
     Int16                        * sduBitsUsed_p
    );
extern  Int8
UrlUtilGetSegmentNumLis
    (UrlDataSegmentType             segmentType
    );
extern  void
UrlUtilSetTxDataPduLengthIndicators
    (UrlDataSegmentType             segmentType ,
     Int16                          pduDataOctets ,
     Int8                         * pduListData_p,
     Int32                        * pduListBitOffset_p
    );
#if defined UPGRADE_3G_EDCH
extern  void
UrlUtilSetTxDataPduLengthIndicatorsEdch
    (UrlDataSegmentType             segmentType ,
     Int16                          pduDataOctets ,
     Int8                         * pduListData_p,
     Int32                        * pduListBitOffset_p,
     Boolean                        firstli
    );
#endif /*UPGRADE_3G_EDCH*/
extern  void
UrlUtilCipherListClear
    (BearerIdentity                 bearerId,
     UrlCipherConfigList_p          cipherList_p,
     DtcDirection                   direction
    );

extern  void
UrlUtilCipherListAppend
    (BearerIdentity                 bearerId,
     UrlCipherConfigList_p          cipherList_p,
     UrlSequenceNumber              sn,
     UCipheringAlgorithm_r7         algorithm,
     Int8                           ksi,
     UrlHyperFrameNumber            hfn,
     DtcDirection                   direction
    );

extern  void
UrlUtilCipherListDelete
    (BearerIdentity                 bearerId,
     UrlCipherConfigList_p          cipherList_p,
     Int8                           configId,
     DtcDirection                   direction
    );

extern  Int8
UrlUtilCipherListFind
    (BearerIdentity                 bearerId,
     UrlCipherConfigList_p          cipherList_p,
     UrlSequenceNumber              sn,
     UrlSequenceNumber              winLo,
     UrlSequenceNumber              winHi,
     UrlSequenceNumber              winModulus,
     DtcDirection                   direction
    );

extern  void
UrlUtilCipherListTrackSn
    (BearerIdentity                 bearerId,
     UrlCipherConfigList_p          cipherList_p,
     UrlSequenceNumber              sn,
     UrlSequenceNumber              winHi,
     UrlSequenceNumber              winModulus,
     DtcDirection                   direction
    );

extern  UrlHyperFrameNumber
UrlUtilGetHfn
    (UrlSequenceNumber              sn,
     UrlSequenceNumber              refSn,
     UrlHyperFrameNumber            refHfn,
     UrlHyperFrameNumber            hfnModulus,
     UrlCipherConfigList_p          cipherList_p,
     Int8                           cipherConfig
    );

extern  void
UrlUtilCipherListTrackSn
    (BearerIdentity                 bearerId,
     UrlCipherConfigList_p          cipherList_p,
     UrlSequenceNumber              sn,
     UrlSequenceNumber              winHi,
     UrlSequenceNumber              winModulus,
     DtcDirection                   direction
    );

extern  Boolean
UrlUtilTickTimerExpired
    (KernelTicks                    currTick,
     KernelTicks                    expiryTick,
     KernelTicks                    durationTicks
    );

extern  void
UrlUtilDetermineLiSize
    (Int16                          dataPduOctets,
     Int8                         * liOctets
    );

#if defined (ENABLE_URLC_UNIT_TEST)
extern  void UrlcUtilParseRlcHeaderAndLis
    (Int8                           bearer,
     Int32                          rlcHeader,
     Int8                         * liBuff_p,
     CrlcUnitTestUlInfoRsp        * ulInfoRsp
    );

extern  void UrlcUtilPrintDescriptorInfo
    (dtcF8DescriptorInfo          * currentF8Desc_p
    );

extern  void UrlcUtilPrintDescChainTableHeader
    (char                         * outStr
    );
#endif /* ENABLE_URLC_UNIT_TEST */

#if defined (DATA_IN_SIGNAL)
void
UrlUtilConvertUmDataIndToDataInSignalFmt
    (
     SignalBuffer          * signal_pp
    );
void
UrlUtilConvertTmDataIndToDataInSignalFmt
    (
     SignalBuffer          * signal_pp
    );
void
UrlUtilConvertAmDataIndToDataInSignalFmt
    (
     TaskId                                             taskId,
     SignalBuffer                                     * signal_p
    );
void
UrlUtilConvertUmacDataReqToDataInSignalFmt
    (
     SignalBuffer          * signal_p
    );
void
UrlUtilConvertUmDataReqToTmmFmt
    (
     SignalBuffer          * signal_p
    );
void
UrlUtilConvertTmDataReqToTmmFmt
    (
     SignalBuffer          * signal_p
    );
void
UrlUtilConvertCsdTmDataReqToTmmFmt
    (
     SignalBuffer          * signal_p
    );
void
UrlUtilConvertAmDataReqToTmmFmt
    (
     SignalBuffer          * signal_p
    );
void
UrlUtilConvertUmacDataIndToTmmFmt
    (
     SignalBuffer          * signal_p
    );
void
UrlUtilConvertUmacDlPduListInfoIndToTmmFmt
    (
     SignalBuffer          * signal_p
    );
void
UrlUtilConvertUmacDlPduListInfoRspToDataInSignalFmt
    (
     SignalBuffer          * signal_p
    );
void
UrlUtilConvertAmDataReqToDataInSignalFmt
    (
     SignalBuffer          * signal_p
    );
void
UrlUtilConvertUmDataReqToDataInSignalFmt
    (
     SignalBuffer          * signal_p
    );
void
UrlUtilConvertTmDataReqToDataInSignalFmt
    (
     SignalBuffer          * signal_p
    );
void
UrlUtilConvertUmacTrafficIndToTmmFmt
    (
     SignalBuffer          * signal_p
    );
#if defined (UPGRADE_3G_HSDPA)
void
UrlUtilConvertUmacDlHsPduListInfoIndToTmmFmt
    (
     SignalBuffer          * signal_p
    );

#if defined (ENABLE_URLC_UNIT_TEST)
void
UrlUtilHandleUmacDlEhsPduListInfoInd
    (
     SignalBuffer          * signal_p
    );
#endif

void
UrlUtilConvertUmacDlHsPduListInfoRspToDataInSignalFmt
    (
     SignalBuffer          * signal_p
    );
void
UrlUtilConvertUmacHsDataIndToTmmFmt
    (
     SignalBuffer          * signal_p
    );
#endif /* UPGRADE_3G_HSDPA */
#if defined (ENABLE_URLC_UNIT_TEST)
extern  void
UrlCreateUlLoopbackSignal
    (SignalBuffer          * signal_p,
     UmacDataReq           * umacDataReq_p,
     SignalBuffer          * loopbackSig_p
    );
#endif /* ENABLE_URLC_UNIT_TEST */
#endif /* DATA_IN_SIGNAL */

#if defined (ENABLE_URLC_UNIT_TEST)

void UrlHandleUnitTestPduList(SignalBuffer *rxSignal_p);

#if defined(ENABLE_HS_PDULISTINFO_FUNCTION_CALL)
 extern void UmacEhsPduListInfoIndFn  (UmacEhsPduListInfoInd* umacEhsPduListInfoInd_p ); // decleared in urlmain.c
#endif /* ENABLE_HS_PDULISTINFO_FUNCTION_CALL */

extern  void
UrlcUtilEdchUpdateTrafficInd
    (CrlcUnitTestUpdateTrafficInd *crlcUnitTestUpdateTrafficInd
    );
#endif /* ENABLE_URLC_UNIT_TEST */

#if defined (ENABLE_URL_EXTENDED_DEVCHECK)
#define URL_DEVCHECK_STR_LENGTH          100
#define URL_EXTENDED_DEVCHECK_TIMER_ID   1
#define URL_EXTENDED_DEVCHECK_TIMEOUT_MS 100

extern KiTimer    urlExtendedDevCheckTimer;

extern  void UrlDelayDevCheck (Int32            v1,
                               Int32            v2,
                               Int32            v3,
                               const char      *file,
                               Int16            line);

#undef  DevCheck
#undef  DevParam

#define DevCheck(cOND, v1, v2, v3)                                 \
        {                                                          \
            if(!(cOND))                                            \
            {                                                      \
                UrlDelayDevCheck ((Int32)v1, (Int32)v2, (Int32)v3, \
                                   __FILE__, __LINE__);            \
            }                                                      \
        }
#define DevParam(v1, v2, v3)                                       \
                UrlDelayDevCheck ((Int32)v1, (Int32)v2, (Int32)v3, \
                                   __FILE__, __LINE__);
extern  Boolean
UrlDelayDevCheckTimerExpiry
    (KiTimerExpiry        * kiTimerExpiry_p
    );
#endif /* ENABLE_URL_EXTENDED_DEVCHECK */

#if defined (UPGRADE_3G_EDCH)
extern  void
UrlUtilAllocLiMemAndDescriptor
    (Int32                  bitlength,
     dtcF8DescriptorInfo ** dataCipherInfo_pp,
     dtcF8DescriptorInfo ** liCipherInfo_pp
    );

extern  void
UrlUtilAllocLiDescriptor
    (dtcF8DescriptorInfo  * cipherInfo_p
    );
extern  void
UrlUtilAllocateBufferForDescriptor
    (Int32                  bitlength,
    dtcF8DescriptorInfo   * cipherInfo_p);
#endif /* UPGRADE_3G_EDCH */

#if defined (URLC_TASK_FLOW_PROTECT) && defined (URLC_TASK_FLOW_EXTENDED_DEBUG)
void UrlUtilReleaseTaskFlowSema(UrlSemaCallId callId);
#endif /* URLC_TASK_FLOW_PROTECT */

#endif /* URLUTIL_H */
/* END OF UrlUtil.h */

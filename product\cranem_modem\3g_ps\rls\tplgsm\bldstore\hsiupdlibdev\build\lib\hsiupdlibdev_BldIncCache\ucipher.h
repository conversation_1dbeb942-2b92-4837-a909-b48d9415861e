/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/api/inc/ucipher.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/27 15:10:43 $
 *************************************************************************/
 /** \file
  *  Header file for UCipher.c and UCipherK.c.
  *  Contains function call declarations, constants and types for
  *  use by other 3GPP f8 and f9 algorithms.
 ************************************************************************/

#if !defined (UCIPHER_H)
#define       UCIPHER_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <system.h>

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/

/***************************************************************************
*   Macro Functions
***************************************************************************/

/***************************************************************************
 * Types
 ***************************************************************************/

/***************************************************************************
 * Typed Constants
 ***************************************************************************/

/******************************************************************************/
/** \ingroup ApiCBEcorePLFdriversFrCik
 * This enum is used to select the Ciphering Direction.
 */
/******************************************************************************/
typedef enum UDirectionTag
{
    UDIRECTION_UPLINK   = 0,    /**< UE to RNC. */
    UDIRECTION_DOWNLINK = 1     /**< RNC to UE. */
} UDirection;




                                      
#ifdef F8_F9_SOFT

/***************************************************************************
 * Function Prototypes
 ***************************************************************************/

/** \ingroup ApiCBEcorePLFdriversFrCik
 * @{
 */

/******************************************************************************/
/** This Function implements the 3GPP f8, confidentiality algorithm.
 *  \return void.
 */
/******************************************************************************/
extern void             f8
    (Int8                 * key_p,      /**< Int8 *key_p = Pointer to the cipher key.           */
     Int32                  countC,     /**< Int32 countC = COUNT-C.                            */
     Int32                  bearer,     /**< Int32 bearer = Bearer identity.
                                         *          Note that clause ******* of 25.331
                                         *          states that all users of f8 should
                                         *          subtract 1 before passing it to f8,
                                         *          however to simplify the usage, the
                                         *          subtraction is being done here.             */
     UDirection             dir,        /**< UDirection dir = Direction (uplink or downlink).   */
     Int8                 * data_p,     /**< Int8 *data_p = Pointer to data to be
                                         *                  ciphered/deciphered.                */
     Int16                  bitLength,  /**< Int16 bitLength = Number of bits to be
                                         *                     ciphered/deciphered.             */
     Int16                  bitOffset   /**< Int16 bitOffset = Bit offset from start of data to
                                         *                     first bit of data to be
                                         *                     ciphered/deciphered.             */
    );

/******************************************************************************/
/** This Function implements the 3GPP f9, integrity algorithm.
 *  \return Int8* = Pointer to 32 bit Message Authentication Code.
 */
/******************************************************************************/
extern Int32             f9
    (Int8                 * key_p,      /**< Int8 *key_p = Pointer to the cipher key.           */
     Int32                  countI,     /**< Int32 countI = COUNT-I identity.                   */
     Int32                  fresh,      /**< Int32 fresh = A 32-bit random number input.        */
     UDirection             dir,        /**< UDirection dir = Direction (uplink or downlink).   */
     Int8                 * data_p,     /**< Int8 *data_p = Pointer to data to RRC message.     */
     Int16                  bitLength   /**< Int16 bitLength = Length of RRC message.           */
    );

/** @} */ /* End of ApiCBEcorePLFdriversFrCik group */
#endif /*F8_F9_SOFT*/









                                      
#endif /* UCIPHER_H */
/* End of UCipher.h */

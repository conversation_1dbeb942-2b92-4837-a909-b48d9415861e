/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/url_sig.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2007/03/22 15:29:52 $
 **************************************************************************
 * File Description:
 *
 *    File containing the 'signals' union for URLC.
 **************************************************************************/

#if !defined (URL_SIG_H)
#define       URL_SIG_H

/***************************************************************************
* Nested Include Files
***************************************************************************/
#include <ki_sig.h>
#include <cbmc_sig.h>
#include <cpdc_sig.h>
#include <crlc_sig.h>
#include <urlc_sig.h>
#include <umac_sig.h>
#include <uphy_sig.h>
#include <tmm_sig.h>
#include <ci_dev_engm.h>


#if defined (ENABLE_UPLANE_STATISTICS)
# include <uplanestats_sig.h>
#endif /* ENABLE_UPLANE_STATISTICS */
#if defined (ENABLE_FLEXIBLE_TRACE_FRAMEWORK)
#include <utftf_sig.h>
#endif


#if !defined(ON_PC)
/** UMAC engineering mode report */
typedef CiDevWBHsupaStatisticsEng_Phy 		UmacEdchEngModeStatistics;
#endif /** !ON_PC */

typedef CiDevCommonEngmodeInfoInd			UmacEdchEngModeInd;

typedef CiDevPrimSetExtEngmodeRepOptReq CmacEngEdchModeInfoReq;



union Signal
{
    /* System signals */
    KiTimerExpiry                       kiTimerExpiry;
    UtMemAboveHwmInd                    utMemAboveHwmInd;
    UtMemBelowLwmInd                    utMemBelowLwmInd;

    /* Control plane signals */
    CrlcConfigReq                       crlcConfigReq;
    CrlcSuspendReq                      crlcSuspendReq;
    CrlcSuspendCnf                      crlcSuspendCnf;
    CrlcResumeReq                       crlcResumeReq;
    CrlcStopReq                         crlcStopReq;
    CrlcHaltReq                         crlcHaltReq;
    CrlcContinueReq                     crlcContinueReq;
    CrlcStatusInd                       crlcStatusInd;
    CrlcCountcReq                       crlcCountcReq;
    CrlcCountcCnf                       crlcCountcCnf;
    CrlcCountcInd                       crlcCountcInd;
    CrlcDataPendingInd                  crlcDataPendingInd;
    CrlcPrepareCipherCfgChangeReq       crlcPrepareCipherCfgChangeReq;
    CrlcPrepareCipherCfgChangeCnf       crlcPrepareCipherCfgChangeCnf;
    CrlcAbortCipherConfigReq            crlcAbortCipherConfigReq;
    CrlcCloseTestLoopReq                crlcCloseTestLoopReq;
    CrlcTestModeReq                     crlcTestModeReq;
    CrlcTestModeDiagnosticsRsp          crlcTestModeDiagnosticsRsp;
    CrlcReleaseReq                      crlcReleaseReq;
    CrlcDlAmPduSizeInd                  crlcDlAmPduSizeInd;
    CrlcEngInfoReq                      crlcEngInfoReq;
#if defined(RRC_UPGRADE_ENG_MODE_REDESIGN)
	CrlcEngModeInfoReq					crlcEngModeInfoReq;
#endif /** RRC_UPGRADE_ENG_MODE_REDESIGN */

#if defined (ENABLE_URLC_UNIT_TEST)
    CrlcUnitTestLoopbackModeReq         crlcUnitTestLoopbackModeReq;
    CrlcUnitTestUlAmSduSegmented        crlcUnitTestUlAmSduSegmented;
    CrlcUnitTestUlAmSduExpired          crlcUnitTestUlAmSduExpired;
    CrlcUnitTestUlAmSduDeleted          crlcUnitTestUlAmSduDeleted;
    CrlcUnitTestUpdateTrafficInd        crlcUnitTestUpdateTrafficInd;
    CrlcUnitTestUlInfoRsp               crlcUnitTestUlInfoRsp;
#endif /* ENABLE_URLC_UNIT_TEST */

/*********** added 20110907 start*/

	CrlcPsMonitorControlReq             crlcPsMonitorControlReq;
    CrlcNoPsDataInd                     crlcNoPsDataInd;
/*********** added 20110907 end*/
    /* User plane signals */
    UrlcTmDataReq                       urlcTmDataReq;
    UrlcAmrTmDataReq                    urlcAmrTmDataReq;
    UrlcCsdTmDataReq                    urlcCsdTmDataReq;
    UrlcTmDataInd                       urlcTmDataInd;
    UrlcUmDataReq                       urlcUmDataReq;
#ifdef ENABLE_OPT_DATA_FLOW_OVER_SHMEM
    UrlcUmDataCnf           		 urlcUmDataCnf;
#endif /* ENABLE_OPT_DATA_FLOW_OVER_SHMEM */

#if defined(UMTS7_PRE_R8)
	UrlcPsUmDataInd                     urlcPsUmDataInd;
	UrlcPsTmDataInd						urlcPsTmDataInd;
	UrlcPsAmDataInd						urlcPsAmDataInd;
	UrlcPsAmDataCnf						urlcPsAmDataCnf;
	UrlcPsDataRsp						urlcPsDataRsp;
#ifdef ENABLE_OPT_DATA_FLOW_OVER_SHMEM
    UrlcPsUmDataCnf           		 	urlcPsUmDataCnf;
#endif /* ENABLE_OPT_DATA_FLOW_OVER_SHMEM */
#endif
    UrlcUmDataInd                       urlcUmDataInd;
    UrlcUmDataTransmittedInd            urlcUmDataTransmittedInd;
    UrlcAmDataReq                       urlcAmDataReq;
    UrlcAmDataCnf                       urlcAmDataCnf;
    UrlcAmDataInd                       urlcAmDataInd;
    UrlcAmDlSduDiscardInd               urlcAmDlSduDiscardInd;
    UrlcUlSduDiscardInd                 urlcUlSduDiscardInd;
    UrlcXoffInd                         urlcXoffInd;
    UrlcXonInd                          urlcXonInd;
    UrlcUlConfigChangeInd               urlcUlConfigChangeInd;
    UrlcDlConfigChangeInd               urlcDlConfigChangeInd;
    UrlcEngInfoInd                      urlcEngInfoInd;
	UrlcEngModeInd						urlcEngModeInd;

#if defined (ENABLE_URLC_UNIT_TEST)
	UrlcUnitTestPduList					urlcUnitTestPduList;
#endif

#if defined (ENABLE_UL_THROUGHPUT_IND)
    UrlcDataUlTpInd                     urlcDataUlTpInd;
#endif /* ENABLE_UL_THROUGHPUT_IND */

    /* User plane debug signals */
    UrlcDebugMacDataReq                 urlcDebugMacDataReq;
    UrlcDebugMacDataInd                 urlcDebugMacDataInd;
#if defined (URL_DEBUG_HSDPA_OVER_DTC)
	UrlcDebugHsDiagInd					urlcDebugHsDiagInd;
#endif 	// URL_DEBUG_HSDPA_OVER_DTC
#if defined (URL_DEBUG_HSDPA_OVER_DTC) || defined (URL_DEBUG_EDCH_OVER_DTC)
	UrlcDebugEncodedData				urlcDebugEncodedData;
#endif // URL_DEBUG_HSDPA_OVER_DTC || URL_DEBUG_EDCH_OVER_DTC

    /* MAC signals */
    UmacTrafficReq                      umacTrafficReq;
    UmacNoTrafficReq                    umacNoTrafficReq;
    UmacTrafficInd                      umacTrafficInd;
    UmacDataReq                         umacDataReq;
    UmacDataInd                         umacDataInd;
    UmacTxStatusInd                     umacTxStatusInd;
    UmacUlConfigChangeInd               umacUlConfigChangeInd;
    UmacDlConfigChangeInd               umacDlConfigChangeInd;
    UmacUpdateTrafficInd                umacUpdateTrafficInd;
    UmacDlPduListInfoInd                umacDlPduListInfoInd;
    UmacDlPduListInfoRsp                umacDlPduListInfoRsp;
#if defined (UPGRADE_3G_HSDPA)
    UmacHsDataInd                       umacHsDataInd;
    UmacHsPduListInfoInd                umacHsPduListInfoInd;
    UmacHsPduListInfoRsp                umacHsPduListInfoRsp;
    UmacEhsDataInd                      umacEhsDataInd;
	UmacEdchEngModeInd					umacEdchEngModeInd;
#endif /* UPGRADE_3G_HSDPA */

	
#if defined(RRC_UPGRADE_ENG_MODE_REDESIGN)
		CmacEngEdchModeInfoReq				cmacEngEdchModeInfoReq;
#endif /** RRC_UPGRADE_ENG_MODE_REDESIGN */

#if defined (ENABLE_URLC_UNIT_TEST)
	UmacEhsPduListInfoInd                umacEhsPduListInfoInd;
    UmacEhsPduListInfoRsp                umacEhsPduListInfoRsp;
#endif

#if defined (ENABLE_UL_THROUGHPUT_IND)
    UmacMaxPsUlTranspTpInd              umacMaxPsUlTranspTpInd;
#endif /* ENABLE_UL_THROUGHPUT_IND */

#if defined (ENABLE_FLEXIBLE_TRACE_FRAMEWORK)
    UtFlexibleTraceControlReq           utFlexibleTraceControlReq;
#endif /* ENABLE_FLEXIBLE_TRACE_FRAMEWORK */
# if defined (ENABLE_UPLANE_STATISTICS)
    UplaneStatisticsReport              uplaneStatisticsReport;
# endif /* ENABLE_UPLANE_STATISTICS */
};

#endif /* URL_SIG_H */
/* END OF Url_Sig.h */

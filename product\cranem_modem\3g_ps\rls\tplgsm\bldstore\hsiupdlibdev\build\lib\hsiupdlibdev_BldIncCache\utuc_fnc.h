/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/tplgsm/utinc/utuc_fnc.h#6 $
 *   $Revision: #6 $
 *   $DateTime: 2006/02/09 12:25:17 $
 **************************************************************************
 *  File Description :
 *
 *  This file contains the function prototypes of the UCS2/GSM conversion
 * functions.
 **************************************************************************/

#ifndef UTUC_FNC_H
#define UTUC_FNC_H


/***************************************************************************
 * Nested Include Files
 **************************************************************************/

/***************************************************************************
 * Manifest Constants
 **************************************************************************/

/***************************************************************************
 * Typed Constants
 **************************************************************************/

/***************************************************************************
 * Type Definitions
 **************************************************************************/

typedef enum AlphaIdCodingSchemeTag
{
  OCTET_ALIGNED_GSM_7BIT,
  DOUBLE_OCTET_UCS2 = 0x80,
  SINGLE_OCTET_BASE_POINTER_COMBINED,
  DOUBLE_OCTET_BASE_POINTER_COMBINED
} AlphaIdCodingScheme;

/*-----------------27/06/2002 11:23-----------------
 * New Typded for Extended Character Support
 * --------------------------------------------------*/
typedef enum GsmCharTypeTag
{
  NON_GSM7BIT      	= 0,
  GSM7BIT	    	= 1,
  EXTENDED_GSM7BIT	= 2
}GsmCharType;

/***************************************************************************
 *  Macros
 **************************************************************************/
#define GSM_CHARACTER_SET_SIZE          0x80

/***************************************************************************
 *  Function Prototypes
 **************************************************************************/

extern Char *
utUcs2StringToDebug ( const Int16 * strUcs2_p );

extern Int16
utLengthAlphaIdToUcs2( const Int8 *alphaId,
                       Int16 alphaIdLen );

extern Int16
utUcs2ToAlphaId ( Int8        *alphaId_p,
                  Int16       maxAlphaIdLen,
                  const Int16 *strSrc_p,
                  Int16       srcLen );

extern Int16
utAlphaIdToUcs2 ( Int16      *strDst_p,
                  Int16      maxDstLen,
                  const Int8 *alphaId,
                  Int16      alphaIdLen );

/* extern Boolean
utIsUcs2StringAllGsm ( const Int16 *ucs2String_p,
                       Int16       length);   */

extern GsmCharType
utIsUcs2StringAllEGsm(const Int16 *ucs2Str_p, Int16 ucs2Length);



/* 0204-16329 Modified For Extended Character Support
 * - offset used to calculate number of extra bytes
 *   for each Extended Character */
/* extern Int16
utUcs2ToByteAlignedGsm7Bit ( Int8        *gsmData_p,
                             const Int16 *ucs2String_p,
                             Int16       length,
                             Int16       *offset_p);   */

extern GsmCharType
utUcs2ToByteAlignedEGsm7Bit(const Int16 *ucs2Str_p,
                            Int16 ucs2Length,
                            Int8 *gsmStr_p,
                            Int16 *gsmLength_p,
                            Int16 maxGsmLength,
                            Boolean *gsmBufferTooSmall_p);


extern Int16
utUcs2ToChar ( Char * charStr,
               const Int16 *ucs2String,
               Int16 length);

/* extern Int16
utByteAlignedGsm7BitToUcs2 ( Int16      *ucs2String,
                             const Int8 *gsmData,
                             Int16      length); */

extern GsmCharType
utByteAlignedEGsm7BitToUcs2(const Int8 *gsmStr_p,
                            Int16 gsmDataLength,
                            Int16 *ucs2Str_p,
                            Int16 *ucs2Length_p,
                            Int16 maxUcs2Len,
                            Boolean *ucs2BufferTooSmall_p);

/*0210-20704 */
extern GsmCharType
utSmsByteAlignedEGsm7BitToUcs2(const Int8 *gsmStr_p,
                               Int16 gsmDataLength,
                               Int16 *ucs2Str_p,
                               Int16 *ucs2Length_p,
                               Int16 maxUcs2Len,
                               Boolean *ucs2BufferTooSmall_p,
                               Int16 *numConverted_p);

extern Int16
utByteAlignedGsm7BitToChar ( Char *charString,
                             const Int8 * gsmData,
                             Int16 length);

extern void
utDoubleOctetToUcs2 ( Int16      *ucs2String,
                      const Int8 *srcData_p,
                      Int16      length);

extern void
utUcs2ToDoubleOctet ( Int8        *dst_p,
                      const Int16 *ucs2String,
                      Int16       length);



/* 0204-16329 mapGsmToUcs2 modified for Extended Character Support
 * - gsmString_p pointer to gsmData Buffer
 * - index_p pointer to referrence of array
 * - Length length of gsmString
 */
/*extern Int16
mapGsmToUcs2(const Int8 *gsmString_p, Int16 *index_p, Int16 length); */

extern GsmCharType
mapEGsmToUcs2(Int8 gsm1, Int8 gsm2, Int16 *ucs2_p);


/* 0204-16329 mapUcsToGsm modified for Extended Character Support
 * - gsmData_p pointer to gsm data buffer.
 * - index_p pointer to refference of array
 * - ucs2Char current Ucs2 Character
 * - gsmDataLength length of Gsm Buffer
 * - offSet_p used to calculate extra bytes for each extended character,
 */

/*-----------------17/06/2002 12:27-----------------
 * 0206-18260 modified Length to a pointer to gsmDataLength,
 * --------------------------------------------------*/
/* 0206-18260 Changed gsmDataLength to pointer */
/* extern Int8
mapUcs2ToGsm (Int8 *gsmData_p, Int16 *index_p, Int16 ucs2Char, Int16 *gsmDataLength, Int16 *offset_p ); */

extern GsmCharType
mapUcs2ToEGsm(Int16 ucs2, Int8 *gsm1_p, Int8 *gsm2_p);

extern Char
mapUcs2ToChar ( Int16 ucs2Char );

/* 0204-16329 utUcs2ToGsmLength - new function to get length of Gsm String from Ucs2String,
 *  maybe different due to new Extended Character Support
 *  - gsmData_p pointer to gsm string buffer.
 *  - ucs2String_p pointer to the Ucs2 String buffer
 *  - length size of Ucs2 String.
 *  - returns length of Gsm String including Extended Character Bytes.0
 *
 */

/* extern Int16 utUcs2ToGsmLength(Int8 *gsmData_p, const Int16 *ucs2String_p, Int16 length); */
extern GsmCharType
utGetEGsmLengthOfUcs2(const Int16 *ucs2Str_p, Int16 ucs2Length, Int16 *gsmLength_p);


extern SignedInt16 utCompareUcs2 ( Int16 *firstUcs2Str, Int16 firstLen, Int16 *secondUcs2Str, Int16 secondLen, Boolean doExactMatch);

#endif /* UTUC_FNC_H */

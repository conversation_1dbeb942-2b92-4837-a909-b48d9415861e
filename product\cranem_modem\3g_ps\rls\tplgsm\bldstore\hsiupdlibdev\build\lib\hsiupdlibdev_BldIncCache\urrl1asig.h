/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/
#if !defined (URRL1A_SIG_H)
#define       URRL1A_SIG_H

#define LTE_GSM_MULTI_BCCH_DECODE_MAX_NUM_OF_CELLS	40

#define ECPHY_GSM_L1_PHYSICAL_BLOCK_SIZE       23

#define TC_SYS_INFO_TYPE_2       0x0002   /* TC = 2 */
#define TC_SYS_INFO_TYPE_3       0x0044   /* TC = 2 or TC = 6 */
#define TC_SYS_INFO_TYPE_4       0x0088   /* TC = 3 or TC = 7 */


typedef Int8        EcphyGsmPhysicalBlock [ECPHY_GSM_L1_PHYSICAL_BLOCK_SIZE];


//ICAT EXPORTED STRUCT
typedef struct ErrcGeranMultiArfcnListTag
{
	Int16					arfcn;
	Int16 					nCellBcchControl; // 0  means stop the bsic/bcch decoding
	Boolean					bandIndicator;	 
}ErrcGeranMultiArfcnList;

//ICAT EXPORTED STRUCT
typedef struct ErrGeranArfcnInfoTag
{
	Int16   	arfcn;
	Boolean		bandIndicator;
}ErrGeranArfcnInfo;

/******************************************************************************
 * EcphyGsmMultiBcchDecodeReq
 * Direction: ERRC -> L1A
 * Description:  required Gsm MultiBcchDecodeReq with list of ARFCNs
 *
 ******************************************************************************/
 //ICAT EXPORTED STRUCT
typedef struct EcphyGsmMultiBcchDecodeReqTag
{
	Int8						numOfArfcns;
	Boolean 					reportList; // 1 means that L1/PHY need to report the list of remaining ARFCNs. used when suspending the procedure.
	ErrcGeranMultiArfcnList 	list[LTE_GSM_MULTI_BCCH_DECODE_MAX_NUM_OF_CELLS];			
}EcphyGsmMultiBcchDecodeReq;

/******************************************************************************
 * EcphyGsmMultiBcchDecodeInd
 * Direction: l1A --> ERRC
 * Description:  signas the end of multiBcch process (by numOfArfcns = 0) or send the list of ARFCN which 
 * 				still in the process (this is done in case that reportList=1 in EcphyGsmMultiBcchDecodeReq)
 *
 ******************************************************************************/
 //ICAT EXPORTED STRUCT
typedef struct EcphyGsmMultiBcchDecodeIndTag
{
	Int8						numOfArfcns;
	ErrGeranArfcnInfo 			list[LTE_GSM_MULTI_BCCH_DECODE_MAX_NUM_OF_CELLS];			
}EcphyGsmMultiBcchDecodeInd;

//ICAT EXPORTED STRUCT 
typedef struct EcphyGsmBcchDecodeIndTag
{
    Int16                   arfcn;
    EUFrequency_Band        gsmBandIndicator;
    Int8                    frame[ECPHY_GSM_L1_PHYSICAL_BLOCK_SIZE];
    Int16                   subChannel;
    Boolean                 crcCheckResult;
	/*GPLC shall ignore gapEnd. it is used in L1A->ERRC*/
	Boolean                 gapEnd;

    /*GPLC shall ignore sim2Deactivated. it is used in L1A->ERRC*/
	Boolean                 sim2Deactivated; 
#if defined (ENABLE_NETWORK_SCAN_REPORT) //CQ00130503	
	Int8                    bsic;
#endif

}
EcphyGsmBcchDecodeInd;

#endif /* URRL1A_SIG_H */

/* END OF FILE */

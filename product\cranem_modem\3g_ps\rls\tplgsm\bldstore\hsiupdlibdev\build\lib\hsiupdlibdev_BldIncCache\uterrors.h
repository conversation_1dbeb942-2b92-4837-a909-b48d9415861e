/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/* Null error to allow compilation without UPGRADE_GPRS */

    ERROR_DEF (UTERR_NULL,                                  sizeof (Int8), Int8 tmp)

#if defined (UPGRADE_GPRS)

    /* SNDCP errors */
    ERROR_DEF (UTERR_SNDCP_UNKNOWN_XID_PARAM,               sizeof (Int8),             Int8             e000)
    ERROR_DEF (UTERR_SNDCP_UNKNOWN_HDRCOMP_ALG,             sizeof (Int8),             Int8             e001)
    ERROR_DEF (UTERR_SNDCP_INCORRECT_HDRCOMP_LEN,           sizeof (Int8),             Int8             e002)
    ERROR_DEF (UTERR_SNDCP_UNKNOWN_DATACOMP_ALG,            sizeof (Int8),             Int8             e003)
    ERROR_DEF (UTERR_SNDCP_INCORRECT_DATACOMP_LEN,          sizeof (Int8),             Int8             e004)
    ERROR_DEF (UTERR_SNDCP_INCORRECT_SNDCP_VERNUM_LEN,      sizeof (Int8),             Int8             e005)
    ERROR_DEF (UTERR_SNDCP_INCORRECT_SNDCP_VERNUM,          sizeof (Int8),             Int8             e006)
    ERROR_DEF (UTERR_SNDCP_OPTION_SET_FOR_UNALLOCED_NSAPI,  sizeof (Int16),            Int16            e007)
    ERROR_DEF (UTERR_SNDCP_DUPLICATE_HDRCOMP_ENTITY,        sizeof (Int8),             Int8             e008)
    ERROR_DEF (UTERR_SNDCP_PROHIBITED_CHANGES,              sizeof (Int8),             Int8             e009)
    ERROR_DEF (UTERR_SNDCP_XID_BLK_NEW_CE_NO_NSAPIS,        sizeof (Int8),             Int8             e010)
    ERROR_DEF (UTERR_SNDCP_XID_BLK_NEW_CE_IN_RSP,           sizeof (Int8),             Int8             e011)
    ERROR_DEF (UTERR_SNDCP_XID_BLK_RFC1144_NUMSLOTS_TOOBIG, sizeof (Int8),             Int8             e012)
    ERROR_DEF (UTERR_SNDCP_XID_BLK_RFC1144_INVALID_CHANGES, sizeof (Int8),             Int8             e013)
    ERROR_DEF (UTERR_SNDCP_NONEXISTENT_NSAPI_IN_DATAIND,    sizeof (Nsapi),            Nsapi            e014)
    ERROR_DEF (UTERR_SNDCP_NONEXISTENT_NSAPI_IN_SEQIND,     sizeof (Nsapi),            Nsapi            e015)
    ERROR_DEF (UTERR_SNDCP_INCORRECT_NSAPI_IN_DATAIND,      sizeof (Nsapi),            Nsapi            e016)
    ERROR_DEF (UTERR_SNDCP_INVALID_PCOMP,                   sizeof (Int8),             Int8             e017)
    ERROR_DEF (UTERR_SNDCP_INVALID_DCOMP,                   sizeof (Int8),             Int8             e018)
    ERROR_DEF (UTERR_SNDCP_UNEXPECTED_FIRST_SEGMENT,            \
               sizeof (PduD),                                      \
               PduD             e019)
    ERROR_DEF (UTERR_SNDCP_WRONG_LLC_ACK_MODE_IN_DATAIND,   sizeof (PduD),             PduD             e020)
    ERROR_DEF (UTERR_SNDCP_WRONG_SEGNUM,                    sizeof (PduD),             PduD             e021)
    ERROR_DEF (UTERR_SNDCP_MISSING_SEGMENTS,                sizeof (Int8),             Int8             e022)
    ERROR_DEF (UTERR_SNDCP_CANT_SEND_NPDU_IN_ADM,           sizeof (Nsapi),            Nsapi            e023)
    ERROR_DEF (UTERR_SNDCP_MESSAGE_ERROR_QOS,               sizeof (QualityOfService), QualityOfService e024)
    ERROR_DEF (UTERR_SNDCP_NPDU_OVERFLOW,                   sizeof (Int16),            Int8             e025)
    ERROR_DEF (UTERR_SNDCP_INCORRECT_RX_NPDU_NUM,           sizeof (Int16),            Int16            e026)
    ERROR_DEF (UTERR_SNDCP_NOT_FIRST_SEGMENT,               sizeof (PduD),             PduD             e027)
    ERROR_DEF (UTERR_SNDCP_DUPLICATE_DATACOMP_ENTITY,       sizeof (Int8),             Int8             e028)
    ERROR_DEF (UTERR_SNDCP_XID_BLK_V42BIS_P0_INCOMPAT,      sizeof (Int8),             Int8             e029)
    ERROR_DEF (UTERR_SNDCP_XID_BLK_V42BIS_P1_INCOMPAT,      sizeof (Int16),            Int16            e030)
    ERROR_DEF (UTERR_SNDCP_XID_BLK_V42BIS_P2_INCOMPAT,      sizeof (Int8),             Int8             e031)
    ERROR_DEF (UTERR_SNDCP_XID_BLK_V42BIS_INVALID_CHANGES,  sizeof (Int8),             Int8             e032)
    ERROR_DEF (UTERR_SNDCP_V42BIS_DECOMP_ERROR,             sizeof (PduD),             PduD             e033)
    ERROR_DEF (UTERR_SNDCP_UNEXPECTED_ACTIND,               sizeof (SnSmActivateInd),  SnSmActivateInd  e034)
    ERROR_DEF (UTERR_SNDCP_XID_NSAPIS_INVALID,              sizeof (Int16),            Int16            e035)
    ERROR_DEF (UTERR_SNDCP_NONEXISTENT_NSAPI_IN_DATACNF,    sizeof (Nsapi),            Nsapi            e036)
    ERROR_DEF (UTERR_SNDCP_NONEXISTENT_NSAPI_IN_DATAREQ,    sizeof (Nsapi),            Nsapi            e037)
    ERROR_DEF (UTERR_SNDCP_WRONG_TYPE_OF_DATAREQ,           sizeof (Nsapi),            Nsapi            e038)
    ERROR_DEF (UTERR_SNDCP_NO_MEM_FOR_UL_NPDU_DISCARDED,    sizeof (Nsapi),            Nsapi            e039)
    ERROR_DEF (UTERR_SNDCP_MAX_SNPDUS_IN_NPDU_EXCEEDED,     sizeof (Nsapi),            Nsapi            e040)
    ERROR_DEF (UTERR_SNDCP_XID_BLK_V42BIS_P0_OUT_OF_RANGE,  sizeof (Int8),             Int8             e041)
    ERROR_DEF (UTERR_SNDCP_UNEXPECTED_DATAIND,              sizeof (Nsapi),            Nsapi            e042)
    ERROR_DEF (UTERR_SNDCP_INVALID_SNPDU_LEN,               sizeof (Int16),            Int16            e043)
    ERROR_DEF (UTERR_SNDCP_XOFF_NUM_UNACKED_NPDUS,          sizeof (Int16),            Int16            e044)
    ERROR_DEF (UTERR_SNDCP_XOFF_NUM_UNACKED_SNPDUS,         sizeof (Int16),            Int16            e045)
    ERROR_DEF (UTERR_SNDCP_UNEXPECTED_SIGNAL,               sizeof (Nsapi),            Nsapi            e046)

    /* SM errors */
    //ERROR_DEF (UTERR_SM_STATUS_MSG_RECEIVED,                sizeof (GprsSmStatusMessage), GprsSmStatusMessage statusMsg)
    ERROR_DEF (UTERR_SM_DIFFERENT_PDP_TYPE_RECEIVED,        sizeof (PdpTypeNumber), PdpTypeNumber pdpTypeNumber)
    ERROR_DEF (UTERR_SM_WRONG_TI_FLAG,                      sizeof (MessageHeader), MessageHeader messageHeader)
    ERROR_DEF (UTERR_SM_NONEXISTENT_NSAPI,                  sizeof (Nsapi), Nsapi nsapi)
#if defined (UPGRADE_R99)
    ERROR_DEF (UTERR_SM_NO_MODIFY_RESPONSE,                 sizeof (Nsapi), Nsapi nsapiModified)
#endif
    ERROR_DEF (UTERR_SM_MODIFY_NOT_SUCCESSFUL,              sizeof (Nsapi), Nsapi nsapi2)


#else /* UPGRADE_GPRS */







































                            

#endif /* UPGRADE_GPRS */
/* END OF FILE */

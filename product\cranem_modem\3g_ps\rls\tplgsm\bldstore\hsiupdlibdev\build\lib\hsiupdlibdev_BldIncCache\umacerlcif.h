/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

#ifndef MACERLCIF_H
#define MACERLCIF_H
#if defined (UPGRADE_3G_EDCH)

/******************************************************************************
 * Include Files
 *****************************************************************************/
#include "system.h"
#include "umac_sig.h"

#if !defined(ON_PC)
#include <umaedchengmode.h>
#endif /* !ON_PC */

/******************************************************************************
 * Macros
 *****************************************************************************/
/** Engineering mode opcode*/
#define UMACE_ENG_MODE_HSUPA_STATISTICS				0x10000000 //TODO: replace with ENGM_GS83  (check if works)

/******************************************************************************
 * Type Definitions
 *****************************************************************************/
typedef Int8 UmacEdchTxReqId_t;

//ICAT EXPORTED STRUCT
typedef struct UmacCfnSubframeTag
{
    Int8 cfn;       /* 0 - 255 */
    Int8 subframe;  /* 0 - 4 */
}UmacCfnSubframe;

typedef struct UmacEdchTxStatusIndTag
{
    UmacEdchTxReqId_t   edchTxReqId;
    UmacTxStatus        txStatus;
}UmacEdchTxStatusInd;

typedef struct UmacEdchTrafficReqTag
{
    Boolean             isEdchTrafficReq;   /*TRUE = UmacEdchTrafficReq, FALSE = UmacEdchNoTrafficReq*/
    UmacCfnSubframe     ulCfnSubframe;      /* For logging only */
    Int8                numberOfBearers;    /* 0 - 32 */
    UmacTrafficReqInfo  trafficInfo [maxRB];
}UmacEdchTrafficReq;


typedef struct UmacEdchTrafficIndTag
{
    Boolean                 isEdchTrafficInd; /*TRUE = UmacEdchTrafficInd, FALSE = UmacEdchNoTrafficInd*/
    UmacEdchTxReqId_t       edchTxReqId;
    Int32                   numberOfPdus;
	cipherType_te			cipherAlgorithm;
    dtcF8DescriptorInfo     *cipherInfo_p;
}UmacEdchTrafficInd;

typedef struct UmacEdchBearerSortedByLogChanPriorityTag
{
    Int8                        numberOfBearers;    /* 0 - 32 */
    UmacLogicalBearerIdentity   bearerIdentity [maxRB];
} UmacEdchBearerSortedByLogChanPriority;

/******************************************************************************
 * Function Prototypes
 *****************************************************************************/
void UmacEdchTxStatusIndFn(
        UmacEdchTxStatusInd                     *umacEdchTxStatusInd_p,
        UmacEdchBearerSortedByLogChanPriority   *sort_p);

void UmacEdchUpdateTrafficIndFn(
        UmacEdchBearerSortedByLogChanPriority   *umacEdchBearerSortedByLogChanPriority_p,
        UmacEdchTrafficReq                      *umacEdchTrafficReq_p);

void UmacEdchTrafficIndFn(
        UmacEdchBearerSortedByLogChanPriority   *umacEdchBearerSortedByLogChanPriority_p,
        UmacEdchTrafficInd                      *UmacEdchTrafficInd_p);

#if defined (UPGRADE_UL_ECF)
void UmacEdchReqUlDataUpdateStartIndFn(void);
void UmacEdchReqUlDataUpdateStopIndFn(void);
#endif

void UmacEdchTtiTimingIndFn(void);

Int32 UmacEdchGetHigherLayersPendingBuffersBits(void);

void macerlcInitializeGDB(char* title);

#endif /* UPGRADE_3G_EDCH */

#if !defined(ON_PC)
void UmacEdchEngModeReportIndFn(Int32 msFromLastReport);
extern Boolean UmaEdchIsMaceActive(void);
#endif /* !ON_PC */

#if defined(RRC_UPGRADE_ENG_MODE_REDESIGN)
void UmacEdchHandleEngModeInfoReq(SignalBuffer * rxSignal_p);
#endif /* RRC_UPGRADE_ENG_MODE_REDESIGN */

#endif //MACERLCIF_H


/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/********************************************************************************************************************************
*              MODULE IMPLEMENTATION FILE
*********************************************************************************************************************************
*  COPYRIGHT (C) 2001 Intel Corporation.
*
*  This file and the software in it is furnished under
*  license and may only be used or copied in accordance with the terms of the
*  license. The information in this file is furnished for informational use
*  only, is subject to change without notice, and should not be construed as
*  a commitment by Intel Corporation. Intel Corporation assumes no
*  responsibility or liability for any errors or inaccuracies that may appear
*  in this document or any software that may be provided in association with
*  this document.
*  Except as permitted by such license, no part of this document may be
*  reproduced, stored in a retrieval system, or transmitted in any form or by
*  any means without the express written consent of Intel Corporation.
*
*  Title:   UART Package Common Control
*
*  Filename:    UART_types.c
*
*  Target, subsystem: Common Platform, HAL
*
*  Authors: <AUTHORS>
*
*  Description:  The sub-package Common Services provides synchrony
*  and blocking services for usage of the client package to control
*  the package.
*
*  Last Modified: 18/07/06
*
*  Notes: 18/07/06 - elevy - porting to Tavor A0.
*		  21/01/07 - elevy - unite hal hop drivers
********************************************************************************************************************************/

#if !(defined _UART_TYPES_)
#define _UART_TYPES_

#include "syscfg.h"
#include "intc.h"
#include "UART_config.h"

#if (defined UARTDEBUG)
#include "diag_API.h"
#endif

#define UART_COMMON_CONTROL_FILE
#include "UART_CommonControl.h"
#undef UART_COMMON_CONTROL_FILE


/*----------- Global defines -------------------------------------------------*/

#ifdef AT_OVER_UART
#define INTC_SRC_FFUART INTC_SRC_UART2 
#define INTC_SRC_STUART INTC_SRC_UART1_SLOW
#define INTC_SRC_BTUART (INTC_MAX_PRIMARY_INTERRUPT_SOURCES+95) 
#else
/* Use AT UART1 for UART diag debug */
#define INTC_SRC_FFUART INTC_SRC_UART1_SLOW /*INTC_HW_COMM_UART*/
#define INTC_SRC_BTUART INTC_SRC_UART1_SLOW /*INTC_HW_COMM_UART*/
#define INTC_SRC_STUART INTC_SRC_UART1_SLOW /*INTC_HW_COMM_UART*/
#endif

#if defined(UART_D2_SPECIFIC)
typedef struct
{
	UINT32      STDLLBackup;
	UINT32      STDLHBackup;
	UINT32	  	STIERBackup;
	UINT32      STFCRBackup;
	UINT32      STLCRBackup;
	UINT32      STMCRBackup;
	UINT32      STSCRBackup;
	UINT32      STISRBackup;
} UARTRegistersBackupDB;
#endif

#if defined(PERIPHERAL_CLOCKS_VIA_PRM_API)
 typedef enum
{
	UART_CLK_ON,
	UART_CLK_OFF
}UARTClockState;
 #endif

// VERY FORTUNATELY, all chips have same interrupt assignment for the UARTs - sources 20,21,22
#define FIRST_PLACE_OF_UART_INTERRUPT   INTC_SRC_STUART
#define LAST_PLACE_OF_UART_INTERRUPT    INTC_SRC_FFUART
#define UARTGetInterruptPortNumber(intcSrc) ((UART_Port)_UARTIsrSourceLookUpTable[intcSrc-FIRST_PLACE_OF_UART_INTERRUPT])
#define UARTGetInterruptSRC(port) ((INTC_InterruptSources)_UARTIsrSources[port])

#ifdef _QT_
#define UART_SLEEP_TIMER 2 /* As QT is about 1200 times slower than real Hermon, 2 tick (10mSec) is about 12 seconds*/
#else
#define UART_SLEEP_TIMER 1000 /* UART should be inactive for 5s before it's turned off */
#endif

#define UARTTimerKICK(portNumber) uartActive[portNumber]=TRUE
#define UARTTimerCLEAR(portNumber) uartActive[portNumber]=FALSE

/* Special case - read in OFF timer */
#define	HW_UART_READ_MSR_NOWAKE(uart,val)  \
{									 \
	((val) = *((volatile UINT32 *)((uart) + UART_MSR_ADD))); \
}


/*----------- Global type definitions ----------------------------------------*/

typedef struct
{
	INTC_InterruptSources wakeupRx;
	INTC_InterruptSources wakeupRts;
	INTC_InterruptSources wakeupDtr;
} UartWakeupSources;


const UART_Port _UARTIsrSourceLookUpTable[] = {UART_PORT_STUART,UART_PORT_BTUART,UART_PORT_FFUART};


typedef struct
{
	INTC_InterruptSources intsrc;
#if defined (PERIPHERAL_CLOCKS_VIA_PMU_API)
	BothPeripherals       pmuIdBoth;
#endif
}UartConstInfo;


const UartConstInfo uartInfo[] =
{
	{
		INTC_SRC_FFUART
#if defined (PERIPHERAL_CLOCKS_VIA_PMU_API)
		, BOTH_UART1
#endif
	},
	{
		INTC_SRC_BTUART
#if defined (PERIPHERAL_CLOCKS_VIA_PMU_API)
		, BOTH_UART2
#endif
	},
	{
		INTC_SRC_STUART
#if defined (PERIPHERAL_CLOCKS_VIA_PMU_API)
		, BOTH_UART3
#endif
	}
};
#endif /* _UART_TYPES_*/

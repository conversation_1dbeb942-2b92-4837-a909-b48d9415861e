/*------------------------------------------------------------
(C) Copyright [2006-2010] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/******************************************************************************
 * File Name:   urlstatistics.h
 *
 *
 * File Description: URLC statistics header file
 *
 *****************************************************************************/

/******************************************************************************
 * Type Definitions
 *****************************************************************************/
#define URL_DEBUG_MAX_BEARERS_THROUGHPUT_NUM    3

//ICAT EXPORTED STRUCT
typedef struct UrlcRbThroughputTag
{
    BearerIdentity      bearerId;
    UrlBearerMode       bearerMode;
    Int32               throughput; /* Throughput in bits per second */
} UrlcRbThroughput;
 
 //ICAT EXPORTED STRUCT
 typedef struct UmacEhsMemoryManagerDebugRecordTag
 {
	Int32		maxUnavailableMemory;			/*<Maximum of Total bytes not available to new hsdpa data -to alert when approaching WM */	
	Int32		maxInUseSegments;				/*<Maximum of Total segments used 	*/	
 }UmacEhsMemoryManagerDebugRecord;

 
//ICAT EXPORTED STRUCT
typedef struct UrlcDebugStatisticsTag
{
    /* DL statistics */
    /*****************/
    struct
    {
        /* PDUs/SDUs statistics */
        Int32           pdusNum;                /* Number of received PDUs (Data+Control) since power on */
        Int32           pdusNumInLastInterval;  /* Number of received PDUs in last debug time interval */
        Int32           controlPdusNum;         /* Number of received control PDUs */
        Int32           discardedPdusNum;       /* Number of discarded PDUs */
        Int32           duplicatedPdusNum;      /* Number of received duplicated PDUs */
        Int32           outOfWindowPdusNum;     /* Number of received out-of-window PDUs */
        Int32           outOfSequencePdusNum;   /* Number of received out-of-sequence PDUs */
        Int32           fragmentedPdusNum;      /* Number of received fregmented PDUs */
        Int32           resetPdusNum;           /* Number of received RESET PDUs */
        Int32           nackStatusTrigNum;      /* Number of triggered NACK STATUS */
        Int32           missingPdusReportedNum; /* Number of missing PDUs as reported in NACK STATUS */
        Int32           missingGapsReportedNum; /* Number of missing PDUs gaps as reported in NACK STATUS */
        Int32           failSduAllocNum;        /* Number of discarded PDUs due to SDU allocation failure */
        Int32           pendUnReasmblSdusNum;   /* Number of received SDUs that are waiting in RLC queue
                                                 * due to in sequence delivery */
        Int32           pendReasmblSdusNum;     /* Number of received SDUs that are waiting in RLC queue
                                                 * due to flow control */
        Int32           deliveredSdusNum;       /* Number of SDUs delivered to higher layer */
        Int32           averagePdcpSduBytes;    /* Average size of SDUs that delivered to PDCP in bytes */

        /* Session statistics */
        Int32           aboveHwmNum;            /* Number of received above high water mark indications */
        Int32           aboveVhwmNum;           /* Number of received above very high water mark indications */
        Int32           belowLwmNum;            /* Number of received above below water mark indications */
        Int32           leftRxWindowSize;       /* Smallest left length of RX window of data radio bearer */
        Int32           flowControlState;       /* Flow control state with higher layer indicates whether
                                                 * higer layer is ready to receive DL data or not */
        Int32           throughput;             /* Downlink throughput in bits per second */
        UrlcRbThroughput rbThroughput[URL_DEBUG_MAX_BEARERS_THROUGHPUT_NUM]; /* RBs throughput statistics */

        /* MAC-HS PDUs statistics */
        struct
        {
            Int32           pdusNum;                        /* Number of received MAC-hs PDUs since power on */
            Int32           pdusNumInLastInterval;          /* Number of received MAC-hs PDUs in last debug time interval */
            Int32           avgNumOfRlcPdusInLastInterval;  /* Average numebr of RLC PDUs per Rx TTI, in the last debug interval */
            Int32           addedRxPdusNum;                 /* Number of added MAC-hs PDUs */
            Int32           outOfOrderPdusNum;              /* Number of received out-of-order MAC-HS PDUs */
            Int32           discardedInvalidPdusNum;        /* Number of discarded invalid MAC-hs PDUs */
            Int32           discardedDuplicatedPdusNum;     /* Number of discarded duplicated MAC-hs PDUs */
            Int32           t1TimerExpiredNum;              /* Number of MAC-hs T1 timer expired */
            Int32           rlcPduListNum;                  /* Number of MAC-hs PDUs sent to RLC */
            Int32           paddingPercent;                 /* padding percent in MAC-hs PDUs */
        } macHs;

		/* Statistics structure */
		struct
		{	
		    Int32     macEhsPdus;						/* Number of received MAC-ehs PDUs */
			Int32     pdusNumInLastInterval;            /* Number of received MAC-ehs PDUs in last debug time interval */
			Int32     avgNumOfRlcPdusInLastInterval;    /* Average numebr of RLC PDUs per Rx TTI, in the last debug interval */
			Int32     macEhsReorderingPdus;				/* Number of received MAC-ehs reordering PDUs */
			Int32	  addedReorderingPdus;				/* Number of added MAC-ehs reordering PDUs */
			Int32     reorderingPdusOutOfOrder;			/* Number of received out-of-order MAC-ehs reordering PDUs */
		    Int32     discardedMacEhsPdus;			    /* Number of discarded MAC-ehs PDUs */
			Int32     discardedReorderingPdus;			/* Number of discarded MAC-ehs reordering PDUs by reordering entity */
			Int32     discardedReassembledPdus;			/* Number of discarded MAC-ehs reordering PDUs by reassmble entity */
			Int32	  discardedSegments;				/* Number of discarded mac-ehs segments */
			Int32     t1TimerExpired;					/* Number of MAC-ehs T1 timer expired */
		    Int32     tresetTimerExpired;				/* Number of MAC-ehs Treset timer expired */
			Int32     deliveredReorderingPdus; 			/* Number of MAC-ehs reordering PDUs, delivered to RLC */
		    Int32     rlcPduListNum; 					/* Number of UMAC_EHS_PDU_LIST_INFO_IND signals sent to RLC */
		} macEhs;
		
		/* MAC-EHS MEMORY MANAGER */
		UmacEhsMemoryManagerDebugRecord umaEhsMemm[3];

    } dl;

    /* UL statistics */
    /*****************/
    struct
    {
        Int32           reqSduBytesFromHl;      /* Number of bytes requested from higer layer to transmit */
        Int32           rcvSdusBytesFromHl;     /* Number of bytes received from higer layer to transmit */
        Int32           reqSdusFromHlTimes;     /* Number of times SDUs requested from higer layer to transmit */
        SignedInt32     remainReqBitsFromHl;    /* Number of remined SDUs requested from higher layer */
        Int32           pendUnsegAmSdusBytes;   /* Number of bytes of pending unsegmented SDUs */
        Int32           pendUnsegAmSdusMismatch;/* Number of pending unsegmented SDUs mismatch */
        Int32           dataPdusNum;            /* Number of sent data PDUs */
        Int32           rtxDataPdusNum;         /* Number of retransmitted data PDUs */
        Int32           controlPdusNum;         /* Number of sent control PDUs */
        Int32           statusPaddingPdusNum;   /* Number of sent padding PDUs */
        Int32           resetPdusNum;           /* Number of sent RESET PDUs */
        Int32           dataPdusNackedNum;      /* Number of PDUs nacked by peer */
        Int32           discardedSdusNum;       /* Number of discarded UL SDUs */
        Int32           noTxDataAvailableNum;   /* Number of times of no data in RLC for transmission */
        Int32           fullWindowNum;          /* Number of detected UL full window */
        Int32           bgDeleteInCurrentTti;   /* Number of UL delete background operations done in the last TTI */
#if defined (ENABLE_NEW_URLC_OPTIMISATIONS)		
		Int32			remainNackedUlPdusToHandleInCurTti; /* Number of remaining DL ACK/NACK operations to do last TTI */
#endif /* ENABLE_NEW_URLC_OPTIMISATIONS */		
        Int32           ulDeletePdusAndSdusBitmap; /* Bitmap which keeps the status of ulDeletePdusAndSdus flag for each RB. 
                                                       * Bitmap offset is RB-1, e.g. RB_32 is kept in bit #31 */
        Int32           highestBearerRtxListSize;
        Int32           throughput;                  /* Uplink throughput in bits per second */
        UrlcRbThroughput rbThroughput[URL_DEBUG_MAX_BEARERS_THROUGHPUT_NUM]; /* RBs throughput statistics */
    } ul;
} UrlcDebugStatistics;

/******************************************************************************
 * Global Variables
 *****************************************************************************/
extern UrlcDebugStatistics *urlcDebugStatistics_p;


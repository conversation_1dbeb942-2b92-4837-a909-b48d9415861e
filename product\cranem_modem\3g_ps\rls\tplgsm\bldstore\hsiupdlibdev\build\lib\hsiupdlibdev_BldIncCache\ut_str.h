/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/tplgsm/utinc/ut_str.h#3 $
 *   $Revision: #3 $
 *   $DateTime: 2006/04/13 13:16:41 $
 ***************************************************************************
 * File Description: Type definitions for String utility functions.
 **************************************************************************/

#include <system.h>
#include <kernel.h>

#ifndef UT_STR_H
#define UT_STR_H

/**************************************************************************
 * Nested Include Files
 **************************************************************************/

/**************************************************************************
 * Manifest Constants
 **************************************************************************/

/**************************************************************************
 * Type Definitions
 **************************************************************************/

/**************************************************************************
 * Macros
 **************************************************************************/

/**************************************************************************
 * Function Prototypes
 **************************************************************************/

Int32 utStricmp  (const Char* str1_p, const Char* str2_p);

Int32 utStrnicmp (const Char *str1_p, const Char *str2_p, Int32 length);

#endif /* !defined (UT_STR_H) */

/* END OF FILE */



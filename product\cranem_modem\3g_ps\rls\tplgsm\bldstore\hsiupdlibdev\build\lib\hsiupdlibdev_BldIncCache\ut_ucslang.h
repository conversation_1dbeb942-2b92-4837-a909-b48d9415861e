/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/tplgsm/utinc/ut_ucslang.h#11 $
 *   $Revision: #11 $
 *   $DateTime: 2006/12/12 19:03:51 $
 **************************************************************************
 * File Description
 * ----------------
 *   Include file for Unicode language definitions
 **************************************************************************/

#ifndef UT_UCSLANG_H
#define UT_UCSLANG_H


/***************************************************************************
 * Nested Include Files
 **************************************************************************/

/***************************************************************************
 * Manifest Constants
 **************************************************************************/

/***************************************************************************
 * Typed Constants
 **************************************************************************/

/***************************************************************************
 * Type Definitions
 **************************************************************************/

/* Defines languages we support on the handset. The values here are
 * used in ut_ucsshape.c to provide fast look-up into a table, so if
 * these values are changed make sure ut_ucsshape.c is still
 * consistent.
 *
 * Note that languages needing glyph shaping should be specified at
 * the start of the table (because of their use in ut_ucsshape.c).
 * Languages whose support requires extra data are protected by
 * #ifdef's to minimise memory usage in case the language is not
 * needed.
 */
typedef enum
{
  E_LANG_NONE = 0,
  E_LANG_HANDSET_CURRENT, /*this is used to indicate the the phones current language is specified*/
  E_LANG_ARABIC,
  E_LANG_HEBREW,
  E_LANG_THAI,
  E_LANG_CZECH,
  E_LANG_DANISH,
  E_LANG_DUTCH,
  E_LANG_ENGLISH,
  E_LANG_FINNISH,
  E_LANG_FRENCH,
  E_LANG_GERMAN,
  E_LANG_GREEK,
  E_LANG_HUNGARIAN,
  E_LANG_ICELANDIC,
  E_LANG_ITALIAN,
  E_LANG_MALAY,
  E_LANG_NORWEGIAN,
  E_LANG_POLISH,
  E_LANG_PORTUGUESE,
  E_LANG_RUSSIAN,
  E_LANG_SIMPLIFIED_CHINESE,
  E_LANG_SPANISH,
  E_LANG_SWEDISH,
  E_LANG_TRADITIONAL_CHINESE,
  E_LANG_TURKISH,

  E_LANG_MAX    /*must be LAST in enumeration.*/
} E_UCSLanguage;

/** @brief Definition of the different casing types used
*/
typedef enum
{
  E_UPPER_CASE = 0,
  E_LOWER_CASE
} E_UCSCasing;
#endif /* UT_UCSLANG_H */
/* END OF FILE */

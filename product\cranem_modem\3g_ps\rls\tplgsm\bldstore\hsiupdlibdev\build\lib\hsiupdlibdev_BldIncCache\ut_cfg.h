/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/tplgsm/utinc/ut_cfg.h#3 $
 *   $Revision: #3 $
 *   $DateTime: 2006/09/01 15:09:47 $
 **************************************************************************/
/** \file
 * This file contains the interface for the configurable UCS2 comparison
 * function.
 **************************************************************************/

#ifndef UT_CFG_H
#define UT_CFG_H

/***************************************************************************
 * Nested Include Files
 **************************************************************************/

#include <system.h>

/***************************************************************************
 * Manifest Constants
 **************************************************************************/

/** Maximum length of a UCS2 string that can be compared with the
 * #utStrCmpCfg() function. */
#define MAX_UT_COMPARE_LEN (64)

/** Used as a flag to specify a default, normal UCS2 comparison,
 * sort option in the #utStrCmpCfg() function. */
#define UT_SORT_DEFAULT (0x0000)

/** Used as a flag to specify a custom sort option in the
* #utStrCmpCfg() function. */
#define UT_SORT_CUSTOM  (0x0001)

/** Used as a flag to specify a PinYin sort option in the
 * #utStrCmpCfg() function. This is currently not supported. */
#define UT_SORT_PINYIN  (0x0002)

/***************************************************************************
 *  Function Prototypes
 **************************************************************************/

/** This function determines whether a specified sort order option is
 * supported. This is used to query the options that the utility comparison
 * function (#utStrCmpCfg()) has implemented.
 *
 * \param opts Sort options to check.
 *
 * \retval Boolean TRUE indicates that the sort order option is supported.
 */
extern Boolean utIsSortOrderSupportedCfg (const Int16 opts);


/** This function determines the relative order of two strings.
 * The supplied sort options can be used to apply language specific
 * behaviour. It must support all options which the function
 * #utIsSortOrderSupportedCfg() declares are supported.
 *
 * This functionality is primarily used when sorting phonebook entries.
 *
 * \param s1    Bytes array representing UCS2 charactars of first string.
 * \param s1Len Length of s1 bytes array.
 * \param s2    Bytes array representing UCS2 characters of second string.
 * \param s2Len Length of s2 bytes array.
 * \param opt   Options for the comparison function.
 *
 * \retval SignedInt8 0 Strings are equal.
 * \retval SignedInt8 -1 String 1 comes before string 2.
 * \retval SignedInt8 1 String 1 comes after string 2.
 */
extern SignedInt8 utStrCmpCfg (Char *s1, Int16 s1Len, Char *s2, Int16 s2Len, Int16 opt);

#endif /* UT_CFG_H */

/* END OF FILE */


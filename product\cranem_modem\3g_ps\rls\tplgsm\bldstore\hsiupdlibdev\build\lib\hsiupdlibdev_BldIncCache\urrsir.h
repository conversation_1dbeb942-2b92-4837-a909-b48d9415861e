/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urrsir.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2006/11/08 16:12:14 $
 **************************************************************************
 * File Description:
 *
 *    Header file for URRSIR.C.
 **************************************************************************/

#if !defined (URRSIR_H)
#define       URRSIR_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <urr_sig.h>
#include <uas_asn.h>

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/

/***************************************************************************
*   Macro Functions
***************************************************************************/

/***************************************************************************
 * Types
 ***************************************************************************/

/***************************************************************************
 * Typed Constants
 ***************************************************************************/

/***************************************************************************
 * Function Prototypes
 ***************************************************************************/

void UrrSirInitPersistentData (void);

void UrrSirInit (Boolean initialisation);

void UrrSirCphyNcellBchCnf (RadioResourceControlEntity *urr_p);

void UrrSirProcessInternalSignal (SignalBuffer *signal_p);

void UrrSirSetDebugSysInfoInd (Boolean enabled);

void UrrSirExtractSysInfoFachPayloadData (
                            USystemInformation_FACH *uSystemInformation_FACH_p,
                            UrrSirCellType cellType);

void UrrSirNoCell (void);

UrrSirSibTypeExtExt2 UrrSirGetExtOrExt2SibType(UrrSirDatabase *databasePtr_p,
                                                    Int16 sfn, USegmentIndex segIndex); 
Boolean UrrSirDecodeBcchContainer(Int8 *data_p, Int16 dataLength,
                            UUARFCN uarfcn_DL, UPrimaryScramblingCode psc); 
Boolean UrrSirGetSysInfoContainerInd (void);

Boolean UrrSirGetPredefinedConfig (
                            UPredefinedConfigIdentity predefinedConfigId,
                            UrrSirEncodedPredefinedConfig *predefinedData_p);
void UrrSirRequestCsrToAbortPlmnSearch (void);

void UrrSirBuildAndSendCphyBchReq (Boolean bchOn);


void UrrSirClearPredefinedConfigStore (void);

#if !defined (UPGRADE_EXCLUDE_2G)
void UrrSirUmtsSysInfoInd (UMTS_SysInfo *sysInfo_p,Int8 simId /***********  added simId*/);
void UrrSirUmtsCgiInfoInd (UMTS_CgiInfo *cgiInfo_p,Int8 simId /***********  added simId*/);
void UrrSirSetPredefinedConfigInfoforGsm (UMTS_SysInfo *sysInfo_p);
#endif  /* UPGRADE_EXCLUDE_2G */

void UrrSirResetCrcCount (UrrSirCellType cellType);

void UrrSirDequeueStoredInternalSignals (KiUnitQueue *queue_p);
void UrrSirDequeueStoredExternalSignals (KiUnitQueue *unitQueue_p);
#endif

void UrrSirBackOnCell (void);

void UrrSirCphyRssiScanReqSent (void);
void UrrSirCphyFindCellReqSent (void);
void UrrSirCphyNextCellReqSent (void);
void UrrSirCphyCellSelectReqSent (void);
void UrrSirClearCphyNextCellReqSent (void);
void UrrSirIcsStarted (void);


void UrrSirCphyDrxRssiScanReqSent (void);
void UrrSirCphyDrxFindCellReqSent (void);

void UrrSirCphyDrxRssiScanCnfReceived (const CphyDrxRssiScanCnf * const signal_p);  /* ********** add */
void UrrSirCphyDrxFindCellCnfReceived (const CphyDrxFindCellCnf * const signal_p);   /* ********** add */

void UrrSirNcellBchReq (CphyNcellBchReq *cphyNcellBchReq_p);
void UrrSirNCellSelected (UUARFCN uarfcn_dl, UPrimaryScramblingCode psc);

Boolean UrrSirBcchModificationActivated (void);

Boolean UrrSirScellBchActivated (void);

void UrrSirGetStates (UrrSirCellSelectionState *sirCellSelectionState_p,
                          SibStorageState *sirSibStorageState_p,
                          BchOnOffState *sirBchOnOffState_p);

void UrrCreateAndSendRrcIntScellBcchError (BcchError bcchError);               //-+ ********** 29-Mar-2012 +-

 void UrrCreateAndSendRrcIntNcellBcchError (BcchError bcchError);              //-+ ********** 29-Mar-2012 +-

 void RouteSysInfoType12 (USysInfoType12 *uSysInfoType12_p,
                                 UrrSirCellType cellType);

 void RouteSysInfoType18 (USysInfoType18 *uSysInfoType18_p,
                                 UrrSirCellType cellType);

 void RouteSysInfoType11bis (
     USysInfoType11bis *uSysInfoType11bis_p,
     UrrSirCellType cellType);

/* ********** - add begin */
#if defined UPGRADE_CSG1
 void RouteSysInfoType20 (
      USysInfoType20 *uSysInfoType20_p,
      UrrSirCellType cellType);
#endif //UPGRADE_CSG1
/* ********** - add end */

 Boolean UrrSirsCanRouteSib (UrrSirCellType   cellType,
                                   USIB_Type         sib_Type,
                                   UrrSirSibTypeExtExt2    sib_TypeExt);

 void urrSirSaveDelayedSysInfo (UrrSirCellType    cellType,
                                         USIB_Type         sib_Type,
                                         UrrSirSibTypeExtExt2    sib_TypeExt,
                                         Int8             *data_p,
                                          Int16             dataLength);

#if defined (UPGRADE_DSDS)
void UrrSirSetBchOnOffStateSuspend (void);   /* for CQ00015694 */
void UrrSirAbortBcchReading (void);        /* for CQ00017302 */
void UrrSirSetBackOnCellCalled (Boolean flag);/*CQ00036741*/
void UrrSirCheckToTurnOffBchBeforeSuspended (void); //CQ00112419 added
#endif                                           
Boolean UrrSirGetBackOnCellCalled (void);/*CQ00043620 add*/
/* CQ00060171, BEGIN */
void UrrSirSetCsfbInProgress (void);
void UrrSirClearCsfbInProgress (void);
/* CQ00060171, END */
//CQ00072233 begin
Boolean UrrSirGetCsfbInProgress (void);
void UrrSirSetCsfbOccured (Boolean status);
Boolean UrrSirGetCsfbOccured (void);
//CQ00072233 end

/* CQ00078424 begin */
Boolean isCSFBOnCellWithoutDMCR(void);
/* CQ00078424 end */

/* CQ00063419 begin */
Int16 length2lengthInBytes(Int16 length);

void apendSegmentInChain(segNode *segNode_p);

segNode *createSegNode(USIB_Type     sibType,
                       sibSegment    segType,
                       Int16         sfn,
                       USegmentIndex segIndex,
                       USegCount     seg_Count,
                       Int16         length,
                       Int8          *data_p);

void clearSegChain(void);

void removeSegNode(segNode **segNode_p);

void removeSegNodeFromChain(segNode **segNode_p);

void checkAndApendSegmentInChain(UrrSirCellType       cellType, 
                                 sibSegment           segType, 
                                 USIB_Type            sib_Type, 
                                 Int16                sfn, 
                                 USegmentIndex        segIndex,
                                 USegCount            seg_Count,
                                 Int16                length,
                                 Int8                 *data_p);

void checkAndProcessSegmentInChain(UrrSirCellType cellType);

/* CQ00063419 end */

/* ********** begin */
#if defined (RRC_SIR_OPTIMIZATION)
 Boolean UrrSirIsMibMatchDbAndCleanOldCell (UrrSirCellType cellType);
 void UrrSirDbSysInfoSetTimeStamp(UrrTimeStamp *cellTimeStamp_p);
 Boolean UrrIsTimeStampPeriodElapsed(UrrTimeStamp *timeStamp_p);
 Boolean UrrSirDbSysInfoAreCellTheSamePhysical(UrrSirCellInfo    *cellInfoA_p,
                                                    UrrSirDatabase   *databasePtr_p);
 Boolean UrrSirIsPhysicalParamsEqualToSirCellInfo(UrrSirCellInfo *cellInfoA_p,
                                                                                 UUARFCN  uarfcn, 
                                                                                 UPrimaryScramblingCode  primaryScramblingCode);
 Boolean UrrSirComparePlmn(UPLMN_Identity          *plmn_IdentityA_p,
                                  UPLMN_Identity           *plmn_IdentityB_p);
 Boolean UrrSirDbSysInfoIsCellIdMatch(UrrSirCellInfo     *cellInfoA_p,
                                              UrrSirDatabase *databasePtr_p);
 Boolean UrrSirIsSib3MatchDbAndCleanOldCell (UrrSirCellType cellType);
 void UrrSirDbSysInfoDeleteCell(UT_SINGLE_LINK_LIST_NODE_TYPE (UrrSirVisitedCellsList) *cellToDelete_p);
 void UrrSirCellMatchDb(void);
 void SirReadAndRouteSysInfoFromDataBase(void);
 void UrrSirBuildAndSendCellMatcDbSignal(void);
 void UrrSirStoreEncodedSibsInDb (SignalBuffer *signal_p);
 Boolean UrrSirMoveToListHeadIfCellExistsInDbForSirCon (UUARFCN uarfcn, UPrimaryScramblingCode  primaryScramblingCode);
 UrrSirCellType UrrSirCampOnServOrNeighbour(SignalBuffer *signal_p);
 Int32 UrrSirDbCalculateNewCellSize(UrrSirCellType cellType);
 Int32 UrrSirDbCalculateOneCellSize(UrrSirVisitedCellNode *cell_p);
 Int32 UrrSirDbCalculateVisitedCellsDbSize(void);
 void UrrSirAddCellToVisitedCellsList(UrrSirCellType cellType);
 void UrrSirStoreSibInCellDataBase(UrrSirEncodedSibStoreEntry    *sib_p,
                                   USIB_Type                    sib_Type,
                                   //ExtSibTag                    ExtTag,
                                   //T_USIB_TypeExt2              sib_TypeExt2,
                                   UrrSirSibTypeExtExt2          extSibType,
                                   UrrSirVisitedCellNode        *newCell_p);
 void UrrSirDbDeleteSiMsgsListElement(UrrSirSiMsgsListElement *node_p);
 void UrrSirInitDataBase(void);
 void UrrSirDbFreeVisitedCellMemory(SirUrrCellDb* visitedCell_p);
 void UrrSirSaveTimeStampStartForDataBase(UrrTimeStamp *cellTimeStamp_p);
 void UrrSirSaveTimeStampEndForDataBase(UrrTimeStamp *cellTimeStamp_p);
 void UrrSirSaveTimeElapesForStaticsInfo(void);
 void UrrSirSetTimeStampWhenLeaveCell(void);
 void UrrSirPrintAllDataBaseParameters(void);
 Boolean UrrSirIsSirDbFull(void);
 UrrSirCellInfo  *UrrSirGetCellInfoFromDbTail(void);
 void UrrSirMibChangeForTesting (void);
 UrrSirCellInfo  * UrrSirGetCellInfoFromDbTail(void);
 void UrrSirInitConnectedCellDecode (UUARFCN uarfcn_DL);
 void UrrSirNcellBchReqOff (void);
 
#endif //RRC_SIR_OPTIMIZATION

#if defined(UPGRADE_UL_ECF) // ********** begin
 UUARFCN UrrSirGetDlUarfcn (void);
#endif /* UPGRADE_UL_ECF  */ // ********** end 

/* ********** end */

#if defined(UPGRADE_PLMS)
/* ********** added begin */	
void UrrSirBuildAndSendCphyFgBchReq (Boolean bchOn,UUARFCN  uarfcnDl,UPrimaryScramblingCode psc);
/* ********** added end */	
void UrrSirStartFgCellBch(UUARFCN  uarfcnDl,UPrimaryScramblingCode psc);
void UrrSirStopFgCellBch(Boolean isBchOn);


#endif/*UPGRADE_PLMS*/

Boolean IsSibTypeDeferred(USIB_Type sib_Type, UrrSirSibTypeExtExt2 sib_TypeExt); // ********** added
Boolean UrrSirCanSib19BeDeferred (void); // ********** added
#if defined (UPGRADE_DISABLE_WB_BCH)
void UrrSirCheckDeferredSibs(void);
#endif//UPGRADE_DISABLE_WB_BCH    
 /* ********** added begin */
#if defined (UPGRADE_DSDSWB)
 Boolean SirCheckCanReadDeferredSibsWhenCamping (void);
#endif//UPGRADE_DSDSWB
 /* ********** added end */
void UrrSirUpdateServingCellUarfcn(UUARFCN new_uarfcn_dl); /*********** added */
 /* END OF FILE */

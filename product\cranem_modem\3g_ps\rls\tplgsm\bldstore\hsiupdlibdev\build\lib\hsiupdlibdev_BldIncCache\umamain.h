/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/umamain.h#4 $
 *   $Revision: #4 $
 *   $DateTime: 2007/02/02 13:54:44 $
 **************************************************************************
 * File Description:
 *
 *    Contains the external interface to the system for UMAC
 **************************************************************************/

#if !defined (UMAMAIN_H)
#define       UMAMAIN_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <system.h>
#include <kernel.h>
#include <umautl.h>

/***************************************************************************
 * Function Prototypes
 ***************************************************************************/

#ifdef PC_TTCN_INT_TEST
void UmaGeneralInitialization();
#endif

#if defined (ON_PC)
extern  void            UpMacTaskExitRoutine
    (void);
#endif

#if !defined(ENABLE_UP_MIPS_TEST)
extern KI_ENTRY_POINT UpMacTask (void);
#else



                                      
#endif /* ENABLE_UP_MIPS_TEST */
extern Boolean UmaAwaitingTrafficReq(void);
void UmacProcessDeactivateQSignals ( UmacInternalState macState,
                                     SignalBuffer * rxSignal_p  );
void DequeuePhyFrameInd (void);

void UmacSetEdchState(UmacEdchState newUmacEdchState, Int32 line) ;

void UmaSetEnhancedDlType (UmacEnhancedDlType newEnhancedDlType);
UmacEnhancedDlType UmaGetEnhancedDlType (void);
void UmaClearInterRatTransitionInd (void);
Boolean UmaGetInterRatTransitionInd (void);
#if defined (DEVELOPMENT_VERSION)
void UmacLogDebugParam ( UmacDebugType          type,
                         Int32                  param1,
                         Int32                  param2,
                         Int32                  param3 );
#endif /* DEVELOPMENT_VERSION */

void UmaInitCompressedModeInfo ( void );

#if defined (ENABLE_UMAC_UNIT_TEST)
void UmaXhsHandlePhyHsDataInd(SignalBuffer *sigbuf_p);
#endif

/********** - start*/
void UmaInitHypHfnStore(void);

void UmaInitHypHfnToDetectionPhase(void);

void UmaInitHypHfnToCorrectionPhase(void);

void UmaFixCsHfn(UMacHyperFrameNumber prevDlHfn, UMacHyperFrameNumber newDlHfn);

const CmacCsVoiceRabValiditityParams *UmaGetVoiceRabValidityParams(BearerIdentity firstRbId);
/********** - end*/
/*********** begin*/
#if defined (UPGRADE_UL_ECF)
void UmacEDchRachConfiguration(void);
#endif
/*********** endif*/

#endif

/* END OF FILE */

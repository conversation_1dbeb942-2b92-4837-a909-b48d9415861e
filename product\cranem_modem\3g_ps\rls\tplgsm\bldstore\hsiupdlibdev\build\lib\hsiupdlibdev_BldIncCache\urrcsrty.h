/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/lib/src/urrcsrty.h#8 $
 *   $Revision: #8 $
 *   $DateTime: 2007/03/30 13:53:25 $
 **************************************************************************
 * File Description:
 *
 *    Contains types, definitions and declarations local to CSR.
 **************************************************************************/

#if !defined (URRCSRTY_H)
#define       URRCSRTY_H

/***************************************************************************
* Nested Include Files
***************************************************************************/
/*********** START */
#if !defined (ERRC_UNIT_TEST) 
/*********** END */
#include <system.h>
#include <uas_asn.h>
#include <sir_typ.h>
#include <cphy_sig.h>
#include <grrmrtyp.h>
#include <rrc_sig.h>
#include <urrtypes.h>
#include <urrintsig.h>
#include <urrsirty.h>
#include <rrc_typ.h>
#include <gkisig.h>
#include <diag.h>
#include <urrcsrfsm.h>
#if ! defined (UPGRADE_EXCLUDE_2G)
#include <grrrcsig.h>
#endif
/* ********** : Upgrade to LTE+WCDMA feature : begin */
//#if !defined (EXCLUDE_GSM_PS_AS) || !defined (EXCLUDE_GSM_L1)
#include "lteUtraGsmIratItf.h"
//#endif
/* ********** : Upgrade to LTE+WCDMA feature : end */

/* ********** - add begin */
#if defined UPGRADE_CSG1
#include <rrcsgutils.h>
#endif //UPGRADE_CSG1
/* ********** - add end */

/*********** add begin*/
#if defined UPGRADE_PLMS
#include <plms_sig.h>
#include <urrl1asig.h>
#endif/*UPGRADE_PLMS*/
/*********** add end*/

/***************************************************************************
 * Manifest Constants
 ***************************************************************************/

#define MAX_DL_UARFCN_IN_DETECTED_REQ 100

/* ********** added begin */
//this marks max number of 3G UARFCNs that should be part of special case of ICS (after forcing connection establishment to fail) before starting GSM
//the value of "1" was decided in special and very specific case in testing in Germany in favour of customer (abcfManufactureSpecificInfoNavyEnabled)
#define MAX_NUM_OF_UARFCNS_BEFORE_ATTEMPTING_GSM 1
/* ********** added end */

//The size of the Found GSM cells list
#define MAX_ARFCNS_IN_REMOVE_FROM_UMTS_LIST 100
#define CSR_START_FG_FAST_MODE 1
extern Int32 RRC_TRACE_MASK;

/* ********** - add begin */
#if defined UPGRADE_CSG1
#define MAX_INTER_FREQS_SUPPORTED_BY_L1    (Int8) 2
#endif //UPGRADE_CSG1
/* ********** - add end */

/******************************************************************************
 *  Timers
 */

#if defined (ON_PC)
    #define URR_CSR_GUARD_TIMER_TOUT            30   /* In seconds. */
	#define URR_CSR_DRX_FIND_CELL_GUARD_TIMER_TOUT	80 /* In seconds */	
    #define URR_CSR_SHORT_GUARD_TIMER_TOUT      10   /* In seconds. */
#else
    #define URR_CSR_GUARD_TIMER_TOUT            20   /* In seconds. */
	#define URR_CSR_DRX_FIND_CELL_GUARD_TIMER_TOUT	80 /* In seconds */	
    #define URR_CSR_SHORT_GUARD_TIMER_TOUT       1   /* In seconds. */
#endif
#define URR_CSR_MULTI_BCCH_GUARD_TIMER_TOUT      90   /* In seconds. */ //CQ00110386 added //CQ00111530 increase to 90 from 60

#define DT_CSFB_READ_SIBS_TIME    5     /* In seconds. */ //********** add

#define MAX_FAIL_PLMN_SEARCH_BEFORE_RECOVERY    10

// INIT_HIGHEST_HCS_PRIO is set to 0 (MIN HCS) in order to find the highest HCS priority
#define INIT_HIGHEST_HCS_PRIO                                   0
// INIT_LOWEST_HCS_PRIO is set to 7 (MAX HCS) in order to find the lowest HCS priority
#define INIT_LOWEST_HCS_PRIO                                    7
// INIT_INVALID_HCS - If reference HCS value is INIT_INVALID_HCS then no cell with
// lower HCS priority that Serving HCS was found
#define INIT_INVALID_HCS                                              8
// INIT_INVALID_HCS_NOT_USED - If reference HCS value is INIT_INVALID_HCS_NOT_USED
// then the UE is not fast or HCS is no used.
#define INIT_INVALID_HCS_NOT_USED                           9
#define URRC_CSRR_HOLD_OFF_TOUT                  1
#define URRC_START_CELL_SELECTION_TOUT          12
#define URRC_START_CELL_SELECTION_TOUT_FACH      4
#define URRC_CSRRM_CR_MAX_TOUT                  10
#define URRC_CSRRM_CR_MAX_HYST_TOUT             10

/* PTK_CQ00239728 begin */
#define URRC_SHORT_T_RESELECTION_FACTOR          3      //how many DRX cycles we would like to use the short Treselection after leaving cell DCH
#define URRC_SHORT_T_RESELECTION_PERIOD        200      //the short Treselection period
#define URRC_MSEC_IN_FRAME                      10
/* PTK_CQ00239728 end */



#if defined (URRC_DEBUG_CSR_SUB_PROCESS)
#define URRC_CSRP_GSM_PLMN_SEARCH_TOUT          60
#else
#if !defined UPGRADE_PLMS // ********** added
#define URRC_CSRP_GSM_PLMN_SEARCH_TOUT          120
#else // ********** added
#define URRC_CSRP_GSM_PLMN_SEARCH_TOUT          240// ********** added
#endif// ********** added
#endif

#define URRC_CSRP_320_DRX_CYCLE                         32
#define URRC_CSRP_GSM_PLMN_SEARCH_320_DRX_TOUT          240

#if defined (URRC_DEBUG_CSR_SUB_PROCESS)
#define URRC_CSRTU_T3174_TIMER_TOUT             25
#else
#define URRC_CSRTU_T3174_TIMER_TOUT             15
#endif

/* Delay before informing MM of the failure to find a serving cell during
   OUT OF SERVICE AREA handling. Must be at least 30 seconds. */
#define URRC_CSR_TIMER_OUT_OF_SERVICE_IDLE_TOUT 30

/* ********** - add begin */
#if defined UPGRADE_CSG1
#define CELL_BARRED_TIME_OUT_CSG_NOT_IN_WL 1800  /*  30 minutes. */
#endif //UPGRADE_CSG1
/* ********** - add end */


#define URRC_CSR_START_PLMN_SEARCH_UNINTLVD_SNIFF_CYCLE_TOUT  15

/* OUT OF SERVICE AREA timer. Minimum value is 30 seconds. TS 25.331, 7.2.2. */
#define URRC_CSR_TIMER_OUT_OF_SERVICE_TOUT      30  /* RRC CONNECTED only */
/* ********** begin */
#if !defined (ON_PC)
#define URRC_CSRR_IRAT_PINGPANG_TOUT            120 /* seconds */
#define URRC_CSRR_IRAT_HOLD_OFF_TOUT            300 /* seconds */
#endif
/* ********** end */

#if defined (PS_STRESS_TEST)
/* ********** from 300 -> 20 */
#define URRC_CSR_CAMP_ON_MOD_MEAS_VALUE_TOUT    20 /* seconds ********** */
#endif

#define URRC_EARFCN_BARRED_FOLLOWING_RESELECTION_FAILURE_SPECIAL_PCI     0xFFFE  /* ********** add */
#define URRC_EARFCN_BARRED_FOLLOWING_REDIRECTION_FAILURE_SPECIAL_PCI     0xFFEE  /* ********** add */

/******************************************************************************
 *  Measurements
 */

#define RESET_MONITOR_SENT              -1
#define MAX_WAIT_INDICATIONS            2/*CQ00036574 chande 3 to 2*/
#define MAX_LTE_WAIT_INDICATIONS        1   /*CQ00078295 add */
#define CSR_Q_QUALMIN_DEFAULT          ( -24 * RRC_SYS_INFO_DB_SCALING_FACTOR)
#define CSR_Q_RXLEVMIN_DEFAULT         (-115 * RRC_SYS_INFO_DB_SCALING_FACTOR)
#define CSR_MAX_ALLOWED_TX_PWR_DEFAULT ( -50 * RRC_SYS_INFO_DB_SCALING_FACTOR)

/******************************************************************************
 * General
 */

#define NUM_REJECTED_UARFCNS    4

#define INVALID_CELL_IDENTITY   4000000000UL

#define INVALID_CPICH_RSCP      -32000
#define INVALID_CPICH_EC_N0     -32000
#define CSR_INVALID_RSSI        -32000

#define CSR_INVALID_TIMEOUT_PERIOD   0

#define INVALID_LAC            0xfffe
#define INVALID_RAC            0x00u /* NOTE: there is no invalid RAC as such,
                                        because all 8 bits may be used. This
                                        constant, though, is always used in con-
                                        junctio with a flag indicating whether
                                        PS DOmain info is present or not: hence,
                                        the value of this constant is safe for
                                        CSR purposes. */

#define INVALID_MAX_TX_POWER 0x7fff /* Defined as SignedInt16 in rrc_typ.h. */

#define MAX_US_SEARCH_QUAL       10 /* Maximum value transmitted. */
#define MAX_US_SEARCH_RXLEV      45 /* Maximum value transmitted. */

#define CSR_MAX_S_SEARCH_RAT     10 /* Maximum value transmitted. */

#define MIN_RX_QUAL              24

#define AC_EC_BIT            0x0400 /* Bit 10 */
#define AC_0_TO_9            0x03ff
#define AC_12_TO_14          0x7000
#define AC_11_TO_15          0xfc00

#define LAST_UUE_POWER_CLASS      4

#define DEFAULT_HCS_PRIO          0
#define DEFAULT_Q_HCS_MAPPED      0

#define CELL_ID_NOT_PRESENT    0xff

#if ! defined (UPGRADE_EXCLUDE_2G)
#define CSR_MAX_SUPPORTED_OTHER_RATS  1  /* GSM only. */
#define CSR_URAT_IDENTIFIER_GSM       0  /* Always in this position in the
                                            array "rats" in "CsrRatList".
                                            This protects us from changes in
                                            the ASN.1. */

/* Identifier used by AIS when it acknowledges to CSR that the CCOFU failure
   signal has been sent. */
#define CELL_CHANGE_ORDER_FROM_UTRAN_FAILURE_IDENTIFIER   12

/* Used to mask off bottom 9 bits of PSC from the Tx-Diversity indicator in
   bit 10 */
#define CSR_PSC_MASK                    0x01ff
#define CSR_TX_DIVERSITY_INDIC_BIT      0x0200

#define SIZE_OF_LIST_OF_ALL_GSM_ARFCNS  128    /* In bytes. */
#endif     /* UPGRADE_EXCLUDE_2G */

#define CSR_MAX_FORBIDDEN_LAI_LIST_LEN   10

#define THREE_DIGIT_MNC_LENGTH   3

/* Number of frequencies that can be barred at one time */
/* Merge CQ00039099 begin */
#define CSR_NUM_BARRED_FREQUENCIES      16
/* Merge CQ00039099 end */

/* max number of cells in which Phy measures */
#define MAX_NUM_CELL_RANKED             12
/******************************************************************************
 * System information masks
 */

/** Bitmap for frequency identities.
 * For example: 0x00000002 indicates Freq ID 1.
 */
typedef Int8 FreqIdMask;

#define INVALID_PRIORITY_INDEX       0xFF
#define INVALID_FREQ_INDEX           0xFF
#define LOWEST_ABSOLUTE_PRIO         0xFF

#define MAX_LTE_CELL_MEAS    128 /* maxNumEUTRAFreqs * maxEUTRACellPerFreq */

/* ********** add */
#define CSR_MAX_EUTRA_CELL_BARRED_LIST   64    
#define CSR_MAX_BARRED_TIMER_TICKS       0xFFFFFFFF
/* ********** end */

#define MAX_MULTI_PLMNS      6

#define NUM_EXT_SIBS_TO_ACQUIRE_FOR_CSR  3	// ********** modified
#define NUM_SIBS_TO_ACQUIRE_FOR_CSR  9

/* ********** modify begin */
#define BCH_INFO_MIB_BIT     0x0001
#define BCH_INFO_SI_1_BIT    0x0002
#define BCH_INFO_SI_3_BIT    0x0004
#define BCH_INFO_SI_4_BIT    0x0008
#define BCH_INFO_SI_5_BIT    0x0010
#define BCH_INFO_SI_6_BIT    0x0020
#define BCH_INFO_SI_11_BIT   0x0040
#define BCH_INFO_SI_12_BIT   0x0080
#define BCH_INFO_SI_18_BIT   0x0100

#define BCH_INFO_SI_11_BIS_BIT 0x0200
#define BCH_INFO_SI_19_BIT 0x0400
#define BCH_INFO_SI_20_BIT 0x0800
/* ********** modify end */

#define CSR_CONNECTED_ONLY_SYS_INFO   (    \
            BCH_INFO_SI_4_BIT       | \
            BCH_INFO_SI_6_BIT       | \
            BCH_INFO_SI_12_BIT)

#define CSR_IDLE_SYS_INFO        (    \
            BCH_INFO_MIB_BIT        | \
            BCH_INFO_SI_1_BIT       | \
            BCH_INFO_SI_3_BIT       | \
            BCH_INFO_SI_5_BIT       | \
            BCH_INFO_SI_11_BIT)

#define CSR_SYS_INFO_REQ_IN_IDLE (CSR_IDLE_SYS_INFO)

/* DMCR DEVELOPMENT begin */
#define CSR_IDLE_DMCR_SYS_INFO   (    \
            BCH_INFO_MIB_BIT        | \
            BCH_INFO_SI_1_BIT       | \
            BCH_INFO_SI_3_BIT       | \
            BCH_INFO_SI_5_BIT)
/* DMCR DEVELOPMENT end */

#define CSR_IDLE_EXT_SYS_INFO        (    \
            BCH_INFO_MIB_BIT        | \
            BCH_INFO_SI_1_BIT       | \
            BCH_INFO_SI_3_BIT       | \
            BCH_INFO_SI_5_BIT       | \
            BCH_INFO_SI_11_BIT      | \
            BCH_INFO_SI_11_BIS_BIT)

#define CSR_EXT_SYS_INFO_REQ_IN_IDLE (CSR_IDLE_EXT_SYS_INFO)


#define CSR_SYS_INFO_REQ_IN_CONNECTED (   \
            CSR_IDLE_SYS_INFO             | \
            CSR_CONNECTED_ONLY_SYS_INFO)

#define CSR_SYS_INFO_REQ_FOR_CELL_EVALUATION ( \
        BCH_INFO_MIB_BIT        | \
        BCH_INFO_SI_1_BIT       | \
        BCH_INFO_SI_3_BIT)

#define CSR_ALL_SYS_INFO    (                 \
            CSR_IDLE_EXT_SYS_INFO           | \
            CSR_CONNECTED_ONLY_SYS_INFO     | \
            BCH_INFO_SI_18_BIT)

#define CSR_NO_SYS_INFO_RECEIVED    0

/* ********** added */
#define CSR_DMCR_SIBS    (           \
            BCH_INFO_SI_11_BIT     | \
            BCH_INFO_SI_11_BIS_BIT | \
            BCH_INFO_SI_12_BIT     | \
            BCH_INFO_SI_18_BIT)

/* Flags for use when evaluating the properties of the cells in the neighbour
   cell list */

typedef Int16       CsrCellFlags;

#define CSR_CELL_PRESENT                    ((CsrCellFlags) (0x0001))
#define CSR_CELL_UNAVAILABLE                ((CsrCellFlags) (0x0002))
#define CSR_CELL_H_SATISIFED                ((CsrCellFlags) (0x0004))
#define CSR_CELL_IS_SERVING_CELL            ((CsrCellFlags) (0x0008))
#define CSR_CELL_DELETED                    ((CsrCellFlags) (0x0010))
#define CSR_CELL_BSIC_VERIFIED              ((CsrCellFlags) (0x0020))
#define CSR_CELL_TN_TIMER_EXPIRED           ((CsrCellFlags) (0x0040))
#define CSR_CELL_TRESELECTION_TIMER_EXPIRED ((CsrCellFlags) (0x0080))
#define CSR_CELL_UPDATE_MEAS_AFTER_EXPIRED  ((CsrCellFlags) (0x0100))   /* CQ00006614 add */
#define CSR_CELL_PRIO_CRIT_SATISFIED        ((CsrCellFlags) (0x0200))
/* ********** - add begin */
#if defined UPGRADE_CSG1
#define CSR_CELL_BARRED_CSG               ((CsrCellFlags) (0x0400))
#endif //UPGRADE_CSG1
/* ********** - add end */

/* The Monitor Set cell suitability report flags.  */

typedef Int8        CsrCellsReportBitMask;

#define NOT_YET_EVALUATED_CELLS     ((CsrCellsReportBitMask)(0x01))
#define SUITABLE_CELLS              ((CsrCellsReportBitMask)(0x02))
#define ACCEPTABLE_CELLS            ((CsrCellsReportBitMask)(0x04))
#define NO_SERVICE_CELLS            ((CsrCellsReportBitMask)(0x08))


/* NOTE: MM may send a list of "recommended frequencies" to CSR both in
         RrcPlmnListReq (MAX_STORED_SRCH_FDD_UARFCN) and rrcActReq
         (MAX_FDD_INTER_FREQUENCY_CELLS). These end up in a list of freqs to
         search. CSR may add its own set of frequencies. The overall list must
         be able to contain the sum of
         a) the frequencies received from MM and
         b) CSR's own list of frequencies
         Hence the definitions and checks below, just in case someone changes
         MAX_FDD_INTER_FREQUENCY_CELLS and/or MAX_STORED_SRCH_FDD_UARFCN in
         the RRC-CSR interface. */
#define CSR_OWN_FREQS_TO_SEARCH    5

#if (MAX_FDD_INTER_FREQUENCY_CELLS >= MAX_STORED_SRCH_FDD_UARFCN)
#define CSR_MAX_FREQS_TO_SEARCH    MAX_FDD_INTER_FREQUENCY_CELLS + \
                                   CSR_OWN_FREQS_TO_SEARCH
#else
#define CSR_MAX_FREQS_TO_SEARCH    MAX_STORED_SRCH_FDD_UARFCN + \
                                   CSR_OWN_FREQS_TO_SEARCH
#endif

#define CSR_MAX_FREQS_TO_SNIFF     4

/* Add support for Freq changed after success of CELL_SELECT_CNF
   and check that MONITOR_CELL_REQ has pair of CELL_INFO_REQ
*/

#define CSR_CHANGE_FREQ_MASK        0x8000  /* check whether the HAW point on change freq   */
#define CSR_CHANGE_FREQ_MASK_FIX    0x7FFF  /* Remove the MSB bit represent the FReq Change */
#define CSR_INTRA_INFO_REQ_SENT     0x01
#define CSR_INTER_INFO_REQ_SENT     0x02
#define MILLISECONDS_PER_SECOND     1000

/***************************************************************************
 *  Macro Functions
 ***************************************************************************/

/******************************************************************************
 *
 * Function     : SET_INTEGRATE_MM_LIST_IN_SCAN_ORDER_TABLE
 *
 * Scope        : LOCAL
 *
 * Parameters   : aCTIVE - Whether the MM list has already been integrated to the scan order table or not
 *
 * Returns      : None
 *
 * Description  : Sets the IntegrateMmListInScanOrderTable variable
 *
 *****************************************************************************/
#define SET_INTEGRATE_MM_LIST_IN_SCAN_ORDER_TABLE(aCTIVE, iNDEX) \
    RRC_TRACE_2(URRC_CSR, SET_INTEGRATE_MM_LIST_IN_SCAN_ORDER_TABLE_##iNDEX, URRC_CSR_DEFAULT, \
        "Set IntegrateMmListInScanOrderTable %d -> %d", csrData.IntegrateMmListInScanOrderTable, aCTIVE); \
    csrData.IntegrateMmListInScanOrderTable = aCTIVE;

/* ********** added begin */
/******************************************************************************
 *
 * Function     : SET_CSRR_DELAYING_GSM_RESELECTION
 *
 * Scope        : LOCAL
 *
 * Parameters   : aCTIVE - Whether delaying GSM reselection or not
 *
 * Returns      : None
 *
 * Description  : Sets the csrrData.delayingGsmReselction variable
 *
 *****************************************************************************/
#define SET_CSRR_DELAYING_GSM_RESELECTION(aCTIVE, iNDEX) \
    RRC_TRACE_2(URRC_CSR, SET_CSRR_DELAYING_GSM_RESELECTION##iNDEX, URRC_CSR_DEFAULT, \
                "Set csrrData.delayingGsmReselction %d -> %d", csrrData.delayingGsmReselction, aCTIVE); \
    csrrData.delayingGsmReselction = aCTIVE;
/* ********** added end */
    
/******************************************************************************
 * Function     : SET_CSR_CURRENT_PROC
 * Scope        : LOCAL
 * Parameters   : pROC - New CSR Sub Process
 * Returns      : None
 * Description  : Sets the new active sub process in the CSR
 *****************************************************************************/
#if ! defined (ON_PC)
#define SET_CSR_CURRENT_PROC(pROC,iD) \
    RRC_TRACE_2(URRC_CSR, SET_CSR_CURRENT_PROC_##iD, URRC_CSR_DEFAULT, \
        "Current Active Sub-Process %e{CsrProcId} -> %e{CsrProcId}",\
        csrData.currentActiveCsrProcess,(CsrProcId)pROC);\
    KiLogStateChangeInd(CSR_ACTIVE_PROC_MACHINE_TYPE, (Int16) pROC, (Int16) csrData.currentActiveCsrProcess); \
        csrData.currentActiveCsrProcess = pROC;
#else
#define SET_CSR_CURRENT_PROC(pROC,iD) \
    RRC_TRACE_3(URRC_CSR, SET_CSR_CURRENT_PROC_##iD, URRC_CSR_DEFAULT, \
        "Current Active Sub-Process %d->%d index:%d",\
        csrData.currentActiveCsrProcess,(CsrProcId)pROC, iD);\
    KiLogStateChangeInd(CSR_ACTIVE_PROC_MACHINE_TYPE, (Int16) pROC, (Int16) csrData.currentActiveCsrProcess); \
    csrData.currentActiveCsrProcess = pROC;
#endif

/******************************************************************************
 * Function     : SET_CSRC_STATE
 * Scope        : LOCAL
 * Parameters   : sTATE - New CSRC State
 * Returns      : None
 * Description  : Sets the new state of the CSRC sub process
 *****************************************************************************/
#define SET_CSRC_STATE(sTATE) \
    csrcData.state = sTATE;

/******************************************************************************
 * Function     : SET_CSRS_STATE
 * Scope        : LOCAL
 * Parameters   : sTATE - New CSRS State
 * Returns      : None
 * Description  : Sets the new state of the CSRS sub process
 *****************************************************************************/
/* ********** modify begin */
#define SET_CSRS_STATE(sTATE, iD)                                                       \
    KiLogStateChangeInd(CSRS_STATE_MACHINE_TYPE, (Int16)sTATE, (Int16)csrsData.searchStatus);\
    csrsData.searchStatus = sTATE;

/******************************************************************************
 * Function     : SET_CSRP_STATE
 * Scope        : LOCAL
 * Parameters   : sTATE - New CSRS State
 * Returns      : None
 * Description  : Sets the new state of the CSRS sub process
 *****************************************************************************/
#if !defined UPGRADE_PLMS 
#if defined(KI_RLG_ENABLE_URRC_TRACE_LOG)
#define SET_CSRP_STATE(sTATE, iD) \
	RRC_TRACE_2(URRC_CSR, SET_CSRP_STATE_##iD, URRC_CSR_DEFAULT, \
        "CSRP state change %e{CsrpState}->%e{CsrpState}",\
        csrpData.state, sTATE); \
    KiLogStateChangeInd(CSRP_STATE_MACHINE_TYPE, (Int16)sTATE, (Int16)csrpData.state);\
    URR_LOG_RLG_TRACE_STATE(CSRP_STATE_MACHINE_TYPE, (Int32)sTATE, (Int32)csrpData.state);\
    csrpData.state = sTATE;
#else
#define SET_CSRP_STATE(sTATE, iD) \
	RRC_TRACE_2(URRC_CSR, SET_CSRP_STATE_##iD, URRC_CSR_DEFAULT, \
        "CSRP state change %e{CsrpState}->%e{CsrpState}",\
        csrpData.state, sTATE); \
    KiLogStateChangeInd(CSRP_STATE_MACHINE_TYPE, (Int16)sTATE, (Int16)csrpData.state);\
    csrpData.state = sTATE;
#endif
#else
/* CQ00095497 modify begin */
#if ! defined (ON_PC)
#if defined(KI_RLG_ENABLE_URRC_TRACE_LOG)/*CQ00116947 add */
#define SET_CSRP_ACTIVE_STATE(sTATE, iD) \
	RRC_TRACE_2(URRC_CSR, SET_CSRP_ACTIVE_STATE_##iD, URRC_CSR_DEFAULT, \
        "CSRP ACTIVE state change %e{CsrpActiveState}->%e{CsrpActiveState}",\
        csrpData.activeState, sTATE); \
    KiLogStateChangeInd(CSRP_STATE_MACHINE_TYPE, (Int16)sTATE, (Int16)csrpData.activeState);\
    URR_LOG_RLG_TRACE_STATE(CSRP_STATE_MACHINE_TYPE, (Int32)sTATE, (Int32)csrpData.activeState);\
    csrpData.activeState = sTATE;
/*CQ00116947 add begin*/
#else/*KI_RLG_ENABLE_URRC_TRACE_LOG*/
#define SET_CSRP_ACTIVE_STATE(sTATE, iD) \
	RRC_TRACE_2(URRC_CSR, SET_CSRP_ACTIVE_STATE_##iD, URRC_CSR_DEFAULT, \
        "CSRP ACTIVE state change %e{CsrpActiveState}->%e{CsrpActiveState}",\
        csrpData.activeState, sTATE); \
    KiLogStateChangeInd(CSRP_STATE_MACHINE_TYPE, (Int16)sTATE, (Int16)csrpData.activeState);\
    csrpData.activeState = sTATE;
#endif/*KI_RLG_ENABLE_URRC_TRACE_LOG*/
/*CQ00116947 add end*/

#define SET_CSRP_PLMS_STATE(sTATE, iD) \
	RRC_TRACE_2(URRC_CSR, SET_CSRP_PLMS_STATE_##iD, URRC_CSR_DEFAULT, \
        "CSRP PLMS state change %e{CsrpPlmsState}->%e{CsrpPlmsState}",\
        csrpData.plmsState, sTATE); \
    KiLogStateChangeInd(CSRP_STATE_MACHINE_TYPE, (Int16)sTATE, (Int16)csrpData.plmsState);\
    csrpData.plmsState = sTATE;
#else
#define SET_CSRP_ACTIVE_STATE(sTATE, iD) \
	RRC_TRACE_2(URRC_CSR, SET_CSRP_ACTIVE_STATE_##iD, URRC_CSR_DEFAULT, \
        "CSRP ACTIVE state change %d->%d",\
        csrpData.activeState, sTATE); \
    KiLogStateChangeInd(CSRP_STATE_MACHINE_TYPE, (Int16)sTATE, (Int16)csrpData.activeState);\
    URR_LOG_RLG_TRACE_STATE(CSRP_STATE_MACHINE_TYPE, (Int32)sTATE, (Int32)csrpData.activeState);\
    csrpData.activeState = sTATE;

#define SET_CSRP_PLMS_STATE(sTATE, iD) \
	RRC_TRACE_2(URRC_CSR, SET_CSRP_PLMS_STATE_##iD, URRC_CSR_DEFAULT, \
        "CSRP PLMS state change %d->%d",\
        csrpData.plmsState, sTATE); \
    KiLogStateChangeInd(CSRP_STATE_MACHINE_TYPE, (Int16)sTATE, (Int16)csrpData.plmsState);\
    csrpData.plmsState = sTATE;
#endif /* ON_PC */
/* CQ00095497 modify end */
#endif /* UPGRADE_PLMS */
/* ********** modify end */

#if defined (UPGRADE_CSG1) && defined (UPGRADE_URR_CSG_USING_PLMS)
#define SET_CSRCSGS_PLMS_STATE(sTATE, iD) \
	RRC_TRACE_2(URRC_CSR, SET_CSRCSGS_PLMS_STATE_##iD, URRC_CSR_DEFAULT, \
        "CSRCSGS PLMS state change %e{CsrpPlmsState}->%e{CsrpPlmsState}",\
        csrCsgsData.plmsState, sTATE); \
    KiLogStateChangeInd(CSRCSGS_STATE_MACHINE_TYPE, (Int16)sTATE, (Int16)csrpData.plmsState);\
    csrCsgsData.plmsState = sTATE;

#if defined(KI_RLG_ENABLE_URRC_TRACE_LOG) /*CQ00116947 add */
#define SET_CSRCSGS_ACTIVE_STATE(sTATE, iD) \
	RRC_TRACE_2(URRC_CSR, SET_CSRCSGS_ACTIVE_STATE_##iD, URRC_CSR_DEFAULT, \
        "CSRCSGS ACTIVE state change %e{ActiveState}->%e{ActiveState}",\
        csrCsgsData.activeState, sTATE); \
    KiLogStateChangeInd(CSRCSGS_STATE_MACHINE_TYPE, (Int16)sTATE, (Int16)csrCsgsData.activeState);\
    URR_LOG_RLG_TRACE_STATE(CSRCSGS_STATE_MACHINE_TYPE, (Int32)sTATE, (Int32)csrCsgsData.activeState);\
    csrCsgsData.activeState = sTATE;
/*CQ00116947 add begin*/
#else/*KI_RLG_ENABLE_URRC_TRACE_LOG*/
#define SET_CSRCSGS_ACTIVE_STATE(sTATE, iD) \
	RRC_TRACE_2(URRC_CSR, SET_CSRCSGS_ACTIVE_STATE_##iD, URRC_CSR_DEFAULT, \
        "CSRCSGS ACTIVE state change %e{ActiveState}->%e{ActiveState}",\
        csrCsgsData.activeState, sTATE); \
    KiLogStateChangeInd(CSRCSGS_STATE_MACHINE_TYPE, (Int16)sTATE, (Int16)csrCsgsData.activeState);\
    csrCsgsData.activeState = sTATE;
#endif/*KI_RLG_ENABLE_URRC_TRACE_LOG*/
/*CQ00116947 add end*/

#endif

/* ********** add begin */
#if defined UPGRADE_CSG1
/******************************************************************************
 * Function     : SET_CSRCSGS_STATE
 * Scope        : LOCAL
 * Parameters   : sTATE - New CSRCSGS State
 * Returns      : None
 * Description  : Sets the new state of the CSRCSGS sub process
 *****************************************************************************/
#if defined(KI_RLG_ENABLE_URRC_TRACE_LOG) 
#define SET_CSRCSGS_STATE(sTATE) \
    KiLogStateChangeInd(CSRCSGS_STATE_MACHINE_TYPE, (Int16)sTATE, (Int16)csrCsgsData.state);\
    URR_LOG_RLG_TRACE_STATE(CSRCSGS_STATE_MACHINE_TYPE, (Int32)sTATE, (Int32)csrCsgsData.state);\
    csrCsgsData.state = sTATE;
#else
#define SET_CSRCSGS_STATE(sTATE) \
    KiLogStateChangeInd(CSRCSGS_STATE_MACHINE_TYPE, (Int16)sTATE, (Int16)csrCsgsData.state);\
    csrCsgsData.state = sTATE;
#endif
#endif //UPGRADE_CSG1
/* ********** add end */
/******************************************************************************
 *
 * Function     : SET_CSRR_STATE
 *
 * Scope        : LOCAL
 *
 * Parameters   : sTATE - New CSRR State
 *
 * Returns      : None
 *
 * Description  : Sets the new state of the CSRR sub process
 *
 *****************************************************************************/
#if ! defined (ON_PC)

#if defined(KI_RLG_ENABLE_URRC_TRACE_LOG) /*CQ00116947 add */
#define SET_CSRR_STATE(sTATE) \
    KiLogStateChangeInd(CSRR_STATE_MACHINE_TYPE, (Int16) sTATE, (Int16) csrrData.csrrState);\
    URR_LOG_RLG_TRACE_STATE(CSRR_STATE_MACHINE_TYPE, sTATE, csrrData.csrrState);\
    csrrData.csrrState = sTATE;
/*CQ00116947 add begin*/
#else/*KI_RLG_ENABLE_URRC_TRACE_LOG*/
#define SET_CSRR_STATE(sTATE) \
    KiLogStateChangeInd(CSRR_STATE_MACHINE_TYPE, (Int16) sTATE, (Int16) csrrData.csrrState);\
    csrrData.csrrState = sTATE;
#endif/*KI_RLG_ENABLE_URRC_TRACE_LOG*/
/*CQ00116947 add end*/

#else
#define SET_CSRR_STATE(sTATE) \
    printf("SET_CSRR_STATE, changed from %d to %d", csrrData.csrrState, sTATE);\
    KiLogStateChangeInd(CSRR_STATE_MACHINE_TYPE, (Int16)sTATE,(Int16) csrrData.csrrState);\
    csrrData.csrrState = sTATE;
#endif
/* ********** - add begin */
#if defined UPGRADE_CSG1
/******************************************************************************
 *
 * Function     : SET_CSG_SELECT_STATUS_FSM
 *
 * Scope        : LOCAL
 *
 * Parameters   :   vALUE - The SM new state
 *                          iNDEX - the place the MACRO was called from
 *
 * Returns      : None
 *
 * Description  : Sets the csrCsgSelectStatus in csrCsgData and produces optional debug
 *                printout.
 *
 *****************************************************************************/
#define SET_CSG_SELECT_STATUS_FSM(vALUE, iNDEX)                                \
            RRC_TRACE_2 (URRC_CSR, SET_CSG_SELECT_STATUS_FSM_##iNDEX, URRC_CSR_DEFAULT, \
                        "%e{CsrCsgSelectStatus}->%e{CsrCsgSelectStatus}", csrCsgData.csrCsgSelectStatus,vALUE);  \
            csrCsgData.csrCsgSelectStatus = vALUE;
#endif //UPGRADE_CSG1
/* ********** - add end */

#define SET_CSRS_PLMS_ICS_ACTIVITY(vALUE, iNDEX)                                \
             RRC_TRACE_2 (URRC_CSRS, SET_CSRS_PLMS_ICS_ACTIVITY_##iNDEX, URRC_CSR_DEFAULT,\
      				 "plmsIcsActivity = %e{CsrsPlmsIcsActivityType}", csrsData.plmsIcsParams.plmsIcsActivity,vALUE);\
            csrsData.plmsIcsParams.plmsIcsActivity  = vALUE;

/******************************************************************************
 *
 * Function     : SET_CSRR_MODE
 *
 * Scope        : LOCAL
 *
 * Parameters   : sTATE - New CSRR MODE
 *
 * Returns      : None
 *
 * Description  : Sets the new mode of the CSRR sub process
 *
 *****************************************************************************/
#define SET_CSRR_MODE(mODE) csrrData.csrrMode = mODE;

#define SET_FREQ_LIST_ONLY_SEARCH(vALUE, sERIAL) \
    csrData.freqListOnlySearch = vALUE; \
    RRC_TRACE_1(URRC_CSR, SET_FREQ_LIST_ONLY_SEARCH_##sERIAL, URRC_CSR_DEFAULT, \
                "freqListOnlySearch :%d", vALUE);

#define SET_EXHAUSTIVE_SEARCH(vALUE, sERIAL) \
    csrData.exhaustiveSearch = vALUE; \
    RRC_TRACE_1(URRC_CSR, SET_EXHAUSTIVE_SEARCH_##sERIAL, URRC_CSR_DEFAULT, \
                "exhaustiveSearch :%d", vALUE);

#define SET_SEARCH_TRIGGERED_BY_CSR(vALUE, sERIAL) \
    csrData.searchTriggeredByCsr = vALUE; \
    RRC_TRACE_1(URRC_CSR, SET_SEARCH_TRIGGERED_BY_CSR_##sERIAL, URRC_CSR_DEFAULT, \
                "searchTriggeredByCsr :%d", vALUE);

#define SET_DO_INTERLEAVED_SNIFFING(vALUE, sERIAL) \
    csrData.doInterleavedSniffing = FALSE; \
    RRC_TRACE_1(URRC_CSR, SET_DO_INTERLEAVED_SNIFFING_##sERIAL, URRC_CSR_DEFAULT, \
                "doInterleavedSniffing :%d", vALUE);

#define SET_OOS_CONNECTED_RECOVERY_APPLIES(vALUE, sERIAL) \
    csrData.oosConnectedRecoveryApplies = vALUE; \
    RRC_TRACE_1(URRC_CSR, SET_OOS_CONNECTED_RECOVERY_APPLIES_##sERIAL, URRC_CSR_DEFAULT, \
                "oosConnectedRecoveryApplies :%d", vALUE);

#define SET_OOS_IDLE_RECOVERY_APPLIES(vALUE, sERIAL) \
    csrData.oosIdleRecoveryApplies = vALUE; \
    RRC_TRACE_1(URRC_CSR, SET_OOS_IDLE_RECOVERY_APPLIES_##sERIAL, URRC_CSR_DEFAULT, \
                "oosIdleRecoveryApplies :%d", vALUE);

#define SET_OOS_CONNECTED_NON_DCH_GSM_SEARCH_DONE(vALUE, sERIAL) \
    csrData.oosConnectedNonDchGsmSearchDone = vALUE; \
    RRC_TRACE_1(URRC_CSR, SET_OOS_CONNECTED_NON_DCH_GSM_SEARCH_DONE_##sERIAL, URRC_CSR_DEFAULT, \
                "oosConnectedNonDchGsmSearchDone: %d", vALUE);

#define SET_OOS_CONNECTED_NON_DCH_UMTS_SEARCH_DONE(vALUE, sERIAL) \
    csrData.oosConnectedNonDchUmtsSearchDone = vALUE; \
    RRC_TRACE_1(URRC_CSR, SET_OOS_CONNECTED_NON_DCH_UMTS_SEARCH_DONE_##sERIAL, URRC_CSR_DEFAULT, \
                "oosConnectedNonDchUmtsSearchDone: %d", vALUE);

// ********** content removed

/* The macro should start the URRC_CSRR_FORBIDDEN_TIMER (120s) timer
   only if the received rrcUpdateReq is regarding different UARFCN than
   the one the timer is running for.
   Each function that sets the value to INVALID_UARFCN should explicitly stop
   the URRC_CSRR_FORBIDDEN_TIMER timer if required. */
#define SET_REJECTED_UARFCN(uARFCN, iNDEX) \
{\
    Int8 dummy;\
    RRC_TRACE_2(URRC_CSR, SET_REJECTED_UARFCN_##iNDEX, URRC_CSR_DEFAULT, \
                "numRejectedUarfcns:%d, UARFCN:%d", csrData.numRejectedUarfcns, uARFCN); \
    if ((uARFCN != INVALID_UARFCN) && !CsrIsUarfcnRejected(uARFCN,&dummy)) \
    { \
        if (csrData.numRejectedUarfcns < NUM_REJECTED_UARFCNS) \
        { \
            csrData.rejectedUarfcn[csrData.numRejectedUarfcns].uarfcn = uARFCN; \
            csrData.rejectedUarfcn[csrData.numRejectedUarfcns].timerId = (CsrTimerIdentity)(URRC_CSRR_FORBIDDEN_TIMER + csrData.numRejectedUarfcns); \
            CsrStartTimer(csrData.rejectedUarfcn[csrData.numRejectedUarfcns].timerId, SECONDS_TO_TICKS(120)); \
            csrData.numRejectedUarfcns++; \
        } \
        else \
        { \
            if (csrData.numRejectedUarfcns == NUM_REJECTED_UARFCNS) \
            { \
                CsrStopTimer(csrData.rejectedUarfcn[NUM_REJECTED_UARFCNS - 1].timerId); \
                csrData.rejectedUarfcn[csrData.numRejectedUarfcns - 1].uarfcn = uARFCN; \
                csrData.rejectedUarfcn[csrData.numRejectedUarfcns - 1].timerId = (CsrTimerIdentity)(URRC_CSRR_FORBIDDEN_TIMER + csrData.numRejectedUarfcns - 1); \
                CsrStartTimer(csrData.rejectedUarfcn[csrData.numRejectedUarfcns - 1].timerId, SECONDS_TO_TICKS(120)); \
            } \
        } \
    }\
}

/* ********** added begin */
#if defined (ON_PC)
#define SET_CSRS_SEARCH_CAUSE_AND_DETECTED(cAUSE, iNDEX)                   \
RRC_TRACE_2 (URRC_CSR, SET_CSRS_SEARCH_CAUSE_AND_DETECTED_##iNDEX, URRC_CSR_DEFAULT, \
             "Set SearchCause %d -> %d", csrsData.searchCause, cAUSE);       \
csrsData.searchCause = cAUSE; \
csrsData.detectedNonDchOnIcsStart = UrrCsrGetDetectedOnNonDch();
#else
#define SET_CSRS_SEARCH_CAUSE_AND_DETECTED(cAUSE, iNDEX)                   \
RRC_TRACE_2 (URRC_CSR, SET_CSRS_SEARCH_CAUSE_AND_DETECTED_##iNDEX, URRC_CSR_DEFAULT, \
             "Set SearchCause %e{CsrsSearchCause} -> %e{CsrsSearchCause}", csrsData.searchCause, cAUSE);       \
csrsData.searchCause = cAUSE; \
csrsData.detectedNonDchOnIcsStart = UrrCsrGetDetectedOnNonDch();
#endif


#define SET_CSRS_INTRA_SEARCH_LAST(vALUE, iNDEX)                   \
RRC_TRACE_2 (URRC_CSR, SET_CSRS_INTRA_SEARCH_LAST_##iNDEX, URRC_CSR_DEFAULT, \
             "Set performIntraSearchLast %d -> %d", csrsData.performIntraSearchLast, vALUE);       \
csrsData.performIntraSearchLast = vALUE;
/* ********** added end */

#if !defined MAX
#define MAX(x,y)  (( (x) > (y) ) ? (x) : (y))
#endif
#if !defined MIN
#define MIN(x,y)  (( (x) < (y) ) ? (x) : (y))
#endif

/* NOTE: These macros can only be used to test a SINGLE BIT at a time! */
#define CSR_BIT_IS_SET(z, q)            (((z) & (q)) == (q))
#define CSR_BIT_IS_CLEAR(z, q)          (((z) & (q)) == (0))
#define CSR_SET_BIT(z, q)               ((z) |= (q))
#define CSR_CLEAR_BIT_8(z, q)             ((z) &= ( (Int8) (~(q))) )    /* ********** */
#define CSR_CLEAR_BIT_16(z, q)             ((z) &= ( (Int16) (~(q))) )
#define CSR_CLEAR_BIT_32(z, q)             ((z) &= ( ~(q)) )
/* ********** - add START*/
#define CSR_TEST_INDEX(mASK,iNDEX)        ((mASK) & (1<<(iNDEX)))
#define CSR_CLEAR_32INDEX(mASK, iNDEX)    ((mASK) &= ~((Int32)(1 << (iNDEX))))
#define CSR_SET_32INDEX(mASK, iNDEX)      ((mASK) |= (Int32)(1 << (iNDEX)))
/* ********** - add END*/
/*   When the UE receives RAU Reject from the NW it should receive
 *   rrcConnectionRelease. If it is received till 500 milliseconds passes then
 *   allow reselection and stop the timer. */
#define ASSIGN_BLOCK_RESELECT(Value)                \
        csrData.blockReselectTillRelease = Value;

/*****************************************************************************
 * Operator use access classes are 11 or 15. If any of these bits are set,
 * the expression will be TRUE.
 */

#define OPERATOR_USE_ACCESS_CLASS_SET(a)   (0x8800 & (a))


/******************************************************************************
 * Scaling factors and decoding factors for received system information IEs.
 */

#define CSR_MULTIPLY_BY_TWO(s)          ( (s) << 1 )
#define CSR_MULTIPLY_BY_TWO_PLUS_ONE(s) ( (CSR_MULTIPLY_BY_TWO(s)) + 1 )
#define CSR_DIVIDE_BY_TWO(s)            ( (s) / 2 )
#define CSR_DIVIDE_BY_FIVE(s)           ( (s) / 5 )

/***************************************************************************
 * Types
 ***************************************************************************/

typedef Int8        CsrCellId;
typedef Int16       CsrSysInfoMask;

//ICAT EXPORTED ENUM
typedef enum CsrrReselectionBlockingCritTag
{
    CSRR_NO_BLOCKING,
    CSRR_MEASUREMENTS_BLOCK_RESELECTION,
    CSRR_TEMPORAL_500MS_BLOCK_RESELECTION,
    CSRR_REJECTED_UARFCN_BLOCK_RESELECTION,
    CSRR_HOLD_OFF_TIMER_BLOCK_RESELECTION      /* CQ00047726 add */
}
CsrrReselectionBlockingCrit;

typedef enum CsrRedirectionActionTag
{
    CSR_REDIRECT_NONE,
    CSR_REDIRECT_FIRST_ATTEMPT,
    CSR_REDIRECT_AWAKE_L1,
    CSR_REDIRECT_ABORT,
    CSR_REDIRECT_ICS          /* ********** add */
}
CsrRedirectionAction;

//ICAT EXPORTED ENUM
typedef enum CsrSearchTypeTag
{
    CSR_FOREGROUND_SEARCH,
    CSR_BACKGROUND_SEARCH
}
CsrSearchType;
/* ********** modify begin */
//ICAT EXPORTED ENUM
typedef enum CsrPlmnAbortRequestSourceTag
{
    //CSR_INTERNAL_ABORT,              /* Request from within CSRP. */
    CSR_ABORT_REQ_FROM_CONTROLLER,   /* Request from the CSR Controller. */
    CSR_ABORT_REQ_FROM_CSRR
#if !defined (UPGRADE_PLMS)
    ,
    CSR_SUSPEND_REQ_STOP_LTE    
#endif/*UPGRADE_PLMS*/
/*********** add end*/
}
CsrPlmnAbortRequestSource;
/* ********** modify end */
/* ********** add begin */
#if defined (UPGRADE_CSG1)
//ICAT EXPORTED ENUM
typedef enum CsrCsgsAbortRequestSourceTag
{
    CSRCSGS_INTERNAL_ABORT,              /* Request from within CSRCSGS. */
    CSRCSGS_ABORT_REQ_FROM_CONTROLLER,   /* Request from the CSR Controller. */
    CSRCSGS_ABORT_REQ_FROM_CSRR
    ,
    CSRCSGS_SUSPEND_REQ_STOP_LTE
}
CsrCsgsAbortRequestSource;
#endif //UPGRADE_CSG1
/* ********** add end */


//ICAT EXPORTED ENUM
typedef enum CsrSourceTag
{
    CSR_NO_SOURCE,
    CSR_SOURCE_CSRS,
    CSR_SOURCE_CSRR,
#if ! defined (UPGRADE_EXCLUDE_2G)
    CSR_SOURCE_CSRTU,
#endif
    CSR_SOURCE_GMM,
    CSR_SOURCE_URR_SP_CER,
    CSR_SOURCE_URR_SP,    /* URRC sub-process external to CSR. */
    CSR_SOURCE_PLMS  	  /*search started in another RR*/
}
CsrSource;

typedef enum CsrOosConnNonDchRecoveryFinishSourceTag
{
    CSR_OOS_CONN_NON_DCH_RECOVERY_FINISH_NO_SOURCE,
    CSR_OOS_CONN_NON_DCH_RECOVERY_FINISH_SOURCE_TIMER_EXP,
    CSR_OOS_CONN_NON_DCH_RECOVERY_FINISH_SOURCE_ABORT,
    CSR_OOS_CONN_NON_DCH_RECOVERY_FINISH_SOURCE_3G_ICS_END
}
CsrOosConnNonDchRecoveryFinishSource;

//ICAT EXPORTED ENUM
typedef enum CsrCellEvalTag
{
    //The order of the ENUM values must not be changed since it is used by bestEvaluation in function csrcEvaluateCell()
    CSR_CELL_NO_EVALUATION,
    CSR_CELL_EV_NO_SERVICE,
    CSR_CELL_EV_S_FAILED,
    CSR_CELL_EV_AUTH_FAILED,
    CSR_CELL_EV_INITIAL_EMERGENCY_ONLY,
    CSR_CELL_EV_EMERGENCY_ONLY,
    CSR_CELL_EV_FORBIDDEN_NAT_LA,
    CSR_CELL_EV_FORBIDDEN_LA,
    CSR_CELL_EV_SERVICE
}
CsrCellEval;

#define CSR_SNIFF_INTRA_FREQ_IN_CIL    0x01
#define CSR_SNIFF_INTER_FREQS_IN_CIL   0x02

typedef Int8        CsrTypesOfFreqsToSniffBitMask;

typedef enum CsrTReselectionToStartTag
{
    CSR_START_ALL,
    CSR_START_GSM,
    CSR_START_NON_GSM
}
CsrTReselectionToStart;

typedef enum CsrPlmnMatchResultTag
{
    CSR_PLMN_NO_ACTION,
    CSR_PLMN_MISMATCH,
    CSR_PLMN_NOT_FOUND,
    CSR_PLMN_MATCH
}
CsrPlmnMatchResult;

/* The following enum is currently used only when building an rrcActInd. */
typedef enum CsrSourceForDataToSendToMmTag
{
    CSR_FROM_PRIMARY_DATA_BASE,
    CSR_FROM_STORED_NAS_INFO
}
CsrSourceForDataToSendToMm;

/*****************************************************/

//ICAT EXPORTED ENUM
typedef enum CsrRssiScanTypeTag
{
    RSSI_SCAN_NONE,
    RSSI_SCAN_LIST,
    RSSI_SCAN_RANGE
}
CsrRssiScanType;

//ICAT EXPORTED ENUM
typedef enum CsrRssiScanStageTag
{
    CSR_FREQUENCY_SCAN_NONE          = 0,   /* No Search */
    CSR_FREQUENCY_SCAN_MM_LIST       = 1,   /* Search for MM list of UARFCNs  */
    CSR_FREQUENCY_SCAN_PRIME_LIST    = 2,   /* Search FDD band prime UARFCNs */
    CSR_FREQUENCY_SCAN_WHOLE_BAND    = 3    /* Search all FDD band UARFCNs  */
}
CsrRssiScanStage;

typedef enum CsrUarfcnScanStatusTag
{
    UARFCN_CANDIDATE,       /* UARFCN is candidate for scanning  */
    UARFCN_DONT_SCAN,               /* UARFCN will not be scanned  */
    UARFCN_TO_SCAN,             /* UARFCN will be scanned  */
    UARFCN_SCAN_NEXT_TIME,      /* UARFCN will be scanned next time  */
    UARFCN_SCANNED_NOT_DETECTED,    /* UARFCN was scanned. No cells detected  */
    UARFCN_SCANNED_DETECTED     /* UARFCN was scanned. Cell(s) detected */
}
CsrUarfcnScanStatus;

/* Modification of this enum also requires update of CsrrReselectorModeString in urrutm.c */
//ICAT EXPORTED ENUM
typedef enum CsrrReselectorModeTag
{
    CSRR_NO_MODE,
    CSRR_PRE_CAMP_SYS_INFO_ACQUIS,
    CSRR_POST_CAMP_SYS_INFO_ACQUIS
}
CsrrReselectorMode;

typedef enum CsrPriorityStatusTag
{
	SYS_INFO_PRIO,
    DEDICATED_PRIO
}
CsrPriorityStatus;

#if defined (ENABLE_SEARCH_ABORT_BY_SIM2_SERVICE)
typedef enum CsrAbortReselectCauseTag
{
    RECEIVE_SUSPEND,
    RECEIVE_ABORT_SEARCH
}
CsrAbortReselectCause;
#endif//ENABLE_SEARCH_ABORT_BY_SIM2_SERVICE    

/*******************************************************/
/******** UARFCN RSSI SCAN RESULTS TABLE (Start) **********/

#define INVALID_UARFCN_INDEX  0xffff

typedef Int16 CsrStoredTableIndex;

typedef struct CsrUarfcnScanResultsTableElementTag
{
   UUARFCN                  uarfcn;
   SignedInt16              utra_CarrierRSSI;   /* UARFCN last RSSI results. Updated upon RssiScanCnf */
   CsrUarfcnScanStatus      uarfcnScanStatus;       /* UARFCN scan status. Updated during the search */
   RssiScanGrade            rssiScanRawGrade;   /* Used for grading Additional UARFCNs */
   /* The score that each band got from L1. valid only when scanInFftMode = TRUE */
   SignedInt16              bandScore;
}
CsrUarfcnScanResultsTableElement;

typedef struct CsrUarfcnScanResultsTableTag
{
    Int16                               resultsTableSize;   /* RSSI scan results table size */
    CsrUarfcnScanResultsTableElement    *resultsTable;      /* RSSI scan results table. Ordered by (BAND,UARFCN) */
}
CsrUarfcnScanResultsTable;

/******** UARFCN RSSI SCAN RESULTS TABLE (End) **********/
/*******************************************************/

/*******************************************************/
/******** UARFCN RSSI SCAN ORDER TABLE (Start) **********/


typedef struct CsrStoredScanOrderTableElementTag
{
    UUARFCN                 uarfcn;
    RssiScanGrade           rssiScanGrade;
}
CsrUarfcnScanOrderTableElement;

typedef struct CsrUarfcnScanOrderTableTag
{
    Int16                       numOfOrderedUarfcns;
    CsrUarfcnScanOrderTableElement  *orderTable;        /* RSSI scan results table. Ordered by (BAND,UARFCN) */
}
CsrUarfcnScanOrderTable;

/******** UARFCN RSSI SCAN ORDER TABLE (End) **********/
/*******************************************************/

/*******************************************************/
/******** MAIN UARFCN RSSI SCAN TABLE (Start) **********/

typedef struct CsrStoredSearchTableTag
{
    CsrRssiScanStage            rssiScanStage;      /* Current search stage (in sync with csrData.rssiScanStage) */
    CsrUarfcnScanResultsTable   scanResultsTable;   /* Nested Table for the RSSI scan results */
    CsrUarfcnScanOrderTable     scanOrderTable;     /* Nested Table for the UARFCN scan order */
    Boolean                     resetTable;         /* Flag for reseting the RSSI scan results */
    RssiScanGrade               maxRssiScanGrade;   /* Max grade in last scan */
}
CsrStoredSearchTable;

/********  RSSI SCAN RESULTS-related (End) ***********/
/*****************************************************/


/******** Multiband -related (Start) **********/
/*****************************************************/

typedef enum FddPhyBandsReadStateTag
{
    CSR_FDD_PHY_BANDS_NOT_READ,
    CSR_FDD_PHY_BANDS_READING,
    CSR_FDD_PHY_BANDS_READ
}
FddPhyBandsReadState;

typedef struct CsrFddBandDataTag
{
    FddBandsMask    fddBandsToScanFirstBits;/*CQ00032585 add*/
             /* This is a bit mask giving the bands to scan first. This
                will generally be the last used band(s). */

    FddBandsMask    fddBandsToScanBits;
             /* This is a bit mask giving all the bands we must scan. */

    FddBandsMask    currentFddBandBit;
             /* This is a bit mask giving the band we are currently in. */

    FddBandIndex    currentFddBandIndex;
             /* This is an array index giving the band we are currently in.
                It is calculated from fddCurrentBandToScanBit, and band I
                has a value of currentBandIndex = 0 */

    FddBandRegion   fddBandRegion;
             /* This is an array index giving the band region we are currently in.*/

    Boolean         additionalUarfcns;
             /*  This Boolean is set to to TRUE when our current band
                 has additional UARFCNs that have not been scanned yet.*/
}
CsrFddBandData;


typedef struct MultiBandTag
{

    FddBandsMask            phySupportedFddBandsBits;
                            /* Supported FDD bands as reported by CPHY. */

    FddBandsMask            meConfiguredFddBandsBits;
                            /* Configured FDD Bands by higher layers */

    FddBandMode             lastFddBandMode;
                            /* The RrcActReq  and RrcPlmnListReq fddBandMode field gives us the last
                               band that was used and this band will be scanned first. This is the information that
                               is given to MM by the RrcActCnf and RrcActInd */

    CsrFddBandData          bandData;
                            /* Structure used to track progress */

    FddPhyBandsReadState    phyBandReadState;
                            /*  Indicates the state of reading the supported phy bands */
    FddBandsMask            fddBandsToScan;
                            
    /* ********** begin */
#if defined (SS_IPC_SUPPORT)    /* CQ00069538 modified */
    Boolean               onlyRegistration;   /* TRUE:then UE must use only the band requested */
#endif
    /* ********** end */
}
MultiBand;

/******** Multiband -related (End) **********/
/*****************************************************/

/***********************************************/
/******** CSR PROCESS-related (Start) **********/


#if ! defined (UPGRADE_EXCLUDE_2G)
typedef enum CsrSelectionContextTag
{
    /* The following values indicate whether a reselection GSM->UMTS or
       UMTS->GSM is in progress. CSR_NO_SELECTION_CONTEXT simply indicates
       that none of such reselections is in progress. */

    CSR_NO_SELECTION_CONTEXT,
    CSR_SELECTION_FROM_GSM_TO_UMTS,     /* Normal GSM -> UMTS (re)selection */
    CSR_SELECTION_FROM_UMTS_TO_GSM      /* Normal UMTS -> GSM (re)selection */
    ,
    CSR_SELECTION_FROM_LTE_TO_UMTS,     /* Normal LTE -> UMTS (re)selection */
    CSR_SELECTION_FROM_UMTS_TO_LTE      /* Normal UMTS -> LTE (re)selection */
#if !defined (L1_SUPPORT_GSM_FG_PLMN_SEARCH) && !defined UPGRADE_PLMS /*********** add UPGRADE_PLMS*/
            ,
    CSR_SWITCH_FROM_UMTS_TO_GSM_FGPS /*swtching mode in order to perform FG PLMN search on GSM*/
#endif
#if !defined (L1_SUPPORT_GSM_FG_PLMN_SEARCH) && !defined UPGRADE_PLMS /*********** add*/
/* ********** begin */    
            ,
    CSR_SWITCH_FROM_UMTS_TO_LTE_FGPS
/* ********** end */
#endif/*********** add*/
}
CsrSelectionContext;
#endif

/******** CSR PROCESS-related (End)   **********/
/***********************************************/


/***********************************************/
/******** MONITOR SET-related (Start)   ********/

//ICAT EXPORTED ENUM
typedef enum CsrMonitorSetDeterminationModeTag
{
    CSR_RULES_BASED,
    CSR_ALL_CELLS_IN_SYS_INFO
}
CsrMonitorSetDeterminationMode;

/******** MONITOR SET-related (End)   **********/
/***********************************************/

typedef enum CsrRankedCellsListSearchTypeTag
{
    CSR_GSM_CELLS_BETTER_THAN_ANY_3G_CELLS,
          /* Search for the best ranked GSM cells (best to worse order) that]
             are also better than any other ranked 3G cells. */

    CSR_BEST_GSM_CELLS
         /* Search for best ranked GSM cells (best to worse order) regardless
            of whether they are better or worse than the ranked 3G cells. */

    ,
    CSR_LTE_CELLS_BETTER_THAN_ANY_OTHER_RAT_CELLS
          /* Search for the best ranked LTE cells (best to worse order) that]
             are also better than any other ranked 3G/GSM cells. */
} CsrRankedCellsListSearchType;


typedef enum CsrTasksDoneTag
{
    CSR_NO_TASKS_DONE,
    CSR_TASKS_DONE_CELL_EVALUATED
} CsrTasksDone;


typedef enum CsrCellAccordingToServiceTypeTag
{
    CSR_NO_INFO_AVAILABLE_FOR_CELL,
    CSR_CELL_SUITABLE,
    CSR_CELL_ACCEPTABLE,
    CSR_CELL_NO_SERVICE
}
CsrCellAccordingToServiceType;

typedef enum CsrBarredStatusTag
{
    CSR_NOT_BARRED,
    CSR_BARRED_NO_SERVICE,
    CSR_BARRED_EMERGENCY_ONLY
}
CsrBarredStatus;

typedef enum CsrIntraFreqBarringTypeTag
{
    CSR_NO_INTRA_FREQ_BARRING,
    CSR_INTRA_FREQ_BARRING,
    CSR_INTRA_FREQ_BARRING_MAX_TBARRED_TIMER
}
CsrIntraFreqBarringType;

typedef enum CsrPlmnTypeTag
{
    //The order of the ENUM values should not be changed since it is used in csrrkNewCellBetterThanCurrent()
    CSR_PLMN_UNKNOWN,
    CSR_PLMN_NOT_VALID,
    CSR_PLMN_EQUIVALENT,
    CSR_PLMN_SELECTED
}
CsrPlmnType;

//ICAT EXPORTED ENUM
typedef enum CsrDataBaseTypeTag
{
    CSR_PRIMARY_DATA_BASE,
    CSR_SECONDARY_DATA_BASE,
    CSR_TEMP_DATA_BASE,
    CSR_SIB12_DATA_BASE
}
CsrDataBaseType;

typedef struct CsrLaiTag
{
    Mcc     mcc;
    Mnc     mnc;
    Lac     lac;
}
CsrLai;

typedef struct CsrLastCellUeWasCampedOnTag
{
    UUMTS_CellInformation          servingCellInformation;
}
CsrLastCellUeWasCampedOn;

typedef struct CsrMmInfoTag
{
  /* MM-related info (MM INFO):
    a) UCellIdentity              (sib 3)
    b) Plmn (MCC, MNC)            (MIB, IE CN Information Info (mcc, mnc only))
    c) threeDigitMncEncoding      (MIB)
    d) Access Control Classes     (sib 3)
    e) "NAS SYS INFO" (defined in 25.331) (sib 1, IE CN Information Info)
       i) LAC
      ii) T3212                  (CS domain)
     iii) AttachDeatach Allowed  (CS domain)
      iv) RAC                    (PS domain)
       v) GPRS Network Mode      (PS domain)

    f) Evaluation                (internal to CSR, mapped onto ActivateStatus)
  */

    UCellIdentity                  cellIdentity;
    MultiplePlmnTable              multiplePlmnList;
    Int8                           suitablePlmnBitmap;
    Boolean                        plmnIdentityValid;
    PlmnListEntry                  plmnIdentity;
    Boolean                        primaryPlmnIdentityValid;
    PlmnListEntry                  primaryPlmnIdentity;
    Int16                          barredAccessClasses;
    UCN_CommonSysInfoGSM_MAP       cn_CommonSysInfoGSM_MAP;
    Boolean                        cs_DomainSpecificSysInfoPresent;
    UCS_DomainSpecificSysInfo      cs_DomainSpecificSysInfo;
    Boolean                        ps_DomainSpecificSysInfoPresent;
    UPS_DomainSpecificSysInfo      ps_DomainSpecificSysInfo;
    CsrCellEval                    evaluation;
}
CsrMmInfo;

typedef struct
{
    SignedInt16                    cpich_Ec_N0;
    SignedInt16                    cpich_RSCP;

    /* This RSSI value is used in Serving \ Intra \ Inter \ cells   */
    /* Since all Intra cells have the same RSSI value, it is stored */
    /* only in index [0]                                            */
    SignedInt16                    rssi_13b3;
}
CsrQMeasurement;

typedef struct UT_CRMaxIntTag
{
    T_UT_CRMax tag;
    union
    {
        /* 0 */
        Int8                                               _dummy1_;
        /* 1 */
        UN_CR_T_CRMaxHyst                                  t30;
        /* 2 */
        UN_CR_T_CRMaxHyst                                  t60;
        /* 3 */
        UN_CR_T_CRMaxHyst                                  t120;
        /* 4 */
        UN_CR_T_CRMaxHyst                                  t180;
        /* 5 */
        UN_CR_T_CRMaxHyst                                  t240;
    }
    choice;
}
UT_CRMaxInt;

typedef struct UCellSelectReselectInfoTreselectionScalingTag
{
    Boolean                                            non_HCS_t_CR_MaxPresent;
    UT_CRMaxInt                                        non_HCS_t_CR_Max;
    Boolean                                            speedDependentScalingFactorPresent;
    USpeedDependentScalingFactor                       speedDependentScalingFactor;
    Boolean                                            interFrequencyTreselectionScalingFactorPresent;
    UTreselectionScalingFactor                         interFrequencyTreselectionScalingFactor;
    Boolean                                            interRATTreselectionScalingFactorPresent;
    UTreselectionScalingFactor                         interRATTreselectionScalingFactor;
}
UCellSelectReselectInfoTreselectionScaling;

/* ********** - add begin */
#if defined UPGRADE_CSG1



typedef struct CellCsgParamsTag
{
    Boolean                        isCsg;        //indicated that this cell is Csg Cell (CSG ID is present, CSG Indocator might be or not be present). note: the cell CSG id might not be in WL
    Boolean                        csgIdPresent;           /**< CSG ID presence flag */
    CsgId                          csgId;                  /**< CSG ID */
    Boolean                        csgIndicatorPresent; // indicate that the CSG indicator is present in MIB
    CsgScellCapmingType            csgCampingType; //indicates if we camp on the SCell as a csg cell or not
    Boolean                        csgPscSplitInfoPresent;
    UCSG_PSCSplitInfo              csgPscSplitInfo;
    Boolean                        hnbNamePresent;
    HnbNameStr                     hnbName;
}
CellCsgParams;
#endif //UPGRADE_CSG1
/* ********** - add end */
typedef enum CsrScanTypeTag
{
    CSR_ICS_SEARCH,
    CSR_BG_PLMN_SEARCH,
    CSR_FG_PLMN_SEARCH
/* ********** add begin */
#if defined UPGRADE_CSG1
   ,CSR_BG_CSG_SEARCH,
    CSR_FG_CSG_SEARCH
#endif //UPGRADE_CSG1
/* ********** add end */

}
CsrScanType;

typedef SignedInt16                CsrHCS_dB;
typedef SignedInt16                CsrOffsetS_N;
typedef SignedInt16                CsrPrimaryCPICH_TX_Power;
typedef SignedInt16                CsrQualMin;
typedef SignedInt16                CsrRxlevMin;
typedef SignedInt16                CsrQualMinOffset;
typedef SignedInt16                CsrRxlevMinOffset;
typedef SignedInt16                CsrSearchQual;
typedef SignedInt16                CsrSearchRXLEV;
typedef Int16                      CsrHyst_S;
#if ! defined (UPGRADE_EXCLUDE_2G)
typedef SignedInt16                CsrInterRATCellIndividualOffset;
typedef SignedInt16                CsrCarrierRssi;
#endif
typedef SignedInt16                CsrRsrp;
typedef SignedInt16                CsrRsrq;

typedef struct CsrQualRxlevMinOffsetTag
{
    CsrQualMinOffset                                  q_QualMin_Offset;
    CsrRxlevMinOffset                                 q_RxlevMin_Offset;
}
CsrQualRxlevMinOffset;

typedef struct
{
    CsrQualMin                     q_QualMin;
    CsrRxlevMin                    q_RxlevMin;
    UMaxGsmTxPower                 maxAllowedUL_TX_Power;
    Boolean                        offsetParamsPresent;
    CsrQualRxlevMinOffset          offsetParams;            /* From 25.331 -10.3.2.3*/
#if ! defined (UPGRADE_EXCLUDE_2G)
    UMaxGsmTxPower                 gsmUeMaxTxPower;
#endif
}
CsrCellSelectionInfo;

#if ! defined (UPGRADE_EXCLUDE_2G)
typedef struct
{
    URAT_Identifier                rat_Identifier;
    Boolean                        sSearchRatPresent;
    CsrSearchQual                  s_SearchRAT;
    Boolean                        s_HCS_RATPresent;
    CsrSearchRXLEV                 s_HCS_RAT;
    CsrSearchQual                  s_Limit_SearchRAT;
}
CsrSib3_4RatListElement;

typedef struct
{
    Int8                           numOfRats;
                  /*  NOTE: the following structure is meant to contain the
                      thresholds the UE nedds to apply the measurement rules
                      Given that one inter-RAT technology only (i.e., GSM) is
                      supported by the stack, the array has one element only. */
    CsrSib3_4RatListElement        rats [CSR_MAX_SUPPORTED_OTHER_RATS];
}
CsrRatList;
#endif    /* UPGRADE_EXCLUDE_2G */

typedef struct
{
    Boolean                        s_IntrasearchPresent;
    CsrSearchQual                  s_Intrasearch;
    Boolean                        s_IntersearchPresent;
    CsrSearchQual                  s_Intersearch;
    Boolean                        s_SearchHCSPresent;
    CsrSearchRXLEV                 s_SearchHCS;
#if ! defined (UPGRADE_EXCLUDE_2G)
    CsrRatList                     ratList;
#endif
} CsrNCellSearchInfo;

typedef struct
{
    UHCS_PRIO                      hcs_PRIO;
    CsrHCS_dB                      q_HCS_dB;
    Int8                           t_PenaltyTime;
    UTemporaryOffset1              temporaryOffset1;
    UTemporaryOffset2              temporaryOffset2;
} CsrNCellHcsInfo;

typedef struct
{
    UHCS_PRIO                      hcs_PRIO;
    CsrHCS_dB                      q_HCS_dB;
    Int8                           n_CR;         /* 1..16 */
    Int8                           t_CRMax;      /* 0..240 */
    UT_CRMaxHyst                   t_CRMaxHyst;
} CsrHcsInfo;

typedef struct
{
    T_UCellSelectReselectInfoSIB_3_4_cellSelQMeas  QMeas;

    UT_Reselection_S               t_Reselection_S;
    CsrHyst_S                      q_Hyst_l_S;
    CsrHyst_S                      q_Hyst_2_S;

    UT_Reselection_S_Fine          t_Reselection_S_FACH;
    CsrHyst_S                      q_Hyst_l_S_FACH;
    CsrHyst_S                      q_Hyst_2_S_FACH;

    UT_Reselection_S               t_Reselection_S_PCH;
    CsrHyst_S                      q_Hyst_l_S_PCH;
    CsrHyst_S                      q_Hyst_2_S_PCH;

    CsrNCellSearchInfo             csrNCellSearchInfo;
    CsrHcsInfo                     hcsInfo;
    UCellSelectReselectInfoTreselectionScaling extesnsions5c0;
}
CsrCellReselectionInfo;

typedef struct
{
    UCellBarred                    cellBarred;
    UReservedIndicator             resForOpUse;
    UReservedIndicator             resExtension;
    Int16                          accessControlClass;
}
CsrCellAccessRestrictions;

typedef struct
{
    CsrOffsetS_N                   q_Offset1S_N;
    CsrOffsetS_N                   q_Offset2S_N;
    CsrNCellHcsInfo                hcsInfo;
}
CsrNCellReselectionInfo;

typedef struct CsrIntraFreqNCellInfoTag
{
    CsrCellEval                    evaluation;
    CsrCellFlags                   flags;
    UIntraFreqCellID               intraFreqCellID;
    MultiplePlmnTable              multiplePlmnList;
    Int8                           suitablePlmnBitmap;
    CsrLai                         Lai[MAX_PLMN_IN_CELL];
    UmtsCell                       umtsCellInfo;
    UCellIndividualOffset          cellIndividualOffset;
    Boolean                        primaryCPICH_TX_PowerPresent;
    CsrPrimaryCPICH_TX_Power       primaryCPICH_TX_Power;
    CsrCellAccessRestrictions      accessRestrictions;
    CsrCellSelectionInfo           cellSelectionInfo;
    CsrNCellReselectionInfo        cellReselectionInfo;
    CsrQMeasurement                q_measured;
    Boolean                        detected;
    Boolean                        rankedHigherThanServing;                    //-+ ********** 16-Apr-2012 +-
/* ********** - add begin */
#if defined UPGRADE_CSG1
    Boolean                        isCsg;
    Boolean                        isCsgDetected; //indicates whether this cell is CSG detected cell and is not in tht Ncell list
#endif //UPGRADE_CSG1
/* ********** - add end */
}
CsrIntraFreqNCellInfo;

//ICAT EXPORTED STRUCT
typedef struct CsrIntraFreqNCellListTag
{
    Int8                           numberOfCells;
    CsrIntraFreqNCellInfo          cellInfo[maxCellMeas];
}
CsrIntraFreqNCellList;

typedef struct CsrInterFreqNCellInfoTag
{
    CsrCellEval                    evaluation;
    CsrCellFlags                   flags;
    UInterFreqCellID               interFreqCellID;
    MultiplePlmnTable              multiplePlmnList;
    CsrLai                         Lai[MAX_PLMN_IN_CELL];
    UInterFrequencyCell            interFcellInfo;
    UCellIndividualOffset          cellIndividualOffset;
    Boolean                        primaryCPICH_TX_PowerPresent;
    CsrPrimaryCPICH_TX_Power       primaryCPICH_TX_Power;
    CsrCellAccessRestrictions      accessRestrictions;
    CsrCellSelectionInfo           cellSelectionInfo;
    CsrNCellReselectionInfo        cellReselectionInfo;
    CsrQMeasurement                q_measured;
    Boolean                        rankedHigherThanServing;                    //-+ ********** 16-Apr-2012 +-
    Int8                           priorityIndex;/* index of proirityInfoList */
    Int8                           freqIndex;    /* index of FrequencyList */
/* ********** - add begin */
#if defined UPGRADE_CSG1
    Boolean                        isCsg;
#endif //UPGRADE_CSG1
/* ********** - add end */
}
CsrInterFreqNCellInfo;

//ICAT EXPORTED STRUCT
typedef struct CsrInterFreqNCellListTag
{
    Int8                           numberOfCells;
    CsrInterFreqNCellInfo          cellInfo [maxCellMeas];
}
CsrInterFreqNCellList;

#if ! defined (UPGRADE_EXCLUDE_2G)
/* NOTE: strictly speaking, an inter-RAT cell could currently be of two
         types. A GSM-cell data structure has been hardcoded, because the
         stack is not envisaged ever to support any other technologies. */
typedef struct CsrGsmNCellInfoTag
{
    CsrCellEval                    evaluation;
    CsrCellFlags                   flags;
    UInterRATCellID                interRATCellID;
    MultiplePlmnTable              multiplePlmnList;
    CsrLai                         Lai[MAX_PLMN_IN_CELL];
    UMaxGsmTxPower                 maxAllowedUlTxPower;
    CsrRxlevMin                    q_RxlevMin;
    CsrInterRATCellIndividualOffset  interRatCellIndividualOffset;
    UGsmCell                       gsmCellInfo;
    UFrequency_Band                gsmBandIndicator;
    CsrNCellReselectionInfo        cellReselectionInfo;
    CsrCarrierRssi                 rssiLevel;
    Boolean                        rankedHigherThanServing;                    //-+ ********** 16-Apr-2012 +-
    Int8                           priorityIndex;/* index of proirityInfoList */
    Int8                           freqIndex;    /* index of FrequencyList */
}
CsrGsmNCellInfo;

//ICAT EXPORTED STRUCT
typedef struct CsrGsmNCellListTag
{
    Int8                           numberOfCells;
    CsrGsmNCellInfo                cellInfo [maxCellMeas];
    UFrequency_Band                gsmBandIndicator;
}
CsrGsmNCellList;
#endif /* (UPGRADE_EXCLUDE_2G) */

typedef struct CsrSIB3_4CellInfoTag
{
    CsrCellAccessRestrictions      accessRestrictions;
    CsrCellSelectionInfo           cellSelectionInfo;
    CsrCellReselectionInfo         cellReselectionInfo;
}
CsrSIB3_4CellInfo;

typedef struct CsrServingCellPriorityInfoTag
{
    Boolean                     priorityPresent;
    Int8                        priority;           /* 0 to maxPrio_1 */
    Int8                        s_PrioritySearch1;  /* 0 to 62 by step of 2 */
    /* CQ00052235 begin */
    Boolean                     s_PrioritySearch2Present;
    Int8                        s_PrioritySearch2;  /* 0 to 7 */ 
    /* CQ00052235 end */
    Int8                        threshServingLow;   /* 0 to 62 by step of 2 */
    Boolean                     threshServingLow2Present;
    Int8                        threshServingLow2;  /* 0 to 31 */
}
CsrServingCellPriorityInfo;

typedef struct CsrUtraPriorityInfoTag
{
    UUARFCN                     uarfcn;
    SignedInt8                  qRxLevMinFDD;   /* -119 to -25 by step of 2 */
    Boolean                     threshXhighPresent;
    Int8                        threshXhigh;    /* 0 to 62 by step of 2 */
    Boolean                     threshXlowPresent;
    Int8                        threshXlow;     /* 0 to 62 by step of 2 */
    Boolean                     qQualMinFDDPresent;
    SignedInt16                 qQualMinFDD;  /* -34 to -3 */
}
CsrUtraFddPriorityInfo;

typedef struct CsrUtraFddPriorityInfoListTag
{
    Int8                        numOfFrequencyList;
    CsrUtraFddPriorityInfo      frequencyList[maxNumTDDFreqs];
}
CsrUtraFddPriorityInfoList;

typedef struct CsrltePriorityInfoTag
{
    Earfcn                      earfcn;         //********** change  UEARFCN to Earfcn
    SignedInt16                 qRxLevMinEUTRA; /* -140 to -44 by step of 2 */
    Boolean                     threshXhighPresent;
    Int8                        threshXhigh;    /* 0 to 62 by step of 2 */
    Boolean                     threshXlowPresent;
    Int8                        threshXlow;     /* 0 to 62 by step of 2 */
    Boolean                     qQualMinEUTRAPresent;
    SignedInt16                 qQualMinEUTRA;  /* -34 to -3 */
    Boolean                     threshXhigh2Present;
    Int8                        threshXhigh2;   /* 0 to 31 */
    Boolean                     threshXlow2Present;
    Int8                        threshXlow2;    /* 0 to 31 */
}
CsrltePriorityInfo;

typedef struct CsrltePriorityInfoListTag
{
    Int8                        numOfFrequencyList;
    CsrltePriorityInfo          frequencyList[maxNumEUTRAFreqs];
}
CsrltePriorityInfoList;

typedef struct CsrGsmPriorityInfoTag
{
    UBCCH_ARFCN                 bcch_arfcn;
    UFrequency_Band             gsmBandIndicator;
    SignedInt8                  qRxLevMinGSM;   /* -115 to -56 by step of 2 */
    Boolean                     threshXhighPresent;
    Int8                        threshXhigh;    /* 0 to 62 by step of 2 */
    Boolean                     threshXlowPresent;
    Int8                        threshXlow;     /* 0 to 62 by step of 2 */
}
CsrGsmPriorityInfo;

typedef struct CsrGsmPriorityInfoListTag
{
    Int8                        numOfFrequencyList;
    CsrGsmPriorityInfo          frequencyList[maxCellMeas];
}
CsrGsmPriorityInfoList;

typedef union CsrPriorityLevelRadioAccessTechnologyTag
{
    CsrUtraFddPriorityInfoList  utraFDD;
    CsrltePriorityInfoList      eutra;
    CsrGsmPriorityInfoList      gsm;
}
CsrPriorityLevelRadioAccessTechnology;

typedef struct CsrPriorityLevelTag
{
    Boolean                                 priorityPresent;
    Int8                                    priority;   /* 0 to maxPrio_1 */
    T_UPriorityLevel_radioAccessTechnology  radioAccessTechnologyType;
    CsrPriorityLevelRadioAccessTechnology   radioAccessTechnology;
}
CsrPriorityLevel;

typedef struct CsrPriorityInfoListTag
{
    CsrPriorityStatus                       priorityStatus;
    Boolean                                 utraServingCellPresent;
    CsrServingCellPriorityInfo              utraServingCell;
    Int8                                    numberOfPrioInfoList;
    CsrPriorityLevel                        prioInfoList[maxNumPrio];
    Boolean                                 eutraDetection;
}
CsrPriorityInfoList;

typedef struct CsrEutraCellInfoTag
{
    UEUTRA_PhysicalCellIdentity    physicalCellIdentity;
    CsrCellFlags                   flags;
    Int8                           lteCellID;
    CsrRsrp                        rsrp;    /** 1/8 dBm steps (from -1120 1/8dbm to -352 1/8dBm).    */ /*CQ00075960  modify*/
    CsrRsrq                        rsrq;    /** 1/8 dBm steps ( from -320 1/8dBm to -24 1/8dBm.    */ /*CQ00075960  modify*/
/* ********** - add begin */
#if defined UPGRADE_CSG1
    Earfcn                          earfcn; //this is valid only for reslection due to CSG_SELECT_REQ procedure
    Boolean                        campOnlyIfCellCsg; //used for reselection to LTE. LTE RAT will check this flag to know if needs to camp on this cell or not (after reading SIBs and fetching CSG data)
    Boolean                        isCsg;  //indicated that this is a known CSG cell (known info from its SIBs)
#endif //UPGRADE_CSG1
/* ********** - add end */
}
CsrEutraCellInfo;

typedef struct CsrEutraFreqInfoTag
{
    Earfcn                         earfcn; //********** change  UEARFCN to Earfcn
    UEUTRA_MeasurementBandwidth    measurementBandwidth;
    Int8                           numberOfEutraBlackListedCells;
    UEUTRA_PhysicalCellIdentity    eutraBlackListedCell[maxEUTRACellPerFreq];
    Int8                           priorityIndex;/* index of proirityInfoList */
    Int8                           freqIndex;    /* index of FrequencyList */
    /*Int8                                             numberOfMeasuredCells;*//*CQ00063664 remove*/
    CsrEutraCellInfo               measuredCell[maxEUTRACellPerFreq];
}
CsrEutraFreqInfo;

typedef struct CsrEutraFreqInfoListTag
{
    Int8                           numberOfFreqs;
    CsrEutraFreqInfo               freqInfo[maxNumEUTRAFreqs];
}
CsrEutraFreqInfoList;

/* SIB19 */
typedef union CsrGsmFollowingArfcnsTag
{
    /* 0 */
    UGSM_CellGroup_explicitListOfARFCNs                explicitListOfARFCNs;
    /* 1 */
    UGSM_CellGroup_equallySpacedARFCNs                 equallySpacedARFCNs;
    /* 2 */
    UGSM_CellGroup_followingARFCNs_variableBitMapOfARFCNs_str variableBitMapOfARFCNs;
    /* 3 */
    UGSM_CellGroup_continuousRangeOfARFCNs             continuousRangeOfARFCNs;
}
CsrGsmFollowingArfcns;

typedef struct CsrSib19Tag
{
    UUTRA_PriorityInfoList                             utra_PriorityInfoList;
    Int8                                               numOfFddFreqs;
    UUTRAN_FDD_Frequency                               utranFddFreqs[maxNumFDDFreqs];

    Boolean                                            gsm_PriorityInfoListPresent;
    Int8                                               numOfGsmCellGroup;
    UGSM_PriorityInfo                                  gsm_PriorityInfo[maxNumGSMCellGroup];
    CsrGsmFollowingArfcns                              choice[maxNumGSMCellGroup];

    Boolean                                            eutra_FrequencyAndPriorityInfoListPresent;
    Int8                                               numOfEutraFreq;
    UEUTRA_FrequencyAndPriorityInfo                    eutra_FrequencyAndPriorityInfo[maxNumEUTRAFreqs];

    Boolean                                            v920NonCriticalExtensionsPresent;
    USysInfoType19_v920NonCriticalExtensions           v920NonCriticalExtensions;
    Int8                                               numOfEutraFreq_v920ext;
    UEUTRA_FrequencyAndPriorityInfo_v920ext            eutra_FrequencyAndPriorityInfo_v920ext[maxNumEUTRAFreqs];
}
CsrSib19;

//ICAT EXPORTED STRUCT
typedef struct CsrDsacDataTag
{
    /* ********** modified begin */
    //Boolean                multiplePlmnListIncluded;   /* whether IE multiple PLMN list included in MIB */
    //UMultiplePLMN_List_r6  multiplePlmnList;        /* data of  IE multiple PLMN list*/
    Int8    lastReportedCsDomainIsRestrictedBitmap; /* last CS bitmap data sent to MM 
                                                       Each bit represents a PLMN.
                                                       Each bit is set to 1 if access is restricted (barred)
                                                                    or to 0 if access is not restricted (not barred) */
    Int8    lastReportedPsDomainIsRestrictedBitmap; /* last PS bitmap data sent to MM 
                                                       Each bit represents a PLMN.
                                                       Each bit is set to 1 if access is restricted (barred)
                                                                    or to 0 if access is not restricted (not barred) */
    Boolean dsacRestrictionsHaveChanged;            /* indicates DSAC data has changed, RRC need to inform MM */
    Int16   csRestrictionsBitmapArr[MAX_PLMN_IN_CELL]; /* Array of CS bitmaps according to sys info - 
                                                          Each cell in the array represent a differemnt PLMN.
                                                          Each bit is set to 0 if access is barred
                                                                       or to 1 if access is not barred */
    Int16   psRestrictionsBitmapArr[MAX_PLMN_IN_CELL]; /* Array of PS bitmaps according to sys info - 
                                                          Each cell in the array represent a differemnt PLMN
                                                          Each bit is set to 0 if access is barred
                                                                       or to 1 if access is not barred */
    /* ********** modified end */
}
CsrDsacData;


/* **********, added begin */
//ICAT EXPORTED STRUCT
typedef struct CsrPpacDataTag
{
    /* ********** modified begin */
    UPagingPermissionWithAccessControlParameters_pagingResponseRestrictionIndicator         pagingResponseRestrictionIndicatorArr[MAX_PLMN_IN_CELL];
    UPagingPermissionWithAccessControlParameters_locationRegistrationRestrictionIndicator   locationRegistrationRestrictionIndicatorArr[MAX_PLMN_IN_CELL];
    Int16   locationRegistrationRestrictionsBitmapArr[MAX_PLMN_IN_CELL]; /* Array of Location/Registration bitmap (according to sys info)
                                                                            Each cell in the array represent a differemnt PLMN
                                                                            Each bit is set to 0 if access is barred
                                                                                         or to 1 if access is not barred */
    Boolean ppacRestrictionsHaveChanged;                         /* indicates PPAC data has changed, RRC needs to inform MM */

    /* The next parameters indicate last PPAC data sent to MM */
    /* Each bit represents a PLMN.
       Each bit is set to 1 if access is restricted (barred)
                    or to 0 if access is not restricted (not barred) */
    Int8    lastPagingRestrictedOnCsBitmap;
    Int8    lastPagingRestrictedOnPsBitmap;
    Int8    lastLocationRegistrationRestrictedOnCsBitmap;
    Int8    lastLocationRegistrationRestrictedOnPsBitmap;
    /* ********** modified end */
}
CsrPpacData;
/* **********, added end */


//ICAT EXPORTED STRUCT
typedef struct CsrSCellInfoTag
{
    CsrCellEval                    evaluation;
    CsrCellFlags                   flags;
    UCellIdentity                  sib3CellIdentity;
    UCellIdentity                  sib4CellIdentity;
    /* The MIB PLMN is saved for the purpose of special features which need it
    For example: The DSAC feature */
    Plmn                           mibPlmn;
    /* ********** added begin */
    Boolean                        multiplePlmnListIncluded;
    Int8                           indexOfDuplicateMibPlmnInMultipleList; 
    /* ********** added end */
    MultiplePlmnTable              multiplePlmnList;
    Int8                           suitablePlmnBitmap;
    Boolean                        mncHasThreeDigits;
    Boolean                        cs_DomainSpecificSysInfoPresent;
    UCS_DomainSpecificSysInfo      cs_DomainSpecificSysInfo;
    Int16                          lac;
    Boolean                        ps_DomainSpecificSysInfoPresent;
    UPS_DomainSpecificSysInfo      ps_DomainSpecificSysInfo;
    UUMTS_CellInformation          servingCellInformation;
    Boolean                        readSFN_Indicator;
    Boolean                        primaryCPICH_TX_PowerPresent;
    CsrPrimaryCPICH_TX_Power       primaryCPICH_TX_Power;
    UCellIndividualOffset          cellIndividualOffset;
    Boolean                        txDiversityIndicator;
    Boolean                        referenceTimeDifferenceToCellPresent;
    T_UReferenceTimeDifferenceToCell referenceTimeDifferenceToCellType;
    Int16                          referenceTimeDifferenceToCell;
    Int16                          drxCycleLength;
    Boolean                        sib4presentInDb;
    CsrSIB3_4CellInfo              sib3CellInfo;
    CsrSIB3_4CellInfo              sib4CellInfo;

    Boolean                        sib5aichPresent;
                                   /* Indicates whether IE aichInfo is present
                                      in sib5 */
    Boolean                        sib5pichPresent;
                                   /* Indicates whether IE pichInfo is present
                                      in sib5 */
    /*********** add begin*/
    Boolean                        sib5ContainFach;
                                   /* Indicates whether sib5 contains FACH*/
    Boolean                        sib6ContainFach;
                                   /* Indicates whether sib6 contains FACH*/
    /*********** add end*/
    Boolean                        sib5hasArrived;
                                   /* When handling arrival of both sib5 and
                                      sib6, indicated whether si5 has already
                                      arrived, i.e. before sib6. */
    Boolean                        sib6aichPresent;
                                   /* Indicates whether IE aichInfo is present
                                      in dib6 */
    Boolean                        sib6pichPresent;
                                   /* Indicates whether IE pichInfo is present
                                      in sib6 */
    Boolean                        sib6hasArrived;
                                   /* When handling arrival of both sib5 and
                                      sib6, indicated whether si6 has already
                                      arrived, i.e. before sib5. */
    Boolean                        sib6indicator;
                                   /* Copy of the value of the latest received
                                      sib5 IE "sib6indicator". */

    CsrSysInfoMask                 sibsSchedulingInfoMask;
                                   /* This variable indicates which sibs are
                                     scheduled in the current serving, (if
                                     primary database) or candidate serving
                                     cell (if secondary database). The constants
                                     defined for the "system information masks",
                                     see above in this file, are used to specify
                                     the sib position in the mask.
                                     Bit = 1 if the relevant sib is scheduled.*/
    CsrSysInfoMask                 sibsUpdateSchedulingInfoMask;
                                   /* This variable indicates which sibs have
                                     been updated (i.e., IEs values have
                                     changed) and are therefore read by SIR (and
                                     passed on to CSR) the very next time they
                                     are broadcast in the cell (provided there
                                     are no reception errors. The constants
                                     defined for the "system information masks",
                                     see above in this file, are used to specify
                                     the sib position in the mask.
                                     Bit = 1 if the relevant sib has been
                                     updated. */

    CsrQMeasurement                q_measured;
                                   /* Contains both quality and quantity
                                      measurements for the cell. */

    Boolean                        sib5BandIndicatorPresent;
                                   /* True if sib5BandIndicator is valid. */

    URadioFrequencyBandFDD         sib5BandIndicator;
                                   /* Latest sib5BandIndicator. */

    Boolean                        sib5BandIndicator2Present;
                                   /* True if sib5BandIndicator2 is valid. */

    URadioFrequencyBandFDD2        sib5BandIndicator2;
                                   /* Latest sib5BandIndicator2. */

    Boolean                        sib6BandIndicatorPresent;
                                   /* True if sib6BandIndicator is valid. */

    URadioFrequencyBandFDD         sib6BandIndicator;
                                   /* Latest sib6BandIndicator. */

    Boolean                        sib6BandIndicator2Present;
                                   /* True if sib6BandIndicator2 is valid. */

    URadioFrequencyBandFDD2        sib6BandIndicator2;
                                   /* Latest sib6BandIndicator2. */
    /*DSAC info */
    Boolean                         dsacIeIncluded;
                                    /* DSAC_PARAM from the spec.Used on connected mode*/
    CsrDsacData                     dsacData;
/* ********** - add begin */
#if defined UPGRADE_CSG1
    CellCsgParams                   csgParams;
#endif //UPGRADE_CSG1
/* ********** - add end */

    /* **********, added begin */
	/*PPAC info */
    Boolean                         ppacIeIncluded;
                                    /* PPAC_PARAM from the spec.Used on connected mode*/
    CsrPpacData                     ppacData;
    /* **********, added end */

    Boolean                        sib19presentInDb;
    //UrrSirAllocatedMemList         storedSib19MemList; // ********** add   /*********** remove*/
    CsrSib19                       storedSib19;
                                   /* stored SIB19 */
    /* DMCR DEVELOPMENT begin */
    CsrSysInfoMask                 bchInfoRcvdMask;
    Boolean                        deferredMeasurementStatus;
    UIntraFreqMeasQuantity_FDD_sib3  intraFreqReportingQuantitySib3;
    /* DMCR DEVELOPMENT end */
}
CsrSCellInfo;


//ICAT EXPORTED STRUCT
typedef struct CsrUSysInfoTag
{
    CsrSCellInfo                   s_cellInfo;
    Boolean                        fachMeasOccasionInfoPresent;
    Int8                           fachMeasOccasionCoeff;
    Boolean                        interFreqFDDmeasIndicator;
    Boolean                        interRATmeasIndicatorPresent;
    UFACH_MeasurementOccasionInfo_inter_RAT_meas_ind   interRATmeasIndicators;
    T_UCellSelectReselectInfoSIB_3_4_cellSelQMeas      QMeas;
    Boolean                        sib4indicator;
    Boolean                        sib12indicator;
                                   /* Indicates whether sib12 is broadcast in
                                      the cell. */
    Boolean                        sib12PresentInDb;
    Boolean                        sib11_12read;
                                   /* Set to true upon reception of either
                                      sib11 or sib 12. Used by the CSR database
                                      module to check whether sib11/12 infor-
                                      mation is availabe or not. So far used
                                      for MCR purposes only. */
    Boolean                        hcsUsed;
    Boolean                        hcsUsedBackup;
    Boolean                        mergeSib11Sib12Occured;

    CsrIntraFreqNCellList          intraFreqNCellList;
#if defined (L1_SUPPORT_IDLE_INTRA_DETECT)  /*********** add*/
    CsrIntraFreqNCellList          intraDetectedFreqNCellList;
#endif /*********** add*/
/* ********** - add START*/
#if defined UPGRADE_CSG1
    CsrIntraFreqNCellList          intraFreqDetectedCsgNCellList; //csg intraF Ncells which are not in Ncell list
    CsrInterFreqNCellList          interFreqDetectedCsgNCellList; //csg interF Ncells which are not in Ncell list
#endif // UPGRADE_CSG1
/* ********** - add END*/
    CsrInterFreqNCellList          interFreqNCellList;
#if ! defined (UPGRADE_EXCLUDE_2G)
    CsrGsmNCellList                gsmNCellList;
#endif
    CsrIntraFreqNCellList          intraFreqNCellListBackup;
#if defined (L1_SUPPORT_IDLE_INTRA_DETECT)  /*********** add*/
    CsrIntraFreqNCellList          intraDetectedFreqNCellListBackup;
#endif /*********** add*/
    CsrInterFreqNCellList          interFreqNCellListBackup;
#if ! defined (UPGRADE_EXCLUDE_2G)
    CsrGsmNCellList                gsmNCellListBackup;
#endif
    CsrPriorityInfoList            priorityInfoList;    /* 25331 13.4.15c */
    CsrEutraFreqInfoList           eutraFreqInfoList;
}
CsrUSysInfo;

typedef struct CsrForbiddenLaiListTag
{
    Int16   listSize;
    Int16   nextEntry;
    CsrLai  lai [CSR_MAX_FORBIDDEN_LAI_LIST_LEN];
}
CsrForbiddenLaiList;

typedef struct CsrUeDataTag
{
    CsrForbiddenLaiList  forbiddenLAList;
                         /* This list is the "Forbidden LAs for regional
                            provision of service" list described in TS 23.122,
                            section 3.2. These LAs are those for which
                            registration with the Network has been rejected
                            with cause CAUSE_LA_NOT_ALLOWED, because the
                            Operator wants locally to restric access to the
                            Network. Only cells in the HPLMN or EPLMNs should
                            be affected. */

    CsrForbiddenLaiList  forbiddenNatLAList;
                         /* This list contains LAs for which registration with
                            the Network has been rejected with cause
                            CAUSE_NATIONAL_ROAMING_NOT_ALLOWED or
                            CAUSE_LA_NOT_ALLOWED (see TS 23.122, section 3.1,
                            "Forbidden LAs for roaming"). */


    CsrForbiddenLaiList  forbiddenPlmnList;

    Int16                accessClass;
    Plmn                 hplmn[MAX_NUM_OF_EHPLMN+1];
    Int16                numHplmn;
    UMaxGsmTxPower       maxUETxPower;
#if ! defined (UPGRADE_EXCLUDE_2G)
    UMaxGsmTxPower       maxTxPowerInGsmBand [NUM_BANDS];
                         /* Currently, the following applies:
                            maxTxPowerInGsmBand [0] is for GSM,
                            maxTxPowerInGsmBand [1] is for DCS,
                            maxTxPowerInGsmBand [2] is for PCS. */
#endif
    UMaxGsmTxPower		    maxTxPowerInLte;
    //TdLteBandsMaskForLCR    supportedTddLteBands;//********** removed
    //FddLteBandsMaskFor23G   supportedFddLteBands;//********** removed
    //LteBandsMask            supportedTddLteBandsFromErrc;//********** removed
    //LteBandsMask            supportedFddLteBandsFromErrc;//********** removed
    TddLteBandsMask                   supportedTddLteBands;//********** added
    FddLteBandsMask                   supportedFddLteBands;//********** added
#if defined (UPGRADE_4G_BAND_EXT)
    EutraBandMask                     supportedFddLteBandsExt;
#endif
}
CsrUeData;

typedef struct CsrEquivalentPlmnsTag
{
    Int8                           numberofEquivalentPlmns;
    Plmn                           equivalentPlmns [MAX_EQUIVALENT_PLMNS];
} CsrEquivalentPlmnList;

typedef RrcActReq CsrGmmInfo;


typedef struct CsrSavedInfoFromIntSigSelectCellTag
{
    UUMTS_CellInformation  cellInfo;
    Boolean                reconfigurationWasInProgress;
}
CsrSavedInfoFromIntSigSelectCell;


/********  Pending Request Info (Start)  ********/

typedef enum CsrProcedureTag
{
    CSR_NO_PROCEDURE,
    CSR_PLMN_SEARCH,
    CSR_INITIAL_CELL_SELECTION,
    CSR_PLMN_SEARCH_ABORT,
    CSR_TRIGGER_OOS_RECOVERY,
/* ********** - add begin */
#if defined UPGRADE_CSG1
    CSR_INFORM_NAS_OOS_CONNECTED,
    CSR_CSG_SEARCH,
    CSR_FP_SYS_INFO_SEARCH,
#else //UPGRADE_CSG1
/* ********** - add end */
    CSR_INFORM_NAS_OOS_CONNECTED,
/* ********** - add begin */
#endif //UPGRADE_CSG1
	CSR_ICS_SEARCH_ABORT,
	CSR_ICS_SEARCH_ABORT_BY_IRATSTOP,/************/
	CSR_ICS_INIT_REQ
/* ********** - add end */
}
CsrProcedure;

typedef union CsrRequestTag
{
    RrcPlmnListReq rrcPlmnListReq;
/* ********** add begin */
#if defined UPGRADE_CSG1
	RrcCsgListReq  rrcCsgListReq;
#endif //UPGRADE_CSG1
/* ********** add end */

    RrcActReq      rrcActReq;
	RrcDeactReq    rrcDeactReq;
	ActivationTime activationTime;
}
CsrRequest;

typedef struct CsrPendingRequestTag
{
    CsrProcedure    requestType;
    CsrRequest      request;
    UrrSubProcessId processToReplyTo;
}
CsrPendingRequest;

/********  Pending Request Info (End)  ********/


/********  List Of Frequencies To Search and To Sniff Info (Start)  ********/

typedef Int8 CsrOosStage;

//ICAT EXPORTED STRUCT
typedef struct CsrListOfFddFreqsToSearchTag
{
    Int16                         numberOfFddFrequencies;
    UUMTS_FrequencyListInfo       frequencyListInfo [CSR_MAX_FREQS_TO_SEARCH];
}
CsrListOfFddFreqsToSearch;

typedef struct CsrListOfFddFreqsToSniffTag
{
    Int16                         numberOfFddFrequencies;
    UUMTS_FrequencyListInfo       frequencyListInfo [CSR_MAX_FREQS_TO_SNIFF];
}
CsrListOfFddFreqsToSniff;

/********  List Of Frequencies To Search and To Sniff Info (End)  ********/

//ICAT EXPORTED STRUCT
typedef struct UrrCSRCActionTag
{
    UrrCSRExitCodes exitCode;
    Boolean selectionDueToMoveFromGsmToUmts;
    Boolean selectionTriggeredByRrcSubProcess;
    CsrSource source;
    CsrCellEval cellEval;
    Int32 calledFrom;
}
UrrCSRCAction;

//ICAT EXPORTED STRUCT
typedef struct UrrCSRSActionTag
{
    UrrCSRExitCodes exitCode;
    UrrSmcMode  mode;
    UrrSmcConnectedState state;
    Int32 calledFrom;
}
UrrCSRSAction;

//ICAT EXPORTED STRUCT
typedef struct UrrCSRRActionTag
{
    UrrCSRExitCodes exitCode;
    CsrrReselectorState csrrState;
/* ********** removed - internal PLMN search removed */    
    ActivationTime activationTime;
/* ********** removed */
    Boolean onlyActivatePending;
    Boolean resumeBackground;
    Boolean dontResetCsrrState;
    Int32 calledFrom;
    Boolean restartIcs;
}
UrrCSRRAction;

//ICAT EXPORTED STRUCT
typedef struct UrrCSRPActionTag
{
    UrrCSRExitCodes exitCode;
    CsrPlmnAbortRequestSource abortSource;
    BandMode bandMode;
    Boolean restartCellReselection;
    Int32 calledFrom;
}
UrrCSRPAction;
/* ********** add begin */
#if defined UPGRADE_CSG1
//ICAT EXPORTED STRUCT
typedef struct UrrCSRCSGSActionTag
{
    UrrCSRExitCodes exitCode;
    CsrCsgsAbortRequestSource abortSource;
    BandMode bandMode;
    Boolean restartCellReselection;
    Int32 calledFrom;
}
UrrCSRCSGSAction;
#endif //UPGRADE_CSG1
/* ********** add end */

/*********** START */
#endif
/*********** END */

/******* CSR Timers. **********/

//ICAT EXPORTED ENUM
typedef enum CsrTimerIdentityTag
{
    /* Generic Guard Timer */
    URRC_CSR_GUARD_TIMER      = 0,    /* CSR timer 0 - userValue 0x3000 */

    /* Out Of Service Timers */
    URRC_CSRR_NSERV_TIMER,
    URRC_START_CELL_SELECTION_TIMER,
    URRC_CSR_TIMER_OUT_OF_SERVICE,
    URRC_CSR_START_UNINTERLEAVED_SNIFF,
    URRC_CSR_START_PLMN_SEARCH_UNINTERLEAVED_SNIFF,

    /* Cell Selection/Reselection Timers */
    URRC_CSRR_RESELECTION_TIMER,
    URRC_CSRR_HOLD_OFF_TIMER,
    /* PTK_CQ00239728 begin */
    URRC_SHORT_T_RESELECTION_TIMER,
    /* PTK_CQ00239728 end */
    URRC_CSR_REDIRECTION_TIMER,

    /* Mobility Detector Timers */
    URRC_CSRRM_CR_MAX_TIMER,
    URRC_CSRRM_CR_MAX_HYST_TIMER,

    /* Barred Frequency timers */
    // 12
    URRC_CSR_FREQUENCY_BARRED_TIMER,

    /* INTRA Frequency Timers. */
     // 16
    URRC_CSRR_INTRA_F_BARRED_TIMER =
        URRC_CSR_FREQUENCY_BARRED_TIMER + CSR_NUM_BARRED_FREQUENCIES,
     // 48
    URRC_CSRR_INTRA_F_TN_TIMER     =
        URRC_CSRR_INTRA_F_BARRED_TIMER + maxCellMeas,
    // 80
    URRC_CSRR_INTRA_F_TRESELECTION_TIMER =
        URRC_CSRR_INTRA_F_TN_TIMER + maxCellMeas,

    /* INTER Frequency Timers. */
    // 112
    URRC_CSRR_INTER_F_BARRED_TIMER =
        URRC_CSRR_INTRA_F_TRESELECTION_TIMER + maxCellMeas,
    // 144
    URRC_CSRR_INTER_F_TN_TIMER =
        URRC_CSRR_INTER_F_BARRED_TIMER + maxCellMeas,
    // 176
    URRC_CSRR_INTER_F_TRESELECTION_TIMER =
        URRC_CSRR_INTER_F_TN_TIMER + maxCellMeas,

    /* Serving Cell Barred Timer. */

    // 208
    URRC_CSRR_SCELL_BARRED_TIMER   =
        URRC_CSRR_INTER_F_TRESELECTION_TIMER + maxCellMeas,

#if ! defined (UPGRADE_EXCLUDE_2G)
    /* Inter RAT neighbour cell Timers */
    // 209
    URRC_CSRR_INTER_RAT_BARRED_TIMER,
    // 241
    URRC_CSRR_INTER_RAT_TN_TIMER =
        URRC_CSRR_INTER_RAT_BARRED_TIMER + maxCellMeas,
    // 273
    URRC_CSRR_INTER_RAT_TRESELECTION_TIMER =
        URRC_CSRR_INTER_RAT_TN_TIMER + maxCellMeas,

    /* GSM Timers */
    // 305
/* ********** modify begin */	
#if !defined UPGRADE_PLMS    
    URRC_CSRP_GSM_PLMN_SEARCH_TIMER =
#else
    URRC_CSRP_MRAT_PLMN_SEARCH_TIMER =
#endif
/* ********** modify end */
        URRC_CSRR_INTER_RAT_TRESELECTION_TIMER + maxCellMeas,
     //306
    URRC_CSRTU_T3174_TIMER,
    //307
    URRC_CSRP_GSM_CELLS_FOUND_TIMER,
#endif
    URRC_CSRR_DT_CSFB_READ_SIBS_TIMER,/*********** add*/
    URRC_CSRR_CSFB_SEARCH_TIMER,/*********** add*/

        /* ********** begin */
#if defined (PS_STRESS_TEST)
        // 477 or 346 or 247
        URRC_CSR_CAMPON_MOD_MEAS_TIMER,
#endif
        /* ********** end */
    /* ********** add begin */
#if defined UPGRADE_CSG1
        // 478 OR 347 OR 248
        URRC_CSRCSGS_LTE_CSG_SEARCH_TIMER,
#endif //UPGRADE_CSG1
    /* ********** add end */


    /* LTE neighbour cell Timers */
    /* ********** begin */
    //308
    //URRC_CSRR_LTE_BARRED_TIMER,
    //436
    //URRC_CSRR_LTE_TRESELECTION_TIMER =
    //    URRC_CSRR_LTE_BARRED_TIMER + MAX_LTE_CELL_MEAS,
    
    //308
    URRC_CSRR_LTE_TRESELECTION_TIMER,
    
    //436
    URRC_CSRR_T322_TIMER =
        URRC_CSRR_LTE_TRESELECTION_TIMER + MAX_LTE_CELL_MEAS,
/* ********** modify begin */		
#if !defined UPGRADE_PLMS
    //437
    URRC_CSRP_LTE_PLMN_SEARCH_TIMER,
#else
    URRC_CSRP_MRAT_ABORT_PLMS_TIMER, /* ********** added */	
#endif

/* ********** modify end */
    //438
    URRC_CSR_EUTRA_CELL_BARRED_LIST_TIMER,     /* ********** */

    // 439 or 308 or 209
    URRC_CSRR_FORBIDDEN_TIMER,

    // 443 or 312 or 213
    URRC_CSRR_INTRA_DETECTED_F_TRESELECTION_TIMER =
        URRC_CSRR_FORBIDDEN_TIMER + NUM_REJECTED_UARFCNS,

    /* NOTE: this is not a real timer and must be last in the list! */
    /* ********** begin */

#if !defined (ON_PC)
    // 475 or 344 or 245
    URRC_CSRR_IRAT_PINGPANG_TIMER = 
    /*Merge CQ00029737 begin*/
        URRC_CSRR_INTRA_DETECTED_F_TRESELECTION_TIMER + maxCellMeas,
    /*Merge CQ00029737 end*/
    // 476 or 345 or 246
    URRC_CSRR_IRAT_HOLD_OFF_TIMER,
#endif
    /* ********** end */

/*WARNING: placing timers at this point might overrun one of the intra detected timers*/
    

/*Merge CQ00029737 begin*/  
#if !defined (ON_PC)
    // 476 or 345 or 246   477 or 346 or 247    478 or 347 or 248   
    URRC_CSR_NUM_OF_TIMERS
#else
//********** begin
    // 475 or 344 or 245
    URRC_CSR_NUM_OF_TIMERS = URRC_CSRR_INTRA_DETECTED_F_TRESELECTION_TIMER + maxCellMeas
//********** end
#endif
/*Merge CQ00029737 end*/    
    /* ********** end */
}
CsrTimerIdentity;
/*********** START */
#if !defined ERRC_UNIT_TEST
/*********** END */


typedef struct UrrCsrRejectedUarfcnTag
{
    UUARFCN uarfcn;
    CsrTimerIdentity timerId;
}
UrrCsrRejectedUarfcn;

typedef struct CsrShortOosDuringFachInfoTag
{
    Int32                   pdpDeactivatedTime;
    /*tells RRC the exact time in which last PDP was deactivated.
    This is important for feature in which RRC sends cellUpdate (reEntringServiceArea) in CELL_FACH when
    coming back from short OOS only for T sec after deactivating last PDP*/

    Boolean                 anyPdpCtxActive;
    /*TRUE: there is at least one PDP CTX active at the moment
    FALSE: no active PDP*/

    Boolean                 cellUpdateReqDueToShortOosInFach;
    /*CSR requested CMR to send cellUpdate (reEnteringServiceArea */
}
CsrShortOosDuringFachInfo;

/* ********** begin */
typedef struct UrrCsrEutraCellBarredListTag
{    
    Earfcn          earfcn;//********** change  UEARFCN to Earfcn
    UEUTRA_PhysicalCellIdentity   pci;
    Boolean         flags;               //if already save barred eutra cell this pos - TRUE:saved
    KernelTicks     startBarredTimer;    //Saving timestamp that will barred cell the start time
    KernelTicks     expiredBarredTime;   // Saving timestamp that will barred cell the expired time
}
UrrCsrEutraCellBarredList;
/* ********** end */

typedef struct CsrDataTag
{
    CsrPendingRequest             *pendingRequest_p;
                                /* This variable points to dynamically allocated
                                   memory that is initialised and used at run-
                                   time. It contains the latest pending request
                                   (received by the CSR Controller) that must be
                                   kept pending until other actions, currently
                                   abortion of the current procedure, have been
                                   carried out. */

    CsrProcId                      currentActiveCsrProcess;
                                /* Current active CSR process. */

    CsrUeData                      ueData;
                                /* Contains the UE configuration data sent to
                                   CSR by MM. */

    CsrGmmInfo                     gmmInfo;
                                /* NOTE: MM can send to CSR
                                   a) a "selectedPlmn" in rrcUpdateReq
                                      Depending on the AM_MODE, this PLMN is
                                      stored in gmmInfo.requestedPlmn:
                                      overwriting!
                                   b) a "requestedPlmn" in rrcActReq
                                      This plmn is stored in
                                      gmmInfo.requestedPlmn: overwriting!
                                   CSR always refers to this PLMN as the PLMN
                                   the UE must try to camp on. This PLMN may or
                                   may not be the same as the HPLMN. */

                 /* ICS/PLMN Search parameters. (Start) */

    Boolean                        freqListOnlySearch;
                                /* TRUE: stop searching after having searched
                                         the frequencies that have been
                                         set up in the search list (i.e.
                                         listOfFrequenciesToSearch).
                                   FALSE: may continue to search further (i.e.,
                                          Prime List Search or Whole Band scan).
                                   This variable is used to control ICS
                                   searching. */
    Boolean                        exhaustiveSearch;
                                /* TRUE: Continue searching whole band after having searched
                                         the prime list frequencies.
                                   FALSE: Stop searching after having searched
                                         the prime list frequencies.
                                   This variable is used to control ICS
                                   searching. */

    Boolean                        searchTriggeredByCsr;
                                /* TRUE: ICS or PLMN Search has been triggered
                                         by CSR itself.
                                   FALSE: These searches have been triggered by
                                          MM.
                                   Initialised and used at run-time. */

    Boolean                        doInterleavedSniffing;
                                /* TRUE: ICS or PLMN Search must perform
                                         interleaved sniffing.
                                   FALSE: ICS or PLMN Search must not perform
                                         interleaved sniffing.
                                   Initialised and used at run-time. */

    Int16                           frequenciesScannedSinceLastSniff;
                                /* Number of frequencies scanned since the last
                                   sniff. Initialised and used at run-time. */

    Int16                           frequenciesScannedSinceStartOfSniff;
                                /* Number of frequencies scanned since the start
                                   of the last sniff. Initialised and used at
                                   run-time. */

    CsrListOfFddFreqsToSearch      listOfFrequenciesToSearch;
                                /* This variable is a repository for the list of
                                   frequencies ICS and UMTS PLMN Search need to
                                   scan. The following cases apply:
                                   a) CIL search or any ad hoc search: list of
                                         freqs from CIL. Sniffing may be
                                         interleaved with these frequencies.
                                   b) RFs search: list of Recommended Freqs from
                                         MM. Again, sniffing may be interleaved.
                                   NOTE: for Prime List search and Whole Band
                                         search, no initial repository is
                                         applicable. Min and max of the band are
                                         sent to PHY in the RSSI scan request.*/

    /* ********** added begin */
    UUMTS_FrequencyListInfo        listOfIntraCellsToSearch; 
    Boolean                        intraDetectedActiveInDch;
    /* ********** added end */

    CsrListOfFddFreqsToSniff       listOfFreqsToSniff;
                                /* This variable is a repository for the list of
                                   frequencies to sniff when interleaved sniffing
                                   is used to recover service. */


                 /* ICS/PLMN Search parameters. (End) */

    CsrUSysInfo                    *primaryDataBase_p;
                                /* Pointer to the Primary Database (used for
                                   data relating to the current
                                   serving cell and its neigh-cells. */

    CsrUSysInfo                    *secondaryDataBase_p;
                                /* Pointer to the Secondary Database (used for
                                   data relating to candidate cells and their
                                   neigh-cells). */

    CsrUSysInfo                    *temporaryDataBase_p;
                                /* It's the temporary storage for the sib12 CIL.
                                   It's used upon (re)selection and sys info
                                   update handling. This CIL is merged into the
                                   sib11 one in the SDB. */

    CsrUSysInfo                    *sib12DataBase_p;
                                /* This is the latest sib12 CIL that CSR must
                                   use in CONNECTED. */

    CsrEquivalentPlmnList          equivalentPlmnList;
                                /* List of equivalent PLMNs. */

#if ! defined (UPGRADE_EXCLUDE_2G)
    Boolean                        handOverToUmtsInProgress;
                                /* This variable is set to TRUE upon a
                                   DEACTIVATED -> CELL_DCH transition.
                                   TRUE: the UE is in DCH due to a Handover to UTRAN.
                                   FALSE: otherwise. */

    Boolean                        cellChangeOrderToUmtsInProgress;
                                /* TRUE if a CCOTU is in progress. FALSE otherwise. */

    Boolean                        cellChangeOrderFromUmtsInProgress;
                                /* TRUE if a CCOFU is in progress.
                                   FALSE otherwise. */

    CsrProcId                      activeCsrProcessUponMovingToGsm;
                                /* This variable indicates which CSR process was
                                   running when either a CCOFU request or a
                                   UMTS->GSM reslection request was received
                                   and executed. */

    CsrSelectionContext            selectionContext;
                                /* See the relevant data type declaration above. */

#endif

    CsrMmInfo                mmInfo;
                          /* This variable is used to keep track of the
                             information that is of interest to MM (logical
                             connection to the Network).
                             In RRC IDLE, it is always kept in synch with the
                             identical info in the Primary Database.
                             In RRC CONNECTED, it is updated only upon reception
                             of IE CN INFORMATION INFO or IE CN INFORMATION INFO
                             FULL.
                             This information is sent to MM in an RrcActInd,
                             RrcActCnf, RrcCellUpdateInd or RrcSysInfoInd as
                             appropriate. */

    CsrLastCellUeWasCampedOn lastCellTheUeCampedOn;
                          /* This variable contains info relating to the last
                             cell the UE was camped on in mode/state CELL_FACH,
                             CELL_PCH, URA_PCH, IDLE and SUSPENDED. In CELL_DCH
                             and DEACTIVATED, there is no notion of serving
                             cell, so this variable is initialised to an invalid
                             value just before entering these two states.
                             This variable is only used to set the field
                             UrrInternalSignalCampedOnCell.servingCellHasChanged.
                             It is not used for any other purpose. */

    Boolean                  internalSignalNoCellSent;
                          /* This variable is set to TRUE whenever the internal
                             signal URR_INTERNAL_SIGNAL_NO_CELL has been sent -
                             this is whenever CSR is no longer camped on
                             any cell. */

    CsrStoredSearchTable     storedRssiScanResultsTable;
                          /* Stores the RSSI results. */

    Boolean                  rrcUpdateReqMustBeHandled;
                          /* TRUE if an rrcUpdateReq has been received whilst reselecting.
                    FALSE otherwise. */

    Boolean                  simMustBeRegardedAsValid;
                          /* TRUE: the SIM must be regarded as valid and its
                                   data usable.
                             FALSE: the SIM must be regarded as not valid and
                                    its data not usable. NOTE that this applies
                                    regardless of whether the SIM is physically
                                    present or not. This is because there are
                                    instances in which the NAS may declare the
                                    SIM "logically invalid" although the SIM is
                                    in fact physically in the UE.
                             The value of this variable is controlled by two
                             events:
                             a) reception of signal "RrcMsActReq", field
                                "msDataValid",
                             b) reception of signal "RrcUpdateReq", field
                                "simPresent". */

    CsrProcId                csrProcessStartedByMmUponDeactivatedToIdle;

    UUARFCN                  frequencyDisabledUponConnectionRejection;
                          /* This variable contains either
                             a) the latest frequency disabled by CER in
                                conjunction with a connection rejection.
                             b) INVALID_UARFCN, i.e. no frequency present. */

    Boolean                  savedInfoFromIntSigSelectCellIsValid;
                          /* TRUE: values saved from the received
                                   URR_INTERNAL_SIGNAL_SELECT_CELL must be for-
                                   warded to the relevant RRC subprocesses upon
                                   the next camping on a cell.
                             FALSE: otherwise. */

    CsrSavedInfoFromIntSigSelectCell
                             savedInfoFromIntSigSelectCell;
                          /* The following variable contains
                             UrrInternalSignalSelectCell-related information
                             that must be forwarded to RBC and CMR in
                             UrrInternalSignalCampedOnCell for signal
                             reconciliation purposes. It is initalised at run-
                             time whenever necessary. */

                 /* OOS Recovery Parameters. (Start) */

    Boolean                  oosIdleRecoveryApplies;
                          /* TRUE: CSR is in a state in which full service is
                                   no longer available and the relevant RRC IDLE
                                   recovery mechanisms (i.e.
                                   a) Interleaved Sniffing during ICS
                                   b) Interleaved Sniffing during UMTS PLMN S,
                                   c) Interleaved Sniffing during GSM PLMN S,
                                   d) uninterleaved sniffing
                                   apply.
                             FALSE: otherwise. */

    Boolean                  oosConnectedRecoveryApplies;
                          /* TRUE: CSR is in a state in which full service is
                                   no longer available and the relevant CONNECTED
                                   recovery mechanisms (i.e.
                                   a) Interleaved Sniffing during ICS
                                   b) uninterleaved sniffing
                                   apply.
                             FALSE: otherwise. */

    Boolean                  oosConnectedNonDchGsmSearchDone ;
    Boolean                  oosConnectedNonDchUmtsSearchDone ;
                         /* TRUE: during ICS stage in OOS RRC finished full cell search in 2G / 3G  */

    CsrOosStage              oosRecoveryStage;
                          /* Indicates how deep into loss of service CSR is.
                             This information is used to adjust both the
                             frequency of the recovery attempts and their
                             scope. This variable is used for both RRC IDLE and
                             RRC CONNECTED, because these modes are mutually
                             exclusive. See section "OOS/LOS stage
                             configuration" in file urrcsr.c. */

    Boolean				   performLatestCampFreqSearch; /*********** add */ 						

    UUARFCN                  lastFreqUeWasCampedOnBeforeOos;
                          /* Used during OOS (limited service) recovery to expand
                             the search beyond the frequency from the current
                             neighbour cell list and RRC_ACT_REQ to include the
                             frequency on which the UE last had (at least limited)
                             service. */

                     /* OOS Recovery Parameters. (End) */

    UUARFCN                  barredFrequenciesList [CSR_NUM_BARRED_FREQUENCIES];
                          /* List of frequencies that are or should be
                             considered barred */

    Boolean                  startup;
                          /* TRUE: CSR has not yet camped on any cell since it
                                   was activated.
                             FALSE: otherwise, i.e. it has camped either on an
                                    acceptable or suitable cell. */

    Boolean                 powerUpRssiScan;
                            /*  When powering up, until SIM data is read (If exists) , there is not enough time to
                perform RSSI scan after MmrCampReq->RrcActReq on all configured FDD bands and then
                start a find cell sequence. Thus in power up, ONLY the last used band is scanned.*/

    Boolean                 sirIsActive;
                          /* Flag to signal to CSR that SIR has finished re-
                             reading the SIBS after BACK_ON_CELL _OR_ that SIR
                             has distributed all SIBS after a CAMPED_ON_CELL.
                             Used to pend requests from MM until RRC as a whole
                             has had the opportuinity to finish the (re-)
                             selection.*/

    Int8                    CsrProcTimerBitmap;
                          /* This is a flag bitmap which saves the CSR Proc that
                                opened a Guard Timer, so that when the timer expires
                                if the current active CSR proc is the one who opened
                                the guard timer, only then we will call for
                                timer expiry*/
                         /* 7 downto 0 - [][][][][CSRC][CSRS][CSRP][CSRR] */
    Boolean                 blockReselectTillRelease;
                    /* This flag is intended to stop the reselection ability
                      * for a period of 500ms or until connection release message
                      * arrives from the network. It is used after Routing Area Reject
                      * the UE gets from the network. */

    SignedInt8              IntraMonitorRequested;
    SignedInt8              InterMonitorRequested;
    SignedInt8              GsmMonitorRequested;
    SignedInt8              LteMonitorRequested;
                    /* Each of this counters is for counting the number of measurement
                     * indication messages received, in order to make the right
                     * decision. This is neccessary to be sure that CSRR make the
                     * reselection decision with whole measurements information */

    UrrCsrRejectedUarfcn            rejectedUarfcn[NUM_REJECTED_UARFCNS];
    Int8                            numRejectedUarfcns;
                    /* This parameter holds the UARFCN of the rejected (routing area update)
                     * UARFCN for maximum of 120s and prevents reselection procedure to
                     * be initiated on cells located on that UARFCN.
                     * Whan timer expires or any cell selection initiated the blocking is removed */

    MultiBand               multiBand;
                            /* Structure representing multiband data. */
    Boolean                 isCsrsStartFrequencyScan;

    Boolean                 ifMeDataReq;

    /* Add the pairs cehcking RAT , INTER , INTRA*/
    Int8                    configuredPair;

    /** mark as TRUE if cell info changed in case of cell info update.
    * next should send cphyMonitorIntraFreqCellReq
    */
    Boolean                 cellInfoChanged;

    GrrRcReselectCause      reselectCause;
    /* If reselection due to redirection occured, set to TRUE. */
    Boolean                 redirectionOccured;
    Boolean                 redirectionGetFindCellInfo;
    Boolean                 redirectionFromLte;   /* TRUE: from LTE;  FALSE from GSM */

    // CSR Redesign
    UrrCSRCAction           csrCSRCAction;
    UrrCSRSAction           csrCSRSAction;
    UrrCSRPAction           csrCSRPAction;
    UrrCSRRAction           csrCSRRAction;
/* ********** add begin */

#if defined UPGRADE_CSG1
	UrrCSRCSGSAction		csrCSRCSGSAction;
#endif //UPGRADE_CSG1
/* ********** add end */

    /* This flag will be set TRUE when handling newUeState, and will be set to FALSE
       after measurements update in CsrrServCellMeasInAmState */
    Boolean                 newUeStateReceived;
    Boolean                 detectedOnNonDch;
    Boolean                 IntegrateMmListInScanOrderTable;
    Int16                   numOfFreqsToScanInPrimeListStage[NUM_FDD_BANDS]; /* Number of UARFCNs to scan in the PRIME_LIST stage */
	Int16                   allNumOfFreqsToScanInPrimeListStage; /* all number of UARFCNs to scan in the PRIME_LIST stage *//*********** add*/
    Int8                    numUarfcnsInFindCellReq; /* Number of UARFCNs inserted into DrxFindCellReq in PRIME_LIST stage */
    /* PTK_CQ00236759 begin */
    Boolean                 icsAbortedForPlmnSearch;
    /* PTK_CQ00236759 end */
    CsrShortOosDuringFachInfo shortOosDuringFachInfo;
    Int8                    numOfConsecutiveFailedPlmnSearchNoPg;
    //as name hints
    //- TRUE in case RRC is OOS && MM was informed about it
    //- FALSE in any other case
    Boolean                 rrcIsOosAndMmInformed;
    Boolean                 hpplmnActReq;   /*TRUE - indicates that ActReq was sent from MM to RRC with HPPLMN reason*/
    Boolean                 readingSibs;    /* CQ00003418 add */
    Boolean                 bcchErrorDuringBcchMod;/* CQ00113171 add */
    
    /* Flag to indicate that CSR is now recovering from DCH failure - Can be Radio link failure or RLC unrecoverable error */ 
    Boolean                 recoveryFromDchFailure;                   /* for CQ00020620 PTK_CQ00236875 */

	Boolean                 bufferedFroDmcrReading; /*CQ00084399 add*/

    /* ********** added begin */
    /***********************************************************************************************/
    //regarding the following 3 parameters please see the following details:
    //1. it is used when NSERV expires during connection establishment
    //2. CER will then be requested by CSR to abort connection establishment ASAP.
    //3. when done it will inform RRC and tell RRC that it should start ICS when PHY_CONFIG_FINISH arrives (which indicates that connection abortion was fully aborted as FACH/RACH released)
    //4. RRC will then mark special flag that indicates that in this special case it should use limited number of UARFCNs in its MM list
    //5. right after limiting the number of UARFCNs in MM list CSR will mark another flag indicating ICS in 2G should be done after ICS in 3G is finished (which includes ONLY the limited
    //number of UARFCNs)
    /***********************************************************************************************/

    //indicates ICS needs to be started when PHY_CONFIG_FINISH arrives
    // - set to TRUE: upon CER request to set it to TRUE (in case T300 expired and CER would like CSR to start ICS after PHY releases FACH/RACH
    // - used: when PHY_CONFIG_FINISH arrives
    // - set to FALSE: right after it was checed in PHY_CONFIG_FINISH arrives and it was checked
    // - reset to FALSE: START_CELL_SELECTION_TIMER stop (real stop) / START_CELL_SELECTIO_TIMER expiry / start of intra reselection / CAMPED_ON_CELL / BACK_ON_CELL / INIT / CSR_DEACTIVATION
    Boolean                 startIcsUponPhyConfigFinish;

    //indicates that during ICS, MM list stage, number of 3G UARFCNs should be limited.
    // - set to TRUE: STATR_CELL_SELECTION expiry (real expiry / fake expiry)
    // - used: start of ICS in IDLE
    // - set to FALSE: when starting ICS in IDLE, right after it was checked
    // - reset to FALSE: CAMPED_ON_CELL / BACK_ON_CELL / INIT / CSR_DEACTIVATION
    Boolean                 limitNumFddFreqsBefore2gAttempt;

    //force CSRS to have 2G search during ICS after MM list is done
    // - set to TRUE: upon ICS initaition in IDLE, in case we would like to have 2G ICS as well.
    // - used: end of ICS in IDLE in case search was triggered by RRC / update SIR if number of MAX_BAD_CRC PDUsshould be limited to lower number than usual
    // - set to FALSE: upon ICS initaition in IDLE, in case we would not like to have 2G ICS as well /
    // - reset to FALSE: end of ICS in CONNECTED / CAMPED_ON_CELL / BACK_ON_CELL / INIT / CSR_DEACTIVATION
    Boolean                 force2gSearchAfterMmStageDone;
    /* ********** added end */

#if defined (UPGRADE_DSDS)
#if !defined(UPGRADE_COMMON_STORE)
    Boolean       searchSuspendBycsr;          /* for ********** add */
#endif/*UPGRADE_COMMON_STORE*/
    Boolean       oosConnectedSendActIndFlag;  /* ********** add */
    Boolean       beforeReselectCtrlPchState;  /* for ********** */
    Boolean       returnRrcActCnfForSuspend;   /* for ********** */
    Boolean       priorityReselectionFlag;     /* for ********** */
    Boolean       cellSelectionOfResumeFlag;   /* for ********** */
    Boolean       bgPlmnSearchInd;             /* for ********** */
    Boolean       triggerSelect121Flag;        /* ********** add */
#if defined (ENABLE_SEARCH_ABORT_BY_SIM2_DEACT)
    Boolean       abortSearchBySim2Deact;
#endif
#if defined (ENABLE_SEARCH_ABORT_BY_SIM2_SERVICE)
    Boolean       abortSearchBySim2Suspend;
#endif
#endif
    Boolean       abortBackgroundPlmnSearch;   /* for CQ00013136 */
    /* for ********** add Begin */                                
    Boolean                 reselToGsmThresholdValid;
    SignedInt16             fddEcnoThresholdValue;
    SignedInt16             fddRscpThresholdValue;
    /* for ********** add End */ 
    Boolean                 plmnSearchCellSelectTimerExpired;    /* ********** add,********** */

    /*********** begin*/
    /*********** removed Boolean                 isRSCPN120Received;*/
    /*********** end*/
    Boolean                 radioLinkFailureFlag;    /* ********** add */
    UUMTS_CellInformation   enterDCHPreServingCellInformation;    /* ********** */
    
    /* ********** begin */
#if defined (PS_STRESS_TEST)
    Boolean                 camponModMeasTimerExpired;
#endif
    /* ********** end */

    /* ********** begin */
    UrrCsrEutraCellBarredList   eutraCellBarredList[CSR_MAX_EUTRA_CELL_BARRED_LIST];
    /* ********** end */
	
    /* ********** PTK_CQ00304242, added begin */
    // A flag indicating if CBS is forbidden due to PLMS
    Boolean                 cbsForbiddenDueToPlms;
    /* ********** PTK_CQ00304242, added end */
	
    Boolean                  redirectFromLteDeactToIdle;      /* ********** */
    Boolean                  requestStopIratFlag;             /* ********** add */
    Boolean                  nasAbortIratReselect;            /* ********** add */
/* ********** - add start*/	
#if defined UPGRADE_CSG1	
    Boolean                  calledFromNservExpiry;
#endif	
/* ********** - add end*/
    Boolean                  eCall;            /* ********** add */
    Boolean                  cellSelectConfigParamPresent;            /* ********** added */
    RrcSetCellSelectConfigReq cellSelectConfigParam;                  /* ********** added*/
}
CsrData;

typedef struct CsrTimerTag
{
    KiTimer    timer;
    void       (*expiryFn)(CsrTimerIdentity timerId);
}
CsrTimer;

/*********************************
 *  Some CSR data types.
 */
typedef union CsrCellPointerTag
{
    CsrSCellInfo                    *servCell_p;
    CsrIntraFreqNCellInfo           *intraFreqNCell_p;
    CsrInterFreqNCellInfo           *interFreqNCell_p;
#if ! defined (UPGRADE_EXCLUDE_2G)
    CsrGsmNCellInfo                 *gsmNCell_p;
#endif
    CsrEutraCellInfo                *lteNCell_p;
}
CsrCellPointer;

//ICAT EXPORTED ENUM
typedef enum CsrCellTypesTag
{
    CSR_NO_CELL_TYPE,     /* Just initial value, i.e. no type defined yet.*/
    CSR_SERVING_CELL,
#if ! defined (UPGRADE_EXCLUDE_2G)
    CSR_GSM_NEIGH_CELL,
#endif
    CSR_INTRA_FREQ_NEIGH_CELL,
    CSR_INTER_FREQ_NEIGH_CELL
    ,
    CSR_LTE_NEIGH_CELL
}
CsrCellTypes;

typedef struct CsrCellTag
{
    CsrCellTypes    cellType;
    CsrCellPointer  cell_p;
}
CsrCell;

typedef enum CsrQMeasTypeTag
{
    CSR_QMEAS_TYPE_ECNO,
    CSR_QMEAS_TYPE_RSCP,
    CSR_QMEAS_TYPE_GSM
}
CsrQMeasType;

typedef struct CsrrkRankingListElementTag
{
    struct CsrrkRankingListElementTag   *next_p;
    CsrCell                             rankedCell;
    SignedInt16                         rankingValue;
    Int8                                nListCellIndex;
/* ********** - add begin */
#if defined UPGRADE_CSG1
    Boolean                             campOnlyIfCellCsg;
#endif //UPGRADE_CSG1
/* ********** - add end */
}
CsrrkRankingListElement;

/*********************************
 *  Some CSRFU data types.
 */

typedef enum CsrfuValidationOutcomeTag
{
    CSRFU_ACCEPT,
    CSRFU_REJECT,
    CSRFU_IGNORE
}
CsrfuValidationOutcome;

typedef struct CsrcDataTag
{
    Boolean                    selectionIsTriggeredByRrcSubProcess;
                            /* TRUE: selection triggered off by signal
                                     URR_INTERNAL_SIGNAL_SELECT_CELL
                               FALSE: otherwise */

    CsrSource                  source;
    CsrTasksDone               tasksDone;
    CsrCellEval                cellEval;
    CsrSysInfoMask             bchInfoRcvdMask;
    CsrSysInfoMask             bchInfoExpectedMask;
    ActivationTime             requestedActivationTime;

    UPrimaryScramblingCode     requestedPsc;
                            /* It stores the requested psc upon reception of a
                               URR_INTERNAL_SIGNAL_SELECT_CELL. The requested
                               frequency is stored in the secondary database
                               and never changes. The psc of a cell found
                               when scanning a frequency is also stored in the
                               secondary database. The value of the psc stored
                               in the sec. database may therefore change.
                               The psc stored in "requestedPsc" never changes.
                               Upon reception of a
                               URR_INTERNAL_SIGNAL_SELECT_CELL, this value is
                               used to work out whether CSRC is already in the
                               process of servicing the received request. */

    CsrcState                  state;
    /* for ********** add begin */
    #if defined (UPGRADE_DSDS)
     UPrimaryScramblingCode     requestedPscAndIntarPsc[MAX_STORED_SCRAMBLING_CODES];
     Int8                       requestedPscAndIntarPscNum;
    #endif
    /* for ********** add end */
} CsrcData;

/*********** CSRR Data Types ***************/
#if ! defined (UPGRADE_EXCLUDE_2G)
typedef struct CsrrReselectToGsmFallBackInfoTag
{
    BandMode     bandMode;
    Arfcn        gsmArfcn;
    Bsic         bsic;
              /* The three variables above are the "GSM fall-back info": the
                 GSM cell CSR can try to fall back on.
                 NOTE: if gsmArfcn is INVALID_ARFCN, then this info is not
                       applicable and, hence, not to be considered. */

    ActivateMode csLevelOfServiceInGsmPreResel;
              /* Level of service in GSM CS domain before 2G->3G reselection. */

    ActivateMode psLevelOfServiceInGsmPreResel;
              /* Level of service in GSM PS domain before 2G->3G reselection. */
}
CsrrReselectToGsmFallBackInfo;
#endif /* UPGRADE_EXCLUDE_2G */

/* CQ00034151 begin */
// ICAT EXPORTED ENUM
typedef enum gsmRedirectionCellProcessStageTag
{
    REDIRECTION_CELL_NONE,
    REDIRECTION_CELL_FIND,
    REDIRECTION_CELL_FIND_SELECT,
    REDIRECTION_CELL_SELECT
}
gsmRedirectionCellProcessStage;
/* CQ00034151 end */

// ICAT EXPORTED STRUCT
typedef struct GsmReselectionRedirectionInfoTag                                //-+ CQ00179036 10-Oct-2011 +-
{                                                                              //-+ CQ00179036 10-Oct-2011 +-
    UUARFCN                  requestedUArfcn [UMAX_RESEL_NUM_FDD_CELLS];       //-+ CQ00179036 10-Oct-2011 +-
    UPrimaryScramblingCode   requestedPscs [UMAX_RESEL_NUM_FDD_CELLS];         //-+ CQ00179036 10-Oct-2011 +-
    Boolean                  txDiversityIndicator[UMAX_RESEL_NUM_FDD_CELLS];   //-+ CQ00179036 10-Oct-2011 +-
/* ********** - add begin */
#if defined UPGRADE_CSG1
    Boolean                 campOnlyIfCellCsg     [UMAX_RESEL_NUM_FDD_CELLS]; //can only camp on cell if it is csg
    Boolean                 selectedCsgIdValid; /**< when TRUE - selectedCsgId value is valid */
    CsgId              selectedCsgId; /**< Selected CSG ID. all cells marked with campOnlyIfCellCsg must camp on this CSG ID only (user's choice) */
	Boolean             csgManuallySelected;    /**< Selected CSG ID chosen by user */   //********** add 
#endif //UPGRADE_CSG1
/* ********** - add end */
    Int8                     numRequestedPscs;                                 //-+ CQ00179036 10-Oct-2011 +-
    /* CQ00034151 begin */
    Int8                     uarfcnWithPscBitmap;
    Int8                     uarfcnWithoutPscBitmap;
    Int8                     searchIndex;
    gsmRedirectionCellProcessStage    redirectionStage;
    /* CQ00034151 end */
}                                                                              //-+ CQ00179036 10-Oct-2011 +-
GsmReselectionRedirectionInfo;                                                 //-+ CQ00179036 10-Oct-2011 +-
                                                                               //-+ CQ00179036 10-Oct-2011 +-
                                                                               //-+ CQ00179036 10-Oct-2011 +-
                                                                               //-+ CQ00179036 10-Oct-2011 +-


typedef struct CsrrReselectToLteFallBackInfoTag
{
    Earfcn      earfcn; //********** change  UEARFCN to Earfcn
    UEUTRA_PhysicalCellIdentity    physicalCellIdentity;
              /* The three variables above are the "LTE fall-back info": the
                 LTE cell CSR can try to fall back on.
                 NOTE: if earfcn is zero, then this info is not
                       applicable and, hence, not to be considered. */

    ActivateMode psLevelOfServiceInLtePreResel;
              /* Level of service in LTE PS domain before 4G->3G reselection. */
}
CsrrReselectToLteFallBackInfo;

typedef struct IratReselectionRedirectionInfoTag
{
    UUARFCN                  requestedUArfcn [IRAT_MAX_UTRA_CELLINFO];
    UPrimaryScramblingCode   requestedCellParaIds [IRAT_MAX_UTRA_CELLINFO];
/* ********** - add begin */
#if defined UPGRADE_CSG1
    Boolean                 campOnlyIfCellCsg     [UMAX_RESEL_NUM_FDD_CELLS];
    Boolean                 selectedCsgIdValid; /**< when TRUE - selectedCsgId value is valid */
    CsgId              selectedCsgId;      /**< Selected CSG ID. all cells marked with campOnlyIfCellCsg must camp on this CSG ID only (user's choice) */
    Boolean                 manuallySelectedCsg; /**< the selected CSG is manually selected */
#endif //UPGRADE_CSG1
/* ********** - add end */
    Int8                     numRequestedCells;
}
IratReselectionRedirectionInfo;

// ********** start  	
#if defined(ENABLE_END_OF_DRX_MEAS_IND) 
//ICAT EXPORTED ENUM
typedef enum CsrrWaitForEndOfDrxMeasIndStateTag
{
    CSRR_NO_WAIT_END_OF_DRX,
    CSRR_WAIT_END_OF_DRX_IMMEDIATELY,
    CSRR_WAIT_END_OF_DRX_AFTER_1_MEAS_REQ,
    CSRR_WAIT_END_OF_DRX_AFTER_2_MEAS_REQ
}CsrrWaitForEndOfDrxMeasIndState;
#endif /* ENABLE_END_OF_DRX_MEAS_IND */
// ********** end  

// ICAT EXPORTED STRUCT                                                        //-+ CQ00179036 10-Oct-2011 +-
typedef struct CsrrDataTag
{
    ActivationTime           activationTime;

    Boolean                  csrrSuspendedDueToBckgrndPlmnSearch;

    Boolean                  sib11updateHasArrived;
                          /* This variable is used wrt sys info updates when
                             both sib11 and sib12 have been updated and, hence,
                             CSR expects to receive them.
                             TRUE: sib11 has arrived and has been stored.
                             FALSE: sib11 has not yet arrived. */

    Boolean                  sib12updateHasArrived;
                          /* This variable is used wrt sys info updates when
                             both sib11 and sib12 have been updated and, hence,
                             CSR expects to receive them.
                             TRUE: sib12 has arrived and has been stored.
                             FALSE: sib12 has not yet arrived. */

    CsrrReselectorState      csrrState;
                          /* The current state of the Reselector Finite State
                             machine. */

    CsrrReselectorMode       csrrMode;
                          /* The current reselection mode of the Reselector:
                             pre-camping or post-camping system information
                             acquisition. */

    CsrSysInfoMask           bchInfoExpectedMask;
    CsrSysInfoMask           bchInfoRcvdMask;

    CsrCell                  latestBestRankedCell;
    Int8                     servingCellMeasCount;
    Boolean                  servingCellMeasured;
    SignedInt16                     rscpMeas;

    Boolean                  activeSetSelection;
                          /* This variable, when TRUE, indicates that the
                             reselection in progress is due to the transition
                             of the UE from DCH to IDLE and is therefore based
                             on the information in the active set. */
    Int8                     cellSelectCounter; /* ********** add */

    CellIdMask               interFreqCellIdMask;
                          /* This bitmask indicates which inter-freq. cells from
                             the inter freq. section of the CIL are in the
                             current Monitor Set.
                             Bit x = 1 => cell with cellId x is in MS.
                             Bit x = 0 => cell with cellId x is in MS. */

    CellIdMask               intraFreqCellIdMask;
                          /* This bitmask indicates which intra-freq. cells from
                             the inter freq. section of the CIL are in the
                             current Monitor Set.
                             Bit x = 1 => cell with cellId x is in MS.
                             Bit x = 0 => cell with cellId x is in MS. */

    CellIdMask               intraDetectedFreqCellIdMask;
                          /* This bitmask indicates which intra freq detected cells are active */

#if ! defined (UPGRADE_EXCLUDE_2G)
    CellIdMask               gsmCellIdMask;
                          /* This bitmask indicates which GSM. cells from
                             the inter freq. section of the CIL are in the
                             current Monitor Set.
                             Bit x = 1 => cell with cellId x is in MS.
                             Bit x = 0 => cell with cellId x is in MS. */
#endif /* UPGRADE_EXCLUDE_2G */

    FreqIdMask               lteFreqMask;
                          /* This bitmask indicates which LTE freqs from
                             the freq. section of the CIL are in the
                             current Monitor Set.
                             Bit x = 1 => cell with freqId x is in MS.
                             Bit x = 0 => cell with freqId x is in MS. */

    CellIdMask               savedInterFreqCellIdMask;
                          /* This bitmask indicates which inter-freq. cells from
                             the inter freq. section of the CIL were in the
                             previous Monitor Set.
                             Bit x = 1 => cell with cellId x is in MS.
                             Bit x = 0 => cell with cellId x is in MS. */

    CellIdMask               savedIntraFreqCellIdMask;
                          /* This bitmask indicates which intra-freq. cells from
                             the inter freq. section of the CIL were in the
                             previous Monitor Set.
                             Bit x = 1 => cell with cellId x is in MS.
                             Bit x = 0 => cell with cellId x is in MS. */

#if ! defined (UPGRADE_EXCLUDE_2G)
    CellIdMask               savedGsmCellIdMask;
                          /* This bitmask indicates which GSM. cells from
                             the inter freq. section of the CIL were in the
                             previous Monitor Set.
                             Bit x = 1 => cell with cellId x is in MS.
                             Bit x = 0 => cell with cellId x is in MS. */
#endif /* UPGRADE_EXCLUDE_2G */

    FreqIdMask               savedLteFreqMask;
                          /* This bitmask indicates which LTE freqs from
                             the freq. section of the CIL are in the
                             current Monitor Set.
                             Bit x = 1 => cell with freqId x is in MS.
                             Bit x = 0 => cell with freqId x is in MS. */

#if ! defined (UPGRADE_EXCLUDE_2G)
    GrrRcReselectToUmtsCnf   *grrRcReselectToUmtsCnf_p;
                          /* This variable points to memory allocated upon
                             reception of a GrrRcReselectToUmtsReq. In case of
                             failure to select the FDD cells specified in the
                             GrrRcReselectToUmtsReq, the error fields in the
                             GrrRcReselectToUmtsCnf are set as CSR fails to
                             select these cells. */

    GrrRcReselectToGsmCnf    *grrRcReselectToGsmCnf_p;
                          /* This variable points to a copy of the signal
                             GrrRcReselectToGsmCnf received during a UMTS->GSM
                             reselection failure. This copy is dynamically
                             allocated. */

    Boolean                  grrRcReselectToGsmCnfHasArrived;
                          /* This variable is used in CSRR state
                             CSRR_AWAIT_RESELECTION_TO_GSM, in which more
                             than one signal is expected.
                             TRUE: rrRcReselectToGsmCnf has been received.
                             FALSE: otherwise */

    UrrSmcModeAndState       modeAndStateUponMovingToGsm;
                          /* This variable contains the UE mode and state
                             before a reselection to GSM or a CCOFU. It is
                             used in case of failure to tell RBC in which
                             mode/state the UE was before attempting the
                             move to GSM. */

    Boolean                  cphySuspendIndHasArrived;
                          /* This variable is used in CSRR state
                             CSRR_AWAIT_RESELECTION_TO_GSM, in which more
                             than one signal is expected.
                             TRUE: cphySuspendInd has been received.
                             FALSE: otherwise */

    Boolean                  cphyCellSelectIndHasArrived;
                          /* This variable is used in CSRR state
                             CSRR_AWAIT_RESELECTION_TO_GSM, in which more
                             than one signal is expected.
                             TRUE: cphyCellSelectInd has been received.
                             FALSE: otherwise */

    Boolean                  phyHasResynchedToTheServingCell;
                          /* This variable is used in CSRR state
                             CSRR_AWAIT_RESELECTION_TO_GSM, in which more
                             than one signal is expected.
                             TRUE: cphyCellSelectInd has reported success
                             FALSE: otherwise */

    /*Boolean                  noCellBeforeReselectToGsm; CQ00061686 del */
                          /* add for CQ00003991
                             This variable is used in CSRR state
                             CSRR_AWAIT_RESELECTION_TO_GSM, in which more
                             than one signal is expected.
                             TRUE: CSR in no cell state before reselect to GSM,
                             do not wait cphyCellSelectInd if reselect failed
                             FALSE: otherwise wait cphyCellSelectInd */

    CsrProcId                activeCsrProcessUponMovingToGsm;
                          /* This variable contains the CSR process that was
                             running just before a reselection to GSM or a
                             CCOFU. It is used in case of failure. */

    CsrrReselectToGsmFallBackInfo *reselectToGsmFallBackInfo_p;
                          /* This variable is a pointer to information that,
                             if present, enables CSRR quickly to reselect to a
                             GSM fall-back cell, in certain cases, shortly after
                             a successful 2G->3G reselection. This info is
                             stored upon reception of GrrrcReselectToUmtsReq.
                             Upon completion of a 2G->3G reselection, MM may or
                             may not start the registration in 3G immediately.
                             MM let's CSR know about the outcome by subsequently
                             sending RrcUpdateReqs. At this point, CSR decides
                             whether immediately to reselect to the GSM fall-
                             back cell or not based on the info stored in this
                             variable. */

    GrrRcReselectCause       reselectionToGsmCause;


    Int8                     numberOfTopGsmCells;
    CsrGsmNCellInfo          * arrayOfTopGsmCells [UMAX_OOS_NUM_GSM_CELLS];
                          /* NOTE: UMAX_OOS_NUM__GSM_CELLS is greater than
                             UMAX_RESEL_NUM_GSM_CELLS and it's used on purpose
                             so as to have an array that caters for all cases
                             (namely, reselection due to OOS and all other
                             cases). Stores the cells passed to GRR as it is
                             possible that the reselection timers expired while
                             in 2G. */

    Int8                     cellsInIratReselectToUmtsReq;
    UUARFCN                  requestedUArfcn [UMAX_RESEL_NUM_FDD_CELLS];
                          /* These two varables are used for storing the
                             frequencies, and their number, that GRR passes to
                             CSR in a GrrrcReselectToUmtsReq. */

    GsmReselectionRedirectionInfo gsmReselectionRedirectionInfo;               //-+ CQ00179036 10-Oct-2011 +-
                                                                               //-+ CQ00179036 10-Oct-2011 +-

    Boolean                  reselectionAllowedDuringBgPlmn;
                             /* This definition controls the reselection during BG PLMN search it can be modified
                                using ACAT exported function - CsrrChangeReselectionDuringBgPlmnState */

#endif /* UPGRADE_EXCLUDE_2G */
    Int8                     badCrcOccured;
                            /* 00000000 - No Bad CRC
                               00000001 - SCell Bad CRC
                               00000010 - NCell Bad CRC
                               00000100 - Pre-Camp SysInfo
                               00001000 - Post-Camp SysInfo */
    Boolean                 redirectionInfoValid;
    Boolean                 redirectionTimerExpired;
    UrrReleaseRedirectionInfo redirectionInfo;
    Boolean                   badCrcOnServing;
    Int8                    waitForRrcChangeStateInd; /* bitmask - 0000000* = wait for RRC_CONN_REL_PROCESSED
		000000*0 = servCellHasBeenRanked value
		00000*00 = RRC_CONN_REL_PROCESSED was received
                               Second bit must not be processed if the first one is off. */
    GrrRcReselectToUmtsReq  savedForRedirection;
    CsrIntraFreqNCellInfo   redirectionCell;
    CsrrkRankingListElement *SortedRankedList_p[MAX_NUM_CELL_RANKED];
/* ********** - add begin */
#if defined UPGRADE_CSG1
    CsrrkRankingListElement *SortedPriorityRankedList_p[MAX_NUM_CELL_RANKED];
#endif //UPGRADE_CSG1
/* ********** - add end */
    Boolean                 fordiddenNatLaStartMeas;     /* ********** add */

    Boolean                 sirBackOnCellJustCalled; /*********** add*/

    /* ********** added begin */
    /* A flag indicating that in this ranking cycle serving cell has been ranked */
    Boolean                 servingCellSCriteriaPassed;
    /* ********** added end */
    
    /* ********** added begin */
    /* PTK_CQ00244082: Best Ranked GSM Cell of all cells after 1st ranking */
    CsrrkRankingListElement *gsmCellBestRankedOfAllCells_p;

    /* PTK_CQ00250162: A flag indicating if T-Reslection timer is running a neighbor WB cell */
    Boolean                 tReslectionTimerOnWbRunning;

    /* PTK_CQ00250162: A flag indicating if we are currently delaying reslection to GSM */
    Boolean                 delayingGsmReselction;

    /* PTK_CQ00250162: Timestamp in which we decided to delaying reslection to GSM */
    KernelTicks             delayingGsmReselctionStartTimestamp;
    /* ********** added end */

    IratReselectionCnf       *iratReselectionCnf_p;
    IratReselectionAck       *iratReselectionAck_p;
    Boolean                  iratReselectAckHasArrived;
    CsrrReselectToLteFallBackInfo *reselectToLteFallBackInfo_p;
    IratReselectCause        reselectionToLteCause;
    Int8                     numberOfTopLteCells;
    CsrEutraCellInfo         * arrayOfTopLteCells [IRAT_MAX_RESEL_TARGET_CELL];

    /* RedirectionInfo from LTE */
    IratReselectionRequest   *savedForIratRedirection;
    IratReselectionRedirectionInfo iratReselectionRedirectionInfo;
    Boolean                  absolutePrioInterFreqisUsed;
    Boolean                  redirectionFromDchToIdle;      /* ********** add */
/* ********** - add begin */
#if defined UPGRADE_CSG1

    /**************CSG*******************/
    CsrrkRankingListElement *SortedCsgIntraRankedList_p;    //only 1 candidate
    CsrrkRankingListElement *SortedCsgInterRankedList_p[MAX_CSG_CELLS_PER_AS_SEARCH]; // max is MAX_CSG_CELLS_PER_AS_SEARCH
    CsrrkRankingListElement *SortedCsgEutraRankedList_p[MAX_CSG_CELLS_PER_AS_SEARCH]; // max is MAX_CSG_CELLS_PER_AS_SEARCH
    /**************CSG*******************/
#endif //UPGRADE_CSG1
/* ********** - add end */
// ********** start  	
#if defined(ENABLE_END_OF_DRX_MEAS_IND)    
// ********** modify start
    Int32                    timeForNextLteInd; // time for next LteMeasInd as reported by L1 in DRX state - used to calculate if Treselection is required in certain cases
    KernelTicks              timeStampOfDrxServingCellMeas; // holds the time stamp of the last ServingMeasInd for DRX, if the last one wasn;t for DRX, it holds 0
    Boolean                  endOfDrxRecoveryIsNeeded; // indicates if L1 missed sending EndOfDrxMeasInd and recovery should occur
// ********** modify end	
#endif /* ENABLE_END_OF_DRX_MEAS_IND */
// ********** end

    Boolean                  noAllowReselectionFlag;          /* ********** add */

    /* ********** add begin */
    /* copy of dediated priorities received from NW or during mobility */
    Boolean                        dedicatedPrioPresentInDb;
    IratDedicatedPrio              dedicatedPrio;       
    /* ********** add begin */
}
CsrrData;

#if !defined (UPGRADE_EXCLUDE_2G)
/* This enum described the GSM cell state during the PLMN search */
//ICAT EXPORTED ENUM
typedef enum CsrpGsmCellStateTag
{
    CSRP_GSM_CELL_IDLE,
#if !defined UPGRADE_PLMS /* ********** added */
    CSRP_GSM_CELL_WAIT_BSIC_DECODE,
#endif     /* ********** added */
    CSRP_GSM_CELL_WAIT_SYS_INFO
}
CsrpGsmCellState;

/* ********** modify begin */
#if !defined UPGRADE_PLMS
/* This enum described the GSM PLMN search manager state */
//ICAT EXPORTED ENUM
typedef enum CsrpGsmPlmnSearchManagerStateTag
{
    CSRP_GSM_MGR_IDLE,
    CSRP_GSM_MGR_WAIT_LIST_MEAS_IND,
    CSRP_GSM_MGR_WAIT_CELLS_SCAN,
    CSRP_GSM_MGR_WAIT_PHY_DEACTIVATE_CNF
}
CsrpGsmPlmnSearchManagerState;
#endif
/* ********** modify end */

typedef Int8 CsrpGsmSysInfoMessageTypesMask;

/* This struct described SI2 of the currently scanned GSM cell */
//ICAT EXPORTED STRUCT
typedef struct CsrpGsmPlmnSi2InfoTag
{
    Int8                            numArfcn; /* number of ARFCNs in SI2 BA list */
    Arfcn                           arfcn [MAX_BA_CA_CHANNELS]; /* ARFCNs of the BA list */
    Boolean                         extensionInd;
    Int8                            baInd;
}
CsrpGsmPlmnSi2Info;

/* This struct described parameters of the currently scanned GSM cell */
//ICAT EXPORTED STRUCT
typedef struct CsrpGsmCellInfoTag
{
    CsrpGsmCellState gsmCellState; /* Current state of the GSM Cell FSM (i.e. GSM Cell Manager). */
    Int8 numReceivedGsmSysInfoMsgs; /* Number of BCCH frames received for the currently scanned GSM cell */
    CsrpGsmSysInfoMessageTypesMask  receivedGsmSysInfoMsgsMask; /* Bitmap of the received SI messages for the currently scanned GSM cell */
    Boolean                         waitForSi2; /* Indicates wether UE waits for SI2 of the currently scanned GSM cell */
    CsrpGsmPlmnSi2Info              si2Ba; /* Storage of SI2 of the currently scanned GSM cell */

    /***** NEW *****/
    UBCCH_ARFCN     arfcn;
    GsmRssiLevel    rssiLevel;
    NetworkBand     networkBand;
    Boolean         gprsSupported;
    Boolean         threeDigitMnc;
    Lac             lac;
    Plmn            plmnIdentity;
/*********** add begin*/
#if defined (UPGRADE_PLMS)
	Boolean		    isDcs1800;
#endif/*UPGRADE_PLMS*/
/*********** add end*/
}
CsrpGsmCellInfo;

//ICAT EXPORTED ENUM
typedef enum CsrpGsmStopSearchSourceTag
{
    CSRP_GSM_STOP_ABORT,
    CSRP_GSM_STOP_SUSPEND,
    CSRP_GSM_STOP_NONE
}
CsrpGsmStopSearchSource;

#endif

typedef Int16 CsrpPlmnIndex;

/* ********** modify begin */
typedef struct CsrpSuspendedInfoTag
{
    Boolean valid;
#if !defined UPGRADE_PLMS 
    CsrpState lastState;
#else	
    CsrpPlmsState lastState;
#endif	
    UUARFCN currentUarfcn;
    Boolean lastScannedUarfcnValid;
    UUARFCN lastScannedUarfcn;
    CsrCellTypes latestBestRankedCellType;
    Boolean      plmnSearchTriggeredByCsr;
#if !defined UPGRADE_PLMS
    CsrpLtePlmnSearchStatus  lastLtePlmnSearchStatus;
#endif
}
CsrpSuspendedInfo;
/* ********** modify end */

/* ********** add begin */
#if defined UPGRADE_CSG1
typedef struct CsrcsgsSuspendedInfoTag
{
    Boolean valid;
    CsrcsgsState lastState;
    UUARFCN currentUarfcn;
    Boolean lastScannedUarfcnValid;
    UUARFCN lastScannedUarfcn;
    CsrCellTypes latestBestRankedCellType;
    Boolean      plmnSearchTriggeredByCsr;
    CsrcsgsLteCsgSearchStatus  lastLteCsgSearchStatus;
}
CsrcsgsSuspendedInfo;
#endif //UPGRADE_CSG1
/* ********** add begin */

typedef struct CsrpPlmnInfoTag
{
    struct CsrpPlmnInfoTag         *next_p;
    Boolean                        gprsSupported;
    Boolean                        threeDigitMnc;
    Boolean                        notSuitableForHpplmn; /*this flag indicated wether modified Scriteria do to HPPLMN was met(0) or not(1) - 25.331 5.2.3.1.2*/
    Lac                            lac;
    Plmn                           plmnIdentity;
    CsrQMeasurement                q_measured[MAX_STORED_SRCH_FDD_UARFCN_PER_PLMN];
    Int8                           numUarfcns;
    UUARFCN                        uarfcn_dl[MAX_STORED_SRCH_FDD_UARFCN_PER_PLMN];
    UPrimaryScramblingCode         psc;
#if !defined (UPGRADE_EXCLUDE_2G)
    UBCCH_ARFCN                    arfcn;
    GsmBsic                        bsic;
    GsmRssiLevel                   rssiLevel;
#endif
    NetworkBand                    networkBand;
    /* ********** add begin */
    Int8                           numFreqs;
    Int32                          freq[MAX_STORED_SRCH_FREQ_PER_PLMN];// ********** change Int16 to Int32
    /* ********** add end */
#if defined (UPGRADE_PLMS) //********** begin
    Boolean                        isStrong;
#endif //FG_PMS_URR //********** end
}
CsrpPlmnInfo;

/* ********** add begin */
#if defined UPGRADE_CSG1
typedef struct CsrcsgsCsgInfoTag
{
    struct CsrcsgsCsgInfoTag         *next_p;
    Boolean                        gprsSupported;
    Boolean                        threeDigitMnc;
    Boolean                        notSuitableForHpplmn; /*this flag indicated wether modified Scriteria do to HPPLMN was met(0) or not(1) - 25.331 5.2.3.1.2*/
    Lac                            lac;
    Plmn                           plmnIdentity;
	UCellIdentity				   cellIdentity;
    CsrQMeasurement                q_measured[MAX_STORED_SRCH_FDD_UARFCN_PER_PLMN];
    Int8                           numUarfcns;
    UUARFCN                        uarfcn_dl[MAX_STORED_SRCH_FDD_UARFCN_PER_PLMN];
	Int16						   strongestInFreqBitmap; //Set bit indicates that this CSG is the stronest in corresponding uarfcn_dl index
    UPrimaryScramblingCode         psc;
#if !defined (UPGRADE_EXCLUDE_2G)
    UBCCH_ARFCN                    arfcn;
    GsmBsic                        bsic;
    GsmRssiLevel                   rssiLevel;
#endif
    NetworkBand                    networkBand;
    /* ********** add begin */
    Int8                           numFreqs;
    Int32                          freq[MAX_STORED_SRCH_FREQ_PER_PLMN];// ********** change Int16 to Int32
    /* ********** add end */
	Boolean 				       csgInfoPresent;
	CsgId 						   csgId;
	Boolean 					   hnbNamePresent;
	HnbNameStr 					   hnbName;
	Boolean						   readSib9;

}
CsrcsgsCsgInfo;
#endif //UPGRADE_CSG1
/* ********** add end */

//ICAT EXPORTED STRUCT
typedef struct CsrpPlmnListTag
{
    CsrpPlmnInfo  *head_p;
    CsrpPlmnInfo  *tail_p;
    CsrpPlmnIndex elements;
    CsrpPlmnIndex strongElements;
    SignedInt16   strongLevel;
}
CsrpPlmnList;
/* ********** add begin */
#if defined UPGRADE_CSG1
//ICAT EXPORTED STRUCT
typedef struct CsrcsgsCsgListTag
{
    CsrcsgsCsgInfo  *head_p;
    CsrcsgsCsgInfo  *tail_p;
    CsrpPlmnIndex elements; //total number of elements
    CsrpPlmnIndex strongElements;
    SignedInt16   strongLevel;
}
CsrcsgsCsgList;
#endif //UPGRADE_CSG1
/* ********** add end */


typedef struct CsrpArfcnFoundElementTag
{
    UBCCH_ARFCN  arfcn;
    BandMode     bandIndex;
}
CsrpArfcnFoundElement;

typedef struct CsrpArfcnFoundTableTag
{
    Int8 numOfArfcns;
    CsrpArfcnFoundElement arfcnsList[MAX_ARFCNS_IN_REMOVE_FROM_UMTS_LIST]; /* Created for removing GSM cells from UMTS search */
}
CsrpArfcnFoundTable;


#if !defined (L1_SUPPORT_GSM_FG_PLMN_SEARCH) && !defined UPGRADE_PLMS /*********** add UPGRADE_PLMS*/
typedef struct CsrpFgPlmnSearchSwitchDataTag
{
    UrrSmcModeAndState    modeAndStateBeforeSwitch;
    GrrRcGsmPlmnListCnf    *grrrcGsmPlmnListCnf_p;
/* ********** begin */    
    ErrRcLtePlmnListCnf   *errrcLtePlmnListCnf_p; 
/* ********** end */
}
CsrpFgPlmnSearchSwitchData;
#endif
/* ********** modify begin */
typedef struct CsrpDataTag
{
	/* Variables common to UMTS and GSM PLMN Search. */
    CsrSearchType                   searchType;
    CsrpPlmnInfo                    curPlmnInfo;
    /* Saves multiple plmn list information for current cell we read MIB for */
    MultiplePlmnTable              multiplePlmnList;
    RrcPlmnListReq                 rrcPlmnListReq;
#if defined (UPGRADE_PLMS) //********** begin   
    Boolean                         fgPlmsInProgress;
#endif //UPGRADE_PLMS //********** end

/*********** add begin*/
#if defined (UPGRADE_PLMS)
    Boolean                        plmsAbortedDueToIratSuccefullReslection; /*********** - add */	
    Boolean                        abortByReselection;
	NetworkMode					   lastRatsToSearch;
    Boolean                        ltePlmnResultsPresent;
    Boolean                        umtsPlmnResultsPresent;
    Boolean                        gsmPlmnResultsPresent;
	PlmnListStatus                 savedSearchResult;

    BandMode					   gsmBandsNotScan; 	/**Bitmap for GSM BANDS to Scan */
    FddBandsMask				   fddBandsNotScan; 	/**Bitmap for FDD BANDS to Scan */
    TddLteBandsMask 			   tddLteBandsNotScan;	/**Bitmap for TDD LTE BANDS to Scan */
    FddLteBandsMask 			   fddLteBandsNotScan;	/**Bitmap for FDD LTE BANDS to Scan */
#if defined (UPGRADE_4G_BAND_EXT)
    EutraBandMask                  fddLteBandsExtNotScan;
#endif
	//Boolean                        abortOngoing;  /* ********** removed - not used */
    Boolean                        getSibsIgnoredDueToWaitingForSuspendCnf; /* ********** added */
	Boolean							gsmMultiBcchFromLteInit;  //**********
	Boolean                        abortReqDuringSuspension;
	/*+UCD interference detection parameters*/
	Boolean						    interferenceDetectionStatusPresent;//********** add
	RrInterferenceDetectStatus		interferenceDetectionStatus;//********** add
#endif/*UPGRADE_PLMS*/
/*********** add end*/

    CsrPlmnAbortRequestSource      abortRequestSource;
                            /* This variable is initialised at run-time.
                               It indicates whether the abort request
                               a) comes from within CSRP itself (in GSM:
                                  namely PHY cannot decode GSM BCH due to
                                  lack of time).
                               b) comes from the CSR Controller or
                               c) CSRR. */

    /* Variables specific to UMTS PLMN search */
    UUARFCN                        nextUarfcn_dl;
#if !defined UPGRADE_PLMS 
    CsrpState                     state;
#else	
    CsrpActiveState                activeState;
    CsrpPlmsState                  plmsState;
#endif	
#if !defined (L1_SUPPORT_GSM_FG_PLMN_SEARCH) && !defined UPGRADE_PLMS /*********** add UPGRADE_PLMS*/
    CsrpFgPlmnSearchSwitchData fgPlmnSearchSwitchData;
#endif
    CsrpPlmnList                   umtsPlmnList;
/* ********** modify begin */	
#if !defined UPGRADE_PLMS    
    CsrRssiScanType                rssiScanType;
    CsrRssiScanStage               rssiScanStage;
    Int8                           numCellsSearchedInUarfcn;
    Int8 numOfArfcnsInCphyGsmListBcchReq; /* Number of ARFCNs last requested from lower layers. */
    Int8 numOfArfcnsScannedInCurrentBand; /* Number of ARFCNs scanned in the current GSM band. */
    Int8 numOfArfcnsInCphyGsmListMeasInd; /* Number of ARFCNs last indicated by lower layers. */
#endif
/* ********** modify end */
    PlmnListStatus                 umtsSearchOutcome;
    PlmnListStatus                 gsmSearchOutcome;
                                /* This variable is used to store the search
                                   outcome just before deactivating PHY (in the
                                   cases in which this deactivation is
                                   relevant). The value of this variable is then
                                   used upon deactivation confirmation in the
                                   reply to MM. */
    CsrSysInfoMask                 bchInfoRcvdMask;
    CsrSysInfoMask                 bchInfoExpectedMask;
    CsrpSuspendedInfo          	   suspensionInfo;
/* ********** moved up */
    Boolean                        hpplmnSearchRequested;

    /* Variables specific to GSM PLMN search */
#if !defined (UPGRADE_EXCLUDE_2G)
#if !defined UPGRADE_PLMS 
    CsrpGsmPlmnSearchManagerState gsmPlmnSearchManagerState; /* Current state of the GSM PLMN search manager. */
#endif	
    CsrpGsmCellInfo gsmCellInfo[CPHY_GSM_MULTI_BCCH_DEC_NUM_CELLS]; /* Information of the currently scanned GSM cell */
    GsmArfcnList *gsmArfcnsBitmap_p; /* ARFCNs bitmap that contains the ARFCNs that need to be scanned or being scanned */
    CsrpPlmnList gsmPlmnList; /* List of GSM PLMNs. */
	BandMode supportedGsmBands; /* Supported GSM bands which GSM bands are supported by the UE.
	    NOTE: these bands are the UNION of the logical bands supported by the higher layers of the stack AND the bands that PHY supports.  */
    BandMode bandsScannedSoFar; /* GSM bands scanned so far. */
    BandMode bandsToScan; /* GSM bands to scan during the search. */
    BandMode currentGsmBand; /* Current GSM band. */
/* ********** moved up */
    GsmArfcnList arfcnsFoundListPerPlmn; /* Created for the PLAY feature */
    CsrpArfcnFoundTable arfcnsFoundList; /* Created for removing found GSM cells from the UMTS PLMN search */
#if !defined(UPGRADE_PLMS)//**********
    RrcPlmnListReq rrcPlmnListReqUseAfterFailed; /* Save the request from MM to be used if resuming after PLMN_LIST_FAILED */
    PlmnListStatus plmnSearchLastStatus; /* Saves the last status returned by the plmn search to be used if resuming after PLMN_LIST_FAILED */
#endif//**********
    CphyGsmListMeasInd latestCphyGsmListMeasInd; /* Latest cphyGsmListMeasInd signal */

    /* GSM PLMN Search optimization */
    Int8         numArfcnsToStop;         /* Number of ARFCNs sent to L1 in order to stop BCCH decode on */
    UBCCH_ARFCN  *stopArfcnList_p;        /* List of ARFCNs sent to L1 in order to stop BCCH decode on */
    Boolean      waitForMultiBcchDecodeInd;      /* Whether abort/suspend process can continue or need to wait for ind from L1 */
#if !defined UPGRADE_PLMS 	
    CsrpGsmStopSearchSource gsmStopSearchSource; /* Process requested to stop serach: abort or suspend */
#endif	
/* ********** begin */
#if defined (UPGRADE_DSDS)
    Boolean isAbortForSuspended;
#endif
#endif
/********************* LTE PLMN Manager variables. (Start) *******/
#if !defined UPGRADE_PLMS
    CsrpLtePlmnSearchStatus        ltePlmnSearchState;
                                /* Current state of the LTE PLMN Manager. */
#endif                                
    CphyDrxFindLteCellCnf          *findLteCellCnf_p;
    CsrpPlmnList                   ltePlmnList;
    /* ********** begin */
    //FddLteBandsMaskFor23G          tdlteBandsToScan;//********** removed
    //FddLteBandsMaskFor23G          fddlteBandsToScan;//********** removed
    /* ********** begin */
    TddLteBandsMask          tdlteBandsToScan;//********** added
    FddLteBandsMask          fddlteBandsToScan;//********** added
#if defined (UPGRADE_4G_BAND_EXT)
    EutraBandMask            fddlteBandsExtToScan;
#endif
    /* The LTE PLMN Search Manager sets this
       variable to the LTE band bitmaps it decides
       that it must scan for a 4G PLMN search in
       3G. */
    /* ********** begin                               
    FddLteBandsMaskFor23G          currentLteBand;
    ********** end */
                                /* Indicates which LTE band is currently being
                                   examined. */
#if !defined UPGRADE_PLMS //********** added
    CsrpLtePlmnSearchType          ltePlmnSearchType;
    CsrpLtePlmnSearchType          nowLtePlmnType;      /* TDDLTE and FDDLTE at one time exist */
    Int8                           currentLteBandIndex;
#endif //********** added
    Int8                           lteFreqIndex;
    Int8                           lteCellIndex;
    EutraCell                      lteCell;
    Int8                           badSibBlockCount;
    Boolean                        stopSearch;
/********************* LTE PLMN Manager variables. (End) *********/


#if defined UPGRADE_PLMS
    Boolean                        resumePlmsUponPhyConfigFinish;       /* ********** added */
    SignedInt16                    irrGetUmtsSibsReq_rscp;              /* ********** added */
    SignedInt16                    irrGetUmtsSibsReq_ecno;              /* ********** added */
    Boolean                        sib5Required;                        /* ********** added */
#endif


}
CsrpData;
/* ********** modify end */
/* ********** add begin */
#if defined (UPGRADE_CSG1)
typedef struct CsrCsgsDataTag
{
	/* Variables common to UMTS and GSM PLMN Search. */
    CsrSearchType              searchType;
    CsrcsgsCsgInfo                   curPlmnInfo;
    /* Saves multiple plmn list information for current cell we read MIB for */
    MultiplePlmnTable             multiplePlmnList;
    RrcCsgListReq                 rrcCsgListReq;
    Boolean                       dualModeRrcCsgListReq; /*CQ00072326 - add*/
    Boolean                       shortTimerForScell;//********** modify
    CsrCsgsAbortRequestSource      abortRequestSource;
                            /* This variable is initialised at run-time.
                               It indicates whether the abort request
                               a) comes from within CSRCSGS itself (in GSM:
                                  namely PHY cannot decode GSM BCH due to
                                  lack of time).
                               b) comes from the CSR Controller or
                               c) CSRR. */

    /* Variables specific to UMTS PLMN search */
    UUARFCN                        nextUarfcn_dl;

#if defined (UPGRADE_URR_CSG_USING_PLMS)
    CsrpActiveState                activeState;
    CsrpPlmsState                  plmsState;
#else/*UPGRADE_URR_CSG_USING_PLMS*/
#endif/*UPGRADE_URR_CSG_USING_PLMS*/
CsrcsgsState					 state;

#if !defined (L1_SUPPORT_GSM_FG_PLMN_SEARCH) && !defined UPGRADE_PLMS /*********** add UPGRADE_PLMS*/
    CsrpFgPlmnSearchSwitchData fgPlmnSearchSwitchData;
#endif
    CsrcsgsCsgList                 umtsCsgList;
    CsrRssiScanType                rssiScanType;
    CsrRssiScanStage               rssiScanStage;
    CsgListStatus                 umtsSearchOutcome;
    CsgListStatus                 gsmSearchOutcome;
                                /* This variable is used to store the search
                                   outcome just before deactivating PHY (in the
                                   cases in which this deactivation is
                                   relevant). The value of this variable is then
                                   used upon deactivation confirmation in the
                                   reply to MM. */
    CsrSysInfoMask                 bchInfoRcvdMask;
    CsrSysInfoMask                 bchInfoExpectedMask;
    CsrcsgsSuspendedInfo           suspensionInfo;
    Int8                           numCellsSearchedInUarfcn;
    Boolean                        hpplmnSearchRequested;

    /* Variables specific to GSM PLMN search */
#if !defined (UPGRADE_EXCLUDE_2G)
	// ********** removed: 
    //CsrpGsmPlmnSearchManagerState gsmPlmnSearchManagerState; /* Current state of the GSM PLMN search manager. */
    CsrpGsmCellInfo gsmCellInfo[CPHY_GSM_MULTI_BCCH_DEC_NUM_CELLS]; /* Information of the currently scanned GSM cell */
    GsmArfcnList *gsmArfcnsBitmap_p; /* ARFCNs bitmap that contains the ARFCNs that need to be scanned or being scanned */
    CsrpPlmnList gsmPlmnList; /* List of GSM PLMNs. */
	BandMode supportedGsmBands; /* Supported GSM bands which GSM bands are supported by the UE.
	    NOTE: these bands are the UNION of the logical bands supported by the higher layers of the stack AND the bands that PHY supports.  */
    BandMode bandsScannedSoFar; /* GSM bands scanned so far. */
    BandMode bandsToScan; /* GSM bands to scan during the search. */
    BandMode currentGsmBand; /* Current GSM band. */
    Int8 numOfArfcnsInCphyGsmListBcchReq; /* Number of ARFCNs last requested from lower layers. */
    Int8 numOfArfcnsScannedInCurrentBand; /* Number of ARFCNs scanned in the current GSM band. */
    Int8 numOfArfcnsInCphyGsmListMeasInd; /* Number of ARFCNs last indicated by lower layers. */
    GsmArfcnList arfcnsFoundListPerPlmn; /* Created for the PLAY feature */
    CsrpArfcnFoundTable arfcnsFoundList; /* Created for removing found GSM cells from the UMTS PLMN search */
    RrcCsgListReq rrcCsgListReqUseAfterFailed; /* Save the request from MM to be used if resuming after CSG_LIST_FAILED */
    CsgListStatus csgSearchLastStatus; /* Saves the last status returned by the csg search to be used if resuming after CSG_LIST_FAILED */
    CphyGsmListMeasInd latestCphyGsmListMeasInd; /* Latest cphyGsmListMeasInd signal */

    /* GSM PLMN Search optimization */
    Int8         numArfcnsToStop;         /* Number of ARFCNs sent to L1 in order to stop BCCH decode on */
    UBCCH_ARFCN  *stopArfcnList_p;        /* List of ARFCNs sent to L1 in order to stop BCCH decode on */
    Boolean      waitForMultiBcchDecodeInd;      /* Whether abort/suspend process can continue or need to wait for ind from L1 */
    CsrpGsmStopSearchSource gsmStopSearchSource; /* Process requested to stop serach: abort or suspend */
 /********** - Deleted **********/

/* ********** begin */
#if defined (UPGRADE_DSDS)
    Boolean isAbortForSuspended;
#endif
#endif
/********************* LTE PLMN Manager variables. (Start) *******/
    CsrcsgsLteCsgSearchStatus      lteCsgSearchState;
                                /* Current state of the LTE PLMN Manager. */
    CphyDrxFindLteCellCnf          *findLteCellCnf_p;
    CsrcsgsCsgList                 lteCsgList;
    /* ********** begin */
    //FddLteBandsMaskFor23G          tdlteBandsToScan;//********** removed
    //FddLteBandsMaskFor23G          fddlteBandsToScan;//********** removed
    /* ********** begin */
    TddLteBandsMask          tdlteBandsToScan;//********** added
    FddLteBandsMask          fddlteBandsToScan;//********** added
#if defined (UPGRADE_4G_BAND_EXT)
    EutraBandMask            fddlteBandsExtToScan;
#endif
    /* The LTE PLMN Search Manager sets this
       variable to the LTE band bitmaps it decides
       that it must scan for a 4G PLMN search in
       3G. */
    /* ********** begin                               
    FddLteBandsMaskFor23G          currentLteBand;
    ********** end */
                                /* Indicates which LTE band is currently being
                                   examined. */
    CsrpLtePlmnSearchType          ltePlmnSearchType;
    CsrpLtePlmnSearchType          nowLtePlmnType;      /* TDDLTE and FDDLTE at one time exist */
    Int8                           currentLteBandIndex;
    Int8                           lteFreqIndex;
    Int8                           lteCellIndex;
    EutraCell                      lteCell;
    Int8                           badSibBlockCount;
    Boolean                        stopSearch;
/********************* LTE PLMN Manager variables. (End) *********/

#if defined UPGRADE_URR_CSG_USING_PLMS
    Boolean                        resumePlmsUponPhyConfigFinish;       /* ********** added */
    SignedInt16                    irrGetUmtsSibsReq_rscp;              /* ********** added */
    SignedInt16                    irrGetUmtsSibsReq_ecno;              /* ********** added */
    Boolean                        sib5Required;                        /* ********** added */
#endif

}
CsrCsgsData;
#endif //UPGRADE_CSG1
/* ********** add end */

typedef struct
{
    CsrCellEval             cellEval;
    UUARFCN                 uarfcn_dl;
    UPrimaryScramblingCode  psc;
} CsrsBestCellSoFar;

typedef struct CsrsPlmnRejectElemTag
{
    struct CsrsPlmnRejectElemTag *next_p;
    UUARFCN                      uarfcn_dl;
    UPrimaryScramblingCode       psc;
    CsrCellEval                  cellEval;
} CsrsPlmnRejectElem;

typedef struct CsrsPlmnRejectListTag
{
    CsrsPlmnRejectElem           *first_p;
    CsrsPlmnRejectElem           *last_p;
} CsrsPlmnRejectList;

/* ********** added begin */
//ICAT EXPORTED ENUM
typedef enum CsrsSearchCauseTag
{
    CSRS_SEARCH_CAUSE_DEFAULT,
    CSRS_SEARCH_CAUSE_START_CELL_SELECT_EXPIRY,
    CSRS_SEARCH_CAUSE_CELL_SELECT_REQUESTED_PSC_FAIL,
    CSRS_SEARCH_CAUSE_CELL_SELECT_INVALID_PSC_FAIL,
    CSRS_SEARCH_CAUSE_CELL_SELECT_INVALID_UARFCN,
    CSRS_SEARCH_CAUSE_NO_CANDIDATES_ON_LEAVING_DCH,
    CSRS_SEARCH_CAUSE_NO_CELL_FOUND_ON_LEAVING_DCH,
    CSRS_SEARCH_CAUSE_NO_CELLS_AFTER_INTRA_FREQ_RESEL_FAIL,
    CSRS_SEARCH_CAUSE_NO_CELLS_AFTER_INTER_FREQ_RESEL_FAIL,
    CSRS_SEARCH_CAUSE_NO_CELLS_AFTER_INTER_RAT_RESEL_FAIL,
    CSRS_SEARCH_CAUSE_NO_CELLS_RANKED_NO_RECOVERY_TIMERS,
    CSRS_SEARCH_CAUSE_CONN_NON_DCH_3G_ONLY_CONT,
    CSRS_SEARCH_CAUSE_CONN_NON_DCH_2G_3G_CONT,
    CSRS_SEARCH_CAUSE_RRC_ACT_REQ,
    //CSRS_SEARCH_CAUSE_DCH_FAILURE_MOVE_TO_FACH,
    CSRS_SEARCH_CAUSE_RESTART_ICS_AFTER_CONN_TO_IDLE
}CsrsSearchCause;
/* ********** added end */

//ICAT EXPORTED ENUM
typedef enum CsrsPlmsIcsActivityTypeTag
{
	CSRS_ICS_NO_ACTIVITY,			//not handling any ICS request from PLMS
	CSRS_ICS_EVAL_REQUESTED,		//handling irrIcsEvalReq
	CSRS_ICS_EVAL_ABORT_REQUESTED,  //handling irrIcsEvalAbortReq
	CSRS_ICS_CAMP_REQUESTED,		//handling irrIcsCampingReq
	CSRS_ICS_DEACTIVATING			//deactivating afte ICS completed
	
}CsrsPlmsIcsActivityType;


typedef enum CsrsPlmsIcsStageTypeTag
{
	CSRS_ICS_ACT_REQ_STAGE,			
	CSRS_ICS_IDLE_OOS_STAGE,		
	CSRS_ICS_CONNECTED_OOS_STAGE,  
}CsrsPlmsIcsStageType;


typedef struct CsrsPlmsIcsParamsTag
{
	CsrsPlmsIcsActivityType	plmsIcsActivity;	/*current csrs activity for ICS search handled by PLMS*/	
    Boolean            		allowAnyCellCamp;   /**< When TRUE URRC is allowed to camp on ANY-Cell. When FALSE, only on SUITABLE cells. */
#if defined(URR_MRAT_ICS_SEARCH)
	IrrIcsEvalReq			pendingEvalReq;		//stored IrrIcsEvalReq
#endif
	CsrsPlmsIcsStageType	icsStageType;	
}CsrsPlmsIcsParams;	



typedef struct CsrsDataTag
{
    ActivationTime          activationTime;
    CsrSource               source;
    CsrsSearchStatus        searchStatus;    /* CSRS current FSMachine state. */
    CsrSysInfoMask          bchInfoRcvdMask;
    CsrSysInfoMask          bchInfoExpectedMask;
    UUMTS_FrequencyListInfo curFreqInfo;   /* Freq info for current freq. */
    CsrsBestCellSoFar       bestCellSoFar; /* Cell info for current best cell.*/
    CsrsPlmnRejectList      plmnRejectList;
    CsrRssiScanType         rssiScanType;
                            /* Indicates whether we are scanning a specified
                               set of stored frequencies or sections of
                               supported bands. This is done in accordance with
                               the structure of the signal CphyRssiScanReq. */
    CsrRssiScanStage        rssiScanStage;
                            
    /* ********** added begin */
    CsrsSearchCause         searchCause;
    Boolean                 performIntraSearchLast; 
    Boolean                 intraListScanInProgress;
    Boolean                 detectedNonDchOnIcsStart;
    /* ********** added end */
	CsrsPlmsIcsParams		plmsIcsParams;
} CsrsData;
/* ********** - add begin */
#if defined UPGRADE_CSG1
typedef struct CsrCsgDataTag
{
    /****CSG SELECT params******/
    PlmnCsgId            requestedSelectedCsgId;
    CsrCsgSelectStatus        csrCsgSelectStatus; // csg select FSM
    Int8                      numOfCsgCellsToSelect;
    CsrCell                   csgCells[RR_MAX_CELLS_PER_CSG];
    Int8                      numberOfCsgSortedCells;
    CsrrkRankingListElement  *csgSortedList[RR_MAX_CELLS_PER_CSG];
    /****CSG SELECT params******/

    /****CSG AUTONOMUS SEARCH params****/

    Int8                      numOfStrongestInterFCsgCells;
    CsrInterFreqNCellInfo     strongestInterFCsgCell[MAX_CSG_CELLS_PER_AS_SEARCH];
    Int8                      numOfStrongestEutraCsgCells;
    CsrEutraCellInfo          strongestEutraCsgCell[MAX_CSG_CELLS_PER_AS_SEARCH];
    Boolean                   hcsUsed; //saved HCS state
    Boolean                   resultsFromASValid; //states that that during ranking in ReselectACellifNeccessary we should consider results from AS only and not from Ncell results
	Boolean						csgRemovedFromWl; //********** add
    /****CSG AUTONOMUS SEARCH params****/

   /* IrrCsgListReq             irrCsgListReq;
    IrrFpSysInfoReq           irrFpSysInfoReq;

    CsrSearchType             csgSearchType;
    CsrSearchType             fpSysInfoSearchType;*/
}CsrCsgData;
#endif //UPGRADE_CSG1
/* ********** - add end */

/***************************************************************************
 * Variables
 ***************************************************************************/
#if defined(UPGRADE_DSDSWB)
extern CsrsData  csrsData1[URRC_TASK_NUM];
#define csrsData csrsData1[URRC_DSDS_TASK]
#else/*UPGRADE_DSDSWB*/
extern CsrsData    csrsData;
#endif /* UPGRADE_DSDSWB */


/***************************************************************************
 * Variables
 ***************************************************************************/
#if defined(UPGRADE_DSDSWB)
extern CsrrData  csrrData1[URRC_TASK_NUM];
#define csrrData csrrData1[URRC_DSDS_TASK]
#else/*UPGRADE_DSDSWB*/
extern CsrrData csrrData;
#endif /* UPGRADE_DSDSWB */

/***************************************************************************
 * Variables
 ***************************************************************************/
#if defined(UPGRADE_DSDSWB)
extern CsrcData  csrcData1[URRC_TASK_NUM];
#define csrcData csrcData1[URRC_DSDS_TASK]
#else/*UPGRADE_DSDSWB*/
extern CsrcData csrcData;
#endif /* UPGRADE_DSDSWB */

#if defined(UPGRADE_DSDSWB)
extern CsrpData  csrpData1[URRC_TASK_NUM];
#define csrpData csrpData1[URRC_DSDS_TASK]
#else/*UPGRADE_DSDSWB*/
extern CsrpData csrpData;
#endif /* UPGRADE_DSDSWB */

/* ********** - add begin */
#if defined UPGRADE_CSG1
#if defined(UPGRADE_DSDSWB)
extern CsrCsgData  csrCsgData1[URRC_TASK_NUM];
#define csrCsgData csrCsgData1[URRC_DSDS_TASK]
#else/*UPGRADE_DSDSWB*/
extern CsrCsgData csrCsgData;
#endif /* UPGRADE_DSDSWB */
#endif //UPGRADE_CSG1
/* ********** - add end */
/* ********** add begin */
#if defined UPGRADE_CSG1
#if defined(UPGRADE_DSDSWB)
extern CsrCsgsData  csrCsgsData1[URRC_TASK_NUM];
#define csrCsgsData csrCsgsData1[URRC_DSDS_TASK]
#else/*UPGRADE_DSDSWB*/
extern CsrCsgsData csrCsgsData;
#endif /* UPGRADE_DSDSWB */
#endif //UPGRADE_CSG1
/* ********** add end */

/***************************************************************************
 * Typed Constants
 ***************************************************************************/

/***************************************************************************
 * Global Data declarations
 ***************************************************************************/
#if defined(UPGRADE_DSDSWB)
extern CsrData  csrData1[URRC_TASK_NUM];
#define csrData csrData1[URRC_DSDS_TASK]
#else/*UPGRADE_DSDSWB*/
extern CsrData  csrData;
#endif /* UPGRADE_DSDSWB */

#if defined(UPGRADE_DSDSWB)
extern CsrTimer csrTimers1[URRC_TASK_NUM][URRC_CSR_NUM_OF_TIMERS];
#define csrTimers csrTimers1[URRC_DSDS_TASK]
#else/*UPGRADE_DSDSWB*/
extern CsrTimer csrTimers [URRC_CSR_NUM_OF_TIMERS];
#endif /* UPGRADE_DSDSWB */

#if !defined (UPGRADE_EXCLUDE_2G)
#if defined(UPGRADE_DSDSWB)
extern BandMode    supportedGsmBands1[URRC_TASK_NUM];
#define supportedGsmBandsV supportedGsmBands1[URRC_DSDS_TASK]
#else/*UPGRADE_DSDSWB*/
extern BandMode supportedGsmBands;
#endif /* UPGRADE_DSDSWB */
#endif

/***************************************************************************
 * General Function Prototypes
 ***************************************************************************/
/* CSR functions */

        /* MM INFO SERVICES */

PlmnListEntry *CsrGetSelectedPlmn (CsrSCellInfo * cellInfo_p);

Boolean CsrComparePlmnList  (
                MultiplePlmnTable * srcPlmnList_p,
                MultiplePlmnTable * dstPlmnList_p);

void CsrCopyPlmnList (
                MultiplePlmnTable * dstPlmnList_p,
                MultiplePlmnTable * srcPlmnList_p,
//CQ00065146 start				
                Boolean             doNotCopyDuplicate);
//CQ00065146 end

void CsrSavePlmnList (
                const UPLMN_Identity * const uPlmn_p,
                UMultiplePLMN_List_r6 * multiplePlmn_p,
                MultiplePlmnTable * plmnList_p,
                Int8 * indexOfDuplicateMibPlmnInMultipleList_p); /* ********** */

/* ********** added begin */
void UrrCsrdbCalculateDsacRestrictionsBitmapForNas(
        Int8         *csDomainIsRestrictedBitmapForNas_p,
        Int8         *psDomainIsRestrictedBitmapForNas_p,
        CsrSCellInfo *sCellInfo_p );

void UrrCsrdbCalculatePpacRestrictionsBitmapForNas(
        Int8         *pagingIsRestrictedOnCsBitmapForNas_p, 
        Int8         *pagingIsRestrictedOnPsBitmapForNas_p,
        Int8         *locationRegistrationIsRestrictedOnCsBitmapFoNas_p, 
        Int8         *locationRegistrationIsRestrictedOnPsBitmapFoNas_p,
        CsrSCellInfo *sCellInfo_p,
        Int8         csDomainIsRestrictedBitmap,
        Int8         psDomainIsRestrictedBitmap);

void  CsrdbCheckIfDsacAndPpacRestrictionsHaveChanged(CsrSCellInfo *db_p);
/* ********** added begin */

void  CsrSetMmInfoFromDataInDataBase (
          CsrMmInfo       * const mmInfo_p,
          CsrDataBaseType   const dbType);

void  CsrSendIndToMmBasedOnMmInfoChanges (void);

void  CsrCodeAndSendRrcStatusInd (void);

void  CsrCodeUSysInfoFromCsrSysInfo (USystemInformation * const u_sysInfo_p);

ActivateStatus  CsrBuildAndSendRrcActCnf (Boolean onlyRegFail);    /* ********** add par onlyRegFail */

void CsrHandleRrcActInd (const CsrSourceForDataToSendToMm getDataFrom,
                                    OldSignalType              oldSignalType,//********** modify
                                    Boolean specialBool);//********** modify

void CsrHandleRrcActCnf (void);

void CsrHandleRrcCellUpdateInd (void);

//void  CsrBuildAndSendRrcCellUpdateInd (void);

void  CsrInformMmOfServingCellStatus (
#if ! defined (UPGRADE_EXCLUDE_2G)
          Boolean                   reselectionDueToGsmToUmtsMove,
#endif
          CsrCellEval               cellEval,
          CsrSource                 source,
          Boolean                   activeSetSelection);

Boolean CsrRejectedUarfcnListIsEmpty (void);

Boolean CsrIsUarfcnRejected(UUARFCN uarfcn_dl, Int8 *index_p);

void    CsrResetRejectedUarfcnIfExists (CsrTimerIdentity timerId);
void    CsrResetRejectedUarfcnsList (void);

void  CsrInformMmAndRrcOfServingCellStatus_UpdateEvaluation (CsrCellEval eval);

void  CsrInformMmAndRrcOfServingCellStatus (
#if ! defined (UPGRADE_EXCLUDE_2G)
          Boolean                   reselectionDueToCommandFromGsm,
#endif
          CsrCellEval               cellEval,
          CsrSource                 source,
          Boolean                   activeSetSelection);

        /* EQUIVALENT PLMN LIST SERVICES */
Boolean
      CsrPlmnIsEquivalentPlmn (Plmn * const selectedPlmn_p,
                               Plmn * const plmn_p,
                               Boolean mncHasThreeDigits);

        /* STORED INFO TABLE SERVICES */

#if !defined UPGRADE_PLMS /* ********** added */
void CsrMarkUarfcnsWithGsmCellsAsDontScan (void);
#endif /* ********** added */

void CsrStoreRssiScanAndDetermineUarfcnsToScan (
                                      CsrStoredSearchTable      *storedTable_p,
                                const CsrRssiScanStage          rssiScanStage,
                                const CsrRssiScanType           rssiScanType,
                                const FddBandsMask              phySupportedFddBandsBits,
                                const FddBandIndex              bandIndex,
                                const Int16                     numberOfElements,
                                      UtraRssiReport            *report_p,
                                const Boolean                   freqListOnlySearch,
                                const Boolean                   isDuringResume);

void CsrInitStoredSearchTable (CsrStoredSearchTable *searchTable_p);

void CsrMarkMmListAsUarfcnCandidate (void);

UUARFCN ArfcnToUarfcn (UBCCH_ARFCN arfcn, BandMode band, FddBandIndex *fddBand_p);

void CsrSetUarfcnAsDetected (UUARFCN uarfcn_dl);

void CsrSetUarfcnAsNotDetected (UUARFCN uarfcn_dl);

Boolean CsrIsUarfcnDetected (UUARFCN uarfcn_dl);

UUARFCN
     CsrGetNextUarfcnToSearch (const CsrRssiScanStage rssiScanStage);

    /* FDD MULTIBAND SERVICES */

void CsrUpdateBandRegionFromMcc (const Mcc mcc, UUARFCN  uarfcn_dl);

void CsrUpdateBandRegionFromSib5 (
        const CsrDataBaseType dataBaseTpye);

void CsrUpdateBandFromSib5 (const CsrDataBaseType dataBaseType, UUARFCN oldUarfcnDl, UUARFCN *newUarfcnDl);

Boolean  UrrCsrGetBandIndexFromSchedulingInfo (UUARFCN oldUarfcnDl, Boolean sib5bisScheduled, UUARFCN *newUarfcnDl); /* ********** modified */	

Boolean
     CsrInitBandScan (
        const CsrRssiScanStage rssiScanStage,
        const CsrScanType rssiScanSource);

void CsrSendNextRssiScan (const CsrRssiScanType rssiScanType,
    const CsrRssiScanStage rssiScanStage,
    const CsrSearchType searchType);

void CsrFakeNextRssiScan (const CsrRssiScanType rssiScanType,
                                const   CsrRssiScanStage rssiScanStage,
                                const CsrSearchType searchType,
							    CphyDrxRssiScanCnf *cphyDrxRssiScanCnf_p);

void
     CsrBuildAddtionalFreqScanList (
        CsrListOfFddFreqsToSearch*       list_p,
        const FddBandIndex               bandIndex);

FddBandRegion CsrGetFddBandRegion (void);

Boolean
        CsrIsCellOnSupportedBand (
            const CsrDataBaseType database);

Boolean
        CsrMoveToNextBandToScan (CsrFddBandData *bandData);

        /* PHY INTERFACE SERVICES */

void CsrBuildAndSendRssiScanReq (const CsrRssiScanType           rssiScanType,
                                         const FddBandIndex              fddBandIndex,
                                         const CsrListOfFddFreqsToSearch *fddCellInformation_p,
                                         const ActivationTime            activationTime);

void CsrBuildAndSendDrxRssiScanReq (const CsrRssiScanType               rssiScanType,
                                             const   FddBandIndex               fddBandIndex,
                                             const CsrListOfFddFreqsToSearch  *fddCellInformation_p,
                                             const ActivationTime             activationTime);

void  CsrBuildAndSendCphyFindCellReq (const UUARFCN uarfcn_dl,
	const ActivationTime activationTime,
	const Int8 numberOfPsc,
    UPrimaryScramblingCode *psc,				 /* ********** modified */
    Boolean                 reportDetectedList); /* ********** modified */

void CsrBuildAndSendCphyDrxFindCellReq (
                const CsrRssiScanStage          rssiScanStage,
          const UUARFCN                  uarfcn_dl,
          const ActivationTime           activationTime,
          const Int8                     numberOfPsc,
                UPrimaryScramblingCode * psc);

void  CsrBuildAndSendCphyNextCellReq (void);
/* ********** add begin */
#if defined UPGRADE_CSG1
void  CsrBuildAndSendCphyDrxNextCellReq (void);
#endif
/* ********** add end */

//RP-040110/2286
void UrrCsrrHandleNWCommandedCellSelection(void);

void CsrrCmacRachFailuresAlertInd(void);    /* ********** added*/

void  CsrBuildAndSendCphyCellSelectReq (
          const UUARFCN           uarfcn_dl,
          const ActivationTime    activationTime,
                UmtsCell        * umtsCellToSend_p);

#if !defined (UPGRADE_EXCLUDE_2G)
void  CsrBuildAndSendCphyGetNextGsmBcchReq (void);

#if !defined UPGRADE_PLMS   /* ********** added */
void  CsrBuildAndSendCphyGsmListBcchReq (
          const BandMode              bandMode,
                GsmArfcnList  * const arfcnsList_p,
          const Boolean               measureRssi,
          const Int8                  numCellsToReport);

#endif /* ********** added */
void CsrpHandleCphyGsmMultiBcchDecodeInd (CphyGsmMultiBcchDecodeInd * const cphyGsmMultiBcchDecodeInd);

//void  CsrBuildAndSendCphyGsmListStopReq (void);
#if !defined (L1_SUPPORT_GSM_FG_PLMN_SEARCH) && !defined UPGRADE_PLMS /*********** add UPGRADE_PLMS*/
void CsrBuildAndSendGrrrcGsmPlmnListReq (RrcPlmnListReq * const plmnListReq_p);
CsrSelectionContext CsrGetSelectionContext(void);    /* ********** add */
#endif
#endif

#if !defined (L1_SUPPORT_GSM_FG_PLMN_SEARCH) && !defined UPGRADE_PLMS /*********** add*/
/* ********** begin */
void CsrBuildAndSendErrrcLtePlmnListReq(RrcPlmnListReq * const plmnListReq_p);
/* ********** end */
#endif
        /* DATA CONVERSION SERVICES */

#if ! defined (UPGRADE_EXCLUDE_2G)
Int8  Csr3gBsicToGsmBsic (UBSIC * const threegBsic_p);
#endif

void  CsrHplmnFromImsi (MobileIdElement *imsi_p, Plmn *hplmn_p);

Boolean CsrHplmnMatches (Boolean useRrcEstablishReq,
                                UAreaIdentity *areaIdentityFromNas_p,
                                const CsrDataBaseType database);

Boolean CsrPlmnMatches (Plmn *requestedPlmn, Plmn foundPlmn, Boolean mncHasThreeDigits);

Boolean CsrCompareVplmnMccWithHplmnMcc (UAreaIdentity *areaIdentityFromNas_p);
Boolean CsrCheckIfMccInHplmns (Mcc mcc);

void  UplmnToPlmn (const UPLMN_Identity * const uPlmn_p, Plmn * const plmn_p);

ActivateStatus
      CsrCellEvalToActivateStatus (const CsrCellEval cellEvaluation);

/* PTK_CQ00236868 begin */
void CsrcAbortSearchEvent(void);
/* PTK_CQ00236868 end */


        /* DATA DECODING SERVICES */
CsrHCS_dB
      CsrDecode_Q_Hcs (
          UQ_HCS                                        qHcs, /* CQ00066864 deleted const */
          const T_UCellSelectReselectInfoSIB_3_4_cellSelQMeas QMeas);

void CsrSetHcsUsed(CsrUSysInfo *sysInfoDb_p, Boolean isUsed, Boolean updateBackup,
    Int8 traceIdx);

#if ! defined (UPGRADE_EXCLUDE_2G)
CsrHCS_dB
      CsrDecodeQHcsValueForGsmCells (const UQ_HCS qHcs);
#endif

Int8  CsrDecodePenaltyTimeRSCP (const UPenaltyTime_RSCP * const penaltyTime_p,
                                      UTemporaryOffset1 * const tempOffset_p);

Int8  CsrDecodePenaltyTimeECN0 (const UPenaltyTime_ECN0 * const penaltyTime_p,
                                      UTemporaryOffset1 * const tempOffset1_p,
                                      UTemporaryOffset2 * const tempOffset2_p);

        /* SIR INTERFACE SERVICES */
void  CsrInformSubProcessesCellSelected (
          UUARFCN                uarfcn_dl,
          UPrimaryScramblingCode primaryScramblingCode);

        /* TIMER SERVICES */
void  CsrStartTimer (const CsrTimerIdentity timerId, const FrameTicks duration);

void  CsrStopTimer (const CsrTimerIdentity timerId);

void  CsrStopAllTnTimers (void);

void  CsrStopAllTbarredTimers (void);

void  CsrStopAllTReselectionTimers (void);

void  CsrStopIntraFTreselectionTimers (CellIdMask cellIdMask);

void CsrStopIntraDetectedFTreselectionTimers (CellIdMask cellIdMask);

void  CsrStopInterFTreselectionTimers (CellIdMask cellIdMask);

#if ! defined (UPGRADE_EXCLUDE_2G)
void  CsrStopGsmTreselectionTimers (CellIdMask cellIdMask);
#endif

void  CsrStopIntraFTnTimers (CellIdMask cellIdMask);

void  CsrStopInterFTnTimers (CellIdMask cellIdMask);

#if ! defined (UPGRADE_EXCLUDE_2G)
void  CsrStopGsmTnTimers (CellIdMask cellIdMask);
#endif

void  CsrStopNonPersistentTimers (const Boolean stopTnservTimer);

Boolean
      CsrTimerIsRunning (const CsrTimerIdentity timerId);

CsrTimerIdentity
      CsrGetNeighCellTbarredTimerId (CsrCell * const cell_p);

CsrTimerIdentity
      CsrGetTreselectionTimerId (CsrCell * const cell_p);

        /* LAI LIST MANAGER */
Boolean
      CsrLaiIsInList  (
          CsrLai* const lai,
          CsrForbiddenLaiList* const list);

Boolean
      CsrIsForbiddenLai (CsrLai *lai_p);

Boolean
      CsrIsForbiddenNatLai (CsrLai *lai_p);

Boolean CsrIsForbiddenPlmn (CsrLai *lai_p);

Boolean
      CsrHandleRrcAreaIdentityUpdate (
          UrrInternalSignalRrcUpdateReq * const rrcUpdate_p,
          CsrCellEval                   *       newEvaluation_p);

Boolean
      CsrServCellInForbiddenNatLa (void);

Boolean
      CsrServCellInForbiddenLa (void);

Boolean
      CsrServCellInForbiddenPlmn (void);

Boolean
      CsrRequestedPlmnOfServingCellIsForbidden (Boolean checkPlmn, Boolean checkNatLa);

Boolean
      CsrServCellManualForbidden (void);

Boolean
      CsrLaiMatches (const CsrLai * const src_p, const CsrLai * const dst_p);

        /* BARRED FREQUENCY MANAGER */
Boolean
      CsrFrequencyIsBarred (const UUARFCN freq, Int8 * const freqIndex);

void  CsrBarFrequency (const UUARFCN freq, const UT_Barred duration);

CsrIntraFreqBarringType
      CsrFreqBarred (CsrCellAccessRestrictions *cellAccessRestrictions_p);


        /* CSR INITIALISATION SERVICES */
void  CsrUpdateRequestedPlmn (UrrInternalSignalRrcUpdateReq * const rrcUpdate_p);

void  CsrInitGmmFddInfo (FddCellInformation *fddCellInformation);

void  CsrInitGmmInfo (CsrGmmInfo *gmmInfo_p);

void  CsrSetLastCellTheUeCampedOn (
          const UUARFCN                        uarfcn,
          const UPrimaryScramblingCode         psc);

Boolean  CsrStoreEquivalentPlmns (const Int8 numberOfEPlmns, Plmn * const plmn_p);

        /* SCHEDULING INFO SERVICES */
Boolean
      CsrSystemInfoHasBeenUpdated (
          const CsrSysInfoMask  sysInfoToCheck,
          const CsrDataBaseType dbType);

Boolean
      CsrSystemInfoIsScheduled (
          const CsrSysInfoMask  sysInfoToCheck,
          const CsrDataBaseType dbType);

Boolean
      CsrSchedulingInfoBarsCell (
          UrrInternalSignalIndicateSibScheduling * const sig_p);

Boolean
      CsrReportOnCellOnAllSibsReceived (
          const CsrSysInfoMask         receivedSibs,
          const CsrSysInfoMask         scheduledSibs,
                UT_Barred      * const tBarredDuration);

        /* SIB5 and 6 HANDLING SERVICES */
Boolean
      CsrSib5_6IesMakeCellUnavailable (CsrSCellInfo * const servCellInfo_p);

Boolean
      CsrPichInfoIsPresentInSccpchList (
          const USCCPCH_SystemInformationList * const sccpchList_p);

/*********** add begin*/
Boolean CsrFachIsPresentInSccpchList (
             const USCCPCH_SystemInformationList * const sccpchList_p);
/*********** add end*/

Boolean
      CsrAichInfoIsPresentInPrachList (
          const UPRACH_SystemInformationList * const prachList_p);

Boolean
      CsrSib5MakesTheCellUnavailable (
          const CsrDataBaseType dbType,
          const UrrSmcMode      ueMode);

void  CsrStoreSib5Info (
          const USysInfoType5  * const sib5_p,
          const CsrDataBaseType        dbType);

void  CsrStoreSib6Info (
          const USysInfoType6  * const sib6_p,
          const CsrDataBaseType        dbType);

void  CsrUpdateSib5SysInfoReceptionMasks (
                CsrSysInfoMask * const expectedSysInfoFlags_p,
                CsrSysInfoMask * const receivedSysInfoFlags_p,
          const Boolean                sibIndicator);

void  CsrUpdateSib6SysInfoReceptionMasks (
          CsrSysInfoMask * const receivedSysInfoFlags_p);

Boolean
      CsrSib6MakesTheCellUnavailable (
          const CsrDataBaseType dbType,
          const UrrSmcMode      ueMode);

#if ! defined (UPGRADE_EXCLUDE_2G)
        /* GSM BANDS HANDLING SERVICES */
Boolean
      CsrGsmArfcnIsInCommonDcsPcsRange (const UBCCH_ARFCN arfcn);

Boolean
      CsrGsmArfcnIsInEndPcsEndDcsRange (const UBCCH_ARFCN arfcn);
#endif      /* UPGRADE_EXCLUDE_2G */

        /* CSR CONTROLLER */
void  CsrResumePreCcofuCsrProcedure (
          const UrrSmcConnectedState ueConnectedState);

        /* SIGNAL INTERFACE SERVICES */
void CsrStoreIntSigSelectCellFieldsToBeReturned (
          const UrrInternalSignalSelectCell * const requestedCellInfo_p);

//void  CsrBuildAndSendRrcSysInfoInd (void);

void  CsrStopNCellBchDecode (void);

void CsrSyncHawNCellBchState(void);

#if ! defined (UPGRADE_EXCLUDE_2G)
Boolean
     CsrGrrrcReselToGsmCnfShowsCcofuFailure (const GrrRcErrorCause cause);

void CsrMapGsmCcofuErrorOntoReportableError (
          const GrrRcErrorCause                      gsmErrorCause,
               T_UInterRAT_ChangeFailureCause * const reportableError_p);

void  CsrSendIntSigReselectToGsmFailure (
          const UrrSubProcessId            destinationSubProcess,
                UrrSmcModeAndState * const modeAndState_p);

void  CsrSendIntSigCellSelectInd (
          const UrrSubProcessId destinationSubProcess,
          const Boolean         success);

void  CsrSendGrrRcReselectToUmtsCnf (
          GrrRcReselectToUmtsCnf * const grrRcReselectToUmtsCnf_p);

void  CsrSendUmphReselectToUmtsFailReq (void);

#endif /* UPGRADE_EXCLUDE_2G */

void  CsrSendIntSigReselectToLteFailure (
          const UrrSubProcessId            destinationSubProcess,
                UrrSmcModeAndState * const modeAndStateBeforeMovingToLte_p);
void CsrSendCphLteReselectToUmtsFailReq (void);

        /* IN/OUT OF SERVICE AREA FUNCTIONS */
void  CsrActionsOnLossOfServiceEnded (void);

void  CsrActionsOnOnsetOfOosIdle (const ActivationTime activationTime);

void  CsrActionsOnOutOfServiceAreaDetection (void);

void  CsrDeactivatePhyAndRelatedActions (void);

void  CsrCloseNoCellWindow (void);

void  CsrInformSirOfEndOfNoCellCondition (void);

void  CsrStartIcsAtThisConnectedOosStage (void);

void  CsrStartIcsAtThisIdleOosStage (const ActivationTime activationTime);

#if ! defined (UPGRADE_EXCLUDE_2G)
Boolean
      CsrGsmSearchIsNeededAtThisConnectedOosStage (void);

Int8  CsrMaxNumberOfGsmCellsToScanAtThisOosStage (void);
#endif /* UPGRADE_EXCLUDE_2G */

Int16 CsrGetTuntrlvdSniffDurationAtThisOosConnStage (
          const Boolean gsmSearchJustDone);

void  CsrUpdateLastFreqUeWasCampedOnBeforeOos (void);

        /* EVALUATION MAPPING SERVICES */
Boolean
      CsrCellProvidesNoService (const CsrCellEval eval);

Boolean
      CsrCellServiceTypeIsBetter (
          const CsrCellEval newCell,
          const CsrCellEval bestCell);

CsrCellAccordingToServiceType
      CsrServiceTypeProvidedBy (
          const CsrCell * const thisCell_p);

CsrCellAccordingToServiceType
      CsrMapEvaluationOntoServiceType (
          const CsrCellEval evaluation);

#if ! defined (UPGRADE_EXCLUDE_2G)
ActivateMode
      CsrMapEvaluationOntoActivateMode (const CsrCellEval evaluation);

GrrRcIrrCellStatus
      CsrMapEvaluationOntoGrrRcIrrCellStatus (const CsrCellEval);

void CsrDeallocateGsmFallBackInfo (
                CsrrReselectToGsmFallBackInfo ** const gsmFallBackInfo_p);
#endif

        /* SEARCH CONTROL SERVICES */
#if defined (DEVELOPMENT_VERSION)
void  CsrDisplayListOfFrequenciesToSearch (void);
#endif  /* DEVELOPMENT_VERSION */

#if 0  /* ********** added - internal PLMN search removed */
void  CsrSetUpPlmnSearchSpecificParameters (
          RrcPlmnListReq * const rrcPlmnListReq_p);
#endif /* ********** added */

void  CsrAddFreqToListOfFreqsToSearch (const UUARFCN uarfcn);

Boolean
      CsrDelFreqFromListOfFreqsToSearch (const UUARFCN uarfcn);

void  CsrSetupSearchWhenTriggeredByMm (void);

void  CsrCopyMmPlmnSearchFreqListIntoListOfFreqsToSearch (
          FddUarfcnList             * const mmListOfFreqs_p,
          CsrListOfFddFreqsToSearch * const listOfFrequesToSearch_p);

void  CsrCopyMmIcsFreqListIntoSearchList (
          FddCellInformation        * const mmListOfFreqs_p,
          CsrListOfFddFreqsToSearch * const listOfFrequesToSearch_p);

void  CsrAddMmIcsFreqListIntoSearchList (
          FddCellInformation        * const mmListOfFreqs_p,
          CsrListOfFddFreqsToSearch * const listOfFrequesToSearch_p);

void  CsrSetupFrequenciesToSniffIntoSniffList (
          const CsrTypesOfFreqsToSniffBitMask freqsBitMask);

void  CsrSetupFrequenciesToSniffIntoSearchList (
          const CsrTypesOfFreqsToSniffBitMask freqsBitMask);


        /*  GENERAL UTILITY FUNCTIONS */

void  CsrHandleDatabaseMergesUponReselection (void);

void  CsrUpdateExpectedSysInfo (
          CsrSysInfoMask *expectedMask,
          CsrSysInfoMask oldSIB,
          CsrSysInfoMask newSIB);

void  CsrCsrpAbortCompleteEvent (void);
/* ********** add begin */
#if defined UPGRADE_CSG1
void  CsrCsrcsgsAbortCompleteEvent (void);
#endif //UPGRADE_CSG1
/* ********** add begin */

void CsrrHandleTreselectionTimersIfNecessary (
                const Boolean servCellHasBeenRanked,
                const CsrTReselectionToStart startTReselectionFor);

void  CsrCsrrReselectionCompleteEvent (void);
void CsrStorePendingRequest (CsrProcedure requestType,UrrSubProcessId process,void *request_p);/* ********** removed static */
void  CsrAbortCsrrDueToTicsExpiry (Boolean phyNoActionInd); /* ********** */

void CsrAbortPendingPlmnIfExists(void);

Boolean CsrValidateMinCellRequirements (
          const SignedInt16 cpichRscp,
          const SignedInt16 cpichEcNo,
          CsrProcId currentProcId,
          Boolean indicatePlmnSearch/*********** add*/);

UMaxAllowedUL_TX_Power CsrGetMaxUlAllowedTxPower(void);

void  CsrInformRrcSubProcessesOfNoCellCondition (void);

void  CsrUpdatePlmn (Plmn plmn);

/* CSRP functions (PLMN SELECTION) */

Boolean CsrGetPlmnSuspensionInfoValidity(void);
/* ********** add begin */
#if defined UPGRADE_CSG1
Boolean CsrGetCsgSuspensionInfoValidity(void);
void CsrCsgUnBarCsgCells (void);
void CsrcsgsDoNotWaitForSib5 (void);
void CsrcsgsDoNotWaitForSib20 (void);
void CsrcsgsSuspendBackgroundCsgSearch(Boolean nServTimExpiredFlag);   /* ********** add param */
void CsrcsgsResumeBackgroundCsgSearch(Boolean abortSuspended, const CsrCsgsAbortRequestSource abortRequestSource);
void  CsrcsgsRrcCsgListReq (RrcCsgListReq * const urr_p,
    const CsrSearchType searchType,
    const Boolean initialiseSearchParameters);
void  CsrcsgsInit(const Boolean initialiseSearchParameters, const Boolean causeIsDeactivate);
void  CsrcsgsSaveCsgSearchRequestEvent (RrcCsgListReq * const rrcCsgListReq_p);
void  CsrcsgsInformMm (const CsgListStatus status);
void  CsrcsgsAbortCsgSearchEvent (const CsrCsgsAbortRequestSource abortSource);
void  CsrcsgsCphyRssiScanCnf (CphyRssiScanCnf * const cphyRssiScanCnf_p);
void  CsrcsgsCphyDrxRssiScanCnf (CphyDrxRssiScanCnf * const cphyDrxRssiScanCnf_p);
void  CsrcsgsCphyFindCellCnf (CphyFindCellCnf * const cphyFindCellCnf_p);
void  CsrcsgsCphyDrxFindCellCnf (CphyDrxFindCellCnf * const drxFindCellCnf_p);
void  CsrcsgsInteractionWithPhyAllowedEvent (void);
void CsrcsgsHandleRrcCsgListCnf (const CsgListStatus status
#if !defined (UPGRADE_EXCLUDE_2G)
                                    ,const NetworkMode    networkMode
#endif
                                    );
void  CsrcsgsSCellSib3Ind (const USysInfoType3 * const sib3_p);
void CsrcsgsTerminateUmtsCsgSearch(void);
void CsrcsgsInitCsgSearchManager(const Boolean initialiseSearchParameters);
void  CsrcsgsSCellSib5Ind  (const USysInfoType5 * const sib3_p);
void  CsrcsgsNCellSib3Ind (const USysInfoType3 * const sib3_p);
void  CsrcsgsNCellSib20Ind (const USysInfoType20 * const sib20_p);
void  CsrcsgsHandleBcchErrorEvent (const UrrSirCellType cellType);                //-+ ********** 29-Mar-2012 +-
void  CsrcsgsGuardTimerExpiryEvent (void);
void CsrcsgsHandleSibSchedulingInfoEvent (CsrSysInfoMask sibsSchedulingInfoMask, Boolean Sib5bisScheduled);
void  CsrcsgsCphyDrxNextCellCnf (CphyDrxNextCellCnf * const cphyDrxNextCellCnf_p);
void CsrcsgsActionsOnUmtsSearchAbortionComplete (Boolean isGsm, Boolean isAbortForOOS /* ********** add */);
void CsrcsgsInitLteCsgSearchManager (Boolean startSearch);
void CsrcsgsDoLteCsgSearch (Boolean fromSuspension);   /* ********** add par fromSuspension*/
CsrpLtePlmnSearchType CsrcsgsLteCsgSearchBandsType (void);
void  CsrcsgsHandleCphyLteNcellBchInd (
                               CphyLteNcellBchInd * const cphyLteNcellBchInd_p);
void  CsrcsgsHandleCphyLteNcellStopBchCnf (
                       CphyLteNcellStopBchCnf * const cphyLteNcellStopBchCnf_p);
#if 0/*********** remove*/
void CsrcsgsHandleFgLTESearch (RrcCsgListReq * const plmnListReq_p);
#endif/*********** remove*/
void CsrcsgsLtePlmnCsgTimerExpired (const CsrTimerIdentity timerId);
void CsrcsgsTerminateLteCsgSearch (void);
void  CsrcsgsHandleCphyDrxFindLteCellAbortCnf (void);
Boolean CsrcsgsLteCsgSearchDoing (void);
void  CsrcsgsHandleCphyDrxFindLteCellCnf (
                         CphyDrxFindLteCellCnf * const cphyDrxFindLteCellCnf_p);

#if defined (UPGRADE_URR_CSG_USING_PLMS)
void CsrcsgsHandleIrrGetUmtsSibsReq (IrrGetUmtsSibsReq *irrGetUmtsSibsReq_p, Boolean backGroundSearch);
void CsrpNBchDecodingRequestToL1 (Boolean  onOff,
                                         UUARFCN  uarfcnDl,
                                         UPrimaryScramblingCode psc);
void CsrcsgsCphyNCellBchCnfEvent (UrrInternalSignalNcellBchCnf * const ncellBchCnf_p); 
void CsrcsgsPlmsSearchComplete (IrrPlmnListCnf * irrPlmnListCnf_p);
void CsrcsgsHandleIrrGetLteSibsReq (IrrGetLteSibsReq * const irrGetLteSibsReq_p);

#endif/*UPGRADE_URR_CSG_USING_PLMS*/

#endif //UPGRADE_CSG1
/* ********** add end */

/* ********** modify begin */
#if !defined UPGRADE_PLMS 
Boolean CsrGetCheckCsrpState(CsrpState state);
#else
Boolean CsrGetCheckCsrpState(CsrpActiveState state);
#endif
/* ********** modify end */

void CsrPrepareTablesToReceiveDataForPlmnResume (void);

void CsrCopyNumOfFreqsToScanInPrimeListStage (Int8 * dstNumOfFreqsPerBand_p, Int8 * srcNumOfFreqsPerBand_p);

void CsrInitNumOfFreqsToScanInPrimeListStage (Int8 * numOfFreqsPerBand_p);

Int16 CsrTotalNumOfFreqsToScanInPrimeListStage (void);

void CsrpDoNotWaitForSib5 (void);

void CsrpSuspendBackgroundPlmnSearch(Boolean nServTimExpiredFlag);   /* ********** add param */

void CsrpResumeBackgroundPlmnSearch(Boolean abortSuspended, const CsrPlmnAbortRequestSource abortRequestSource);

#if !defined UPGRADE_PLMS /* ********** added */
void CsrpGsmStartPlmnSearch (Boolean fromSuspension);

void  CsrpRrcPlmnListReq (RrcPlmnListReq * const urr_p,
    const CsrSearchType searchType,
    const Boolean initialiseSearchParameters);
#endif /* ********** added */

void  CsrpInit(const Boolean initialiseSearchParameters, const Boolean causeIsDeactivate);


void  CsrpTerminationEvent (void);

void  CsrpBchDecodeAborted (void);

void  CsrpSavePlmnSearchRequestEvent (RrcPlmnListReq * const rrcPlmnListReq_p);


void  CsrpInformMm (const PlmnListStatus status);


#if ! defined (UPGRADE_EXCLUDE_2G)
#if !defined UPGRADE_PLMS 	// ********** added
void  CsrpGsmPlmnSearchTimerExpired (const CsrTimerIdentity timerId);
#endif	// ********** added
void  CsrpGsmCellsFoundTimerExpired (const CsrTimerIdentity timerId);
#endif

void  CsrpAbortPlmnSearchEvent (const CsrPlmnAbortRequestSource abortSource, Boolean causedBySIM2Service);/*CQ00098682 add causedBySIM2Service*/

#if !defined UPGRADE_PLMS /* ********** added */
void  CsrpCphyRssiScanCnf (CphyRssiScanCnf * const cphyRssiScanCnf_p);
void  CsrpCphyDrxRssiScanCnf (CphyDrxRssiScanCnf * const cphyDrxRssiScanCnf_p);
#endif /* ********** added */

#if !defined UPGRADE_PLMS /* ********** added */
void  CsrpCphyFindCellCnf (CphyFindCellCnf * const cphyFindCellCnf_p);
void  CsrpCphyDrxFindCellCnf (CphyDrxFindCellCnf * const drxFindCellCnf_p);
#endif /* ********** added */

//#if !defined UPGRADE_PLMS // ********** added //********** removed
void  CsrpInteractionWithPhyAllowedEvent (void);
//#endif // ********** added                    //********** removed

void CsrpHandleRrcPlmnListCnf (const PlmnListStatus status
#if !defined(UPGRADE_PLMS_SEARCH_API)/*********** add MACRO*/
#if !defined (UPGRADE_EXCLUDE_2G)
                                    ,const NetworkMode    networkMode
#endif
#endif/*********** add MACRO*/
                                    );


#if ! defined (UPGRADE_EXCLUDE_2G)
#if !defined UPGRADE_PLMS /* ********** added */
void  CsrpHandleCphyGsmListMeasInd (CphyGsmListMeasInd * const cphyGsmListMeasInd_p);
void  CsrpHandleCphyGsmListBsicInd (CphyGsmListBsicInd * const cphyGsmListBsicInd_p);
#endif /* ********** added */
void  CsrpHandleCphyGsmBcchDecodeInd (CphyGsmBcchDecodeInd * const cphyGsmBcchDecodeInd_p);
#endif

#if defined(UPGRADE_PLMS)
void CsrpSCellMibInd (const UPLMN_Identity * const uPlmn_p,
                            UMultiplePLMN_List_r6 * multiplePlmn_p);
#else/*UPGRADE_PLMS*/
void CsrpSCellMibInd (const UPLMN_Identity * const uPlmn_p,
                            UMultiplePLMN_List_r6 * multiplePlmn_p
/* ********** - add begin */
#if defined UPGRADE_CSG1
                            ,Boolean csgIndicatorPresent
#endif //UPGRADE_CSG1
/* ********** - add end */
                                                            );
#endif/*UPGRADE_PLMS*/

void  CsrpSCellSib3Ind (const USysInfoType3 * const sib3_p);

void CsrpTerminateUmtsPlmnSearch (void);

void CsrpInitPlmnSearchManager(const Boolean initialiseSearchParameters);


void  CsrpSCellSib5Ind  (const USysInfoType5 * const sib3_p);
UUARFCN CsrChangeUarfcnToOverlappingBand (UUARFCN old_uarfcn, FddBandIndex newBand);

void CsrpNCellMibInd (const UPLMN_Identity * const uPlmn_p,
                            UMultiplePLMN_List_r6 * multiplePlmn_p,
                            Boolean sib5bisScheduled);
/* ********** add begin */
#if defined UPGRADE_CSG1
void CsrcsgsNCellMibInd (const UPLMN_Identity * const uPlmn_p,
                            UMultiplePLMN_List_r6 * multiplePlmn_p,
                            Boolean sib5bisScheduled);

void CsrcsgsSCellMibInd (const UPLMN_Identity * const uPlmn_p,
                            UMultiplePLMN_List_r6 * multiplePlmn_p
/* ********** - add begin */
                            ,Boolean csgIndicatorPresent
/* ********** - add end */
																);
#endif //UPGRADE_CSG1
/* ********** add end */

void  CsrpNCellSib3Ind (const USysInfoType3 * const sib3_p);
void CsrpNCellSib5Ind (const USysInfoType5 * const sib5_p); /* ********** added */


void  CsrpHandleBcchErrorEvent (const UrrSirCellType cellType);                //-+ ********** 29-Mar-2012 +-
void  CsrpGuardTimerExpiryEvent (void);


void CsrpHandleSibSchedulingInfoEvent (CsrSysInfoMask sibsSchedulingInfoMask, Boolean Sib5bisScheduled);

#if !defined UPGRADE_PLMS /* ********** added */
void  CsrpCphyNextCellCnf (CphyNextCellCnf * const cphyNextCellCnf_p);
#endif /* ********** added */

#if !defined UPGRADE_PLMS // ********** added
void CsrpActionsOnUmtsSearchAbortionComplete (Boolean isGsm, Boolean isAbortForOOS /* ********** add */);
#endif// ********** added

void CsrpGsmTerminatePlmnSearch (void);

#if !defined UPGRADE_PLMS /* ********** modify begin */
void  CsrpHandleCphyDrxFindLteCellCnf (
                         CphyDrxFindLteCellCnf * const cphyDrxFindLteCellCnf_p);
Boolean CsrpLtePlmnSearchDoing (void);
void CsrpDoLtePlmnSearch (Boolean fromSuspension);   /* ********** add par fromSuspension*/
#endif /* ********** modify end */

void  CsrpHandleCphyLteNcellBchInd (
                               CphyLteNcellBchInd * const cphyLteNcellBchInd_p);
void  CsrpHandleCphyLteNcellStopBchCnf (
                       CphyLteNcellStopBchCnf * const cphyLteNcellStopBchCnf_p);
#if !defined UPGRADE_PLMS// ********** added
void  CsrpHandleCphyDrxFindLteCellAbortCnf (void);
#endif// ********** added
/* ********** removed */
//CsrpLtePlmnSearchType CsrpLtePlmnSearchBandsType (void); //********** deleted
void CsrpInitLtePlmnSearchManager (Boolean startSearch);

/* ********** removed */
void CsrpTerminateLtePlmnSearch (void);
/* ********** modify begin */
#if !defined UPGRADE_PLMS
void CsrpLtePlmnSearchTimerExpired (const CsrTimerIdentity timerId);
#else
void CsrpMratPlmnSearchTimerExpired (const CsrTimerIdentity timerId);
void CsrpMratAbortPlmsTimerExpired (const CsrTimerIdentity timerId); /* ********** added */	
#endif
/* ********** modify end */

/* ********** add begin */
#if defined UPGRADE_PLMS
void CsrpSetResumePlmsUponPhyConfigFinish (Boolean resume); 
#endif
/* ********** add end */

/* CSRC functions (SELECT CELL) */

void CsrcTerminationEvent (void);

void  CsrcAbortSelectionEvent (void);

void CsrcActionsUponStartingToSelectACell (
               UrrInternalSignalSelectCell * const requestedCellInfo_p);

CsrBarredStatus
      CsrcCheckAccessRestrictions (const CsrDataBaseType database);

Boolean CsrcCheckCellSelectionCriteria (
            CsrCellSelectionInfo     *cellSelectionInfo_p,
            CsrQMeasurement          *q_measured_p,
      const Boolean                  cellIsOfInterRatType,
      const Boolean                  debugPrint,
            Boolean                  hysteresisScriteriaRequested,
      const UMaxGsmTxPower           maxUeTxPowerForCell);    /* CQ00068365 added */

Boolean
      CsrcCheckModeAndSibMaskOnSib3Rx (
          const USysInfoType3     * const sib3_p,
                CsrSysInfoMask    * const expectedSysInfoMask_p,
                CsrSysInfoMask    * const receivedSysInfoMask_p);

void  CsrcCphyCellSelectCnf (CphyCellSelectCnf * const cphyCellSelectCnf_p);

void  CsrcCphyFindCellCnf (CphyFindCellCnf * const cphyFindCellCnf_p);

void  CsrcCphyNextCellCnf (CphyNextCellCnf * const cphyNextCellCnf_p);

void  CsrcGuardTimerExpiryEvent (void);

void  CsrcHandleBcchErrorEvent (const UrrSirCellType cellType);                //-+ ********** 29-Mar-2012 +-

void  CsrcInit (void);

#if ! defined (UPGRADE_EXCLUDE_2G)
void CsrcSelectACellUponCcotuEvent (
                UUMTS_CellInformation * const cell_p,
          const Boolean                       txDiversityIndicator);
#endif

UT_Barred
      CsrcProcessAccessRestrictions (
          const CsrDataBaseType   database,
          const UUARFCN           freq);

//********** start
#if defined UPGRADE_CSG1 //**********
Boolean CsrcScellMeetsBasicRequirements(void);
#endif
//********** end

CsrCellEval
      CsrcEvaluateCell (const CsrDataBaseType database);

void  CsrcHandleSibSchedulingInfoEvent (
          const CsrSysInfoMask     sibsSchedulingInfoMask,
          const CsrSysInfoMask     sibsUpdatesInfoMask,
          Boolean                  sib5bisScheduled);

void CsrcMibInd (const UPLMN_Identity * const uPlmn_p,
                     UMultiplePLMN_List_r6 * multiplePlmn_p
/* ********** - add begin */
#if defined UPGRADE_CSG1
                    ,Boolean csgIndicatorPresent
#endif //UPGRADE_CSG1
/* ********** - add end */
                                                            );

void  CsrcSib1Ind (
          const UNAS_SystemInformationGSM_MAP * const cn_CommonGSM_MAP_NAS_SysInfo_p,
          const UCN_DomainSysInfoList         * const cn_DomainSysInfoList_p);

void  CsrcSib3Ind (const USysInfoType3 * const sib3_p);

void  CsrcSib4Ind (const USysInfoType4 * const sib4_p);

void  CsrcSib5Ind (const USysInfoType5 * const sib5_p);

void  CsrcSib6Ind (const USysInfoType6 * const sib6_p);

void CsrcSib11Ind (const USysInfoType11 * const  sib11_p);

/* ********** - add begin */
#if defined UPGRADE_CSG1
void CsrcSib20Ind (const USysInfoType20 * const sib20_p);
#endif //UPGRADE_CSG1
/* ********** - add end */

void CsrcSib11bisInd (const USysInfoType11bis * const  sib11bis_p);

void CsrcSib12Ind (const USysInfoType12 * const sib12_p);
void CsrcSib18Ind (const USysInfoType18 * const sib18_p);

void CsrcSib19ArrivedInd (void); // ********** added

void  CsrcAllSibsReceived (void);

/* CSRS functions (CELL SELECTION) */

void  CsrsInit (void);

void CsrsInitCsrsData (Boolean clearBestCellSoFar);
#if !defined UPGRADE_PLMS
void CsrsDoMmListScan (void);
void CsrsDoPrimeListScan (void);
void CsrsUpdateBestCellSoFar (
                CsrCellEval       curCellEval,
                CsrSCellInfo      *servingCellInfo_p);
#endif //UPGRADE_PLMS
void  CsrCsrsInternalSignalSelectCell (UrrInternalSignalSelectCell *requestedCellInfo_p);
void CsrActionsOnNotSelectingAnyCell (void);
void  CsrsSetReqServingCell (UUMTS_CellInformation *requestedServingCell);

Boolean
      CsrsCellSelectionRunning (void);

void  CsrsStopCellSelection (void);

void  CsrsHandleSibSchedulingInfoEvent (
          const  CsrSysInfoMask      sibsSchedulingInfoMask,
          const  CsrSysInfoMask      sibsUpdatesInfoMask,
          Boolean                    sib5bisScheduled);

#if !defined UPGRADE_PLMS
void  CsrsCphyRssiScanCnf (CphyRssiScanCnf * const cphyRssiScanCnf_p);

void  CsrsCphyFindCellCnf (CphyFindCellCnf * const cphyFindCellCnf_p);

void  CsrsCphyNextCellCnf (CphyNextCellCnf * const cphyNextCellCnf_p);

void  CsrsCphyCellSelectCnf (CphyCellSelectCnf * const cphyCellSelectCnf_p);
#endif //UPGRADE_PLMS

void  CsrsInteractionWithPhyAllowedEvent (void);

void CsrsMibInd (const UPLMN_Identity * const uPlmn_p,
                     UMultiplePLMN_List_r6 * multiplePlmn_p
/* ********** - add begin */
#if defined UPGRADE_CSG1
                      ,Boolean csgIndicatorPresent
#endif //UPGRADE_CSG1
/* ********** - add end */
                                                            );

void  CsrsSib1Ind (
          const UNAS_SystemInformationGSM_MAP * const cn_CommonGSM_MAP_NAS_SysInfo_p,
          const UCN_DomainSysInfoList         * const cn_DomainSysInfoList_p);

void  CsrsSib3Ind (const USysInfoType3 * const sib3_p);

void  CsrsSib4Ind (const USysInfoType4 * const sib4_p);

void  CsrsSib5Ind (const USysInfoType5 * const sib5_p);

void  CsrsSib6Ind (const USysInfoType6 * const sib6_p);

void CsrsSib11Ind (const USysInfoType11 * const sib11_p);

void CsrsSib11bisInd (const USysInfoType11bis * const sib11bis_p);


void CsrsSib12Ind (const USysInfoType12 * const sib12_p);
void CsrsSib18Ind (const USysInfoType18 * const sib18_p);

void CsrsSib19ArrivedInd (void); // ********** added

/* ********** - add begin */
#if defined UPGRADE_CSG1
void CsrsSib20Ind (const USysInfoType20 * const  sib20_p);
#endif //UPGRADE_CSG1
/* ********** - add end */

void  CsrsAllSibsReceived (void);
void  CsrsHandleBcchErrorEvent (const UrrSirCellType cellType);                //-+ ********** 29-Mar-2012 +-
void  CsrsGuardTimerExpiryEvent (void);
void  CsrsDeallocAllDynamicMemory (void);
void  CsrsAbortSearchEvent (void);
Boolean CsrsSearchTriggeredByMM (void);
ActivationTime CsrsGetCsrsActivationTime (void);


CsrsPlmsIcsActivityType CsrsGetPlmsIcsActivity(void);
#if defined (URR_MRAT_ICS_SEARCH)
Boolean CsrsIsPlmsIcsSupportedForIcsCause(CsrsSearchCause         searchCause);
void CsrsStartPlmsIcsSearch(void);
void CsrsCphyFgBchCnf (RadioResourceControlEntity *urr_p);
void CsrsHandleIcsEvalReq(IrrIcsEvalReq*				irrIcsEvalReq_p);
void CsrsHandleIcsCampingReq(IrrIcsCampingReq*				irrIcsCampingReq_p);
void CsrsHandleIcsSearchCnf(IrrIcsSearchCnf*				irrIcsSearchCnf_p);
void CsrsAbortCellEval(void);
void CsrsHandleIcsEvalAbortReq(void);
void CsrsHandleIcsAbortCnf(void);
void  CsrsHandleCphySetWbCnf(void);
/* ********** added begin */
void CsrsHandleIrrIcsReselectionRejByNasInd(void);
/* ********** added end */
void CsrsDbStoreInterNcellsInMmListForIdleOos(RrMmListSearchParams* mmListSearchParams_p);
void CsrsDbStoreIratNcellsInMmList(RrMmListSearchParams* mmListSearchParams_p);
void CsrdbStoreActReqInfoInRrComDb(RrcActReq *actReq_p);
void CsrdbLoadRrComDbInfo(void);
void CsrsHandleGrrRcReselectToUmtsReq(GrrRcReselectToUmtsReq * grrRcReselectToUmtsReq_p);
Boolean CsrsValidateIcsEvalReq(IrrIcsEvalReq*				irrIcsEvalReq_p);
void CsrsCompleteIcsEval(IratReselectCellStatus  cellStatus,RrIcsEvalResults evalResult, Boolean umtsCellInfoPresent, IrrIcsUmtsCellInfo *umtsCellInfo);/*********** add umtsCellInfoPresent and umtsCellInfo*/
void CsrsHandleLteIRatReselectRequest(IratReselectionRequest *iratReselectionRequest_p);
void CsrsGrrRcReselectToGsmCnfEvent(GrrRcReselectToGsmCnf*   grrRcReselectToGsmCnf_p);
void CsrsHandleIratReselectAck(IratReselectionAck*      iratReselectionAck_p);
void CsrsDbStoreInterNcellsInMmListForConnectedOos(RrMmListSearchParams* mmListSearchParams_p);
void CsrSendCphyIcsInitReq(ActivationTime*          activationTime_p);
void CsrsHandleIcsAbortCnfWhenLteToUmts(void);/*********** add*/
#endif
/* CSRR functions (CELL RESELECTION) */

void CsrsRemoveBufferedRrcDeactReq (void);//********** add

        /* INITIALISATION FUNCTIONS */
void  CsrrInit (const Boolean totalInitialisation); /* ********** */

        /* AUXILIARY FUNCTIONS */

void CsrrHandleDeactivatedToCellDchMode (void);

void CsrrCphyDetectedCellMeasInd (CphyDetectedCellMeasInd *cphyDetectedCellMeasInd_p); /* ********** added */

void CsrrCphyFindCellCnf (CphyFindCellCnf * const cphyFindCellCnf_p);

void CsrrHandleRedirectionWithOnlyUarfcn(Boolean awakeL1, Boolean getFindCellInfo);

void CsrrActionsUponFinishingOffReselection (void);

void CsrrFinishOffReselectionCycle (void);

void CsrrAbortReselectionEvent (void);

void CsrrStartCellReselection (Boolean restartCellReselection);

void CsrrHandleDeactivatedToIdleMode (void);

void CsrrUeSuspendedToIdle (void);

void CsrrUeSuspendedToDch (void);

void CsrrSuspendCellReselectionToDch (void);

void CsrrHandleTReselectionUponStateChange (
                const UrrSmcModeAndState *newModeAndState,
                const UrrSmcModeAndState *currentModeAndState);

void CsrrActionsOnConnectedNonDchToSuspended (void);

void CsrrStartOosIdleRecoveryIfRequired (void);

void CsrrNonOosActionsOnMoveToGsmFailInNonDch (void);

void CsrrResumeReselOn3gTo2GReselFailure (void);

void CsrrStartCsrrAfterReselToGsmFailure (const Boolean mustReselect);
void  CsrrStopIntraFTreselectionTimers (CellIdMask cellIdMask);

void  CsrrStopInterFTreselectionTimers (CellIdMask cellIdMask);

#if ! defined (UPGRADE_EXCLUDE_2G)
void  CsrrStopGsmTreselectionTimers (CellIdMask cellIdMask);
#endif

void  CsrrBuildAndSendCOFromUtranFail (
          const URRC_TransactionIdentifier     receivedTransactionId,
          const T_UInterRAT_ChangeFailureCause failureCause,
          const UProtocolErrorCause            protocoErrorCause);

Boolean
      CsrrIsReselectorModeNoMode (void);

Boolean
      CsrrGetActiveSetSelection (void);

Boolean
      CsrrTheseCellsAreTheSame (
          CsrCell * const cell1_p,
          CsrCell * const cell2_p,
          UUARFCN         servingCellFreq);

void  CsrrCodeAndSendCphyCellSelectReq (
          UUARFCN   uarfcn_dl,
          UmtsCell  *umtsCellToSend);

CsrPlmnType
      CsrrIsPlmnSelectedOrEquivalent (Plmn plmn);

void  CsrrStopTreselectionTimerForThisCell (CsrCell * const cell_p);

void  CsrrRestartReselCycleImmediately (void);

void  CsrrActionOnTreselectionTimerForThisCell (
                CsrCell * const cell_p,
          const Boolean         startIfNecessary);

void  CsrrPlmnSearchSuspendsReselection (const CsrSearchType type);

Boolean
      CsrrIsSuspendedDueToBackgroundPlmnSearch (void);

Boolean
      CsrrTreselectionMustBeConsidered (void);

        /* EVENT HANDLING FUNCTIONS */
void  CsrrStoreSib3data (const USysInfoType3 * const sib3_p, CsrDataBaseType dbType);

void  CsrrStoreSib4data (const USysInfoType4 * const sib4_p, CsrDataBaseType dbType);

        /* MODE/STATE CHANGE FUNCTIONS */
void  CsrrHandleLeavingCellDch (
          UrrInternalSignalPhyToIdleState *phyToIdleState_p);

UUARFCN CsrrGetRequestedUarfcn(void);

CsrSource CsrcGetCsrSource(void);
        /* Sib reception related */
void  CsrrHandleSibSchedulingInfoEvent (
                 UrrInternalSignalIndicateSibScheduling * const sig_p,
          const  CsrSysInfoMask                          sibsSchedulingInfoMask,
          const  CsrSysInfoMask                          sibsUpdatesInfoMask,
          Boolean                                                     sib5bisScheduled);

void CsrrSCellMibIndEvent (const UPLMN_Identity * const uPlmn_p,
                                  UMultiplePLMN_List_r6 * multiplePlmn_p
/* ********** - add begin */
#if defined UPGRADE_CSG1
                                ,Boolean csgIndicatorPresent
#endif //UPGRADE_CSG1
/* ********** - add end */
                                                            );

void  CsrrSCellSib1IndEvent (
          const UNAS_SystemInformationGSM_MAP * const cn_CommonGSM_MAP_NAS_SysInfo_p,
          const UCN_DomainSysInfoList         * const cn_DomainSysInfoList_p);

void  CsrrSCellSib3IndEvent (const USysInfoType3 * const sib3_p);

void  CsrrSCellSib4IndEvent (const USysInfoType4 * const sib4_p);

void  CsrrSCellSib5IndEvent (const USysInfoType5 * const sib5_p);

void  CsrrSCellSib6IndEvent (const USysInfoType6 * const sib6_p);

void CsrrSCellSib11IndEvent (const USysInfoType11 * const  sib11_p);

void CsrrSCellSib11bisIndEvent (const USysInfoType11bis * const sib11bis_p);

void CsrrSCellSib12IndEvent (const USysInfoType12 * const sib12_p);

void  CsrrSCellSib18IndEvent (const USysInfoType18 * const sib18_p);

/* ********** - add begin */
#if defined UPGRADE_CSG1
void CsrrSCellSib20IndEvent (const USysInfoType20 * const  sib20_p);
#endif //UPGRADE_CSG1
/* ********** - add end */
void  CsrrSCellSib19IndEvent (const USysInfoType19 * const sib19_p);
void  CsrrSib19ArrivedInd(void); //********** add

void  CsrrUpdateDedicatedPriorityInfo (
                        UDedicatedPriorityInformation *dedicatedPriorityInfo_p);

void  CsrrT322expiryEvent (CsrTimerIdentity timerId);

void  CsrrReselectToRedirectedCell(CsrRedirectionAction redirectionAction);

void CsrrNCellMibIndEvent (const UPLMN_Identity * const uPlmn_p,
                                  UMultiplePLMN_List_r6 * multiplePlmn_p,
                                  Boolean sib5bisScheduled
/* ********** - add begin */
#if defined UPGRADE_CSG1
                                  ,Boolean csgIndicatorPresent
#endif //UPGRADE_CSG1
/* ********** - add end */
                                    );


void  CsrrNCellSib1IndEvent (
          const UNAS_SystemInformationGSM_MAP * const cn_CommonGSM_MAP_NAS_SysInfo_p,
          const UCN_DomainSysInfoList         * const cn_DomainSysInfoList_p);

void  CsrrNCellSib3IndEvent (const USysInfoType3 * const sib3_p);

void  CsrrNCellSib4IndEvent (const USysInfoType4 * const sib4_p);

void  CsrrNCellSib5IndEvent (const USysInfoType5 * const sib5_p);

void  CsrrNCellSib6IndEvent (const USysInfoType6 * const sib6_p);

void CsrrNCellSib11IndEvent (const USysInfoType11 * const sib11_p);

void CsrrNCellSib11bisIndEvent (const USysInfoType11bis * const sib11bis_p);

void CsrrNCellSib12IndEvent (const USysInfoType12 * const sib12_p);

void CsrrNCellSib18IndEvent (const USysInfoType18 * const sib18_p);

/* ********** - add begin */
#if defined UPGRADE_CSG1
void CsrrNCellSib20IndEvent (const USysInfoType20 * const sib20_p);
#endif //UPGRADE_CSG1
/* ********** - add end */

void CsrrTriggerCellSelection (UUARFCN uarfcn_dl,
                                  Boolean otherRat,
                                  UWaitTime waitTime,
                                  Boolean useActivationTime);

void  CsrrAllSibsReceived (void);

        /* L1 - related */
void  CsrrDrxCycleLengthEvent (
          const UrrInternalSignalDrxCycle *urrInternalSignalDrxCycle_p);

void  CsrrCphyNCellBchCnfEvent (UrrInternalSignalNcellBchCnf * const ncellBchCnf_p);

void  CsrrCphySelectCellCnfEvent (CphyCellSelectCnf * const cphyCellSelectCnf_p);

        /* Timer expiry-related */
void  CsrrStartCellSelectTimerExpired (const CsrTimerIdentity timerId);

void  CsrrHoldOffTimerExpired (const CsrTimerIdentity timerId);

void CsrrDtCsfbReadSibsTimerExpired (const CsrTimerIdentity timerId); //********** add

void CsrrCsfbSearchTimerExpired (const CsrTimerIdentity timerId);/*********** add */

void  CsrrForbiddenTimerExpired(const CsrTimerIdentity timerId);

void  CsrrReselectionTimerExpired (const CsrTimerIdentity timerId);

void  CsrrNServTimerExpired (const CsrTimerIdentity timerId);

void  CsrrCellbarredTimerExpired (const CsrTimerIdentity timerId);

void  CsrrNCellTnTimerExpired (const CsrTimerIdentity timerId);

void  CsrrNCellTreselectionTimerExpired (const CsrTimerIdentity timerId);

void  CsrrTimerStartPlmnSearchIntlvdSniffExpired (void);

void  CsrrHandleMibTimeoutEvent (const UrrSirCellType cellType);
void  CsrrHandleBadCrcEvent (const UrrSirCellType cellType);
void  CsrrHandleIgnoredBcchEvent (const UrrSirCellType cellType);              //-+ ********** 29-Mar-2012 +-

void  CsrrHandleBcchErrorEvent (const UrrSirCellType cellType, const BcchError bcchError);          //-+ ********** 29-Mar-2012 +-

void  CsrrGuardTimerExpiryEvent (void);
/* ********** begin */
#if !defined (ON_PC)
void  CsrrIratPingpangTimerExpired (const CsrTimerIdentity timerId);
void  CsrrIratHoldOffTimerExpired (const CsrTimerIdentity timerId);
#endif
/* ********** end */

        /*  Mobility Detector - related */
void  CsrrChangeOfUeSpeedEvent (void);

        /* Mode\state change-related */
void  CsrrDeallocAllDynamicMemory (const Boolean termination);

        /* GSM-stack related */
#if ! defined (UPGRADE_EXCLUDE_2G)
void  CsrrResumeAfterCcofuFailure (const UrrSmcConnectedState connectedState);

void  CsrrCphyCellSelectIndAfterCcofuFailureEvent (
          const Boolean phyHasResynched);

void  CsrrCellChangeOrderFromUtranEvent (void);

void  CsrrGrrRcReselectToGsmCnfEvent (
          GrrRcReselectToGsmCnf * const signal_p);

void CsrrReselectToGsmOnOos (void);

void CsrrTriggerSelectionInGsm (
                UrrInternalSignalSelectCell * const sig_p); //********* add

void CsrrReselectACellIfNecessary (Boolean handleDetected,
                                        Boolean onlyRank); /* ********** added*/

void  CsrrCphyCellSelectIndEvent (CphyCellSelectInd * const signal_p);

void  CsrrGrrRcReselectToUmtsReqEvent (GrrRcReselectToUmtsReq * const signal_p);

void  CsrrIntSignalReselToGsmCnfEvent (void);

void  CsrrUmphReselectToUmtsFailCnfEvent (void);

void  CsrrMoveToOtherRatOnConnectionRejectEvent (
          const CsrProcId                           interruptedCsrProcess,
                UrrInternalSignalSelectCell * const sig_p);

void  CsrrCphySuspendIndEvent (void);

void  CsrrHandleGsmFallBackInfoDeletionEvent (const Boolean gsmRatArfcnValid);

#endif /* UPGRADE_EXCLUDE_2G */

        /* Others */

void  CsrrRrcUpdateReqEvent (void);

void  CsrrResynchPhyWithServCellEvent (void);

void  CsrrDisableFreqOnConnectionRejectEvent (void);

void CsrrActiveCsrpActive(void);
CsrrReselectorState CsrrGetCsrrstate(void);
/* ********** - add start*/
#if defined UPGRADE_CSG1
Boolean CsrGetCalledFromNserv(void);
#endif
/* ********** - add end*/
void  CsrrHandleLaiUpdate (CsrLai * const lai_p, const CsrCellEval evaluation);
void  CsrrHandlePlmnUpdate (Plmn * const plmn_p, const CsrCellEval evaluation);

void  CsrrEndOfPhyConfigurationEvent (void);

void  CsrrHandleNetworkAuthFailEvent (void);

void  CsrrServingCellMeasIndEvent (
            const CphyServingCellMeasurementInd * const sCellMeasurement_p);

void  CsrrCphyMonitorIntraFreqCellInd (
            const CphyMonitorIntraFreqCellInd * const intraFreqMeas_p);

void  CsrrCphyMonitorInterFreqCellInd (
            const CphyMonitorInterFreqCellInd * const interFreqMeas_p);

#if !defined (UPGRADE_EXCLUDE_2G)
void  CsrrCphyMonitorGsmCellInd (
            const CphyMonitorGsmCellInd * const gsmMeas_p);
#endif

void  CsrrCphyMonitorLteCellInd (const CphyMonitorLteCellInd * const lteMeas_p);

void  CsrrIratReselectionReqEvent (IratReselectionRequest * const signal_p);

void  CsrrDeallocateBcchContainer (
                IratReselectionRequest * iratReselectionRequest_p); /* ********** add */
#if !defined (ON_PC)
void  CsrrUpdateListFromFakeLteFreqs (void);    /* for fake LTE Ncell */
#endif

void CsrrHandleCphyDeactivateCnf (void);   /* ********** add */

void CsrrDetermineAndActuateNCellMeasurements (const Boolean restart);

Boolean CsrrInReselectorMode(void);        /* ********** */
/*********** add begin*/
void CsrrTriggerMonitorEventAfterFailToGsm (GrrRcReselectCause cause);
void CsrrTriggerMonitorEventAfterFailToLte (IratReselectCause cause);
/*********** add end*/

/* CSRRK functions (CELL RANKING) */
void CsrrkInit (void);

void CsrrkListCleanUp (void);

void CsrrkResetListPtr (void);

void CsrrkHandleTreselectionTimersForRankedCells (
    const Boolean servCellHasBeenRanked,
    const CsrTReselectionToStart startTReselectionFor);

void CsrrkClearTreselectionExpiryFlags (void);                                 //-+ ********** 16-Apr-2012 +-

void CsrrkInitRankedHigherThanServingFlag (void);                              //-+ ********** 16-Apr-2012 +-

Boolean CsrrkShouldCheckTReselection(const CsrTReselectionToStart startTReselectionFor,
                                            const CsrCellTypes csrCellType);

Boolean CsrrkCheckPenaltyTimeState(CsrCellId cellId,
                                         CsrCellTypes cellType);

Boolean
      CsrrkThereAreCellsSatisfyingHCrit
        (UHCS_PRIO *highestCellPriority_p,
         UHCS_PRIO *lowestCellPriority_p,
         UHCS_PRIO *highestAmongSHcsLower_p,
         Boolean servingCellSCriteria);

void  CsrrkDoSecondRanking (
          const Boolean   useOfHcs,
          const UHCS_PRIO sCellPriority);

Boolean
      CsrrkRankSCell (
          const CsrCell   *const cellToRank_p,
          const Boolean   useOfHcs,
          const Boolean   ueIsFast,
          const UHCS_PRIO highestCellPriority,
          const UHCS_PRIO lowestCellPriority,
          const UHCS_PRIO highestAmongLower,
          const Boolean   leavingDch);

Int8  CsrrkRankIntraFreqCells (
          CellIdMask      cellsToRankMask,
          const Boolean   useOfHcs,
          const Boolean   ueIsFast,
          const UHCS_PRIO highestCellPriority,
          const UHCS_PRIO lowestCellPriority,
          const UHCS_PRIO highestAmongLower,
          const Boolean   leavingDch,
          const UHCS_PRIO reference,
          Boolean         rankAll);
#if defined UPGRADE_CSG1
Int8 CsrrkRankIntraDetectedFreqCsgCells (
    CellIdMask        cellsToRankMask,
    const Boolean     useOfHcs,
    const Boolean     ueIsFast,
    const UHCS_PRIO   highestCellPriority,
    const UHCS_PRIO   lowestCellPriority,
    const UHCS_PRIO   highestAmongLower,
    const Boolean     leavingDch,
    const UHCS_PRIO   reference,
    Boolean           rankAll);
#endif //UPGRADE_CSG1

#if defined (L1_SUPPORT_IDLE_INTRA_DETECT)  /*********** add*/
Int8  CsrrkRankIntraDetectedFreqCells (
          CellIdMask      cellsToRankMask,
          const Boolean   useOfHcs,
          const Boolean   ueIsFast,
          const UHCS_PRIO highestCellPriority,
          const UHCS_PRIO lowestCellPriority,
          const UHCS_PRIO highestAmongLower,
          const Boolean   leavingDch,
          const UHCS_PRIO reference,
          Boolean         rankAll);
#endif /*********** add*/

Int8  CsrrkRankInterFreqCells (
          CellIdMask      cellsToRankMask,
          const Boolean   useOfHcs,
          const Boolean   ueIsFast,
          const UHCS_PRIO highestCellPriority,
          const UHCS_PRIO lowestCellPriority,
          const UHCS_PRIO highestAmongLower,
          const Boolean   leavingDch,
          const UHCS_PRIO reference,
          Boolean         rankAll);

#if ! defined (UPGRADE_EXCLUDE_2G)
Int8  CsrrkRankGsmCells (
          CellIdMask      cellsToRankMask,
          const Boolean   useOfHcs,
          const Boolean   ueIsFast,
          const UHCS_PRIO highestCellPriority,
          const UHCS_PRIO lowestCellPriority,
          const UHCS_PRIO highestAmongLower,
          const Boolean   leavingDch,
          const UHCS_PRIO reference,
          Boolean         rankAll);
#endif

UHCS_PRIO CsrrkThereIsCellWithLowerHCS(UHCS_PRIO servingHCS,
                                       CellIdMask cellsToRankMask,
                                       CsrCellTypes cellType);

CsrCell CsrrkGetBestRankingCell (Int8 indexInRankList);

void CsrrkSortRankedList(Boolean tReselectionMustBeConsidered, Boolean servCellHasBeenRanked); /* ********** */

CsrCellsReportBitMask
      CsrrkGetSuitabilityReport (const CsrCellsReportBitMask reportRequestMask);

#if ! defined (UPGRADE_EXCLUDE_2G)
void  CsrrkGetBestRankedGsmCells (
          const CsrRankedCellsListSearchType         searchType,
                Int8                         * const numberOfTopGsmCells_p,
                CsrGsmNCellInfo             ** const arrayOfTopGsmCells_p);
#endif

/* ********** added begin */
Boolean CsrrkTryToRevertToGsmCell(Int8 id);

void CsrrkPrintSortedRankedListElement(CsrrkRankingListElement *listElem_p, Int8 i);

void CsrrkRemoveElementFromRankingList(CsrrkRankingListElement *elementToRemove);
/* ********** added end */

#if defined (DEVELOPMENT_VERSION)
void  CsrrkDebugPrintOutRankingList (const Int8 rankingNumber);
#endif

SignedInt16 CsrrkCalcSrxlev (
            CsrCellSelectionInfo                   *cellSelectionInfo_p,
            SignedInt16                             qMeas,
            T_UPriorityLevel_radioAccessTechnology  radioAccessTechnologyType);

SignedInt16 CsrrkCalcSqual (Boolean                 q_QualMinPresent,
                            SignedInt16             q_QualMin,
                            SignedInt16             qMeas);

Boolean CsrrkRankAbsolutePrioInterFreqCells (
              Int8                                    sCellPriority,
              Boolean                                 sCellLessThanThresh,
              Boolean                                 sCellQualLessThanThresh,
              CsrPriorityInfoList                   * priorityInfoList_p,
              Boolean                                 tReselectionMustBeHandled);

Boolean CsrrkRankAbsolutePrioGsmCells (
              Int8                                    sCellPriority,
              Boolean                                 sCellLessThanThresh,
              Boolean                                 sCellQualLessThanThresh,
              CsrPriorityInfoList                   * priorityInfoList_p,
              Boolean                                 tReselectionMustBeHandled);

Boolean CsrrkRankAbsolutePrioLteCells (
              Int8                                    sCellPriority,
              Boolean                                 sCellLessThanThresh,
              Boolean                                 sCellQualLessThanThresh,
              CsrPriorityInfoList                   * priorityInfoList_p,
              Boolean                                 tReselectionMustBeHandled);

CsrCell CsrrkGetBestRankingPrioCell (const Boolean tReselectionMustBeConsidered,
                                           Int8    indexInRankList);

void  CsrrkGetBestRankedLteCells (
            const CsrRankedCellsListSearchType         searchType,
                  Int8                         * const numberOfTopLteCells_p,
                  CsrEutraCellInfo            ** const arrayOfTopLteCells_p);

/* CSRRMD functions (MOBILITY DETECTOR) */
void  CsrrmdActivateMobilityDetector (const Boolean ueMobilityStateMustBeInitialised);

void  CsrrmdDeactivateMobilityDetector (void);

void  CsrrmdCsrrHasReselectedACell (void);

void  CsrrmdTcrMaxTimerExpired (CsrTimerIdentity timerId);

void  CsrrmdTcrMaxHystTimerExpired (CsrTimerIdentity timerId);

void CsrrmdSetMobilityState(Boolean isHighMobility);

void CsrrmdChangeHcsMode(void);

Boolean CsrrmdUeIsFast (void);

/* void CsrrChangeOfUeSpeedEvent (void); NOTE: this function should be
   moved from csrr to csrrmd. */

/* CSRRM functions (MONITOR SET DETERMINATION) */
void  CsrrmInit (void);

void  CsrrmSetDeterminationModeTo (CsrMonitorSetDeterminationMode mode);

void  CsrrmDetermineMonitorSet (void);

CellIdMask
      CsrrmInterFreqCellsInMonitorSet (void);

CellIdMask CsrrmIntraFreqCellsInMonitorSet (void);

Int8  CsrrmNoOfCellsInMonitorSetOfType (CsrCellTypes cellType);

#if ! defined (UPGRADE_EXCLUDE_2G)
CellIdMask
      CsrrmGsmCellsInMonitorSet (void);
#endif

Boolean CsrmmMonitorSetIsEmpty (void);

FreqIdMask CsrrmLteFreqsInMonitorSet (void);

void  CsrrmClearLteFreqsInMonitorSet (void);

Int8  CsrrmGetNumOfLteHighPrioLayers (void);

Int8  CsrrmGetNumOfFddHighPrioLayers (void);

Int8  CsrrmGetNumOfGsmHighPrioLayers (void);

void CsrrInitStoredDedicatedPrio (void); /* ********** added */

/* CSRDB functions (CELL SELECT/RESELECT DATABASE) */
Boolean CsrdbAreGsmCellsPresentInCil (void);    /* ********** added */

void  CsrdbGroupSCellUmtsCellInfoInto (UmtsCell       * const umtsCell_p,
                                       CsrDataBaseType  const dbType);

void  CsrdbInitUmtsCellInfoFor (UmtsCell * const umtsCell_p);

void  CsrdbInitUInterFrequencyCellInfo (
          UInterFrequencyCell * const interFreqCellInfo_p);

void  CsrdbClearCellInfoList (CsrDataBaseType dbType);

#if ! defined (UPGRADE_EXCLUDE_2G)
UFrequency_Band
      CsrdbGetBandMode (CsrDataBaseType dbType);
#endif

CsrCellAccessRestrictions
      *CsrdbGetCellAccessRestrictions (CsrDataBaseType dbType);

UCellIdentity
      CsrdbGetCellIdentity (CsrDataBaseType dbType);

void  CsrdbGetCellIdMasks (
#if ! defined (UPGRADE_EXCLUDE_2G)
                CellIdMask      * const gsmCellIdMask_p,
#endif
                CellIdMask      * const intraFreqCellIdMask_p,
                CellIdMask      * const interFreqCellIdMask_p);

CsrSCellInfo
      *CsrdbGetCellInfo (CsrDataBaseType dbType);

void CsrdbExtractSib11_12NcellLists (
          const CsrDataBaseType                 database,
          const CsrDataBaseType                 referenceSCellDatabase,
          const UMeasurementControlSysInfo_use_of_HCS * const si_measurementControlSysInfo_p,
          const Boolean                         sib11,
          const USysInfoType11 * const          sib11_p,
          const USysInfoType12 * const          sib12_p);

void CsrdbExtractSib11bisNcellLists (
          const CsrDataBaseType                 database,
          const CsrDataBaseType                 referenceSCellDatabase,
          const USysInfoType11bis      * const  sib11bis_p);

void  CsrdbGetCellInfoLists (
          const CsrDataBaseType             database,
#if ! defined (UPGRADE_EXCLUDE_2G)
                UGsmCell            * const newGsmCellList,
#endif
                UmtsCell            * const newIntraFreqCellList,
                UInterFrequencyCell * const newInterFreqCellList);

Boolean
      CsrdbIntraFreqNCellListIsDifferent (
          const CsrDataBaseType               dbType,
          const CsrIntraFreqNCellList * const newIntraFreqCellList);

Boolean
      CsrdbInterFreqNCellListIsDifferent (
          const CsrDataBaseType               dbType,
          const CsrInterFreqNCellList * const newInterFreqCellList);

#if ! defined (UPGRADE_EXCLUDE_2G)
Boolean
      CsrdbGsmNCellListIsDifferent(
          const CsrDataBaseType         dbType,
          const CsrGsmNCellList * const newGsmCellList);
#endif

Boolean
      CsrdNCellListIsDifferent (
          const CsrDataBaseType               dbType,
#if ! defined (UPGRADE_EXCLUDE_2G)
          const CsrGsmNCellList       * const newGsmCellList,
#endif
          const CsrIntraFreqNCellList * const newIntraFreqCellList,
          const CsrInterFreqNCellList * const newInterFreqCellList);

void  CsrdbActionsOnSwappingDatabases (const Boolean cellFreqChanged);

CsrCellReselectionInfo
      *CsrdbGetCellReselectionInfo (CsrDataBaseType dbType);

CsrCellSelectionInfo
      *CsrdbGetCellSelectionInfo (CsrDataBaseType dbType);

#if ! defined (UPGRADE_EXCLUDE_2G)
void CsrdbGetGsmCellsFreqListInfo (
    const CsrDataBaseType database,
    GsmCellInformation * const gsmCellListToBuild_p);
#endif

void CsrdbGetFddCellsFreqListInfo (
    const CsrDataBaseType database,
    FddCellInformation * const fddCellInformation);

CsrHcsInfo *CsrdbGetHcsInfo (void);

UT_CRMaxInt *CsrdbGetNonHcsMobilitySpeedParams (void);

CsrInterFreqNCellInfo
      *CsrdbGetInterFreqNCellInfo (CsrDataBaseType dbType);

#if ! defined (UPGRADE_EXCLUDE_2G)
CsrGsmNCellInfo
      *CsrdbGetGsmNCellInfo (CsrDataBaseType dbType);
#endif

CsrEutraFreqInfoList *CsrdbGetLteNCellInfo (CsrDataBaseType dbType);

#if defined (UPGRADE_RR_MEM_OPT)
CsrPriorityInfoList *CsrdbGetRriorityInfoList (CsrDataBaseType dbType);
#endif
void UrrCsrdbGetFddLteEuarfcnList(const CsrDataBaseType          dbType,
                                       LteEuarfcnList   * const lteFreqInfo_p);

CsrIntraFreqNCellInfo
      *CsrdbGetIntraFreqNCellInfo (CsrDataBaseType dbType);

CsrIntraFreqNCellInfo *CsrdbGetIntraDetectedFreqNCellInfo (CsrDataBaseType dbType);

CsrInterFreqNCellList
      *CsrdbGetInterFreqNCellList (CsrDataBaseType dbType);

#if ! defined (UPGRADE_EXCLUDE_2G)
CsrGsmNCellList
      *CsrdbGetGsmNCellList (CsrDataBaseType dbType);
#endif

CsrIntraFreqNCellList
      *CsrdbGetIntraFreqNCellList (CsrDataBaseType dbType);

#if defined (L1_SUPPORT_IDLE_INTRA_DETECT)  /*********** add*/
CsrIntraFreqNCellList                                                          //-+ ********** 16-Apr-2012 +-
      *CsrdbGetIntraDetectedFreqNCellList (CsrDataBaseType dbType);            //-+ ********** 16-Apr-2012 +-
#endif /*********** add*/

UInterFreqCellID
      CsrdbGetNextFreeInterFreqCellInfoPos (
          CsrInterFreqNCellList  *csr_interFreqNCellList_p);

UIntraFreqCellID
      CsrdbGetNextFreeIntraFreqCellInfoPos (
          CsrIntraFreqNCellList  *csr_intraFreqNCellList_p);

#if ! defined (UPGRADE_EXCLUDE_2G)
UInterRATCellID
      CsrdbGetNextFreeGsmCellInfoPos (
          CsrGsmNCellList  *csr_gsmNCellList_p);

void  CsrdbGetGsmCellsInCil (
          const Int8              requestedNumberOfCells,
                Int8             *cellsFound_p,
                CsrGsmNCellInfo **destinationGsmCell_p);
#endif

CsrUSysInfo
      *CsrdbGetPointerToDatabase (CsrDataBaseType dbType);
#if defined (UPGRADE_RR_MEM_OPT)
void CsrdbAllocAndInitDB (CsrDataBaseType dbType);
void CsrdbFreeAllDynamicDB (void);
void  CsrdbFreeDynamicDB (CsrDataBaseType dbType);
CsrUSysInfo *CsrdbGetPointerToDatabaseWithoutAlloc (CsrDataBaseType dbType);
#endif //UPGRADE_RR_MEM_OPT

T_UCellSelectReselectInfoSIB_3_4_cellSelQMeas
      CsrdbGetQMeas (CsrDataBaseType dbType);

UUMTS_CellInformation
      *CsrdbGetUumtsCellInfo (CsrDataBaseType dbType);

void  CsrdbGetDatabases (
          CsrUSysInfo ** const database1_p,
          CsrUSysInfo ** const database2_p,
          CsrUSysInfo ** const database3_p,
          CsrUSysInfo ** const database4_p);

void  CsrdbInit (CsrDataBaseType dbType);

void  CsrdbInitCellSelectionInfo (CsrCellSelectionInfo *cellSelectionInfo_p);

void  CsrdbInitDatabase (CsrDataBaseType dbType);

void CsrdbInitHcsNeighCellInfoECN0(
           CsrNCellHcsInfo                       * const csrHcsNeighCellInfo_p,
     const UHCS_NeighbouringCellInformation_ECN0 * const sourceCellInfo_p,
     UCellSelectReselectInfo_v590ext             * UCellSelectReselectInfo_v590ext_p);

void CsrdbInitHcsNeighCellInfoRSCP(
           CsrNCellHcsInfo                       * const csrHcsNeighCellInfo_p,
     const UHCS_NeighbouringCellInformation_RSCP * const sourceCellInfo_p,
     UCellSelectReselectInfo_v590ext             * UCellSelectReselectInfo_v590ext_p);

#if ! defined (UPGRADE_EXCLUDE_2G)
void CsrdbInitHcsNeighCellInfoGSM (
           CsrNCellHcsInfo                       * const csrHcsNeighCellInfo_p,
     const UHCS_NeighbouringCellInformation_RSCP * const sourceCellInfo_p,
     UCellSelectReselectInfo_v590ext             * UCellSelectReselectInfo_v590ext_p);
#endif

void  CsrdbInitNCellReselectionInfo (
          CsrNCellReselectionInfo *cellReselectionInfo_p);

/* CQ00034489 begin */
void  CsrdbInvalidateServingCellInfo (Boolean shouldEraseScellPlmn);
/* CQ00034489 end */

void  CsrdbReinstateServingCellInfo (const CsrDataBaseType dbType);

CsrIntraFreqNCellInfo *CsrdbGetServCellInIntraFreqList (
          const CsrDataBaseType  dbType);

void  CsrdbStoreServCellMeasurements (
          const CsrDataBaseType dbType,
          const SignedInt16     cpichEcN0,
          const SignedInt16     cpichRscp);

void  CsrdbStoreAccessRestrictionsSib3_4 (
          const UCellAccessRestriction     * const uCellAccessRestriction_p,
                CsrCellAccessRestrictions  * const accessRestrictions_p);

void  CsrdbStoreCellInformation (
          const CsrDataBaseType         dbType,
          const UUARFCN                 uarfcn_dl,
                UmtsCell        * const umtsCell_p);

void CsrdbStoreCellReselectionSib3_4 (
          const UCellSelectReselectInfoSIB_3_4 * const uCellSelectReselectInfoSIB_p,
                CsrCellReselectionInfo         * const cellReselectionInfo_p,
          const Boolean                                pchFachExtensionsPresent,
          const UCellSelectReselectInfoPCHFACH_v5b0ext
                                               * const pchFachExtensions_p);

void CsrdbStoreCellSelectionSib3_4 (
          const UCellSelectReselectInfoSIB_3_4     * const uCellSelectReselectInfoSIB_p,
          const UCellSelectReselectInfoSIB_3_4_fdd * const fddInfo_p,
                CsrCellSelectionInfo               * const cellSelectionInfo_p,
          const Boolean                                    deltaQExtensionsPresent,
          const UCellSelectReselectInfo_v590ext    * const deltaQExtensions_p,
          const Boolean                                    cell_v770extPresent,
          const USysInfoType3_v770ext_IEs          * const cell_v770ext);

void  CsrdbStoreHCSInfoSib3_4 (
          const UCellSelectReselectInfoSIB_3_4 * const uCellSelectReselectInfoSIB_p,
          const UHCS_ServingCellInformation    * const uHcsInfo_p,
                CsrCellReselectionInfo         * const cellReselectionInfo_p,
                CsrHcsInfo                     * const hcsInfo_p,
          const Boolean                                 deltaQExtensionsPresent,
          const UCellSelectReselectInfo_v590ext * const deltaQExtensions_p);

void  CsrdbStoreInterRATmeasInd (
          const CsrDataBaseType                 database,
          const UFACH_MeasurementOccasionInfo_inter_RAT_meas_ind
                                        * const interRATmeasIndicators_p);

void  CsrdbStoreNCellSearchInfoSib3_4 (
          const UCellSelectReselectInfoSIB_3_4_fdd * const fddInfo_p,
                CsrNCellSearchInfo                 * const csrNCellSearchInfo_p);

void  CsrdbStoreAndHandleDsacRestrictions(
          const USysInfoType3    * const sib3_p,
                CsrDataBaseType         dbType);

/* **********, added begin */
void  CsrdbStoreAndHandlePpacRestrictions(
          const USysInfoType3    * const sib3_p,
                CsrDataBaseType         dbType);
/* **********, added end */

#if ! defined (UPGRADE_EXCLUDE_2G)
void  CsrdbStoreNCellRatListSib3_4 (
          const UCellSelectReselectInfoSIB_3_4_fdd * const fddInfo_p,
                CsrRatList                         * const ratList_p);
#endif

void  CsrdbStoreSib12Data (const CsrDataBaseType database);

void  CsrdbMergeSib11withSib12 (const CsrDataBaseType originSib12Database);

void  CsrdbSwapDatabases (void);

void  CsrdbCopyDataBase (
          const CsrDataBaseType sourceDb,
          const CsrDataBaseType destinationDb);

void  CsrdbSetCellFlagsMask (
                CsrCell       * const cell_p,
          const CsrCellFlags          flag);

void  CsrdbResetCellFlags (const CsrCellFlags flags);
void CsrdbResetNonLteCellFlags (const CsrCellFlags flags); /* ********** added */   

CsrCellFlags CsrdbGetCellFlagsMask (CsrCell * const cell_p);

void CsrdbSetRankedHigherThanServing (CsrCell * const cell_p, Boolean valueToSet);          //-+ ********** 16-Apr-2012 +-

Boolean CsrdbFddCellIsInIntraFPartOfCil (
          const CsrDataBaseType                 dbType,
          const UUARFCN                         uarfcnToFind,
          const UPrimaryScramblingCode          pscToFind,
          const UUARFCN                         servCellFreq,
                CsrIntraFreqNCellInfo  ** const foundCell_p,
                Boolean                         onlyUarfcn);

Boolean CsrdbFddCellIsInInterFPartOfCil (
          const CsrDataBaseType                 dbType,
          const UUARFCN                         uarfcnToFind,
          const UPrimaryScramblingCode          pscToFind,
                CsrInterFreqNCellInfo  ** const foundCell_p,
                Boolean                         onlyUarfcn);

#if ! defined (UPGRADE_EXCLUDE_2G)
Boolean CsrdbCellIsInGsmFPartOfCil (
          const CsrDataBaseType          dbType,
                CsrGsmNCellInfo  * const gsmCell_p,
                CsrGsmNCellInfo ** const foundCell_p);
#endif   /* UPGRADE_EXCLUDE_2G */

void  CsrdbAddIntraFNCellToDb (
          const CsrDataBaseType               dbType,
                CsrIntraFreqNCellInfo * const intraFreq_p,
          const UUARFCN                       uarfcn);

void  CsrdbAddInterFNCellToDb (
          const CsrDataBaseType               dbType,
                CsrInterFreqNCellInfo * const interFreq_p);

#if ! defined (UPGRADE_EXCLUDE_2G)
void  CsrdbAddGsmFNCellToDb (
          const CsrDataBaseType          dbType,
                CsrGsmNCellInfo  * const gsmCell_p);
#endif   /* UPGRADE_EXCLUDE_2G */

#if defined (DEVELOPMENT_VERSION)
CsrCellId
      CsrdbGetCellId (CsrCell * const cell_p);

void  CsrdbDisplayCellInfoList (const CsrDataBaseType dbType);

void  CsrdbDisplayServingCellInfo (const CsrDataBaseType dbType);

void  CsrdbDisplaySearchSetupInfo (const Boolean plmnSearch);
#endif /* DEVELOPMENT_VERSION */

void  CsrdbCompileListOfIntraFreqPscsFromCil (
          const CsrDataBaseType                 dbType,
                UUMTS_FrequencyListInfo * const listElement_p);

Int8  CsrdbCompileListOfInterFreqsAndPscsFromCil (
          const CsrDataBaseType dbType,
          UUMTS_FrequencyListInfo * const freqArray_p,
          const Int16   freqsArrayLength);

void  CsrdbSib11_12McrUpdate (
#if ! defined (UPGRADE_EXCLUDE_2G)
          CellIdMask                oldGsmCellIdMask,
          CellIdMask                newGsmCellIdMask,
          UGsmCell                  *newGsmCellList,
#endif
          CellIdMask                oldIntraFreqCellIdMask,
          CellIdMask                oldInterFreqCellIdMask,
          CellIdMask                newIntraFreqCellIdMask,
          CellIdMask                newInterFreqCellIdMask,
          UmtsCell                  *newIntraFreqCellList,
          UInterFrequencyCell       *newInterFreqCellList);

Boolean CsrdbIsHcsUsed (void);

Boolean CsrdbGetInterFreqFDDmeasIndicator (const CsrDataBaseType dbType);

UT_Reselection_S CsrdbGetTReselection (const CsrDataBaseType database);

UT_Reselection_S CsrdbGetTReselectionForModeAndState (
          const CsrDataBaseType      database,
          const UrrSmcModeAndState * newModeAndState);

void CsrdbStoreSib12 (const CsrDataBaseType referenceSCellDatabase,
                            const USysInfoType12 * const sib12_p);

/* ********** - add begin */
#if defined UPGRADE_CSG1
void CsrdbStoreSib20(const USysInfoType20 * const sib20_p, const CsrDataBaseType dbType);
#endif //UPGRADE_CSG1
/* ********** - add end */

void CsrdbStoreSib11 (const USysInfoType11 * const sib11_p, Boolean sib11bisExistFlag);  /* CQ00045722 */

void CsrdbStoreSib11bis (const USysInfoType11bis * const  sib11bis_p);

/* CQ00068365 added begin */
Band UrrCsrdbGetGsmBand(
          const UBCCH_ARFCN     arfcn,
          const UFrequency_Band gsmBandIndicator);
/* CQ00068365 added end */

void  CsrdbExplicitlyBarCandidateIfNeeded (
          const CsrDataBaseType               database,
          const UUARFCN                       uarfcn,
          const UPrimaryScramblingCode        psc,
          const UT_Barred                     duration);

void  CsrdbInitPriorityInfo (CsrPriorityInfoList  *priorityInfoList_p);

void  CsrdbInitEutraFreqInfo (CsrEutraFreqInfoList *eutraFreqInfoList_p);

void  CsrdbUpdateUtraPriorityInfoList (
          const UUTRA_PriorityInfoList  * utraPriorityInfoList_p,
          const Int8                      numOfFddFreqs,
          const UUTRAN_FDD_Frequency    * utraFddFreq_p,
                CsrPriorityInfoList     * priorityInfoList_p);

void  CsrdbUpdateUtraPriorityInfoListV920ext (
          const UUTRA_PriorityInfoList_v920ext  * utraPriorityInfoListv920ext_p,
                CsrPriorityInfoList     * priorityInfoList_p);

void  CsrdbUpdateGsmPriorityInfoList  (
          const Int8                                 numOfGsmCellGroup,
          const UGSM_PriorityInfo                   *gsm_PriorityInfo_p,
                CsrPriorityInfoList                 *priorityInfoList_p);

void  CsrdbUpdateEutraPriorityInfoList  (
          const Int8                                         numOfEutraFreq,
          const UEUTRA_FrequencyAndPriorityInfo            * eutraFreqPriorityInfo_p,
          const Int8                                         numOfEutraFreq_v920ext,
          const UEUTRA_FrequencyAndPriorityInfo_v920ext    * eutraFreqPriorityInfo_v920ext_p,
// ********** begin
#if defined(UPGRADE_4G_FROM_3G_MFBI)  
          const USysInfoType19_va80NonCriticalExtensions   *va80NonCriticalExtensions, // ********** add
#endif 
// ********** end        
                CsrPriorityInfoList                        * priorityInfoList_p,
                CsrEutraFreqInfoList                       * eutraFreqInfoList_p);

void  CsrdbUpdateSysPriorityInfo (const CsrSib19        * const sib19_p,
                                        CsrDataBaseType         dbType);

void  CsrdbUpdateDecicatedPriorityInfo (
          const UPriorityLevelList      * const dedicatePriorityInfoList_p,
                CsrPriorityInfoList     *       priorityInfoList_p);

void  CsrdbStoreIratDedicatedPrio (const IratDedicatedPrio * const dedicatedPrio_p);

Boolean CsrdbGetIratDedicatedPrio (IratDedicatedPrio * const dedicatedPrio_p);

Boolean CsrdbIsAbsolutePrioritiesUsed (const CsrDataBaseType dbType);

void  CsrdbUpdateNcellListPrioInfo (CsrPriorityInfoList * priorityInfoList_p);

void  CsrdbSib19McrUpdate (CsrEutraFreqInfoList * lteFreqInfoList_p);

Boolean CsrdbPriorityInfoPresentForLayers (
              CsrPriorityInfoList                    *priorityInfoList_p,
              T_UPriorityLevel_radioAccessTechnology  radioAccessTechnologyType,
              Boolean                                 checkThreshold);
/* ********** begin */
/*void CsrStopAllLteTreselectionTimers (void);*/
void CsrStopLteTreselectionTimers (FreqIdMask freqIdMask);
/* ********** end */

void CsrdbClearLteCellInfoList (CsrDataBaseType dbType);    /* ********** */

/* ********** begin */
void CsrInitEutraCellBarredList(void);
void CsrInsertEutraCellToBarredList(const Earfcn earfcn, //********** change  UEARFCN to Earfcn
                                           const UEUTRA_PhysicalCellIdentity pci,
                                           const Int16 duration,
                                           const KernelTicks currentTime);
void CsrDeleteEutraCellFromBarredList(const Int8 cellIndex);
Int8 CsrGetNextFreePosOfEutraCellBarredList(void);
void CsrGetMinBarredCellTimerDuration(KernelTicks * const minDuration_p, const KernelTicks currentTime);
Boolean CsrEutraCellIsBarredList(const Earfcn rankearfcn, //********** change  UEARFCN to Earfcn
                                      const UEUTRA_PhysicalCellIdentity rankpci);
void CsrEutraCellBarredTimerExpired(const CsrTimerIdentity timerId);
Int8  CsrFindStartBarredTimerOldestCellPosInBarredList(void);
/* ********** end */
void CsrDeleteEutraSameFreqCellsInBarredList(const Earfcn earfcn);    /* ********** add */ //********** change  UEARFCN to Earfcn
Boolean CsrEutraCellIsBarredListBy3GSIM(const Earfcn rankearfcn);/*********** add*/ //********** change  UEARFCN to Earfcn


#if ! defined (UPGRADE_EXCLUDE_2G)
/* CSRTU functions (CELL CHANGE ORDER TO UTRAN) */

void  CsrtuInitManager (void);

//void  CsrtuIntSigRrcConnEstablishedEvent (void);

void  CsrtuCellChangeOrderToUtranReq (GrrRcReselectToUmtsReq * const signal_p);

void  CsrtuUeModeStateChangeEvent (
          const UrrSmcModeAndState *newModeAndState_p,
          const UrrSmcModeAndState *currentModeAndState_p);

void  CsrtuCampedOnCellOutcomeEvent (
          const Boolean                       successfulCamping,
                UUMTS_CellInformation * const infoOfCellCampedOn_p);

void  CsrtuUmphReselectToUmtsFailCnf (void);

void  CsrtuT3174expiryEvent (CsrTimerIdentity timerId);


/* CSRFU functions (CELL CHANGE ORDER FROM UTRAN) */

void  CsrfuInit (const Boolean initPointerToCcofuIes);

CsrfuValidationOutcome CsrfuValidateCCOFromUtran (
          const UCellChangeOrderFromUTRAN      * const changeOrder_p,
                T_UInterRAT_ChangeFailureCause * const ccofuFailCause_p,
                UProtocolErrorCause            * const protocolFailCause_p);

Boolean CsrfuStoreCCOFUInfoElements (
          const UCellChangeOrderFromUTRAN   * const changeOrder_p);

void  CsrfuCellChangeOrderFromUtranEvent (
                UrrSmcModeAndState * const modeAndState_p);

void  CsrfuBuildAndSendCOFromUtranFail (
          const URRC_TransactionIdentifier     receivedTransactionId,
          const T_UInterRAT_ChangeFailureCause failureCause,
          const UProtocolErrorCause            protocoErrorCause);

void  CsrfuGrrRcReselectToGsmCnfEvent (GrrRcReselectToGsmCnf * const signal_p);

void  CsrfuIntSignalAirSignalSendStatusEvent (void);

/* PTK_CQ00239728 begin */
void CsrrShortTReselectionTimerExpired (const CsrTimerIdentity timerId);
/* PTK_CQ00239728 end */

void  CsrfuDeallocAllDynamicMemory (void);

void  CsrfuCphyCellSelectIndEvent (CphyCellSelectInd * const signal_p);

void  CsrfuCphySuspendIndEvent (void);

void  CsrfuCphySynchIndEvent (void);

void  CsrfuCphyOutOfSynchIndEvent (void);

void  CsrfuCellUpdateOccurredEvent (void);

void  CsrfuUeModeStateChangeEvent (
          const UrrSmcModeAndState *newModeAndState_p,
          const UrrSmcModeAndState *currentModeAndState_p);

void  CsrfuCcofuGoAheadFromRbcEvent (
          UrrInternalSignalReselectToGsmCnf * const sig_p);

/* CQ00002028, begin */
void  CsrResetCcoVariable (void);
/* CQ00002028, end */
#endif

Boolean CsrCanUEInitiateReselection(void);

void    CsrSetMonitorFlag(CsrCellTypes monitorType, SignedInt8 value);

void CsrsStartIcsCycle (ActivationTime activationTime, CsrSource source,
                                                Int16 numberOfFddFrequencies, Boolean clearBestCellSoFar, Boolean lastIntraSearchCycle); /* ********** added lastIntraSearchCycle */


void    CsrIncMonitorFlag(CsrCellTypes monitorType);

void    CsrClearReselectionConditionFlags(void);

Boolean CsrrCheckReselectionConditions(
    CsrCell *latestBestRankedCell_p,
    Boolean servCellHasBeenRanked);

void CsrHandleIntSigRrcConnRelProcessed(void);


void CsrUpdateCellEvaluationFlags(Int16 numRequestedPlmns, Plmn *requestedPlmns);

extern CsrStoredTableIndex CsrUarfcnToTableIndex (const UUARFCN uarfcn, FddBandsMask phySupportedFddBandsBits);

extern UUARFCN CsrTableIndexToUarfcn (CsrUarfcnScanResultsTable *scanResultsTable_p,const CsrStoredTableIndex index);

extern void CsrRemoveUarfcnsThatWillNotGiveService (    const   UUARFCN uarfcn_dl,
                                                                const   FddBandsMask    phySupportedFddBandsBits,
                                                                        CsrUarfcnScanResultsTable *scanResultsTable_p,
                                                                        CsrUarfcnScanOrderTable   *scanOrderTable_p,//********** modify
                                                                        Boolean removeAlsoGcf);//********** modify

extern void CsrAddUarfcnToScanOrderTable(   const   UUARFCN dl_uarfcn,
                                                    const   RssiScanGrade rssiScanGrade,
                                                            CsrUarfcnScanOrderTable *scanOrderTable_p);

extern void CsrRemoveUarfcnFromScanOrderTable(const UUARFCN uarfcn_dl,
                                                        CsrUarfcnScanOrderTable   *scanOrderTable_pm,
                                                        Boolean removeTillUarfcn,
                                                        Boolean suspendAction);

extern Int16 CsrCalcScanResultsTableSize(FddBandsMask phySupportedFddBandsBits);

extern void CsrCalcUarfcnsRawGradesOffsets(
                                                const   FddBandIndex            bandIndex,
                                                const   Int16                   numberOfElements,
                                                        UtraRssiReport          *report_p,
                                                        UtraRssiReportRanking   *ranking_p);


extern void CsrCalcRangeUarfcnsRawGrades(   const   SignedInt16             rssiScanThreshold,
                                                    const   Int16                   numberOfElements,
                                                            UtraRssiReport          *report_p,
                                                            UtraRssiReportRanking   *ranking_p,
                                                            FddBandsMask            phySupportedFddBandsBits,
                                                            CsrUarfcnScanResultsTable *scanResultsTable_p);

extern void CsrCalcAddUarfcnsRawGrades( const   SignedInt16     rssiScanThreshold,
                                                const   FddBandsMask            phySupportedFddBandsBits,
                                                const   FddBandIndex            bandIndex,
                                                const   Int16                   numberOfElements,
                                                        UtraRssiReport          *report_p,
                                                        UtraRssiReportRanking   *ranking_p,
                                                        CsrUarfcnScanResultsTable *scanResultsTable_p);

extern void CsrCalcDiscreteUarfcnsListRawGrades(const   SignedInt16             rssiScanThreshold,
                                                        const   Int16                   numberOfElements,
                                                                UtraRssiReport          *report_p,
                                                                UtraRssiReportRanking   *ranking_p);

extern RssiScanRank CsrCalculateUarfcnRanking(  const   UUARFCN                 currentUarfcn,
                                                    const   RssiScanGrade           currentRssiScanGrade,
                                                            UtraRssiReport          *report_p,
                                                            UtraRssiReportRanking   *ranking_p);

extern Boolean CsrUarfcnInPrimeList (const CsrRssiScanType rssiScanType,
                                        const FddBandIndex   bandIndex,
                                        const UUARFCN uarfcn_dl,
                                        const RssiScanRank rssiScanRank);

void CsrAbortPendingAction(void);

/* ********** added begin */
Boolean CsrsNeedToPostponeIntraSearch(Int16 numberOfFddFrequencies);
void CsrsRemoveIntraCellsFromSearchList (
         UUMTS_FrequencyListInfo        *intraList_p,
         CsrListOfFddFreqsToSearch      *searchList_p);
void CsrInitListOfFreqsToSearch (CsrListOfFddFreqsToSearch *listOfFrequesToSearch_p);
void CsrBuildConnectedMmList (void);
/* ********** added end */

FddBandsMask CsrpGetSavedBandsToScan(void);
/* ********** add begin */
#if defined UPGRADE_CSG1
FddBandsMask CsrcsgsGetSavedBandsToScan(void);
#endif //UPGRADE_CSG1
/* ********** add end */

#if !defined (L1_SUPPORT_GSM_FG_PLMN_SEARCH) && !defined UPGRADE_PLMS /*********** add UPGRADE_PLMS*/
void CsrpHandleFgGsmSearch (RrcPlmnListReq * const plmnListReq_p);
void CsrpHandleModeStateChange (const UrrSmcModeAndState * const newModeAndState_p, 
											const UrrSmcModeAndState * const currentModeAndState_p);
void CsrpHandleGsmFgPlmnSearchFinish (void);
void CsrpHandleGrrrcGsmPlmnListCnf(GrrRcGsmPlmnListCnf* const signal_p);
#endif
void CsrpHandleGrrrcGsmPlmnListInd(GrrRcGsmPlmnListInd* const signal_p);
#if !defined (L1_SUPPORT_GSM_FG_PLMN_SEARCH) && !defined UPGRADE_PLMS /*********** add UPGRADE_PLMS*/
void CsrpAbortFgGsmSearch(void);
/* ********** modify begin */
#if !defined UPGRADE_PLMS 
Boolean CsrpCheckState(CsrpState state);
#else
Boolean CsrpCheckState(CsrpActiveState state);
#endif
/* ********** modify end */
/* ********** add begin */
#if defined UPGRADE_CSG1
Boolean CsrcsgsCheckState(CsrcsgsState state);
#endif // UPGRADE_CSG1
/* ********** add end */

#endif

/* ********** begin */
/* ********** modify begin */
#if !defined UPGRADE_PLMS /* ********** added */
void CsrpHandleLteFgPlmnSearchFinish (void);
void CsrpAbortFgLTESearch(void);
#endif /* ********** added */

void CsrpHandleErrrcLtePlmnListInd(ErrRcLtePlmnListInd* const signal_p);
#if !defined (L1_SUPPORT_GSM_FG_PLMN_SEARCH) && !defined UPGRADE_PLMS /*********** add*/
void CsrpHandleErrrcLtePlmnListCnf(ErrRcLtePlmnListCnf* const signal_p);
#endif /*********** add*/
/* ********** modify end */

#if defined (UPGRADE_PLMS) //********** begin
void CsrpHandleIntSigCphyDeactivateCnf(void);
Boolean CsrGetCsrpFgPlmsInProgress(void); //********** add
Boolean CsrpWaitDeactiveCnfForSearchComplete(void);
#else /* ********** modify begin */
void CsrpHandleFgLTESearch (RrcPlmnListReq * const plmnListReq_p);
void CsrpHandleFgGsmSearch (RrcPlmnListReq * const plmnListReq_p);
#endif //UPGRADE_PLMS /* ********** modify end */
/* ********** add begin */
#if defined UPGRADE_CSG1
//void CsrcsgsAbortFgLTESearch(void);
#endif //UPGRADE_CSG1
/* ********** add end */


/* ********** end */

/************ CSRC *************/

UrrCSRCAction *CsrGetPointerToCSRCAction(void);
UrrCSRSAction *CsrGetPointerToCSRSAction(void);
UrrCSRRAction *CsrGetPointerToCSRRAction(void);
UrrCSRPAction *CsrGetPointerToCSRPAction(void);
/* ********** add begin */
#if defined UPGRADE_CSG1
UrrCSRCSGSAction *CsrGetPointerToCSRCSGSAction(void);
#endif //UPGRADE_CSG1
/* ********** add end */

void CsrHandleCSRCAction(UrrCSRCAction *action_p);
void CsrHandleCSRSAction(UrrCSRSAction *action_p);
void CsrHandleCSRRAction(UrrCSRRAction *action_p);
void CsrSendPrimeStageResults(CsrSearchType searchType);
void CsrHandleCSRPAction(UrrCSRPAction *action_p);
/* ********** add begin */
#if defined UPGRADE_CSG1
void CsrHandleCSRCSGSAction(UrrCSRCSGSAction *action_p);
#endif //UPGRADE_CSG1
/* ********** add end */

void CsrdbUpdateDrxCycleLength (Int16 drxCycleLength);

/********* END CSRC ************/

/* ********** merge from TTC main begin */
void  UrrCsrAbortPlmnSearchForDeact (void);
void CsrpCodeAndSendRrcPlmnListCnf (const PlmnListStatus status
#if !defined(UPGRADE_PLMS_SEARCH_API)/*********** add MACRO*/
                                       #if !defined (UPGRADE_EXCLUDE_2G)
                                               ,const NetworkMode    networkMode
                                       #endif
#endif/*********** add MACRO*/
                                                          );
/* ********** merge from TTC main end */

/* ********** add begin */
#if defined UPGRADE_CSG1
void CsrcsgsCodeAndSendRrcCsgListCnf (const CsgListStatus status
                                       #if !defined (UPGRADE_EXCLUDE_2G)
                                               ,const NetworkMode    networkMode
                                       #endif
                                                          );
#endif //UPGRADE_CSG1								   
/* ********** add end */

#if defined (UPGRADE_DSDS)
void CsrpPlmnSearchAbortForSuspend (void);
Boolean CsrrGetSuspendIndHasArrived (void);
void CsrrIntraFreqReselectionAbort (void);    /* CQ00015514 */
Boolean CsrpIfAwaitPhyDeactivateCnf(void);   /* for CQ00016754 */
/* ********** add begin */
#if defined UPGRADE_CSG1
Boolean CsrcsgsIfAwaitPhyDeactivateCnf(void);   /* for CQ00016754 */
void CsrcsgsCsgSearchAbortForSuspend (void);
#endif //UPGRADE_CSG1
/* ********** add end */

/* CQ00016250 begin */
Boolean CsrsIfAwaitPhyDeactivateCnf(void);
/* CQ00016250 end */
void CsrrInvalidateMeasurementsUponReselection (void);     /* for CQ00017555 */
void CsrsTerminationEvent (void);  /* for ********** */
Boolean UrrCsrrCheckDsSimCellReselectionOnGoing (void);   /* CQ00031540 add */
#endif
void UrrCsrAbortPendignPlmnSearchForDeact (void);  /* for CQ00017469 */
void UrrCsrrSetRedirectionInfo(UrrReleaseRedirectionInfo *redirectionInfo);
void UrrCsrrSetRedirectionInfoValidity(Boolean valid);
void CsrrAbortPlmnSearch(void);/*CQ00025460 add*/
void CsrrInitMeasRscpValue (void);   /* Add for CQ00036516 */
void CsrPrintHplmns (void); /*CQ00049473*/
CsrProcId CsrGetCurrentActiveCsrProcess(void);   /* Add for CQ00044730 */

Boolean UrrCsrChekPlmn (Plmn *plmn_p);/*CQ00051974*/

Boolean CsrdbFddCheckCellIsInIntraFList(
            const CsrDataBaseType                 dbType,
            const UPrimaryScramblingCode          pscToFind);    /*CQ00056084 add */

/* ********** begin */
#if defined (PS_STRESS_TEST)
void CsrCamponModMeasTimerExpired (const CsrTimerIdentity timerId);
void CsrrModifyIntraAndInterFreqMeasurementRx (void);
void SetLteCellIn3G (Int16* value);     /* ********** */
#endif
/* ********** end */

/* ********** begin */
IratReselectCellStatus CsrrMapIrrCellStatusToIratCellStatus (
                                    const GrrRcIrrCellStatus   cellStatusForGrr);
IratReselectCellStatus CsrMapEvaluationOntoToIratCellStatus (const CsrCellEval evaluation);

void CsrrUpdateIratReplyToMm (
                      IratReselectionCnf     * const iratReselectionCnf_p,
                      const IratReselectCellStatus   failureCause,
                      Boolean                        preserveSetTbarred,
                      Int16                          duration);
void CsrrDeallocateLteFallBackInfo (
                CsrrReselectToLteFallBackInfo ** const lteFallBackInfo_pp);
Boolean UrrCsrSelectionFromLtetoUmtsIsRedirection(void);
/* ********** end */
void CsrMoveFromDeactivatedToIdle (void);    /* ********** */
Boolean UrrCsrSelectionFromLtetoUmtsIsOos(void);   /* ********** */
void CsrMoveFromIdleToDeactivated (void);

Boolean UrrCsrRedirectionFromLtetoUmts(void); /*********** add */


/*********** START */
#endif//!defined ERRC_UNIT_TEST
/*********** END */
/* ********** - add begin */
#if defined UPGRADE_CSG1
CsrrkRankingListElement * CsrrkCreateRankingListElement (const CsrCell* const cell_p);

void CsrrkMergeRankingLists(void);

void CsrrkSortPriorityRankedList(Boolean scellRanked, Boolean tReselectionMustBeHandled);

CsrCell CsrCsgGetCsgSelectCell (Int8 indexInRankList);
void CsrCsgGetBestRankedLteCellsInCsgSelect (
                  Int8                         * const numberOfTopLteCells_p,
                  CsrEutraCellInfo            ** const arrayOfTopLteCells_p);
void CsrCsgUpdateMngrWithSelectResult(Boolean isSuccessful);

Boolean CsrCsgGetListOfCsgCellsFromIrrCsgSelectReq(IrrCsgInfo*  rrcCsgSelectReq_p, Int8 *numOfCsgCells, CsrCell *csgCellList);

void CsrCsgCleanUpCsgCellListForCsgSelect (void);

void CsrCsgDetermineAndUpdateCsgCampingType(void);

Boolean CsrCsgIsScellCsgInWl(const CsrDataBaseType database);

void CsrrkSortCsgRankedLists(void);
/* ********** - add START*/
void CsrrmSetIntraFreqDetectedCsgCellsMonitoredSet (CellIdMask value);

CellIdMask CsrrmGetIntraFreqDetectedCsgCellsMonitoredSet (void);

CsrIntraFreqNCellInfo *CsrdbGetIntraFreqDetectedCsgNCellInfo (CsrDataBaseType dbType);

/* ********** - add END*/

/************CSRCSG************/

/*Boolean CsrrkComputeListOfCellsToBeRankedNoHcs(CellIdMask *intraCellsToBeRanked_p, CellIdMask *intraDetectedCellsToBeRanked_p,
                      CellIdMask *interCellsToBeRanked_p, CellIdMask *gsmCellsToBeRanked_p, CellIdMask *intraDetectedCsgCellsToBeRanked_p);

Boolean CsrrkComputeListOfCellsToBeRankedHcsSlow(CellIdMask *intraCellsToBeRanked_p, CellIdMask *intraDetectedCellsToBeRanked_p,
                                                CellIdMask *interCellsToBeRanked_p, CellIdMask *gsmCellsToBeRanked_p);


Boolean CsrrkComputeListOfCellsToBeRankedHcsFast(CellIdMask *intraCellsToBeRanked_p, CellIdMask *intraDetectedCellsToBeRanked_p,
                                                CellIdMask *interCellsToBeRanked_p, CellIdMask *gsmCellsToBeRanked_p);

void CsrrkRankCells(Boolean scellShouldBeRanked, CellIdMask intraCellsToBeRanked, CellIdMask intraDetectedCellsToBeRanked,
                         CellIdMask interCellsToBeRanked, CellIdMask gsmCellsToBeRanked, CellIdMask intraDetectedCsgCellsToBeRanked);

void CsrrkPriorityRankCells(CellIdMask interCellToBePriorityRanked, CellIdMask interDetectedCsgCellToBePriorityRanked, CellIdMask gsmCellToBePriorityRanked,
                                  FreqIdMask eutraFreqsToBePriorityRanked, Int64 eutraCellsToBePriorityRanked[maxNumEUTRAFreqs]);

*/
void CsrrkCsgRankingListCleanUp (void);

void UrrCsrCsgHandleIrrCsgSelectReq (RadioResourceControlEntity *urr_p);

void UrrCsrCsgHandleMsDataChangedInd (RadioResourceControlEntity *urr_p);

//void UrrCsrCsgHandleIrrCellSelectedRsp (RadioResourceControlEntity *urr_p);

void UrrCsrCsgHandleCsgListReq (RadioResourceControlEntity *urr_p);

void UrrCsrCsgHandleCsgListCnf (RadioResourceControlEntity *urr_p);

//void CsrCsgSendIrrFpSysInfoReq(void);

//void UrrCsrCsgHandleIrrFpSysInfoReq (RadioResourceControlEntity *urr_p);

//void CsrCsgSendIrrCsgListResumeReq(IrrCsgListResumeReq *irrCsgListResumeReq_p);

//void CsrCsgSendIrrFpSysInfoResumeReq(IrrFpSysInfoResumeReq *irrFpSysInfoResumeReq_p);

//void UrrCsrCsgHandleIrrFpSysInfoSuspendCnf(RadioResourceControlEntity *urr_p);

//void CsrCsgSendIrrFpSysInfoSuspendReq(IrrFpSysInfoSuspendReq *irrFpSysInfoSuspendReq_p);

//void CsrCsgSendIrrFpSysInfoAbortReq(IrrFpSysInfoAbortReq *irrFpSysInfoAbortReq_p);

//void UrrCsrCsgHandleIrrFpSysInfoAbortReq(RadioResourceControlEntity *urr_p);

//void UrrCsrCsgHandleIrrCsgListSuspendCnf(RadioResourceControlEntity *urr_p);

//void CsrCsgSendIrrCsgListSuspendReq(IrrCsgListSuspendReq *irrCsgListSuspendReq_p);

//void CsrCsgSendIrrCsgListAbortReq(IrrCsgListAbortReq *irrCsgListAbortReq_p);

//void UrrCsrCsgHandleIrrCsgListAbortReq (RadioResourceControlEntity *urr_p);

void UrrCsrCsgHandleMsDataChanged (RadioResourceControlEntity *urr_p);

//void UrrCsrCsgHandleIrrFpSysInfoCnf (RadioResourceControlEntity *urr_p);

//void CsrCsgSendIrrFpSysInfoCnf(IrrFpSysInfoCnf *irrFpSysInfoCnf_p);

//void CsrCsgSendIrrCsgListCnf(IrrCsgListCnf *irrCsgListCnf_p);
void CsrCsgInit(void);

void CsrCsgSendIrrCsgListReq(void);

//Boolean CsrrEutraFrequencyInMonitorSet (UEARFCN earfcn, Int8 * indexInEfil);

//void CsrprUpdateCellInEutraCellInfoList(Int8 indexInEfil, Int16 pci, Boolean updateIsMeasured, Boolean updateIsCsg);

void CsrCsgUpdateScellCsgCampingType(CsgScellCapmingType csgCampingType, Int8 idx);

Boolean CsrCsgIsScellHybridCsg(void);

//void CsrCsgStartCsgSearch(void);
//void  CsrCsgStartFpSysInfoSearch(void);
//void CsrCsgSuspendCsgSearch (void);
//void CsrCsgSuspendFpSysInfoSearch(void);
//void CsrCsgResumeCsgSearch (void);
//void CsrCsgResumeFpSysInfoSearch (void);
//void CsrCsgAbortCsgSearch (void);
//void CsrCsgAbortFpSysInfoSearch (void);

//void CsrrkReSortConsideringCsgCells(FreqIdMask eutraFreqsToBePriorityRanked, Int64 eutraCellsToBePriorityRanked[maxNumEUTRAFreqs]);

void CsrrSortedCsgListCleanup (void);


//void CsrrkFindStrongestEutraCsgCells (FreqIdMask eutraFreqsToBePriorityRanked, Int64 eutraCellsToBePriorityRanked[maxNumEUTRAFreqs]);

void CsrrkReBuildSortedListWhenCampedOnNonCsg (CsrrkRankingListElement *candidateIntraFCsgCell, CsrrkRankingListElement *interFreqCsgCells,
                                 Int8 numOfInterFCsgCells,CsrrkRankingListElement *EutraCsgCells, Int8 numOfEutraCsgCells);

void CsrrkReBuildSortedListWhenCampedOnCsg(Boolean *intraFCandidateCsgCellExists, Boolean *sCellHighestRankedInFreq, CsrrkRankingListElement *candidateIntraFCsgCell,
                                                          CsrrkRankingListElement *EutraCsgCells, Int8 numOfEutraCsgCells);

void UrrCsrCsgAutoCsgSelectReq (RadioResourceControlEntity *urr_p);

void UrrCsrCsgHandleUpdateASMeasResultsReq(IrrUpdateAsMeasResultsReq *urr_p); /* ********** - add modify*/

Boolean CsrCsgIsScellCsg(void);

void CsrCsgClearAutoSearchTempData (void);

Boolean CsrrkIsCsgCandidateCellExist (void);

void UrrCsrCsgHandleRrCsgSelectReq (RadioResourceControlEntity *urr_p);

/********** - Deleted **********/
void UrrCsrRrcCsgListReq (RadioResourceControlEntity *urr_p);
void UrrCsrCsgHandleRrAutoCsgSelectReq (RadioResourceControlEntity *urr_p);

void UrrCsrRrcCsgListCnf(RadioResourceControlEntity *urr_p);

void CsrCsgFindAndMarkLteCellAsNotCsg (Earfcn earfcn,UEUTRA_PhysicalCellIdentity pci);//********** change  UEARFCN to Earfcn

void  CsrdbExplicitlyBarCandidateCsgCell (
          const CsrDataBaseType         database,
          const UUARFCN                 uarfcn,
          const UPrimaryScramblingCode  psc,
          const Int16               duration);

void CsrcsgsSaveSupportLteEuarfcnInList(Earfcn *const freqCellList_p,Int16 *numOfFreq); /* ********** add *///********** change  UEARFCN to Earfcn

#endif //UPGRADE_CSG1
/* ********** - add end */

// ********** begin
Int32 CsrrCalculateTreselectionDuration(CsrCellReselectionInfo *sCellReslectionInfo_p, CsrCell * cell_p);
#if defined(ENABLE_END_OF_DRX_MEAS_IND)
void UrrCsrrHandleEndOfDrxMeasInd(RadioResourceControlEntity *urr_p);
Boolean CsrrGetReselectACellIfNecessayAtEndOfDrx(void);
Int32 CsrrCalculateMaxTimeBetweenEndOfDrxMeasInd(void); // ********** modify
#endif /* ENABLE_END_OF_DRX_MEAS_IND */
// ********** end


/*********** add begin*/
#if defined (UPGRADE_PLMS)
/*********** add begin*/
void CsrpSetabortByReselection (Boolean abortByReselection);
Boolean CsrpGetabortByReselection (void);
void CsrpIrrGetUmtsSibsReq (RadioResourceControlEntity * urr_p);
void CsrpHandleIrrGetUmtsSibsReq (IrrGetUmtsSibsReq *irrGetUmtsSibsReq_p, Boolean backGroundSearch	);
void CsrBuildAndSendIrrGetUmtsSibsCnf (
    Boolean              cellFound,
	Boolean      		 freqBandIndicatorIsSet,
	Int8                 freqBandIndicator,	/* ********** modified */
	Boolean              skipBand);			/* ********** added */

void CsrpCphyNCellBchCnfEvent (UrrInternalSignalNcellBchCnf * const ncellBchCnf_p);
/*********** add end*/
void CsrpHandleIrrGetLteSibsReq (IrrGetLteSibsReq * const irrGetLteSibsReq_p);
void CsrBuildAndSendIrrGetLteSibsCnf (
    Boolean              cellFound,
	EutraBandWidth 		 bandWidth,
	Int8                 freqBandIndicator);

void CsrpPlmsPlmsRrcPlmnListReq (
         RrcPlmnListReq * const plmnListReq_p,
   const CsrSearchType      searchType,
   const Boolean                initialiseSearchParameters);

void CsrpPlmsSetNotScannedBands (RrcPlmnListReq *rrcPlmnListReq_p);// ********** added
#if !defined UPGRADE_PLMS /* ********** added */
void CsrpPlmsDoLtePlmnSearch (RrcPlmnListReq * const plmnListReq_p);
#endif /* ********** added */
void CsrpIrrGetLteSibsReq (RadioResourceControlEntity * urr_p);
void CsrpPlmsPlmnSearchComplete (PlmnListStatus                 savedSearchResult);
#if !defined UPGRADE_PLMS	/* ********** added */
void CsrBuildAndSendIrrPlmnListAbortReq (void);
#endif	/* ********** added */
void CsrpHandleIrrPlmnListAbortCnf (PlmnListStatus searchResult);/*********** add results*/
void CsrpHandleIrrPlmnListCnf (RadioResourceControlEntity * urr_p);
void CsrpHandleIrrUmtsSibsAbortReq (RadioResourceControlEntity * urr_p);
void CsrBuildAndSendIrrUmtsSibsAbortCnf (void);
void CsrpIrrGetGsmSisReq (RadioResourceControlEntity * urr_p);
void CsrpIrrLteSibsAbortReq (RadioResourceControlEntity * urr_p); //********** add
void CsrBuildAndSendIrrLteSibsAbortCnf (void); //********** add
/*********** - modify add*/	
void CsrpIrrGsmSisAbortReq (RadioResourceControlEntity * urr_p);
void CsrBuildAndSendIrrGsmSisAbortCnf (void);
/*********** - modify end*/	
void CsrpPlmsSetLteBandsToScan(RrcPlmnListReq * const plmnListReq_p)  ;//********** add

#if !defined(UPGRADE_PLMS_SEARCH_API)/*********** add*/
Boolean CsrpCheckIfPlmnResultsExist (RrcPlmnListReq * const plmnListReq_p);
void CsrpPlmsReplyCnfImmediately (RrcPlmnListReq * const plmnListReq_p,CsrSearchType      searchType);
#endif /*UPGRADE_PLMS_SEARCH_API*//*********** add*/

void CsrpRepotPlmnSearchResults(PlmnListStatus searchResult);


/*********** - modify start*/	
void UrrCsrpHandleIrrPlmnListSuspendCnf(RadioResourceControlEntity * urr_p);
void UrrCsrSendIrrPlmnListResumeReq(void);
void UrrCsrSendIrrPlmnListSuspendReq(void);
/*********** - modify end*/	
#endif /*UPGRADE_PLMS*/

/*********** add end*/
Boolean UrrCheckBackgroundSearchOngoing (void);    /* ********** moved from UPGRADE_PLMS */
void CsrrReselectDueToOosAwakeL1 (CsrDataBaseType dbType);   /* ********** add */

void CsrGetCurrentPlmnInCellDch (Lai *lai,MultiplePlmnTable *plmnTable_p);    /* ********** added *//*********** add plmnTable_p*/

// ********** begin
#if defined(UPGRADE_4G_FROM_3G_MFBI) 
//void CsrdbInitStoredSib19Data(CsrSCellInfo * scellInfo_p); /*********** remove*/
#endif
// ********** end 

#if defined (UPGRADE_PLMS)
void CsrpCphyFgBchCnf (RadioResourceControlEntity *urr_p);
#if defined (UPGRADE_URR_CSG_USING_PLMS)
Boolean CsrpIsSib5Required (UUARFCN uarfcn, FddBandsMask scannedFddBands, Boolean *skipBand);	
#endif
#endif/*UPGRADE_PLMS*/

/* ********** add begin */
void CsrrStoreDedicatedPrioUponMobilityTo3G (IratDedicatedPrio *dedicatedPrio_p);   
Boolean CsrrFillDedicatedPrioInMobilityReq (IratDedicatedPrio *dedicatedPrio_p);
/* ********** add end */



void CsrAddLteToUmtsOosIcsFreqListIntoSearchList(
        CsrListOfFddFreqsToSearch * const listOfFrequesToSearch_p);    /* ********** add */
void CsrsdbSetSource(CsrSource               source);

Boolean UrrCsrChekPlmnCampOnSBM (void);/*********** add*/

void CsrpSaveSupportLteEuarfcnInList(Earfcn *const freqCellList_p,Int16 *numOfFreq); /*********** add*/ //********** change  UEARFCN to Earfcn

//********** added begin
Boolean UrrCheckLteBandIsSupported (const URadioFrequencyBandEUTRA bandInd);
Boolean UrrCheckEarfcnIsSupported (const Earfcn euarfcn);//********** change  UEARFCN to Earfcn
//********** added end
//********** added begin 
#if defined (UPGRADE_PLMS)
Boolean CsrpWaitDeactiveCnfForSearchComplete(void);
#endif
//********** added end 
Boolean UrrcCsrCheckIfReportScanResultEnable(void);//********** added 

#endif  /* !defined (URRCSRTY_H) */

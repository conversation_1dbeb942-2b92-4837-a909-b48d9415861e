/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/l1/3g.typ/api/inc/upsl1sig.h#3 $
 *   $Revision: #3 $
 *   $DateTime: 2007/03/30 13:53:25 $
 **************************************************************************
 * File Description: Signal Id to Signal structure mapping for shared signal
 *                   interfaces between 3G PS and 3G L1.
 **************************************************************************/

#if defined (UPGRADE_3G)

#if !defined (EXCLUDE_CPHY) // if add new cphy msg, pls update kistacticfilter_w.h 
#if !defined (SIGNAL_MAPPING) //2013.3.21 Jiantao modified for LTE+W+G UE PC VS project
SIG_DEF( SIG_CPHY_DUMMY = CPHY_SIGNAL_BASE,         EmptySignal                     cphy_dummy )
#endif
SIG_DEF( SIG_CPHY_UE_CAPABILITY_CONFIG_REQ,         CphyUeCapabilityConfigReq       cphyUeCapabilityConfigReq )
SIG_DEF( SIG_CPHY_FIND_CELL_REQ,                    CphyFindCellReq                 cphyFindCellReq )
SIG_DEF( SIG_CPHY_FIND_CELL_CNF,                    CphyFindCellCnf                 cphyFindCellCnf )
SIG_DEF( SIG_CPHY_DETECTED_CELL_MEAS_IND,           CphyDetectedCellMeasInd         cphyDetectedCellMeasInd )   /* CQ00082411 added */
SIG_DEF( SIG_CPHY_NEXT_CELL_REQ,                    CphyNextCellReq                 cphyNextCellReq )
SIG_DEF( SIG_CPHY_NEXT_CELL_CNF,                    CphyNextCellCnf                 cphyNextCellCnf )
SIG_DEF( SIG_CPHY_CELL_SELECT_REQ,                  CphyCellSelectReq               cphyCellSelectReq )
SIG_DEF( SIG_CPHY_CELL_SELECT_CNF,                  CphyCellSelectCnf               cphyCellSelectCnf )
SIG_DEF( SIG_CPHY_BCH_REQ,                          CphyBchReq                      cphyBchReq )
SIG_DEF( SIG_CPHY_BCH_SKIP_REQ,                     CphyBchSkipReq                  cphyBchSkipReq )
SIG_DEF( SIG_CPHY_NCELL_BCH_REQ,                    CphyNcellBchReq                 cphyNcellBchReq )
SIG_DEF( SIG_CPHY_NCELL_BCH_CNF,                    CphyNcellBchCnf                 cphyNcellBchCnf )
SIG_DEF( SIG_CPHY_NCELL_BCH_SKIP_REQ,               CphyNcellBchSkipReq             cphyNcellBchSkipReq )
SIG_DEF( SIG_CPHY_PCH_CONFIG_REQ,                   CphyPchConfigReq                cphyPchConfigReq )
SIG_DEF( SIG_CPHY_INITIAL_FACH_CONFIG_REQ,          CphyInitialFachConfigReq        cphyInitialFachConfigReq )
SIG_DEF( SIG_CPHY_FACH_CONFIG_REQ,                  CphyFachConfigReq               cphyFachConfigReq )
SIG_DEF( SIG_CPHY_RACH_CONFIG_REQ,                  CphyRachConfigReq               cphyRachConfigReq )
SIG_DEF( SIG_CPHY_CBS_CONFIG_REQ,                   CphyCbsConfigReq                cphyCbsConfigReq )
SIG_DEF( SIG_CPHY_SCCPCH_CONFIG_REQ,                CphySccpchConfigReq             cphySccpchConfigReq )
SIG_DEF( SIG_CPHY_UL_TRCH_CONFIG_REQ,               CphyUlTrchConfigReq             cphyUlTrchConfigReq )
SIG_DEF( SIG_CPHY_DL_TRCH_CONFIG_REQ,               CphyDlTrchConfigReq             cphyDlTrchConfigReq )
SIG_DEF( SIG_CPHY_DL_TFC_CONFIG_REQ,                CphyDlTfcConfigReq              cphyDlTfcConfigReq )
SIG_DEF( SIG_CPHY_RL_COMMON_SETUP_REQ,              CphyRlCommonSetupReq            cphyRlCommonSetupReq )
SIG_DEF( SIG_CPHY_RL_SETUP_REQ,                     CphyRlSetupReq                  cphyRlSetupReq )
SIG_DEF( SIG_CPHY_RL_RELEASE_REQ,                   CphyRlReleaseReq                cphyRlReleaseReq )
SIG_DEF( SIG_CPHY_CCTRCH_CONFIG_CNF,                CphyCcTrChConfigCnf             cphyCcTrChConfigCnf )
SIG_DEF( SIG_CPHY_CCTRCH_RELEASE_REQ,               CphyCcTrChReleaseReq            cphyCcTrChReleaseReq )
SIG_DEF( SIG_CPHY_SERVING_CELL_MEASUREMENT_REQ,     CphyServingCellMeasurementReq   cphyServingCellMeasurementReq )
SIG_DEF( SIG_CPHY_SERVING_CELL_MEASUREMENT_IND,     CphyServingCellMeasurementInd   cphyServingCellMeasurementInd )
SIG_DEF( SIG_CPHY_STOP_SERVING_CELL_MEAS_REQ,       CphyStopServingCellMeasReq      cphyStopServingCellMeasReq )
SIG_DEF( SIG_CPHY_INTRA_FREQ_CELL_INFO_REQ,         CphyIntraFreqCellInfoReq        cphyIntraFreqCellInfoReq )
SIG_DEF( SIG_CPHY_INTER_FREQ_CELL_INFO_REQ,         CphyInterFreqCellInfoReq        cphyInterFreqCellInfoReq )
SIG_DEF( SIG_CPHY_MONITOR_INTRA_FREQ_CELL_REQ,      CphyMonitorIntraFreqCellReq     cphyMonitorIntraFreqCellReq )
SIG_DEF( SIG_CPHY_MONITOR_INTER_FREQ_CELL_REQ,      CphyMonitorInterFreqCellReq     cphyMonitorInterFreqCellReq )
SIG_DEF( SIG_CPHY_MONITOR_INTRA_FREQ_CELL_IND,      CphyMonitorIntraFreqCellInd     cphyMonitorIntraFreqCellInd )
SIG_DEF( SIG_CPHY_MONITOR_INTER_FREQ_CELL_IND,      CphyMonitorInterFreqCellInd     cphyMonitorInterFreqCellInd )
SIG_DEF( SIG_CPHY_FREQ_MEAS_ON_RACH_REQ,            CphyFreqMeasOnRachReq           cphyFreqMeasOnRachReq )
SIG_DEF( SIG_CPHY_FREQ_MEAS_ON_RACH_CNF,            CphyFreqMeasOnRachCnf           cphyFreqMeasOnRachCnf )
SIG_DEF( SIG_CPHY_STOP_INTRA_FREQ_CELL_MEAS_REQ,    CphyStopIntraFreqCellMeasReq    cphyStopIntraFreqCellMeasReq )
SIG_DEF( SIG_CPHY_STOP_INTER_FREQ_CELL_MEAS_REQ,    CphyStopInterFreqCellMeasReq    cphyStopInterFreqCellMeasReq )
SIG_DEF( SIG_CPHY_STOP_FREQ_MEAS_ON_RACH_REQ,       CphyStopFreqMeasOnRachReq       cphyStopFreqMeasOnRachReq )
SIG_DEF( SIG_CPHY_STOP_DETECTED_CELL_MEAS_REQ,      CphyStopDetectedCellMeasReq     cphyStopDetectedCellMeasReq )
SIG_DEF( SIG_CPHY_STOP_UE_INTERNAL_MEAS_REQ,        CphyStopUeInternalMeasReq       cphyStopUeInternalMeasReq )
SIG_DEF( SIG_CPHY_DETECTED_CELL_MEASUREMENT_REQ,    CphyDetectedCellMeasurementReq  cphyDetectedCellMeasurementReq )
SIG_DEF( SIG_CPHY_DETECTED_CELL_MEASUREMENT_IND,    CphyDetectedCellMeasurementInd  cphyDetectedCellMeasurementInd )
SIG_DEF( SIG_CPHY_UE_INTERNAL_MEASUREMENT_REQ,      CphyUeInternalMeasurementReq    cphyUeInternalMeasurementReq )
SIG_DEF( SIG_CPHY_UE_INTERNAL_MEASUREMENT_IND,      CphyUeInternalMeasurementInd    cphyUeInternalMeasurementInd )
/* CQ00072273 begin */
SIG_DEF( SIG_CPHY_UE_RX_TX_TIME_DIFF_TYPE2_MEASUREMENT_IND, CphyUeRxTxTimeDiffType2MeasurementInd    cphyUeRxTxTimeDiffType2MeasurementInd )
SIG_DEF( SIG_CPHY_UE_RX_TX_TIME_DIFF_TYPE2_MEASUREMENT_REQ, CphyUeRxTxTimeDiffType2MeasurementReq    cphyUeRxTxTimeDiffType2MeasurementReq )
/* CQ00072273 end */
SIG_DEF( SIG_CPHY_SYNC_IND,                         CphySyncInd                     cphySyncInd )
SIG_DEF( SIG_CPHY_OUT_OF_SYNC_IND,                  CphyOutOfSyncInd                cphyOutOfSyncInd )
SIG_DEF( SIG_CPHY_DEACTIVATE_REQ,                   CphyDeactivateReq               cphyDeactivateReq )
SIG_DEF( SIG_CPHY_DEACTIVATE_CNF,                   CphyDeactivateCnf               cphyDeactivateCnf )
SIG_DEF( SIG_CPHY_MEASURE_INTRA_FREQ_CELLS_IND,     CphyMeasureIntraFreqCellsInd    cphyMeasureIntraFreqCellsInd )
SIG_DEF( SIG_CPHY_MEASURE_INTER_FREQ_CELLS_IND,     CphyMeasureInterFreqCellsInd    cphyMeasureInterFreqCellsInd )
SIG_DEF( SIG_CPHY_COMPRESSED_MODE_CONFIG_REQ,       CphyCompressedModeConfigReq     cphyCompressedModeConfigReq )
SIG_DEF( SIG_CPHY_COMPRESSED_MODE_ERROR_IND,        CphyCompressedModeErrorInd      cphyCompressedModeErrorInd )
SIG_DEF( SIG_CPHY_RSSI_SCAN_REQ,                    CphyRssiScanReq                 cphyRssiScanReq )
SIG_DEF( SIG_CPHY_RSSI_SCAN_CNF,                    CphyRssiScanCnf                 cphyRssiScanCnf )
SIG_DEF( SIG_CPHY_DL_TRCH_CRC_REQ,                  CphyDlTrchCrcReq                cphyDlTrchCrcReq )
SIG_DEF( SIG_CPHY_DL_TRCH_CRC_CNF,                  CphyDlTrchCrcCnf                cphyDlTrchCrcCnf )
SIG_DEF( SIG_CPHY_GSM_CELL_INFO_REQ,                CphyGsmCellInfoReq              cphyGsmCellInfoReq )
SIG_DEF( SIG_CPHY_MONITOR_GSM_CELL_REQ,             CphyMonitorGsmCellReq           cphyMonitorGsmCellReq )
SIG_DEF( SIG_CPHY_MONITOR_GSM_CELL_IND,             CphyMonitorGsmCellInd           cphyMonitorGsmCellInd )
SIG_DEF( SIG_CPHY_STOP_GSM_CELL_MEAS_REQ,           CphyStopGsmCellMeasReq          cphyStopGsmCellMeasReq )
#if defined (RRC_UPGRADE_ENG_MODE_REDESIGN) //********** begin
	SIG_DEF( SIG_CPHY_ENG_MODE_INFO_REQ,				CphyEngModeInfoReq				cphyEngModeInfoReq )
#endif //RRC_UPGRADE_ENG_MODE_REDESIGN :********** end
//********** start
#if defined (UPGRADE_ESCC)
SIG_DEF( SIG_CPHY_TC_HSCCH_RELEASE_REQ,				CphyTcHscchReleaseReq			cphyTcHscchReleaseReq)
SIG_DEF( SIG_CPHY_TC_HSCCH_RELEASE_CNF, 			CphyTcHscchReleaseCnf			cphyTcHscchReleaseCnf) 
SIG_DEF( SIG_CPHY_TC_HSCCH_SETUP_REQ,				CphyTcHscchSetupReq 			cphyTcHscchSetupReq)
SIG_DEF( SIG_CPHY_TC_HSCCH_SETUP_CNF, 				CphyTcHscchSetupCnf				cphyTcHscchSetupCnf) 
SIG_DEF( SIG_CPHY_SERVING_CELL_CHANGE_IND,			CphyServingCellChaneInd			cphyServingCellChaneInd)

#endif//UPGRADE_ESCC
//********** end

/* Start of bsic decode signals used by MCR. */
SIG_DEF( SIG_CPHY_GSM_BSIC_DECODE_REQ,              CphyBsicDecodeReq               cphyBsicDecodeReq )
SIG_DEF( SIG_CPHY_GSM_BSIC_DECODE_IND,              CphyBsicDecodeInd               cphyBsicDecodeInd )
/* End of bsic decode signals used by MCR. */

/* Start of signals used for GSM PLMN search when the 3G stack is active. */
#if !defined (UPGRADE_PLMS) /* CQ00107079 added */
SIG_DEF( SIG_CPHY_GSM_LIST_BCCH_REQ,                CphyGsmListBcchReq              cphyGsmListBcchReq )
SIG_DEF( SIG_CPHY_GSM_LIST_MEAS_IND,                CphyGsmListMeasInd              cphyGsmListMeasInd )
SIG_DEF( SIG_CPHY_GSM_LIST_BSIC_IND,                CphyGsmListBsicInd              cphyGsmListBsicInd )
#endif
SIG_DEF( SIG_CPHY_GSM_BCCH_DECODE_IND,              CphyGsmBcchDecodeInd            cphyGsmBcchDecodeInd )
SIG_DEF( SIG_CPHY_GSM_MULTI_BCCH_DECODE_REQ,        CphyGsmMultiBcchDecodeReq       cphyGsmMultiBcchDecodeReq )
SIG_DEF( SIG_CPHY_GSM_MULTI_BCCH_DECODE_IND,        CphyGsmMultiBcchDecodeInd       cphyGsmMultiBcchDecodeInd )
/* End of signals used for GSM PLMN search when the 3G stack is active. */

SIG_DEF( SIG_CPHY_CELL_SELECT_IND,                  CphyCellSelectInd               cphyCellSelectInd )
SIG_DEF( SIG_CPHY_SUSPEND_IND,                      CphySuspendInd                  cphySuspendInd )
SIG_DEF( SIG_CPHY_SWITCH_RAT_REQ,                   CphySwitchRatReq                cphySwitchRatReq )
SIG_DEF( SIG_CPHY_SWITCH_RAT_CNF,                   CphySwitchRatCnf                cphySwitchRatCnf )
SIG_DEF( SIG_CPHY_DRX_RSSI_SCAN_REQ,                CphyDrxRssiScanReq              cphyDrxRssiScanReq )
SIG_DEF( SIG_CPHY_DRX_RSSI_SCAN_CNF,                CphyDrxRssiScanCnf              cphyDrxRssiScanCnf )
SIG_DEF( SIG_CPHY_DRX_FIND_CELL_REQ,                CphyDrxFindCellReq              cphyDrxFindCellReq )
SIG_DEF( SIG_CPHY_DRX_FIND_CELL_CNF,                CphyDrxFindCellCnf              cphyDrxFindCellCnf )
SIG_DEF( SIG_CPHY_DRX_NEXT_CELL_REQ,                CphyDrxNextCellReq              cphyDrxNextCellReq )
SIG_DEF( SIG_CPHY_DRX_NEXT_CELL_CNF,                CphyDrxNextCellCnf              cphyDrxNextCellCnf )
SIG_DEF( SIG_CPHY_UL_TFC_CONFIG_REQ,                CphyUlTfcConfigReq              cphyUlTfcConfigReq )

#if defined(UPGRADE_3G_HSDPA)
SIG_DEF( SIG_CPHY_DL_HS_DSCH_TR_CH_CONFIG_REQ,      CphyDlHsDschTrChConfigReq       cphyDlHsDschTrChConfigReq )
SIG_DEF( SIG_CPHY_HS_SCCH_CONFIG_REQ,               CphyHsScchConfigReq             cphyHsScchConfigReq )
SIG_DEF( SIG_CPHY_HS_DSCH_RELEASE_REQ,              CphyHsDschReleaseReq            cphyHsDschReleaseReq )
SIG_DEF( SIG_CPHY_CPC_CONFIG_REQ,                   CphyCpcConfigReq                cphyCpcConfigReq )

#endif /* UPGRADE_3G_HSDPA */

SIG_DEF( SIG_CPHY_UE_CAPABILITY_CONFIG_CNF,         CphyUeCapabilityConfigCnf       cphyUeCapabilityConfigCnf )
SIG_DEF( SIG_PLW_CPHY_NO_ACTION_IND,                PlwCphyNoActionInd              plwCphyNoActionInd)
SIG_DEF( SIG_PLW_CPHY_T319_EXPIRY_IND,              PlwCphyT319ExpiryInd            plwCphyT319ExpiryInd)

#if defined (UPGRADE_DSDS)
SIG_DEF( SIG_CPHY_SUSPEND_REQ,                      CphySuspendReq                  cphySuspendReq )
SIG_DEF( SIG_CPHY_RESUME_REQ,                       CphyResumeReq                   cphyResumeReq )
SIG_DEF( SIG_CPHY_RESUME_CNF,                       CphyResumeCnf                   cphyResumeCnf )
SIG_DEF( SIG_CPHY_STOP_ACTIVITY_REQ,                CphyStopActivityReq             cphyStopActivityReq )
SIG_DEF( SIG_CPHY_DS_CONTROL_PCH_REQ,               CphyDsControlPchReq             cphyDsControlPchReq )
SIG_DEF( SIG_CPHY_DS_PAGING_FAILURE_IND,            CphyDspagingFailureInd          cphyDspagingFailureInd )/*CQ00017792 add*/
SIG_DEF( SIG_CPHY_CANCEL_STOP_ACTIVITY_REQ,         CphyCancelStopActivityReq       cphyCancelStopActivityReq )/*CQ00019491 add*/
SIG_DEF( SIG_CPHY_SUSPEND_BY_PCH_REQ,               CphySuspendByPchReq             cphySuspendByPchReq ) /*CQ00038099, added */
SIG_DEF( SIG_CPHY_RESUME_BY_PCH_REQ,                CphyResumeByPchReq              cphyResumeByPchReq ) /*CQ00038099, added */

SIG_DEF( SIG_CPHY_ABORT_PLMN_REQ,                   CphyAbortPlmnReq                cphyAbortPlmnReq )/*CQ00049404*/
#endif  /* UPGRADE_DSDS */

#endif /* ! EXCLUDE_CPHY */

SIG_DEF( SIG_CBMC_OUT_OF_SYNC_IND,                  EmptySignal                     cbmcOutOfSyncInd )
SIG_DEF( SIG_CBMC_LEVEL_2_PERIOD_ENDED_IND,         CbmcLevel2InfoInd               cbmcLevel2PeriodEndedInd )
SIG_DEF( SIG_CPHY_CELL_LOCK_REQ,                    CphyCellLockReq                 cphyCellLockReq )
SIG_DEF( SIG_CPHY_CELL_LOCK_CNF,                    EmptySignal                     cphyCellLockCnf )

/* ********** htfeng : Upgrade to LTE+WCDMA feature : begin */
#if defined (UPGRADE_LTE)
SIG_DEF( SIG_CPHY_HANDOVER_TO_LTE_REQ,              CphyHandoverToLteReq            cphyHandoverToLteReq )
SIG_DEF( SIG_CPHY_HANDOVER_TO_LTE_CNF,              CphyHandoverToLteCnf            cphyHandoverToLteCnf )
SIG_DEF( SIG_CPHY_HANDOVER_TO_LTE_REVERT_REQ,       CphyHandoverToLteRevertReq      cphyHandoverToLteRevertReq )
SIG_DEF( SIG_CPHY_HANDOVER_TO_UMTS_FAIL_REQ,        CphyHandoverToUmtsFailReq       cphyHandoverToUmtsFailReq )
SIG_DEF( SIG_CPHY_HANDOVER_TO_UMTS_FAIL_CNF,        CphyHandoverToUmtsFailCnf       cphyHandoverToUmtsFailCnf )
SIG_DEF( SIG_CPHY_LTE_CELL_INFO_REQ,                CphyLteCellInfoReq              cphyLteCellInfoReq )
SIG_DEF( SIG_CPHY_MONITOR_LTE_CELL_REQ,             CphyMonitorLteCellReq           cphyMonitorLteCellReq )
SIG_DEF( SIG_CPHY_MONITOR_LTE_CELL_IND,             CphyMonitorLteCellInd           cphyMonitorLteCellInd )
SIG_DEF( SIG_CPHY_IDLE_INTERVAL_INFO_REQ,           CphyIdleIntervalInfoReq         cphyIdleIntervalInfoReq )
SIG_DEF( SIG_CPHY_STOP_LTE_CELL_MEAS_REQ,           CphyStopLteCellMeasReq          cphyStopLteCellMeasReq )
SIG_DEF( SIG_CPHY_LTE_NCELL_BCH_REQ,                CphyLteNcellBchReq              cphyLteNcellBchReq )
SIG_DEF( SIG_CPHY_LTE_NCELL_BCH_IND,                CphyLteNcellBchInd              cphyLteNcellBchInd )
SIG_DEF( SIG_CPHY_LTE_NCELL_STOP_BCH_CNF,           CphyLteNcellStopBchCnf          cphyLteNcellStopBchCnf )
SIG_DEF( SIG_CPHY_DRX_FIND_LTE_CELL_REQ,            CphyDrxFindLteCellReq           cphyDrxFindLteCellReq )
SIG_DEF( SIG_CPHY_DRX_FIND_LTE_CELL_CNF,            CphyDrxFindLteCellCnf           cphyDrxFindLteCellcnf )
SIG_DEF( SIG_CPHY_DRX_FIND_LTE_CELL_ABORT_REQ,      CphyDrxFindLteCellAbortReq      cphyDrxFindLteCellAbortReq )
SIG_DEF( SIG_CPHY_DRX_FIND_LTE_CELL_ABORT_CNF,      CphyDrxFindLteCellAbortCnf      cphyDrxFindLteCellAbortcnf )
/* ********** - Update IRAT feature - begin */
SIG_DEF( SIG_CPHY_LTE_RESELECT_TO_UMTS_FAIL_REQ,    CphyLteReselectToUmtsFailReq    cphyLteReselectToUmtsFailReq)
SIG_DEF( SIG_CPHY_LTE_RESELECT_TO_UMTS_FAIL_CNF,	CphyLteReselectToUmtsFailCnf    cphyLteReselectToUmtsFailCnf)
/* ********** - Update IRAT feature - end */
#endif
/* ********** htfeng : Upgrade to LTE+WCDMA feature : end */
#if defined(UPGRADE_UL_ECF) // ********** begin
SIG_DEF( SIG_CPHY_COMMON_EDCH_RESOURCE_RELEASE_IND,      CphyEdchCommonResourceRelInd cphyEdchCommonResourceRelInd )
#endif /* UPGRADE_UL_ECF  */ // ********** end
// ********** begin  
#if defined(ENABLE_END_OF_DRX_MEAS_IND)
SIG_DEF (SIG_CPHY_END_OF_DRX_MEAS_IND,              CphyEndOfDrxMeasInd         cphyEndOfDrxMeasInd)
#endif /* ENABLE_END_OF_DRX_MEAS_IND */ 
// ********** end  
#if defined (UPGRADE_PLMS) 
//CQ78250
SIG_DEF( SIG_CPHY_FG_FIND_CELL_REQ,                 CphyFgFindCellReq       cphyFgFindCellReq )
SIG_DEF( SIG_CPHY_FG_FIND_CELL_CNF,                 CphyFgFindCellCnf       cphyFgFindCellCnf )
SIG_DEF( SIG_CPHY_FG_BCH_REQ,                       CphyFgBchReq            cphyFgBchReq )
SIG_DEF( SIG_CPHY_FG_BCH_CNF,                       CphyFgBchCnf             cphyFgBchCnf  )

SIG_DEF( SIG_CPHY_SET_WB_REQ,						CphySetWbReq			cphySetWbReq )
SIG_DEF( SIG_CPHY_SET_WB_CNF,						CphySetWbCnf			cphySetWbCnf  )
SIG_DEF( SIG_CPHY_ICS_INIT_REQ, 					CphyIcsInitReq			cphyIcsInitReq )
SIG_DEF( SIG_CPHY_ICS_INIT_CNF, 					CphyIcsInitCnf			cphyIcsInitCnf )

SIG_DEF( SIG_CPHY_STOP_FIND_CELL_REQ,     			CphyStopFindCellReq     cphyStopFindCellReq )//CQ00090333 modify
SIG_DEF( SIG_CPHY_STOP_FIND_CELL_CNF,     			CphyStopFindCellCnf     cphyStopFindCellCnf  )
#endif 



#if !defined (EXCLUDE_UPHY)
#if !defined (SIGNAL_MAPPING) //2013.3.21 Jiantao modified for LTE+W+G UE PC VS project
SIG_DEF( SIG_UPHY_DUMMY = UPHY_SIGNAL_BASE,         EmptySignal                     uphy_dummy )
#endif

#if !defined (PS_L2_R8_API)
SIG_DEF( SIG_PHY_ACCESS_REQ,                        PhyAccessReq                    phyAccessReq )
#endif /*PS_L2_R8_API*/
SIG_DEF( SIG_PHY_ACCESS_CNF,                        PhyAccessCnf                    phyAccessCnf )


SIG_DEF( SIG_PHY_DATA_REQ,                          PhyDataReq                      phyDataReq )
SIG_DEF( SIG_PHY_DATA_IND,                          PhyDataInd                      phyDataInd )
SIG_DEF( SIG_PHY_STATUS_IND,                        PhyStatusInd                    phyStatusInd )
SIG_DEF( SIG_PHY_FRAME_IND,                         PhyFrameInd                     phyFrameInd )
SIG_DEF( SIG_PHY_STATE_IND,                         PhyStateInd                     phyStateInd )

#if defined(UPGRADE_3G_HSDPA)
SIG_DEF( SIG_PHY_HS_DATA_IND,                       PhyHsDataInd                    phyHsDataInd )
//SIG_DEF( SIG_HS_DATA_IND,                       	PhyHsDataInd                    phyHsDataInd )
//SIG_DEF( SIG_EHS_DATA_IND,							PhyHsDataInd					phyEhsDataInd )
#if defined(ON_PC)
SIG_DEF( SIG_PHY_HS_ASSIGN_POINTER_REQ,             PhyHsAssignPointerReqOnPc       phyHsAssignPointerReq )
#else /* ON_PC */
SIG_DEF( SIG_PHY_HS_ASSIGN_POINTER_REQ,             EmptySignal                     phyHsAssignPointerReq )
#endif /* ON_PC */
SIG_DEF( SIG_PHY_HS_UNASSIGN_POINTER_IND,           PhyHsUnassignPointerInd         phyHsUnassignPointerInd )
#endif /* UPGRADE_3G_HSDPA */

#if defined (UPGRADE_3G_EDCH)
SIG_DEF (SIG_PHY_EDCH_RX_CH_DATA_IND,                   PhyEdchRxChDataInd              phyEdchRxChDataInd)
#if defined (UPGRADE_UL_ECF)
SIG_DEF( SIG_PHY_EDCH_ACCESS_CNF,					PhyEdchAccessCnf				phyEdchAccessCnf )
SIG_DEF( SIG_PHY_COMMON_EDCH_RESOURCE_RELEASE_IND,					PhyEdchCommonResourceRelInd				phyEdchCommonResourceRelInd)
#endif
#endif /* UPGRADE_3G_EDCH */

SIG_DEF( SIG_PHY_EDCH_TTI_IND,                      EmptySignal                     phyEdchTtiInd )
SIG_DEF( SIG_PHY_EDCH_DATA_REQ,                     EmptySignal                     phyEdchDataReq )

#endif /* EXCLUDE_UPHY */

#endif
/* END OF FILE */

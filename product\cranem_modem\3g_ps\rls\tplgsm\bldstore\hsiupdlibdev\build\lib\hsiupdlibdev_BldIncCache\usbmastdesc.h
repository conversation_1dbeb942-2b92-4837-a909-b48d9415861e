/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/devices/transports/usb.mod/pub/src/usbmastdesc.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/24 10:16:13 $
 **************************************************************************
 *  File Description :
 *    Device descriptors for the TTPCom Ltd USB Mass Storage Class interface.
 **************************************************************************/
#if defined(UPGRADE_USB)
#if defined(USB_MASS_STORAGE_BULK_ONLY_INTERFACE)

/* This file may be included multiple times in a descriptor set. */

/*******************************************************************************
 * TTPCom Mass Storage Class interface descriptors.
 ******************************************************************************/

  /*
   * Interface 0, Alternate 0
   */
  USB_INTERFACE_DESCRIPTOR(USB_MAST_INTERFACE_NUMBER,         /* bInterfaceNumber */
                           USB_MAST_INTERFACE_ALTERNATE_NUMBER, /* bAlternateSetting */
                           USB_MAST_NUM_ENDPOINT_DESCRIPTORS, /* bNumEndpoints */
                           USB_INTERFACE_CLASS_MASS_STORAGE,  /* bInterfaceClass */
                           USB_INTERFACE_SUB_CLASS_MS_SCSI,   /* bInterfaceSubclass */
                           USB_INTERFACE_PROTOCOL_BULK_ONLY,  /* bInterfaceProtocol */
                           USB_STRING_NOT_DEFINED_INDEX),     /* iInterface */

  USB_ENDPOINT_DESCRIPTOR((USB_MAST_IN_PHYSICAL_ENDPOINT
                           |USB_ENDPOINT_ATTR_DIRECTION_IN),  /* bEndpointAddress */
                          USB_ENDPOINT_TYPE_BULK,             /* bmAttributes */
                          64,                                 /* wMaxPacketSize */
                          0),                                 /* bInterval (ms) */

  USB_ENDPOINT_DESCRIPTOR((USB_MAST_OUT_PHYSICAL_ENDPOINT
                           |USB_ENDPOINT_ATTR_DIRECTION_OUT), /* bEndpointAddress */
                          USB_ENDPOINT_TYPE_BULK,             /* bmAttributes */
                          64,                                 /* wMaxPacketSize */
                          0)                                  /* bInterval (ms) */

#endif /* USB_MASS_STORAGE_BULK_ONLY_INTERFACE */
#endif /* UPGRADE_USB */
/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/***************************************************************************
 * TTPCom Software Copyright (c) TTP Communications Ltd 1997-2004
 ***************************************************************************
 * $Id: //central/main/wsd/sys/tmm.mod/api/cfg/tmmmem.h#1 $
 * $Revision: #1 $
 * $DateTime: 2006/10/24 11:37:14 $
 ***************************************************************************
 * File Description: Traffic Memory Manager Memory Configuration
 *************************************************************************/

/* This this file may be included more than once in a source file.
 * Hence it is not protected by any compile time switches. */

/***************************************************************************
 *  Constants
 **************************************************************************/
/* This is the ratio of the HWM that is used to calculate when to turn off
 * flow control when the system is triggered by the allocation count to flow
 * control on */
#define AC_LWM_HWM_RATIO 5

/* This ratio determines the amount that the free memory rises above
 * the high water mark, before we turn off flow-control */
#define TMM_HYST_BUFF_RATIO 6

/* Set the default memory manager to use */

#if defined (UPGRADE_3G)


//for 2ms
#define TMM_3G_DYN_UL_POOL_SIZE_BYTES       200000
#define TMM_3G_DYN_UL_POOL_HWM_BYTES        180000 //     100ms round trip/2 ms * 11484 + RLC window
#define TMM_3G_DYN_UL_POOL_VHWM_BYTES       190000 /// 

//for 10 msc
//#define TMM_3G_DYN_UL_POOL_SIZE_BYTES        55000
//#define TMM_3G_DYN_UL_POOL_HWM_BYTES         25000 //  45%   100ms round trip/10 ms * 20000
//#define TMM_3G_DYN_UL_POOL_VHWM_BYTES        37125 // 67.5%





#if defined (ON_PC)
#define TMM_3G_DYN_UL_POOL_HWM_ALLOC_COUNT  410
#else
#define TMM_3G_DYN_UL_POOL_HWM_ALLOC_COUNT  140
#endif /* ON_PC */

#if defined(UPGRADE_3G_HSDPA)

#if !defined (UPS_CFG_HS_DSCH_CATEGORY_EXT_14) 
// values for CATEGORY 8: max HSDPA rate is 7.2 Mbps
// in category 8 max number of bits per tti is 14411
// in category 6 max number of bits per tti is 7298
// therefore the pool size for category 8 should be around {(14411/7298)*100000 + 80000} =~ 280000
#define TMM_3G_DYN_DL_POOL_SIZE_BYTES       448000
#define TMM_3G_DYN_DL_POOL_HWM_BYTES        320000
#define TMM_3G_DYN_DL_POOL_VHWM_BYTES       400000
#else
// values for CATEGORY 14: max HSDPA rate is 21 Mbps
// in category 14 max number of bits per tti is 42192
// in category 6 max number of bits per tti is 7298
// therefore the pool size for category 8 should be around {(42192/7298)*100000 + 80000} =~ 658130
#if defined(ENABLE_URLC_UNIT_TEST)
#define TMM_3G_DYN_DL_POOL_SIZE_BYTES       1500000
#define TMM_3G_DYN_DL_POOL_HWM_BYTES       1480000
#define TMM_3G_DYN_DL_POOL_VHWM_BYTES     1490000

#else
#define TMM_3G_DYN_DL_POOL_SIZE_BYTES       848000
#define TMM_3G_DYN_DL_POOL_HWM_BYTES        720000
#define TMM_3G_DYN_DL_POOL_VHWM_BYTES       800000
#endif
#endif
// values for CATEGORY 6: max HSDPA rate is 3.6 Mbps
//#define TMM_3G_DYN_DL_POOL_SIZE_BYTES       180000
//#define TMM_3G_DYN_DL_POOL_HWM_BYTES        110000
//#define TMM_3G_DYN_DL_POOL_VHWM_BYTES       162000
#else
#define TMM_3G_DYN_DL_POOL_SIZE_BYTES       300000//80000
#define TMM_3G_DYN_DL_POOL_HWM_BYTES       280000//40000
#define TMM_3G_DYN_DL_POOL_VHWM_BYTES     290000 //72000
#endif /* UPGRADE_3G_HSDPA */

#define TMM_3G_TMDL_POOL_SIZE_BYTES         300000//80000
#define TMM_3G_TMDL_POOL_HWM_BYTES         280000//72000

#if defined (UPGRADE_3G_EDCH)
/* defines the maximum size that can be allocated for RLC EDCH LIs and control */
#define TMM_3G_EDCH_POOL_SIZE_BYTES         0x10000 /* 64KB */
#endif /* UPGRADE_3G_EDCH */
#endif /* UPGRADE_3G */

/***************************************************************************
 *  Access Macros
 **************************************************************************/

#if defined (TMM_GET_VIRT_POOL_NAME)
#define TMM_MEM_POOL_DEF_VIRT( POOL_NAME, MEM_MGR, POOL_SIZE, HWM, VHWM, MIN_TAIL, AC_MAX, AC_HWM) \
                               POOL_NAME,

#elif defined (TMM_GET_REAL_POOL_NAME)
#define TMM_MEM_POOL_DEF_REAL( POOL_NAME, MEM_MGR, POOL_SIZE, HWM, VHWM, MIN_TAIL, AC_MAX, AC_HWM) \
                               POOL_NAME,


#elif defined (TMM_GET_MAPPED_POOL_NAME)
#define TMM_MEM_POOL_DEF_MAPPED( POOL_NAME, MEM_MGR, POOL_SIZE, HWM, VHWM, MIN_TAIL, AC_MAX, AC_HWM) \
                                 POOL_NAME,

#elif defined (TMM_SET_VIRT_POOL_TABLE)
#define TMM_MEM_POOL_DEF_VIRT( POOL_NAME, MEM_MGR, POOL_SIZE, HWM, VHWM, MIN_TAIL, AC_MAX, AC_HWM) \
                              {POOL_SIZE, MEM_MGR, VHWM, HWM, HWM - (HWM / TMM_HYST_BUFF_RATIO), \
                               MIN_TAIL, AC_MAX, AC_HWM, AC_HWM - (AC_HWM / AC_LWM_HWM_RATIO)},

#elif defined (TMM_SET_REAL_POOL_TABLE)
#define TMM_MEM_POOL_DEF_REAL( POOL_NAME, MEM_MGR, POOL_SIZE, HWM, VHWM, MIN_TAIL, AC_MAX, AC_HWM) \
                              {POOL_SIZE, MEM_MGR, VHWM, HWM, HWM - (HWM / TMM_HYST_BUFF_RATIO), \
                               MIN_TAIL, AC_MAX, AC_HWM, AC_HWM - (AC_HWM / AC_LWM_HWM_RATIO)},

#elif defined (TMM_SET_MAPPED_POOL_TABLE)
#define TMM_MEM_POOL_DEF_MAPPED( POOL_NAME, MEM_MGR, POOL_SIZE, HWM, VHWM, MIN_TAIL, AC_MAX, AC_HWM) \
                                {POOL_SIZE, MEM_MGR, VHWM, HWM, HWM - (HWM / TMM_HYST_BUFF_RATIO), \
                                 MIN_TAIL, AC_MAX, AC_HWM, AC_HWM - (AC_HWM / AC_LWM_HWM_RATIO)},

#elif defined (TMM_GET_MEM_MGR_NAME)
#define TMM_MEM_MGR_DEF( MEM_MGR) MEM_MGR,


#elif defined (TMM_GET_PROFILE_NAME)
#define TMM_PROFILE_DEF( PROF_NAME, VIRT_POOL_1, VIRT_POOL_2) \
                         PROF_NAME,


#elif defined (TMM_SET_PROFILE_TABLE)
#define TMM_PROFILE_DEF( PROF_NAME, VIRT_POOL_1, VIRT_POOL_2) \
                         {{VIRT_POOL_1, VIRT_POOL_2}},


#endif


/***************************************************************************
 *  Define unused access Macros
 **************************************************************************/
#if !defined (TMM_PROFILE_DEF)
#define TMM_PROFILE_DEF( PROF_NAME, VIRT_POOL_1, VIRT_POOL_2)
#endif

#if !defined (TMM_MEM_MGR_DEF)
#define TMM_MEM_MGR_DEF( MEM_MGR)
#endif

#if !defined (TMM_MEM_POOL_DEF_VIRT)
#define TMM_MEM_POOL_DEF_VIRT( POOL_NAME, MEM_MGR, POOL_SIZE, HWM, VHWM,  MIN_TAIL, AC_MAX, AC_HWM)
#endif

#if !defined (TMM_MEM_POOL_DEF_REAL)
#define TMM_MEM_POOL_DEF_REAL( POOL_NAME, MEM_MGR, POOL_SIZE, HWM, VHWM, MIN_TAIL, AC_MAX, AC_HWM)
#endif

#if !defined (TMM_MEM_POOL_DEF_MAPPED)
#define TMM_MEM_POOL_DEF_MAPPED( POOL_NAME, MEM_MGR, POOL_SIZE, HWM, VHWM, MIN_TAIL, AC_MAX, AC_HWM)
#endif



/***************************************************************************
 * Build Checks
 **************************************************************************/
#if defined (TMM_BUILD_CHECK)
/* This checks if TMM is required in the build. The following UPGRADES require TMM
 * services and TMM should be enabled in these builds. */
#if defined (UPGRADE_3G)     || defined (UPGRADE_EDGE) || defined (UPGRADE_GPRS) ||  0  ||  0  ||  0

#if !defined (ENABLE_TMM) && !defined (EXCLUDE_TMM)
#define ENABLE_TMM
#endif

#endif
#endif /* TMM_BUILD_CHECK */

/***************************************************************************
 *  Memory Pools and Sizes
 **************************************************************************/

/* This section defines the pool name and its properties.
 *
 * Pool Name
 * =========
 * The pool name will form the pool ID that calls to TMM will use.
 *
 *
 * Memory Manager
 * ==============
 * This is the ID of the memory manager that is used to alloc and free memory blocks
 * from this pool. The memory manager IDs are defined in tmmcfg.h.
 *
 *
 * Pool Size
 * =========
 * This is the size of the pool in bytes.
 *
 *
 * HWM
 * ===
 * The HWM is the High Water Mark in bytes. This indicates the amount that free
 * memory falls below, before tasks are sent signals to flow control their allocations.
 *
 * The High Water Mark should typically be set to 1/2 the pool size.
 *
 * The corresponding Low Water Mark, (the amount that allocated memory must drop below before
 * tasks are sent signals to indicate that their allocation restrictions are removed), is
 * calculated at compile time from the HWM. The calculation is as follows:
 *
 *      LWM = HWM - (HWM / TMM_HYST_BUFF_RATIO)
 *
 * Setting HWM to 0 disables flow control for the pool.
 *
 *
 * VHWM
 * ======
 * The VHWM is the Very High Water Mark in bytes. This indicates the amount that free
 * memory falls below, before tasks are sent signals of Very high water mark to flow control their allocations.
 *
 * The Very High Water Mark should typically be set to 9/10 of the pool size.
 *
 * Setting VHWM to 0 disables VHWM flow control for the pool.
 *
 * Note that due to performance constraints,  there will be VHWM signaling only after the pool is in OVERLOADED state, meaning that HWM signaling had already been done.
 * This implies an assumption that there will be no direct jump from NORMAL pool state to VERY OVERLOADED state.
 * In case there is such jump, then it will only initiate a HWM signaling.
 *
 *
 * Min Tail Length
 * ===============
 * The Min Tail Length is the minimum difference in bytes between the current TMM block
 * length and the new TMM block length that TMM will tail the block. This value is used
 * in the TmmFreeTail () call.
 *
 *
 * Alloc Count Max
 * ===============
 * This is the maximum number of TMM blocks that can be allocated from this pool by tasks
 * that are registered for flow control. If the number of allocations reaches this value,
 * then requests for blocks by tasks that are registered for flow control will be denied.
 * Tasks that are not registered for flow control will be allowed to allocate memory.
 *
 * Setting this value to 0 disables the max alloc count mechanism for the pool.
 *
 *
 * Alloc Count HWM
 * ===============
 * This is the number of allocated TMM blocks from this pool that can occur before
 * TMM will signal to the registered flow control tasks to flow control.
 *
 * Setting this value to 0 disables the alloc count flow control mechanism for the pool.
 *
 *
 * For each memory manager definition, the appropriate alloc, free and tail functions
 * must be configured in TMM. This is done in TMMCFG.C.
 */

/*
 *      Virtual Pools
 *
 * Virtual pools are called directly by users of TMM. Virtual pool parameters can change depending on the
 * current physical transport mechanism (ie. 3G, 2.5G etc..). Hence, Virtual pools are setup with their
 * parameters zero'ed. The parameter changes are performed according to TMM profiles. If you add a Virtual
 * pool, you MUST add the corresponding mapping information to the TMM Profile Definition below. Virtual
 * pools inherit properties from Mapped pools. The inheritance is defined by TMM profiles.
 *
 *==========================================================================================================================
 *                       Pool Name                  Memory           Pool Size   HWM      VHWM     Min Tail  Alloc  Alloc
 *                                                  Manager          (Bytes)     (bytes)  (bytes)  Length    Count  Count
 *                                                                                                 (bytes)   Max    HWM
 *=========================================================================================================================*/

TMM_MEM_POOL_DEF_VIRT   (TMM_DEFAULT_POOL_UL,       TMM_UL_MEM_MGR,  0,    0,         0,       0,       0,     0)
TMM_MEM_POOL_DEF_VIRT   (TMM_DEFAULT_POOL_DL,       TMM_DL_MEM_MGR,  0,    0,         0,       0,       0,     0)


/*
 *      Real Pools
 *
 * Real pools can be called directly by users of TMM. Real pool parameters remain constant and are never
 * modified.
 *
 *===================================================================================================================
 *                       Pool Name                  Memory                Pool Size   HWM      Min Tail Alloc  Alloc
 *                                                  Manager               (Bytes)     (bytes)  Length   Count  Count
 *                                                                                             (bytes)  Max    HWM
 *===================================================================================================================*/
/* eg:
TMM_MEM_POOL_DEF_REAL   (TMM_DYN_MEM_POOL_REAL1,    TMM_DEFAULT_MEM_MGR,  20000,      10000,   0,       0,     0)
*/
#ifdef LTE_W_PS
TMM_MEM_POOL_DEF_REAL   (TMM_DEFAULT_POOL_TMDL,     TMM_TMDL_MEM_MGR, TMM_3G_TMDL_POOL_SIZE_BYTES,
                                                                                 TMM_3G_TMDL_POOL_HWM_BYTES,
                                                                                          0,       0,        0,     0)
#if defined(UPGRADE_3G_EDCH)
TMM_MEM_POOL_DEF_REAL   (TMM_DEFAULT_POOL_EDCH,     TMM_FAST_MEM_MGR, TMM_3G_EDCH_POOL_SIZE_BYTES,    
                                                                                 0,       0,       0,        0,     0)
#endif //UPGRADE_3G_EDCH 
#endif
/*
 *      Mapped Pools
 *
 * Mapped pools are never referenced directly by users of TMM. Mapped pools are used to modify the parameters
 * of virtual pools during operation, depending on the current underlying physical transport mechanism
 * (ie. 3G, 2.5G etc..). The mapped pools are mapped according to TMM profiles.
 *
 *==========================================================================================================================
 *                       Pool Name                  Memory           Pool Size   HWM      VHWM     Min Tail  Alloc  Alloc
 *                                                  Manager          (Bytes)     (bytes)  (bytes)  Length    Count  Count
 *                                                                                                 (bytes)   Max    HWM
 *=========================================================================================================================*/

#if defined (TMM_PC_SIMULATION)
/* ///////////////////// PC /////////////////// */
TMM_MEM_POOL_DEF_MAPPED (TMM_DYN_MEM_POOL_UL_PC,    TMM_UL_MEM_MGR,  20000,      5000,    0,    0,       0,     0)
TMM_MEM_POOL_DEF_MAPPED (TMM_DYN_MEM_POOL_DL_PC,    TMM_DL_MEM_MGR,  10000,      0,       0,    0,       0,     0)

TMM_MEM_POOL_DEF_MAPPED (TMM_DYN_MEM_POOL_UL_PC_2,  TMM_PC_MEM_MGR,  30000,      20000,   0,    0,       0,     0)
TMM_MEM_POOL_DEF_MAPPED (TMM_DYN_MEM_POOL_DL_PC_2,  TMM_PC_MEM_MGR,  20000,      1000,    0,    0,       0,     0)

TMM_MEM_POOL_DEF_MAPPED (TMM_DYN_MEM_POOL_UL_PC_3,  TMM_UL_MEM_MGR,  40000,      30000,   0,    0,       0,     0)
TMM_MEM_POOL_DEF_MAPPED (TMM_DYN_MEM_POOL_DL_PC_3,  TMM_DL_MEM_MGR,  10000,      0,       0,    0,       0,     0)

TMM_MEM_POOL_DEF_MAPPED (TMM_DYN_MEM_POOL_UL_PC_4,  TMM_PC_MEM_MGR,  20000,      10000,   0,    0,       0,     0)
TMM_MEM_POOL_DEF_MAPPED (TMM_DYN_MEM_POOL_DL_PC_4,  TMM_PC_MEM_MGR,  10000,      0,       0,    0,       0,     0)
#endif

#if defined (UPGRADE_3G)
#ifdef LTE_W_PS
TMM_MEM_POOL_DEF_MAPPED (TMM_DYN_MEM_POOL_UL_3G,    TMM_UL_MEM_MGR,  TMM_3G_DYN_UL_POOL_SIZE_BYTES,
                                                                                TMM_3G_DYN_UL_POOL_HWM_BYTES,
                                                                                        TMM_3G_DYN_UL_POOL_VHWM_BYTES,
                                                                                              200,     160,   TMM_3G_DYN_UL_POOL_HWM_ALLOC_COUNT)
TMM_MEM_POOL_DEF_MAPPED (TMM_DYN_MEM_POOL_DL_3G,    TMM_DL_MEM_MGR,  TMM_3G_DYN_DL_POOL_SIZE_BYTES,
                                                                                TMM_3G_DYN_DL_POOL_HWM_BYTES,
                                                                                        TMM_3G_DYN_DL_POOL_VHWM_BYTES,
                                                                                              200,     0,     0)
#else
//TMM_MEM_POOL_DEF_MAPPED (TMM_DYN_MEM_POOL_UL_3G,    TMM_DEFAULT_MEM_MGR,  80000,      45000,    54000,   200,     160,   110)
TMM_MEM_POOL_DEF_MAPPED (TMM_DYN_MEM_POOL_UL_3G,    TMM_UL_MEM_MGR,  280000,      200000,    220000,   200,     460,   400)

# if defined(UPGRADE_3G_HSDPA)
/* ///////////////////// HSDPA /////////////////// */
//TMM_MEM_POOL_DEF_MAPPED (TMM_DYN_MEM_POOL_DL_3G,    TMM_DEFAULT_MEM_MGR, 180000,      110000,    162000,   200,     0,     0)
#if defined(UPGRADE_3G_HSPA_PLUS)  
TMM_MEM_POOL_DEF_MAPPED (TMM_DYN_MEM_POOL_DL_3G,    TMM_DL_MEM_MGR, 1000000,      800000,    900000,   200,     0,     0)
#else
TMM_MEM_POOL_DEF_MAPPED (TMM_DYN_MEM_POOL_DL_3G,    TMM_DL_MEM_MGR, 360000,      220000,    324000,   200,     0,     0)
#endif
# else
/* ///////////////////// 3G /////////////////// */
TMM_MEM_POOL_DEF_MAPPED (TMM_DYN_MEM_POOL_DL_3G,    TMM_DL_MEM_MGR,  80000,      40000,    72000,   200,     0,     0)
#endif /* UPGRADE_HSDPA */
#endif/*LTE_W_PS */
#endif

#if defined (UPGRADE_EDGE)
/* ///////////////////// EDGE /////////////////// */
TMM_MEM_POOL_DEF_MAPPED (TMM_DYN_MEM_POOL_UL_EDGE,  TMM_UL_MEM_MGR,  60000,      30000,    0,   0,       160,   110)
TMM_MEM_POOL_DEF_MAPPED (TMM_DYN_MEM_POOL_DL_EDGE,  TMM_DL_MEM_MGR,  20000,      0,        0,   0,       0,     0)
#endif

#if defined (UPGRADE_GPRS)
/* ///////////////////// GPRS /////////////////// */
TMM_MEM_POOL_DEF_MAPPED (TMM_DYN_MEM_POOL_UL_GPRS,  TMM_UL_MEM_MGR,  20000,      10000,    0,   0,       40,    35)
TMM_MEM_POOL_DEF_MAPPED (TMM_DYN_MEM_POOL_DL_GPRS,  TMM_DL_MEM_MGR,  10000,      0,        0,   0,       0,     0)
#endif














#if !defined (UPGRADE_GPRS)
/* ///////////////////// CSD FOR MAPAL WITHOUT TCPIP and GPRS /////////////////// */
TMM_MEM_POOL_DEF_MAPPED (TMM_DYN_MEM_POOL_UL_CSD,   TMM_UL_MEM_MGR,  7000,       3500,    0,    0,       0,     0)
TMM_MEM_POOL_DEF_MAPPED (TMM_DYN_MEM_POOL_DL_CSD,   TMM_DL_MEM_MGR,  10000,      0,    0,       0,       0,     0)
#endif /* UPGRADE_GPRS */




/***************************************************************************
 *  TMM Memory Managers
 **************************************************************************/
/***************************************************************************
 * This section defines the IDs of the available memory managers that each
 * memory pool uses. Add new memory managers here. Then add the appropriate
 * calls to memory manager specific alloc / free / resize etc. to tmmcfg.c
 **************************************************************************/
#if !defined (KI_DISABLE_DYNAMIC_MEMORY)
TMM_MEM_MGR_DEF (TMM_UL_MEM_MGR)
TMM_MEM_MGR_DEF (TMM_DL_MEM_MGR)
TMM_MEM_MGR_DEF (TMM_TMDL_MEM_MGR)
TMM_MEM_MGR_DEF (TMM_FAST_MEM_MGR)
#endif

#if defined (TMM_PC_SIMULATION)
TMM_MEM_MGR_DEF (TMM_PC_MEM_MGR)
#endif

/***************************************************************************
 *  TMM Profiles
 **************************************************************************/
/* TMM Profiles map Mapped pools to Virtual pools. The number of entries in each profile definition
 * after the profile name must be the same number as the number of virtual pools.
 *
 * For example:
 * if TMM_PROFILE_PC is selected, the pool properties of TMM_DYN_MEM_POOL_UL_PC will be copied into
 * the TMM_DEFAULT_POOL_UL pool properties, and the pool properties of TMM_DYN_MEM_POOL_DL_PC will
 * be copied into TMM_DEFAULT_POOL_DL.
 *
 * On initialisation, the first Profile in this list is used.
 *
 *============================================================================================================
 *               Profile name               TMM_DEFAULT_POOL_UL         TMM_DEFAULT_POOL_DL
 *============================================================================================================*/
#if defined (TMM_PC_SIMULATION)
/* ///////////////////// PC /////////////////// */
TMM_PROFILE_DEF (TMM_PROFILE_PC,            TMM_DYN_MEM_POOL_UL_PC,     TMM_DYN_MEM_POOL_DL_PC)
TMM_PROFILE_DEF (TMM_PROFILE_PC_2,          TMM_DYN_MEM_POOL_UL_PC_2,   TMM_DYN_MEM_POOL_DL_PC_2)
TMM_PROFILE_DEF (TMM_PROFILE_PC_3,          TMM_DYN_MEM_POOL_UL_PC_3,   TMM_DYN_MEM_POOL_DL_PC_3)
TMM_PROFILE_DEF (TMM_PROFILE_PC_4,          TMM_DYN_MEM_POOL_UL_PC_4,   TMM_DYN_MEM_POOL_DL_PC_4)
#endif

#if defined (UPGRADE_3G)
/* ///////////////////// 3G /////////////////// */
TMM_PROFILE_DEF (TMM_PROFILE_3G,            TMM_DYN_MEM_POOL_UL_3G,     TMM_DYN_MEM_POOL_DL_3G)
#endif

#if defined (UPGRADE_EDGE)
/* ///////////////////// EDGE /////////////////// */
TMM_PROFILE_DEF (TMM_PROFILE_EDGE,          TMM_DYN_MEM_POOL_UL_EDGE,   TMM_DYN_MEM_POOL_DL_EDGE)
#endif

#if defined (UPGRADE_GPRS)
/* ///////////////////// GPRS /////////////////// */
TMM_PROFILE_DEF (TMM_PROFILE_GPRS,          TMM_DYN_MEM_POOL_UL_GPRS,   TMM_DYN_MEM_POOL_DL_GPRS)
#endif





#if !defined (UPGRADE_GPRS)
/* ///////////////////// CSD /////////////////// */
TMM_PROFILE_DEF (TMM_PROFILE_CSD,           TMM_DYN_MEM_POOL_UL_CSD,    TMM_DYN_MEM_POOL_DL_CSD)
#endif




/* Undefine the access macros */
#undef TMM_BUILD_CHECK
#undef TMM_GET_VIRT_POOL_NAME
#undef TMM_GET_REAL_POOL_NAME
#undef TMM_GET_MAPPED_POOL_NAME

#undef TMM_SET_VIRT_POOL_TABLE
#undef TMM_SET_REAL_POOL_TABLE
#undef TMM_SET_MAPPED_POOL_TABLE

#undef TMM_GET_MEM_MGR_NAME

#undef TMM_GET_PROFILE_NAME
#undef TMM_SET_PROFILE_TABLE


/* Undefine the access switches */
#undef TMM_MEM_POOL_DEF_VIRT
#undef TMM_MEM_POOL_DEF_REAL
#undef TMM_MEM_POOL_DEF_MAPPED
#undef TMM_PROFILE_DEF
#undef TMM_PROFILE_DEF
#undef TMM_PROFILE_DEF
#undef TMM_MEM_MGR_DEF
/* END OF FILE */

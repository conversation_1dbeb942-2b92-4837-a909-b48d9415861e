/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/phy/3g.mod/api/inc/uphsyswordsizes.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/11/09 16:04:03 $
 **************************************************************************
 * File Description: Definition of different word sizes.
 **************************************************************************/

#ifndef UPHWORDSIZES_H
#define UPHWORDSIZES_H

/* AH_TO_DO these constants should all be UPH_SYS_XXX */

/*******************************************************************************
** Includes
*******************************************************************************/

#include <system.h>

/*******************************************************************************
** Constants
*******************************************************************************/

/*******************************************************************************
* Define      : UPH_WORD_SIZE_8
* Group       : UPH
* Description : Word size for 8 bit processing.
*******************************************************************************/
#define UPH_WORD_SIZE_8                 (8)

/*******************************************************************************
* Define      : UPH_LOG2_WORD_SIZE_8
* Group       : UPH
* Description : Logarithm base 2 of the word size for 8 bit processing.
*******************************************************************************/
#define UPH_LOG2_WORD_SIZE_8            (3)

/*******************************************************************************
* Define      : UPH_WORD_SIZE_8_MASK
* Group       : UPH
* Description : Masks a 8 bit word.
*******************************************************************************/
#define UPH_WORD_SIZE_8_MASK            ((1 << UPH_WORD_SIZE_8) - 1)

/*******************************************************************************
* Define      : UPH_BIT_IN_WORD_SIZE_8_MASK
* Group       : UPH
* Description : Used to calculate the position of a bit within an 8 bit
*               word.
*******************************************************************************/
#define UPH_BIT_IN_WORD_SIZE_8_MASK     ((1 << UPH_LOG2_WORD_SIZE_8) - 1)

/*******************************************************************************
* Define      : UPH_WORD_SIZE_16
* Group       : UPH
* Description : Word size for 16 bit processing.
*******************************************************************************/
#define UPH_WORD_SIZE_16                (16)

/*******************************************************************************
* Define      : UPH_LOG2_WORD_SIZE_16
* Group       : UPH
* Description : Logarithm base 2 of the word size for 16 bit processing.
*******************************************************************************/
#define UPH_LOG2_WORD_SIZE_16           (4)

/*******************************************************************************
* Define      : UPH_WORD_SIZE_16_MASK
* Group       : UPH
* Description : Masks a 16 bit word.
*******************************************************************************/
#define UPH_WORD_SIZE_16_MASK           ((1 << UPH_WORD_SIZE_16) - 1)

/*******************************************************************************
* Define      : UPH_BIT_IN_WORD_SIZE_16_MASK
* Group       : UPH
* Description : Used to calculate the position of a bit within a 16 bit
*               word.
*******************************************************************************/
#define UPH_BIT_IN_WORD_SIZE_16_MASK    ((1 << UPH_LOG2_WORD_SIZE_16) - 1)

/*******************************************************************************
* Define      : UPH_WORD_SIZE_16_LSB
* Group       : UPH
* Description : Least significant bit of a 16 bit word.
*******************************************************************************/
#define UPH_WORD_SIZE_16_LSB            (1)

/*******************************************************************************
* Define      : UPH_WORD_SIZE_16_MSB
* Group       : UPH
* Description : Most significant bit of a 16 bit word.
*******************************************************************************/
#define UPH_WORD_SIZE_16_MSB            (1 << (UPH_WORD_SIZE_16 - 1))

/*******************************************************************************
* Define      : UPH_WORD_SIZE_32
* Group       : UPH
* Description : Word size for 32 bit processing.
*******************************************************************************/
#define UPH_WORD_SIZE_32                (32)

/*******************************************************************************
* Define      : UPH_WORD_SIZE_32_MASK
* Group       : UPH
* Description : Masks a 32 bit word.
*******************************************************************************/
#define UPH_WORD_SIZE_32_MASK                                                  \
    (UPH_WORD_SIZE_16_MASK | (UPH_WORD_SIZE_16_MASK << UPH_WORD_SIZE_16))

/*******************************************************************************
* Define      : UPH_LOG2_WORD_SIZE_32
* Group       : UPH
* Description : Logarithm base 2 of the word size for 32 bit processing.
*******************************************************************************/
#define UPH_LOG2_WORD_SIZE_32           (5)

/*******************************************************************************
* Define      : UPH_BIT_IN_WORD_SIZE_32_MASK
* Group       : UPH
* Description : Used to calculate the position of a bit within a 32 bit
*               word.
*******************************************************************************/
#define UPH_BIT_IN_WORD_SIZE_32_MASK    ((1 << UPH_LOG2_WORD_SIZE_32) - 1)

/*******************************************************************************
* Define      : UPH_BYTES_PER_WORD16
* Group       : UPH
* Description : Number of bytes per 16 bit word.
*******************************************************************************/
#define UPH_BYTES_PER_WORD16          (1 << (     UPH_LOG2_WORD_SIZE_16     \
                                                  - UPH_LOG2_WORD_SIZE_8))

/*******************************************************************************
* Define      : UPH_NIBBLE_SIZE
* Group       : UPH
* Description : Number of bits in a nibble.
*******************************************************************************/
#define UPH_NIBBLE_SIZE                 (4)

/*******************************************************************************
* Define      : UPH_NIBBLE_MASK
* Group       : UPH
* Description : Masks the bits from one nibble within a larger word.
*******************************************************************************/
#define UPH_NIBBLE_MASK                 ((1 << UPH_NIBBLE_SIZE) - 1)

/*******************************************************************************
* Define      : UPH_NIBBLES_PER_WORD16
* Group       : UPH
* Description : Number of nibbles per 16 bit word.
*******************************************************************************/
#define UPH_NIBBLES_PER_WORD16          (1 << (     UPH_LOG2_WORD_SIZE_16     \
                                                  - UPH_LOG2_NIBBLE_SIZE))

/*******************************************************************************
* Define      : UPH_NIBBLES_PER_WORD16_BY2
* Group       : UPH
* Description : Number of nibbles per 16 bit word divided by 2.
*******************************************************************************/
#define UPH_NIBBLES_PER_WORD16_BY2      (UPH_NIBBLES_PER_WORD16 >> 1)

/*******************************************************************************
* Define      : UPH_NIBBLE_IN_WORD_MASK
* Group       : UPH
* Description : Given a nibble index NI, NI & UPH_NIBBLE_IN_WORD_MASK is
*               the position of the nibble within the word it lies in.
*******************************************************************************/
#define UPH_NIBBLE_IN_WORD_MASK         ((1 << UPH_LOG2_NIBBLE_SIZE) - 1)

/*******************************************************************************
* Define      : UPH_LOG2_NIBBLE_SIZE
* Group       : UPH
* Description : Logarithm base 2 of the number of bits in a nibble.
*******************************************************************************/
#define UPH_LOG2_NIBBLE_SIZE            (2)

/*******************************************************************************
** Macros
*******************************************************************************/

/*******************************************************************************
** Typedefs
*******************************************************************************/

/*******************************************************************************
** Global Data
*******************************************************************************/

/*******************************************************************************
** Global Function Prototypes
*******************************************************************************/

#endif
/* END OF FILE */

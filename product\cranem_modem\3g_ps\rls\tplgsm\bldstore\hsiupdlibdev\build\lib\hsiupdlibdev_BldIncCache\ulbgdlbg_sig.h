/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psas/3g.mod/api/inc/ulbgdlbg_sig.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2011/03/03 15:10:43 $
 **************************************************************************
 * File Description:
 *
 * 3G PS Signal Data Type definitions for the ULBG-DLBG Interface
 **************************************************************************/

#if !defined ULBGDLBG_SIG_H
#define      ULBGDLBG_SIG_H

/***************************************************************************
* Nested Include Files
***************************************************************************/

#include <ups_typ.h>
#include "rabstate.h"
#include "sm_sig.h"
#if 0
#include <sndata.h>
#endif

#include <ulbgsndata.h>

/***************************************************************************
*   Manifest Constants
***************************************************************************/

/***************************************************************************
*   Macro Functions
***************************************************************************/

#define ULBG_SIGNALS  \
        UlbgDlbgDeactivateInd       ulbgDlbgDeactivateInd;      \
        UlbgDlbgPdpInd              ulbgDlbgPdpInd;             \
        UlbgDlbgResetPdpCounterInd  ulbgDlbgResetPdpCounterInd; \
        UlbgDlbgXidReq              ulbgDlbgXidReq;             \
        EmptySignal                 ulbgDlbgDataRsp;            \


/***************************************************************************
*   Types
***************************************************************************/


typedef struct UlbgDlbgDeactivateIndTag
/** \struct UlbgDlbgDeactivateInd
* \brief A signal is used by ULBG to inform DLBG of Nsapi deactivation.
*/
{
	Nsapi nsapi;
	Int16 pdpIndex;
}
UlbgDlbgDeactivateInd;


/** \struct UlbgDlbgResetPdpCounterInd
* \brief A signal sent from ULBG to DLBG upon PDP data counter reset.
*/
typedef struct UlbgDlbgResetPdpCounterIndTag
{
	Int16  pdpIndex;
}UlbgDlbgResetPdpCounterInd;

typedef struct UlbgDlbgRabmPdpDataReqTag
{
    Nsapi    nsapi;
    Int16    pdpIndex;
    Boolean  sendCnf;
}UlbgDlbgXidReq;

/** \struct ulbgDlbgPdpInd
* \brief A signal sent from ULBG to DLBG upon reception of Suspend/Resume indication from SM.
* In order to forward PDP context initialization data to the DLBG of every PDP context.
* in order to allow their allocation in DLBG.
*/
typedef struct UlbgDlbgPdpIndTag
{
	UlbgDlbgXidReq rabmPdpDlData[MAX_NUM_NSAPIS];
}UlbgDlbgPdpInd;



/***************************************************************************
 * Global Variables
 ***************************************************************************/
extern SndcpEntity *ulbgSn_p;	/** ULBG DB */

/** @} */ //3G_RRC_UPDCP

/***************************************************************************
* Function Prototypes
***************************************************************************/




#endif
/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/


/************************************************************************/
/*                                                                      */
/* Title: IPC Comm. pacakge header file                                 */
/*                                                                      */
/* Filename:  WS_IPCICATFunc.h                                          */
/*                                                                      */
/* Authors: <AUTHORS>
/*                                                                      */
/* Target, subsystem: Common Platform, HAL                              */
/************************************************************************/
#ifndef _WS_IPCICATFunc_H_
#define _WS_IPCICATFunc_H_

void traceCmdSentToDSP (IPC_CmdMsgParams *cmdToSend);
void traceDataSentToDSP (UINT32 *data, UINT16 dataSize);
void traceDataReceivedFromDSP (UINT32 *data, UINT16 dataSize);
void traceMsgReceivedFromDSP (UINT16 msgOpCode, UINT16 msgLength, UINT8 *msgData);
void traceCmdBufferFull(UINT32 head, UINT32 tail, UINT32 required, UINT32 cmdsPending);

#endif

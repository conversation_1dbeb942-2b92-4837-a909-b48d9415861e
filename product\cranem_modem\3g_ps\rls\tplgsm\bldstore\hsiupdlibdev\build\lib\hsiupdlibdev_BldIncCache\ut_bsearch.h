/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/******************************************************************************
 * File Name:	ut_bsearch.h
 *
 *
 * File Description: binary search  header file
 *
 *****************************************************************************/

#if !defined (UT_BSEARCH_H)
#define       UT_BSEARCH_H
#include "system.h"

#define UT_BSEARCH_NOT_FOUND	0xFFFFFFFF

/******************************************************************************
 *
 * Function     : utBinarySearch
 *
 * Scope        : Global
 *
 * Parameters   : pointer to long array, the array length and the search criteria
 * Returns      : the index of the minimum element that is greater or equal than the search criteria in the table,
 *                or UT_BSEARCH_NOT_FOUND if not found.
 *
 * Description  : Binary search algoritm
 *****************************************************************************/
Int32 utBinarySearch( const Int32 Array[] ,  Int32 ArrayLength ,  Int32  SearchValue);
SignedInt32 utBinarySearchOff( const Int32 Array[] ,  Int32 ArrayLength ,  Int32  SearchValue, Int32 iCompareElementInt32offset,Int32 elementSizeInInt32);

#endif /* UT_BSEARCH_H */

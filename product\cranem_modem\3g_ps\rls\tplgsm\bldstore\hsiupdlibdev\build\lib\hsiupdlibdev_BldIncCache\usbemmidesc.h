/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/devices/transports/usb.mod/pub/src/usbemmidesc.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2006/10/24 10:16:13 $
 **************************************************************************
 *  File Description :
 *    Device descriptors for the TTPCom Ltd USB EMMI interface.
 **************************************************************************/
#if defined(UPGRADE_USB)
#if defined(USB_TTPCOM_EMMI_INTERFACE)

/* This file may be included multiple times in a descriptor set. */

/*******************************************************************************
 * TTPCom EMMI interface descriptors.
 ******************************************************************************/

  /*
   * Interface 0, Alternate 0
   */
  USB_INTERFACE_DESCRIPTOR(USB_EMMI_INTERFACE_NUMBER,              /* bInterfaceNumber */
                           USB_EMMI_INTERFACE_ALTERNATE_NUMBER,    /* bAlternateSetting */
                           USB_EMMI_NUM_ENDPOINT_DESCRIPTORS,      /* bNumEndpoints */
                           USB_INTERFACE_PROTOCOL_VENDOR,          /* bInterfaceClass */
                           USB_TTPCOM_INTERFACE_SUBCLASS_GENIE,    /* bInterfaceSubclass */
                           USB_TTPCOM_INTERFACE_PROTOCOL_EMMI,     /* bInterfaceProtocol */
                           USB_STRING_NOT_DEFINED_INDEX),          /* iInterface */

  USB_ENDPOINT_DESCRIPTOR((USB_EMMI_IN_PHYSICAL_ENDPOINT
                           |USB_ENDPOINT_ATTR_DIRECTION_IN),       /* bEndpointAddress */
                          USB_ENDPOINT_TYPE_BULK,                  /* bmAttributes */
                          64,                                      /* wMaxPacketSize */
                          0),                                      /* bInterval (ms) */

  USB_ENDPOINT_DESCRIPTOR((USB_EMMI_OUT_PHYSICAL_ENDPOINT
                           |USB_ENDPOINT_ATTR_DIRECTION_OUT),      /* bEndpointAddress */
                          USB_ENDPOINT_TYPE_BULK,                  /* bmAttributes */
                          64,                                      /* wMaxPacketSize */
                          0)                                       /* bInterval (ms) */

#endif /* USB_TTPCOM_EMMI_INTERFACE */
#endif /* UPGRADE_USB */
/* END OF FILE */

/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/******************************************************************************
 * File Name:	umaedchengmode.h
 *
 *
 * File Description: UMAC E-DCH ENG MODE header file
 *
 *****************************************************************************/

#if !defined (UMAEDCHENGMODE_H)
#define       UMAEDCHENGMODE_H

/******************************************************************************
 * Include Files
 *****************************************************************************/
#include <system.h>



/******************************************************************************
* Type Definitions
*****************************************************************************/
#if !defined (ON_PC)
typedef struct UmacEdchEngModeDebugStatisticsTag
{
	/* HSUPA engineering mode data */ 
	UINT32					octetCnt;				/* Total number of octets transmitted in the measurement period, including retransmissions */
	UINT16					ackBlkCnt;				/* Positive acknowledgment (ACK) block count, indicating the number of blocks transmitted by the UE and successfully received by the Node B */				
	UINT16					nackBlkCnt;				/* Negative acknowledgment (NACK) block count, indicating the number of blocks transmitted by the UE but not successfully decoded/received by the Node B */
	UINT8					servingGrant[32];		/* Uplink Serving Grant contains distribution count of E-AGCH Absolute Grant Value Indexes observed during that metric reporting period */
	UINT16					happyCnt;				/* Number of sampled happy bits during the measurement period */
	UINT16					unhappyCnt;				/* Number of sampled unhappy bits during the measurement period */
	UINT16					etfciSampleCnt;			/* Number of E-TFCI samples in the array ETFCI */
	UINT8					etfci[128];				/* Array of uplink E-DCH transport format combination indicator (E-TFCI) samples in the range (0..127), used to identify the transport block size on E-DCH */
}UmacEdchEngModeDebugStatistics;

extern UmacEdchEngModeDebugStatistics	umacEdchEngModeDebugStatistics_g; 
#endif /* !ON_PC */

#endif /* UMAEDCHENGMODE_H */
